#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库空间清理工具
================
解决数据库空间不足问题
"""

import mysql.connector
import os
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

class DatabaseCleanupTool:
    def __init__(self):
        """初始化数据库连接"""
        self.db_config = {
            'host': '************',
            'database': 'finance',
            'user': 'root',
            'password': '',
            'charset': 'utf8mb4',
            'autocommit': True
        }
        self.connection = None
        self.cursor = None
        
    def connect_database(self):
        """连接数据库"""
        try:
            print("🔗 连接MySQL数据库...")
            self.connection = mysql.connector.connect(**self.db_config)
            self.cursor = self.connection.cursor()
            print(f"✅ 成功连接到数据库: {self.db_config['host']}/{self.db_config['database']}")
            return True
        except mysql.connector.Error as e:
            print(f"❌ 数据库连接失败: {e}")
            return False
    
    def check_database_size(self):
        """检查数据库大小"""
        try:
            print("\n📊 检查数据库空间使用情况...")
            
            # 检查数据库总大小
            self.cursor.execute("""
                SELECT 
                    table_schema as 'Database',
                    ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) as 'Size_MB'
                FROM information_schema.tables 
                WHERE table_schema = 'finance'
                GROUP BY table_schema
            """)
            
            db_size = self.cursor.fetchone()
            if db_size:
                print(f"📊 数据库 '{db_size[0]}' 总大小: {db_size[1]} MB")
            
            # 检查各表大小
            self.cursor.execute("""
                SELECT 
                    table_name,
                    ROUND(((data_length + index_length) / 1024 / 1024), 2) as 'Size_MB',
                    table_rows
                FROM information_schema.TABLES 
                WHERE table_schema = 'finance'
                ORDER BY (data_length + index_length) DESC
                LIMIT 20
            """)
            
            tables = self.cursor.fetchall()
            print(f"\n📊 前20个最大的表:")
            print("表名                          | 大小(MB) | 行数")
            print("-" * 55)
            
            total_size = 0
            for table in tables:
                table_name = table[0]
                size_mb = float(table[1])
                row_count = table[2] if table[2] is not None else 0
                total_size += size_mb
                print(f"{table_name:30s} | {size_mb:8.2f} | {row_count:10d}")
            
            print(f"\n📊 前20个表总大小: {total_size:.2f} MB")
            
            return True
            
        except mysql.connector.Error as e:
            print(f"❌ 检查数据库大小失败: {e}")
            return False
    
    def check_disk_space(self):
        """检查磁盘空间"""
        try:
            print("\n💾 检查磁盘空间...")
            
            # 检查MySQL数据目录
            self.cursor.execute("SHOW VARIABLES LIKE 'datadir'")
            datadir_result = self.cursor.fetchone()
            if datadir_result:
                datadir = datadir_result[1]
                print(f"📁 MySQL数据目录: {datadir}")
            
            # 检查可用空间 (这个查询可能在某些MySQL版本中不可用)
            try:
                self.cursor.execute("SELECT @@datadir")
                mysql_datadir = self.cursor.fetchone()[0]
                print(f"📁 MySQL数据目录: {mysql_datadir}")
            except:
                pass
            
            return True
            
        except mysql.connector.Error as e:
            print(f"❌ 检查磁盘空间失败: {e}")
            return False
    
    def find_cleanup_candidates(self):
        """查找可以清理的数据"""
        try:
            print("\n🔍 查找可清理的数据...")
            
            cleanup_candidates = []
            
            # 1. 查找重复的备份表
            self.cursor.execute("""
                SELECT table_name 
                FROM information_schema.tables 
                WHERE table_schema = 'finance' 
                AND table_name LIKE '%backup%'
                ORDER BY table_name
            """)
            
            backup_tables = self.cursor.fetchall()
            if backup_tables:
                print(f"\n📋 发现 {len(backup_tables)} 个备份表:")
                for table in backup_tables:
                    table_name = table[0]
                    
                    # 获取表大小
                    self.cursor.execute(f"""
                        SELECT 
                            ROUND(((data_length + index_length) / 1024 / 1024), 2) as size_mb,
                            table_rows
                        FROM information_schema.TABLES 
                        WHERE table_schema = 'finance' AND table_name = '{table_name}'
                    """)
                    
                    table_info = self.cursor.fetchone()
                    if table_info:
                        size_mb = float(table_info[0])
                        row_count = table_info[1] if table_info[1] is not None else 0
                        print(f"   • {table_name}: {size_mb:.2f} MB, {row_count} 行")
                        cleanup_candidates.append(('backup_table', table_name, size_mb))
            
            # 2. 查找临时表
            self.cursor.execute("""
                SELECT table_name 
                FROM information_schema.tables 
                WHERE table_schema = 'finance' 
                AND (table_name LIKE 'temp_%' OR table_name LIKE 'tmp_%')
                ORDER BY table_name
            """)
            
            temp_tables = self.cursor.fetchall()
            if temp_tables:
                print(f"\n📋 发现 {len(temp_tables)} 个临时表:")
                for table in temp_tables:
                    table_name = table[0]
                    
                    # 获取表大小
                    self.cursor.execute(f"""
                        SELECT 
                            ROUND(((data_length + index_length) / 1024 / 1024), 2) as size_mb,
                            table_rows
                        FROM information_schema.TABLES 
                        WHERE table_schema = 'finance' AND table_name = '{table_name}'
                    """)
                    
                    table_info = self.cursor.fetchone()
                    if table_info:
                        size_mb = float(table_info[0])
                        row_count = table_info[1] if table_info[1] is not None else 0
                        print(f"   • {table_name}: {size_mb:.2f} MB, {row_count} 行")
                        cleanup_candidates.append(('temp_table', table_name, size_mb))
            
            # 3. 查找旧的日志表
            self.cursor.execute("""
                SELECT table_name 
                FROM information_schema.tables 
                WHERE table_schema = 'finance' 
                AND (table_name LIKE '%log%' OR table_name LIKE '%history%')
                ORDER BY table_name
            """)
            
            log_tables = self.cursor.fetchall()
            if log_tables:
                print(f"\n📋 发现 {len(log_tables)} 个日志/历史表:")
                for table in log_tables:
                    table_name = table[0]
                    
                    # 获取表大小
                    self.cursor.execute(f"""
                        SELECT 
                            ROUND(((data_length + index_length) / 1024 / 1024), 2) as size_mb,
                            table_rows
                        FROM information_schema.TABLES 
                        WHERE table_schema = 'finance' AND table_name = '{table_name}'
                    """)
                    
                    table_info = self.cursor.fetchone()
                    if table_info:
                        size_mb = float(table_info[0])
                        row_count = table_info[1] if table_info[1] is not None else 0
                        print(f"   • {table_name}: {size_mb:.2f} MB, {row_count} 行")
                        cleanup_candidates.append(('log_table', table_name, size_mb))
            
            return cleanup_candidates
            
        except mysql.connector.Error as e:
            print(f"❌ 查找清理候选失败: {e}")
            return []
    
    def optimize_tables(self):
        """优化表以释放空间"""
        try:
            print("\n🔧 优化表以释放空间...")
            
            # 获取所有表
            self.cursor.execute("""
                SELECT table_name 
                FROM information_schema.tables 
                WHERE table_schema = 'finance'
                ORDER BY table_name
            """)
            
            tables = self.cursor.fetchall()
            optimized_count = 0
            
            for table in tables:
                table_name = table[0]
                try:
                    print(f"   🔧 优化表: {table_name}")
                    self.cursor.execute(f"OPTIMIZE TABLE `{table_name}`")
                    optimized_count += 1
                except mysql.connector.Error as e:
                    print(f"   ⚠️ 优化 {table_name} 失败: {e}")
            
            print(f"✅ 成功优化 {optimized_count} 个表")
            return True
            
        except mysql.connector.Error as e:
            print(f"❌ 优化表失败: {e}")
            return False
    
    def cleanup_old_data(self, days_to_keep=365):
        """清理旧数据"""
        try:
            print(f"\n🗑️ 清理 {days_to_keep} 天前的旧数据...")
            
            cutoff_date = datetime.now() - timedelta(days=days_to_keep)
            cutoff_str = cutoff_date.strftime('%Y-%m-%d')
            
            # 查找有日期字段的表
            self.cursor.execute("""
                SELECT DISTINCT table_name 
                FROM information_schema.columns 
                WHERE table_schema = 'finance' 
                AND column_name IN ('date', 'created_at', 'timestamp', 'time')
                ORDER BY table_name
            """)
            
            date_tables = self.cursor.fetchall()
            cleaned_count = 0
            
            for table in date_tables:
                table_name = table[0]
                
                # 检查表中是否有旧数据
                try:
                    # 先检查日期字段名
                    date_column = None
                    for col in ['date', 'created_at', 'timestamp', 'time']:
                        self.cursor.execute(f"""
                            SELECT COUNT(*) 
                            FROM information_schema.columns 
                            WHERE table_schema = 'finance' 
                            AND table_name = '{table_name}' 
                            AND column_name = '{col}'
                        """)
                        if self.cursor.fetchone()[0] > 0:
                            date_column = col
                            break
                    
                    if date_column:
                        # 检查旧数据数量
                        self.cursor.execute(f"""
                            SELECT COUNT(*) 
                            FROM `{table_name}` 
                            WHERE `{date_column}` < '{cutoff_str}'
                        """)
                        
                        old_count = self.cursor.fetchone()[0]
                        if old_count > 0:
                            print(f"   🗑️ 表 {table_name} 有 {old_count} 条旧数据")
                            
                            # 询问是否删除（这里先跳过实际删除，只显示）
                            print(f"   ⚠️ 建议手动确认后删除: DELETE FROM `{table_name}` WHERE `{date_column}` < '{cutoff_str}';")
                            cleaned_count += 1
                
                except mysql.connector.Error as e:
                    print(f"   ⚠️ 检查 {table_name} 失败: {e}")
            
            print(f"📊 发现 {cleaned_count} 个表包含旧数据")
            return True
            
        except mysql.connector.Error as e:
            print(f"❌ 清理旧数据失败: {e}")
            return False
    
    def generate_cleanup_script(self, cleanup_candidates):
        """生成清理脚本"""
        try:
            print("\n📝 生成清理脚本...")
            
            script_content = f"""-- 数据库清理脚本
-- 生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
-- 注意: 执行前请确保已备份重要数据

USE finance;

-- 1. 删除备份表
"""
            
            backup_tables = [item for item in cleanup_candidates if item[0] == 'backup_table']
            if backup_tables:
                script_content += "\n-- 备份表 (可以安全删除)\n"
                for _, table_name, size_mb in backup_tables:
                    script_content += f"-- DROP TABLE IF EXISTS `{table_name}`; -- 释放 {size_mb:.2f} MB\n"
            
            temp_tables = [item for item in cleanup_candidates if item[0] == 'temp_table']
            if temp_tables:
                script_content += "\n-- 临时表 (可以安全删除)\n"
                for _, table_name, size_mb in temp_tables:
                    script_content += f"-- DROP TABLE IF EXISTS `{table_name}`; -- 释放 {size_mb:.2f} MB\n"
            
            log_tables = [item for item in cleanup_candidates if item[0] == 'log_table']
            if log_tables:
                script_content += "\n-- 日志表 (请谨慎删除)\n"
                for _, table_name, size_mb in log_tables:
                    script_content += f"-- DROP TABLE IF EXISTS `{table_name}`; -- 释放 {size_mb:.2f} MB\n"
            
            script_content += """
-- 2. 优化所有表
-- SELECT CONCAT('OPTIMIZE TABLE `', table_name, '`;') 
-- FROM information_schema.tables 
-- WHERE table_schema = 'finance';

-- 3. 清理二进制日志 (如果启用了)
-- PURGE BINARY LOGS BEFORE DATE_SUB(NOW(), INTERVAL 7 DAY);

-- 4. 重建表以回收空间 (对大表谨慎使用)
-- ALTER TABLE large_table_name ENGINE=InnoDB;
"""
            
            # 保存脚本
            with open('database_cleanup_script.sql', 'w', encoding='utf-8') as f:
                f.write(script_content)
            
            print("✅ 清理脚本已生成: database_cleanup_script.sql")
            
            # 计算可释放的空间
            total_space = sum(item[2] for item in cleanup_candidates)
            print(f"📊 预计可释放空间: {total_space:.2f} MB")
            
            return True
            
        except Exception as e:
            print(f"❌ 生成清理脚本失败: {e}")
            return False
    
    def show_recommendations(self):
        """显示清理建议"""
        print("\n💡 数据库空间清理建议:")
        print("=" * 60)
        
        print("🔧 立即可执行的操作:")
        print("   1. 运行 OPTIMIZE TABLE 优化所有表")
        print("   2. 删除明确的备份表和临时表")
        print("   3. 清理MySQL二进制日志")
        
        print("\n⚠️ 需要谨慎考虑的操作:")
        print("   1. 删除历史数据 (建议保留至少1年)")
        print("   2. 删除日志表 (可能影响审计)")
        print("   3. 重建大表 (耗时较长)")
        
        print("\n🛡️ 预防措施:")
        print("   1. 设置自动清理旧数据的定时任务")
        print("   2. 监控数据库大小增长")
        print("   3. 定期优化表")
        print("   4. 考虑数据归档策略")
        
        print("\n📝 使用生成的清理脚本:")
        print("   1. 检查 database_cleanup_script.sql")
        print("   2. 取消注释需要执行的语句")
        print("   3. 在测试环境先验证")
        print("   4. 备份后在生产环境执行")
    
    def close_connection(self):
        """关闭数据库连接"""
        if self.cursor:
            self.cursor.close()
        if self.connection:
            self.connection.close()
        print("🔒 数据库连接已关闭")
    
    def run(self):
        """执行主流程"""
        print("🎯 数据库空间清理工具")
        print("=" * 60)
        print(f"📅 执行时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"🏠 数据库: {self.db_config['host']}/{self.db_config['database']}")
        
        try:
            # 1. 连接数据库
            if not self.connect_database():
                return False
            
            # 2. 检查数据库大小
            if not self.check_database_size():
                return False
            
            # 3. 检查磁盘空间
            self.check_disk_space()
            
            # 4. 查找清理候选
            cleanup_candidates = self.find_cleanup_candidates()
            
            # 5. 优化表
            if not self.optimize_tables():
                return False
            
            # 6. 检查旧数据
            self.cleanup_old_data()
            
            # 7. 生成清理脚本
            if cleanup_candidates:
                self.generate_cleanup_script(cleanup_candidates)
            
            # 8. 显示建议
            self.show_recommendations()
            
            print("\n🎉 数据库空间分析完成!")
            print("📝 请查看生成的清理脚本和建议")
            
            return True
            
        except Exception as e:
            print(f"❌ 执行失败: {e}")
            return False
        
        finally:
            self.close_connection()

def main():
    """主函数"""
    cleaner = DatabaseCleanupTool()
    success = cleaner.run()
    
    if success:
        print("\n✅ 分析完成!")
        print("📝 下一步: 检查生成的清理脚本并执行适当的清理操作")
    else:
        print("\n❌ 分析失败!")

if __name__ == "__main__":
    main()
