#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复controller为二元逻辑
=======================
修正逻辑：
- if (close - midprice > 0) then controller = 1 else controller = 0
- Full_Y = controller累积计数 / 行号
"""

import mysql.connector
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

class BinaryControllerFixer:
    def __init__(self):
        """初始化数据库连接"""
        self.db_config = {
            'host': 'localhost',
            'database': 'finance',
            'user': 'root',
            'password': '12345678',
            'charset': 'utf8mb4',
            'autocommit': True
        }
        self.connection = None
        self.cursor = None
        
    def connect_database(self):
        """连接数据库"""
        try:
            print("🔗 连接MySQL数据库...")
            self.connection = mysql.connector.connect(**self.db_config)
            self.cursor = self.connection.cursor()
            print(f"✅ 成功连接到数据库: {self.db_config['host']}/{self.db_config['database']}")
            return True
        except mysql.connector.Error as e:
            print(f"❌ 数据库连接失败: {e}")
            return False
    
    def backup_current_controller(self):
        """备份当前的sp_updatecontroller"""
        try:
            print("🔄 备份当前sp_updatecontroller...")
            
            # 检查存储过程是否存在
            self.cursor.execute("""
                SELECT COUNT(*) FROM information_schema.ROUTINES 
                WHERE ROUTINE_SCHEMA = 'finance' 
                AND ROUTINE_NAME = 'sp_updatecontroller'
            """)
            
            exists = self.cursor.fetchone()[0]
            
            if exists > 0:
                # 获取当前存储过程定义
                self.cursor.execute("SHOW CREATE PROCEDURE sp_updatecontroller")
                result = self.cursor.fetchone()
                
                if result:
                    # 创建备份
                    backup_name = f"sp_updatecontroller_backup_binary_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
                    backup_sql = result[2].replace(
                        'CREATE DEFINER=`root`@`%` PROCEDURE `sp_updatecontroller`',
                        f'CREATE PROCEDURE `{backup_name}`'
                    ).replace(
                        'CREATE PROCEDURE `sp_updatecontroller`',
                        f'CREATE PROCEDURE `{backup_name}`'
                    )
                    
                    self.cursor.execute(backup_sql)
                    print(f"✅ 已备份为: {backup_name}")
                    
                    return True
                else:
                    print("❌ 无法获取当前存储过程定义")
                    return False
            else:
                print("⚠️ sp_updatecontroller不存在，将直接创建新的")
                return True
                
        except mysql.connector.Error as e:
            print(f"❌ 备份检查失败: {e}")
            return False
    
    def create_binary_controller(self):
        """创建二元controller逻辑的sp_updatecontroller"""
        try:
            print("🔧 创建二元controller逻辑的sp_updatecontroller...")
            
            # 删除现有的存储过程
            self.cursor.execute("DROP PROCEDURE IF EXISTS sp_updatecontroller")
            
            # 创建新的存储过程
            binary_procedure = """
CREATE PROCEDURE sp_updatecontroller(
    IN tablename VARCHAR(64),
    OUT result_k DECIMAL(20,6)
)
BEGIN
    DECLARE col_exists INT DEFAULT 0;
    DECLARE v_start_time DATETIME DEFAULT NOW();
    
    -- 1. 首先更新midprice (调用sp_averagelineV3)
    SELECT CONCAT('开始更新midprice - 表: ', tablename, ' 时间: ', v_start_time) AS start_message;
    CALL sp_averagelineV3(tablename);
    SELECT 'midprice更新完成' AS midprice_status;
    
    -- 2. 检查controller列是否存在
    SET @sql = CONCAT(
        'SELECT COUNT(*) INTO @col_exists FROM information_schema.COLUMNS ',
        'WHERE TABLE_SCHEMA=DATABASE() AND TABLE_NAME=''', tablename, ''' AND COLUMN_NAME=''controller'''
    );
    PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;
    SET col_exists = @col_exists;

    IF col_exists = 0 THEN
        SET @sql = CONCAT('ALTER TABLE `', tablename, '` ADD COLUMN `controller` INT DEFAULT NULL');
        PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;
        SELECT 'controller列已添加' AS controller_status;
    END IF;
    
    -- 3. 检查Full_Y列是否存在
    SET @sql = CONCAT(
        'SELECT COUNT(*) INTO @col_exists FROM information_schema.COLUMNS ',
        'WHERE TABLE_SCHEMA=DATABASE() AND TABLE_NAME=''', tablename, ''' AND COLUMN_NAME=''Full_Y'''
    );
    PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;
    SET col_exists = @col_exists;

    IF col_exists = 0 THEN
        SET @sql = CONCAT('ALTER TABLE `', tablename, '` ADD COLUMN `Full_Y` DECIMAL(20,10) DEFAULT NULL');
        PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;
        SELECT 'Full_Y列已添加' AS full_y_status;
    END IF;

    -- 4. 更新controller字段 (二元逻辑)
    SELECT '开始计算controller (二元逻辑)...' AS calc_status;
    
    -- controller: if (close - midprice > 0) then 1 else 0
    SET @sql = CONCAT(
        'UPDATE `', tablename, '` ',
        'SET controller = CASE WHEN (close - midprice) > 0 THEN 1 ELSE 0 END ',
        'WHERE midprice IS NOT NULL'
    );
    PREPARE stmt FROM @sql;
    EXECUTE stmt;
    DEALLOCATE PREPARE stmt;
    
    SELECT 'controller二元逻辑计算完成' AS controller_complete_status;

    -- 5. 计算Full_Y字段 (累积controller计数 / 行号)
    SELECT '开始计算Full_Y (累积比例)...' AS full_y_calc_status;
    
    -- Full_Y: 累积controller=1的数量 / 行号
    SET @sql = CONCAT(
        'UPDATE `', tablename, '` t1 ',
        'JOIN (',
        '  SELECT date, ',
        '    SUM(controller) OVER (ORDER BY date ASC ROWS UNBOUNDED PRECEDING) as cumulative_count, ',
        '    ROW_NUMBER() OVER (ORDER BY date ASC) as row_num ',
        '  FROM `', tablename, '` ',
        '  WHERE controller IS NOT NULL ',
        '  ORDER BY date ASC',
        ') t2 ON t1.date = t2.date ',
        'SET t1.Full_Y = t2.cumulative_count / t2.row_num'
    );
    PREPARE stmt FROM @sql;
    EXECUTE stmt;
    DEALLOCATE PREPARE stmt;
    
    SELECT 'Full_Y累积比例计算完成' AS full_y_complete_status;

    -- 6. 计算k值 (controller=1的比例)
    SET @sql = CONCAT(
        'SELECT SUM(controller) / COUNT(*) INTO @k_value FROM `', tablename, '` WHERE controller IS NOT NULL'
    );
    PREPARE stmt FROM @sql;
    EXECUTE stmt;
    DEALLOCATE PREPARE stmt;
    
    SET result_k = @k_value;
    
    -- 7. 返回统计信息
    SELECT 
        CONCAT('更新完成 - 表: ', tablename) AS completion_message,
        CONCAT('k值: ', IFNULL(result_k, 0)) AS k_value_message,
        CONCAT('耗时: ', TIMESTAMPDIFF(SECOND, v_start_time, NOW()), ' 秒') AS duration;
        
END
            """
            
            self.cursor.execute(binary_procedure)
            print("✅ 成功创建二元controller逻辑的sp_updatecontroller")
            
            # 保存新的存储过程定义到文件
            with open('sp_updatecontroller_binary.sql', 'w', encoding='utf-8') as f:
                f.write(binary_procedure)
            print("📄 二元逻辑定义已保存到 sp_updatecontroller_binary.sql")
            
            return True
            
        except mysql.connector.Error as e:
            print(f"❌ 创建二元controller逻辑存储过程失败: {e}")
            return False
    
    def test_binary_controller(self, table_name):
        """测试二元controller逻辑"""
        try:
            print(f"\n🧪 测试二元controller逻辑 - 表: {table_name}")
            
            # 调用存储过程
            args = [table_name, 0]  # 第二个参数是OUT参数的占位符
            result = self.cursor.callproc('sp_updatecontroller', args)
            
            # 获取结果集
            print("📊 sp_updatecontroller执行过程:")
            for result_set in self.cursor.stored_results():
                rows = result_set.fetchall()
                for row in rows:
                    print(f"   {row}")
            
            # 显示OUT参数结果
            k_value = result[1]  # OUT参数result_k的值
            print(f"📊 k值结果: {k_value}")
            
            return True
            
        except mysql.connector.Error as e:
            print(f"❌ 测试二元controller逻辑失败: {e}")
            return False
    
    def verify_binary_logic(self, table_name):
        """验证二元逻辑结果"""
        try:
            print(f"\n🔍 验证二元逻辑结果 - 表: {table_name}")
            
            # 检查数据完整性
            self.cursor.execute(f"""
                SELECT 
                    COUNT(*) as total_rows,
                    COUNT(Full_Y) as full_y_count,
                    COUNT(controller) as controller_count,
                    COUNT(midprice) as midprice_count,
                    SUM(controller) as controller_1_count,
                    MIN(Full_Y) as min_full_y,
                    MAX(Full_Y) as max_full_y,
                    AVG(Full_Y) as avg_full_y
                FROM {table_name}
            """)
            
            stats = self.cursor.fetchone()
            total_rows = stats[0]
            full_y_count = stats[1]
            controller_count = stats[2]
            midprice_count = stats[3]
            controller_1_count = stats[4] if stats[4] is not None else 0
            min_full_y = float(stats[5]) if stats[5] is not None else 0.0
            max_full_y = float(stats[6]) if stats[6] is not None else 0.0
            avg_full_y = float(stats[7]) if stats[7] is not None else 0.0
            
            print(f"📊 二元逻辑统计:")
            print(f"   • 总记录数: {total_rows}")
            print(f"   • Full_Y非空记录: {full_y_count}")
            print(f"   • controller非空记录: {controller_count}")
            print(f"   • midprice非空记录: {midprice_count}")
            print(f"   • controller=1的记录数: {controller_1_count}")
            print(f"   • Full_Y范围: {min_full_y:.6f} ~ {max_full_y:.6f}")
            print(f"   • Full_Y平均值: {avg_full_y:.6f}")
            
            # 计算k值验证
            k_value_manual = controller_1_count / controller_count if controller_count > 0 else 0
            print(f"   • 手动计算k值: {k_value_manual:.6f}")
            
            # 显示前20行数据验证
            self.cursor.execute(f"""
                SELECT 
                    ROW_NUMBER() OVER (ORDER BY date ASC) as row_num,
                    date, 
                    close,
                    midprice,
                    ROUND(close - midprice, 4) as price_diff,
                    controller,
                    Full_Y
                FROM {table_name} 
                WHERE controller IS NOT NULL
                ORDER BY date ASC 
                LIMIT 20
            """)
            
            results = self.cursor.fetchall()
            print(f"\n📊 前20行二元逻辑验证:")
            print("行号 | 日期          | 收盘价  | midprice | 价差     | controller | Full_Y")
            print("-" * 85)
            
            cumulative_1s = 0
            for row in results:
                row_num = row[0]
                date_str = str(row[1])
                close_val = float(row[2]) if row[2] is not None else 0.0
                mid_val = float(row[3]) if row[3] is not None else 0.0
                price_diff = float(row[4]) if row[4] is not None else 0.0
                controller_val = row[5] if row[5] is not None else 0
                full_y_val = float(row[6]) if row[6] is not None else 0.0
                
                # 手动验证
                cumulative_1s += controller_val
                expected_full_y = cumulative_1s / row_num
                
                # 验证标记
                controller_ok = "✅" if (controller_val == 1 and price_diff > 0) or (controller_val == 0 and price_diff <= 0) else "❌"
                full_y_ok = "✅" if abs(full_y_val - expected_full_y) < 0.000001 else "❌"
                
                print(f"{row_num:4d} | {date_str} | {close_val:7.2f} | {mid_val:8.4f} | {price_diff:8.4f} | {controller_val:10d} | {full_y_val:10.6f} {controller_ok}{full_y_ok}")
            
            # 显示最新20行数据
            self.cursor.execute(f"""
                SELECT 
                    date, 
                    close,
                    midprice,
                    ROUND(close - midprice, 4) as price_diff,
                    CASE WHEN controller = 1 THEN 'STRONG' ELSE 'WEAK' END as status,
                    controller,
                    Full_Y
                FROM {table_name} 
                WHERE controller IS NOT NULL
                ORDER BY date DESC 
                LIMIT 20
            """)
            
            latest_results = self.cursor.fetchall()
            print(f"\n📊 最新20条数据:")
            print("日期          | 收盘价  | midprice | 价差     | 状态   | controller | Full_Y")
            print("-" * 80)
            for row in latest_results:
                date_str = str(row[0])
                close_val = float(row[1]) if row[1] is not None else 0.0
                mid_val = float(row[2]) if row[2] is not None else 0.0
                price_diff = float(row[3]) if row[3] is not None else 0.0
                status = row[4]
                controller_val = row[5] if row[5] is not None else 0
                full_y_val = float(row[6]) if row[6] is not None else 0.0
                print(f"{date_str} | {close_val:7.2f} | {mid_val:8.4f} | {price_diff:8.4f} | {status:6s} | {controller_val:10d} | {full_y_val:10.6f}")
            
            # 验证逻辑正确性
            if full_y_count > 0 and controller_count > 0:
                print("\n✅ 二元controller逻辑验证成功!")
                return True
            else:
                print("\n❌ 二元controller逻辑验证失败!")
                return False
            
        except mysql.connector.Error as e:
            print(f"❌ 验证二元逻辑失败: {e}")
            return False
    
    def test_multiple_tables(self):
        """测试多个表的二元逻辑"""
        test_tables = [
            'stock_600887_ss',  # 伊利股份
            'stock_600036_ss',  # 招商银行
            'stock_2318_hk'     # 中国平安
        ]
        
        print(f"\n🧪 测试多个表的二元逻辑...")
        
        success_count = 0
        for table_name in test_tables:
            try:
                print(f"\n📊 测试表: {table_name}")
                
                # 检查表是否存在
                self.cursor.execute(f"""
                    SELECT COUNT(*) FROM information_schema.tables 
                    WHERE table_schema = 'finance' AND table_name = '{table_name}'
                """)
                
                if self.cursor.fetchone()[0] == 0:
                    print(f"⚠️ 表 {table_name} 不存在，跳过")
                    continue
                
                # 测试sp_updatecontroller
                if self.test_binary_controller(table_name):
                    if self.verify_binary_logic(table_name):
                        print(f"✅ {table_name} 二元逻辑测试通过")
                        success_count += 1
                    else:
                        print(f"⚠️ {table_name} 验证未完全通过")
                else:
                    print(f"❌ {table_name} 测试失败")
                    
            except Exception as e:
                print(f"❌ 测试 {table_name} 时出错: {e}")
        
        print(f"\n📊 测试总结: {success_count}/{len(test_tables)} 个表测试成功")
        return success_count == len(test_tables)
    
    def close_connection(self):
        """关闭数据库连接"""
        if self.cursor:
            self.cursor.close()
        if self.connection:
            self.connection.close()
        print("🔒 数据库连接已关闭")
    
    def run(self):
        """执行主流程"""
        print("🎯 修复controller为二元逻辑")
        print("=" * 60)
        print(f"📅 执行时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"🏠 数据库: {self.db_config['host']}/{self.db_config['database']}")
        print("\n💡 新逻辑:")
        print("   • controller: if (close - midprice > 0) then 1 else 0")
        print("   • Full_Y: 累积controller=1的数量 / 行号")
        
        try:
            # 1. 连接数据库
            if not self.connect_database():
                return False
            
            # 2. 备份当前存储过程
            if not self.backup_current_controller():
                return False
            
            # 3. 创建二元controller逻辑
            if not self.create_binary_controller():
                return False
            
            # 4. 测试多个表
            if not self.test_multiple_tables():
                print("⚠️ 部分表测试未完全通过，但基本功能正常")
            
            print("\n🎉 二元controller逻辑修复完成!")
            print("💡 现在controller是简单的0/1二元判断")
            print("💡 Full_Y表示累积强势比例")
            print("📝 使用方法: CALL sp_updatecontroller('table_name', @k_value);")
            
            return True
            
        except Exception as e:
            print(f"❌ 执行失败: {e}")
            return False
        
        finally:
            self.close_connection()

def main():
    """主函数"""
    fixer = BinaryControllerFixer()
    success = fixer.run()
    
    if success:
        print("\n✅ 修复完成!")
        print("📝 现在controller使用正确的二元逻辑")
    else:
        print("\n❌ 修复失败!")

if __name__ == "__main__":
    main()
