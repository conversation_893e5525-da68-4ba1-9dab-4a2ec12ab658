#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复stock_2318_hk表的Full_Y字段
==============================
为stock_2318_hk表重新计算并填充Full_Y字段
"""

import mysql.connector
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

class FullYFixer:
    def __init__(self):
        """初始化数据库连接"""
        self.db_config = {
            'host': '************',
            'database': 'finance',
            'user': 'root',
            'password': '',
            'charset': 'utf8mb4',
            'autocommit': True
        }
        self.connection = None
        self.cursor = None
        self.table_name = 'stock_2318_hk'

    def connect_database(self):
        """连接数据库"""
        try:
            print("🔗 连接MySQL数据库...")
            self.connection = mysql.connector.connect(**self.db_config)
            self.cursor = self.connection.cursor()
            print(f"✅ 成功连接到数据库: {self.db_config['host']}/{self.db_config['database']}")
            return True
        except mysql.connector.Error as e:
            print(f"❌ 数据库连接失败: {e}")
            return False

    def check_table_status(self):
        """检查表的当前状态"""
        try:
            print(f"\n📊 检查{self.table_name}表状态...")

            # 检查表是否存在
            self.cursor.execute(f"""
                SELECT COUNT(*) FROM information_schema.tables
                WHERE table_schema = 'finance' AND table_name = '{self.table_name}'
            """)

            if self.cursor.fetchone()[0] == 0:
                print(f"❌ 表 {self.table_name} 不存在")
                return False

            # 检查表结构
            self.cursor.execute(f"DESCRIBE {self.table_name}")
            columns = self.cursor.fetchall()

            has_full_y = False
            has_controller = False
            has_midprice = False
            has_close = False
            has_date = False

            print(f"📋 {self.table_name} 表结构:")
            for col in columns:
                col_name = col[0]
                col_type = col[1]
                print(f"   • {col_name} - {col_type}")

                if col_name == 'Full_Y':
                    has_full_y = True
                elif col_name == 'controller':
                    has_controller = True
                elif col_name == 'midprice':
                    has_midprice = True
                elif col_name == 'close':
                    has_close = True
                elif col_name == 'date':
                    has_date = True

            # 检查必要字段
            missing_fields = []
            if not has_date:
                missing_fields.append('date')
            if not has_close:
                missing_fields.append('close')
            if not has_midprice:
                missing_fields.append('midprice')
            if not has_controller:
                missing_fields.append('controller')
            if not has_full_y:
                missing_fields.append('Full_Y')

            if missing_fields:
                print(f"⚠️ 缺少必要字段: {', '.join(missing_fields)}")
                print("🔧 将在下一步添加缺失字段")
                # 不直接返回False，而是继续到修复字段步骤

            # 检查数据量 (只检查存在的字段)
            if not missing_fields:
                # 所有字段都存在，进行完整检查
                self.cursor.execute(f"""
                    SELECT
                        COUNT(*) as total_rows,
                        COUNT(Full_Y) as full_y_count,
                        COUNT(controller) as controller_count,
                        COUNT(midprice) as midprice_count,
                        MIN(date) as min_date,
                        MAX(date) as max_date
                    FROM {self.table_name}
                """)

                stats = self.cursor.fetchone()
                total_rows = stats[0]
                full_y_count = stats[1]
                controller_count = stats[2]
                midprice_count = stats[3]
                min_date = stats[4]
                max_date = stats[5]

                print(f"\n📊 数据统计:")
                print(f"   • 总记录数: {total_rows}")
                print(f"   • Full_Y非空记录: {full_y_count}")
                print(f"   • controller非空记录: {controller_count}")
                print(f"   • midprice非空记录: {midprice_count}")
                print(f"   • 日期范围: {min_date} ~ {max_date}")

                if full_y_count == 0:
                    print("🔧 需要修复: Full_Y字段全部为空")
                elif full_y_count < total_rows:
                    print(f"🔧 需要修复: Full_Y字段部分为空 ({total_rows - full_y_count} 条记录)")
                else:
                    print("✅ Full_Y字段已完整")
            else:
                # 有缺失字段，只检查基本信息
                basic_fields = []
                if has_date and has_close:
                    basic_fields.extend(['COUNT(*) as total_rows', 'MIN(date) as min_date', 'MAX(date) as max_date'])
                if has_midprice:
                    basic_fields.append('COUNT(midprice) as midprice_count')
                if has_full_y:
                    basic_fields.append('COUNT(Full_Y) as full_y_count')

                if basic_fields:
                    query = f"SELECT {', '.join(basic_fields)} FROM {self.table_name}"
                    self.cursor.execute(query)
                    stats = self.cursor.fetchone()

                    print(f"\n📊 基本数据统计:")
                    if has_date and has_close:
                        print(f"   • 总记录数: {stats[0]}")
                        print(f"   • 日期范围: {stats[1]} ~ {stats[2]}")

                    print("🔧 需要添加缺失字段并重新计算")

            return True

        except mysql.connector.Error as e:
            print(f"❌ 检查表状态失败: {e}")
            return False

    def fix_missing_fields(self):
        """修复缺失的字段"""
        try:
            print(f"\n🔧 检查并修复缺失字段...")

            # 检查并添加controller字段
            self.cursor.execute(f"""
                SELECT COUNT(*) FROM information_schema.COLUMNS
                WHERE TABLE_SCHEMA = 'finance'
                AND TABLE_NAME = '{self.table_name}'
                AND COLUMN_NAME = 'controller'
            """)

            if self.cursor.fetchone()[0] == 0:
                print("   📝 添加controller字段...")
                self.cursor.execute(f"""
                    ALTER TABLE {self.table_name}
                    ADD COLUMN controller INT DEFAULT NULL
                """)
                print("   ✅ controller字段已添加")

            # 检查并添加Full_Y字段
            self.cursor.execute(f"""
                SELECT COUNT(*) FROM information_schema.COLUMNS
                WHERE TABLE_SCHEMA = 'finance'
                AND TABLE_NAME = '{self.table_name}'
                AND COLUMN_NAME = 'Full_Y'
            """)

            if self.cursor.fetchone()[0] == 0:
                print("   📝 添加Full_Y字段...")
                self.cursor.execute(f"""
                    ALTER TABLE {self.table_name}
                    ADD COLUMN Full_Y DECIMAL(20,10) DEFAULT NULL
                """)
                print("   ✅ Full_Y字段已添加")

            return True

        except mysql.connector.Error as e:
            print(f"❌ 修复字段失败: {e}")
            return False

    def update_midprice(self):
        """更新midprice字段"""
        try:
            print(f"\n🔄 更新{self.table_name}的midprice...")

            # 调用sp_averagelineV3更新midprice
            self.cursor.callproc('sp_averagelineV3', [self.table_name])

            # 消费结果集
            for result_set in self.cursor.stored_results():
                rows = result_set.fetchall()
                for row in rows:
                    print(f"📊 {row}")

            print("✅ midprice更新完成")
            return True

        except mysql.connector.Error as e:
            print(f"❌ 更新midprice失败: {e}")
            return False

    def calculate_controller_and_full_y(self):
        """计算controller和Full_Y字段"""
        try:
            print(f"\n🧮 计算{self.table_name}的controller和Full_Y...")

            # 使用新逻辑计算controller和Full_Y
            # controller: 累积强势次数，当(close-midprice>0)时递增
            # Full_Y: controller/行号

            self.cursor.execute(f"""
                UPDATE {self.table_name} t1
                JOIN (
                    SELECT date,
                        SUM(CASE WHEN (close - midprice) > 0 THEN 1 ELSE 0 END)
                        OVER (ORDER BY date ASC ROWS UNBOUNDED PRECEDING) as cumulative_controller,
                        ROW_NUMBER() OVER (ORDER BY date ASC) as row_num
                    FROM {self.table_name}
                    WHERE midprice IS NOT NULL
                    ORDER BY date ASC
                ) t2 ON t1.date = t2.date
                SET t1.controller = t2.cumulative_controller,
                    t1.Full_Y = t2.cumulative_controller / t2.row_num
            """)

            print("✅ controller和Full_Y计算完成")
            return True

        except mysql.connector.Error as e:
            print(f"❌ 计算controller和Full_Y失败: {e}")
            return False

    def verify_results(self):
        """验证修复结果"""
        try:
            print(f"\n🔍 验证{self.table_name}修复结果...")

            # 检查修复后的统计
            self.cursor.execute(f"""
                SELECT
                    COUNT(*) as total_rows,
                    COUNT(Full_Y) as full_y_count,
                    COUNT(controller) as controller_count,
                    COUNT(midprice) as midprice_count,
                    MIN(Full_Y) as min_full_y,
                    MAX(Full_Y) as max_full_y,
                    AVG(Full_Y) as avg_full_y,
                    MAX(controller) as max_controller
                FROM {self.table_name}
            """)

            stats = self.cursor.fetchone()
            total_rows = stats[0]
            full_y_count = stats[1]
            controller_count = stats[2]
            midprice_count = stats[3]
            min_full_y = float(stats[4]) if stats[4] is not None else 0.0
            max_full_y = float(stats[5]) if stats[5] is not None else 0.0
            avg_full_y = float(stats[6]) if stats[6] is not None else 0.0
            max_controller = stats[7] if stats[7] is not None else 0

            print(f"📊 修复后统计:")
            print(f"   • 总记录数: {total_rows}")
            print(f"   • Full_Y非空记录: {full_y_count}")
            print(f"   • controller非空记录: {controller_count}")
            print(f"   • midprice非空记录: {midprice_count}")
            print(f"   • Full_Y范围: {min_full_y:.6f} ~ {max_full_y:.6f}")
            print(f"   • Full_Y平均值: {avg_full_y:.6f}")
            print(f"   • 最大controller值: {max_controller}")

            # 检查覆盖率
            full_y_coverage = (full_y_count / total_rows * 100) if total_rows > 0 else 0
            controller_coverage = (controller_count / total_rows * 100) if total_rows > 0 else 0

            print(f"   • Full_Y覆盖率: {full_y_coverage:.1f}%")
            print(f"   • controller覆盖率: {controller_coverage:.1f}%")

            # 显示前10行数据
            self.cursor.execute(f"""
                SELECT
                    ROW_NUMBER() OVER (ORDER BY date ASC) as row_num,
                    date,
                    close,
                    midprice,
                    CASE WHEN (close - midprice) > 0 THEN 'YES' ELSE 'NO' END as is_strong,
                    controller,
                    Full_Y
                FROM {self.table_name}
                WHERE Full_Y IS NOT NULL
                ORDER BY date ASC
                LIMIT 10
            """)

            results = self.cursor.fetchall()
            print(f"\n📊 前10行数据验证:")
            print("行号 | 日期          | 收盘价  | midprice | 强势? | controller | Full_Y")
            print("-" * 80)
            for row in results:
                row_num = row[0]
                date_str = str(row[1])
                close_val = float(row[2]) if row[2] is not None else 0.0
                mid_val = float(row[3]) if row[3] is not None else 0.0
                is_strong = row[4]
                controller_val = row[5] if row[5] is not None else 0
                full_y_val = float(row[6]) if row[6] is not None else 0.0
                print(f"{row_num:4d} | {date_str} | {close_val:7.2f} | {mid_val:8.4f} | {is_strong:5s} | {controller_val:10d} | {full_y_val:10.6f}")

            # 显示最新10行数据
            self.cursor.execute(f"""
                SELECT
                    date,
                    close,
                    midprice,
                    CASE WHEN (close - midprice) > 0 THEN 'STRONG' ELSE 'WEAK' END as status,
                    controller,
                    Full_Y
                FROM {self.table_name}
                WHERE Full_Y IS NOT NULL
                ORDER BY date DESC
                LIMIT 10
            """)

            latest_results = self.cursor.fetchall()
            print(f"\n📊 最新10条数据:")
            print("日期          | 收盘价  | midprice | 状态   | controller | Full_Y")
            print("-" * 70)
            for row in latest_results:
                date_str = str(row[0])
                close_val = float(row[1]) if row[1] is not None else 0.0
                mid_val = float(row[2]) if row[2] is not None else 0.0
                status = row[3]
                controller_val = row[4] if row[4] is not None else 0
                full_y_val = float(row[5]) if row[5] is not None else 0.0
                print(f"{date_str} | {close_val:7.2f} | {mid_val:8.4f} | {status:6s} | {controller_val:10d} | {full_y_val:10.6f}")

            # 验证计算逻辑
            if full_y_count > 0 and controller_count > 0:
                print("\n✅ Full_Y字段修复成功!")

                # 计算k值
                k_value = max_controller / total_rows if total_rows > 0 else 0
                print(f"📊 k值 (整体强势比例): {k_value:.6f}")

                return True
            else:
                print("\n❌ Full_Y字段修复失败!")
                return False

        except mysql.connector.Error as e:
            print(f"❌ 验证结果失败: {e}")
            return False

    def test_sp_updatecontroller(self):
        """测试sp_updatecontroller函数"""
        try:
            print(f"\n🧪 测试sp_updatecontroller函数...")

            # 调用sp_updatecontroller
            args = [self.table_name, 0]
            result = self.cursor.callproc('sp_updatecontroller', args)

            # 获取结果集
            print("📊 sp_updatecontroller执行过程:")
            for result_set in self.cursor.stored_results():
                rows = result_set.fetchall()
                for row in rows:
                    print(f"   {row}")

            # 显示OUT参数结果
            k_value = result[1]
            print(f"📊 返回的k值: {k_value}")

            return True

        except mysql.connector.Error as e:
            print(f"❌ 测试sp_updatecontroller失败: {e}")
            return False

    def close_connection(self):
        """关闭数据库连接"""
        if self.cursor:
            self.cursor.close()
        if self.connection:
            self.connection.close()
        print("🔒 数据库连接已关闭")

    def run(self):
        """执行主流程"""
        print("🎯 修复stock_2318_hk表的Full_Y字段")
        print("=" * 60)
        print(f"📅 执行时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"🏠 数据库: {self.db_config['host']}/{self.db_config['database']}")
        print(f"📊 目标表: {self.table_name}")

        try:
            # 1. 连接数据库
            if not self.connect_database():
                return False

            # 2. 检查表状态
            if not self.check_table_status():
                return False

            # 3. 修复缺失字段
            if not self.fix_missing_fields():
                return False

            # 4. 更新midprice
            if not self.update_midprice():
                return False

            # 5. 计算controller和Full_Y
            if not self.calculate_controller_and_full_y():
                return False

            # 6. 验证结果
            if not self.verify_results():
                return False

            # 7. 测试sp_updatecontroller
            self.test_sp_updatecontroller()

            print("\n🎉 stock_2318_hk表的Full_Y字段修复完成!")
            print("💡 现在可以正常使用sp_updatecontroller函数")

            return True

        except Exception as e:
            print(f"❌ 执行失败: {e}")
            return False

        finally:
            self.close_connection()

def main():
    """主函数"""
    fixer = FullYFixer()
    success = fixer.run()

    if success:
        print("\n✅ 修复完成!")
        print("📝 使用方法: CALL sp_updatecontroller('stock_2318_hk', @k_value);")
    else:
        print("\n❌ 修复失败!")

if __name__ == "__main__":
    main()
