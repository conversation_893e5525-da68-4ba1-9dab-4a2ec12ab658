#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
HSI50正确止盈止损回测系统 - 30K资金20年历史
==========================================

正确的止盈止损逻辑：
做多（买涨）策略：
- 止盈：当价格上涨超过take_profit时，exit_price = price * (1 + take_profit)
- 止损：当价格下跌超过stop_loss时，exit_price = price * (1 - stop_loss)

做空（买跌）策略：
- 止盈：当价格下跌超过take_profit时，exit_price = price * (1 - take_profit)
- 止损：当价格上涨超过stop_loss时，exit_price = price * (1 + stop_loss)

作者: Cosmoon NG
日期: 2025年7月
"""

import pandas as pd
import yfinance as yf
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

class CorrectStopProfitHSI50:
    def __init__(self):
        """初始化正确止盈止损HSI50回测系统"""
        self.symbol = "^HSI"  # 恒生指数
        self.initial_capital = 30000  # 30K港币
        self.data = None
        
        # 策略参数
        self.strategy_params = {
            # Cosmoon博弈论参数
            'high_profit_y': 0.5,           # 高值盈利区Y阈值
            'high_profit_x': 0.5,           # 高值盈利区X阈值
            'control_zone_min': 0.333,      # 控股商控制区下限
            'control_zone_max': 0.4,        # 控股商控制区上限
            'strong_loss_y': 0.25,          # 强亏损区Y阈值
            'strong_loss_x': 0.25,          # 强亏损区X阈值
            
            # 止盈止损参数 - 1:2赔率
            'take_profit': 0.02,            # 止盈2%
            'stop_loss': 0.01,              # 止损1%
            
            # 凯利公式参数
            'kelly_win_rate': 0.67,         # 胜率预期 (基于1:2赔率)
            'kelly_odds_ratio': 2.0,        # 赔率1:2
            'max_position_ratio': 0.25,     # 最大仓位25%
            'max_total_positions': 2,       # 最多同时持有2个仓位
            
            # 交易参数
            'transaction_cost': 30,         # 每笔交易成本30港币
            'point_value': 50,              # 每个恒指点子价值50港元
            'compound_interest': True,      # 复利计算
        }
        
        self.trades = []
        self.daily_portfolio = []
        self.current_positions = []
        self.current_capital = self.initial_capital
    
    def fetch_hsi_data(self):
        """获取恒生指数20年历史数据"""
        print("📈 获取恒生指数20年历史数据...")
        
        try:
            # 获取20年数据
            end_date = datetime.now()
            start_date = end_date - timedelta(days=20*365)
            
            ticker = yf.Ticker(self.symbol)
            self.data = ticker.history(start=start_date, end=end_date)
            
            if self.data.empty:
                print("❌ 数据获取失败：返回空数据")
                return False
            
            self.data.reset_index(inplace=True)
            self.data.columns = [col.lower() for col in self.data.columns]
            
            print(f"✅ 成功获取恒生指数数据:")
            print(f"   • 数据期间: {self.data['date'].min().strftime('%Y-%m-%d')} 至 {self.data['date'].max().strftime('%Y-%m-%d')}")
            print(f"   • 总记录数: {len(self.data):,} 天")
            
            return True
            
        except Exception as e:
            print(f"❌ 获取数据失败: {e}")
            return False
    
    def calculate_indicators(self):
        """计算技术指标和策略参数"""
        print("📊 计算技术指标和策略参数...")
        
        # 移动平均线
        self.data['ma_20'] = self.data['close'].rolling(window=20).mean()
        self.data['ma_60'] = self.data['close'].rolling(window=60).mean()
        
        # RSI
        delta = self.data['close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
        rs = gain / loss
        self.data['rsi'] = 100 - (100 / (1 + rs))
        
        # 成交量指标
        self.data['volume_ma'] = self.data['volume'].rolling(window=20).mean()
        self.data['volume_ratio'] = self.data['volume'] / self.data['volume_ma']
        
        # Y值计算 (控制系数)
        price_vs_ma20 = self.data['close'] / self.data['ma_20']
        base_y = np.where(price_vs_ma20 >= 1, 
                         0.5 + 0.4 * np.tanh((price_vs_ma20 - 1) * 3),
                         0.5 - 0.4 * np.tanh((1 - price_vs_ma20) * 3))
        
        # 趋势调整
        ma_trend = (self.data['ma_20'] / self.data['ma_60']).fillna(1)
        trend_adjustment = 0.1 * np.tanh((ma_trend - 1) * 2)
        
        # 成交量调整
        volume_adjustment = 0.05 * np.tanh((self.data['volume_ratio'] - 1))
        
        self.data['y_probability'] = base_y + trend_adjustment + volume_adjustment
        self.data['y_probability'] = np.clip(self.data['y_probability'], 0.1, 0.9)
        self.data['y_probability'].fillna(0.5, inplace=True)
        
        # X值计算 (资金流比例)
        price_change = (self.data['close'] - self.data['open']) / self.data['open']
        money_flow = self.data['volume'] * price_change
        
        def calc_inflow_ratio(flows):
            if len(flows) == 0 or flows.isna().all():
                return 0.5
            flows = flows.dropna()
            if len(flows) == 0:
                return 0.5
            
            inflows = flows[flows > 0].sum()
            outflows = abs(flows[flows < 0].sum())
            total_flow = inflows + outflows
            
            return inflows / total_flow if total_flow > 0 else 0.5
        
        base_x = money_flow.rolling(window=20).apply(calc_inflow_ratio, raw=False)
        rsi_adjustment = 0.3 * (self.data['rsi'] / 100 - 0.5)
        
        self.data['inflow_ratio'] = base_x + rsi_adjustment
        self.data['inflow_ratio'] = np.clip(self.data['inflow_ratio'], 0.1, 0.9)
        self.data['inflow_ratio'].fillna(0.5, inplace=True)
        
        # E值计算 (Cosmoon博弈论核心公式)
        self.data['e_value'] = (8 * self.data['inflow_ratio'] * self.data['y_probability'] - 
                               3 * self.data['inflow_ratio'] - 3 * self.data['y_probability'] + 1)
        
        print("✅ 指标计算完成")
    
    def calculate_kelly_position(self, win_rate_modifier=1.0):
        """计算凯利公式仓位"""
        win_rate = self.strategy_params['kelly_win_rate'] * win_rate_modifier
        odds_ratio = self.strategy_params['kelly_odds_ratio']
        
        # 凯利公式: f = (bp - q) / b
        # 其中 b = 赔率, p = 胜率, q = 败率
        b = odds_ratio
        p = win_rate
        q = 1 - p
        
        kelly_fraction = (b * p - q) / b
        
        # 限制最大仓位
        return max(0, min(kelly_fraction, self.strategy_params['max_position_ratio']))
    
    def classify_strategy_zone(self, y_val, x_val, e_val):
        """分类策略区域"""
        if (y_val > self.strategy_params['high_profit_y'] and 
            x_val > self.strategy_params['high_profit_x'] and
            e_val > 0):
            return 'HIGH_PROFIT'
        elif (self.strategy_params['control_zone_min'] < y_val < self.strategy_params['control_zone_max']):
            return 'CONTROL'
        elif (y_val < self.strategy_params['strong_loss_y'] or 
              x_val < self.strategy_params['strong_loss_x']) and e_val < 0:
            return 'STRONG_LOSS'
        else:
            return 'OTHER'
    
    def check_stop_conditions(self, position, current_price):
        """检查止盈止损 - 正确的计算方式"""
        entry_price = position['entry_price']
        direction = position['direction']
        take_profit = self.strategy_params['take_profit']
        stop_loss = self.strategy_params['stop_loss']
        
        if direction == 'LONG':
            # 做多（买涨）策略
            # 止盈：当价格上涨超过take_profit时
            take_profit_price = entry_price * (1 + take_profit)
            # 止损：当价格下跌超过stop_loss时
            stop_loss_price = entry_price * (1 - stop_loss)
            
            if current_price >= take_profit_price:
                return True, '止盈', take_profit_price, (take_profit_price - entry_price) / entry_price
            elif current_price <= stop_loss_price:
                return True, '止损', stop_loss_price, (stop_loss_price - entry_price) / entry_price
        
        else:  # SHORT
            # 做空（买跌）策略
            # 止盈：当价格下跌超过take_profit时
            take_profit_price = entry_price * (1 - take_profit)
            # 止损：当价格上涨超过stop_loss时
            stop_loss_price = entry_price * (1 + stop_loss)
            
            if current_price <= take_profit_price:
                return True, '止盈', take_profit_price, (entry_price - take_profit_price) / entry_price
            elif current_price >= stop_loss_price:
                return True, '止损', stop_loss_price, (entry_price - stop_loss_price) / entry_price
        
        return False, '', current_price, 0

    def execute_trade(self, date, price, direction, zone, kelly_modifier=1.0):
        """执行交易"""
        # 计算凯利公式仓位
        kelly_position = self.calculate_kelly_position(kelly_modifier)

        # 计算投资金额
        investment_amount = self.current_capital * kelly_position

        if investment_amount < self.strategy_params['transaction_cost'] * 2:
            return  # 资金不足

        # 计算股数 (以恒指点数为单位)
        shares = (investment_amount - self.strategy_params['transaction_cost']) / price

        # 创建持仓记录
        position = {
            'entry_date': date,
            'entry_price': price,
            'shares': shares,
            'direction': direction,
            'zone': zone,
            'investment': investment_amount
        }

        self.current_positions.append(position)

    def backtest_strategy(self):
        """执行正确止盈止损策略回测"""
        print("\n🚀 开始正确止盈止损HSI50回测...")
        print("="*60)
        print(f"💰 初始资金: {self.initial_capital:,} 港币")
        print(f"📊 策略: Cosmoon博弈论 + 正确止盈止损 + 凯利公式")
        print(f"📈 做多止盈: 价格 * (1 + {self.strategy_params['take_profit']*100}%) = 价格 * {1+self.strategy_params['take_profit']}")
        print(f"📉 做多止损: 价格 * (1 - {self.strategy_params['stop_loss']*100}%) = 价格 * {1-self.strategy_params['stop_loss']}")
        print(f"📈 做空止盈: 价格 * (1 - {self.strategy_params['take_profit']*100}%) = 价格 * {1-self.strategy_params['take_profit']}")
        print(f"📉 做空止损: 价格 * (1 + {self.strategy_params['stop_loss']*100}%) = 价格 * {1+self.strategy_params['stop_loss']}")
        print(f"🎲 凯利公式: 1:{self.strategy_params['kelly_odds_ratio']} 赔率")
        print(f"🔄 复利计算: {self.strategy_params['compound_interest']}")
        print("="*60)

        # 统计变量
        total_trades = 0
        winning_trades = 0
        losing_trades = 0

        # 跳过前60天用于指标计算
        for i in range(60, len(self.data)):
            row = self.data.iloc[i]
            date = row['date']
            price = row['close']
            y_val = row['y_probability']
            x_val = row['inflow_ratio']
            e_val = row['e_value']

            # 分类策略区域
            zone = self.classify_strategy_zone(y_val, x_val, e_val)

            # 检查现有持仓的止盈止损
            positions_to_close = []
            for j, position in enumerate(self.current_positions):
                should_exit, exit_reason, exit_price, profit_pct = self.check_stop_conditions(position, price)

                if should_exit:
                    # 计算实际盈亏
                    if position['direction'] == 'LONG':
                        price_diff = exit_price - position['entry_price']
                    else:  # SHORT
                        price_diff = position['entry_price'] - exit_price

                    gross_profit = price_diff * position['shares']
                    net_profit = gross_profit - self.strategy_params['transaction_cost']

                    # 复利计算
                    if self.strategy_params['compound_interest']:
                        self.current_capital += net_profit

                    # 记录交易
                    trade_record = {
                        'entry_date': position['entry_date'].strftime('%Y-%m-%d'),
                        'exit_date': date.strftime('%Y-%m-%d'),
                        'direction': position['direction'],
                        'zone': position['zone'],
                        'entry_price': round(position['entry_price'], 2),
                        'exit_price': round(exit_price, 2),
                        'current_price': round(price, 2),
                        'shares': round(position['shares'], 2),
                        'investment': round(position['investment'], 2),
                        'gross_profit': round(gross_profit, 2),
                        'transaction_cost': self.strategy_params['transaction_cost'],
                        'net_profit': round(net_profit, 2),
                        'profit_pct': round(profit_pct * 100, 2),
                        'capital_after': round(self.current_capital, 2),
                        'y_value': round(y_val, 3),
                        'x_value': round(x_val, 3),
                        'e_value': round(e_val, 3),
                        'exit_reason': exit_reason
                    }

                    self.trades.append(trade_record)

                    if net_profit > 0:
                        winning_trades += 1
                    else:
                        losing_trades += 1

                    total_trades += 1
                    positions_to_close.append(j)

            # 移除已平仓的持仓
            for j in sorted(positions_to_close, reverse=True):
                del self.current_positions[j]

            # 根据策略区域决定开仓
            current_position_count = len(self.current_positions)

            if (zone == 'HIGH_PROFIT' and
                current_position_count < self.strategy_params['max_total_positions']):
                # 高值盈利区：买涨
                self.execute_trade(date, price, 'LONG', zone, kelly_modifier=1.0)

            elif (zone == 'STRONG_LOSS' and
                  current_position_count < self.strategy_params['max_total_positions']):
                # 强亏损区：买跌
                self.execute_trade(date, price, 'SHORT', zone, kelly_modifier=0.8)

            elif (zone == 'OTHER' and
                  current_position_count < self.strategy_params['max_total_positions'] and
                  e_val < -0.1):  # 只在E值较低时买跌
                # 其他区域：买跌
                self.execute_trade(date, price, 'SHORT', zone, kelly_modifier=0.6)

            # 控股商控制区：观望，不开新仓

            # 记录每日组合价值
            position_value = 0
            for position in self.current_positions:
                if position['direction'] == 'LONG':
                    unrealized_profit = (price - position['entry_price']) * position['shares']
                else:  # SHORT
                    unrealized_profit = (position['entry_price'] - price) * position['shares']
                position_value += position['investment'] + unrealized_profit

            total_value = self.current_capital + position_value

            self.daily_portfolio.append({
                'date': date.strftime('%Y-%m-%d'),
                'price': price,
                'capital': self.current_capital,
                'position_value': position_value,
                'total_value': total_value,
                'y_value': y_val,
                'x_value': x_val,
                'e_value': e_val,
                'zone': zone,
                'positions_count': len(self.current_positions)
            })

        print(f"\n✅ 正确止盈止损回测完成!")
        print(f"📊 总交易次数: {total_trades}")
        print(f"📈 盈利次数: {winning_trades}")
        print(f"📉 亏损次数: {losing_trades}")
        if total_trades > 0:
            print(f"🎯 胜率: {winning_trades/total_trades*100:.1f}%")

        return pd.DataFrame(self.trades), pd.DataFrame(self.daily_portfolio)

    def analyze_results(self, trades_df, daily_df):
        """分析回测结果"""
        print("\n📊 正确止盈止损HSI50回测结果分析")
        print("=" * 60)

        # 基本统计
        total_trades = len(trades_df)
        final_capital = self.current_capital
        final_total_value = daily_df['total_value'].iloc[-1] if len(daily_df) > 0 else self.initial_capital
        total_return = final_capital - self.initial_capital
        total_return_rate = (total_return / self.initial_capital) * 100

        # 年化收益率
        years = 20
        annual_return_rate = ((final_capital / self.initial_capital) ** (1/years) - 1) * 100

        if total_trades > 0:
            winning_trades = len(trades_df[trades_df['net_profit'] > 0])
            win_rate = winning_trades / total_trades * 100
            max_profit = trades_df['net_profit'].max()
            max_loss = trades_df['net_profit'].min()
            avg_profit = trades_df['net_profit'].mean()
            total_trading_profit = trades_df['net_profit'].sum()

            # 按区域分析
            zone_stats = trades_df.groupby('zone').agg({
                'net_profit': ['count', 'sum', 'mean'],
                'profit_pct': 'mean'
            }).round(2)

            # 按方向分析
            direction_stats = trades_df.groupby('direction').agg({
                'net_profit': ['count', 'sum', 'mean'],
                'profit_pct': 'mean'
            }).round(2)

            # 按退出原因分析
            exit_reason_stats = trades_df.groupby('exit_reason').agg({
                'net_profit': ['count', 'sum', 'mean'],
                'profit_pct': 'mean'
            }).round(2)
        else:
            winning_trades = 0
            win_rate = 0
            max_profit = 0
            max_loss = 0
            avg_profit = 0
            total_trading_profit = 0
            zone_stats = pd.DataFrame()
            direction_stats = pd.DataFrame()
            exit_reason_stats = pd.DataFrame()

        # 输出结果
        print(f"💰 资金统计:")
        print(f"• 初始资金: {self.initial_capital:,} 港元")
        print(f"• 最终资金: {final_capital:,.0f} 港元")
        print(f"• 最终总价值: {final_total_value:,.0f} 港元")
        print(f"• 总收益: {total_return:,.0f} 港元")
        print(f"• 总收益率: {total_return_rate:.2f}%")
        print(f"• 年化收益率: {annual_return_rate:.2f}%")

        if total_trades > 0:
            print(f"\n📈 交易统计:")
            print(f"• 总交易次数: {total_trades}")
            print(f"• 盈利次数: {winning_trades}")
            print(f"• 胜率: {win_rate:.1f}%")
            print(f"• 最大单笔盈利: {max_profit:,.0f} 港元")
            print(f"• 最大单笔亏损: {max_loss:,.0f} 港元")
            print(f"• 平均每笔盈亏: {avg_profit:,.0f} 港元")
            print(f"• 交易总盈亏: {total_trading_profit:,.0f} 港元")

            print(f"\n📊 区域分析:")
            print(zone_stats)

            print(f"\n📊 方向分析:")
            print(direction_stats)

            print(f"\n📊 退出原因分析:")
            print(exit_reason_stats)

            # 止盈止损效果验证
            take_profit_trades = len(trades_df[trades_df['exit_reason'] == '止盈'])
            stop_loss_trades = len(trades_df[trades_df['exit_reason'] == '止损'])

            print(f"\n🎯 止盈止损效果:")
            print(f"• 止盈次数: {take_profit_trades} ({take_profit_trades/total_trades*100:.1f}%)")
            print(f"• 止损次数: {stop_loss_trades} ({stop_loss_trades/total_trades*100:.1f}%)")

            if take_profit_trades > 0:
                avg_take_profit = trades_df[trades_df['exit_reason'] == '止盈']['profit_pct'].mean()
                print(f"• 平均止盈收益率: {avg_take_profit:.2f}%")

            if stop_loss_trades > 0:
                avg_stop_loss = trades_df[trades_df['exit_reason'] == '止损']['profit_pct'].mean()
                print(f"• 平均止损亏损率: {avg_stop_loss:.2f}%")

            # 凯利公式验证
            actual_win_rate = win_rate / 100
            actual_odds_ratio = abs(max_profit / max_loss) if max_loss != 0 else 2

            print(f"\n🎲 凯利公式验证:")
            print(f"• 实际胜率: {actual_win_rate:.3f}")
            print(f"• 实际赔率: 1:{actual_odds_ratio:.2f}")
            print(f"• 预设胜率: {self.strategy_params['kelly_win_rate']:.3f}")
            print(f"• 预设赔率: 1:{self.strategy_params['kelly_odds_ratio']:.2f}")

        # 保存到Excel
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"HSI50正确止盈止损回测结果_{timestamp}.xlsx"

        with pd.ExcelWriter(filename, engine='openpyxl') as writer:
            if len(trades_df) > 0:
                trades_df.to_excel(writer, sheet_name='交易记录', index=False)
            daily_df.to_excel(writer, sheet_name='每日数据', index=False)

            # 汇总统计
            summary_data = {
                '项目': ['初始资金(港元)', '最终资金(港元)', '最终总价值(港元)',
                        '总收益(港元)', '总收益率(%)', '年化收益率(%)',
                        '总交易次数', '盈利次数', '胜率(%)',
                        '止盈次数', '止损次数',
                        '最大单笔盈利(港元)', '最大单笔亏损(港元)', '平均每笔盈亏(港元)'],
                '数值': [self.initial_capital, round(final_capital, 0), round(final_total_value, 0),
                        round(total_return, 0), round(total_return_rate, 2), round(annual_return_rate, 2),
                        total_trades, winning_trades, round(win_rate, 1),
                        take_profit_trades if total_trades > 0 else 0,
                        stop_loss_trades if total_trades > 0 else 0,
                        round(max_profit, 0) if total_trades > 0 else 0,
                        round(max_loss, 0) if total_trades > 0 else 0,
                        round(avg_profit, 0) if total_trades > 0 else 0]
            }
            pd.DataFrame(summary_data).to_excel(writer, sheet_name='汇总统计', index=False)

            if len(zone_stats) > 0:
                zone_stats.to_excel(writer, sheet_name='区域分析')
            if len(direction_stats) > 0:
                direction_stats.to_excel(writer, sheet_name='方向分析')
            if len(exit_reason_stats) > 0:
                exit_reason_stats.to_excel(writer, sheet_name='退出原因分析')

        print(f"\n✅ 详细结果已保存至: {filename}")
        return filename

def main():
    """主函数"""
    print("🏢 HSI50正确止盈止损回测系统")
    print("=" * 60)
    print("💰 总资金: 30,000港元")
    print("📊 分析周期: 20年历史数据")
    print("🎯 策略: Cosmoon博弈论 + 正确止盈止损 + 凯利公式")
    print("📈 做多策略:")
    print("   • 止盈: 价格 * (1 + 2%) = 价格 * 1.02")
    print("   • 止损: 价格 * (1 - 1%) = 价格 * 0.99")
    print("📉 做空策略:")
    print("   • 止盈: 价格 * (1 - 2%) = 价格 * 0.98")
    print("   • 止损: 价格 * (1 + 1%) = 价格 * 1.01")
    print("🎲 凯利公式: 1:2赔率, 最大仓位25%")
    print("🔄 复利计算: 启用")

    # 创建回测器
    backtester = CorrectStopProfitHSI50()

    # 获取数据
    if not backtester.fetch_hsi_data():
        return

    # 计算指标
    backtester.calculate_indicators()

    # 执行回测
    trades_df, daily_df = backtester.backtest_strategy()

    # 分析结果
    backtester.analyze_results(trades_df, daily_df)

if __name__ == "__main__":
    main()
