#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于散户情绪的XY策略分析报告
==========================
对比原始策略与基于散户行为的优化策略
"""

import pandas as pd
import numpy as np
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

def analyze_retail_sentiment_strategy():
    """分析基于散户情绪的策略"""
    
    print("📊 基于散户情绪的XY策略深度分析")
    print("=" * 80)
    print(f"📅 分析时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 策略对比数据
    strategy_comparison = {
        '策略版本': [
            '原始XY策略',
            '散户情绪策略'
        ],
        '策略逻辑': [
            'Y>0.43且X>0.43看涨',
            '散户情绪逆向+控股商跟随'
        ],
        '最终资金': [
            '3,007,115港元',
            '1,077,002港元'
        ],
        '年化收益率': [
            '5.40%',
            '2.40%'
        ],
        '最大回撤': [
            '9.32%',
            '6.46%'
        ],
        '总交易次数': [
            '5,049笔',
            '2,765笔'
        ],
        '看涨交易': [
            '5,049笔 (100%)',
            '2,542笔 (91.9%)'
        ],
        '看跌交易': [
            '0笔 (0%)',
            '223笔 (8.1%)'
        ],
        '胜率': [
            '40.4%',
            '41.0%'
        ]
    }
    
    df_comparison = pd.DataFrame(strategy_comparison)
    print(f"\n📊 策略对比总览:")
    print(df_comparison.to_string(index=False))
    
    # 详细分析
    print(f"\n🔍 详细分析:")
    
    print(f"\n   1. 📈 收益率对比:")
    print(f"      • 原始策略: 5.40%年化收益率，35年增长100倍")
    print(f"      • 散户策略: 2.40%年化收益率，35年增长2.3倍")
    print(f"      • 收益差距: 原始策略收益率是散户策略的2.25倍")
    print(f"      • 结论: 原始策略在收益方面明显优于散户策略")
    
    print(f"\n   2. 🛡️ 风险控制对比:")
    print(f"      • 原始策略: 9.32%最大回撤")
    print(f"      • 散户策略: 6.46%最大回撤")
    print(f"      • 风险优势: 散户策略回撤降低30.7%")
    print(f"      • 结论: 散户策略在风险控制方面更优秀")
    
    print(f"\n   3. 📊 交易特征对比:")
    print(f"      • 原始策略: 5,049笔交易，100%看涨")
    print(f"      • 散户策略: 2,765笔交易，91.9%看涨，8.1%看跌")
    print(f"      • 交易频率: 散户策略减少45.2%的交易次数")
    print(f"      • 多空平衡: 散户策略实现了一定的多空平衡")
    
    print(f"\n   4. ✅ 胜率对比:")
    print(f"      • 原始策略: 40.4%总胜率")
    print(f"      • 散户策略: 41.0%总胜率，看跌60.5%胜率")
    print(f"      • 结论: 散户策略胜率略高，看跌策略表现优秀")
    
    # 散户情绪策略的核心逻辑分析
    print(f"\n🎯 散户情绪策略核心逻辑:")
    
    print(f"\n   💡 策略理念:")
    print(f"   • X = 散户买升概率 (基于价格位置、动量、成交量等)")
    print(f"   • 1-X = 散户买跌概率")
    print(f"   • Y = 控股商控制比例")
    print(f"   • 核心思想: 散户情绪逆向 + 控股商跟随")
    
    print(f"\n   📈 具体策略:")
    print(f"   1. 散户过度买升时做空 (X>0.60)")
    print(f"      • 逻辑: 散户追高往往是顶部信号")
    print(f"      • 实际执行: 223笔看跌交易，60.5%胜率")
    
    print(f"   2. 散户过度买跌时做多 (1-X>0.60)")
    print(f"      • 逻辑: 散户恐慌往往是底部信号")
    print(f"      • 实际执行: 部分看涨交易来源于此")
    
    print(f"   3. 控股商强势时跟随 (Y>0.55且X<0.50)")
    print(f"      • 逻辑: 控股商主导时跟随其方向")
    print(f"      • 实际执行: 大部分看涨交易来源于此")
    
    # 散户情绪分布分析
    print(f"\n📊 散户情绪分布分析:")
    
    print(f"   35年散户情绪统计:")
    print(f"   • 散户过度买升: 135天 (1.5%) - 极少出现")
    print(f"   • 散户过度买跌: 3,631天 (41.4%) - 经常出现")
    print(f"   • 散户情绪中性: 5,006天 (57.1%) - 大部分时间")
    
    print(f"\n   💡 关键发现:")
    print(f"   • 散户在41.4%的时间里倾向于买跌")
    print(f"   • 散户很少过度买升 (仅1.5%时间)")
    print(f"   • 这解释了为什么看跌交易相对较少")
    
    # 策略优缺点分析
    print(f"\n⚖️ 两种策略优缺点对比:")
    
    print(f"\n   🎯 原始XY策略:")
    print(f"   ✅ 优点:")
    print(f"      • 收益率高 (5.40%年化)")
    print(f"      • 逻辑简单，易于执行")
    print(f"      • 35年验证，稳定可靠")
    print(f"      • 交易频率高，充分利用市场机会")
    
    print(f"   ⚠️ 缺点:")
    print(f"      • 100%看涨，缺乏多空平衡")
    print(f"      • 最大回撤较高 (9.32%)")
    print(f"      • 过度依赖市场长期上涨")
    
    print(f"\n   🎯 散户情绪策略:")
    print(f"   ✅ 优点:")
    print(f"      • 风险控制更好 (6.46%回撤)")
    print(f"      • 实现多空平衡 (8.1%看跌)")
    print(f"      • 看跌胜率高 (60.5%)")
    print(f"      • 交易逻辑更符合市场心理学")
    
    print(f"   ⚠️ 缺点:")
    print(f"      • 收益率较低 (2.40%年化)")
    print(f"      • 策略复杂度较高")
    print(f"      • 交易机会减少45%")
    print(f"      • 看跌交易亏损较大")
    
    # 实盘应用建议
    print(f"\n🚀 实盘应用建议:")
    
    print(f"\n   💰 资金配置策略:")
    print(f"   方案1: 单一策略配置")
    print(f"   • 追求高收益: 100%原始XY策略")
    print(f"   • 追求低风险: 100%散户情绪策略")
    
    print(f"\n   方案2: 混合策略配置 (推荐)")
    print(f"   • 70%原始XY策略 + 30%散户情绪策略")
    print(f"   • 预期年化收益: 约4.5%")
    print(f"   • 预期最大回撤: 约8.0%")
    print(f"   • 实现收益与风险的平衡")
    
    print(f"\n   方案3: 动态切换策略")
    print(f"   • 牛市期间: 主要使用原始XY策略")
    print(f"   • 熊市期间: 主要使用散户情绪策略")
    print(f"   • 震荡市: 两种策略并用")
    
    # 进一步优化方向
    print(f"\n💡 进一步优化方向:")
    
    print(f"\n   1. 🎯 散户情绪策略优化:")
    print(f"      • 改进散户情绪计算公式")
    print(f"      • 增加更多技术指标")
    print(f"      • 优化止盈止损比例")
    print(f"      • 动态调整仓位大小")
    
    print(f"   2. 📊 原始策略优化:")
    print(f"      • 在明显高位增加止盈")
    print(f"      • 在极端情况下暂停交易")
    print(f"      • 结合宏观经济指标")
    
    print(f"   3. 🔄 混合策略优化:")
    print(f"      • 根据市场环境动态调整权重")
    print(f"      • 增加市场状态识别模块")
    print(f"      • 优化策略切换时机")
    
    # 风险提示
    print(f"\n⚠️ 重要风险提示:")
    
    print(f"\n   📊 散户情绪策略风险:")
    print(f"   • 收益率显著低于原始策略")
    print(f"   • 策略复杂度增加执行难度")
    print(f"   • 散户情绪计算可能存在偏差")
    print(f"   • 看跌交易平均亏损较大")
    
    print(f"\n   💰 资金管理建议:")
    print(f"   • 不要投入超过承受能力的资金")
    print(f"   • 定期评估策略表现")
    print(f"   • 保持充足的现金储备")
    print(f"   • 分散投资，避免过度集中")
    
    # 总结
    print(f"\n🎉 总结:")
    
    print(f"\n   🎯 核心结论:")
    print(f"   • 原始XY策略在收益方面表现更优")
    print(f"   • 散户情绪策略在风险控制方面更优")
    print(f"   • 两种策略各有优势，可以互补")
    print(f"   • 混合使用可能是最佳选择")
    
    print(f"\n   📊 适用人群:")
    print(f"   • 原始策略: 追求高收益的积极投资者")
    print(f"   • 散户策略: 风险厌恶的稳健投资者")
    print(f"   • 混合策略: 平衡型投资者")
    
    print(f"\n   💡 最终建议:")
    print(f"   基于35年回测数据，建议采用70%原始XY策略")
    print(f"   + 30%散户情绪策略的混合配置，既能获得")
    print(f"   较高收益，又能控制风险，实现最佳平衡。")

def calculate_mixed_strategy_performance():
    """计算混合策略表现"""
    
    print(f"\n📊 混合策略表现预估:")
    print(f"=" * 40)
    
    # 原始策略数据
    original_return = 0.054
    original_drawdown = 0.0932
    original_final = 3007115
    
    # 散户策略数据
    retail_return = 0.024
    retail_drawdown = 0.0646
    retail_final = 1077002
    
    # 混合策略 (70%原始 + 30%散户)
    mixed_weight_original = 0.7
    mixed_weight_retail = 0.3
    
    mixed_return = original_return * mixed_weight_original + retail_return * mixed_weight_retail
    mixed_drawdown = original_drawdown * mixed_weight_original + retail_drawdown * mixed_weight_retail
    mixed_final = original_final * mixed_weight_original + retail_final * mixed_weight_retail
    
    print(f"   混合策略配置: 70%原始策略 + 30%散户策略")
    print(f"   预期年化收益率: {mixed_return*100:.2f}%")
    print(f"   预期最大回撤: {mixed_drawdown*100:.2f}%")
    print(f"   预期最终资金: {mixed_final:,.0f}港元")
    print(f"   风险调整收益: {mixed_return/mixed_drawdown:.2f}")
    
    print(f"\n   💡 混合策略优势:")
    print(f"   • 收益率: 比散户策略高87.5%")
    print(f"   • 风险控制: 比原始策略回撤降低13.7%")
    print(f"   • 平衡性: 兼顾收益与风险")

def main():
    """主函数"""
    analyze_retail_sentiment_strategy()
    calculate_mixed_strategy_performance()
    
    print(f"\n🎉 基于散户情绪的XY策略分析完成！")
    print(f"   关键发现: 散户情绪策略成功实现了多空平衡，")
    print(f"   虽然收益率较低，但风险控制更优秀。")
    print(f"   建议采用混合策略以获得最佳平衡。")

if __name__ == "__main__":
    main()
