#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查test表列名
=============

检查test表的实际列名结构

作者: Cosmoon NG
日期: 2025年7月
"""

import pymysql

def check_test_columns():
    """检查test表列名"""
    db_config = {
        'host': 'localhost',
        'port': 3306,
        'database': 'finance',
        'user': 'root',
        'password': '12345678',
        'charset': 'utf8mb4'
    }
    
    try:
        connection = pymysql.connect(**db_config)
        cursor = connection.cursor()
        
        # 检查表结构
        cursor.execute("DESCRIBE test")
        columns = cursor.fetchall()
        
        print("📊 test表列结构:")
        print("="*50)
        for i, col in enumerate(columns, 1):
            print(f"{i:2d}. {col[0]} ({col[1]})")
        
        # 显示前3条数据
        cursor.execute("SELECT * FROM test LIMIT 3")
        sample_data = cursor.fetchall()
        
        print(f"\n📋 前3条数据示例:")
        print("="*50)
        column_names = [col[0] for col in columns]
        
        for i, row in enumerate(sample_data, 1):
            print(f"\n记录{i}:")
            for j, (col_name, value) in enumerate(zip(column_names, row)):
                print(f"  {col_name}: {value}")
        
        connection.close()
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")

if __name__ == "__main__":
    check_test_columns()
