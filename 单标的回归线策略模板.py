#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
单标的回归线策略模板
==================

使用方法：
1. 修改 SYMBOL 和 NAME 变量
2. 运行代码即可获得回测结果

作者: Cosmoon NG
"""

import pandas as pd
import yfinance as yf
import numpy as np
from scipy import stats
from datetime import datetime, timedelta

# ========== 配置区域 ==========
SYMBOL = "^HSI"  # 修改这里：股票代码
NAME = "恒生指数"  # 修改这里：股票名称
# =============================

class SimpleRegressionStrategy:
    def __init__(self):
        self.symbol = SYMBOL
        self.name = NAME
        self.initial_capital = 30000
        self.monthly_addition = 2000

        # 止盈止损参数
        self.take_profit_long = 0.016
        self.stop_loss_long = 0.008
        self.take_profit_short = 0.008
        self.stop_loss_short = 0.016

        self.position = 0
        self.entry_price = 0
        self.shares = 0
        self.trades = []
        self.equity_curve = []
        self.last_month = None

    def fetch_data(self):
        """获取数据"""
        print(f"📈 获取{self.name}数据...")
        end_date = datetime.now()
        start_date = end_date - timedelta(days=25*365)

        ticker = yf.Ticker(self.symbol)
        self.data = ticker.history(start=start_date, end=end_date)
        self.data.reset_index(inplace=True)
        self.data.columns = [col.lower() for col in self.data.columns]

        print(f"✅ 获取{len(self.data)}天数据")
        return len(self.data) > 0

    def calculate_indicators(self):
        """计算指标"""
        print("📊 计算技术指标...")

        # 回归线
        self.data['i'] = range(1, len(self.data) + 1)
        slope, intercept, r_value, _, _ = stats.linregress(self.data['i'], self.data['close'])
        self.data['regression_line'] = intercept + slope * self.data['i']
        self.data['price_position'] = (self.data['close'] - self.data['regression_line']) / self.data['regression_line']
        self.r_squared = r_value ** 2

        # RSI
        delta = self.data['close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(14).mean()
        self.data['rsi'] = 100 - (100 / (1 + gain/loss))

        # Y值
        momentum = self.data['close'].pct_change(10)
        self.data['y_value'] = np.clip((self.data['rsi']/100 + np.tanh(momentum*5) + 1)/2, 0.1, 0.9)

        # X值
        price_change = (self.data['close'] - self.data['open']) / self.data['open']
        money_flow = self.data['volume'] * price_change

        def inflow_ratio(flows):
            flows = flows.dropna()
            if len(flows) == 0: return 0.5
            inflows = flows[flows > 0].sum()
            outflows = abs(flows[flows < 0].sum())
            total = inflows + outflows
            return inflows/total if total > 0 else 0.5

        self.data['x_value'] = np.clip(money_flow.rolling(20).apply(inflow_ratio), 0.1, 0.9)
        self.data['x_value'].fillna(0.5, inplace=True)

        # E值
        self.data['e_value'] = (8 * self.data['x_value'] * self.data['y_value'] -
                               3 * self.data['x_value'] - 3 * self.data['y_value'] + 1)

        print(f"✅ 指标计算完成 (R² = {self.r_squared:.4f})")

    def get_signal(self, row):
        """获取交易信号"""
        if (row['e_value'] > 0 and row['x_value'] > 0.45 and
            row['y_value'] > 0.45 and row['price_position'] < 0):
            return 'LONG'
        elif ((row['y_value'] < 0.25 or row['x_value'] < 0.25) and
              row['price_position'] > 0):
            return 'SHORT'
        return 'HOLD'

    def backtest(self):
        """执行回测"""
        print(f"🚀 开始{self.name}回测...")

        capital = self.initial_capital
        total_trades = 0
        winning_trades = 0

        for i in range(60, len(self.data)):
            row = self.data.iloc[i]
            date = row['date']
            price = row['close']
            high = row['high']
            low = row['low']

            # 每月加资金
            current_month = date.replace(day=1)
            if self.last_month is None or current_month > self.last_month:
                self.last_month = current_month
                capital += self.monthly_addition

            # 检查退出
            if self.position != 0:
                should_exit = False
                exit_price = price

                if self.position == 1:  # 多头
                    if (high - self.entry_price) / self.entry_price >= self.take_profit_long:
                        should_exit, exit_price = True, self.entry_price * (1 + self.take_profit_long)
                    elif (self.entry_price - low) / self.entry_price >= self.stop_loss_long:
                        should_exit, exit_price = True, self.entry_price * (1 - self.stop_loss_long)

                elif self.position == -1:  # 空头
                    if (self.entry_price - low) / self.entry_price >= self.take_profit_short:
                        should_exit, exit_price = True, self.entry_price * (1 - self.take_profit_short)
                    elif (high - self.entry_price) / self.entry_price >= self.stop_loss_short:
                        should_exit, exit_price = True, self.entry_price * (1 + self.stop_loss_short)

                if should_exit:
                    if self.position == 1:
                        profit = (exit_price - self.entry_price) * self.shares
                        capital += self.shares * exit_price
                    else:
                        profit = (self.entry_price - exit_price) * self.shares
                        capital -= self.shares * exit_price

                    if profit > 0: winning_trades += 1
                    total_trades += 1
                    self.position = 0

            # 检查开仓
            if self.position == 0:
                signal = self.get_signal(row)
                if signal in ['LONG', 'SHORT']:
                    if self.symbol.endswith('.HK'):
                        self.shares = int(capital * 0.95 / price / 100) * 100
                    else:
                        self.shares = capital * 0.95 / price

                    if self.shares > 0:
                        if signal == 'LONG':
                            capital -= self.shares * price
                            self.position = 1
                        else:
                            capital += self.shares * price
                            self.position = -1
                        self.entry_price = price

            # 计算总价值
            if self.position == 1:
                total_value = capital + self.shares * price
            elif self.position == -1:
                total_value = capital + (self.entry_price - price) * self.shares
            else:
                total_value = capital

            self.equity_curve.append({
                'date': date.strftime('%Y-%m-%d'),
                'total_value': total_value,
                'price': price
            })

        self.final_value = self.equity_curve[-1]['total_value']

        # 分析结果
        equity_df = pd.DataFrame(self.equity_curve)
        total_invested = self.initial_capital + len(equity_df) // 30 * self.monthly_addition
        net_return = self.final_value - total_invested
        net_return_rate = net_return / total_invested * 100

        start_date = pd.to_datetime(equity_df['date'].iloc[0])
        end_date = pd.to_datetime(equity_df['date'].iloc[-1])
        years = (end_date - start_date).days / 365
        annual_return = ((self.final_value / self.initial_capital) ** (1/years) - 1) * 100

        print(f"\n📊 {self.name}回测结果:")
        print(f"• 总投入: {total_invested:,} 港币")
        print(f"• 最终价值: {self.final_value:,.0f} 港币")
        print(f"• 净收益: {net_return:,.0f} 港币")
        print(f"• 净收益率: {net_return_rate:.2f}%")
        print(f"• 年化收益率: {annual_return:.2f}%")
        print(f"• 回归线R²: {self.r_squared:.4f}")
        print(f"• 交易次数: {total_trades}")
        if total_trades > 0:
            print(f"• 胜率: {winning_trades/total_trades*100:.1f}%")

def main():
    strategy = SimpleRegressionStrategy()
    if strategy.fetch_data():
        strategy.calculate_indicators()
        strategy.backtest()

if __name__ == "__main__":
    main()
