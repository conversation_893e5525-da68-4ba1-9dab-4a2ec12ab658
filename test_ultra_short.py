#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
超短线参数测试
测试新的风险回报比参数效果
"""

def test_ultra_short_parameters():
    """测试超短线参数的计算效果"""
    
    print("🎯 超短线风险回报比参数测试")
    print("="*50)
    
    # 新的超短线参数
    stop_loss = 0.0006    # 止损 0.06%
    take_profit = 0.0012  # 止盈 0.12%
    
    print(f"止损比例: {stop_loss*100:.2f}%")
    print(f"止盈比例: {take_profit*100:.2f}%")
    print(f"风险回报比: {take_profit/stop_loss:.1f}:1")
    
    # 测试价格示例
    test_prices = [10.00, 12.40, 15.50]
    
    print(f"\n📊 价格计算示例:")
    print("-"*60)
    print(f"{'价格':<8} {'做多止盈':<10} {'做多止损':<10} {'做空止盈':<10} {'做空止损':<10}")
    print("-"*60)
    
    for price in test_prices:
        long_tp = price * (1 + take_profit)
        long_sl = price * (1 - stop_loss)
        short_tp = price * (1 - take_profit)
        short_sl = price * (1 + stop_loss)
        
        print(f"{price:<8.2f} {long_tp:<10.3f} {long_sl:<10.3f} {short_tp:<10.3f} {short_sl:<10.3f}")
    
    print("-"*60)
    
    # 分析超短线特点
    print(f"\n💡 超短线参数特点:")
    print(f"   • 止盈止损区间很小，适合高频交易")
    print(f"   • 对价格波动非常敏感")
    print(f"   • 需要更精确的入场时机")
    print(f"   • 单笔盈亏较小，但交易频率高")
    
    # 计算不同价格下的盈亏金额 (假设2500资金)
    capital = 2500
    print(f"\n💰 基于{capital}HK资金的盈亏计算:")
    print("-"*40)
    
    for price in test_prices:
        profit_amount = capital * take_profit
        loss_amount = capital * stop_loss
        
        print(f"价格 {price:.2f}:")
        print(f"   止盈收益: +{profit_amount:.2f} HK ({take_profit*100:.2f}%)")
        print(f"   止损亏损: -{loss_amount:.2f} HK ({stop_loss*100:.2f}%)")
        print(f"   盈亏比: {profit_amount/loss_amount:.1f}:1")
        print()
    
    # 与之前参数对比
    old_stop_loss = 0.006
    old_take_profit = 0.012
    
    print(f"📈 参数对比:")
    print(f"   旧参数: 止损{old_stop_loss*100:.1f}%, 止盈{old_take_profit*100:.1f}%")
    print(f"   新参数: 止损{stop_loss*100:.2f}%, 止盈{take_profit*100:.2f}%")
    print(f"   敏感度提升: {old_stop_loss/stop_loss:.0f}倍")
    
    # 预期影响
    print(f"\n🔮 预期影响:")
    print(f"   ✅ 交易频率可能大幅增加")
    print(f"   ✅ 单笔风险显著降低")
    print(f"   ⚠️ 需要更精确的信号")
    print(f"   ⚠️ 手续费影响相对增大")

if __name__ == "__main__":
    test_ultra_short_parameters()
