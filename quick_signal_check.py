#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速信号检查器 - 简洁版
快速获取东亚银行(0023.HK)的买入信号和价格
"""

import yfinance as yf
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

def get_quick_signal():
    """快速获取交易信号"""
    ticker = '0023.HK'
    
    try:
        # 获取数据
        end_date = datetime.now()
        start_date = end_date - timedelta(days=100)
        data = yf.download(ticker, start=start_date, end=end_date, progress=False)
        
        if data.empty:
            return "❌ 数据获取失败"
        
        # 处理数据
        if isinstance(data.columns, pd.MultiIndex):
            data.columns = [col[0] for col in data.columns]
        
        data = data.rename(columns={
            'Open': 'open', 'High': 'high', 'Low': 'low', 
            'Close': 'close', 'Volume': 'volume'
        })
        
        # 计算指标
        data = calculate_quick_indicators(data)
        latest = data.iloc[-1]
        
        # 获取关键值
        current_price = latest['close']
        x_val = latest['money_flow_ratio']
        y_val = latest['full_y']
        e_val = latest['e_value']
        price_pos = latest['price_position']
        regression_price = latest['regression_line']
        
        # 判断信号
        signal_result = determine_quick_signal(current_price, x_val, y_val, e_val, price_pos, regression_price)
        
        return signal_result
        
    except Exception as e:
        return f"❌ 分析失败: {e}"

def calculate_quick_indicators(df):
    """快速计算指标"""
    # 回归线
    window = 60
    df['regression_line'] = df['close'].rolling(window=window).apply(
        lambda x: np.polyval(np.polyfit(range(len(x)), x, 1), len(x)-1), raw=False
    )
    
    # RSI
    delta = df['close'].diff()
    gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
    rs = gain / loss
    df['rsi'] = 100 - (100 / (1 + rs))
    
    # 资金流比例 (X值)
    price_change = (df['close'] - df['open']) / df['open']
    money_flow = df['volume'] * price_change
    
    def calc_inflow_ratio(flows):
        if len(flows) == 0 or flows.isna().all():
            return 0.5
        flows = flows.dropna()
        if len(flows) == 0:
            return 0.5
        
        inflows = flows[flows > 0].sum()
        outflows = abs(flows[flows < 0].sum())
        total_flow = inflows + outflows
        
        return inflows / total_flow if total_flow > 0 else 0.5
    
    df['money_flow_ratio'] = money_flow.rolling(window=20).apply(calc_inflow_ratio, raw=False)
    df['money_flow_ratio'] = np.clip(df['money_flow_ratio'], 0.1, 0.9).fillna(0.5)
    
    # Full_Y值 (控制系数)
    price_momentum = df['close'].pct_change(10)
    df['full_y'] = (df['rsi'] / 100 + np.tanh(price_momentum * 5) + 1) / 2
    df['full_y'] = np.clip(df['full_y'], 0.1, 0.9)
    
    # E值
    df['e_value'] = 8 * df['money_flow_ratio'] * df['full_y'] - 3 * df['money_flow_ratio'] - 3 * df['full_y'] + 1
    
    # 价格位置
    df['price_position'] = (df['close'] - df['regression_line']) / df['regression_line']
    
    return df

def determine_quick_signal(current_price, x_val, y_val, e_val, price_pos, regression_price):
    """快速判断信号"""
    
    # 策略参数
    x_threshold_long = 0.45
    y_threshold_long = 0.45
    x_threshold_short = 0.25
    y_threshold_short = 0.25
    
    # 止盈止损参数
    take_profit_long = 0.012   # 多头止盈 1.2%
    stop_loss_long = 0.008     # 多头止损 0.8%
    take_profit_short = 0.008  # 空头止盈 0.8%
    stop_loss_short = 0.012    # 空头止损 1.2%
    
    result = []
    result.append("🏦 东亚银行(0023.HK) 快速信号")
    result.append("=" * 40)
    result.append(f"📅 时间: {datetime.now().strftime('%Y-%m-%d %H:%M')}")
    result.append(f"💰 当前价: {current_price:.2f} 港币")
    result.append(f"📊 回归线: {regression_price:.2f} 港币")
    result.append("")
    result.append(f"🔢 指标: X={x_val:.3f} Y={y_val:.3f} E={e_val:.3f}")
    result.append(f"📍 位置: {price_pos*100:.1f}% ({'高于' if price_pos > 0 else '低于'}回归线)")
    result.append("")
    
    # 检查买涨条件
    long_condition = (e_val > 0 and 
                     x_val > x_threshold_long and 
                     y_val > y_threshold_long and 
                     price_pos < 0)
    
    # 检查买跌条件
    short_condition = ((y_val < y_threshold_short or x_val < x_threshold_short) and 
                      price_pos > 0)
    
    if long_condition:
        # 看涨信号
        stop_loss_price = current_price * (1 - stop_loss_long)
        take_profit_price = current_price * (1 + take_profit_long)
        
        result.append("🟢 信号: 看涨 - 买入做多")
        result.append(f"📍 入场价: {current_price:.2f} 港币")
        result.append(f"🛑 止损价: {stop_loss_price:.2f} 港币 (-{stop_loss_long*100:.1f}%)")
        result.append(f"🎯 止盈价: {take_profit_price:.2f} 港币 (+{take_profit_long*100:.1f}%)")
        result.append(f"📊 风险收益比: 1:{(take_profit_long/stop_loss_long):.1f}")
        
    elif short_condition:
        # 看跌信号
        stop_loss_price = current_price * (1 + stop_loss_short)
        take_profit_price = current_price * (1 - take_profit_short)
        
        result.append("🔴 信号: 看跌 - 买入做空")
        result.append(f"📍 入场价: {current_price:.2f} 港币")
        result.append(f"🛑 止损价: {stop_loss_price:.2f} 港币 (+{stop_loss_short*100:.1f}%)")
        result.append(f"🎯 止盈价: {take_profit_price:.2f} 港币 (-{take_profit_short*100:.1f}%)")
        result.append(f"📊 风险收益比: 1:{(take_profit_short/stop_loss_short):.1f}")
        
    else:
        # 观望信号
        result.append("⚪ 信号: 观望 - 等待机会")
        result.append("❌ 当前不满足买涨或买跌条件")
        
        # 简单的条件检查
        result.append("")
        result.append("📋 条件检查:")
        result.append(f"  买涨: E>0({'✓' if e_val > 0 else '✗'}) X>0.45({'✓' if x_val > 0.45 else '✗'}) Y>0.45({'✓' if y_val > 0.45 else '✗'}) 价格低于回归线({'✓' if price_pos < 0 else '✗'})")
        result.append(f"  买跌: (Y<0.25或X<0.25)({'✓' if (y_val < 0.25 or x_val < 0.25) else '✗'}) 价格高于回归线({'✓' if price_pos > 0 else '✗'})")
    
    result.append("")
    result.append("⚠️ 风险提醒: 严格止盈止损，控制风险")
    
    return "\n".join(result)

def main():
    """主函数"""
    signal_info = get_quick_signal()
    print(signal_info)

if __name__ == "__main__":
    main()
