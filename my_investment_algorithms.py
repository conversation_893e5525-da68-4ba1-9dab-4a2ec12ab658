#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
我的投资算法实现
==============
完整的投资策略算法实现
包含5个核心策略的计算逻辑和交易信号生成
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

class MyInvestmentAlgorithms:
    """我的投资算法类"""

    def __init__(self):
        """初始化算法参数"""
        # 基础参数
        self.initial_capital = 30000
        self.monthly_addition = 1000
        self.max_position_ratio = 0.35

        # 止盈止损参数
        self.take_profit_long = 0.016   # 多头止盈1.6%
        self.stop_loss_long = 0.008     # 多头止损0.8%
        self.take_profit_short = 0.008  # 空头止盈0.8%
        self.stop_loss_short = 0.016    # 空头止损1.6%

        # 策略阈值参数
        self.y_threshold = 0.43         # Y值门槛
        self.x_threshold = 0.43         # X值门槛

    def calculate_basic_xy_indicators(self, df):
        """计算基础XY指标"""
        print("📊 计算基础XY指标...")

        # 计算Y指标 (控股商控制比例)
        df['midprice'] = (df['High'] + df['Low']) / 2
        df['controller'] = (df['Close'] > df['midprice']).astype(int)
        df['cumulative_controller'] = df['controller'].cumsum()
        df['row_number'] = range(1, len(df) + 1)
        df['Y'] = df['cumulative_controller'] / df['row_number']

        # 计算传统X指标
        df['X_traditional'] = 1 - df['Y']

        return df

    def calculate_retail_sentiment_x(self, df):
        """计算散户情绪X指标 (散户买升概率)"""
        print("🧠 计算散户情绪X指标...")

        # 计算技术指标
        df['ma_20'] = df['Close'].rolling(window=20).mean()
        df['ma_60'] = df['Close'].rolling(window=60).mean()

        # 计算RSI
        delta = df['Close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
        rs = gain / loss
        df['rsi'] = 100 - (100 / (1 + rs))

        # 计算价格动量
        df['momentum'] = df['Close'].pct_change(5)

        # 计算相对成交量
        df['volume_ma'] = df['Volume'].rolling(20).mean()
        df['relative_volume'] = df['Volume'] / df['volume_ma']

        # 计算价格在区间的位置
        df['price_percentile'] = df['Close'].rolling(20).apply(
            lambda x: (x.iloc[-1] - x.min()) / (x.max() - x.min()) if x.max() != x.min() else 0.5
        )

        # 综合计算散户买升概率
        df['X_sentiment'] = (
            # RSI权重30% - RSI越高，散户越倾向买升
            np.clip(df['rsi'] / 100, 0, 1) * 0.30 +

            # 动量权重25% - 正动量时散户更倾向追涨
            np.clip((df['momentum'] + 0.05) / 0.10, 0, 1) * 0.25 +

            # 相对成交量权重25% - 成交量放大时散户更活跃
            np.clip((df['relative_volume'] - 0.5) / 1.5, 0, 1) * 0.25 +

            # 价格位置权重20% - 价格越高散户越倾向买升
            df['price_percentile'] * 0.20
        )

        # 确保在0-1范围内
        df['X_sentiment'] = np.clip(df['X_sentiment'], 0, 1)

        return df

    def calculate_money_flow_x(self, df):
        """计算资金流X指标 (散户买升概率基于资金流)"""
        print("💰 计算资金流X指标...")

        # 计算典型价格
        df['typical_price'] = (df['High'] + df['Low'] + df['Close']) / 3

        # 计算资金流量
        df['money_flow'] = df['typical_price'] * df['Volume']

        # 计算价格变化方向
        df['price_change'] = df['typical_price'].diff()

        # 分类资金流
        df['positive_money_flow'] = np.where(df['price_change'] > 0, df['money_flow'], 0)
        df['negative_money_flow'] = np.where(df['price_change'] < 0, df['money_flow'], 0)

        # 计算MFI
        period = 14
        df['positive_mf_sum'] = df['positive_money_flow'].rolling(period).sum()
        df['negative_mf_sum'] = df['negative_money_flow'].rolling(period).sum()
        df['money_flow_ratio'] = df['positive_mf_sum'] / (df['negative_mf_sum'] + 1e-10)
        df['mfi'] = 100 - (100 / (1 + df['money_flow_ratio']))

        # 计算VWAP
        df['vwap'] = (df['typical_price'] * df['Volume']).rolling(20).sum() / df['Volume'].rolling(20).sum()
        df['price_vs_vwap'] = df['Close'] / df['vwap']

        # 综合计算散户买升概率
        df['X_money_flow'] = (
            # MFI权重40%
            (df['mfi'] / 100) * 0.40 +

            # 价格相对VWAP权重25%
            np.clip((df['price_vs_vwap'] - 0.98) / 0.04, 0, 1) * 0.25 +

            # 相对成交量权重20%
            np.clip((df['relative_volume'] - 0.5) / 1.5, 0, 1) * 0.20 +

            # 价格位置权重15%
            df['price_percentile'] * 0.15
        )

        df['X_money_flow'] = np.clip(df['X_money_flow'], 0, 1)

        return df

    def calculate_retail_ratio_x(self, df):
        """计算散户资金占比X指标 (我的创新算法)"""
        print("🎯 计算散户资金占比X指标...")

        # 计算当日总资金流
        df['total_money_flow'] = df['typical_price'] * df['Volume']

        # 计算价格在当日区间的位置
        df['intraday_position'] = (df['Close'] - df['Low']) / (df['High'] - df['Low'])
        df['intraday_position'] = df['intraday_position'].fillna(0.5)

        # 计算价格变化幅度
        df['price_change_pct'] = df['Close'].pct_change().abs()

        # 计算价格相对于近期均价的位置
        df['price_vs_ma5'] = df['Close'] / df['Close'].rolling(5).mean()
        df['price_vs_ma5'] = df['price_vs_ma5'].fillna(1.0)

        # 计算散户参与度指标
        extreme_position = np.maximum(df['intraday_position'], 1 - df['intraday_position'])
        volume_factor = np.clip(df['relative_volume'] - 1, 0, 2) / 2
        volatility_factor = np.clip(df['price_change_pct'] * 100, 0, 5) / 5
        deviation_factor = np.clip(np.abs(df['price_vs_ma5'] - 1) * 10, 0, 1)

        # 综合散户参与度
        retail_participation = (
            extreme_position * 0.4 +      # 极端价格位置权重40%
            volume_factor * 0.3 +          # 成交量异常权重30%
            volatility_factor * 0.2 +      # 价格波动权重20%
            deviation_factor * 0.1         # 偏离均值权重10%
        )

        retail_participation = np.clip(retail_participation, 0, 1)

        # 计算散户资金流
        df['retail_money_flow'] = df['total_money_flow'] * retail_participation

        # 计算X = 散户资金流 / 总资金流
        df['X_retail_ratio'] = df['retail_money_flow'] / df['total_money_flow']
        df['X_retail_ratio'] = np.clip(df['X_retail_ratio'], 0, 1)

        return df

    def calculate_hsi50_indicators(self, df):
        """计算HSI50策略指标"""
        print("🔥 计算HSI50策略指标...")

        # 计算X指标 (基于成交量和价格)
        df['volume_sma'] = df['Volume'].rolling(20).mean()
        df['volume_ratio'] = df['Volume'] / df['volume_sma']
        df['price_momentum'] = df['Close'].pct_change(5)

        df['X_hsi50'] = np.clip(
            (df['volume_ratio'] - 0.5) * 0.5 +
            (df['price_momentum'] + 0.02) * 25 + 0.5, 0, 1
        )

        # 计算Y指标 (基于价格位置)
        df['high_20'] = df['High'].rolling(20).max()
        df['low_20'] = df['Low'].rolling(20).min()
        df['Y_hsi50'] = (df['Close'] - df['low_20']) / (df['high_20'] - df['low_20'])
        df['Y_hsi50'] = df['Y_hsi50'].fillna(0.5)

        # 计算E指标 (趋势指标)
        df['ema_12'] = df['Close'].ewm(span=12).mean()
        df['ema_26'] = df['Close'].ewm(span=26).mean()
        df['E_hsi50'] = df['ema_12'] - df['ema_26']

        # 计算回归线
        window = 60
        df['regression_line'] = df['Close'].rolling(window).apply(
            lambda x: np.polyval(np.polyfit(range(len(x)), x, 1), len(x)-1)
        )
        df['price_position'] = (df['Close'] - df['regression_line']) / df['regression_line']

        return df

    def generate_traditional_xy_signals(self, df):
        """生成传统XY策略信号"""
        print("⚖️ 生成传统XY策略信号...")

        signals = []
        for i, row in df.iterrows():
            if pd.isna(row['Y']) or pd.isna(row['X_traditional']):
                signals.append(0)
                continue

            # 多头信号: Y>0.43 且 X>0.43
            if row['Y'] > self.y_threshold and row['X_traditional'] > self.x_threshold:
                signals.append(1)
            # 空头信号: Y<0.25 或 X<0.25
            elif row['Y'] < 0.25 or row['X_traditional'] < 0.25:
                signals.append(-1)
            else:
                signals.append(0)

        df['signal_traditional'] = signals
        return df

    def generate_sentiment_signals(self, df):
        """生成散户情绪策略信号"""
        print("🧠 生成散户情绪策略信号...")

        signals = []
        for i, row in df.iterrows():
            if pd.isna(row['Y']) or pd.isna(row['X_sentiment']):
                signals.append(0)
                continue

            # 散户过度买升时做空
            if row['X_sentiment'] > 0.65 and row['mfi'] > 70:
                signals.append(-1)
            # 散户过度买跌时做多
            elif row['X_sentiment'] < 0.35 and row['mfi'] < 30:
                signals.append(1)
            # 控股商强势且散户情绪适中时做多
            elif row['Y'] > 0.55 and 0.4 < row['X_sentiment'] < 0.6:
                signals.append(1)
            else:
                signals.append(0)

        df['signal_sentiment'] = signals
        return df

    def generate_money_flow_signals(self, df):
        """生成资金流策略信号"""
        print("💰 生成资金流策略信号...")

        signals = []
        for i, row in df.iterrows():
            if pd.isna(row['Y']) or pd.isna(row['X_money_flow']) or pd.isna(row['mfi']):
                signals.append(0)
                continue

            # 散户资金大量流入买升时做空
            if row['X_money_flow'] > 0.65 and row['mfi'] > 70:
                signals.append(-1)
            # 散户资金大量流入买跌时做多
            elif (1 - row['X_money_flow']) > 0.65 and row['mfi'] < 30:
                signals.append(1)
            # 控股商强势且资金流健康时做多
            elif row['Y'] > 0.55 and 0.4 < row['X_money_flow'] < 0.6:
                signals.append(1)
            else:
                signals.append(0)

        df['signal_money_flow'] = signals
        return df

    def generate_retail_ratio_signals(self, df):
        """生成散户资金占比策略信号"""
        print("🎯 生成散户资金占比策略信号...")

        signals = []
        for i, row in df.iterrows():
            if pd.isna(row['Y']) or pd.isna(row['X_retail_ratio']):
                signals.append(0)
                continue

            # 多头信号: Y>0.43 且 X>0.43 (X=散户资金占比)
            if row['Y'] > self.y_threshold and row['X_retail_ratio'] > self.x_threshold:
                signals.append(1)
            # 空头信号: Y<0.25 或 X<0.25
            elif row['Y'] < 0.25 or row['X_retail_ratio'] < 0.25:
                signals.append(-1)
            else:
                signals.append(0)

        df['signal_retail_ratio'] = signals
        return df

    def generate_hsi50_enhanced_signals(self, df):
        """生成HSI50增强版策略信号"""
        print("🔥 生成HSI50增强版策略信号...")

        signals = []
        for i, row in df.iterrows():
            if (pd.isna(row['Y_hsi50']) or pd.isna(row['X_hsi50']) or
                pd.isna(row['E_hsi50']) or pd.isna(row['price_position'])):
                signals.append(0)
                continue

            # 多头信号: E>0 且 X>0.45 且 Y>0.45 且价格低于回归线
            if (row['E_hsi50'] > 0 and row['X_hsi50'] > 0.45 and
                row['Y_hsi50'] > 0.45 and row['price_position'] < 0):
                signals.append(1)

            # 空头信号: 满足任一条件且价格高于回归线
            elif row['price_position'] > 0:
                if (row['Y_hsi50'] < 0.3 or row['X_hsi50'] < 0.3 or
                    (row['X_hsi50'] > 0.45 and row['Y_hsi50'] < 0.35) or
                    (row['X_hsi50'] < 0.45 and row['Y_hsi50'] > 0.35)):
                    signals.append(-1)
                else:
                    signals.append(0)
            else:
                signals.append(0)

        df['signal_hsi50_enhanced'] = signals
        return df

    def calculate_all_indicators(self, df):
        """计算所有指标"""
        print("🎯 开始计算所有投资指标...")

        # 确保数据格式正确
        df['Date'] = pd.to_datetime(df['Date'])
        df = df.sort_values('Date').reset_index(drop=True)

        # 计算基础指标
        df = self.calculate_basic_xy_indicators(df)
        df = self.calculate_retail_sentiment_x(df)
        df = self.calculate_money_flow_x(df)
        df = self.calculate_retail_ratio_x(df)
        df = self.calculate_hsi50_indicators(df)

        print("✅ 所有指标计算完成")
        return df

    def generate_all_signals(self, df):
        """生成所有策略信号"""
        print("🎯 开始生成所有策略信号...")

        df = self.generate_traditional_xy_signals(df)
        df = self.generate_sentiment_signals(df)
        df = self.generate_money_flow_signals(df)
        df = self.generate_retail_ratio_signals(df)
        df = self.generate_hsi50_enhanced_signals(df)

        print("✅ 所有策略信号生成完成")
        return df

    def get_portfolio_signal(self, row, weights):
        """获取投资组合信号"""
        signals = {
            'traditional': row.get('signal_traditional', 0),
            'sentiment': row.get('signal_sentiment', 0),
            'money_flow': row.get('signal_money_flow', 0),
            'retail_ratio': row.get('signal_retail_ratio', 0),
            'hsi50_enhanced': row.get('signal_hsi50_enhanced', 0)
        }

        # 加权平均信号
        weighted_signal = sum(signals[strategy] * weight
                            for strategy, weight in weights.items()
                            if strategy in signals)

        # 转换为交易信号
        if weighted_signal > 0.3:
            return 1  # 多头
        elif weighted_signal < -0.3:
            return -1  # 空头
        else:
            return 0  # 观望

    def analyze_signals_summary(self, df):
        """分析信号统计"""
        print("\n📊 信号统计分析:")
        print("=" * 60)

        strategies = ['traditional', 'sentiment', 'money_flow', 'retail_ratio', 'hsi50_enhanced']

        for strategy in strategies:
            signal_col = f'signal_{strategy}'
            if signal_col in df.columns:
                long_signals = (df[signal_col] == 1).sum()
                short_signals = (df[signal_col] == -1).sum()
                neutral_signals = (df[signal_col] == 0).sum()
                total_signals = len(df)

                print(f"\n   {strategy.upper()}策略:")
                print(f"   • 多头信号: {long_signals}次 ({long_signals/total_signals*100:.1f}%)")
                print(f"   • 空头信号: {short_signals}次 ({short_signals/total_signals*100:.1f}%)")
                print(f"   • 观望信号: {neutral_signals}次 ({neutral_signals/total_signals*100:.1f}%)")

    def backtest_strategy(self, df, strategy_name, weights=None):
        """回测单个策略或组合策略"""
        print(f"\n🎯 开始回测{strategy_name}策略...")

        capital = self.initial_capital
        position = 0
        entry_price = 0
        entry_date = None
        trades = []

        max_capital = capital
        max_drawdown = 0

        for i in range(60, len(df)):  # 从第60天开始，确保指标计算完整
            row = df.iloc[i]
            date = row['Date']

            # 每月增加资金
            capital = self.add_monthly_capital(date, capital)

            # 更新最大资金和回撤
            if capital > max_capital:
                max_capital = capital
            current_drawdown = (max_capital - capital) / max_capital
            if current_drawdown > max_drawdown:
                max_drawdown = current_drawdown

            # 获取交易信号
            if weights:
                signal = self.get_portfolio_signal(row, weights)
            else:
                signal = row.get(f'signal_{strategy_name}', 0)

            # 检查平仓条件
            if position != 0:
                should_exit, exit_reason, exit_price = self.check_exit_conditions(row, position, entry_price)

                if should_exit:
                    profit = self.calculate_trade_profit(position, entry_price, exit_price, capital)
                    capital += profit

                    trades.append({
                        'entry_date': entry_date,
                        'exit_date': date,
                        'direction': '看涨' if position == 1 else '看跌',
                        'entry_price': entry_price,
                        'exit_price': exit_price,
                        'profit': profit,
                        'exit_reason': exit_reason,
                        'capital_after': capital
                    })

                    position = 0

            # 检查开仓条件
            if position == 0 and signal != 0:
                position = signal
                entry_price = row['Close']
                entry_date = date

        return {
            'final_capital': capital,
            'max_drawdown': max_drawdown,
            'trades': trades,
            'total_trades': len(trades),
            'winning_trades': len([t for t in trades if t['profit'] > 0])
        }

    def check_exit_conditions(self, row, position, entry_price):
        """检查平仓条件"""
        if position == 1:  # 多头
            if row['High'] >= entry_price * (1 + self.take_profit_long):
                return True, "止盈", entry_price * (1 + self.take_profit_long)
            elif row['Low'] <= entry_price * (1 - self.stop_loss_long):
                return True, "止损", entry_price * (1 - self.stop_loss_long)
        elif position == -1:  # 空头
            if row['Low'] <= entry_price * (1 - self.take_profit_short):
                return True, "止盈", entry_price * (1 - self.take_profit_short)
            elif row['High'] >= entry_price * (1 + self.stop_loss_short):
                return True, "止损", entry_price * (1 + self.stop_loss_short)

        return False, "", 0

    def calculate_trade_profit(self, position, entry_price, exit_price, capital):
        """计算交易盈亏"""
        position_size = capital * self.max_position_ratio

        if position == 1:  # 多头
            profit_ratio = (exit_price - entry_price) / entry_price
        else:  # 空头
            profit_ratio = (entry_price - exit_price) / entry_price

        return position_size * profit_ratio

    def add_monthly_capital(self, date, capital):
        """每月增加资金"""
        if not hasattr(self, 'last_month'):
            self.last_month = None

        current_month = date.replace(day=1)

        if self.last_month is None or current_month > self.last_month:
            self.last_month = current_month
            return capital + self.monthly_addition

        return capital

def main():
    """主函数 - 算法使用示例"""
    print("🎯 我的投资算法系统")
    print("=" * 60)
    print("这是一个完整的投资算法实现，包含:")
    print("• 5个核心策略的指标计算")
    print("• 完整的信号生成逻辑")
    print("• 投资组合信号合成")
    print("• 完整的回测引擎")
    print("• 详细的统计分析")

    print(f"\n💡 使用方法:")
    print(f"1. 准备包含OHLCV数据的DataFrame")
    print(f"2. 创建算法实例: algo = MyInvestmentAlgorithms()")
    print(f"3. 计算指标: df = algo.calculate_all_indicators(df)")
    print(f"4. 生成信号: df = algo.generate_all_signals(df)")
    print(f"5. 回测策略: results = algo.backtest_strategy(df, 'retail_ratio')")
    print(f"6. 分析结果: algo.analyze_signals_summary(df)")

    print(f"\n🎯 核心算法特点:")
    print(f"• 基于35年历史数据验证")
    print(f"• 包含传统技术分析和行为金融学")
    print(f"• 支持多策略组合")
    print(f"• 完整的风险控制机制")
    print(f"• 内置回测引擎")

    print(f"\n📊 支持的策略:")
    print(f"• traditional: 传统XY策略")
    print(f"• sentiment: 散户情绪策略")
    print(f"• money_flow: 资金流策略")
    print(f"• retail_ratio: 散户资金占比策略")
    print(f"• hsi50_enhanced: HSI50增强版策略")

    print(f"\n💰 投资组合示例:")
    print(f"weights = {{'traditional': 0.4, 'sentiment': 0.3, 'retail_ratio': 0.2, 'hsi50_enhanced': 0.1}}")
    print(f"results = algo.backtest_strategy(df, 'portfolio', weights)")

    print(f"\n🚀 开始您的算法交易之旅！")

if __name__ == "__main__":
    main()
