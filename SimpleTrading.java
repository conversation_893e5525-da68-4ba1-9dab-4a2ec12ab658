public class SimpleTrading {
    public static void main(String[] args) {
        System.out.println("交易止盈止损计算器");
        System.out.println("==================");
        
        double entryPrice = 12.420;
        double stopLoss = 0.0006;
        double takeProfit = 0.0012;
        
        // 多头计算
        double longTP = entryPrice * (1 + takeProfit);
        double longSL = entryPrice * (1 - stopLoss);
        
        System.out.println("多头 (入场: " + entryPrice + "):");
        System.out.println("  止盈: " + String.format("%.3f", longTP));
        System.out.println("  止损: " + String.format("%.3f", longSL));
        
        // 空头计算
        double shortTP = entryPrice * (1 - takeProfit);
        double shortSL = entryPrice * (1 + stopLoss);
        
        System.out.println("空头 (入场: " + entryPrice + "):");
        System.out.println("  止盈: " + String.format("%.3f", shortTP));
        System.out.println("  止损: " + String.format("%.3f", shortSL));
    }
}
