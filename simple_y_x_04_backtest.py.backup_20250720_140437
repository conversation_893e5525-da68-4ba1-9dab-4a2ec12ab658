#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化策略回测：Y>0.4 且 X>0.4
5万港币资金，10年历史回测
"""

import pymysql
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

def simple_y_x_04_backtest():
    """Y>0.4 且 X>0.4 策略回测"""
    
    db_config = {
        'host': '************',
        'port': 3306,
        'database': 'finance',
        'user': 'root',
        'password': '',
        'charset': 'utf8mb4'
    }
    
    try:
        connection = pymysql.connect(**db_config)
        cursor = connection.cursor()
        
        print("💰 Y>0.4 且 X>0.4 策略回测")
        print("="*60)
        print("📊 数据源: HK2800 (盈富基金)")
        print("💰 初始资金: 50,000 港币")
        print("📅 回测期间: 最近10年")
        print("🎯 策略: Y>0.4 且 X>0.4 (简化条件)")
        
        # 1. 获取最近10年数据
        end_date = datetime.now()
        start_date = end_date - timedelta(days=365*10)
        
        cursor.execute("""
            SELECT date, close, adj_close, y_probability, inflow_ratio
            FROM hk2800 
            WHERE date >= %s 
            AND y_probability IS NOT NULL 
            AND inflow_ratio IS NOT NULL
            ORDER BY date ASC
        """, (start_date.date(),))
        
        data = cursor.fetchall()
        df = pd.DataFrame(data, columns=['date', 'close', 'adj_close', 'y_probability', 'inflow_ratio'])
        
        for col in ['close', 'adj_close', 'y_probability', 'inflow_ratio']:
            df[col] = pd.to_numeric(df[col])
        df['date'] = pd.to_datetime(df['date'])
        
        print(f"📊 数据范围: {len(df)} 条记录")
        print(f"📅 实际时间: {df['date'].min().strftime('%Y-%m-%d')} 到 {df['date'].max().strftime('%Y-%m-%d')}")
        
        # 2. 定义策略条件
        df['signal'] = (df['y_probability'] > 0.4) & (df['inflow_ratio'] > 0.4)
        
        # 计算Edeta作为参考
        df['edeta'] = (8 * df['inflow_ratio'] - 3) * df['y_probability'] - 3 * df['inflow_ratio'] + 1
        
        # 3. 策略统计
        total_days = len(df)
        signal_days = df['signal'].sum()
        years = (df['date'].max() - df['date'].min()).days / 365.25
        
        print(f"\n📊 策略统计:")
        print("="*40)
        print(f"   总交易日: {total_days}")
        print(f"   信号天数: {signal_days}")
        print(f"   信号频率: {signal_days/total_days*100:.2f}%")
        print(f"   年均交易: {signal_days/years:.1f}次")
        print(f"   实际年限: {years:.1f}年")
        
        # 4. 不同仓位策略回测
        initial_capital = 50000
        position_strategies = [
            {'name': '保守 (10%)', 'position': 0.10},
            {'name': '平衡 (15%)', 'position': 0.15},
            {'name': '积极 (20%)', 'position': 0.20},
            {'name': '激进 (25%)', 'position': 0.25},
        ]
        
        def backtest_position_strategy(position_size, holding_days=60):
            """回测特定仓位策略"""
            
            signal_data = df[df['signal']].copy()
            
            if len(signal_data) == 0:
                return None
            
            # 蒙特卡洛模拟
            np.random.seed(42)
            num_simulations = 1000
            final_amounts = []
            
            for sim in range(num_simulations):
                capital = initial_capital
                
                for _, row in signal_data.iterrows():
                    entry_date = row['date']
                    entry_price = row['adj_close']
                    
                    # 计算仓位大小
                    position_amount = capital * position_size
                    shares = position_amount / entry_price
                    
                    # 找到退出价格
                    exit_date = entry_date + timedelta(days=holding_days)
                    future_data = df[df['date'] >= exit_date]
                    
                    if not future_data.empty:
                        exit_price = future_data.iloc[0]['adj_close']
                        
                        # 计算盈亏
                        pnl = shares * (exit_price - entry_price)
                        capital += pnl
                        
                        # 防止资金为负
                        capital = max(capital, 0)
                
                final_amounts.append(capital)
            
            return {
                'mean_final': np.mean(final_amounts),
                'median_final': np.median(final_amounts),
                'percentile_5': np.percentile(final_amounts, 5),
                'percentile_25': np.percentile(final_amounts, 25),
                'percentile_75': np.percentile(final_amounts, 75),
                'percentile_95': np.percentile(final_amounts, 95),
                'std_final': np.std(final_amounts)
            }
        
        # 5. 测试不同持有期
        holding_periods = [30, 60, 90, 120]
        
        print(f"\n💰 不同持有期表现 (15%仓位):")
        print("="*60)
        print("持有期   最终金额   总收益    年化收益   成功率")
        print("-" * 60)
        
        best_holding = None
        best_return = 0
        
        for days in holding_periods:
            result = backtest_position_strategy(0.15, days)
            
            if result:
                final_amount = result['median_final']
                total_return = (final_amount - initial_capital) / initial_capital
                annual_return = ((final_amount / initial_capital) ** (1/years)) - 1
                success_rate = sum(1 for x in [result['percentile_5'], result['percentile_25'], result['median_final'], result['percentile_75'], result['percentile_95']] if x > initial_capital) / 5
                
                print(f"{days:>3}天   {final_amount:>8,.0f}   {total_return*100:>+6.2f}%   {annual_return*100:>+7.2f}%   {success_rate*100:>5.0f}%")
                
                if annual_return > best_return:
                    best_return = annual_return
                    best_holding = days
        
        # 6. 最佳持有期详细分析
        if best_holding:
            print(f"\n🏆 最佳持有期: {best_holding}天")
            print("="*50)
            
            print(f"📊 不同仓位策略对比 ({best_holding}天持有):")
            print("="*60)
            print("策略        仓位   最终金额   总收益    年化收益   风险评级")
            print("-" * 60)
            
            best_strategy = None
            best_annual = 0
            
            for strategy in position_strategies:
                result = backtest_position_strategy(strategy['position'], best_holding)
                
                if result:
                    final_amount = result['median_final']
                    total_return = (final_amount - initial_capital) / initial_capital
                    annual_return = ((final_amount / initial_capital) ** (1/years)) - 1
                    
                    # 风险评级
                    volatility = result['std_final'] / initial_capital
                    if volatility < 0.1:
                        risk_rating = "低风险"
                    elif volatility < 0.2:
                        risk_rating = "中风险"
                    else:
                        risk_rating = "高风险"
                    
                    print(f"{strategy['name']:<10} {strategy['position']*100:>4.0f}%  {final_amount:>9,.0f}  {total_return*100:>+7.2f}%  {annual_return*100:>+8.2f}%   {risk_rating}")
                    
                    if annual_return > best_annual:
                        best_annual = annual_return
                        best_strategy = strategy
            
            # 7. 最佳策略详细分析
            if best_strategy:
                print(f"\n🎯 推荐策略: {best_strategy['name']}")
                print("="*50)
                
                result = backtest_position_strategy(best_strategy['position'], best_holding)
                
                print(f"📊 详细表现:")
                print(f"   仓位大小: {best_strategy['position']*100:.0f}%")
                print(f"   单次投资: {initial_capital * best_strategy['position']:,.0f} 港币")
                print(f"   持有期: {best_holding}天")
                print(f"   预期最终: {result['median_final']:,.0f} 港币")
                print(f"   预期收益: {result['median_final'] - initial_capital:+,.0f} 港币")
                print(f"   年化收益: {best_annual*100:+.2f}%")
                
                print(f"\n📊 概率分析:")
                print("概率     最终金额     收益")
                print("-" * 30)
                percentiles = [5, 25, 50, 75, 95]
                amounts = [result['percentile_5'], result['percentile_25'], result['median_final'], 
                          result['percentile_75'], result['percentile_95']]
                
                for p, amount in zip(percentiles, amounts):
                    profit = amount - initial_capital
                    print(f"{p:>2}%     {amount:>9,.0f}    {profit:>+8,.0f}")
                
                # 8. 年度收益预测
                print(f"\n📅 年度收益预测:")
                print("="*40)
                
                annual_profit = (result['median_final'] - initial_capital) / years
                monthly_profit = annual_profit / 12
                
                print(f"   年均收益: {annual_profit:+,.0f} 港币")
                print(f"   月均收益: {monthly_profit:+,.0f} 港币")
                
                for year in range(1, 6):
                    projected_amount = initial_capital * ((1 + best_annual) ** year)
                    projected_profit = projected_amount - initial_capital
                    print(f"   第{year}年末: {projected_amount:,.0f} 港币 (累计收益{projected_profit:+,.0f})")
        
        # 9. 与买入持有对比
        start_price = df.iloc[0]['adj_close']
        end_price = df.iloc[-1]['adj_close']
        buy_hold_return = (end_price - start_price) / start_price
        buy_hold_annual = ((1 + buy_hold_return) ** (1/years)) - 1
        buy_hold_final = initial_capital * (1 + buy_hold_return)
        
        print(f"\n📊 策略对比:")
        print("="*50)
        print(f"   买入持有最终: {buy_hold_final:,.0f} 港币")
        print(f"   买入持有年化: {buy_hold_annual*100:+.2f}%")
        print(f"   策略最终: {result['median_final']:,.0f} 港币")
        print(f"   策略年化: {best_annual*100:+.2f}%")
        print(f"   超额收益: {(best_annual - buy_hold_annual)*100:+.2f}%")
        
        # 10. 风险分析
        print(f"\n🛡️ 风险分析:")
        print("="*30)
        
        single_position = initial_capital * best_strategy['position']
        max_loss_per_trade = single_position * 0.1  # 假设10%止损
        
        print(f"   单笔投资: {single_position:,.0f} 港币")
        print(f"   单笔最大风险: {max_loss_per_trade:,.0f} 港币")
        print(f"   年均交易: {signal_days/years:.1f}次")
        print(f"   最差情况: {result['percentile_5']:,.0f} 港币")
        print(f"   亏损概率: 约5%")
        
        # 11. 实战建议
        print(f"\n💡 实战建议:")
        print("="*30)
        print(f"✅ 推荐仓位: {best_strategy['position']*100:.0f}%")
        print(f"✅ 单笔投资: {single_position:,.0f} 港币")
        print(f"✅ 持有期: {best_holding}天")
        print(f"✅ 止损设定: 10%")
        print(f"✅ 止盈设定: 根据持有期自然退出")
        print(f"✅ 预期年收益: {annual_profit:+,.0f} 港币")
        
        return {
            'best_strategy': best_strategy['name'],
            'best_position': best_strategy['position'],
            'best_holding': best_holding,
            'expected_final': result['median_final'],
            'annual_return': best_annual
        }
        
    except Exception as e:
        print(f"❌ 回测失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        if 'connection' in locals():
            connection.close()

if __name__ == "__main__":
    simple_y_x_04_backtest()
