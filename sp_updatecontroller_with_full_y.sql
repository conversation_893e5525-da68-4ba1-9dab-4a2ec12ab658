
CREATE PROCEDURE sp_updatecontroller(
    IN tablename VARCHAR(64),
    OUT result_k DECIMAL(20,6)
)
BEGIN
    DECLARE col_exists INT DEFAULT 0;
    DECLARE v_start_time DATETIME DEFAULT NOW();
    DECLARE done INT DEFAULT FALSE;
    DECLARE current_row_num INT DEFAULT 0;
    DECLARE current_controller INT;
    DECLARE record_date DATE;
    DECLARE running_sum DECIMAL(20,10) DEFAULT 0;

    -- 游标声明 - 使用动态表名
    DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;

    -- 1. 首先更新midprice (调用sp_averagelineV3)
    SELECT CONCAT('开始更新midprice - 表: ', tablename, ' 时间: ', v_start_time) AS start_message;
    CALL sp_averagelineV3(tablename);
    SELECT 'midprice更新完成' AS midprice_status;

    -- 2. 检查controller列是否存在
    SET @sql = CONCAT(
        'SELECT COUNT(*) INTO @col_exists FROM information_schema.COLUMNS ',
        'WHERE TABLE_SCHEMA=DATABASE() AND TABLE_NAME=''', tablename, ''' AND COLUMN_NAME=''controller'''
    );
    PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;
    SET col_exists = @col_exists;

    IF col_exists = 0 THEN
        SET @sql = CONCAT('ALTER TABLE `', tablename, '` ADD COLUMN `controller` INT');
        PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;
        SELECT 'controller列已添加' AS controller_status;
    END IF;

    -- 3. 更新controller字段
    SET @sql = CONCAT(
        'UPDATE `',
        tablename,
        '` SET controller = CASE WHEN close < midprice THEN 0 WHEN close > midprice THEN 1 ELSE 3 END'
    );
    PREPARE stmt FROM @sql;
    EXECUTE stmt;
    DEALLOCATE PREPARE stmt;
    SELECT 'controller字段更新完成' AS controller_update_status;

    -- 4. 检查Full_Y列是否存在
    SET @sql = CONCAT(
        'SELECT COUNT(*) INTO @col_exists FROM information_schema.COLUMNS ',
        'WHERE TABLE_SCHEMA=DATABASE() AND TABLE_NAME=''', tablename, ''' AND COLUMN_NAME=''Full_Y'''
    );
    PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;
    SET col_exists = @col_exists;

    IF col_exists = 0 THEN
        SET @sql = CONCAT('ALTER TABLE `', tablename, '` ADD COLUMN `Full_Y` DECIMAL(20,10) DEFAULT NULL');
        PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;
        SELECT 'Full_Y列已添加' AS full_y_status;
    END IF;

    -- 5. 计算Full_Y字段
    SELECT '开始计算Full_Y...' AS full_y_calc_status;

    -- 先创建临时表来计算Full_Y
    SET @sql = CONCAT('DROP TEMPORARY TABLE IF EXISTS temp_full_y');
    PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;

    SET @sql = CONCAT(
        'CREATE TEMPORARY TABLE temp_full_y AS ',
        'SELECT date, ',
        '  @running_sum := @running_sum + ',
        '    CASE WHEN controller = 1 THEN 1.0/@row_num ELSE 0 END as Full_Y ',
        'FROM (',
        '  SELECT date, controller, ',
        '    @row_num := @row_num + 1 as row_num ',
        '  FROM `', tablename, '` ',
        '  CROSS JOIN (SELECT @row_num := 0, @running_sum := 0) r ',
        '  ORDER BY date ASC',
        ') t'
    );
    PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;

    -- 更新原表
    SET @sql = CONCAT(
        'UPDATE `', tablename, '` t1 ',
        'JOIN temp_full_y t2 ON t1.date = t2.date ',
        'SET t1.Full_Y = t2.Full_Y'
    );
    PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;

    -- 清理临时表
    DROP TEMPORARY TABLE IF EXISTS temp_full_y;

    SELECT 'Full_Y计算完成' AS full_y_complete_status;

    -- 6. 计算k值并存入OUT参数
    SET @sql = CONCAT(
        'SELECT SUM(CASE WHEN controller = 1 THEN 1 ELSE 0 END) / NULLIF(COUNT(*), 0) INTO @k_value ',
        'FROM `', tablename, '`'
    );
    PREPARE stmt FROM @sql;
    EXECUTE stmt;
    DEALLOCATE PREPARE stmt;

    SET result_k = @k_value;

    -- 7. 返回统计信息
    SELECT
        CONCAT('更新完成 - 表: ', tablename) AS completion_message,
        CONCAT('k值: ', IFNULL(result_k, 0)) AS k_value_message,
        CONCAT('耗时: ', TIMESTAMPDIFF(SECOND, v_start_time, NOW()), ' 秒') AS duration;

END
            