#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
我的核心交易算法
==============
精简版的核心交易算法，专门用于实际交易
包含最重要的指标计算和信号生成逻辑
"""

import pandas as pd
import numpy as np
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

class MyTradingAlgorithmCore:
    """我的核心交易算法类"""

    def __init__(self):
        """初始化核心参数"""
        # 策略阈值
        self.y_threshold = 0.43
        self.x_threshold = 0.43

        # 风险控制参数
        self.take_profit_long = 0.016   # 多头止盈1.6%
        self.stop_loss_long = 0.008     # 多头止损0.8%
        self.take_profit_short = 0.008  # 空头止盈0.8%
        self.stop_loss_short = 0.016    # 空头止损1.6%

        # 仓位控制
        self.max_position_ratio = 0.35  # 最大仓位35%

    def calculate_y_indicator(self, df):
        """计算Y指标 (基于HSI50算法的价格位置指标)"""
        df = df.copy()

        # 使用HSI50算法计算Y值
        # Y = (Close - Low_20) / (High_20 - Low_20)
        window = 20
        df['high_20'] = df['High'].rolling(window).max()
        df['low_20'] = df['Low'].rolling(window).min()

        # 计算价格在20日区间的位置
        df['Y'] = (df['Close'] - df['low_20']) / (df['high_20'] - df['low_20'])

        # 处理除零情况
        df['Y'] = df['Y'].fillna(0.5)

        # 确保Y值在0-1范围内
        df['Y'] = np.clip(df['Y'], 0, 1)

        return df

    def calculate_x_indicator(self, df):
        """计算X指标 (基于HSI50算法的成交量和价格指标)"""
        df = df.copy()

        # 使用HSI50算法计算X值
        # 基于成交量比率和价格动量

        # 计算成交量移动平均和比率
        volume_window = 20
        df['volume_sma'] = df['Volume'].rolling(volume_window).mean()
        df['volume_ratio'] = df['Volume'] / df['volume_sma']
        df['volume_ratio'] = df['volume_ratio'].fillna(1.0)

        # 计算价格动量
        momentum_period = 5
        df['price_momentum'] = df['Close'].pct_change(momentum_period)
        df['price_momentum'] = df['price_momentum'].fillna(0)

        # 计算相对强弱指标
        delta = df['Close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
        rs = gain / (loss + 1e-10)
        df['rsi'] = 100 - (100 / (1 + rs))
        df['rsi'] = df['rsi'].fillna(50)

        # 综合计算X值 (类似HSI50算法)
        # X值综合考虑成交量、动量和RSI
        volume_component = np.clip((df['volume_ratio'] - 0.5) * 0.5, -0.5, 0.5)
        momentum_component = np.clip(df['price_momentum'] * 25, -0.5, 0.5)
        rsi_component = (df['rsi'] - 50) / 100

        df['X'] = 0.5 + volume_component + momentum_component + rsi_component * 0.3

        # 确保X值在0-1范围内
        df['X'] = np.clip(df['X'], 0, 1)

        return df

    def calculate_sentiment_indicators(self, df):
        """计算散户情绪相关指标"""
        df = df.copy()

        # 确保有典型价格和总资金流
        if 'typical_price' not in df.columns:
            df['typical_price'] = (df['High'] + df['Low'] + df['Close']) / 3
        if 'total_money_flow' not in df.columns:
            df['total_money_flow'] = df['typical_price'] * df['Volume']

        # 计算MFI (资金流指标)
        df['price_change'] = df['typical_price'].diff()
        df['positive_money_flow'] = np.where(df['price_change'] > 0, df['total_money_flow'], 0)
        df['negative_money_flow'] = np.where(df['price_change'] < 0, df['total_money_flow'], 0)

        period = 14
        df['positive_mf_sum'] = df['positive_money_flow'].rolling(period).sum()
        df['negative_mf_sum'] = df['negative_money_flow'].rolling(period).sum()
        df['money_flow_ratio'] = df['positive_mf_sum'] / (df['negative_mf_sum'] + 1e-10)
        df['MFI'] = 100 - (100 / (1 + df['money_flow_ratio']))

        # 计算RSI
        delta = df['Close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
        rs = gain / loss
        df['RSI'] = 100 - (100 / (1 + rs))

        return df

    def generate_trading_signals(self, df):
        """生成交易信号 (基于HSI50增强版算法)"""
        df = df.copy()

        # 计算回归线 (用于价格位置判断)
        window = 60
        df['regression_line'] = df['Close'].rolling(window).apply(
            lambda x: np.polyval(np.polyfit(range(len(x)), x, 1), len(x)-1) if len(x) == window else np.nan
        )
        df['price_position'] = (df['Close'] - df['regression_line']) / df['regression_line']
        df['price_position'] = df['price_position'].fillna(0)

        signals = []
        for i, row in df.iterrows():
            if (pd.isna(row['Y']) or pd.isna(row['X']) or pd.isna(row['price_position'])):
                signals.append(0)
                continue

            y_val = row['Y']
            x_val = row['X']
            price_pos = row['price_position']

            # 多头信号: Y>0.45 且 X>0.45 且价格低于回归线
            if y_val > 0.45 and x_val > 0.45 and price_pos < 0:
                signals.append(1)

            # 空头信号: 满足任一条件且价格高于回归线
            elif price_pos > 0:
                if (y_val < 0.3 or x_val < 0.3 or
                    (x_val > 0.45 and y_val < 0.35) or
                    (x_val < 0.45 and y_val > 0.35)):
                    signals.append(-1)
                else:
                    signals.append(0)
            else:
                signals.append(0)

        df['signal'] = signals
        return df

    def generate_enhanced_signals(self, df):
        """生成增强版交易信号 (结合多个条件)"""
        df = df.copy()

        signals = []
        for i, row in df.iterrows():
            if (pd.isna(row['Y']) or pd.isna(row['X']) or
                pd.isna(row.get('MFI', np.nan)) or pd.isna(row.get('RSI', np.nan))):
                signals.append(0)
                continue

            # 增强版信号逻辑
            y_val = row['Y']
            x_val = row['X']
            mfi_val = row.get('MFI', 50)
            rsi_val = row.get('RSI', 50)

            # 强多头信号
            if (y_val > self.y_threshold and x_val > self.x_threshold and
                mfi_val < 70 and rsi_val < 70):
                signals.append(1)

            # 强空头信号
            elif (y_val < 0.25 or x_val < 0.25 or
                  (mfi_val > 80 and rsi_val > 80)):
                signals.append(-1)

            # 散户过度参与时的逆向信号
            elif x_val > 0.7 and mfi_val > 75:
                signals.append(-1)  # 散户过度买入，做空
            elif x_val < 0.3 and mfi_val < 25:
                signals.append(1)   # 散户过度卖出，做多

            else:
                signals.append(0)

        df['enhanced_signal'] = signals
        return df

    def calculate_position_size(self, capital, signal_strength=1.0):
        """计算仓位大小"""
        base_position = capital * self.max_position_ratio
        return base_position * signal_strength

    def calculate_stop_levels(self, entry_price, direction):
        """计算止盈止损位"""
        if direction == 1:  # 多头
            take_profit = entry_price * (1 + self.take_profit_long)
            stop_loss = entry_price * (1 - self.stop_loss_long)
        else:  # 空头
            take_profit = entry_price * (1 - self.take_profit_short)
            stop_loss = entry_price * (1 + self.stop_loss_short)

        return take_profit, stop_loss

    def get_latest_signal(self, df):
        """获取最新的交易信号"""
        if len(df) == 0:
            return None

        latest = df.iloc[-1]

        signal_info = {
            'date': latest.get('Date', datetime.now()),
            'close_price': latest.get('Close', 0),
            'y_value': latest.get('Y', 0),
            'x_value': latest.get('X', 0),
            'signal': latest.get('signal', 0),
            'enhanced_signal': latest.get('enhanced_signal', 0),
            'mfi': latest.get('MFI', 50),
            'rsi': latest.get('RSI', 50)
        }

        return signal_info

    def process_market_data(self, df):
        """处理市场数据的完整流程"""
        print("🎯 开始处理市场数据...")

        # 确保数据格式正确
        df['Date'] = pd.to_datetime(df['Date'])
        df = df.sort_values('Date').reset_index(drop=True)

        # 计算所有指标
        print("📊 计算Y指标...")
        df = self.calculate_y_indicator(df)

        print("💰 计算X指标...")
        df = self.calculate_x_indicator(df)

        print("🧠 计算情绪指标...")
        df = self.calculate_sentiment_indicators(df)

        print("🎯 生成交易信号...")
        df = self.generate_trading_signals(df)
        df = self.generate_enhanced_signals(df)

        print("✅ 数据处理完成")
        return df

    def get_strategy_summary(self):
        """获取策略摘要"""
        return {
            'strategy_name': 'HSI50增强版XY策略',
            'core_logic': 'Y>0.45且X>0.45且价格低于回归线做多',
            'short_conditions': [
                'Y<0.3 或 X<0.3 且价格高于回归线',
                'X>0.45 且 Y<0.35 且价格高于回归线',
                'X<0.45 且 Y>0.35 且价格高于回归线'
            ],
            'x_definition': '基于成交量比率、价格动量和RSI的综合指标',
            'y_definition': '价格在20日高低区间的位置 (Close-Low20)/(High20-Low20)',
            'risk_control': {
                'take_profit_long': f'{self.take_profit_long*100:.1f}%',
                'stop_loss_long': f'{self.stop_loss_long*100:.1f}%',
                'take_profit_short': f'{self.take_profit_short*100:.1f}%',
                'stop_loss_short': f'{self.stop_loss_short*100:.1f}%',
                'max_position': f'{self.max_position_ratio*100:.0f}%'
            }
        }

def main():
    """主函数 - 核心算法演示"""
    print("🎯 我的核心交易算法")
    print("=" * 60)

    # 创建算法实例
    algo = MyTradingAlgorithmCore()

    # 显示策略摘要
    summary = algo.get_strategy_summary()
    print(f"📊 策略名称: {summary['strategy_name']}")
    print(f"🎯 多头逻辑: {summary['core_logic']}")
    print(f"🔴 空头条件:")
    for i, condition in enumerate(summary['short_conditions'], 1):
        print(f"   {i}. {condition}")
    print(f"💰 X定义: {summary['x_definition']}")
    print(f"📈 Y定义: {summary['y_definition']}")

    print(f"\n🛡️ 风险控制:")
    for key, value in summary['risk_control'].items():
        print(f"   • {key}: {value}")

    print(f"\n💡 使用方法:")
    print(f"1. 准备OHLCV数据: df = pd.read_csv('your_data.csv')")
    print(f"2. 创建算法实例: algo = MyTradingAlgorithmCore()")
    print(f"3. 处理数据: df = algo.process_market_data(df)")
    print(f"4. 获取信号: signal = algo.get_latest_signal(df)")
    print(f"5. 执行交易: 根据信号进行买卖操作")

    print(f"\n🎯 核心特点:")
    print(f"• 基于HSI50增强版算法")
    print(f"• 结合价格位置和成交量分析")
    print(f"• 多重空头条件，捕获更多机会")
    print(f"• 使用回归线判断趋势方向")
    print(f"• 完善的风险控制机制")
    print(f"• 适合实盘交易使用")

    print(f"\n🚀 开始您的算法交易！")

if __name__ == "__main__":
    main()
