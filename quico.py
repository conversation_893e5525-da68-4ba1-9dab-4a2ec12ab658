#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
QUICO - Quick Signal Checker
东亚银行(0023.HK) 快速信号检查器
完整版：包含看涨、看跌、观望的详细分析和价格设置
"""

import yfinance as yf
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')
import sys

class QUICO:
    def __init__(self, ticker='0023.HK', name='东亚银行'):
        """初始化QUICO快速信号检查器"""
        self.ticker = ticker
        self.name = name
        
        # 策略参数
        self.x_threshold_long = 0.45    # 买涨X阈值
        self.y_threshold_long = 0.45    # 买涨Y阈值
        self.x_threshold_short = 0.25   # 买跌X阈值
        self.y_threshold_short = 0.25   # 买跌Y阈值
        
        # 1:2赔率设置 (平衡收益与持仓时间)
        self.take_profit_long = 0.016   # 多头止盈 1.6% (1:2赔率)
        self.stop_loss_long = 0.008     # 多头止损 0.8%
        self.take_profit_short = 0.020  # 空头止盈 2.0% (1:2赔率)
        self.stop_loss_short = 0.010    # 空头止损 1.0%
    
    def get_signal(self):
        """获取完整信号分析"""
        try:
            print("🔍 QUICO - 快速信号检查器")
            print("=" * 60)
            print(f"📊 目标: {self.ticker} ({self.name})")
            print(f"⏰ 时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            print("=" * 60)
            
            # 获取数据
            data = self._fetch_data()
            if data is None:
                return "❌ 数据获取失败"
            
            # 计算指标
            data = self._calculate_indicators(data)
            latest = data.iloc[-1]
            
            # 分析信号
            signal_result = self._analyze_signal(latest)
            
            return signal_result
            
        except Exception as e:
            return f"❌ 分析失败: {e}"
    
    def _fetch_data(self):
        """获取市场数据"""
        try:
            print("📡 获取最新数据...")
            end_date = datetime.now()
            start_date = end_date - timedelta(days=100)
            
            data = yf.download(self.ticker, start=start_date, end=end_date, progress=False)
            
            if data.empty:
                return None
            
            # 处理多级列名
            if isinstance(data.columns, pd.MultiIndex):
                data.columns = [col[0] for col in data.columns]
            
            # 重命名列
            data = data.rename(columns={
                'Open': 'open', 'High': 'high', 'Low': 'low', 
                'Close': 'close', 'Volume': 'volume'
            })
            
            data = data.reset_index()
            data = data.rename(columns={'Date': 'date'})
            
            print(f"✅ 数据获取成功 ({len(data)} 条记录)")
            return data
            
        except Exception as e:
            print(f"❌ 数据获取失败: {e}")
            return None
    
    def _calculate_indicators(self, df):
        """计算技术指标"""
        print("🔧 计算技术指标...")
        
        # 回归线
        window = 60
        df['regression_line'] = df['close'].rolling(window=window).apply(
            lambda x: np.polyval(np.polyfit(range(len(x)), x, 1), len(x)-1), raw=False
        )
        
        # RSI
        delta = df['close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
        rs = gain / loss
        df['rsi'] = 100 - (100 / (1 + rs))
        
        # 资金流比例 (X值)
        price_change = (df['close'] - df['open']) / df['open']
        money_flow = df['volume'] * price_change
        
        def calc_inflow_ratio(flows):
            if len(flows) == 0 or flows.isna().all():
                return 0.5
            flows = flows.dropna()
            if len(flows) == 0:
                return 0.5
            
            inflows = flows[flows > 0].sum()
            outflows = abs(flows[flows < 0].sum())
            total_flow = inflows + outflows
            
            return inflows / total_flow if total_flow > 0 else 0.5
        
        df['money_flow_ratio'] = money_flow.rolling(window=20).apply(calc_inflow_ratio, raw=False)
        df['money_flow_ratio'] = np.clip(df['money_flow_ratio'], 0.1, 0.9).fillna(0.5)
        
        # Full_Y值 (控制系数)
        price_momentum = df['close'].pct_change(10)
        df['full_y'] = (df['rsi'] / 100 + np.tanh(price_momentum * 5) + 1) / 2
        df['full_y'] = np.clip(df['full_y'], 0.1, 0.9)
        
        # E值
        df['e_value'] = 8 * df['money_flow_ratio'] * df['full_y'] - 3 * df['money_flow_ratio'] - 3 * df['full_y'] + 1
        
        # 价格位置
        df['price_position'] = (df['close'] - df['regression_line']) / df['regression_line']
        
        print("✅ 指标计算完成")
        return df
    
    def _analyze_signal(self, latest):
        """分析交易信号"""
        print("\n📈 市场状态分析")
        print("-" * 40)
        
        # 提取关键数据
        current_price = latest['close']
        regression_price = latest['regression_line']
        x_val = latest['money_flow_ratio']
        y_val = latest['full_y']
        e_val = latest['e_value']
        price_pos = latest['price_position']
        rsi = latest['rsi']
        
        # 显示基本信息
        print(f"💰 当前价格: {current_price:.2f} 港币")
        print(f"📊 回归线价格: {regression_price:.2f} 港币")
        print(f"📍 价格位置: {price_pos*100:.1f}% ({'高于' if price_pos > 0 else '低于'}回归线)")
        print(f"📈 RSI指标: {rsi:.1f}")
        
        print(f"\n🔢 关键指标:")
        print(f"  • X值 (资金流比例): {x_val:.3f}")
        print(f"  • Y值 (控制系数): {y_val:.3f}")
        print(f"  • E值 (博弈期望): {e_val:.3f}")
        
        # 判断信号类型
        signal_type = self._determine_signal_type(x_val, y_val, e_val, price_pos)
        
        print(f"\n🎯 信号分析结果")
        print("=" * 40)
        
        if signal_type == 'LONG':
            self._show_bullish_signal(current_price, x_val, y_val, e_val, price_pos)
        elif signal_type == 'SHORT':
            self._show_bearish_signal(current_price, x_val, y_val, e_val, price_pos)
        else:
            self._show_wait_signal(current_price, x_val, y_val, e_val, price_pos)
        
        # 显示策略说明
        self._show_strategy_summary()
        
        return signal_type
    
    def _determine_signal_type(self, x_val, y_val, e_val, price_pos):
        """确定信号类型"""
        # 买涨条件
        long_condition = (e_val > 0 and 
                         x_val > self.x_threshold_long and 
                         y_val > self.y_threshold_long and 
                         price_pos < 0)
        
        # 买跌条件
        short_condition = ((y_val < self.y_threshold_short or x_val < self.x_threshold_short) and 
                          price_pos > 0)
        
        if long_condition:
            return 'LONG'
        elif short_condition:
            return 'SHORT'
        else:
            return 'WAIT'
    
    def _show_bullish_signal(self, current_price, x_val, y_val, e_val, price_pos):
        """显示看涨信号"""
        print("🟢 看涨信号 - 建议买入做多")
        print(f"✅ 满足条件: E值>0({e_val:.3f}) + X值>{self.x_threshold_long}({x_val:.3f}) + Y值>{self.y_threshold_long}({y_val:.3f}) + 价格低于回归线")
        
        # 计算价格
        entry_price = current_price
        stop_loss_price = entry_price * (1 - self.stop_loss_long)
        take_profit_price = entry_price * (1 + self.take_profit_long)
        
        print(f"\n💰 交易价格设置:")
        print(f"  📍 入场价位: {entry_price:.2f} 港币")
        print(f"  🛑 止损价位: {stop_loss_price:.2f} 港币 (下跌{self.stop_loss_long*100:.1f}%)")
        print(f"  🎯 止盈价位: {take_profit_price:.2f} 港币 (上涨{self.take_profit_long*100:.1f}%)")
        
        # 风险收益分析
        risk = entry_price - stop_loss_price
        reward = take_profit_price - entry_price
        
        print(f"\n📊 风险收益分析:")
        print(f"  • 单股风险: {risk:.2f} 港币")
        print(f"  • 单股收益: {reward:.2f} 港币")
        print(f"  • 风险收益比: 1:{(reward/risk):.1f}")
        
        # 置信度
        confidence = self._calculate_confidence(x_val, y_val, e_val, 'LONG')
        print(f"  • 信号置信度: {confidence:.0f}%")
    
    def _show_bearish_signal(self, current_price, x_val, y_val, e_val, price_pos):
        """显示看跌信号"""
        print("🔴 看跌信号 - 建议买入做空")
        
        # 确定触发条件
        if y_val < self.y_threshold_short and x_val < self.x_threshold_short:
            condition_text = f"Y值<{self.y_threshold_short}({y_val:.3f}) 且 X值<{self.x_threshold_short}({x_val:.3f})"
        elif y_val < self.y_threshold_short:
            condition_text = f"Y值<{self.y_threshold_short}({y_val:.3f})"
        else:
            condition_text = f"X值<{self.x_threshold_short}({x_val:.3f})"
        
        print(f"✅ 满足条件: {condition_text} + 价格高于回归线")
        
        # 计算价格
        entry_price = current_price
        stop_loss_price = entry_price * (1 + self.stop_loss_short)
        take_profit_price = entry_price * (1 - self.take_profit_short)
        
        print(f"\n💰 交易价格设置:")
        print(f"  📍 入场价位: {entry_price:.2f} 港币")
        print(f"  🛑 止损价位: {stop_loss_price:.2f} 港币 (上涨{self.stop_loss_short*100:.1f}%)")
        print(f"  🎯 止盈价位: {take_profit_price:.2f} 港币 (下跌{self.take_profit_short*100:.1f}%)")
        
        # 风险收益分析
        risk = stop_loss_price - entry_price
        reward = entry_price - take_profit_price
        
        print(f"\n📊 风险收益分析:")
        print(f"  • 单股风险: {risk:.2f} 港币")
        print(f"  • 单股收益: {reward:.2f} 港币")
        print(f"  • 风险收益比: 1:{(reward/risk):.1f}")
        
        # 置信度
        confidence = self._calculate_confidence(x_val, y_val, e_val, 'SHORT')
        print(f"  • 信号置信度: {confidence:.0f}%")
    
    def _show_wait_signal(self, current_price, x_val, y_val, e_val, price_pos):
        """显示观望信号"""
        print("⚪ 观望信号 - 建议等待机会")
        print("❌ 当前不满足买涨或买跌条件")
        
        print(f"\n📋 条件检查:")
        print(f"  🟢 买涨条件:")
        print(f"    • E值>0: {'✅' if e_val > 0 else '❌'} ({e_val:.3f})")
        print(f"    • X值>{self.x_threshold_long}: {'✅' if x_val > self.x_threshold_long else '❌'} ({x_val:.3f})")
        print(f"    • Y值>{self.y_threshold_long}: {'✅' if y_val > self.y_threshold_long else '❌'} ({y_val:.3f})")
        print(f"    • 价格低于回归线: {'✅' if price_pos < 0 else '❌'} ({price_pos*100:.1f}%)")
        
        print(f"  🔴 买跌条件:")
        print(f"    • Y值<{self.y_threshold_short} 或 X值<{self.x_threshold_short}: {'✅' if (y_val < self.y_threshold_short or x_val < self.x_threshold_short) else '❌'}")
        print(f"    • 价格高于回归线: {'✅' if price_pos > 0 else '❌'} ({price_pos*100:.1f}%)")
        
        print(f"\n💡 等待建议:")
        if abs(price_pos) < 0.01:  # 接近回归线
            print("  • 价格接近回归线，等待明确突破方向")
        elif e_val > -0.2 and (x_val > 0.35 or y_val > 0.35):
            print("  • 接近买涨条件，关注价格回落机会")
        elif (y_val < 0.35 or x_val < 0.35) and price_pos > -0.02:
            print("  • 接近买跌条件，关注价格上涨机会")
        else:
            print("  • 指标偏中性，耐心等待明确信号")
    
    def _calculate_confidence(self, x_val, y_val, e_val, signal_type):
        """计算信号置信度"""
        if signal_type == 'LONG':
            confidence = 0
            if e_val > 0.5: confidence += 30
            elif e_val > 0.2: confidence += 25
            elif e_val > 0: confidence += 20
            
            if x_val > 0.7: confidence += 25
            elif x_val > 0.6: confidence += 20
            elif x_val > 0.5: confidence += 15
            elif x_val > 0.45: confidence += 10
            
            if y_val > 0.7: confidence += 25
            elif y_val > 0.6: confidence += 20
            elif y_val > 0.5: confidence += 15
            elif y_val > 0.45: confidence += 10
            
            return min(confidence, 100)
        
        elif signal_type == 'SHORT':
            confidence = 0
            if y_val < 0.15 or x_val < 0.15: confidence += 40
            elif y_val < 0.2 or x_val < 0.2: confidence += 35
            elif y_val < 0.25 or x_val < 0.25: confidence += 30
            
            if e_val < -0.5: confidence += 30
            elif e_val < -0.2: confidence += 25
            elif e_val < 0: confidence += 20
            
            return min(confidence, 100)
        
        return 0
    
    def _show_strategy_summary(self):
        """显示策略总结"""
        print(f"\n📚 策略说明:")
        print(f"  🟢 看涨: E>0 + X>{self.x_threshold_long} + Y>{self.y_threshold_long} + 价格低于回归线")
        print(f"  🔴 看跌: (Y<{self.y_threshold_short} 或 X<{self.x_threshold_short}) + 价格高于回归线")
        print(f"  ⚪ 观望: 不满足以上条件时")
        
        print(f"\n⚠️ 风险提醒:")
        print(f"  • 严格执行止盈止损，不可贪婪或恐惧")
        print(f"  • 根据资金情况合理控制仓位大小")
        print(f"  • 市场有风险，投资需谨慎")

def main():
    """主函数"""
    # 支持命令行参数输入ticker和名称
    if len(sys.argv) > 1:
        ticker = sys.argv[1]
        name = sys.argv[2] if len(sys.argv) > 2 else ticker
        quico = QUICO(ticker, name)
    else:
        quico = QUICO()
    quico.get_signal()

if __name__ == "__main__":
    main()
