CREATE PROCEDURE `sp_updatecontroller_backup_full_y_20250720_120759`(
    IN tablename VARCHAR(64),
    OUT result_k DECIMAL(20,6)
)
BEGIN
    DECLARE col_exists INT DEFAULT 0;
    DECLARE v_start_time DATETIME DEFAULT NOW();

    -- 1. 首先更新midprice (调用sp_averagelineV3)
    SELECT CONCAT('开始更新midprice - 表: ', tablename, ' 时间: ', v_start_time) AS start_message;
    CALL sp_averagelineV3(tablename);
    SELECT 'midprice更新完成' AS midprice_status;

    -- 2. 检查controller列是否存在（原逻辑）
    SET @sql = CONCAT(
        'SELECT COUNT(*) INTO @col_exists FROM information_schema.COLUMNS ',
        'WHERE TABLE_SCHEMA=DATABASE() AND TABLE_NAME=''', tablename, ''' AND COLUMN_NAME=''controller'''
    );
    PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;
    SET col_exists = @col_exists;

    IF col_exists = 0 THEN
        SET @sql = CONCAT('ALTER TABLE `', tablename, '` ADD COLUMN `controller` INT');
        PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;
        SELECT 'controller列已添加' AS controller_status;
    END IF;

    -- 3. 更新controller字段（原逻辑）
    SET @sql = CONCAT(
        'UPDATE `',
        tablename,
        '` SET controller = CASE WHEN close < midprice THEN 0 WHEN close > midprice THEN 1 ELSE 3 END'
    );
    PREPARE stmt FROM @sql;
    EXECUTE stmt;
    DEALLOCATE PREPARE stmt;
    SELECT 'controller字段更新完成' AS controller_update_status;

    -- 4. 计算k值并存入OUT参数（原逻辑）
    SET @sql = CONCAT(
        'SELECT SUM(CASE WHEN controller = 1 THEN 1 ELSE 0 END) / NULLIF(COUNT(*), 0) INTO @k_value ',
        'FROM `', tablename, '`'
    );
    PREPARE stmt FROM @sql;
    EXECUTE stmt;
    DEALLOCATE PREPARE stmt;

    SET result_k = @k_value;

    -- 5. 返回统计信息
    SELECT
        CONCAT('更新完成 - 表: ', tablename) AS completion_message,
        CONCAT('k值: ', IFNULL(result_k, 0)) AS k_value_message,
        CONCAT('耗时: ', TIMESTAMPDIFF(SECOND, v_start_time, NOW()), ' 秒') AS duration;

END