#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
从SQLite数据库获取真实资金流数据更新test表
======================================

功能：
1. 从SQLite数据库(hk00023_20year.db)获取真实资金流数据
2. 连接MySQL数据库的test表
3. 根据开仓日期匹配并更新真实资金流数据

作者: Cosmoon NG
日期: 2025年7月
"""

import pymysql
import sqlite3
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

class TestTableSQLiteFlowUpdater:
    def __init__(self):
        """初始化更新器"""
        self.mysql_config = {
            'host': '************',
            'port': 3306,
            'database': 'finance',
            'user': 'root',
            'password': '',
            'charset': 'utf8mb4'
        }
        self.sqlite_db = "hk00023_20year.db"
        self.mysql_conn = None
        self.sqlite_conn = None
        
    def connect_databases(self):
        """连接数据库"""
        try:
            # 连接MySQL
            self.mysql_conn = pymysql.connect(**self.mysql_config)
            print(f"✅ 成功连接MySQL数据库: {self.mysql_config['host']}")
            
            # 连接SQLite
            self.sqlite_conn = sqlite3.connect(self.sqlite_db)
            print(f"✅ 成功连接SQLite数据库: {self.sqlite_db}")
            
            return True
        except Exception as e:
            print(f"❌ 连接数据库失败: {e}")
            return False
    
    def load_sqlite_money_flow_data(self):
        """从SQLite数据库加载真实资金流数据"""
        try:
            print("📊 从SQLite数据库加载真实资金流数据...")
            
            # 从SQLite读取资金流数据
            sql = """
                SELECT date, close, volume, 
                       money_flow_in, money_flow_out, net_money_flow,
                       money_flow_ratio, money_flow_intensity
                FROM hk00023 
                ORDER BY date
            """
            
            df = pd.read_sql_query(sql, self.sqlite_conn)
            df['date'] = pd.to_datetime(df['date'])
            
            print(f"✅ 成功加载SQLite资金流数据: {len(df)} 条记录")
            print(f"📅 数据期间: {df['date'].min().strftime('%Y-%m-%d')} 至 {df['date'].max().strftime('%Y-%m-%d')}")
            
            # 显示数据统计
            print(f"📊 资金流数据统计:")
            print(f"   • 平均流入: {df['money_flow_in'].mean():,.0f}")
            print(f"   • 平均流出: {df['money_flow_out'].mean():,.0f}")
            print(f"   • 平均流入比例: {df['money_flow_ratio'].mean():.3f}")
            
            return df
            
        except Exception as e:
            print(f"❌ 加载SQLite资金流数据失败: {e}")
            return None
    
    def check_test_table_structure(self):
        """检查test表结构"""
        try:
            cursor = self.mysql_conn.cursor()
            
            # 检查是否已有真实资金流列
            cursor.execute("SHOW COLUMNS FROM test LIKE '%流%'")
            existing_flow_columns = cursor.fetchall()
            
            if not existing_flow_columns:
                print("⚠️ test表没有真实资金流列，正在添加...")
                
                # 添加真实资金流列
                alter_statements = [
                    "ALTER TABLE test ADD COLUMN `真实流入` DECIMAL(15,2) DEFAULT 0 COMMENT '开仓日真实资金流入'",
                    "ALTER TABLE test ADD COLUMN `真实流出` DECIMAL(15,2) DEFAULT 0 COMMENT '开仓日真实资金流出'",
                    "ALTER TABLE test ADD COLUMN `净资金流` DECIMAL(15,2) DEFAULT 0 COMMENT '开仓日净资金流'",
                    "ALTER TABLE test ADD COLUMN `资金流比例` DECIMAL(8,6) DEFAULT 0.5 COMMENT '开仓日资金流入比例'",
                    "ALTER TABLE test ADD COLUMN `资金流强度` DECIMAL(8,4) DEFAULT 1.0 COMMENT '开仓日资金流强度'"
                ]
                
                for statement in alter_statements:
                    cursor.execute(statement)
                
                self.mysql_conn.commit()
                print("✅ 成功添加真实资金流列")
            else:
                print("✅ test表已有真实资金流列")
            
            return True
            
        except Exception as e:
            print(f"❌ 检查test表结构失败: {e}")
            return False
    
    def update_test_table_with_sqlite_flow(self, flow_df):
        """使用SQLite资金流数据更新test表"""
        try:
            print("💾 使用SQLite真实资金流数据更新test表...")
            
            cursor = self.mysql_conn.cursor()
            
            # 获取test表的交易记录
            cursor.execute("""
                SELECT 交易序号, 开仓日期, 股票代码, 交易方向, 策略区域, 净利润
                FROM test 
                ORDER BY 交易序号
            """)
            
            test_records = cursor.fetchall()
            print(f"📊 test表记录数: {len(test_records)}")
            
            # 创建日期到资金流的映射
            flow_df['date_str'] = flow_df['date'].dt.strftime('%Y-%m-%d')
            flow_dict = {}
            for _, row in flow_df.iterrows():
                flow_dict[row['date_str']] = {
                    'money_flow_in': row['money_flow_in'],
                    'money_flow_out': row['money_flow_out'],
                    'net_money_flow': row['net_money_flow'],
                    'money_flow_ratio': row['money_flow_ratio'],
                    'money_flow_intensity': row['money_flow_intensity']
                }
            
            # 更新每条交易记录
            update_count = 0
            matched_count = 0
            
            for record in test_records:
                trade_id, open_date, stock_code, direction, zone, profit = record
                
                # 只处理HK00023的记录
                if stock_code != 'HK00023':
                    continue
                
                matched_count += 1
                
                # 查找对应日期的资金流数据
                if open_date in flow_dict:
                    flow_data = flow_dict[open_date]
                    
                    # 更新数据库
                    update_sql = """
                        UPDATE test 
                        SET `真实流入` = %s,
                            `真实流出` = %s,
                            `净资金流` = %s,
                            `资金流比例` = %s,
                            `资金流强度` = %s
                        WHERE 交易序号 = %s
                    """
                    
                    cursor.execute(update_sql, (
                        float(flow_data['money_flow_in']),
                        float(flow_data['money_flow_out']),
                        float(flow_data['net_money_flow']),
                        float(flow_data['money_flow_ratio']),
                        float(flow_data['money_flow_intensity']),
                        trade_id
                    ))
                    
                    update_count += 1
                else:
                    print(f"⚠️ 未找到日期 {open_date} 的资金流数据")
            
            self.mysql_conn.commit()
            print(f"✅ 成功更新 {update_count}/{matched_count} 条HK00023记录的真实资金流数据")
            return True
            
        except Exception as e:
            print(f"❌ 更新test表真实资金流失败: {e}")
            return False
    
    def verify_and_display_results(self):
        """验证并显示更新结果"""
        try:
            cursor = self.mysql_conn.cursor()
            
            # 检查更新后的数据
            cursor.execute("""
                SELECT 交易序号, 开仓日期, 交易方向, 策略区域, 净利润,
                       `真实流入`, `真实流出`, `净资金流`, `资金流比例`, `资金流强度`
                FROM test 
                WHERE `真实流入` > 0 OR `真实流出` > 0
                ORDER BY 交易序号 
                LIMIT 30
            """)
            
            updated_data = cursor.fetchall()
            
            print("\n📊 test表前30条真实资金流数据:")
            print("="*160)
            print(f"{'序号':<4} {'开仓日期':<12} {'方向':<4} {'策略区域':<15} {'净利润':<8} {'真实流入':<12} {'真实流出':<12} {'净资金流':<12} {'流比例':<8} {'强度':<8}")
            print("-" * 160)
            
            for row in updated_data:
                trade_id, date, direction, zone, profit, flow_in, flow_out, net_flow, ratio, intensity = row
                print(f"{trade_id:<4} {date:<12} {direction:<4} {zone:<15} {int(profit):<8} "
                      f"{float(flow_in):<12,.0f} {float(flow_out):<12,.0f} {float(net_flow):<12,.0f} "
                      f"{float(ratio):<8.3f} {float(intensity):<8.2f}")
            
            # 统计信息
            cursor.execute("""
                SELECT 
                    COUNT(*) as total_records,
                    COUNT(CASE WHEN `真实流入` > 0 OR `真实流出` > 0 THEN 1 END) as flow_records,
                    AVG(`真实流入`) as avg_in,
                    AVG(`真实流出`) as avg_out,
                    AVG(`资金流比例`) as avg_ratio,
                    SUM(净利润) as total_profit
                FROM test
                WHERE `真实流入` > 0 OR `真实流出` > 0
            """)
            
            stats = cursor.fetchone()
            total, flow_records, avg_in, avg_out, avg_ratio, total_profit = stats
            
            print(f"\n📈 test表真实资金流数据统计:")
            print(f"   • 有真实资金流数据的记录: {flow_records:,}")
            print(f"   • 平均真实流入: {float(avg_in):,.0f}")
            print(f"   • 平均真实流出: {float(avg_out):,.0f}")
            print(f"   • 平均资金流比例: {float(avg_ratio):.3f}")
            print(f"   • 总盈亏: {int(total_profit):+,}港币")
            
            # 按策略区域分析
            cursor.execute("""
                SELECT 策略区域, 
                       COUNT(*) as count,
                       AVG(`资金流比例`) as avg_ratio,
                       AVG(`真实流入`) as avg_in,
                       AVG(`真实流出`) as avg_out,
                       SUM(净利润) as total_profit
                FROM test 
                WHERE `真实流入` > 0 OR `真实流出` > 0
                GROUP BY 策略区域
                ORDER BY total_profit DESC
            """)
            
            zone_stats = cursor.fetchall()
            
            print(f"\n📊 按策略区域的真实资金流分析:")
            print("-" * 100)
            for zone, count, avg_ratio, avg_in, avg_out, total_profit in zone_stats:
                print(f"• {zone}: {count}次, 平均流比例{float(avg_ratio):.3f}, "
                      f"平均流入{float(avg_in):,.0f}, 平均流出{float(avg_out):,.0f}, "
                      f"总盈亏{int(total_profit):+,}港币")
            
            # 按资金流比例分析
            cursor.execute("""
                SELECT 
                    CASE 
                        WHEN `资金流比例` > 0.7 THEN '强流入(>0.7)'
                        WHEN `资金流比例` > 0.5 THEN '中等流入(0.5-0.7)'
                        WHEN `资金流比例` >= 0.3 THEN '中等流出(0.3-0.5)'
                        ELSE '强流出(<0.3)'
                    END as flow_category,
                    COUNT(*) as count,
                    AVG(`资金流比例`) as avg_ratio,
                    SUM(净利润) as total_profit,
                    AVG(净利润) as avg_profit
                FROM test 
                WHERE `真实流入` > 0 OR `真实流出` > 0
                GROUP BY flow_category
                ORDER BY avg_ratio DESC
            """)
            
            flow_stats = cursor.fetchall()
            
            print(f"\n📈 按资金流比例分析:")
            print("-" * 80)
            for category, count, avg_ratio, total_profit, avg_profit in flow_stats:
                print(f"• {category}: {count}次, 平均比例{float(avg_ratio):.3f}, "
                      f"总盈亏{int(total_profit):+,}港币, 平均{int(avg_profit):+}港币")
            
            return True
            
        except Exception as e:
            print(f"❌ 验证结果失败: {e}")
            return False
    
    def close_connections(self):
        """关闭数据库连接"""
        if self.mysql_conn:
            self.mysql_conn.close()
            print("\n🔒 MySQL数据库连接已关闭")
        if self.sqlite_conn:
            self.sqlite_conn.close()
            print("🔒 SQLite数据库连接已关闭")

def main():
    """主函数"""
    print("🏦 从SQLite数据库获取真实资金流数据更新test表")
    print("="*70)
    
    # 创建更新器
    updater = TestTableSQLiteFlowUpdater()
    
    # 连接数据库
    if not updater.connect_databases():
        return
    
    # 加载SQLite资金流数据
    flow_df = updater.load_sqlite_money_flow_data()
    if flow_df is None:
        updater.close_connections()
        return
    
    # 检查test表结构
    if not updater.check_test_table_structure():
        updater.close_connections()
        return
    
    # 更新test表的真实资金流数据
    if not updater.update_test_table_with_sqlite_flow(flow_df):
        updater.close_connections()
        return
    
    # 验证并显示结果
    updater.verify_and_display_results()
    
    # 关闭连接
    updater.close_connections()
    
    print("\n🎉 test表真实资金流数据补全完成!")
    print("📊 新增列:")
    print("   • 真实流入: 开仓日真实资金流入")
    print("   • 真实流出: 开仓日真实资金流出")
    print("   • 净资金流: 开仓日净资金流")
    print("   • 资金流比例: 开仓日资金流入比例")
    print("   • 资金流强度: 开仓日资金流强度")
    print("💡 数据来源: SQLite数据库 (hk00023_20year.db)")

if __name__ == "__main__":
    main()
