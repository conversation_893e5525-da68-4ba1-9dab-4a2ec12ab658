#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import yfinance as yf
import pandas as pd
import numpy as np

def calculate_correct_xye():
    """正确计算0023.HK的XYE指标"""
    
    print("重新计算0023.HK的XYE指标")
    print("=" * 50)
    
    # 获取足够的历史数据
    ticker = yf.Ticker("0023.HK")
    hist = ticker.history(period="60d")
    
    if hist.empty:
        print("无法获取数据")
        return None
    
    # 1. 计算Y值 (价格在20日区间的相对位置)
    window = 20
    hist['high_20'] = hist['High'].rolling(window).max()
    hist['low_20'] = hist['Low'].rolling(window).min()
    
    # Y值公式: (当前价格 - 20日最低) / (20日最高 - 20日最低)
    hist['y_value'] = (hist['Close'] - hist['low_20']) / (hist['high_20'] - hist['low_20'])
    hist['y_value'] = hist['y_value'].fillna(0.5).clip(0, 1)
    
    # 2. 计算X值 (基于MFI的资金流强度)
    hist['typical_price'] = (hist['High'] + hist['Low'] + hist['Close']) / 3
    hist['money_flow'] = hist['typical_price'] * hist['Volume']
    hist['price_change'] = hist['typical_price'].diff()
    
    # 正负资金流
    hist['positive_mf'] = np.where(hist['price_change'] > 0, hist['money_flow'], 0)
    hist['negative_mf'] = np.where(hist['price_change'] < 0, hist['money_flow'], 0)
    
    # 14日资金流比率
    period = 14
    hist['positive_mf_14'] = hist['positive_mf'].rolling(period).sum()
    hist['negative_mf_14'] = hist['negative_mf'].rolling(period).sum()
    hist['money_flow_ratio'] = hist['positive_mf_14'] / (hist['negative_mf_14'] + 1e-10)
    
    # MFI (Money Flow Index) 0-100
    hist['mfi'] = 100 - (100 / (1 + hist['money_flow_ratio']))
    
    # X值: MFI归一化到0-1
    hist['x_value'] = hist['mfi'] / 100
    
    # 3. 计算E值 (Cosmoon核心公式)
    # E = (8 * X - 3) * Y - 3 * X + 1
    hist['e_value'] = (8 * hist['x_value'] - 3) * hist['y_value'] - 3 * hist['x_value'] + 1
    
    # 获取最新数据
    latest = hist.iloc[-1]
    
    print(f"今日数据 (2025-07-22):")
    print(f"收盘价: {latest['Close']:.2f} 港元")
    print(f"20日最高: {latest['high_20']:.2f} 港元")
    print(f"20日最低: {latest['low_20']:.2f} 港元")
    print(f"14日MFI: {latest['mfi']:.2f}")
    print()
    
    print("正确的XYE指标值:")
    print(f"Y值: {latest['y_value']:.4f}")
    print(f"X值: {latest['x_value']:.4f}")
    print(f"E值: {latest['e_value']:.4f}")
    print()
    
    print("计算验证:")
    y_calc = (latest['Close'] - latest['low_20']) / (latest['high_20'] - latest['low_20'])
    x_calc = latest['mfi'] / 100
    e_calc = (8 * x_calc - 3) * y_calc - 3 * x_calc + 1
    
    print(f"Y = ({latest['Close']:.2f} - {latest['low_20']:.2f}) / ({latest['high_20']:.2f} - {latest['low_20']:.2f}) = {y_calc:.4f}")
    print(f"X = {latest['mfi']:.2f} / 100 = {x_calc:.4f}")
    print(f"E = (8 * {x_calc:.4f} - 3) * {y_calc:.4f} - 3 * {x_calc:.4f} + 1 = {e_calc:.4f}")
    
    return {
        'y_value': latest['y_value'],
        'x_value': latest['x_value'],
        'e_value': latest['e_value'],
        'mfi': latest['mfi'],
        'close': latest['Close'],
        'high_20': latest['high_20'],
        'low_20': latest['low_20']
    }

def update_excel_with_correct_values():
    """用正确的XYE值更新Excel文件"""
    
    # 计算正确的XYE值
    xye_data = calculate_correct_xye()
    if xye_data is None:
        return
    
    # 读取现有Excel文件
    try:
        df = pd.read_excel('交易记录追踪0023HK.xlsx')
        
        # 更新最新记录的XYE值
        if len(df) > 0:
            df.loc[df.index[-1], 'Y值'] = xye_data['y_value']
            df.loc[df.index[-1], 'X值'] = xye_data['x_value']
            df.loc[df.index[-1], 'E值'] = xye_data['e_value']
            df.loc[df.index[-1], '备注'] = f"市场观察收盘价{xye_data['close']:.2f}港元(XYE已修正)"
            
            # 保存更新后的文件
            df.to_excel('交易记录追踪0023HK.xlsx', index=False)
            
            print()
            print("Excel文件已更新正确的XYE值")
            print("更新后的记录:")
            print(df.iloc[-1][['交易日期', 'Y值', 'X值', 'E值', '备注']].to_string())
        
    except Exception as e:
        print(f"更新Excel失败: {e}")

if __name__ == "__main__":
    calculate_correct_xye()
    update_excel_with_correct_values()
