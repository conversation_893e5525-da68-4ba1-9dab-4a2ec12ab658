-- 完整版存储过程：更新controller、Full_Y和E字段
DELIMITER $$

CREATE PROCEDURE `sp_updatecontroller_complete`(
    IN tablename VARCHAR(64),
    OUT result_k DECIMAL(20,6)
)
BEGIN
    DECLARE col_exists INT DEFAULT 0;
    DECLARE v_start_time DATETIME DEFAULT NOW();
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        GET DIAGNOSTICS CONDITION 1
            @sqlstate = RETURNED_SQLSTATE, 
            @errno = MYSQL_ERRNO, 
            @text = MESSAGE_TEXT;
        SELECT CONCAT('错误: ', @errno, ' - ', @text) AS error_message;
    END;

    START TRANSACTION;

    -- 1. 首先更新midprice (调用sp_averagelineV3)
    SELECT CONCAT('开始更新midprice - 表: ', tablename, ' 时间: ', v_start_time) AS start_message;
    CALL sp_averagelineV3(tablename);
    SELECT 'midprice更新完成' AS midprice_status;

    -- 2. 检查并添加controller列
    SET @sql = CONCAT(
        'SELECT COUNT(*) INTO @col_exists FROM information_schema.COLUMNS ',
        'WHERE TABLE_SCHEMA=DATABASE() AND TABLE_NAME=''', tablename, ''' AND COLUMN_NAME=''controller'''
    );
    PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;
    SET col_exists = @col_exists;

    IF col_exists = 0 THEN
        SET @sql = CONCAT('ALTER TABLE `', tablename, '` ADD COLUMN `controller` INT');
        PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;
        SELECT 'controller列已添加' AS controller_status;
    END IF;

    -- 3. 检查并添加Full_Y列
    SET @sql = CONCAT(
        'SELECT COUNT(*) INTO @col_exists FROM information_schema.COLUMNS ',
        'WHERE TABLE_SCHEMA=DATABASE() AND TABLE_NAME=''', tablename, ''' AND COLUMN_NAME=''Full_Y'''
    );
    PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;
    SET col_exists = @col_exists;

    IF col_exists = 0 THEN
        SET @sql = CONCAT('ALTER TABLE `', tablename, '` ADD COLUMN `Full_Y` DECIMAL(10,3)');
        PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;
        SELECT 'Full_Y列已添加' AS full_y_status;
    END IF;

    -- 4. 检查并添加E列
    SET @sql = CONCAT(
        'SELECT COUNT(*) INTO @col_exists FROM information_schema.COLUMNS ',
        'WHERE TABLE_SCHEMA=DATABASE() AND TABLE_NAME=''', tablename, ''' AND COLUMN_NAME=''E'''
    );
    PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;
    SET col_exists = @col_exists;

    IF col_exists = 0 THEN
        SET @sql = CONCAT('ALTER TABLE `', tablename, '` ADD COLUMN `E` DECIMAL(10,6)');
        PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;
        SELECT 'E列已添加' AS e_status;
    END IF;

    -- 5. 更新controller字段
    SET @sql = CONCAT(
        'UPDATE `', tablename, '` ',
        'SET controller = CASE ',
        '    WHEN close < midprice THEN 0 ',
        '    WHEN close > midprice THEN 1 ',
        '    ELSE 3 ',
        'END'
    );
    PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;
    SELECT 'controller字段更新完成' AS controller_update_status;

    -- 6. 更新Full_Y字段 (假设Full_Y = (high + low + close) / 3)
    SET @sql = CONCAT(
        'UPDATE `', tablename, '` ',
        'SET Full_Y = ROUND((high + low + close) / 3, 3) ',
        'WHERE high IS NOT NULL AND low IS NOT NULL AND close IS NOT NULL'
    );
    PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;
    SELECT 'Full_Y字段更新完成' AS full_y_update_status;

    -- 7. 更新E字段 (假设E = (close - open) / open)
    SET @sql = CONCAT(
        'UPDATE `', tablename, '` ',
        'SET E = ROUND((close - open) / NULLIF(open, 0), 6) ',
        'WHERE open IS NOT NULL AND close IS NOT NULL AND open != 0'
    );
    PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;
    SELECT 'E字段更新完成' AS e_update_status;

    -- 8. 计算k值并存入OUT参数
    SET @sql = CONCAT(
        'SELECT SUM(CASE WHEN controller = 1 THEN 1 ELSE 0 END) / NULLIF(COUNT(*), 0) INTO @k_value ',
        'FROM `', tablename, '`'
    );
    PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;
    SET result_k = @k_value;

    COMMIT;

    -- 9. 返回统计信息
    SELECT
        CONCAT('更新完成 - 表: ', tablename) AS completion_message,
        CONCAT('k值: ', IFNULL(result_k, 0)) AS k_value_message,
        CONCAT('耗时: ', TIMESTAMPDIFF(SECOND, v_start_time, NOW()), ' 秒') AS duration;

    -- 10. 显示更新统计
    SET @sql = CONCAT(
        'SELECT ',
        '    COUNT(*) as total_records, ',
        '    SUM(CASE WHEN controller = 0 THEN 1 ELSE 0 END) as controller_0, ',
        '    SUM(CASE WHEN controller = 1 THEN 1 ELSE 0 END) as controller_1, ',
        '    SUM(CASE WHEN controller = 3 THEN 1 ELSE 0 END) as controller_3, ',
        '    COUNT(CASE WHEN Full_Y IS NOT NULL THEN 1 END) as full_y_updated, ',
        '    COUNT(CASE WHEN E IS NOT NULL THEN 1 END) as e_updated ',
        'FROM `', tablename, '`'
    );
    PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;

END$$

DELIMITER ;
