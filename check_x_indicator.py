#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查X指标
========
查看数据库中是否有X相关的指标
"""

import mysql.connector
import pandas as pd
import warnings
warnings.filterwarnings('ignore')

def check_x_indicator():
    """检查X指标"""
    
    # 数据库配置
    db_config = {
        'host': 'localhost',
        'database': 'finance',
        'user': 'root',
        'password': '12345678',
        'charset': 'utf8mb4'
    }
    
    try:
        print("🔍 检查X指标和相关字段")
        print("=" * 50)
        
        connection = mysql.connector.connect(**db_config)
        cursor = connection.cursor()
        
        # 1. 检查hkhsi50表的所有字段
        print("📊 hkhsi50表的所有字段:")
        cursor.execute("DESCRIBE hkhsi50")
        columns = cursor.fetchall()
        
        for col in columns:
            col_name = col[0]
            col_type = col[1]
            print(f"   • {col_name} - {col_type}")
        
        # 2. 查找包含X的字段
        x_columns = [col[0] for col in columns if 'x' in col[0].lower() or 'X' in col[0]]
        if x_columns:
            print(f"\n🎯 包含X的字段:")
            for col in x_columns:
                print(f"   • {col}")
        else:
            print(f"\n⚠️ 没有找到包含X的字段")
        
        # 3. 检查是否有其他可能的X指标
        print(f"\n📈 检查可能的X相关指标:")
        
        # 查看最新几条数据
        cursor.execute("""
            SELECT Date, Close, new_controller, new_Full_Y, y_probability
            FROM hkhsi50 
            ORDER BY Date DESC 
            LIMIT 5
        """)
        
        recent_data = cursor.fetchall()
        print(f"最新5条数据:")
        print("日期          | 收盘价    | controller | Full_Y    | y_prob")
        print("-" * 65)
        for row in recent_data:
            date_str = str(row[0])
            close_val = float(row[1]) if row[1] is not None else 0.0
            controller_val = row[2] if row[2] is not None else 0
            full_y_val = float(row[3]) if row[3] is not None else 0.0
            y_prob_val = float(row[4]) if row[4] is not None else 0.0
            print(f"{date_str} | {close_val:9.2f} | {controller_val:10d} | {full_y_val:9.6f} | {y_prob_val:6.4f}")
        
        # 4. 如果没有X，我们可以创建X指标
        print(f"\n💡 如果您需要X指标，我可以基于以下方式创建:")
        print(f"   1. X = 1 - Full_Y (Full_Y的反向指标)")
        print(f"   2. X = controller的反向 (0变1，1变0)")
        print(f"   3. X = 基于价格偏离的自定义指标")
        print(f"   4. X = 基于y_probability的变换")
        
        # 5. 计算一些可能的X指标示例
        print(f"\n🧮 计算示例X指标:")
        
        # 加载数据计算X
        query = """
            SELECT Date, Close, new_controller, new_Full_Y, y_probability, new_midprice
            FROM hkhsi50 
            WHERE Date >= '2024-01-01'
            ORDER BY Date ASC
            LIMIT 100
        """
        
        df = pd.read_sql(query, connection)
        
        # 计算各种X指标
        df['X1'] = 1 - df['new_Full_Y']  # Full_Y的反向
        df['X2'] = 1 - df['new_controller']  # controller的反向
        df['X3'] = (df['Close'] - df['new_midprice']) / df['new_midprice']  # 价格偏离
        df['X4'] = 1 - df['y_probability']  # y_probability的反向
        
        print(f"   • X1 (1-Full_Y) 范围: {df['X1'].min():.6f} ~ {df['X1'].max():.6f}")
        print(f"   • X2 (1-controller) 范围: {df['X2'].min():.0f} ~ {df['X2'].max():.0f}")
        print(f"   • X3 (价格偏离) 范围: {df['X3'].min():.6f} ~ {df['X3'].max():.6f}")
        print(f"   • X4 (1-y_probability) 范围: {df['X4'].min():.6f} ~ {df['X4'].max():.6f}")
        
        # 6. 显示X指标的统计特性
        print(f"\n📊 X指标统计特性:")
        for col in ['X1', 'X2', 'X3', 'X4']:
            mean_val = df[col].mean()
            std_val = df[col].std()
            print(f"   • {col}: 均值={mean_val:.6f}, 标准差={std_val:.6f}")
        
        # 7. 分析哪个X指标最有用
        print(f"\n🎯 X指标实用性分析:")
        
        # X1 (1-Full_Y) - 弱势比例
        x1_low = (df['X1'] < df['X1'].quantile(0.3)).sum()
        x1_high = (df['X1'] > df['X1'].quantile(0.7)).sum()
        print(f"   • X1 (弱势比例): 低位{x1_low}天, 高位{x1_high}天")
        
        # X2 (1-controller) - 弱势信号
        x2_weak = (df['X2'] == 1).sum()
        print(f"   • X2 (弱势信号): 弱势{x2_weak}天 ({x2_weak/len(df)*100:.1f}%)")
        
        # X3 (价格偏离) - 负偏离
        x3_negative = (df['X3'] < 0).sum()
        print(f"   • X3 (负偏离): 负偏离{x3_negative}天 ({x3_negative/len(df)*100:.1f}%)")
        
        # X4 (1-y_probability) - 压价概率
        x4_high = (df['X4'] > 0.5).sum()
        print(f"   • X4 (压价概率): 压价倾向{x4_high}天 ({x4_high/len(df)*100:.1f}%)")
        
        connection.close()
        
        print(f"\n💡 建议:")
        print(f"   • 如果您想要弱势指标，推荐使用 X1 (1-Full_Y)")
        print(f"   • 如果您想要价格偏离指标，推荐使用 X3")
        print(f"   • 如果您想要压价概率，推荐使用 X4 (1-y_probability)")
        
        return True
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False

def main():
    """主函数"""
    check_x_indicator()

if __name__ == "__main__":
    main()
