#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
东亚银行(0023.HK)交易记录追踪系统
================================

基于"交易记录追踪0023.HK .xlsx"的逻辑严格执行
实现完整的交易记录、追踪和分析功能

特点：
1. 严格按照Excel表格结构记录交易
2. 实时追踪持仓状态和盈亏
3. 完整的交易历史记录
4. 自动计算收益率和风险指标
5. 生成详细的交易报告

作者: Cosmoon NG
日期: 2025年7月
"""

import yfinance as yf
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import matplotlib.pyplot as plt
from scipy import stats
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class TradingTracker0023HK:
    """东亚银行交易记录追踪系统"""

    def __init__(self):
        """初始化交易追踪系统"""
        self.symbol = "0023.HK"
        self.initial_capital = 10000  # 初始资金
        self.monthly_addition = 3000  # 每月追加资金

        # 交易参数（严格执行）
        self.take_profit_long = 0.025   # 多头止盈 2.5%
        self.stop_loss_long = 0.015     # 多头止损 1.5%
        self.take_profit_short = 0.015  # 空头止盈 1.5%
        self.stop_loss_short = 0.025    # 空头止损 2.5%

        # 当前状态
        self.position = 0  # 当前持仓：1=多头，-1=空头，0=空仓
        self.current_price = 0  # 当前持仓价格
        self.current_capital = self.initial_capital

        # 交易记录表格结构（基于Excel文件）
        self.trading_records = pd.DataFrame(columns=[
            '交易日期',      # 交易发生日期
            '交易类型',      # 开仓/平仓
            '交易方向',      # 多头/空头
            '交易价格',      # 成交价格
            '持仓数量',      # 持仓股数
            '交易金额',      # 交易总金额
            '手续费',        # 交易手续费
            '净交易额',      # 扣除手续费后金额
            '持仓成本',      # 平均持仓成本
            '当前市值',      # 当前持仓市值
            '浮动盈亏',      # 未实现盈亏
            '实现盈亏',      # 已实现盈亏
            '累计盈亏',      # 累计总盈亏
            '账户余额',      # 当前账户余额
            '总资产',        # 总资产价值
            '收益率',        # 当日收益率
            '累计收益率',    # 累计收益率
            'Y值',          # 技术指标Y值
            'X值',          # 技术指标X值
            'E值',          # 技术指标E值
            '信号强度',      # 交易信号强度
            '风险等级',      # 当前风险等级
            '备注'          # 交易备注
        ])

        print(f"🎯 {self.symbol}交易记录追踪系统已启动")
        print("📊 严格按照Excel表格逻辑执行交易记录")

    def load_market_data(self, period="2y"):
        """加载市场数据"""
        print(f"\n📊 加载{self.symbol}市场数据...")
        try:
            # 获取历史数据
            end_date = datetime.now()
            start_date = end_date - timedelta(days=int(period[:-1])*365)

            ticker = yf.Ticker(self.symbol)
            hist_data = ticker.history(start=start_date, end=end_date)

            if hist_data.empty:
                raise ValueError(f"无法获取{self.symbol}的数据")

            # 转换为标准格式
            self.market_data = pd.DataFrame({
                'date': hist_data.index,
                'open': hist_data['Open'],
                'high': hist_data['High'],
                'low': hist_data['Low'],
                'close': hist_data['Close'],
                'volume': hist_data['Volume']
            })

            # 数据清理
            self.market_data = self.market_data.dropna()
            self.market_data = self.market_data.sort_values('date').reset_index(drop=True)

            # 计算技术指标
            self.calculate_technical_indicators()

            print(f"✅ 成功加载 {len(self.market_data)} 条数据")
            print(f"   数据范围: {self.market_data['date'].min().date()} 至 {self.market_data['date'].max().date()}")
            print(f"   当前价格: {self.market_data['close'].iloc[-1]:.2f} 港元")

        except Exception as e:
            print(f"❌ 数据加载失败: {e}")
            raise

    def calculate_technical_indicators(self):
        """计算技术指标（XYE系统）"""
        print("   计算XYE技术指标...")

        # Y值：价格在20日区间的位置
        window = 20
        self.market_data['high_20'] = self.market_data['high'].rolling(window).max()
        self.market_data['low_20'] = self.market_data['low'].rolling(window).min()
        self.market_data['y_value'] = (self.market_data['close'] - self.market_data['low_20']) / (self.market_data['high_20'] - self.market_data['low_20'])
        self.market_data['y_value'] = self.market_data['y_value'].fillna(0.5).clip(0, 1)

        # X值：资金流强度指标
        self.market_data['typical_price'] = (self.market_data['high'] + self.market_data['low'] + self.market_data['close']) / 3
        self.market_data['money_flow'] = self.market_data['typical_price'] * self.market_data['volume']
        self.market_data['price_change'] = self.market_data['typical_price'].diff()

        # 正负资金流
        self.market_data['positive_mf'] = np.where(self.market_data['price_change'] > 0, self.market_data['money_flow'], 0)
        self.market_data['negative_mf'] = np.where(self.market_data['price_change'] < 0, self.market_data['money_flow'], 0)

        # 14日资金流比率
        period = 14
        self.market_data['positive_mf_14'] = self.market_data['positive_mf'].rolling(period).sum()
        self.market_data['negative_mf_14'] = self.market_data['negative_mf'].rolling(period).sum()
        self.market_data['money_flow_ratio'] = self.market_data['positive_mf_14'] / (self.market_data['negative_mf_14'] + 1e-10)

        # MFI和X值
        self.market_data['mfi'] = 100 - (100 / (1 + self.market_data['money_flow_ratio']))
        self.market_data['x_value'] = self.market_data['mfi'] / 100

        # E值：Cosmoon核心公式
        self.market_data['e_value'] = (8 * self.market_data['x_value'] - 3) * self.market_data['y_value'] - 3 * self.market_data['x_value'] + 1

        # 计算回归线
        self.market_data['i'] = range(1, len(self.market_data) + 1)
        if len(self.market_data) > 60:
            slope, intercept, r_value, _, _ = stats.linregress(self.market_data['i'], self.market_data['close'])
            self.market_data['regression_line'] = intercept + slope * self.market_data['i']
            self.market_data['price_position'] = (self.market_data['close'] - self.market_data['regression_line']) / self.market_data['regression_line']

        print(f"   XYE指标计算完成")

    def generate_trading_signal(self, row):
        """生成交易信号"""
        # 多头信号
        if (row['e_value'] > 0 and
            row['x_value'] > 0.45 and
            row['y_value'] > 0.45 and
            row['price_position'] < 0):
            return 1, "强烈买入"

        # 空头信号
        elif ((row['y_value'] < 0.3 or row['x_value'] < 0.3 or
               (row['x_value'] > 0.45 and row['y_value'] < 0.35) or
               (row['x_value'] < 0.45 and row['y_value'] > 0.35)) and
              row['price_position'] > 0):
            return -1, "强烈卖出"

        else:
            return 0, "观望"

    def calculate_risk_level(self, row):
        """计算风险等级"""
        volatility = abs(row['price_position'])

        if volatility < 0.02:
            return "低风险"
        elif volatility < 0.05:
            return "中风险"
        elif volatility < 0.1:
            return "高风险"
        else:
            return "极高风险"

    def add_trading_record(self, date, trade_type, direction, price, quantity=0,
                          realized_pnl=0, note=""):
        """添加交易记录到Excel表格结构"""

        # 获取当前技术指标
        current_data = self.market_data[self.market_data['date'].dt.date == date.date()]
        if len(current_data) == 0:
            # 如果没有当天数据，使用最新数据
            current_data = self.market_data.iloc[-1]
            y_val, x_val, e_val = current_data['y_value'], current_data['x_value'], current_data['e_value']
        else:
            current_data = current_data.iloc[-1]
            y_val, x_val, e_val = current_data['y_value'], current_data['x_value'], current_data['e_value']

        # 计算交易相关数据
        trade_amount = price * quantity if quantity > 0 else 0
        commission = trade_amount * 0.001  # 0.1%手续费
        net_amount = trade_amount - commission

        # 计算当前持仓和盈亏
        if self.position != 0:
            current_market_value = price * abs(quantity) if quantity > 0 else 0
            unrealized_pnl = (price - self.current_price) * abs(quantity) if self.position == 1 else (self.current_price - price) * abs(quantity)
        else:
            current_market_value = 0
            unrealized_pnl = 0

        # 更新账户余额
        if trade_type == "开仓":
            self.current_capital -= net_amount
        elif trade_type == "平仓":
            self.current_capital += net_amount + realized_pnl

        # 计算总资产
        total_assets = self.current_capital + current_market_value

        # 计算收益率
        if len(self.trading_records) > 0:
            prev_total = self.trading_records['总资产'].iloc[-1]
            daily_return = (total_assets - prev_total) / prev_total * 100
        else:
            daily_return = 0

        cumulative_return = (total_assets - self.initial_capital) / self.initial_capital * 100

        # 生成交易信号
        signal, signal_strength = self.generate_trading_signal(current_data)
        risk_level = self.calculate_risk_level(current_data)

        # 创建新的交易记录
        new_record = {
            '交易日期': date,
            '交易类型': trade_type,
            '交易方向': direction,
            '交易价格': price,
            '持仓数量': quantity,
            '交易金额': trade_amount,
            '手续费': commission,
            '净交易额': net_amount,
            '持仓成本': self.current_price if self.position != 0 else 0,
            '当前市值': current_market_value,
            '浮动盈亏': unrealized_pnl,
            '实现盈亏': realized_pnl,
            '累计盈亏': realized_pnl + unrealized_pnl,
            '账户余额': self.current_capital,
            '总资产': total_assets,
            '收益率': daily_return,
            '累计收益率': cumulative_return,
            'Y值': y_val,
            'X值': x_val,
            'E值': e_val,
            '信号强度': signal_strength,
            '风险等级': risk_level,
            '备注': note
        }

        # 添加到记录表
        self.trading_records = pd.concat([self.trading_records, pd.DataFrame([new_record])], ignore_index=True)

        print(f"📝 交易记录已添加: {date.strftime('%Y-%m-%d')} | {trade_type} | {direction} | {price:.2f}")

    def execute_trading_strategy(self):
        """执行交易策略并记录"""
        print(f"\n🎯 开始执行{self.symbol}交易策略...")

        # 从第60天开始交易（确保有足够历史数据）
        for i in range(60, len(self.market_data)):
            row = self.market_data.iloc[i]
            date = row['date']
            price = row['close']

            # 每月增加资金
            self.add_monthly_capital(date)

            # 检查止盈止损
            if self.position != 0:
                self.check_stop_conditions(row)

            # 如果空仓，检查开仓信号
            if self.position == 0:
                signal, signal_strength = self.generate_trading_signal(row)

                if signal == 1:  # 多头信号
                    quantity = int(self.current_capital * 0.8 / price)  # 80%仓位
                    if quantity > 0:
                        self.position = 1
                        self.current_price = price
                        self.add_trading_record(date, "开仓", "多头", price, quantity, 0, f"XYE多头信号: {signal_strength}")

                elif signal == -1:  # 空头信号
                    quantity = int(self.current_capital * 0.8 / price)  # 80%仓位
                    if quantity > 0:
                        self.position = -1
                        self.current_price = price
                        self.add_trading_record(date, "开仓", "空头", price, quantity, 0, f"XYE空头信号: {signal_strength}")

        print(f"✅ 交易策略执行完成，共生成 {len(self.trading_records)} 条交易记录")

    def check_stop_conditions(self, row):
        """检查止盈止损条件"""
        price = row['close']
        date = row['date']

        if self.position == 1:  # 多头持仓
            profit_ratio = (price - self.current_price) / self.current_price

            if profit_ratio >= self.take_profit_long:  # 止盈
                realized_pnl = profit_ratio * self.current_capital * 0.8
                self.add_trading_record(date, "平仓", "多头止盈", price, 0, realized_pnl, f"止盈平仓: +{profit_ratio*100:.2f}%")
                self.position = 0

            elif profit_ratio <= -self.stop_loss_long:  # 止损
                realized_pnl = profit_ratio * self.current_capital * 0.8
                self.add_trading_record(date, "平仓", "多头止损", price, 0, realized_pnl, f"止损平仓: {profit_ratio*100:.2f}%")
                self.position = 0

        elif self.position == -1:  # 空头持仓
            profit_ratio = (self.current_price - price) / self.current_price

            if profit_ratio >= self.take_profit_short:  # 止盈
                realized_pnl = profit_ratio * self.current_capital * 0.8
                self.add_trading_record(date, "平仓", "空头止盈", price, 0, realized_pnl, f"止盈平仓: +{profit_ratio*100:.2f}%")
                self.position = 0

            elif profit_ratio <= -self.stop_loss_short:  # 止损
                realized_pnl = profit_ratio * self.current_capital * 0.8
                self.add_trading_record(date, "平仓", "空头止损", price, 0, realized_pnl, f"止损平仓: {profit_ratio*100:.2f}%")
                self.position = 0

    def add_monthly_capital(self, date):
        """每月增加资金"""
        last_month = getattr(self, 'last_month', None)
        current_month = date.replace(day=1)

        if last_month is None or current_month > last_month:
            self.last_month = current_month
            self.current_capital += self.monthly_addition
            self.add_trading_record(date, "资金注入", "追加资金", 0, 0, 0, f"每月追加资金: {self.monthly_addition}")

    def save_trading_records(self):
        """保存交易记录到Excel文件"""
        filename = f"交易记录追踪{self.symbol.replace('.', '_')}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"

        try:
            # 格式化数据
            df_save = self.trading_records.copy()

            # 格式化日期
            df_save['交易日期'] = pd.to_datetime(df_save['交易日期']).dt.strftime('%Y-%m-%d')

            # 格式化数值
            numeric_cols = ['交易价格', '交易金额', '手续费', '净交易额', '持仓成本',
                           '当前市值', '浮动盈亏', '实现盈亏', '累计盈亏', '账户余额',
                           '总资产', '收益率', '累计收益率', 'Y值', 'X值', 'E值']

            for col in numeric_cols:
                if col in df_save.columns:
                    df_save[col] = pd.to_numeric(df_save[col], errors='coerce').round(4)

            # 保存到Excel
            df_save.to_excel(filename, index=False, sheet_name='交易记录')

            print(f"💾 交易记录已保存到: {filename}")
            return filename

        except Exception as e:
            print(f"❌ 保存交易记录失败: {e}")
            return None

    def analyze_trading_performance(self):
        """分析交易表现"""
        print(f"\n📊 {self.symbol}交易表现分析")
        print("=" * 60)

        if len(self.trading_records) == 0:
            print("❌ 没有交易记录可分析")
            return

        # 基本统计
        total_trades = len(self.trading_records[self.trading_records['交易类型'] == '开仓'])
        profit_trades = len(self.trading_records[self.trading_records['实现盈亏'] > 0])
        loss_trades = len(self.trading_records[self.trading_records['实现盈亏'] < 0])

        print(f"📈 交易统计:")
        print(f"   总交易次数: {total_trades}")
        print(f"   盈利交易: {profit_trades}")
        print(f"   亏损交易: {loss_trades}")
        if total_trades > 0:
            print(f"   胜率: {profit_trades/(profit_trades+loss_trades)*100:.2f}%")

        # 收益分析
        if len(self.trading_records) > 0:
            final_assets = self.trading_records['总资产'].iloc[-1]
            total_return = self.trading_records['累计收益率'].iloc[-1]
            max_return = self.trading_records['累计收益率'].max()
            min_return = self.trading_records['累计收益率'].min()

            print(f"\n💰 收益分析:")
            print(f"   初始资金: {self.initial_capital:,.2f} 港元")
            print(f"   最终资产: {final_assets:,.2f} 港元")
            print(f"   累计收益率: {total_return:.2f}%")
            print(f"   最高收益率: {max_return:.2f}%")
            print(f"   最大回撤: {min_return:.2f}%")

        # 风险分析
        realized_pnl = self.trading_records['实现盈亏']
        if len(realized_pnl[realized_pnl != 0]) > 0:
            avg_profit = realized_pnl[realized_pnl > 0].mean()
            avg_loss = realized_pnl[realized_pnl < 0].mean()
            max_profit = realized_pnl.max()
            max_loss = realized_pnl.min()

            print(f"\n⚠️ 风险分析:")
            print(f"   平均盈利: {avg_profit:.2f} 港元")
            print(f"   平均亏损: {avg_loss:.2f} 港元")
            print(f"   最大盈利: {max_profit:.2f} 港元")
            print(f"   最大亏损: {max_loss:.2f} 港元")
            if avg_loss != 0:
                print(f"   盈亏比: {abs(avg_profit/avg_loss):.2f}")

        # XYE信号分析
        signal_counts = self.trading_records['信号强度'].value_counts()
        print(f"\n🎯 XYE信号分析:")
        for signal, count in signal_counts.items():
            print(f"   {signal}: {count}次")

    def create_performance_charts(self):
        """创建表现分析图表"""
        if len(self.trading_records) == 0:
            print("❌ 没有数据可绘制图表")
            return

        try:
            # 创建图表
            fig, axes = plt.subplots(2, 2, figsize=(16, 12))
            fig.suptitle(f'{self.symbol} 交易表现分析', fontsize=16, fontweight='bold')

            # 1. 资产增长曲线
            ax1 = axes[0, 0]
            dates = pd.to_datetime(self.trading_records['交易日期'])
            assets = self.trading_records['总资产']
            ax1.plot(dates, assets, 'b-', linewidth=2, label='总资产')
            ax1.axhline(y=self.initial_capital, color='r', linestyle='--', alpha=0.7, label='初始资金')
            ax1.set_title('资产增长曲线')
            ax1.set_ylabel('资产价值 (港元)')
            ax1.legend()
            ax1.grid(True, alpha=0.3)

            # 2. 累计收益率
            ax2 = axes[0, 1]
            returns = self.trading_records['累计收益率']
            ax2.plot(dates, returns, 'g-', linewidth=2)
            ax2.axhline(y=0, color='r', linestyle='--', alpha=0.7)
            ax2.set_title('累计收益率')
            ax2.set_ylabel('收益率 (%)')
            ax2.grid(True, alpha=0.3)

            # 3. 交易盈亏分布
            ax3 = axes[1, 0]
            realized_pnl = self.trading_records['实现盈亏']
            realized_pnl = realized_pnl[realized_pnl != 0]  # 排除0值
            if len(realized_pnl) > 0:
                ax3.hist(realized_pnl, bins=20, color='blue', alpha=0.7, edgecolor='black')
                ax3.axvline(x=0, color='red', linestyle='--', linewidth=2)
                ax3.set_title('交易盈亏分布')
                ax3.set_xlabel('盈亏金额 (港元)')
                ax3.set_ylabel('频次')
                ax3.grid(True, alpha=0.3)

            # 4. XYE指标走势
            ax4 = axes[1, 1]
            y_values = self.trading_records['Y值']
            x_values = self.trading_records['X值']
            e_values = self.trading_records['E值']

            ax4.plot(dates, y_values, 'g-', alpha=0.8, label='Y值')
            ax4.plot(dates, x_values, 'r-', alpha=0.8, label='X值')
            ax4.axhline(y=0.45, color='orange', linestyle='--', alpha=0.5, label='0.45阈值')
            ax4.axhline(y=0.3, color='purple', linestyle='--', alpha=0.5, label='0.3阈值')
            ax4.set_title('XYE指标走势')
            ax4.set_ylabel('指标值')
            ax4.legend()
            ax4.grid(True, alpha=0.3)

            # 调整布局并保存
            plt.tight_layout()
            chart_filename = f'{self.symbol.replace(".", "_")}_交易分析图表_{datetime.now().strftime("%Y%m%d_%H%M%S")}.png'
            plt.savefig(chart_filename, dpi=300, bbox_inches='tight')
            plt.close()

            print(f"📊 分析图表已保存到: {chart_filename}")
            return chart_filename

        except Exception as e:
            print(f"❌ 图表创建失败: {e}")
            return None

    def generate_trading_report(self):
        """生成完整的交易报告"""
        print(f"\n📋 生成{self.symbol}交易报告...")

        # 保存交易记录
        excel_file = self.save_trading_records()

        # 分析交易表现
        self.analyze_trading_performance()

        # 创建图表
        chart_file = self.create_performance_charts()

        # 生成总结报告
        if len(self.trading_records) > 0:
            print(f"\n📄 交易报告总结:")
            print(f"   📊 Excel记录文件: {excel_file}")
            print(f"   📈 分析图表文件: {chart_file}")
            print(f"   📝 交易记录条数: {len(self.trading_records)}")
            print(f"   💰 最终资产价值: {self.trading_records['总资产'].iloc[-1]:,.2f} 港元")
            print(f"   📈 累计收益率: {self.trading_records['累计收益率'].iloc[-1]:.2f}%")

        return excel_file, chart_file

def main():
    """主函数 - 严格执行交易记录追踪"""
    print("🎯 东亚银行(0023.HK)交易记录追踪系统")
    print("严格按照Excel表格逻辑执行")
    print("=" * 70)

    try:
        # 创建交易追踪器
        tracker = TradingTracker0023HK()

        # 加载市场数据
        tracker.load_market_data(period="2y")

        # 执行交易策略并记录
        tracker.execute_trading_strategy()

        # 生成完整报告
        tracker.generate_trading_report()

    except Exception as e:
        print(f"\n❌ 程序运行失败: {e}")
        import traceback
        traceback.print_exc()
    else:
        print(f"\n✅ 交易记录追踪系统运行完成")
        print("💡 严格按照Excel表格结构记录所有交易数据")

if __name__ == "__main__":
    main()
