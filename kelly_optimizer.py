#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
凯利公式优化器
为东亚银行策略计算最优赔率和仓位
"""

import numpy as np
import matplotlib.pyplot as plt
import pandas as pd

class KellyOptimizer:
    def __init__(self):
        """初始化凯利优化器"""
        self.win_rate_long = 0.46   # 看涨胜率 (基于历史回测)
        self.win_rate_short = 0.45  # 看跌胜率
        
    def calculate_optimal_odds(self, win_rate):
        """计算最优赔率"""
        print(f"📊 凯利公式最优赔率计算")
        print(f"胜率: {win_rate*100:.1f}%")
        print("-" * 40)
        
        # 测试不同赔率下的凯利值
        odds_range = np.arange(1.0, 3.0, 0.1)
        kelly_values = []
        
        for odds in odds_range:
            # 凯利公式: f = (bp - q) / b
            # 其中 b = 赔率, p = 胜率, q = 败率
            kelly_f = (odds * win_rate - (1 - win_rate)) / odds
            kelly_values.append(max(0, kelly_f))  # 凯利值不能为负
        
        # 找到最优赔率
        max_kelly_idx = np.argmax(kelly_values)
        optimal_odds = odds_range[max_kelly_idx]
        max_kelly = kelly_values[max_kelly_idx]
        
        print(f"🎯 最优赔率: 1:{optimal_odds:.1f}")
        print(f"📈 最大凯利值: {max_kelly:.3f}")
        print(f"💰 建议仓位: {max_kelly*100:.1f}%")
        
        return optimal_odds, max_kelly
    
    def analyze_current_setup(self):
        """分析当前止盈止损设置"""
        print("🔍 当前策略分析")
        print("=" * 50)
        
        # 当前设置
        current_tp_long = 0.012  # 1.2%
        current_sl_long = 0.008  # 0.8%
        current_odds_long = current_tp_long / current_sl_long  # 1.5
        
        current_tp_short = 0.008  # 0.8%
        current_sl_short = 0.012  # 1.2%
        current_odds_short = current_tp_short / current_sl_short  # 0.67
        
        print(f"📈 看涨策略:")
        print(f"  止盈: {current_tp_long*100:.1f}% | 止损: {current_sl_long*100:.1f}%")
        print(f"  当前赔率: 1:{current_odds_long:.2f}")
        
        # 计算当前凯利值
        kelly_long = (current_odds_long * self.win_rate_long - (1 - self.win_rate_long)) / current_odds_long
        print(f"  凯利值: {kelly_long:.3f}")
        print(f"  建议仓位: {max(0, kelly_long)*100:.1f}%")
        
        print(f"\n📉 看跌策略:")
        print(f"  止盈: {current_tp_short*100:.1f}% | 止损: {current_sl_short*100:.1f}%")
        print(f"  当前赔率: 1:{current_odds_short:.2f}")
        
        # 计算当前凯利值
        kelly_short = (current_odds_short * self.win_rate_short - (1 - self.win_rate_short)) / current_odds_short
        print(f"  凯利值: {kelly_short:.3f}")
        print(f"  建议仓位: {max(0, kelly_short)*100:.1f}%")
        
        return kelly_long, kelly_short
    
    def recommend_optimal_setup(self):
        """推荐最优设置"""
        print(f"\n🎯 最优设置推荐")
        print("=" * 50)
        
        # 计算最优赔率
        print("📈 看涨策略优化:")
        optimal_odds_long, max_kelly_long = self.calculate_optimal_odds(self.win_rate_long)
        
        print(f"\n📉 看跌策略优化:")
        optimal_odds_short, max_kelly_short = self.calculate_optimal_odds(self.win_rate_short)
        
        # 推荐具体的止盈止损设置
        print(f"\n💡 推荐设置:")
        print("-" * 30)
        
        # 看涨策略
        if optimal_odds_long >= 1.5:
            # 保持当前止损0.8%，调整止盈
            recommended_sl_long = 0.008
            recommended_tp_long = recommended_sl_long * optimal_odds_long
        else:
            # 保持当前止盈1.2%，调整止损
            recommended_tp_long = 0.012
            recommended_sl_long = recommended_tp_long / optimal_odds_long
        
        print(f"🟢 看涨策略:")
        print(f"  建议止盈: {recommended_tp_long*100:.1f}%")
        print(f"  建议止损: {recommended_sl_long*100:.1f}%")
        print(f"  赔率: 1:{optimal_odds_long:.1f}")
        print(f"  最优仓位: {max_kelly_long*100:.1f}%")
        
        # 看跌策略
        if optimal_odds_short >= 1.0:
            # 调整为合理的止盈止损
            recommended_sl_short = 0.010  # 1.0%
            recommended_tp_short = recommended_sl_short * optimal_odds_short
        else:
            # 如果最优赔率小于1，说明看跌策略不太适合
            recommended_tp_short = 0.008
            recommended_sl_short = 0.010
            optimal_odds_short = recommended_tp_short / recommended_sl_short
        
        print(f"\n🔴 看跌策略:")
        print(f"  建议止盈: {recommended_tp_short*100:.1f}%")
        print(f"  建议止损: {recommended_sl_short*100:.1f}%")
        print(f"  赔率: 1:{optimal_odds_short:.1f}")
        print(f"  最优仓位: {max_kelly_short*100:.1f}%")
        
        return {
            'long': {
                'take_profit': recommended_tp_long,
                'stop_loss': recommended_sl_long,
                'odds': optimal_odds_long,
                'kelly': max_kelly_long
            },
            'short': {
                'take_profit': recommended_tp_short,
                'stop_loss': recommended_sl_short,
                'odds': optimal_odds_short,
                'kelly': max_kelly_short
            }
        }
    
    def compare_strategies(self):
        """对比当前策略和最优策略"""
        print(f"\n📊 策略对比分析")
        print("=" * 50)
        
        # 分析当前设置
        current_kelly_long, current_kelly_short = self.analyze_current_setup()
        
        # 获取最优设置
        optimal_setup = self.recommend_optimal_setup()
        
        print(f"\n📈 看涨策略对比:")
        print(f"  当前凯利值: {max(0, current_kelly_long):.3f}")
        print(f"  最优凯利值: {optimal_setup['long']['kelly']:.3f}")
        improvement_long = (optimal_setup['long']['kelly'] - max(0, current_kelly_long)) / max(0.001, max(0, current_kelly_long)) * 100
        print(f"  改进幅度: {improvement_long:+.1f}%")
        
        print(f"\n📉 看跌策略对比:")
        print(f"  当前凯利值: {max(0, current_kelly_short):.3f}")
        print(f"  最优凯利值: {optimal_setup['short']['kelly']:.3f}")
        improvement_short = (optimal_setup['short']['kelly'] - max(0, current_kelly_short)) / max(0.001, max(0, current_kelly_short)) * 100
        print(f"  改进幅度: {improvement_short:+.1f}%")
        
        print(f"\n💡 结论:")
        if improvement_long > 10:
            print(f"  • 看涨策略有较大改进空间")
        else:
            print(f"  • 看涨策略已接近最优")
            
        if improvement_short > 10:
            print(f"  • 看跌策略需要优化")
        else:
            print(f"  • 看跌策略已接近最优")
    
    def generate_code_update(self):
        """生成代码更新建议"""
        optimal_setup = self.recommend_optimal_setup()
        
        print(f"\n💻 代码更新建议")
        print("=" * 50)
        print(f"# 在quico.py中更新以下参数:")
        print(f"self.take_profit_long = {optimal_setup['long']['take_profit']:.4f}   # {optimal_setup['long']['take_profit']*100:.1f}%")
        print(f"self.stop_loss_long = {optimal_setup['long']['stop_loss']:.4f}     # {optimal_setup['long']['stop_loss']*100:.1f}%")
        print(f"self.take_profit_short = {optimal_setup['short']['take_profit']:.4f}  # {optimal_setup['short']['take_profit']*100:.1f}%")
        print(f"self.stop_loss_short = {optimal_setup['short']['stop_loss']:.4f}   # {optimal_setup['short']['stop_loss']*100:.1f}%")

def main():
    """主函数"""
    print("🧮 凯利公式优化分析")
    print("=" * 60)
    
    optimizer = KellyOptimizer()
    
    # 分析当前设置
    optimizer.analyze_current_setup()
    
    # 推荐最优设置
    optimizer.recommend_optimal_setup()
    
    # 对比分析
    optimizer.compare_strategies()
    
    # 生成代码更新
    optimizer.generate_code_update()
    
    print(f"\n⚠️ 重要提醒:")
    print(f"  • 凯利公式基于历史胜率，实际胜率可能变化")
    print(f"  • 建议先小仓位测试新参数")
    print(f"  • 定期根据实际表现调整参数")

if __name__ == "__main__":
    main()
