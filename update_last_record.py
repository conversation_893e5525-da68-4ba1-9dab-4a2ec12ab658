#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
更新hkhsi50表的最后一条记录
==========================

安全地更新hkhsi50表的最后一条记录，
支持更新所有字段或指定字段

作者: Cosmoon NG
日期: 2025年7月
"""

import pymysql
import yfinance as yf
from datetime import datetime, timedelta
import pandas as pd

# 数据库配置
db_config = {
    'host': 'localhost',
    'user': 'root',
    'password': '12345678',
    'database': 'finance',
    'charset': 'utf8mb4'
}

def get_last_record():
    """获取最后一条记录"""
    print("🔍 获取最后一条记录...")
    
    try:
        conn = pymysql.connect(**db_config)
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT Date, Open, High, Low, Close, Volume, 
                   MoneyFlowRatio, Midprice, Full_Y, Controller, E
            FROM hkhsi50 
            ORDER BY Date DESC 
            LIMIT 1
        """)
        
        record = cursor.fetchone()
        
        if record:
            print(f"📊 当前最后一条记录:")
            print(f"   • 日期: {record[0]}")
            print(f"   • OHLC: {record[1]:.2f} / {record[2]:.2f} / {record[3]:.2f} / {record[4]:.2f}")
            print(f"   • 成交量: {record[5]:,}")
            print(f"   • MoneyFlowRatio: {record[6]:.4f}")
            print(f"   • Midprice: {record[7]:.2f}")
            print(f"   • Full_Y: {record[8]:.4f}")
            print(f"   • Controller: {record[9]}")
            print(f"   • E: {record[10]:.4f}")
            
            conn.close()
            return record
        else:
            print("❌ 没有找到记录")
            conn.close()
            return None
            
    except Exception as e:
        print(f"❌ 获取记录失败: {e}")
        return None

def get_latest_market_data():
    """获取最新的市场数据"""
    print("\n📈 获取最新市场数据...")
    
    try:
        # 获取恒生指数最新数据
        symbol = "^HSI"
        ticker = yf.Ticker(symbol)
        
        # 获取最近5天的数据
        end_date = datetime.now()
        start_date = end_date - timedelta(days=5)
        
        df = ticker.history(start=start_date, end=end_date)
        
        if not df.empty:
            latest = df.iloc[-1]
            latest_date = df.index[-1].date()
            
            print(f"✅ 获取到最新数据:")
            print(f"   • 日期: {latest_date}")
            print(f"   • OHLC: {latest['Open']:.2f} / {latest['High']:.2f} / {latest['Low']:.2f} / {latest['Close']:.2f}")
            print(f"   • 成交量: {latest['Volume']:,.0f}")
            
            return {
                'Date': latest_date,
                'Open': latest['Open'],
                'High': latest['High'],
                'Low': latest['Low'],
                'Close': latest['Close'],
                'Volume': int(latest['Volume'])
            }
        else:
            print("❌ 没有获取到最新数据")
            return None
            
    except Exception as e:
        print(f"❌ 获取市场数据失败: {e}")
        return None

def calculate_indicators(market_data, historical_median=None):
    """计算技术指标"""
    print("\n🧮 计算技术指标...")
    
    try:
        conn = pymysql.connect(**db_config)
        cursor = conn.cursor()
        
        # 获取历史数据用于计算指标
        cursor.execute("""
            SELECT Close, Volume FROM hkhsi50 
            ORDER BY Date DESC 
            LIMIT 60
        """)
        
        historical_data = cursor.fetchall()
        
        if len(historical_data) < 20:
            print("❌ 历史数据不足，无法计算指标")
            return None
        
        # 计算移动平均
        closes = [row[0] for row in historical_data]
        closes.reverse()  # 按时间正序
        closes.append(market_data['Close'])  # 添加最新价格
        
        ma20 = sum(closes[-20:]) / 20
        ma60 = sum(closes[-60:]) / 60 if len(closes) >= 60 else sum(closes) / len(closes)
        
        # 计算Midprice
        midprice = (market_data['High'] + market_data['Low']) / 2
        
        # 计算Controller
        if historical_median is None:
            cursor.execute("SELECT Midprice FROM hkhsi50 ORDER BY Midprice LIMIT 1 OFFSET (SELECT COUNT(*)/2 FROM hkhsi50)")
            result = cursor.fetchone()
            historical_median = result[0] if result else midprice
        
        controller = 1 if market_data['Close'] > historical_median else 0
        
        # 计算MoneyFlowRatio (简化版本)
        price_change = (market_data['Close'] - market_data['Open']) / market_data['Open']
        money_flow_ratio = 0.5 + 0.3 * price_change  # 简化计算
        money_flow_ratio = max(0.1, min(0.9, money_flow_ratio))
        
        # 计算Full_Y (初始值，存储过程会重新计算)
        price_ma20_ratio = market_data['Close'] / ma20
        if price_ma20_ratio >= 1:
            full_y = 0.5 + 0.4 * min(1, (price_ma20_ratio - 1) * 3)
        else:
            full_y = 0.5 - 0.4 * min(1, (1 - price_ma20_ratio) * 3)
        
        full_y = max(0.1, min(0.9, full_y))
        
        # 计算E值
        e_value = 8 * money_flow_ratio * full_y - 3 * money_flow_ratio - 3 * full_y + 1
        
        conn.close()
        
        indicators = {
            'MoneyFlowRatio': money_flow_ratio,
            'Midprice': midprice,
            'Full_Y': full_y,
            'Controller': controller,
            'E': e_value
        }
        
        print(f"✅ 指标计算完成:")
        print(f"   • MoneyFlowRatio: {money_flow_ratio:.4f}")
        print(f"   • Midprice: {midprice:.2f}")
        print(f"   • Full_Y: {full_y:.4f}")
        print(f"   • Controller: {controller}")
        print(f"   • E: {e_value:.4f}")
        
        return indicators
        
    except Exception as e:
        print(f"❌ 计算指标失败: {e}")
        return None

def update_last_record_with_latest():
    """用最新数据更新最后一条记录"""
    print("\n🔄 用最新数据更新最后一条记录...")
    
    # 1. 获取当前最后一条记录
    current_record = get_last_record()
    if not current_record:
        return False
    
    # 2. 获取最新市场数据
    market_data = get_latest_market_data()
    if not market_data:
        return False
    
    # 3. 计算技术指标
    indicators = calculate_indicators(market_data)
    if not indicators:
        return False
    
    # 4. 更新数据库
    try:
        conn = pymysql.connect(**db_config)
        cursor = conn.cursor()
        
        # 获取最后一条记录的日期
        cursor.execute("SELECT Date FROM hkhsi50 ORDER BY Date DESC LIMIT 1")
        last_date = cursor.fetchone()[0]
        
        update_sql = """
            UPDATE hkhsi50 SET 
                Open = %s,
                High = %s,
                Low = %s,
                Close = %s,
                Volume = %s,
                MoneyFlowRatio = %s,
                Midprice = %s,
                Full_Y = %s,
                Controller = %s,
                E = %s
            WHERE Date = %s
        """
        
        cursor.execute(update_sql, (
            market_data['Open'],
            market_data['High'],
            market_data['Low'],
            market_data['Close'],
            market_data['Volume'],
            indicators['MoneyFlowRatio'],
            indicators['Midprice'],
            indicators['Full_Y'],
            indicators['Controller'],
            indicators['E'],
            last_date
        ))
        
        conn.commit()
        
        if cursor.rowcount > 0:
            print(f"✅ 成功更新最后一条记录 ({last_date})")
            
            # 显示更新后的记录
            cursor.execute("""
                SELECT Date, Open, High, Low, Close, Volume, 
                       MoneyFlowRatio, Midprice, Full_Y, Controller, E
                FROM hkhsi50 
                ORDER BY Date DESC 
                LIMIT 1
            """)
            
            updated_record = cursor.fetchone()
            print(f"\n📊 更新后的记录:")
            print(f"   • 日期: {updated_record[0]}")
            print(f"   • OHLC: {updated_record[1]:.2f} / {updated_record[2]:.2f} / {updated_record[3]:.2f} / {updated_record[4]:.2f}")
            print(f"   • 成交量: {updated_record[5]:,}")
            print(f"   • MoneyFlowRatio: {updated_record[6]:.4f}")
            print(f"   • Midprice: {updated_record[7]:.2f}")
            print(f"   • Full_Y: {updated_record[8]:.4f}")
            print(f"   • Controller: {updated_record[9]}")
            print(f"   • E: {updated_record[10]:.4f}")
            
        else:
            print("❌ 没有记录被更新")
            
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 更新失败: {e}")
        return False

def manual_update_last_record():
    """手动更新最后一条记录"""
    print("\n✏️  手动更新最后一条记录...")
    
    # 获取当前记录
    current_record = get_last_record()
    if not current_record:
        return False
    
    print(f"\n请输入新的值 (直接回车保持原值):")
    
    try:
        # 获取用户输入
        new_open = input(f"Open ({current_record[1]:.2f}): ").strip()
        new_high = input(f"High ({current_record[2]:.2f}): ").strip()
        new_low = input(f"Low ({current_record[3]:.2f}): ").strip()
        new_close = input(f"Close ({current_record[4]:.2f}): ").strip()
        new_volume = input(f"Volume ({current_record[5]:,}): ").strip()
        
        # 使用新值或保持原值
        open_val = float(new_open) if new_open else current_record[1]
        high_val = float(new_high) if new_high else current_record[2]
        low_val = float(new_low) if new_low else current_record[3]
        close_val = float(new_close) if new_close else current_record[4]
        volume_val = int(new_volume.replace(',', '')) if new_volume else current_record[5]
        
        # 重新计算指标
        market_data = {
            'Open': open_val,
            'High': high_val,
            'Low': low_val,
            'Close': close_val,
            'Volume': volume_val
        }
        
        indicators = calculate_indicators(market_data)
        if not indicators:
            return False
        
        # 更新数据库
        conn = pymysql.connect(**db_config)
        cursor = conn.cursor()
        
        cursor.execute("SELECT Date FROM hkhsi50 ORDER BY Date DESC LIMIT 1")
        last_date = cursor.fetchone()[0]
        
        update_sql = """
            UPDATE hkhsi50 SET 
                Open = %s, High = %s, Low = %s, Close = %s, Volume = %s,
                MoneyFlowRatio = %s, Midprice = %s, Full_Y = %s, Controller = %s, E = %s
            WHERE Date = %s
        """
        
        cursor.execute(update_sql, (
            open_val, high_val, low_val, close_val, volume_val,
            indicators['MoneyFlowRatio'], indicators['Midprice'], 
            indicators['Full_Y'], indicators['Controller'], indicators['E'],
            last_date
        ))
        
        conn.commit()
        
        if cursor.rowcount > 0:
            print(f"✅ 手动更新成功!")
        else:
            print("❌ 更新失败")
            
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 手动更新失败: {e}")
        return False

def main():
    """主函数"""
    print("🎯 更新hkhsi50表的最后一条记录")
    print("="*50)
    
    while True:
        print(f"\n请选择操作:")
        print(f"1. 查看最后一条记录")
        print(f"2. 用最新市场数据更新")
        print(f"3. 手动更新")
        print(f"4. 退出")
        
        choice = input(f"\n请输入选择 (1-4): ").strip()
        
        if choice == '1':
            get_last_record()
            
        elif choice == '2':
            if update_last_record_with_latest():
                print(f"\n💡 提示: 可以运行存储过程重新计算Full_Y:")
                print(f"   CALL sp_stock_analysis_with_row_coefficients('hkhsi50');")
            
        elif choice == '3':
            if manual_update_last_record():
                print(f"\n💡 提示: 可以运行存储过程重新计算Full_Y:")
                print(f"   CALL sp_stock_analysis_with_row_coefficients('hkhsi50');")
            
        elif choice == '4':
            print(f"👋 再见!")
            break
            
        else:
            print(f"❌ 无效选择，请重新输入")

if __name__ == "__main__":
    main()
