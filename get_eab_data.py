#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
获取东亚银行(00023.HK)的Yahoo Finance数据
======================================
使用yfinance库获取东亚银行的历史股价数据
"""

import yfinance as yf
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

def get_eab_data():
    """获取东亚银行数据"""
    
    print("📊 获取东亚银行(00023.HK)数据")
    print("=" * 60)
    
    # 东亚银行股票代码
    symbol = "0023.HK"
    
    try:
        # 创建股票对象
        eab = yf.Ticker(symbol)
        
        # 获取股票信息
        print("🏦 获取股票基本信息...")
        info = eab.info
        
        print(f"📈 股票名称: {info.get('longName', '东亚银行有限公司')}")
        print(f"🏢 行业: {info.get('industry', '银行业')}")
        print(f"🌍 国家: {info.get('country', '香港')}")
        print(f"💰 市值: {info.get('marketCap', 'N/A')}")
        print(f"📊 股票代码: {symbol}")
        
        # 获取历史数据
        print(f"\n📊 获取历史数据...")
        
        # 获取最近5年的数据
        end_date = datetime.now()
        start_date = end_date - timedelta(days=5*365)
        
        print(f"📅 数据期间: {start_date.strftime('%Y-%m-%d')} 至 {end_date.strftime('%Y-%m-%d')}")
        
        # 下载历史数据
        hist_data = eab.history(start=start_date, end=end_date)
        
        if hist_data.empty:
            print("❌ 未能获取到历史数据")
            return None
        
        print(f"✅ 成功获取 {len(hist_data):,} 条历史记录")
        
        # 数据预处理
        hist_data.reset_index(inplace=True)
        hist_data.columns = ['Date', 'Open', 'High', 'Low', 'Close', 'Volume', 'Dividends', 'Stock Splits']
        
        # 只保留需要的列
        df = hist_data[['Date', 'Open', 'High', 'Low', 'Close', 'Volume']].copy()
        
        # 数据清理
        df = df.dropna()
        
        print(f"📊 数据概览:")
        print(f"   数据期间: {df['Date'].min().strftime('%Y-%m-%d')} 至 {df['Date'].max().strftime('%Y-%m-%d')}")
        print(f"   记录数量: {len(df):,} 条")
        print(f"   最新价格: {df['Close'].iloc[-1]:.2f} 港元")
        print(f"   最高价格: {df['High'].max():.2f} 港元")
        print(f"   最低价格: {df['Low'].min():.2f} 港元")
        print(f"   平均成交量: {df['Volume'].mean():,.0f} 股")
        
        # 保存数据到CSV
        csv_filename = f"eab_0023hk_data_{datetime.now().strftime('%Y%m%d')}.csv"
        df.to_csv(csv_filename, index=False)
        print(f"💾 数据已保存到: {csv_filename}")
        
        # 显示最近10天的数据
        print(f"\n📅 最近10天数据:")
        recent_data = df.tail(10)
        print(recent_data.to_string(index=False, float_format='%.2f'))
        
        return df
        
    except Exception as e:
        print(f"❌ 获取数据失败: {e}")
        print(f"💡 请确保已安装yfinance: pip install yfinance")
        return None

def analyze_eab_data(df):
    """分析东亚银行数据"""
    
    if df is None:
        return
    
    print(f"\n📊 东亚银行数据分析")
    print("=" * 60)
    
    # 计算基本统计指标
    latest_price = df['Close'].iloc[-1]
    price_change = df['Close'].iloc[-1] - df['Close'].iloc[-2]
    price_change_pct = (price_change / df['Close'].iloc[-2]) * 100
    
    # 计算移动平均线
    df['MA20'] = df['Close'].rolling(20).mean()
    df['MA60'] = df['Close'].rolling(60).mean()
    
    # 计算波动率
    df['returns'] = df['Close'].pct_change()
    volatility = df['returns'].std() * np.sqrt(252) * 100  # 年化波动率
    
    # 计算52周高低点
    week_52_high = df['High'].tail(252).max()
    week_52_low = df['Low'].tail(252).min()
    
    print(f"💰 当前价格: {latest_price:.2f} 港元")
    print(f"📈 日涨跌: {price_change:+.2f} 港元 ({price_change_pct:+.2f}%)")
    print(f"📊 20日均线: {df['MA20'].iloc[-1]:.2f} 港元")
    print(f"📊 60日均线: {df['MA60'].iloc[-1]:.2f} 港元")
    print(f"📈 52周最高: {week_52_high:.2f} 港元")
    print(f"📉 52周最低: {week_52_low:.2f} 港元")
    print(f"📊 年化波动率: {volatility:.1f}%")
    
    # 技术分析
    print(f"\n🎯 技术分析:")
    
    # 趋势分析
    if latest_price > df['MA20'].iloc[-1]:
        trend_20 = "🟢 上升"
    else:
        trend_20 = "🔴 下降"
    
    if latest_price > df['MA60'].iloc[-1]:
        trend_60 = "🟢 上升"
    else:
        trend_60 = "🔴 下降"
    
    print(f"   20日趋势: {trend_20}")
    print(f"   60日趋势: {trend_60}")
    
    # 相对位置
    position_52w = (latest_price - week_52_low) / (week_52_high - week_52_low) * 100
    print(f"   52周位置: {position_52w:.1f}%")
    
    # 成交量分析
    avg_volume = df['Volume'].tail(20).mean()
    latest_volume = df['Volume'].iloc[-1]
    volume_ratio = latest_volume / avg_volume
    
    print(f"\n📊 成交量分析:")
    print(f"   最新成交量: {latest_volume:,.0f} 股")
    print(f"   20日平均量: {avg_volume:,.0f} 股")
    print(f"   成交量比率: {volume_ratio:.2f}x")
    
    return df

def apply_my_algorithm_to_eab(df):
    """将我的算法应用到东亚银行数据"""
    
    if df is None:
        return
    
    print(f"\n🎯 应用我的HSI50增强版算法到东亚银行")
    print("=" * 60)
    
    try:
        # 导入我的算法
        from my_trading_algorithm_core import MyTradingAlgorithmCore
        
        # 创建算法实例
        algo = MyTradingAlgorithmCore()
        
        # 处理数据
        df_processed = algo.process_market_data(df.copy())
        
        # 获取最新信号
        signal_info = algo.get_latest_signal(df_processed)
        
        print(f"📅 分析日期: {signal_info['date'].strftime('%Y-%m-%d')}")
        print(f"💰 收盘价: {signal_info['close_price']:.2f} 港元")
        print(f"📊 Y值 (价格位置): {signal_info['y_value']:.4f}")
        print(f"📊 X值 (综合指标): {signal_info['x_value']:.4f}")
        print(f"📊 MFI: {signal_info['mfi']:.1f}")
        print(f"📊 RSI: {signal_info['rsi']:.1f}")
        
        # 解释信号
        signal = signal_info['signal']
        if signal == 1:
            print(f"🟢 交易信号: 做多东亚银行")
            print(f"   原因: Y>0.45且X>0.45且价格低于回归线")
        elif signal == -1:
            print(f"🔴 交易信号: 做空东亚银行")
            print(f"   原因: 满足做空条件且价格高于回归线")
        else:
            print(f"⚪ 交易信号: 观望东亚银行")
            print(f"   原因: 不满足做多或做空条件")
        
        # 计算交易参数
        if signal != 0:
            take_profit, stop_loss = algo.calculate_stop_levels(signal_info['close_price'], signal)
            print(f"\n🎯 建议交易参数:")
            print(f"   入场价格: {signal_info['close_price']:.2f} 港元")
            print(f"   止盈位: {take_profit:.2f} 港元")
            print(f"   止损位: {stop_loss:.2f} 港元")
            
            if signal == 1:
                profit_pct = (take_profit - signal_info['close_price']) / signal_info['close_price'] * 100
                loss_pct = (signal_info['close_price'] - stop_loss) / signal_info['close_price'] * 100
            else:
                profit_pct = (signal_info['close_price'] - take_profit) / signal_info['close_price'] * 100
                loss_pct = (stop_loss - signal_info['close_price']) / signal_info['close_price'] * 100
            
            print(f"   预期盈利: {profit_pct:.1f}%")
            print(f"   最大亏损: {loss_pct:.1f}%")
        
        # 统计最近信号
        recent_signals = df_processed.tail(30)['signal']
        signal_counts = recent_signals.value_counts()
        
        print(f"\n📊 最近30天信号统计:")
        for signal_val, count in signal_counts.items():
            if signal_val == 1:
                signal_name = "🟢 做多"
            elif signal_val == -1:
                signal_name = "🔴 做空"
            else:
                signal_name = "⚪ 观望"
            print(f"   {signal_name}: {count}次")
        
        return df_processed
        
    except ImportError:
        print("❌ 无法导入算法模块")
        print("💡 请确保my_trading_algorithm_core.py文件在当前目录")
        return df
    except Exception as e:
        print(f"❌ 算法应用失败: {e}")
        return df

def main():
    """主函数"""
    print("🏦 东亚银行(00023.HK)数据获取与分析")
    print("=" * 80)
    
    # 获取数据
    df = get_eab_data()
    
    if df is not None:
        # 分析数据
        df = analyze_eab_data(df)
        
        # 应用算法
        df_processed = apply_my_algorithm_to_eab(df)
        
        print(f"\n🎉 东亚银行数据分析完成！")
        print(f"💡 数据已保存，可用于进一步分析")
    else:
        print(f"❌ 数据获取失败，请检查网络连接")

if __name__ == "__main__":
    main()
