#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复HK2800技术指标
"""

import pymysql
import pandas as pd
import numpy as np

def fix_hk2800_indicators():
    """修复HK2800技术指标"""
    
    db_config = {
        'host': '************',
        'port': 3306,
        'database': 'finance',
        'user': 'root',
        'password': '',
        'charset': 'utf8mb4'
    }
    
    try:
        connection = pymysql.connect(**db_config)
        cursor = connection.cursor()
        
        print("🔧 修复HK2800技术指标")
        print("="*40)
        
        # 1. 获取所有数据
        cursor.execute("""
            SELECT date, close, volume
            FROM hk2800 
            ORDER BY date ASC
        """)
        
        data = cursor.fetchall()
        df = pd.DataFrame(data, columns=['date', 'close', 'volume'])
        df['close'] = pd.to_numeric(df['close'])
        df['volume'] = pd.to_numeric(df['volume'])
        
        print(f"📊 处理 {len(df)} 条数据")
        
        # 2. 计算技术指标
        print("📊 计算技术指标...")
        
        # 移动平均线
        df['ma5'] = df['close'].rolling(window=5).mean()
        df['ma10'] = df['close'].rolling(window=10).mean()
        df['ma20'] = df['close'].rolling(window=20).mean()
        df['ma60'] = df['close'].rolling(window=60).mean()
        
        # RSI
        def calculate_rsi(prices, window=14):
            delta = prices.diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=window).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=window).mean()
            rs = gain / loss
            rsi = 100 - (100 / (1 + rs))
            return rsi
        
        df['rsi'] = calculate_rsi(df['close'])
        
        # MACD
        exp1 = df['close'].ewm(span=12).mean()
        exp2 = df['close'].ewm(span=26).mean()
        df['macd'] = exp1 - exp2
        df['macd_signal'] = df['macd'].ewm(span=9).mean()
        
        # 布林带
        df['bb_middle'] = df['close'].rolling(window=20).mean()
        bb_std = df['close'].rolling(window=20).std()
        df['bb_upper'] = df['bb_middle'] + (bb_std * 2)
        df['bb_lower'] = df['bb_middle'] - (bb_std * 2)
        
        # 3. 更新数据库（只更新基本指标）
        print("💾 更新技术指标...")
        
        update_count = 0
        for _, row in df.iterrows():
            try:
                cursor.execute("""
                    UPDATE hk2800 
                    SET ma5 = %s, ma10 = %s, ma20 = %s, ma60 = %s,
                        rsi = %s, macd = %s, macd_signal = %s,
                        bb_upper = %s, bb_middle = %s, bb_lower = %s
                    WHERE date = %s
                """, (
                    float(row['ma5']) if pd.notna(row['ma5']) else None,
                    float(row['ma10']) if pd.notna(row['ma10']) else None,
                    float(row['ma20']) if pd.notna(row['ma20']) else None,
                    float(row['ma60']) if pd.notna(row['ma60']) else None,
                    float(row['rsi']) if pd.notna(row['rsi']) else None,
                    float(row['macd']) if pd.notna(row['macd']) else None,
                    float(row['macd_signal']) if pd.notna(row['macd_signal']) else None,
                    float(row['bb_upper']) if pd.notna(row['bb_upper']) else None,
                    float(row['bb_middle']) if pd.notna(row['bb_middle']) else None,
                    float(row['bb_lower']) if pd.notna(row['bb_lower']) else None,
                    row['date']
                ))
                update_count += 1
            except Exception as e:
                print(f"⚠️ 更新失败: {row['date']} - {e}")
        
        connection.commit()
        print(f"✅ 已更新 {update_count} 条技术指标")
        
        # 4. 验证数据
        cursor.execute("""
            SELECT date, close, y_probability, inflow_ratio, ma20, rsi, macd
            FROM hk2800 
            ORDER BY date DESC 
            LIMIT 5
        """)
        
        verification = cursor.fetchall()
        
        print(f"\n📅 最新5天数据验证:")
        print("日期         价格    Y值    X值    MA20   RSI   MACD")
        print("-" * 60)
        
        for row in verification:
            date, close, y_val, x_val, ma20, rsi, macd = row
            y_str = f"{float(y_val):.3f}" if y_val else "None"
            x_str = f"{float(x_val):.3f}" if x_val else "None"
            ma20_str = f"{float(ma20):.2f}" if ma20 else "None"
            rsi_str = f"{float(rsi):.1f}" if rsi else "None"
            macd_str = f"{float(macd):.3f}" if macd else "None"
            
            print(f"{date}  {float(close):>6.2f}  {y_str:>5}  {x_str:>5}  {ma20_str:>6}  {rsi_str:>4}  {macd_str:>6}")
        
        print(f"\n🎉 HK2800技术指标修复完成！")
        
        return True
        
    except Exception as e:
        print(f"❌ 修复失败: {e}")
        return False
    finally:
        if 'connection' in locals():
            connection.close()

if __name__ == "__main__":
    fix_hk2800_indicators()
