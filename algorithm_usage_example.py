3#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
我的投资算法使用示例
==================
展示如何使用投资算法进行实际交易
"""

import mysql.connector
import pandas as pd
import numpy as np
from datetime import datetime
from my_investment_algorithms import MyInvestmentAlgorithms
import warnings
warnings.filterwarnings('ignore')

class AlgorithmUsageExample:
    """算法使用示例类"""
    
    def __init__(self):
        """初始化"""
        self.host = "localhost"
        self.user = "root"
        self.password = "12345678"
        self.database = "finance"
        
    def connect_database(self):
        """连接数据库"""
        try:
            self.conn = mysql.connector.connect(
                host=self.host,
                user=self.user,
                password=self.password,
                database=self.database
            )
            print("✅ 数据库连接成功")
            return True
        except Exception as e:
            print(f"❌ 数据库连接失败: {e}")
            return False
    
    def load_data(self):
        """加载数据"""
        print("📊 加载hkhsi50数据...")
        
        query = """
            SELECT Date, Open, High, Low, Close, Volume 
            FROM hkhsi50 
            ORDER BY Date ASC
        """
        
        df = pd.read_sql_query(query, self.conn)
        
        # 数据类型转换
        for col in ['Open', 'High', 'Low', 'Close', 'Volume']:
            df[col] = pd.to_numeric(df[col], errors='coerce')
        
        print(f"✅ 成功加载 {len(df):,} 条记录")
        return df
    
    def run_single_strategy_example(self):
        """运行单策略示例"""
        print("\n🎯 单策略回测示例")
        print("=" * 60)
        
        # 加载数据
        if not self.connect_database():
            return
        
        df = self.load_data()
        
        # 创建算法实例
        algo = MyInvestmentAlgorithms()
        
        # 计算所有指标
        df = algo.calculate_all_indicators(df)
        
        # 生成所有信号
        df = algo.generate_all_signals(df)
        
        # 回测散户资金占比策略
        print("\n🎯 回测散户资金占比策略...")
        results = algo.backtest_strategy(df, 'retail_ratio')
        
        # 显示结果
        self.display_backtest_results("散户资金占比策略", results)
        
        # 信号统计
        algo.analyze_signals_summary(df)
        
        self.conn.close()
    
    def run_portfolio_strategy_example(self):
        """运行投资组合策略示例"""
        print("\n💰 投资组合策略回测示例")
        print("=" * 60)
        
        # 加载数据
        if not self.connect_database():
            return
        
        df = self.load_data()
        
        # 创建算法实例
        algo = MyInvestmentAlgorithms()
        
        # 计算所有指标
        df = algo.calculate_all_indicators(df)
        
        # 生成所有信号
        df = algo.generate_all_signals(df)
        
        # 定义投资组合权重
        portfolio_weights = {
            'traditional': 0.4,      # 40% 传统XY策略
            'sentiment': 0.3,        # 30% 散户情绪策略
            'retail_ratio': 0.2,     # 20% 散户资金占比策略
            'hsi50_enhanced': 0.1    # 10% HSI50增强版策略
        }
        
        print(f"📊 投资组合配置:")
        for strategy, weight in portfolio_weights.items():
            print(f"   • {strategy}: {weight*100:.0f}%")
        
        # 回测投资组合
        print("\n🎯 回测投资组合策略...")
        results = algo.backtest_strategy(df, 'portfolio', portfolio_weights)
        
        # 显示结果
        self.display_backtest_results("投资组合策略", results)
        
        self.conn.close()
    
    def run_all_strategies_comparison(self):
        """运行所有策略对比"""
        print("\n📊 所有策略对比示例")
        print("=" * 60)
        
        # 加载数据
        if not self.connect_database():
            return
        
        df = self.load_data()
        
        # 创建算法实例
        algo = MyInvestmentAlgorithms()
        
        # 计算所有指标
        df = algo.calculate_all_indicators(df)
        
        # 生成所有信号
        df = algo.generate_all_signals(df)
        
        # 回测所有策略
        strategies = {
            'traditional': '传统XY策略',
            'sentiment': '散户情绪策略',
            'money_flow': '资金流策略',
            'retail_ratio': '散户资金占比策略'
        }
        
        all_results = {}
        
        for strategy_key, strategy_name in strategies.items():
            print(f"\n🎯 回测{strategy_name}...")
            results = algo.backtest_strategy(df, strategy_key)
            all_results[strategy_name] = results
        
        # 对比结果
        self.compare_all_strategies(all_results)
        
        self.conn.close()
    
    def display_backtest_results(self, strategy_name, results):
        """显示回测结果"""
        print(f"\n📊 {strategy_name}回测结果:")
        print(f"   💰 最终资金: {results['final_capital']:,.2f}港元")
        print(f"   📈 总交易次数: {results['total_trades']}笔")
        print(f"   ✅ 盈利交易: {results['winning_trades']}笔")
        print(f"   📊 胜率: {results['winning_trades']/results['total_trades']*100:.1f}%")
        print(f"   📉 最大回撤: {results['max_drawdown']*100:.2f}%")
        
        if results['trades']:
            profits = [t['profit'] for t in results['trades']]
            print(f"   💰 平均每笔收益: {np.mean(profits):.2f}港元")
            print(f"   📈 最大盈利: {max(profits):.2f}港元")
            print(f"   📉 最大亏损: {min(profits):.2f}港元")
    
    def compare_all_strategies(self, all_results):
        """对比所有策略"""
        print(f"\n📊 策略对比总结:")
        print(f"=" * 80)
        
        print(f"{'策略名称':<15} {'最终资金':<12} {'交易次数':<8} {'胜率':<8} {'最大回撤':<8}")
        print(f"-" * 80)
        
        for strategy_name, results in all_results.items():
            final_capital = results['final_capital']
            total_trades = results['total_trades']
            win_rate = results['winning_trades']/results['total_trades']*100 if results['total_trades'] > 0 else 0
            max_drawdown = results['max_drawdown']*100
            
            print(f"{strategy_name:<15} {final_capital:>10,.0f}元 {total_trades:>6}笔 {win_rate:>6.1f}% {max_drawdown:>6.2f}%")
        
        # 找出最佳策略
        best_return = max(all_results.items(), key=lambda x: x[1]['final_capital'])
        best_winrate = max(all_results.items(), key=lambda x: x[1]['winning_trades']/x[1]['total_trades'] if x[1]['total_trades'] > 0 else 0)
        best_risk = min(all_results.items(), key=lambda x: x[1]['max_drawdown'])
        
        print(f"\n🏆 最佳表现:")
        print(f"   💰 最高收益: {best_return[0]} ({best_return[1]['final_capital']:,.0f}元)")
        print(f"   ✅ 最高胜率: {best_winrate[0]} ({best_winrate[1]['winning_trades']/best_winrate[1]['total_trades']*100:.1f}%)")
        print(f"   🛡️ 最低风险: {best_risk[0]} ({best_risk[1]['max_drawdown']*100:.2f}%回撤)")
    
    def run_real_time_signal_example(self):
        """运行实时信号示例"""
        print("\n⚡ 实时信号生成示例")
        print("=" * 60)
        
        # 加载数据
        if not self.connect_database():
            return
        
        df = self.load_data()
        
        # 创建算法实例
        algo = MyInvestmentAlgorithms()
        
        # 计算所有指标
        df = algo.calculate_all_indicators(df)
        
        # 生成所有信号
        df = algo.generate_all_signals(df)
        
        # 获取最新的信号
        latest_data = df.iloc[-1]
        
        print(f"📅 最新数据日期: {latest_data['Date']}")
        print(f"💰 收盘价: {latest_data['Close']:.2f}")
        
        print(f"\n📊 各策略信号:")
        strategies = {
            'signal_traditional': '传统XY策略',
            'signal_sentiment': '散户情绪策略',
            'signal_money_flow': '资金流策略',
            'signal_retail_ratio': '散户资金占比策略'
        }
        
        for signal_col, strategy_name in strategies.items():
            if signal_col in latest_data:
                signal = latest_data[signal_col]
                signal_text = "🔴 做空" if signal == -1 else "🟢 做多" if signal == 1 else "⚪ 观望"
                print(f"   {strategy_name}: {signal_text}")
        
        # 投资组合信号
        portfolio_weights = {'traditional': 0.4, 'sentiment': 0.3, 'retail_ratio': 0.2, 'money_flow': 0.1}
        portfolio_signal = algo.get_portfolio_signal(latest_data, portfolio_weights)
        portfolio_text = "🔴 做空" if portfolio_signal == -1 else "🟢 做多" if portfolio_signal == 1 else "⚪ 观望"
        print(f"   投资组合信号: {portfolio_text}")
        
        # 关键指标值
        print(f"\n📊 关键指标值:")
        print(f"   Y值 (控股商控制): {latest_data.get('Y', 0):.4f}")
        print(f"   X值 (散户资金占比): {latest_data.get('X_retail_ratio', 0):.4f}")
        print(f"   散户情绪X值: {latest_data.get('X_sentiment', 0):.4f}")
        print(f"   MFI: {latest_data.get('mfi', 0):.1f}")
        
        self.conn.close()

def main():
    """主函数"""
    print("🎯 我的投资算法使用示例")
    print("=" * 80)
    
    example = AlgorithmUsageExample()
    
    print("选择运行示例:")
    print("1. 单策略回测示例")
    print("2. 投资组合策略示例")
    print("3. 所有策略对比示例")
    print("4. 实时信号生成示例")
    print("5. 运行所有示例")
    
    choice = input("\n请输入选择 (1-5): ").strip()
    
    if choice == "1":
        example.run_single_strategy_example()
    elif choice == "2":
        example.run_portfolio_strategy_example()
    elif choice == "3":
        example.run_all_strategies_comparison()
    elif choice == "4":
        example.run_real_time_signal_example()
    elif choice == "5":
        example.run_single_strategy_example()
        example.run_portfolio_strategy_example()
        example.run_all_strategies_comparison()
        example.run_real_time_signal_example()
    else:
        print("❌ 无效选择")
        return
    
    print(f"\n🎉 示例运行完成！")
    print(f"💡 您可以根据这些示例来实现自己的交易系统")

if __name__ == "__main__":
    main()
