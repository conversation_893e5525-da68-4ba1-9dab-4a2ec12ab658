#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建存储过程 sp_stock_ansisyle
=============================

创建用于计算股票技术分析指标的存储过程
包括：MA20, MA60, RSI, X值, Y值, E值

作者: Cosmoon NG
日期: 2025年7月
"""

import pymysql

# 数据库配置
db_config = {
    'host': '************',
    'user': 'root',
    'password': '',
    'database': 'finance',
    'charset': 'utf8mb4'
}

def check_existing_procedures():
    """检查现有的存储过程"""
    print("🔍 检查现有存储过程...")
    
    try:
        conn = pymysql.connect(**db_config)
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT ROUTINE_NAME, ROUTINE_TYPE, CREATED 
            FROM information_schema.ROUTINES 
            WHERE ROUTINE_SCHEMA = 'finance'
            ORDER BY ROUTINE_NAME
        """)
        
        procedures = cursor.fetchall()
        
        if procedures:
            print(f"📋 找到 {len(procedures)} 个存储过程/函数:")
            for proc in procedures:
                print(f"   • {proc[0]} ({proc[1]}) - 创建于 {proc[2]}")
        else:
            print("📋 没有找到任何存储过程")
        
        conn.close()
        return procedures
        
    except Exception as e:
        print(f"❌ 检查存储过程失败: {e}")
        return []

def create_sp_stock_ansisyle():
    """创建存储过程 sp_stock_ansisyle"""
    print("\n🔧 创建存储过程 sp_stock_ansisyle...")
    
    # 存储过程SQL
    procedure_sql = """
    DROP PROCEDURE IF EXISTS sp_stock_ansisyle;
    
    DELIMITER $$
    
    CREATE PROCEDURE sp_stock_ansisyle(IN table_name VARCHAR(64))
    BEGIN
        DECLARE done INT DEFAULT FALSE;
        DECLARE v_date DATE;
        DECLARE v_close DECIMAL(15,4);
        DECLARE v_open DECIMAL(15,4);
        DECLARE v_volume BIGINT;
        
        -- 声明游标
        DECLARE cur CURSOR FOR 
            SELECT date, open, close, volume 
            FROM hkhsi50 
            ORDER BY date;
        
        DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;
        
        -- 更新MA20
        SET @sql = CONCAT('UPDATE ', table_name, ' t1 
                          SET ma20 = (
                              SELECT AVG(t2.close) 
                              FROM ', table_name, ' t2 
                              WHERE t2.date <= t1.date 
                              AND t2.date >= DATE_SUB(t1.date, INTERVAL 19 DAY)
                          )');
        PREPARE stmt FROM @sql;
        EXECUTE stmt;
        DEALLOCATE PREPARE stmt;
        
        -- 更新MA60
        SET @sql = CONCAT('UPDATE ', table_name, ' t1 
                          SET ma60 = (
                              SELECT AVG(t2.close) 
                              FROM ', table_name, ' t2 
                              WHERE t2.date <= t1.date 
                              AND t2.date >= DATE_SUB(t1.date, INTERVAL 59 DAY)
                          )');
        PREPARE stmt FROM @sql;
        EXECUTE stmt;
        DEALLOCATE PREPARE stmt;
        
        -- 更新RSI (简化版本)
        SET @sql = CONCAT('UPDATE ', table_name, ' t1 
                          SET rsi = 50 + (
                              SELECT 
                                  CASE 
                                      WHEN AVG(t2.close) > t1.close THEN -10
                                      WHEN AVG(t2.close) < t1.close THEN 10
                                      ELSE 0
                                  END
                              FROM ', table_name, ' t2 
                              WHERE t2.date <= t1.date 
                              AND t2.date >= DATE_SUB(t1.date, INTERVAL 13 DAY)
                          )');
        PREPARE stmt FROM @sql;
        EXECUTE stmt;
        DEALLOCATE PREPARE stmt;
        
        -- 更新money_flow
        SET @sql = CONCAT('UPDATE ', table_name, ' 
                          SET money_flow = volume * (close - open) / open 
                          WHERE open > 0');
        PREPARE stmt FROM @sql;
        EXECUTE stmt;
        DEALLOCATE PREPARE stmt;
        
        -- 更新base_x (简化版本)
        SET @sql = CONCAT('UPDATE ', table_name, ' t1 
                          SET base_x = 0.5 + (
                              SELECT 
                                  CASE 
                                      WHEN SUM(CASE WHEN t2.money_flow > 0 THEN t2.money_flow ELSE 0 END) > 
                                           ABS(SUM(CASE WHEN t2.money_flow < 0 THEN t2.money_flow ELSE 0 END))
                                      THEN 0.2
                                      ELSE -0.2
                                  END
                              FROM ', table_name, ' t2 
                              WHERE t2.date <= t1.date 
                              AND t2.date >= DATE_SUB(t1.date, INTERVAL 19 DAY)
                              AND t2.money_flow IS NOT NULL
                          )');
        PREPARE stmt FROM @sql;
        EXECUTE stmt;
        DEALLOCATE PREPARE stmt;
        
        -- 更新x_value
        SET @sql = CONCAT('UPDATE ', table_name, ' 
                          SET x_value = GREATEST(0.1, LEAST(0.9, 
                              base_x + 0.3 * (rsi / 100 - 0.5)
                          ))
                          WHERE base_x IS NOT NULL AND rsi IS NOT NULL');
        PREPARE stmt FROM @sql;
        EXECUTE stmt;
        DEALLOCATE PREPARE stmt;
        
        -- 更新base_y
        SET @sql = CONCAT('UPDATE ', table_name, ' 
                          SET base_y = CASE 
                              WHEN close / ma20 >= 1 THEN 
                                  0.5 + 0.4 * TANH((close / ma20 - 1) * 3)
                              ELSE 
                                  0.5 - 0.4 * TANH((1 - close / ma20) * 3)
                          END
                          WHERE ma20 > 0');
        PREPARE stmt FROM @sql;
        EXECUTE stmt;
        DEALLOCATE PREPARE stmt;
        
        -- 更新y_value (简化版本，不包含复杂的趋势和成交量调整)
        SET @sql = CONCAT('UPDATE ', table_name, ' 
                          SET y_value = GREATEST(0.1, LEAST(0.9, 
                              base_y + 
                              0.1 * TANH((ma20 / ma60 - 1) * 2) +
                              0.05 * TANH((volume / (
                                  SELECT AVG(v.volume) 
                                  FROM ', table_name, ' v 
                                  WHERE v.date <= ', table_name, '.date 
                                  AND v.date >= DATE_SUB(', table_name, '.date, INTERVAL 19 DAY)
                              ) - 1))
                          ))
                          WHERE base_y IS NOT NULL AND ma20 > 0 AND ma60 > 0');
        PREPARE stmt FROM @sql;
        EXECUTE stmt;
        DEALLOCATE PREPARE stmt;
        
        -- 更新e_value
        SET @sql = CONCAT('UPDATE ', table_name, ' 
                          SET e_value = 8 * x_value * y_value - 3 * x_value - 3 * y_value + 1
                          WHERE x_value IS NOT NULL AND y_value IS NOT NULL');
        PREPARE stmt FROM @sql;
        EXECUTE stmt;
        DEALLOCATE PREPARE stmt;
        
        SELECT CONCAT('存储过程执行完成，已更新表: ', table_name) AS result;
        
    END$$
    
    DELIMITER ;
    """
    
    try:
        conn = pymysql.connect(**db_config)
        cursor = conn.cursor()
        
        # 执行创建存储过程的SQL
        for statement in procedure_sql.split('$$'):
            statement = statement.strip()
            if statement and not statement.startswith('DELIMITER'):
                cursor.execute(statement)
        
        conn.commit()
        print("✅ 存储过程 sp_stock_ansisyle 创建成功")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 创建存储过程失败: {e}")
        return False

def test_procedure():
    """测试存储过程"""
    print("\n🧪 测试存储过程...")
    
    try:
        conn = pymysql.connect(**db_config)
        cursor = conn.cursor()
        
        # 检查hkhsi50表的记录数
        cursor.execute("SELECT COUNT(*) FROM hkhsi50")
        count_before = cursor.fetchone()[0]
        
        print(f"   • 表中记录数: {count_before}")
        
        # 检查更新前的Y值
        cursor.execute("SELECT COUNT(*) FROM hkhsi50 WHERE y_value IS NOT NULL")
        y_before = cursor.fetchone()[0]
        
        print(f"   • 更新前有Y值的记录: {y_before}")
        
        # 调用存储过程
        print("   • 调用存储过程...")
        cursor.callproc('sp_stock_ansisyle', ['hkhsi50'])
        
        # 获取结果
        for result in cursor.stored_results():
            rows = result.fetchall()
            for row in rows:
                print(f"   • {row[0]}")
        
        conn.commit()
        
        # 检查更新后的Y值
        cursor.execute("SELECT COUNT(*) FROM hkhsi50 WHERE y_value IS NOT NULL")
        y_after = cursor.fetchone()[0]
        
        print(f"   • 更新后有Y值的记录: {y_after}")
        print(f"   • 新增Y值记录: {y_after - y_before}")
        
        # 显示最新的几条记录
        cursor.execute("""
            SELECT date, close, ma20, y_value, x_value, e_value 
            FROM hkhsi50 
            WHERE y_value IS NOT NULL 
            ORDER BY date DESC 
            LIMIT 5
        """)
        
        recent_data = cursor.fetchall()
        print(f"\n📋 最新5条记录:")
        print(f"{'日期':<12} {'收盘价':<10} {'MA20':<10} {'Y值':<8} {'X值':<8} {'E值':<8}")
        print("-" * 65)
        for row in recent_data:
            print(f"{row[0]:<12} {row[1]:<10.2f} {row[2]:<10.2f} {row[3]:<8.3f} {row[4]:<8.3f} {row[5]:<8.3f}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 测试存储过程失败: {e}")
        return False

def main():
    """主函数"""
    print("🔧 创建和测试存储过程 sp_stock_ansisyle")
    print("="*50)
    
    # 1. 检查现有存储过程
    check_existing_procedures()
    
    # 2. 创建存储过程
    if not create_sp_stock_ansisyle():
        return
    
    # 3. 再次检查存储过程
    print("\n🔍 验证存储过程创建...")
    procedures = check_existing_procedures()
    
    sp_found = any('sp_stock_ansisyle' in proc[0] for proc in procedures)
    if sp_found:
        print("✅ 存储过程 sp_stock_ansisyle 已成功创建")
    else:
        print("❌ 存储过程创建可能失败")
        return
    
    # 4. 测试存储过程
    if test_procedure():
        print("\n🎉 存储过程创建和测试完成！")
        print("💡 现在可以使用 sp_stock_ansisyle('hkhsi50') 来更新Y值")
    else:
        print("\n❌ 存储过程测试失败")

if __name__ == "__main__":
    main()
