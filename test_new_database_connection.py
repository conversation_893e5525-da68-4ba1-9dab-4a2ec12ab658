#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试新的数据库连接配置
====================
验证localhost连接是否正常工作
"""

import mysql.connector
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

def test_database_connection():
    """测试数据库连接"""
    
    # 新的数据库配置
    db_config = {
        'host': 'localhost',
        'database': 'finance',
        'user': 'root',
        'password': '12345678',
        'charset': 'utf8mb4',
        'autocommit': True
    }
    
    try:
        print("🧪 测试新的数据库连接配置")
        print("=" * 50)
        print(f"📅 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"🔗 连接参数: {db_config['host']}:{db_config['database']}")
        
        # 连接数据库
        connection = mysql.connector.connect(**db_config)
        cursor = connection.cursor()
        print("✅ 数据库连接成功!")
        
        # 1. 基本信息查询
        print("\n📊 数据库基本信息:")
        cursor.execute("SELECT DATABASE(), VERSION(), NOW()")
        result = cursor.fetchone()
        print(f"   • 当前数据库: {result[0]}")
        print(f"   • 数据库版本: {result[1]}")
        print(f"   • 服务器时间: {result[2]}")
        
        # 2. 检查表数量
        print("\n📋 表统计信息:")
        cursor.execute("SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'finance'")
        table_count = cursor.fetchone()[0]
        print(f"   • 总表数量: {table_count}")
        
        # 3. 检查股票表
        cursor.execute("""
            SELECT COUNT(*) FROM information_schema.tables 
            WHERE table_schema = 'finance' AND table_name LIKE 'stock_%'
        """)
        stock_table_count = cursor.fetchone()[0]
        print(f"   • 股票表数量: {stock_table_count}")
        
        # 4. 列出前10个股票表
        cursor.execute("""
            SELECT table_name FROM information_schema.tables 
            WHERE table_schema = 'finance' AND table_name LIKE 'stock_%'
            ORDER BY table_name
            LIMIT 10
        """)
        stock_tables = cursor.fetchall()
        if stock_tables:
            print(f"   • 前10个股票表:")
            for table in stock_tables:
                print(f"     - {table[0]}")
        
        # 5. 检查hkhsi50表
        cursor.execute("""
            SELECT COUNT(*) FROM information_schema.tables 
            WHERE table_schema = 'finance' AND table_name = 'hkhsi50'
        """)
        hkhsi50_exists = cursor.fetchone()[0]
        if hkhsi50_exists:
            print(f"   ✅ hkhsi50表存在")
            
            # 检查hkhsi50数据量
            cursor.execute("SELECT COUNT(*) FROM hkhsi50")
            hkhsi50_count = cursor.fetchone()[0]
            print(f"   • hkhsi50记录数: {hkhsi50_count}")
            
            # 检查最新日期
            cursor.execute("SELECT MAX(Date) FROM hkhsi50")
            latest_date = cursor.fetchone()[0]
            print(f"   • 最新数据日期: {latest_date}")
        else:
            print(f"   ⚠️ hkhsi50表不存在")
        
        # 6. 测试存储过程
        print("\n🔧 测试存储过程:")
        cursor.execute("""
            SELECT COUNT(*) FROM information_schema.ROUTINES 
            WHERE ROUTINE_SCHEMA = 'finance' 
            AND ROUTINE_NAME = 'sp_updatecontroller'
        """)
        sp_exists = cursor.fetchone()[0]
        if sp_exists:
            print("   ✅ sp_updatecontroller存储过程存在")
        else:
            print("   ⚠️ sp_updatecontroller存储过程不存在")
        
        cursor.execute("""
            SELECT COUNT(*) FROM information_schema.ROUTINES 
            WHERE ROUTINE_SCHEMA = 'finance' 
            AND ROUTINE_NAME = 'sp_averagelineV3'
        """)
        sp_avg_exists = cursor.fetchone()[0]
        if sp_avg_exists:
            print("   ✅ sp_averagelineV3存储过程存在")
        else:
            print("   ⚠️ sp_averagelineV3存储过程不存在")
        
        # 7. 测试简单查询
        print("\n📊 测试数据查询:")
        if hkhsi50_exists and hkhsi50_count > 0:
            cursor.execute("""
                SELECT Date, Close, Volume 
                FROM hkhsi50 
                ORDER BY Date DESC 
                LIMIT 5
            """)
            latest_data = cursor.fetchall()
            print("   • hkhsi50最新5条数据:")
            print("     日期          | 收盘价    | 成交量")
            print("     " + "-" * 40)
            for row in latest_data:
                date_str = str(row[0])
                close_val = float(row[1]) if row[1] is not None else 0.0
                volume_val = int(row[2]) if row[2] is not None else 0
                print(f"     {date_str} | {close_val:9.2f} | {volume_val:10,d}")
        
        # 8. 测试写入权限
        print("\n✍️ 测试写入权限:")
        try:
            # 创建测试表
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS connection_test (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    test_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    test_message VARCHAR(100)
                )
            """)
            
            # 插入测试数据
            cursor.execute("""
                INSERT INTO connection_test (test_message) 
                VALUES ('Database connection test successful')
            """)
            
            # 查询测试数据
            cursor.execute("SELECT COUNT(*) FROM connection_test")
            test_count = cursor.fetchone()[0]
            print(f"   ✅ 写入权限正常 (测试表记录数: {test_count})")
            
            # 清理测试表
            cursor.execute("DROP TABLE IF EXISTS connection_test")
            print("   ✅ 测试表已清理")
            
        except mysql.connector.Error as e:
            print(f"   ⚠️ 写入权限测试失败: {e}")
        
        # 关闭连接
        cursor.close()
        connection.close()
        
        print("\n🎉 数据库连接测试完成!")
        print("✅ 新的数据库配置工作正常")
        print("\n💡 配置摘要:")
        print(f"   • 主机: {db_config['host']}")
        print(f"   • 数据库: {db_config['database']}")
        print(f"   • 用户: {db_config['user']}")
        print(f"   • 密码: {'*' * len(db_config['password'])}")
        
        return True
        
    except mysql.connector.Error as e:
        print(f"❌ 数据库连接失败: {e}")
        print("\n💡 可能的解决方案:")
        print("   1. 检查MySQL/MariaDB服务是否启动")
        print("   2. 确认用户名和密码是否正确")
        print("   3. 检查finance数据库是否存在")
        print("   4. 确认root用户有足够权限")
        print("   5. 检查防火墙设置")
        
        return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def show_updated_files_summary():
    """显示更新文件摘要"""
    print("\n📊 配置更新摘要:")
    print("=" * 50)
    print("✅ 成功更新了96个Python文件的数据库配置")
    print("📄 所有文件都已自动备份")
    print("🔄 配置变更:")
    print("   旧配置: host='************', password=''")
    print("   新配置: host='localhost', password='12345678'")
    print("\n📝 主要更新的文件类型:")
    print("   • 数据分析脚本")
    print("   • 回测策略脚本")
    print("   • 数据库管理工具")
    print("   • 技术指标计算脚本")
    print("   • 交易监控系统")

def main():
    """主函数"""
    show_updated_files_summary()
    success = test_database_connection()
    
    if success:
        print("\n🎉 恭喜！数据库配置更新成功")
        print("📝 现在可以使用新的连接参数访问数据库")
        print("💡 建议测试几个关键功能确保一切正常")
    else:
        print("\n❌ 数据库连接测试失败")
        print("📝 请检查配置并解决连接问题")

if __name__ == "__main__":
    main()
