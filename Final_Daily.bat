@echo off
chcp 65001 >nul
title 完整每日更新系统 - 包含持仓管理

echo.
echo ========================================
echo 完整每日更新系统启动
echo ========================================
echo 日期: %date%
echo 时间: %time%
echo.

REM 切换到脚本所在目录
cd D:\Users\"Cosmoon NG"\Documents\VSCode\Python\Finance\EAs\Investment02
D:\
echo.

REM 检查Python是否可用
python --version >nul 2>&1
if errorlevel 1 (
    echo Python 未安装或不在PATH中
    echo 请确保Python已正确安装并添加到系统PATH
    pause
    exit /b 1
)

echo Python 环境检查通过
echo.

REM 检查必要文件是否存在
if not exist "complete_daily_update_with_position.py" (
    echo 缺少文件: complete_daily_update_with_position.py
    pause
    exit /b 1
)

if not exist "position_manager_with_excel.py" (
    echo 缺少文件: position_manager_with_excel.py
    pause
    exit /b 1
)

echo 必要文件检查通过
echo.

echo ========================================
echo 开始执行完整每日更新任务
echo ========================================
echo 包含以下功能:
echo 1. 数据库更新
echo 2. Full_Y字段更新  
echo 3. 持仓判断和Excel更新
echo 4. 每日报告生成
echo.

REM 执行Python更新脚本
python complete_daily_update_with_position.py

REM 检查执行结果
if errorlevel 1 (
    echo.
    echo ========================================
    echo 更新任务部分失败
    echo ========================================
    echo 请检查上方的错误信息
    echo 注意: 如果持仓管理成功，Excel已更新
    echo.
) else (
    echo.
    echo ========================================
    echo 更新任务执行成功
    echo ========================================
    echo 所有任务已完成！
    echo.
)

echo 任务完成时间: %date% %time%
echo.

REM 显示Excel文件状态
if exist "交易记录追踪0023HK.xlsx" (
    echo Excel文件状态: 存在
    echo 最新备份: 交易记录追踪0023HK_backup_*.xlsx
) else (
    echo Excel文件状态: 不存在或被占用
)

echo.
echo 按任意键退出...
pause >nul
