#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
为新数据库的hkhsi50表添加y_probability (独立版本)
==============================================
不依赖存储过程，直接计算所有技术指标
"""

import mysql.connector
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

class HKHsi50YProbabilityStandalone:
    def __init__(self):
        """初始化数据库连接"""
        self.db_config = {
            'host': 'localhost',
            'database': 'finance',
            'user': 'root',
            'password': '12345678',
            'charset': 'utf8mb4',
            'autocommit': True
        }
        self.connection = None
        self.cursor = None
        self.table_name = 'hkhsi50'
        
    def connect_database(self):
        """连接数据库"""
        try:
            print("🔗 连接MySQL数据库...")
            self.connection = mysql.connector.connect(**self.db_config)
            self.cursor = self.connection.cursor()
            print(f"✅ 成功连接到数据库: {self.db_config['host']}/{self.db_config['database']}")
            return True
        except mysql.connector.Error as e:
            print(f"❌ 数据库连接失败: {e}")
            return False
    
    def check_and_add_columns(self):
        """检查并添加必要的列"""
        try:
            print(f"\n🔧 检查并添加必要的列...")
            
            # 需要添加的列定义
            columns_to_add = [
                ('y_probability', 'DECIMAL(10,6) DEFAULT NULL'),
                ('ma_20', 'DECIMAL(20,6) DEFAULT NULL'),
                ('ma_60', 'DECIMAL(20,6) DEFAULT NULL'),
                ('new_midprice', 'DECIMAL(20,6) DEFAULT NULL'),
                ('new_controller', 'INT DEFAULT NULL'),
                ('new_Full_Y', 'DECIMAL(20,10) DEFAULT NULL')
            ]
            
            added_count = 0
            for col_name, col_definition in columns_to_add:
                # 检查列是否已存在
                self.cursor.execute(f"""
                    SELECT COUNT(*) FROM information_schema.COLUMNS 
                    WHERE TABLE_SCHEMA = 'finance' 
                    AND TABLE_NAME = '{self.table_name}' 
                    AND COLUMN_NAME = '{col_name}'
                """)
                
                if self.cursor.fetchone()[0] == 0:
                    # 添加列
                    alter_sql = f"ALTER TABLE {self.table_name} ADD COLUMN {col_name} {col_definition}"
                    self.cursor.execute(alter_sql)
                    print(f"   ✅ {col_name}列已添加")
                    added_count += 1
                else:
                    print(f"   ⚠️ {col_name}列已存在，跳过")
            
            print(f"📊 总共添加了 {added_count} 个新列")
            return True
            
        except mysql.connector.Error as e:
            print(f"❌ 添加列失败: {e}")
            return False
    
    def calculate_simple_midprice(self):
        """计算简单的midprice (High + Low) / 2"""
        try:
            print(f"\n📊 计算简单midprice...")
            
            self.cursor.execute(f"""
                UPDATE {self.table_name} 
                SET new_midprice = (High + Low) / 2
                WHERE High IS NOT NULL AND Low IS NOT NULL
            """)
            
            print("✅ 简单midprice计算完成")
            return True
            
        except mysql.connector.Error as e:
            print(f"❌ 计算midprice失败: {e}")
            return False
    
    def calculate_moving_averages(self):
        """计算移动平均线"""
        try:
            print(f"\n📈 计算移动平均线...")
            
            # 计算MA20
            print("   📊 计算MA20...")
            self.cursor.execute(f"""
                UPDATE {self.table_name} t1 
                JOIN (
                    SELECT Date, 
                        AVG(Close) OVER (ORDER BY Date ASC ROWS 19 PRECEDING) as ma20_value 
                    FROM {self.table_name} 
                    ORDER BY Date ASC
                ) t2 ON t1.Date = t2.Date 
                SET t1.ma_20 = t2.ma20_value
            """)
            print("   ✅ MA20计算完成")
            
            # 计算MA60
            print("   📊 计算MA60...")
            self.cursor.execute(f"""
                UPDATE {self.table_name} t1 
                JOIN (
                    SELECT Date, 
                        AVG(Close) OVER (ORDER BY Date ASC ROWS 59 PRECEDING) as ma60_value 
                    FROM {self.table_name} 
                    ORDER BY Date ASC
                ) t2 ON t1.Date = t2.Date 
                SET t1.ma_60 = t2.ma60_value
            """)
            print("   ✅ MA60计算完成")
            
            return True
            
        except mysql.connector.Error as e:
            print(f"❌ 计算移动平均线失败: {e}")
            return False
    
    def calculate_y_probability(self):
        """计算y_probability"""
        try:
            print(f"\n🧮 计算y_probability...")
            
            # 基础y_probability计算
            print("   📊 计算基础y_probability...")
            self.cursor.execute(f"""
                UPDATE {self.table_name} 
                SET y_probability = CASE 
                  WHEN ma_20 > 0 THEN 
                    GREATEST(0.1, LEAST(0.9, 
                      CASE WHEN (Close / ma_20) >= 1 THEN 
                        0.5 + 0.4 * ((Close / ma_20 - 1) / (1 + ABS(Close / ma_20 - 1))) 
                      ELSE 
                        0.5 - 0.4 * ((1 - Close / ma_20) / (1 + ABS(1 - Close / ma_20))) 
                      END 
                    )) 
                  ELSE 0.5 
                END 
                WHERE ma_20 IS NOT NULL AND Close IS NOT NULL
            """)
            print("   ✅ 基础y_probability计算完成")
            
            # 添加趋势调整
            print("   📊 添加趋势调整...")
            self.cursor.execute(f"""
                UPDATE {self.table_name} 
                SET y_probability = GREATEST(0.1, LEAST(0.9, 
                  y_probability + CASE 
                    WHEN ma_20 > 0 AND ma_60 > 0 THEN 
                      0.1 * ((ma_20 / ma_60 - 1) / (1 + ABS(ma_20 / ma_60 - 1))) 
                    ELSE 0 
                  END 
                )) 
                WHERE ma_20 IS NOT NULL AND ma_60 IS NOT NULL AND y_probability IS NOT NULL
            """)
            print("   ✅ 趋势调整完成")
            
            return True
            
        except mysql.connector.Error as e:
            print(f"❌ 计算y_probability失败: {e}")
            return False
    
    def calculate_controller_and_full_y(self):
        """计算controller和Full_Y"""
        try:
            print(f"\n📊 计算controller和Full_Y...")
            
            # 计算controller (二元逻辑)
            print("   📊 计算new_controller...")
            self.cursor.execute(f"""
                UPDATE {self.table_name} 
                SET new_controller = CASE WHEN (Close - new_midprice) > 0 THEN 1 ELSE 0 END 
                WHERE new_midprice IS NOT NULL
            """)
            print("   ✅ new_controller计算完成")
            
            # 计算Full_Y (累积比例)
            print("   📊 计算new_Full_Y...")
            self.cursor.execute(f"""
                UPDATE {self.table_name} t1 
                JOIN (
                    SELECT Date, 
                        SUM(new_controller) OVER (ORDER BY Date ASC ROWS UNBOUNDED PRECEDING) as cumulative_count, 
                        ROW_NUMBER() OVER (ORDER BY Date ASC) as row_num 
                    FROM {self.table_name} 
                    WHERE new_controller IS NOT NULL 
                    ORDER BY Date ASC
                ) t2 ON t1.Date = t2.Date 
                SET t1.new_Full_Y = t2.cumulative_count / t2.row_num
            """)
            print("   ✅ new_Full_Y计算完成")
            
            return True
            
        except mysql.connector.Error as e:
            print(f"❌ 计算controller和Full_Y失败: {e}")
            return False
    
    def verify_results(self):
        """验证计算结果"""
        try:
            print(f"\n🔍 验证计算结果...")
            
            # 检查数据完整性
            self.cursor.execute(f"""
                SELECT 
                    COUNT(*) as total_rows,
                    COUNT(y_probability) as y_prob_count,
                    COUNT(new_Full_Y) as full_y_count,
                    COUNT(new_controller) as controller_count,
                    COUNT(ma_20) as ma20_count,
                    COUNT(ma_60) as ma60_count,
                    COUNT(new_midprice) as midprice_count,
                    MIN(y_probability) as min_y_prob,
                    MAX(y_probability) as max_y_prob,
                    AVG(y_probability) as avg_y_prob,
                    SUM(new_controller) as controller_1_count
                FROM {self.table_name}
            """)
            
            stats = self.cursor.fetchone()
            total_rows = stats[0]
            y_prob_count = stats[1]
            full_y_count = stats[2]
            controller_count = stats[3]
            ma20_count = stats[4]
            ma60_count = stats[5]
            midprice_count = stats[6]
            min_y_prob = float(stats[7]) if stats[7] is not None else 0.0
            max_y_prob = float(stats[8]) if stats[8] is not None else 0.0
            avg_y_prob = float(stats[9]) if stats[9] is not None else 0.0
            controller_1_count = stats[10] if stats[10] is not None else 0
            
            print(f"📊 验证统计:")
            print(f"   • 总记录数: {total_rows}")
            print(f"   • y_probability记录数: {y_prob_count}")
            print(f"   • new_Full_Y记录数: {full_y_count}")
            print(f"   • new_controller记录数: {controller_count}")
            print(f"   • ma_20记录数: {ma20_count}")
            print(f"   • ma_60记录数: {ma60_count}")
            print(f"   • new_midprice记录数: {midprice_count}")
            print(f"   • y_probability范围: {min_y_prob:.6f} ~ {max_y_prob:.6f}")
            print(f"   • y_probability平均值: {avg_y_prob:.6f}")
            print(f"   • new_controller=1的数量: {controller_1_count}")
            
            # 计算覆盖率和k值
            if total_rows > 0:
                y_prob_coverage = (y_prob_count / total_rows * 100)
                controller_coverage = (controller_count / total_rows * 100)
                k_value = controller_1_count / controller_count if controller_count > 0 else 0
                
                print(f"   • y_probability覆盖率: {y_prob_coverage:.1f}%")
                print(f"   • new_controller覆盖率: {controller_coverage:.1f}%")
                print(f"   • k值 (强势比例): {k_value:.6f}")
            
            # 显示最新10条数据
            self.cursor.execute(f"""
                SELECT 
                    Date, 
                    Close,
                    ma_20,
                    ma_60,
                    new_midprice,
                    y_probability,
                    new_controller,
                    new_Full_Y
                FROM {self.table_name} 
                WHERE y_probability IS NOT NULL
                ORDER BY Date DESC 
                LIMIT 10
            """)
            
            latest_results = self.cursor.fetchall()
            print(f"\n📊 最新10条数据:")
            print("日期          | 收盘价    | MA20      | MA60      | y_prob  | controller | Full_Y")
            print("-" * 90)
            for row in latest_results:
                date_str = str(row[0])
                close_val = float(row[1]) if row[1] is not None else 0.0
                ma20_val = float(row[2]) if row[2] is not None else 0.0
                ma60_val = float(row[3]) if row[3] is not None else 0.0
                y_prob_val = float(row[5]) if row[5] is not None else 0.0
                controller_val = row[6] if row[6] is not None else 0
                full_y_val = float(row[7]) if row[7] is not None else 0.0
                print(f"{date_str} | {close_val:9.2f} | {ma20_val:9.2f} | {ma60_val:9.2f} | {y_prob_val:7.4f} | {controller_val:10d} | {full_y_val:10.6f}")
            
            # y_probability分布分析
            self.cursor.execute(f"""
                SELECT 
                    CASE 
                        WHEN y_probability < 0.3 THEN '看跌(<0.3)'
                        WHEN y_probability < 0.4 THEN '偏跌(0.3-0.4)'
                        WHEN y_probability < 0.6 THEN '中性(0.4-0.6)'
                        WHEN y_probability < 0.7 THEN '偏涨(0.6-0.7)'
                        ELSE '看涨(>0.7)'
                    END as y_prob_range,
                    COUNT(*) as count,
                    ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM {self.table_name} WHERE y_probability IS NOT NULL), 2) as percentage
                FROM {self.table_name}
                WHERE y_probability IS NOT NULL
                GROUP BY 1
                ORDER BY MIN(y_probability)
            """)
            
            y_prob_distribution = self.cursor.fetchall()
            print(f"\n📊 y_probability分布分析:")
            print("范围           | 数量   | 百分比")
            print("-" * 35)
            for row in y_prob_distribution:
                range_name = row[0]
                count = row[1]
                percentage = float(row[2])
                print(f"{range_name:15s} | {count:6d} | {percentage:6.2f}%")
            
            return True
            
        except mysql.connector.Error as e:
            print(f"❌ 验证结果失败: {e}")
            return False
    
    def close_connection(self):
        """关闭数据库连接"""
        if self.cursor:
            self.cursor.close()
        if self.connection:
            self.connection.close()
        print("🔒 数据库连接已关闭")
    
    def run(self):
        """执行主流程"""
        print("🎯 为新数据库的hkhsi50表添加y_probability (独立版本)")
        print("=" * 70)
        print(f"📅 执行时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"🏠 数据库: {self.db_config['host']}/{self.db_config['database']}")
        print(f"📊 目标表: {self.table_name}")
        
        try:
            # 1. 连接数据库
            if not self.connect_database():
                return False
            
            # 2. 检查并添加必要的列
            if not self.check_and_add_columns():
                return False
            
            # 3. 计算简单midprice
            if not self.calculate_simple_midprice():
                return False
            
            # 4. 计算移动平均线
            if not self.calculate_moving_averages():
                return False
            
            # 5. 计算y_probability
            if not self.calculate_y_probability():
                return False
            
            # 6. 计算controller和Full_Y
            if not self.calculate_controller_and_full_y():
                return False
            
            # 7. 验证结果
            if not self.verify_results():
                return False
            
            print("\n🎉 hkhsi50表的y_probability添加完成!")
            print("💡 新增字段:")
            print("   • y_probability: 控股商托价概率")
            print("   • ma_20, ma_60: 移动平均线")
            print("   • new_midprice: 简单中值价格 (High+Low)/2")
            print("   • new_controller: 二元强弱判断")
            print("   • new_Full_Y: 累积强势比例")
            
            print("\n📝 使用示例:")
            print("   SELECT Date, Close, y_probability, new_controller, new_Full_Y")
            print("   FROM hkhsi50 ORDER BY Date DESC LIMIT 10;")
            
            return True
            
        except Exception as e:
            print(f"❌ 执行失败: {e}")
            return False
        
        finally:
            self.close_connection()

def main():
    """主函数"""
    adder = HKHsi50YProbabilityStandalone()
    success = adder.run()
    
    if success:
        print("\n✅ 任务完成!")
        print("📝 hkhsi50表现在包含完整的y_probability分析")
    else:
        print("\n❌ 任务失败!")

if __name__ == "__main__":
    main()
