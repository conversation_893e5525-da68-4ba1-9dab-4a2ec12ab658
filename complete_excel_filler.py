#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
完整Excel记录填充系统
====================

确保Excel表格的所有字段都被正确填充
包括交易逻辑、技术指标、资金管理等

作者: Cosmoon NG
"""

import pandas as pd
import yfinance as yf
import numpy as np
import mysql.connector
from datetime import datetime, timedelta
from scipy import stats
import os
import warnings
warnings.filterwarnings('ignore')

class CompleteExcelFiller:
    """完整Excel记录填充器"""
    
    def __init__(self):
        self.symbol = "0023.HK"
        self.excel_file = "交易记录追踪0023HK.xlsx"
        
        # MySQL配置
        self.mysql_config = {
            'host': 'localhost',
            'port': 3306,
            'user': 'root',
            'password': '12345678',
            'database': 'finance'
        }
        
        # 交易参数
        self.initial_capital = 10000  # 初始资金
        self.monthly_addition = 3000  # 每月追加资金
        self.position_size = 0.8      # 仓位比例
        self.commission_rate = 0.001  # 手续费率
        
        # 止盈止损参数
        self.take_profit_long = 0.025   # 多头止盈2.5%
        self.stop_loss_long = 0.015     # 多头止损1.5%
        self.take_profit_short = 0.015  # 空头止盈1.5%
        self.stop_loss_short = 0.025    # 空头止损2.5%
        
        print("📊 完整Excel记录填充系统")

    def get_database_data(self):
        """从数据库获取最新的技术指标数据"""
        try:
            connection = mysql.connector.connect(**self.mysql_config)
            cursor = connection.cursor()
            
            # 获取最新记录
            query = """
            SELECT Date, Close, Y_Value, X_Value, E_Value, MFI, RSI, 
                   TypicalPrice, MoneyFlow, PositiveMoneyFlow, NegativeMoneyFlow,
                   MoneyFlowRatio, TradingSignal, i, midprice, Controller, Full_Y, E
            FROM eab_0023hk 
            ORDER BY Date DESC 
            LIMIT 1
            """
            
            cursor.execute(query)
            result = cursor.fetchone()
            
            if result:
                columns = [desc[0] for desc in cursor.description]
                data = dict(zip(columns, result))
                
                cursor.close()
                connection.close()
                
                return data
            else:
                cursor.close()
                connection.close()
                return None
                
        except Exception as e:
            print(f"❌ 数据库查询失败: {e}")
            return None

    def get_market_data(self):
        """获取市场数据"""
        try:
            ticker = yf.Ticker(self.symbol)
            
            # 获取实时数据
            info = ticker.info
            hist = ticker.history(period="5d")
            
            if hist.empty:
                return None
            
            latest = hist.iloc[-1]
            
            return {
                'date': latest.name.date(),
                'open': latest['Open'],
                'high': latest['High'],
                'low': latest['Low'],
                'close': latest['Close'],
                'volume': int(latest['Volume']),
                'previous_close': info.get('regularMarketPreviousClose', latest['Close'])
            }
            
        except Exception as e:
            print(f"❌ 获取市场数据失败: {e}")
            return None

    def load_existing_records(self):
        """加载现有Excel记录"""
        try:
            if os.path.exists(self.excel_file):
                df = pd.read_excel(self.excel_file)
                print(f"📋 加载现有记录: {len(df)}条")
                return df
            else:
                print("📋 创建新的Excel文件")
                return pd.DataFrame()
        except Exception as e:
            print(f"❌ 加载Excel失败: {e}")
            return pd.DataFrame()

    def get_current_position_status(self, df):
        """获取当前持仓状态"""
        if len(df) == 0:
            return {
                'position': 0,  # 0=空仓, 1=多头, -1=空头
                'entry_price': 0,
                'entry_date': None,
                'current_capital': self.initial_capital,
                'total_realized_pnl': 0
            }
        
        # 从最新记录获取状态
        latest = df.iloc[-1]
        
        # 判断持仓状态
        position = 0
        entry_price = 0
        entry_date = None
        
        # 向前查找最近的开仓记录
        for i in range(len(df)-1, -1, -1):
            record = df.iloc[i]
            if record['交易类型'] == '开仓':
                if '多头' in str(record['交易方向']):
                    position = 1
                elif '空头' in str(record['交易方向']):
                    position = -1
                entry_price = record['交易价格']
                entry_date = record['交易日期']
                break
            elif record['交易类型'] == '平仓':
                position = 0
                break
        
        return {
            'position': position,
            'entry_price': entry_price,
            'entry_date': entry_date,
            'current_capital': latest.get('账户余额', self.initial_capital),
            'total_realized_pnl': latest.get('实现盈亏', 0)
        }

    def generate_trading_signal(self, db_data):
        """生成交易信号"""
        if not db_data:
            return 0, "观望", "数据不足"
        
        y = db_data.get('Y_Value', 0.5)
        x = db_data.get('X_Value', 0.5)
        e = db_data.get('E_Value', 0)
        
        # 多头信号
        if e > 0 and x > 0.45 and y > 0.45:
            return 1, "强烈买入", f"XYE多头信号 E={e:.4f}"
        
        # 空头信号
        elif (y < 0.3 or x < 0.3 or 
              (x > 0.45 and y < 0.35) or
              (x < 0.45 and y > 0.35)):
            return -1, "强烈卖出", f"XYE空头信号 Y={y:.4f} X={x:.4f}"
        
        else:
            return 0, "观望", f"无明确信号 E={e:.4f}"

    def check_stop_conditions(self, current_price, position_status):
        """检查止盈止损条件"""
        if position_status['position'] == 0:
            return False, "", 0
        
        entry_price = position_status['entry_price']
        position = position_status['position']
        
        if position == 1:  # 多头持仓
            profit_ratio = (current_price - entry_price) / entry_price
            
            if profit_ratio >= self.take_profit_long:
                return True, "多头止盈", profit_ratio
            elif profit_ratio <= -self.stop_loss_long:
                return True, "多头止损", profit_ratio
                
        elif position == -1:  # 空头持仓
            profit_ratio = (entry_price - current_price) / entry_price
            
            if profit_ratio >= self.take_profit_short:
                return True, "空头止盈", profit_ratio
            elif profit_ratio <= -self.stop_loss_short:
                return True, "空头止损", profit_ratio
        
        return False, "", 0

    def calculate_risk_level(self, db_data, current_price):
        """计算风险等级"""
        if not db_data:
            return "中风险"
        
        # 基于价格波动和技术指标
        rsi = db_data.get('RSI', 50)
        mfi = db_data.get('MFI', 50)
        
        risk_score = 0
        
        # RSI风险评估
        if rsi > 80 or rsi < 20:
            risk_score += 2
        elif rsi > 70 or rsi < 30:
            risk_score += 1
        
        # MFI风险评估
        if mfi > 80 or mfi < 20:
            risk_score += 2
        elif mfi > 70 or mfi < 30:
            risk_score += 1
        
        # 价格位置风险
        midprice = db_data.get('midprice', current_price)
        if abs(current_price - midprice) / midprice > 0.05:
            risk_score += 1
        
        if risk_score >= 4:
            return "极高风险"
        elif risk_score >= 3:
            return "高风险"
        elif risk_score >= 1:
            return "中风险"
        else:
            return "低风险"

    def add_monthly_capital(self, df, current_date):
        """检查是否需要追加月度资金"""
        if len(df) == 0:
            return 0
        
        # 获取上次追加资金的日期
        last_addition_date = None
        for i in range(len(df)-1, -1, -1):
            if '追加资金' in str(df.iloc[i]['备注']):
                last_addition_date = pd.to_datetime(df.iloc[i]['交易日期']).date()
                break
        
        # 如果是新月份，追加资金
        if last_addition_date is None:
            current_month = current_date.replace(day=1)
            if current_date.day >= 1:  # 每月1号后追加
                return self.monthly_addition
        else:
            last_month = last_addition_date.replace(day=1)
            current_month = current_date.replace(day=1)
            if current_month > last_month:
                return self.monthly_addition
        
        return 0

    def create_complete_record(self, market_data, db_data, position_status):
        """创建完整的交易记录"""
        
        current_date = market_data['date']
        current_price = market_data['close']
        
        # 加载现有记录
        df = self.load_existing_records()
        
        # 检查月度资金追加
        monthly_addition = self.add_monthly_capital(df, current_date)
        current_capital = position_status['current_capital'] + monthly_addition
        
        # 生成交易信号
        signal, signal_strength, signal_desc = self.generate_trading_signal(db_data)
        
        # 检查止盈止损
        should_close, close_reason, profit_ratio = self.check_stop_conditions(current_price, position_status)
        
        # 决定交易动作
        trade_type = "观察"
        trade_direction = "无"
        realized_pnl = 0
        quantity = 0
        trade_amount = 0
        
        if should_close:
            # 平仓
            trade_type = "平仓"
            trade_direction = close_reason
            quantity = int(current_capital * self.position_size / position_status['entry_price'])
            trade_amount = current_price * quantity
            realized_pnl = profit_ratio * trade_amount
            current_capital += realized_pnl
            position_status['position'] = 0
            
        elif position_status['position'] == 0 and signal != 0:
            # 开仓
            trade_type = "开仓"
            trade_direction = "多头" if signal == 1 else "空头"
            quantity = int(current_capital * self.position_size / current_price)
            trade_amount = current_price * quantity
            current_capital -= trade_amount * self.commission_rate
            position_status['position'] = signal
            position_status['entry_price'] = current_price
        
        # 计算持仓相关数据
        if position_status['position'] != 0:
            current_quantity = int(current_capital * self.position_size / current_price)
            current_market_value = current_price * current_quantity
            
            if position_status['position'] == 1:
                unrealized_pnl = (current_price - position_status['entry_price']) * current_quantity
            else:
                unrealized_pnl = (position_status['entry_price'] - current_price) * current_quantity
        else:
            current_quantity = 0
            current_market_value = 0
            unrealized_pnl = 0
        
        # 计算费用
        commission = trade_amount * self.commission_rate if trade_type in ["开仓", "平仓"] else 0
        net_amount = trade_amount - commission
        
        # 计算总资产和收益率
        total_assets = current_capital + current_market_value
        
        if len(df) > 0:
            prev_total = df['总资产'].iloc[-1]
            daily_return = (total_assets - prev_total) / prev_total * 100
        else:
            daily_return = 0
        
        cumulative_return = (total_assets - self.initial_capital) / self.initial_capital * 100
        
        # 风险等级
        risk_level = self.calculate_risk_level(db_data, current_price)
        
        # 备注信息
        note_parts = []
        if monthly_addition > 0:
            note_parts.append(f"追加资金{monthly_addition}")
        if db_data:
            note_parts.append(f"数据库已更新")
        note_parts.append(signal_desc)
        note = " ".join(note_parts)
        
        # 创建完整记录
        record = {
            '交易日期': current_date.strftime('%Y-%m-%d'),
            '交易类型': trade_type,
            '交易方向': trade_direction,
            '交易价格': current_price,
            '持仓数量': current_quantity,
            '交易金额': trade_amount,
            '手续费': commission,
            '净交易额': net_amount,
            '持仓成本': position_status['entry_price'] if position_status['position'] != 0 else 0,
            '当前市值': current_market_value,
            '浮动盈亏': unrealized_pnl,
            '实现盈亏': realized_pnl,
            '累计盈亏': realized_pnl + unrealized_pnl,
            '账户余额': current_capital,
            '总资产': total_assets,
            '收益率': daily_return,
            '累计收益率': cumulative_return,
            'Y值': db_data.get('Y_Value', 0.5) if db_data else 0.5,
            'X值': db_data.get('X_Value', 0.5) if db_data else 0.5,
            'E值': db_data.get('E_Value', 0) if db_data else 0,
            '信号强度': signal_strength,
            '风险等级': risk_level,
            '备注': note
        }
        
        return record

    def fill_complete_excel(self):
        """填充完整的Excel记录"""
        print("📊 开始填充完整Excel记录...")
        
        # 获取数据
        market_data = self.get_market_data()
        if not market_data:
            print("❌ 无法获取市场数据")
            return False
        
        db_data = self.get_database_data()
        if not db_data:
            print("⚠️ 无法获取数据库数据，使用默认值")
        
        # 加载现有记录并获取持仓状态
        df = self.load_existing_records()
        position_status = self.get_current_position_status(df)
        
        print(f"📈 当前持仓状态: {position_status}")
        
        # 创建完整记录
        record = self.create_complete_record(market_data, db_data, position_status)
        
        # 添加到DataFrame
        new_df = pd.concat([df, pd.DataFrame([record])], ignore_index=True)
        
        # 保存到Excel
        new_df.to_excel(self.excel_file, index=False)
        
        # 显示结果
        print(f"\n✅ Excel记录填充完成")
        print(f"📊 交易状态: {record['交易类型']}")
        print(f"💰 总资产: {record['总资产']:,.2f} 港元")
        print(f"📈 累计收益率: {record['累计收益率']:.2f}%")
        print(f"🎯 交易信号: {record['信号强度']}")
        print(f"⚠️ 风险等级: {record['风险等级']}")
        print(f"💾 文件: {self.excel_file}")
        
        return True

def main():
    """主函数"""
    print("📊 完整Excel记录填充系统")
    print("=" * 50)
    
    filler = CompleteExcelFiller()
    
    try:
        success = filler.fill_complete_excel()
        if success:
            print("\n🎯 所有字段已完整填充")
        else:
            print("\n❌ 填充失败")
    except Exception as e:
        print(f"❌ 系统错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
