CREATE DEFINER=`root`@`localhost` PROCEDURE `sp_updatecontroller_enhanced`(
    IN tablename VARCHAR(64),
    OUT result_k DECIMAL(20,6)
)
BEGIN
    DECLARE col_exists INT DEFAULT 0;
    DECLARE v_start_time DATETIME DEFAULT NOW();
    DECLARE done INT DEFAULT FALSE;
    DECLARE v_id INT;
    DECLARE v_controller INT;
    DECLARE cumulative_controller_1 INT DEFAULT 0;
    DECLARE row_number INT DEFAULT 0;
    DECLARE calculated_full_y DECIMAL(10,3);

    -- 1. 首先更新midprice (调用sp_averagelineV3)
    SELECT CONCAT('开始更新midprice - 表: ', tablename, ' 时间: ', v_start_time) AS start_message;
    CALL sp_averagelineV3(tablename);
    SELECT 'midprice更新完成' AS midprice_status;

    -- 2. 检查controller列是否存在
    SET @sql = CONCAT(
        'SELECT COUNT(*) INTO @col_exists FROM information_schema.COLUMNS ',
        'WHERE TABLE_SCHEMA=DATABASE() AND TABLE_NAME=''', tablename, ''' AND COLUMN_NAME=''controller'''
    );
    PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;
    SET col_exists = @col_exists;

    IF col_exists = 0 THEN
        SET @sql = CONCAT('ALTER TABLE `', tablename, '` ADD COLUMN `controller` INT');
        PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;
        SELECT 'controller列已添加' AS controller_status;
    END IF;

    -- 3. 检查Full_Y列是否存在
    SET @sql = CONCAT(
        'SELECT COUNT(*) INTO @col_exists FROM information_schema.COLUMNS ',
        'WHERE TABLE_SCHEMA=DATABASE() AND TABLE_NAME=''', tablename, ''' AND COLUMN_NAME=''Full_Y'''
    );
    PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;
    SET col_exists = @col_exists;

    IF col_exists = 0 THEN
        SET @sql = CONCAT('ALTER TABLE `', tablename, '` ADD COLUMN `Full_Y` DECIMAL(10,3)');
        PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;
        SELECT 'Full_Y列已添加' AS full_y_status;
    END IF;

    -- 4. 检查E列是否存在
    SET @sql = CONCAT(
        'SELECT COUNT(*) INTO @col_exists FROM information_schema.COLUMNS ',
        'WHERE TABLE_SCHEMA=DATABASE() AND TABLE_NAME=''', tablename, ''' AND COLUMN_NAME=''E'''
    );
    PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;
    SET col_exists = @col_exists;

    IF col_exists = 0 THEN
        SET @sql = CONCAT('ALTER TABLE `', tablename, '` ADD COLUMN `E` DECIMAL(10,6)');
        PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;
        SELECT 'E列已添加' AS e_status;
    END IF;

    -- 5. 更新controller字段
    SET @sql = CONCAT(
        'UPDATE `', tablename, '` ',
        'SET controller = CASE WHEN close < midprice THEN 0 WHEN close > midprice THEN 1 ELSE 3 END'
    );
    PREPARE stmt FROM @sql;
    EXECUTE stmt;
    DEALLOCATE PREPARE stmt;
    SELECT 'controller字段更新完成' AS controller_update_status;

    -- 6. 更新Full_Y字段（使用游标方式，兼容所有MariaDB版本）
    SELECT '开始计算Full_Y累积比例' AS full_y_start_status;
    
    -- 先清空Full_Y字段
    SET @sql = CONCAT('UPDATE `', tablename, '` SET Full_Y = NULL');
    PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;
    
    -- 声明游标
    BEGIN
        DECLARE cur CURSOR FOR 
            SELECT id, controller 
            FROM information_schema.tables 
            WHERE 1=0; -- 临时声明，稍后重新定义
        
        DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;
        
        -- 重新定义游标
        SET @sql = CONCAT('SELECT id, controller FROM `', tablename, '` ORDER BY date, id');
        
        -- 使用临时表方式计算Full_Y
        SET @sql = CONCAT('DROP TEMPORARY TABLE IF EXISTS temp_full_y_calc');
        PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;
        
        SET @sql = CONCAT(
            'CREATE TEMPORARY TABLE temp_full_y_calc AS ',
            'SELECT id, controller, ',
            '       @row_num := @row_num + 1 as row_num, ',
            '       @cum_ctrl1 := @cum_ctrl1 + (CASE WHEN controller = 1 THEN 1 ELSE 0 END) as cum_controller_1, ',
            '       ROUND(@cum_ctrl1 / @row_num, 3) as calculated_full_y ',
            'FROM (SELECT @row_num := 0, @cum_ctrl1 := 0) r, ',
            '     (SELECT id, controller FROM `', tablename, '` ORDER BY date, id) t'
        );
        PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;
        
        -- 更新Full_Y字段
        SET @sql = CONCAT(
            'UPDATE `', tablename, '` t1 ',
            'JOIN temp_full_y_calc t2 ON t1.id = t2.id ',
            'SET t1.Full_Y = t2.calculated_full_y'
        );
        PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;
        
        -- 清理临时表
        SET @sql = 'DROP TEMPORARY TABLE IF EXISTS temp_full_y_calc';
        PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;
    END;
    
    SELECT 'Full_Y累积比例计算完成' AS full_y_update_status;

    -- 7. 更新E字段（使用表中的MFI字段）
    SELECT '开始计算E字段' AS e_start_status;
    
    -- E = (8*MFI/100 - 3)*Full_Y - 3*MFI/100 + 1
    SET @sql = CONCAT(
        'UPDATE `', tablename, '` ',
        'SET E = ROUND((8*MFI/100 - 3)*Full_Y - 3*MFI/100 + 1, 6) ',
        'WHERE Full_Y IS NOT NULL AND MFI IS NOT NULL'
    );
    PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;
    SELECT 'E字段计算完成' AS e_update_status;

    -- 8. 计算k值并存入OUT参数
    SET @sql = CONCAT(
        'SELECT SUM(CASE WHEN controller = 1 THEN 1 ELSE 0 END) / NULLIF(COUNT(*), 0) INTO @k_value ',
        'FROM `', tablename, '`'
    );
    PREPARE stmt FROM @sql;
    EXECUTE stmt;
    DEALLOCATE PREPARE stmt;

    SET result_k = @k_value;

    -- 9. 返回统计信息
    SELECT
        CONCAT('更新完成 - 表: ', tablename) AS completion_message,
        CONCAT('k值: ', IFNULL(result_k, 0)) AS k_value_message,
        CONCAT('耗时: ', TIMESTAMPDIFF(SECOND, v_start_time, NOW()), ' 秒') AS duration;

    -- 10. 显示详细更新统计
    SET @sql = CONCAT(
        'SELECT ',
        '    COUNT(*) as total_records, ',
        '    SUM(CASE WHEN controller = 0 THEN 1 ELSE 0 END) as controller_0, ',
        '    SUM(CASE WHEN controller = 1 THEN 1 ELSE 0 END) as controller_1, ',
        '    SUM(CASE WHEN controller = 3 THEN 1 ELSE 0 END) as controller_3, ',
        '    MIN(Full_Y) as min_full_y, ',
        '    MAX(Full_Y) as max_full_y, ',
        '    AVG(Full_Y) as avg_full_y, ',
        '    MIN(E) as min_e, ',
        '    MAX(E) as max_e, ',
        '    AVG(E) as avg_e, ',
        '    COUNT(CASE WHEN E IS NOT NULL THEN 1 END) as e_calculated, ',
        '    COUNT(CASE WHEN MFI IS NOT NULL THEN 1 END) as mfi_available ',
        'FROM `', tablename, '`'
    );
    PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;

END
