#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
重新计算Y和X值
==============

根据Cosmoon的博弈论理论重新计算Y和X值：
- Y值：基于均值回归理论，控股商托价概率
- X值：资金流入比例 = 流入资金 / (流入资金 + 流出资金)

作者: Cosmoon NG
日期: 2025年7月
"""

import pymysql
import pandas as pd
import numpy as np
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

class YXRecalculator:
    def __init__(self):
        """初始化Y和X重计算器"""
        self.db_config = {
            'host': 'localhost',
            'port': 3306,
            'database': 'finance',
            'user': 'root',
            'password': '12345678',
            'charset': 'utf8mb4'
        }
        
        self.data = None
        
    def connect_database(self):
        """连接数据库"""
        try:
            self.connection = pymysql.connect(**self.db_config)
            print("✅ 数据库连接成功")
            return True
        except Exception as e:
            print(f"❌ 数据库连接失败: {e}")
            return False
    
    def load_raw_data(self):
        """加载原始数据"""
        print("📊 加载原始数据进行Y和X重计算...")
        
        try:
            # 加载所有可用数据
            query = """
                SELECT 
                    date,
                    open,
                    high,
                    low,
                    close,
                    volume,
                    y_probability as old_y,
                    inflow_ratio as old_x
                FROM hk00023 
                WHERE date >= '2000-01-01'
                ORDER BY date ASC
            """
            
            self.data = pd.read_sql(query, self.connection)
            
            if self.data.empty:
                print("❌ 未找到数据")
                return False
            
            # 数据预处理
            self.data['date'] = pd.to_datetime(self.data['date'])
            self.data = self.data.sort_values('date').reset_index(drop=True)
            
            print(f"📈 原始数据加载完成:")
            print(f"   • 数据期间: {self.data['date'].min().strftime('%Y-%m-%d')} 至 {self.data['date'].max().strftime('%Y-%m-%d')}")
            print(f"   • 总记录数: {len(self.data)} 条")
            
            return True
            
        except Exception as e:
            print(f"❌ 数据加载失败: {e}")
            return False
    
    def calculate_y_values(self):
        """重新计算Y值 - 基于均值回归理论"""
        print("\n🔢 重新计算Y值 (控股商托价概率)...")
        
        # 1. 计算移动平均线作为均值基准
        self.data['ma_20'] = self.data['close'].rolling(window=20).mean()
        self.data['ma_60'] = self.data['close'].rolling(window=60).mean()
        self.data['ma_120'] = self.data['close'].rolling(window=120).mean()
        
        # 2. 计算价格相对于均值的位置
        self.data['price_vs_ma20'] = self.data['close'] / self.data['ma_20']
        self.data['price_vs_ma60'] = self.data['close'] / self.data['ma_60']
        self.data['price_vs_ma120'] = self.data['close'] / self.data['ma_120']
        
        # 3. 计算线性回归趋势 (Cosmoon的均值回归公式)
        # 使用滚动窗口计算局部趋势
        window = 60  # 60天窗口
        
        def calculate_trend_y(series):
            """计算趋势Y值"""
            if len(series) < 10:
                return 0.5  # 默认值
            
            x = np.arange(len(series))
            y = series.values
            
            # 线性回归: y = m + bx
            try:
                # 计算斜率b和截距m
                x_mean = np.mean(x)
                y_mean = np.mean(y)
                
                numerator = np.sum((x - x_mean) * (y - y_mean))
                denominator = np.sum((x - x_mean) ** 2)
                
                if denominator == 0:
                    b = 0
                else:
                    b = numerator / denominator
                
                m = y_mean - b * x_mean
                
                # 计算当前价格相对于趋势线的位置
                current_trend = m + b * (len(series) - 1)
                current_price = series.iloc[-1]
                
                # Y值：价格高于趋势线的概率
                price_ratio = current_price / current_trend if current_trend > 0 else 1
                
                # 将比率转换为概率 (0-1之间)
                if price_ratio >= 1:
                    # 价格高于趋势线，控股商托价概率
                    y_prob = 0.5 + 0.4 * min((price_ratio - 1) / 0.2, 1)  # 最高0.9
                else:
                    # 价格低于趋势线，控股商压价概率
                    y_prob = 0.5 - 0.4 * min((1 - price_ratio) / 0.2, 1)  # 最低0.1
                
                return max(0.1, min(0.9, y_prob))
                
            except:
                return 0.5
        
        # 计算滚动Y值
        print("   • 计算基于均值回归的Y值...")
        self.data['new_y'] = self.data['close'].rolling(window=window).apply(calculate_trend_y, raw=False)
        
        # 4. 结合成交量的影响
        # 高成交量时，价格更可能反映真实意图
        self.data['volume_ma'] = self.data['volume'].rolling(window=20).mean()
        self.data['volume_ratio'] = self.data['volume'] / self.data['volume_ma']
        
        # 调整Y值：高成交量时增强信号
        volume_factor = np.tanh(self.data['volume_ratio'] - 1) * 0.1  # -0.1 到 +0.1
        self.data['new_y'] = self.data['new_y'] + volume_factor
        self.data['new_y'] = np.clip(self.data['new_y'], 0.1, 0.9)
        
        # 填充初始NaN值
        self.data['new_y'].fillna(0.5, inplace=True)
        
        print(f"   ✅ Y值计算完成")
        print(f"   • Y值范围: {self.data['new_y'].min():.3f} - {self.data['new_y'].max():.3f}")
        print(f"   • Y值平均: {self.data['new_y'].mean():.3f}")
    
    def calculate_x_values(self):
        """重新计算X值 - 资金流入比例"""
        print("\n🔢 重新计算X值 (资金流入比例)...")
        
        # 1. 基于价格和成交量计算资金流向
        # 资金流入 = 成交量 × (收盘价 - 开盘价) / 开盘价 (简化模型)
        
        # 计算价格变化率
        self.data['price_change'] = (self.data['close'] - self.data['open']) / self.data['open']
        
        # 计算当日资金流向强度
        self.data['money_flow'] = self.data['volume'] * self.data['price_change']
        
        # 2. 计算滚动资金流入流出
        window = 20  # 20天窗口
        
        def calculate_inflow_ratio(money_flows):
            """计算资金流入比例"""
            if len(money_flows) == 0:
                return 0.5
            
            # 分离流入和流出
            inflows = money_flows[money_flows > 0].sum()
            outflows = abs(money_flows[money_flows < 0].sum())
            
            total_flow = inflows + outflows
            
            if total_flow == 0:
                return 0.5  # 无明显流向
            
            inflow_ratio = inflows / total_flow
            return max(0.1, min(0.9, inflow_ratio))
        
        print("   • 计算滚动资金流入比例...")
        self.data['new_x'] = self.data['money_flow'].rolling(window=window).apply(calculate_inflow_ratio, raw=False)
        
        # 3. 结合相对强弱指标调整
        # 计算RSI作为辅助指标
        def calculate_rsi(prices, window=14):
            """计算RSI"""
            delta = prices.diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=window).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=window).mean()
            rs = gain / loss
            rsi = 100 - (100 / (1 + rs))
            return rsi / 100  # 转换为0-1范围
        
        rsi = calculate_rsi(self.data['close'])
        
        # 结合RSI调整X值
        self.data['new_x'] = 0.7 * self.data['new_x'] + 0.3 * rsi
        self.data['new_x'] = np.clip(self.data['new_x'], 0.1, 0.9)
        
        # 填充初始NaN值
        self.data['new_x'].fillna(0.5, inplace=True)
        
        print(f"   ✅ X值计算完成")
        print(f"   • X值范围: {self.data['new_x'].min():.3f} - {self.data['new_x'].max():.3f}")
        print(f"   • X值平均: {self.data['new_x'].mean():.3f}")
    
    def validate_calculations(self):
        """验证计算结果"""
        print("\n🔍 验证Y和X计算结果...")
        
        # 计算新的E值
        self.data['new_e'] = (8 * self.data['new_x'] * self.data['new_y'] - 
                             3 * self.data['new_x'] - 3 * self.data['new_y'] + 1)
        
        # 统计各区域分布
        high_profit = ((self.data['new_y'] > 0.5) & (self.data['new_x'] > 0.5)).sum()
        control_zone = ((self.data['new_y'] > 0.333) & (self.data['new_y'] < 0.4)).sum()
        strong_loss = ((self.data['new_y'] < 0.25) | (self.data['new_x'] < 0.25)).sum()
        positive_e = (self.data['new_e'] > 0).sum()
        
        total = len(self.data)
        
        print(f"📊 新计算结果统计:")
        print(f"   • 高值盈利区 (Y>0.5, X>0.5): {high_profit} 天 ({high_profit/total*100:.1f}%)")
        print(f"   • 控股商控制区 (0.333<Y<0.4): {control_zone} 天 ({control_zone/total*100:.1f}%)")
        print(f"   • 强亏损区 (Y<0.25或X<0.25): {strong_loss} 天 ({strong_loss/total*100:.1f}%)")
        print(f"   • E>0 (理论盈利): {positive_e} 天 ({positive_e/total*100:.1f}%)")
        
        # 与原值对比
        if 'old_y' in self.data.columns and 'old_x' in self.data.columns:
            print(f"\n📈 与原值对比:")
            
            # 计算相关性
            valid_mask = self.data['old_y'].notna() & self.data['old_x'].notna()
            if valid_mask.sum() > 0:
                y_corr = self.data.loc[valid_mask, 'new_y'].corr(self.data.loc[valid_mask, 'old_y'])
                x_corr = self.data.loc[valid_mask, 'new_x'].corr(self.data.loc[valid_mask, 'old_x'])
                
                print(f"   • Y值相关性: {y_corr:.3f}")
                print(f"   • X值相关性: {x_corr:.3f}")
                
                print(f"   • 原Y值范围: {self.data['old_y'].min():.3f} - {self.data['old_y'].max():.3f}")
                print(f"   • 新Y值范围: {self.data['new_y'].min():.3f} - {self.data['new_y'].max():.3f}")
                print(f"   • 原X值范围: {self.data['old_x'].min():.3f} - {self.data['old_x'].max():.3f}")
                print(f"   • 新X值范围: {self.data['new_x'].min():.3f} - {self.data['new_x'].max():.3f}")
    
    def update_database(self):
        """更新数据库中的Y和X值"""
        print(f"\n💾 更新数据库中的Y和X值...")
        
        try:
            cursor = self.connection.cursor()
            
            # 批量更新
            update_count = 0
            batch_size = 1000
            
            for i in range(0, len(self.data), batch_size):
                batch = self.data.iloc[i:i+batch_size]
                
                for _, row in batch.iterrows():
                    update_sql = """
                        UPDATE hk00023 
                        SET y_probability = %s, inflow_ratio = %s
                        WHERE date = %s
                    """
                    
                    cursor.execute(update_sql, (
                        float(row['new_y']),
                        float(row['new_x']),
                        row['date'].strftime('%Y-%m-%d')
                    ))
                    update_count += 1
                
                # 每批次提交一次
                self.connection.commit()
                print(f"   • 已更新 {min(i+batch_size, len(self.data))}/{len(self.data)} 条记录")
            
            print(f"✅ 数据库更新完成，共更新 {update_count} 条记录")
            
        except Exception as e:
            print(f"❌ 数据库更新失败: {e}")
            self.connection.rollback()
    
    def save_comparison_report(self):
        """保存对比报告"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"y_x_recalculation_report_{timestamp}.csv"
        
        # 选择关键列保存
        report_data = self.data[[
            'date', 'close', 'volume',
            'old_y', 'new_y', 'old_x', 'new_x', 'new_e'
        ]].copy()
        
        report_data.to_csv(filename, index=False, encoding='utf-8-sig')
        print(f"📄 对比报告已保存: {filename}")

def main():
    """主函数"""
    print("🎯 Cosmoon博弈论策略 - Y和X值重计算")
    print("="*60)
    print("📅 计算时间:", datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
    print("👨‍💼 理论作者: Cosmoon NG")
    
    # 创建重计算器
    calculator = YXRecalculator()
    
    # 连接数据库
    if not calculator.connect_database():
        return
    
    # 加载原始数据
    if not calculator.load_raw_data():
        return
    
    # 重新计算Y值
    calculator.calculate_y_values()
    
    # 重新计算X值
    calculator.calculate_x_values()
    
    # 验证计算结果
    calculator.validate_calculations()
    
    # 保存对比报告
    calculator.save_comparison_report()
    
    # 询问是否更新数据库
    print(f"\n❓ 是否要将新计算的Y和X值更新到数据库？")
    print(f"⚠️ 这将覆盖现有的y_probability和inflow_ratio字段")
    
    choice = input("请输入 'yes' 确认更新，或按回车取消: ").strip().lower()
    
    if choice in ['yes', 'y', '是']:
        calculator.update_database()
        print(f"\n🎉 Y和X值重计算并更新完成!")
    else:
        print(f"\n📋 Y和X值已重计算，但未更新数据库")
        print(f"💡 您可以查看对比报告后再决定是否更新")
    
    print(f"💡 现在可以使用新的Y和X值运行Cosmoon策略了!")

if __name__ == "__main__":
    main()
