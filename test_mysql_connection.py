#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import mysql.connector
from datetime import datetime

def test_mysql_connection():
    """测试MySQL连接和存储过程调用"""
    
    print("测试MySQL数据库连接")
    print("=" * 50)
    
    # MySQL配置
    config = {
        'host': 'localhost',
        'port': 3306,
        'user': 'root',
        'password': '12345678'
    }
    
    try:
        # 尝试连接
        print(f"连接到 {config['host']}:{config['port']} ...")
        connection = mysql.connector.connect(**config)
        
        if connection.is_connected():
            print("✅ MySQL连接成功")
            
            cursor = connection.cursor()
            
            # 显示数据库列表
            cursor.execute("SHOW DATABASES")
            databases = cursor.fetchall()
            print(f"📋 可用数据库:")
            for db in databases:
                print(f"   - {db[0]}")
            
            # 尝试选择数据库
            try:
                cursor.execute("USE stock_analysis")
                print("✅ 已选择 stock_analysis 数据库")
                
                # 检查存储过程是否存在
                cursor.execute("""
                    SELECT ROUTINE_NAME 
                    FROM INFORMATION_SCHEMA.ROUTINES 
                    WHERE ROUTINE_SCHEMA = 'stock_analysis' 
                    AND ROUTINE_NAME = 'sp_combined_stock_analysis'
                """)
                
                result = cursor.fetchone()
                if result:
                    print("✅ 找到存储过程: sp_combined_stock_analysis")
                    
                    # 尝试调用存储过程
                    print("🔄 调用存储过程: sp_combined_stock_analysis('eab_0023hk')")
                    cursor.callproc('sp_combined_stock_analysis', ['eab_0023hk'])
                    
                    # 提交事务
                    connection.commit()
                    print("✅ 存储过程执行成功")
                    
                    # 获取结果
                    for result in cursor.stored_results():
                        rows = result.fetchall()
                        if rows:
                            print(f"📊 返回 {len(rows)} 条记录")
                        
                else:
                    print("❌ 未找到存储过程: sp_combined_stock_analysis")
                    
            except mysql.connector.Error as e:
                if e.errno == 1049:  # 数据库不存在
                    print("❌ 数据库 'stock_analysis' 不存在")
                    print("💡 请先创建数据库和存储过程")
                else:
                    print(f"❌ 数据库操作错误: {e}")
            
            cursor.close()
            connection.close()
            print("🔌 MySQL连接已关闭")
            
        else:
            print("❌ MySQL连接失败")
            
    except mysql.connector.Error as e:
        if e.errno == 2003:
            print("❌ 无法连接到MySQL服务器 (连接被拒绝)")
            print("💡 请检查MySQL服务是否启动")
        elif e.errno == 1045:
            print("❌ MySQL认证失败 (用户名或密码错误)")
            print("💡 请检查用户名和密码")
        else:
            print(f"❌ MySQL错误: {e}")
    except Exception as e:
        print(f"❌ 连接失败: {e}")

def create_fallback_system():
    """创建备用系统 (不依赖MySQL)"""
    
    print("\n" + "=" * 50)
    print("创建备用系统 (跳过MySQL)")
    print("=" * 50)
    
    # 这里可以实现不依赖MySQL的版本
    print("💡 如果MySQL不可用，可以:")
    print("1. 跳过数据库更新步骤")
    print("2. 直接更新Excel表格")
    print("3. 记录MySQL连接失败的日志")

if __name__ == "__main__":
    test_mysql_connection()
    create_fallback_system()
