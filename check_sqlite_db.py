#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查SQLite数据库结构
=================

检查当前目录中的 SQLite 数据库文件

作者: Cosmoon NG
日期: 2025年7月
"""

import sqlite3
import os
import pandas as pd

def check_sqlite_databases():
    """检查当前目录中的所有SQLite数据库"""
    # 列出当前目录中的所有.db文件
    db_files = [f for f in os.listdir('.') if f.endswith('.db')]
    
    if not db_files:
        print("❌ 未找到任何SQLite数据库文件")
        return
    
    print(f"找到 {len(db_files)} 个数据库文件：")
    for db_file in db_files:
        print(f"\n检查数据库：{db_file}")
        print("="*50)
        
        try:
            conn = sqlite3.connect(db_file)
            cursor = conn.cursor()
            
            # 获取所有表名
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
            tables = cursor.fetchall()
            
            if not tables:
                print("数据库中没有表")
                continue
            
            print(f"发现 {len(tables)} 个表：")
            
            for table in tables:
                table_name = table[0]
                print(f"\n表名：{table_name}")
                
                # 获取表结构
                cursor.execute(f"PRAGMA table_info({table_name})")
                columns = cursor.fetchall()
                
                print("列结构：")
                for col in columns:
                    print(f"  - {col[1]} ({col[2]})")
                
                # 获取行数
                cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
                row_count = cursor.fetchone()[0]
                print(f"总行数：{row_count:,}")
                
                # 显示前几行数据
                if row_count > 0:
                    df = pd.read_sql(f"SELECT * FROM {table_name} LIMIT 3", conn)
                    print("\n数据样例：")
                    print(df)
            
            conn.close()
            
        except Exception as e:
            print(f"❌ 检查失败: {str(e)}")

def main():
    """主函数"""
    print("\nSQLite数据库检查工具")
    print("="*50)
    
    try:
        check_sqlite_databases()
    except Exception as e:
        print(f"\n❌ 程序运行失败: {str(e)}")
    else:
        print("\n✅ 检查完成")

if __name__ == "__main__":
    main()
