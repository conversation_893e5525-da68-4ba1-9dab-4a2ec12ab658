#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整回归线策略代码 - 三标的回测系统
===============================

包含：
1. HSI50 恒生指数回测
2. HK00023 东亚银行回测  
3. HK0001 长和集团回测
4. 综合分析和报告生成

核心策略：
- 线性回归线判断趋势
- XY值确认信号强度
- 不对称止盈止损
- 每月定投2K港币

作者: Cosmoon NG
日期: 2025年7月16日
"""

import pandas as pd
import yfinance as yf
import numpy as np
import matplotlib.pyplot as plt
from scipy import stats
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

class RegressionStrategy:
    def __init__(self, symbol, name):
        """初始化回归线策略"""
        self.symbol = symbol
        self.name = name
        self.initial_capital = 30000  # 初始资金30K
        self.monthly_addition = 2000  # 每月追加2K
        
        # 不对称止盈止损设计
        self.take_profit_long = 0.016   # 多头止盈 1.6%
        self.stop_loss_long = 0.008     # 多头止损 0.8%
        self.take_profit_short = 0.008  # 空头止盈 0.8%
        self.stop_loss_short = 0.016    # 空头止损 1.6%
        
        # 交易状态
        self.position = 0  # 0=空仓, 1=多头, -1=空头
        self.entry_price = 0
        self.shares = 0
        self.data = None
        
        # 记录
        self.trades = []
        self.equity_curve = []
        self.last_month = None
        self.r_squared = 0
    
    def fetch_data(self):
        """获取历史数据"""
        print(f"📈 获取{self.name}25年历史数据...")
        
        try:
            end_date = datetime.now()
            start_date = end_date - timedelta(days=25*365)
            
            ticker = yf.Ticker(self.symbol)
            self.data = ticker.history(start=start_date, end=end_date)
            
            if self.data.empty:
                print(f"❌ {self.name}数据获取失败")
                return False
            
            self.data.reset_index(inplace=True)
            self.data.columns = [col.lower() for col in self.data.columns]
            
            print(f"✅ {self.name}数据获取成功: {len(self.data):,}天")
            return True
            
        except Exception as e:
            print(f"❌ {self.name}数据获取失败: {e}")
            return False
    
    def calculate_indicators(self):
        """计算技术指标"""
        print(f"📊 计算{self.name}技术指标...")
        
        # 1. 线性回归线 (核心)
        self.data['i'] = range(1, len(self.data) + 1)
        slope, intercept, r_value, p_value, std_err = stats.linregress(
            self.data['i'], self.data['close']
        )
        
        self.data['regression_line'] = intercept + slope * self.data['i']
        self.data['price_position'] = (self.data['close'] - self.data['regression_line']) / self.data['regression_line']
        self.r_squared = r_value ** 2
        
        # 2. RSI
        delta = self.data['close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
        rs = gain / loss
        self.data['rsi'] = 100 - (100 / (1 + rs))
        
        # 3. Y值 (控制系数)
        self.data['price_momentum'] = self.data['close'].pct_change(10)
        self.data['y_value'] = (self.data['rsi'] / 100 + 
                               np.tanh(self.data['price_momentum'] * 5) + 1) / 2
        self.data['y_value'] = np.clip(self.data['y_value'], 0.1, 0.9)
        
        # 4. X值 (资金流比例)
        self.data['volume_ma'] = self.data['volume'].rolling(window=20).mean()
        price_change = (self.data['close'] - self.data['open']) / self.data['open']
        money_flow = self.data['volume'] * price_change
        
        def calc_inflow_ratio(flows):
            if len(flows) == 0 or flows.isna().all():
                return 0.5
            flows = flows.dropna()
            if len(flows) == 0:
                return 0.5
            
            inflows = flows[flows > 0].sum()
            outflows = abs(flows[flows < 0].sum())
            total_flow = inflows + outflows
            
            return inflows / total_flow if total_flow > 0 else 0.5
        
        self.data['x_value'] = money_flow.rolling(window=20).apply(calc_inflow_ratio, raw=False)
        self.data['x_value'] = np.clip(self.data['x_value'], 0.1, 0.9)
        self.data['x_value'].fillna(0.5, inplace=True)
        
        # 5. E值 (期望值)
        self.data['e_value'] = (8 * self.data['x_value'] * self.data['y_value'] - 
                               3 * self.data['x_value'] - 3 * self.data['y_value'] + 1)
        
        print(f"✅ {self.name}指标计算完成 (R² = {self.r_squared:.4f})")
    
    def get_trading_signal(self, row):
        """获取交易信号"""
        # 买涨条件: E>0 且 X>0.45 且 Y>0.45 且价格低于回归线
        if (row['e_value'] > 0 and 
            row['x_value'] > 0.45 and 
            row['y_value'] > 0.45 and 
            row['price_position'] < 0):
            return 'LONG'
        
        # 买跌条件: (Y<0.25 或 X<0.25) 且价格高于回归线
        elif ((row['y_value'] < 0.25 or row['x_value'] < 0.25) and 
              row['price_position'] > 0):
            return 'SHORT'
        
        return 'HOLD'
    
    def check_exit_conditions(self, current_price, high_price, low_price):
        """检查止盈止损条件"""
        if self.position == 0:
            return False, 0, ''
        
        if self.position == 1:  # 多头
            profit_ratio = (high_price - self.entry_price) / self.entry_price
            if profit_ratio >= self.take_profit_long:
                return True, self.entry_price * (1 + self.take_profit_long), 'long_tp'
            
            loss_ratio = (self.entry_price - low_price) / self.entry_price
            if loss_ratio >= self.stop_loss_long:
                return True, self.entry_price * (1 - self.stop_loss_long), 'long_sl'
        
        elif self.position == -1:  # 空头
            profit_ratio = (self.entry_price - low_price) / self.entry_price
            if profit_ratio >= self.take_profit_short:
                return True, self.entry_price * (1 - self.take_profit_short), 'short_tp'
            
            loss_ratio = (high_price - self.entry_price) / self.entry_price
            if loss_ratio >= self.stop_loss_short:
                return True, self.entry_price * (1 + self.stop_loss_short), 'short_sl'
        
        return False, 0, ''
    
    def calculate_shares(self, capital, price):
        """计算股数"""
        if self.symbol.endswith('.HK'):  # 港股100股一手
            max_investment = capital * 0.95
            max_shares = int(max_investment / price)
            lots = max_shares // 100
            return lots * 100
        else:  # 指数
            return capital * 0.95 / price
    
    def add_monthly_capital(self, date, capital):
        """每月增加资金"""
        current_month = date.replace(day=1)
        if self.last_month is None or current_month > self.last_month:
            self.last_month = current_month
            return capital + self.monthly_addition
        return capital
    
    def backtest(self):
        """执行回测"""
        print(f"\n🚀 开始{self.name}回测...")
        
        capital = self.initial_capital
        total_trades = 0
        winning_trades = 0
        
        for i in range(60, len(self.data)):
            row = self.data.iloc[i]
            date = row['date']
            price = row['close']
            high = row['high']
            low = row['low']
            
            # 每月增加资金
            capital = self.add_monthly_capital(date, capital)
            
            # 检查退出条件
            should_exit, exit_price, exit_type = self.check_exit_conditions(price, high, low)
            
            if should_exit:
                # 计算盈亏
                if self.position == 1:  # 多头
                    profit_amount = (exit_price - self.entry_price) * self.shares
                    capital += self.shares * exit_price
                else:  # 空头
                    profit_amount = (self.entry_price - exit_price) * self.shares
                    capital -= self.shares * exit_price
                
                # 记录交易
                self.trades.append({
                    'exit_date': date.strftime('%Y-%m-%d'),
                    'exit_type': exit_type,
                    'exit_price': round(exit_price, 2),
                    'profit_amount': round(profit_amount, 2),
                    'capital_after': round(capital, 2)
                })
                
                if profit_amount > 0:
                    winning_trades += 1
                total_trades += 1
                
                self.position = 0
                self.entry_price = 0
                self.shares = 0
            
            # 检查开仓信号
            if self.position == 0:
                signal = self.get_trading_signal(row)
                
                if signal in ['LONG', 'SHORT']:
                    self.shares = self.calculate_shares(capital, price)
                    if self.shares > 0:
                        if signal == 'LONG':
                            investment = self.shares * price
                            capital -= investment
                            self.position = 1
                        else:  # SHORT
                            proceeds = self.shares * price
                            capital += proceeds
                            self.position = -1
                        
                        self.entry_price = price
                        
                        self.trades.append({
                            'entry_date': date.strftime('%Y-%m-%d'),
                            'entry_type': f'{signal.lower()}_entry',
                            'entry_price': round(price, 2),
                            'shares': self.shares,
                            'price_position': round(row['price_position'], 4),
                            'x_value': round(row['x_value'], 3),
                            'y_value': round(row['y_value'], 3),
                            'e_value': round(row['e_value'], 3)
                        })
            
            # 计算总价值
            if self.position == 1:
                total_value = capital + self.shares * price
            elif self.position == -1:
                unrealized_pnl = (self.entry_price - price) * self.shares
                total_value = capital + unrealized_pnl
            else:
                total_value = capital
            
            # 记录权益
            self.equity_curve.append({
                'date': date.strftime('%Y-%m-%d'),
                'capital': capital,
                'total_value': total_value,
                'position': self.position,
                'price': price,
                'regression_line': row['regression_line'],
                'price_position': row['price_position']
            })
        
        self.final_capital = capital
        self.final_total_value = self.equity_curve[-1]['total_value'] if self.equity_curve else capital
        
        print(f"✅ {self.name}回测完成!")
        print(f"📊 交易次数: {total_trades}, 胜率: {winning_trades/total_trades*100:.1f}%" if total_trades > 0 else "📊 无交易")
        print(f"💰 最终价值: {self.final_total_value:,.0f} 港币")
        
        return pd.DataFrame(self.trades), pd.DataFrame(self.equity_curve)
    
    def analyze_results(self):
        """分析结果"""
        trades_df = pd.DataFrame(self.trades)
        equity_df = pd.DataFrame(self.equity_curve)
        
        if len(equity_df) == 0:
            return
        
        # 基本统计
        entry_trades = trades_df[trades_df['entry_type'].notna()] if 'entry_type' in trades_df.columns else pd.DataFrame()
        exit_trades = trades_df[trades_df['exit_type'].notna()] if 'exit_type' in trades_df.columns else pd.DataFrame()
        
        total_trades = len(entry_trades)
        total_invested = self.initial_capital + (len(equity_df) // 30) * self.monthly_addition
        net_return = self.final_total_value - total_invested
        net_return_rate = (net_return / total_invested) * 100
        
        # 年化收益率
        start_date = pd.to_datetime(equity_df['date'].iloc[0])
        end_date = pd.to_datetime(equity_df['date'].iloc[-1])
        years = (end_date - start_date).days / 365
        annual_return_rate = ((self.final_total_value / self.initial_capital) ** (1/years) - 1) * 100
        
        print(f"\n📊 {self.name}分析结果:")
        print(f"• 总投入: {total_invested:,} 港币")
        print(f"• 最终价值: {self.final_total_value:,.0f} 港币")
        print(f"• 净收益: {net_return:,.0f} 港币")
        print(f"• 净收益率: {net_return_rate:.2f}%")
        print(f"• 年化收益率: {annual_return_rate:.2f}%")
        print(f"• 回归线R²: {self.r_squared:.4f}")
        
        if len(exit_trades) > 0:
            winning_trades = len(exit_trades[exit_trades['profit_amount'] > 0])
            win_rate = winning_trades / len(exit_trades) * 100
            print(f"• 胜率: {win_rate:.1f}%")
        
        return {
            'name': self.name,
            'symbol': self.symbol,
            'total_invested': total_invested,
            'final_value': self.final_total_value,
            'net_return': net_return,
            'net_return_rate': net_return_rate,
            'annual_return': annual_return_rate,
            'r_squared': self.r_squared,
            'total_trades': total_trades,
            'win_rate': win_rate if len(exit_trades) > 0 else 0
        }

def run_comprehensive_backtest():
    """运行综合回测"""
    print("🏢 回归线策略综合回测系统")
    print("=" * 80)
    
    # 三个标的
    targets = [
        ('^HSI', 'HSI50恒生指数'),
        ('0023.HK', 'HK00023东亚银行'),
        ('0001.HK', 'HK0001长和集团')
    ]
    
    results = []
    
    # 逐个回测
    for symbol, name in targets:
        strategy = RegressionStrategy(symbol, name)
        
        if strategy.fetch_data():
            strategy.calculate_indicators()
            trades_df, equity_df = strategy.backtest()
            result = strategy.analyze_results()
            if result:
                results.append(result)
    
    # 综合分析
    if results:
        print(f"\n🏆 综合回测结果汇总")
        print("=" * 80)
        
        total_invested = sum([r['total_invested'] for r in results])
        total_final_value = sum([r['final_value'] for r in results])
        total_net_return = total_final_value - total_invested
        total_return_rate = (total_net_return / total_invested) * 100
        
        print(f"💰 总投入资金: {total_invested:,} 港币")
        print(f"💎 总最终价值: {total_final_value:,.0f} 港币")
        print(f"📈 总净收益: {total_net_return:,.0f} 港币")
        print(f"📊 总收益率: {total_return_rate:.2f}%")
        print(f"🚀 投资倍数: {total_final_value/total_invested:.2f}倍")
        
        # 排名
        results_sorted = sorted(results, key=lambda x: x['annual_return'], reverse=True)
        print(f"\n🏅 年化收益率排名:")
        for i, result in enumerate(results_sorted, 1):
            medal = "🥇" if i == 1 else "🥈" if i == 2 else "🥉"
            print(f"{medal} {result['name']}: {result['annual_return']:.2f}%")
        
        # 保存结果
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        results_df = pd.DataFrame(results)
        filename = f"综合回测结果_{timestamp}.xlsx"
        results_df.to_excel(filename, index=False)
        print(f"\n✅ 综合结果已保存: {filename}")
    
    print("\n" + "=" * 80)
    print("🎉 综合回测完成!")

def create_single_target_template():
    """创建单标的回测模板"""
    template_code = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
单标的回归线策略模板
==================

使用方法：
1. 修改 SYMBOL 和 NAME 变量
2. 运行代码即可获得回测结果

作者: Cosmoon NG
"""

import pandas as pd
import yfinance as yf
import numpy as np
from scipy import stats
from datetime import datetime, timedelta

# ========== 配置区域 ==========
SYMBOL = "^HSI"  # 修改这里：股票代码
NAME = "恒生指数"  # 修改这里：股票名称
# =============================

class SimpleRegressionStrategy:
    def __init__(self):
        self.symbol = SYMBOL
        self.name = NAME
        self.initial_capital = 30000
        self.monthly_addition = 2000

        # 止盈止损参数
        self.take_profit_long = 0.016
        self.stop_loss_long = 0.008
        self.take_profit_short = 0.008
        self.stop_loss_short = 0.016

        self.position = 0
        self.entry_price = 0
        self.shares = 0
        self.trades = []
        self.equity_curve = []
        self.last_month = None

    def fetch_data(self):
        """获取数据"""
        print(f"📈 获取{self.name}数据...")
        end_date = datetime.now()
        start_date = end_date - timedelta(days=25*365)

        ticker = yf.Ticker(self.symbol)
        self.data = ticker.history(start=start_date, end=end_date)
        self.data.reset_index(inplace=True)
        self.data.columns = [col.lower() for col in self.data.columns]

        print(f"✅ 获取{len(self.data)}天数据")
        return len(self.data) > 0

    def calculate_indicators(self):
        """计算指标"""
        print("📊 计算技术指标...")

        # 回归线
        self.data['i'] = range(1, len(self.data) + 1)
        slope, intercept, r_value, _, _ = stats.linregress(self.data['i'], self.data['close'])
        self.data['regression_line'] = intercept + slope * self.data['i']
        self.data['price_position'] = (self.data['close'] - self.data['regression_line']) / self.data['regression_line']
        self.r_squared = r_value ** 2

        # RSI
        delta = self.data['close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(14).mean()
        self.data['rsi'] = 100 - (100 / (1 + gain/loss))

        # Y值
        momentum = self.data['close'].pct_change(10)
        self.data['y_value'] = np.clip((self.data['rsi']/100 + np.tanh(momentum*5) + 1)/2, 0.1, 0.9)

        # X值
        price_change = (self.data['close'] - self.data['open']) / self.data['open']
        money_flow = self.data['volume'] * price_change

        def inflow_ratio(flows):
            flows = flows.dropna()
            if len(flows) == 0: return 0.5
            inflows = flows[flows > 0].sum()
            outflows = abs(flows[flows < 0].sum())
            total = inflows + outflows
            return inflows/total if total > 0 else 0.5

        self.data['x_value'] = np.clip(money_flow.rolling(20).apply(inflow_ratio), 0.1, 0.9)
        self.data['x_value'].fillna(0.5, inplace=True)

        # E值
        self.data['e_value'] = (8 * self.data['x_value'] * self.data['y_value'] -
                               3 * self.data['x_value'] - 3 * self.data['y_value'] + 1)

        print(f"✅ 指标计算完成 (R² = {self.r_squared:.4f})")

    def get_signal(self, row):
        """获取交易信号"""
        if (row['e_value'] > 0 and row['x_value'] > 0.45 and
            row['y_value'] > 0.45 and row['price_position'] < 0):
            return 'LONG'
        elif ((row['y_value'] < 0.25 or row['x_value'] < 0.25) and
              row['price_position'] > 0):
            return 'SHORT'
        return 'HOLD'

    def backtest(self):
        """执行回测"""
        print(f"🚀 开始{self.name}回测...")

        capital = self.initial_capital
        total_trades = 0
        winning_trades = 0

        for i in range(60, len(self.data)):
            row = self.data.iloc[i]
            date = row['date']
            price = row['close']
            high = row['high']
            low = row['low']

            # 每月加资金
            current_month = date.replace(day=1)
            if self.last_month is None or current_month > self.last_month:
                self.last_month = current_month
                capital += self.monthly_addition

            # 检查退出
            if self.position != 0:
                should_exit = False
                exit_price = price

                if self.position == 1:  # 多头
                    if (high - self.entry_price) / self.entry_price >= self.take_profit_long:
                        should_exit, exit_price = True, self.entry_price * (1 + self.take_profit_long)
                    elif (self.entry_price - low) / self.entry_price >= self.stop_loss_long:
                        should_exit, exit_price = True, self.entry_price * (1 - self.stop_loss_long)

                elif self.position == -1:  # 空头
                    if (self.entry_price - low) / self.entry_price >= self.take_profit_short:
                        should_exit, exit_price = True, self.entry_price * (1 - self.take_profit_short)
                    elif (high - self.entry_price) / self.entry_price >= self.stop_loss_short:
                        should_exit, exit_price = True, self.entry_price * (1 + self.stop_loss_short)

                if should_exit:
                    if self.position == 1:
                        profit = (exit_price - self.entry_price) * self.shares
                        capital += self.shares * exit_price
                    else:
                        profit = (self.entry_price - exit_price) * self.shares
                        capital -= self.shares * exit_price

                    if profit > 0: winning_trades += 1
                    total_trades += 1
                    self.position = 0

            # 检查开仓
            if self.position == 0:
                signal = self.get_signal(row)
                if signal in ['LONG', 'SHORT']:
                    if self.symbol.endswith('.HK'):
                        self.shares = int(capital * 0.95 / price / 100) * 100
                    else:
                        self.shares = capital * 0.95 / price

                    if self.shares > 0:
                        if signal == 'LONG':
                            capital -= self.shares * price
                            self.position = 1
                        else:
                            capital += self.shares * price
                            self.position = -1
                        self.entry_price = price

            # 计算总价值
            if self.position == 1:
                total_value = capital + self.shares * price
            elif self.position == -1:
                total_value = capital + (self.entry_price - price) * self.shares
            else:
                total_value = capital

            self.equity_curve.append({
                'date': date.strftime('%Y-%m-%d'),
                'total_value': total_value,
                'price': price
            })

        self.final_value = self.equity_curve[-1]['total_value']

        # 分析结果
        equity_df = pd.DataFrame(self.equity_curve)
        total_invested = self.initial_capital + len(equity_df) // 30 * self.monthly_addition
        net_return = self.final_value - total_invested
        net_return_rate = net_return / total_invested * 100

        start_date = pd.to_datetime(equity_df['date'].iloc[0])
        end_date = pd.to_datetime(equity_df['date'].iloc[-1])
        years = (end_date - start_date).days / 365
        annual_return = ((self.final_value / self.initial_capital) ** (1/years) - 1) * 100

        print(f"\\n📊 {self.name}回测结果:")
        print(f"• 总投入: {total_invested:,} 港币")
        print(f"• 最终价值: {self.final_value:,.0f} 港币")
        print(f"• 净收益: {net_return:,.0f} 港币")
        print(f"• 净收益率: {net_return_rate:.2f}%")
        print(f"• 年化收益率: {annual_return:.2f}%")
        print(f"• 回归线R²: {self.r_squared:.4f}")
        print(f"• 交易次数: {total_trades}")
        if total_trades > 0:
            print(f"• 胜率: {winning_trades/total_trades*100:.1f}%")

def main():
    strategy = SimpleRegressionStrategy()
    if strategy.fetch_data():
        strategy.calculate_indicators()
        strategy.backtest()

if __name__ == "__main__":
    main()
'''

    with open('单标的回归线策略模板.py', 'w', encoding='utf-8') as f:
        f.write(template_code)

    print("✅ 单标的回测模板已创建: 单标的回归线策略模板.py")

if __name__ == "__main__":
    # 运行综合回测
    run_comprehensive_backtest()

    # 创建单标的模板
    create_single_target_template()
