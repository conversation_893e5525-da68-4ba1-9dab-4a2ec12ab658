#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
完整每日更新系统 - 包含持仓管理和Excel更新
执行顺序：
1. 更新数据库 (daily_update_eab_table.py)
2. 更新Full_Y和Controller字段
3. 持仓判断和Excel更新
4. 生成每日报告
"""

import subprocess
import sys
from datetime import datetime
import os
from position_manager_with_excel import PositionManager

def print_header(title):
    """打印标题"""
    print("\n" + "=" * 60)
    print(f"🎯 {title}")
    print("=" * 60)

def print_step(step_num, description):
    """打印步骤"""
    print(f"\n{step_num}️⃣ {description}")
    print("-" * 40)

def run_database_update():
    """步骤1: 更新数据库"""
    print_step(1, "更新数据库 (daily_update_eab_table.py)")

    try:
        # 检查文件是否存在
        if not os.path.exists("fixed_daily_update_eab_table.py"):
            print("⚠️ fixed_daily_update_eab_table.py 不存在，跳过数据库更新")
            return True

        # 运行数据库更新脚本
        result = subprocess.run([sys.executable, "fixed_daily_update_eab_table.py"],
                              capture_output=True, text=True, timeout=120)

        if result.returncode == 0:
            print("✅ 数据库更新成功")
            # 显示关键信息
            output_lines = result.stdout.split('\n')
            for line in output_lines:
                if any(keyword in line for keyword in ['今日数据', '收盘价', 'Y值', 'X值', 'E值', '成功率']):
                    print(f"   {line}")
            return True
        else:
            print("❌ 数据库更新失败")
            print(f"错误信息: {result.stderr}")
            return False

    except subprocess.TimeoutExpired:
        print("❌ 数据库更新超时")
        return False
    except Exception as e:
        print(f"❌ 数据库更新异常: {e}")
        return False

def run_full_y_update():
    """步骤2: 更新Full_Y和Controller字段"""
    print_step(2, "更新Full_Y和Controller字段")

    try:
        # 检查文件是否存在 - 优先使用简化版
        script_to_use = None
        if os.path.exists("simple_full_y_update.py"):
            script_to_use = "simple_full_y_update.py"
        elif os.path.exists("fixed_update_full_y_controller.py"):
            script_to_use = "fixed_update_full_y_controller.py"

        if not script_to_use:
            print("⚠️ Full_Y更新脚本不存在，跳过Full_Y更新")
            return True

        # 运行Full_Y更新脚本
        result = subprocess.run([sys.executable, script_to_use],
                              capture_output=True, text=True, timeout=60)

        if result.returncode == 0:
            print("✅ Full_Y和Controller更新成功")
            # 显示关键信息
            output_lines = result.stdout.split('\n')
            for line in output_lines:
                if any(keyword in line for keyword in ['更新统计', '最新记录', '完成时间']):
                    print(f"   {line}")
            return True
        else:
            print("❌ Full_Y更新失败")
            print(f"错误信息: {result.stderr}")
            return False

    except subprocess.TimeoutExpired:
        print("❌ Full_Y更新超时")
        return False
    except Exception as e:
        print(f"❌ Full_Y更新异常: {e}")
        return False

def run_position_management():
    """步骤3: 持仓管理和Excel更新"""
    print_step(3, "持仓判断和Excel更新")

    try:
        # 创建持仓管理器
        manager = PositionManager()

        # 运行持仓管理
        success = manager.run_daily_position_management()

        if success:
            print("✅ 持仓管理和Excel更新成功")
            return True
        else:
            print("❌ 持仓管理失败")
            return False

    except Exception as e:
        print(f"❌ 持仓管理异常: {e}")
        return False

def generate_daily_report():
    """步骤4: 生成每日报告"""
    print_step(4, "生成每日报告")

    try:
        # 获取最新的持仓状态
        manager = PositionManager()
        market_data = manager.get_latest_market_data()
        position_status = manager.get_current_position_from_excel()

        if not market_data:
            print("❌ 无法获取市场数据")
            return False

        # 生成报告
        report_date = datetime.now().strftime('%Y-%m-%d')
        report_time = datetime.now().strftime('%H:%M:%S')

        print("✅ 每日报告生成成功")
        print(f"\n📊 每日交易报告 ({report_date} {report_time})")
        print("-" * 50)

        # 市场数据
        print(f"📈 市场数据:")
        print(f"   收盘价: {market_data['close']:.2f} 港元")
        print(f"   Y值: {market_data['y_value']:.4f}")
        print(f"   X值: {market_data['x_value']:.4f}")
        print(f"   E值: {market_data['e_value']:.4f}")
        print(f"   RSI: {market_data['rsi']:.2f}")
        print(f"   MFI: {market_data['mfi']:.2f}")

        # 持仓状态
        print(f"\n💼 持仓状态:")
        if position_status['has_position']:
            print(f"   状态: 有持仓")
            print(f"   方向: {position_status['position_direction']}")
            print(f"   数量: {position_status['position_quantity']}股")
            print(f"   成本: {position_status['entry_price']:.2f}")
            print(f"   天数: {position_status['holding_days']}天")
            pnl_ratio = position_status['unrealized_pnl'] / (position_status['entry_price'] * position_status['position_quantity']) * 100
            print(f"   盈亏: {pnl_ratio:.2f}%")
        else:
            print(f"   状态: 空仓")

        print(f"   账户余额: {position_status['current_capital']:,.2f}")
        print(f"   总资产: {position_status['total_assets']:,.2f}")
        print(f"   累计收益率: {position_status['cumulative_return']:.2f}%")

        # 交易信号
        signal = manager.generate_trading_signal(market_data)
        print(f"\n🚦 交易信号:")
        print(f"   信号: {signal['signal']}")
        print(f"   强度: {signal['strength']}/5")
        print(f"   依据: {signal['reason']}")

        # 策略执行状态
        print(f"\n🎯 策略执行:")
        if not position_status['has_position']:
            print(f"   ✅ 符合'尽量不持仓'策略")
            print(f"   📋 当前保持空仓状态")
        else:
            if position_status['holding_days'] <= 3:
                print(f"   ⚠️ 当前有持仓，持仓{position_status['holding_days']}天")
                print(f"   📋 符合短期持仓原则")
            else:
                print(f"   ❌ 持仓时间过长({position_status['holding_days']}天)")
                print(f"   📋 建议尽快平仓")

        return True

    except Exception as e:
        print(f"❌ 报告生成异常: {e}")
        return False

def verify_files_exist():
    """验证必要文件是否存在"""
    required_files = [
        "position_manager_with_excel.py"
    ]

    optional_files = [
        "fixed_daily_update_eab_table.py",
        "fixed_update_full_y_controller.py"
    ]

    missing_required = []
    missing_optional = []

    for file in required_files:
        if not os.path.exists(file):
            missing_required.append(file)

    for file in optional_files:
        if not os.path.exists(file):
            missing_optional.append(file)

    if missing_required:
        print("❌ 缺少必要文件:")
        for file in missing_required:
            print(f"   • {file}")
        return False

    if missing_optional:
        print("⚠️ 缺少可选文件 (将跳过相关步骤):")
        for file in missing_optional:
            print(f"   • {file}")

    return True

def main():
    """主函数"""
    print_header("完整每日更新系统 - 包含持仓管理")
    print(f"🕐 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

    # 验证文件
    if not verify_files_exist():
        print("\n❌ 系统检查失败，请确保必要文件存在")
        return False

    print("✅ 系统检查通过")

    # 执行步骤
    success_count = 0
    total_steps = 4

    # 步骤1: 更新数据库
    if run_database_update():
        success_count += 1

    # 步骤2: 更新Full_Y
    if run_full_y_update():
        success_count += 1

    # 步骤3: 持仓管理
    if run_position_management():
        success_count += 1

    # 步骤4: 生成报告
    if generate_daily_report():
        success_count += 1

    # 总结
    print_header("每日更新完成总结")
    print(f"🕐 完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"📊 成功步骤: {success_count}/{total_steps}")
    print(f"📈 成功率: {success_count/total_steps*100:.1f}%")

    if success_count == total_steps:
        print("🎉 所有更新任务完成！")
        print("✅ 数据库已更新")
        print("✅ Full_Y字段已更新")
        print("✅ 持仓状态已判断")
        print("✅ Excel文件已更新")
        print("✅ 每日报告已生成")
        return True
    else:
        print("⚠️ 部分任务失败，请检查错误信息")
        return False

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n⚠️ 用户中断操作")
        sys.exit(1)
    except Exception as e:
        print(f"\n\n❌ 系统异常: {e}")
        sys.exit(1)
