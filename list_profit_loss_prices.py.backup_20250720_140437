#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
根据close价格和交易方向列出止盈止损价格
===================================

根据策略规则计算止盈止损价格：
- 高值盈利区 (买涨): 止盈+2%, 止损-1%
- 强亏损区 (买跌): 止盈+2%, 止损-1%  
- 其他区域 (买跌): 止盈+1%, 止损-2%
- 控股商控制区 (观望): 无止盈止损

作者: Cosmoon NG
日期: 2025年7月
"""

import pymysql

def list_profit_loss_prices():
    """列出所有记录的止盈止损价格"""
    db_config = {
        'host': '************',
        'port': 3306,
        'database': 'finance',
        'user': 'root',
        'password': '',
        'charset': 'utf8mb4'
    }
    
    try:
        connection = pymysql.connect(**db_config)
        cursor = connection.cursor()
        
        print("📊 根据close价格列出止盈止损价格")
        print("="*80)
        print("🔍 策略规则:")
        print("   • 高值盈利区 (买涨): 止盈+2%, 止损-1%")
        print("   • 强亏损区 (买跌): 止盈+2%, 止损-1%")
        print("   • 其他区域 (买跌): 止盈+1%, 止损-2%")
        print("   • 控股商控制区 (观望): 无止盈止损")
        print("="*80)
        
        # 获取所有记录
        cursor.execute("""
            SELECT 交易序号, 开仓日期, close, 平仓价格, 交易方向, `控制系数`, `资金流比例`,
                   CASE 
                       WHEN `控制系数` > 0.43 AND `资金流比例` > 0.43 THEN '高值盈利区'
                       WHEN `控制系数` > 0.333 AND `控制系数` < 0.4 THEN '控股商控制区'
                       WHEN (`控制系数` < 0.25 OR `资金流比例` < 0.25) AND NOT (`控制系数` > 0.333 AND `控制系数` < 0.4) THEN '强亏损区'
                       ELSE '其他区域'
                   END AS 策略区域
            FROM test 
            ORDER BY 交易序号
        """)
        
        records = cursor.fetchall()
        
        print(f"\n📊 所有记录的止盈止损价格:")
        print("-" * 140)
        print(f"{'序号':<4} {'日期':<12} {'策略区域':<12} {'方向':<6} {'开仓价':<8} {'平仓价':<8} "
              f"{'止盈价':<8} {'止损价':<8} {'实际结果':<10} {'价格变动%':<10}")
        print("-" * 140)
        
        for record in records:
            trade_id, open_date, open_price, close_price, direction, y_val, x_val, zone = record
            
            # 根据策略区域和方向确定止盈止损参数
            if zone == '控股商控制区':
                # 观望，无止盈止损
                profit_price = 0
                loss_price = 0
                result = '观望'
                price_change_pct = 0
            elif zone == '高值盈利区' and direction == '买涨':
                # 买涨: 止盈+2%, 止损-1%
                profit_price = open_price * (1 + 0.02)  # 止盈价格
                loss_price = open_price * (1 - 0.01)    # 止损价格
                
                # 判断实际结果
                if close_price >= profit_price:
                    result = '止盈'
                elif close_price <= loss_price:
                    result = '止损'
                else:
                    result = '到期平仓'
                
                price_change_pct = (close_price - open_price) / open_price * 100
                
            elif zone == '强亏损区' and direction == '买跌':
                # 买跌: 止盈+2%, 止损-1%
                profit_price = open_price * (1 - 0.02)  # 止盈价格 (价格下跌)
                loss_price = open_price * (1 + 0.01)    # 止损价格 (价格上涨)
                
                # 判断实际结果
                if close_price <= profit_price:
                    result = '止盈'
                elif close_price >= loss_price:
                    result = '止损'
                else:
                    result = '到期平仓'
                
                price_change_pct = (open_price - close_price) / open_price * 100  # 买跌收益
                
            elif zone == '其他区域' and direction == '买跌':
                # 买跌: 止盈+1%, 止损-2%
                profit_price = open_price * (1 - 0.01)  # 止盈价格 (价格下跌)
                loss_price = open_price * (1 + 0.02)    # 止损价格 (价格上涨)
                
                # 判断实际结果
                if close_price <= profit_price:
                    result = '止盈'
                elif close_price >= loss_price:
                    result = '止损'
                else:
                    result = '到期平仓'
                
                price_change_pct = (open_price - close_price) / open_price * 100  # 买跌收益
                
            else:
                # 异常情况
                profit_price = 0
                loss_price = 0
                result = '异常'
                price_change_pct = 0
            
            print(f"{trade_id:<4} {open_date:<12} {zone:<12} {direction:<6} {open_price:<8.2f} {close_price:<8.2f} "
                  f"{profit_price:<8.2f} {loss_price:<8.2f} {result:<10} {price_change_pct:<9.2f}%")
        
        # 统计各种结果
        print("\n" + "="*80)
        print("📈 止盈止损统计分析:")
        
        cursor.execute("""
            SELECT 
                CASE 
                    WHEN `控制系数` > 0.43 AND `资金流比例` > 0.43 THEN '高值盈利区'
                    WHEN `控制系数` > 0.333 AND `控制系数` < 0.4 THEN '控股商控制区'
                    WHEN (`控制系数` < 0.25 OR `资金流比例` < 0.25) AND NOT (`控制系数` > 0.333 AND `控制系数` < 0.4) THEN '强亏损区'
                    ELSE '其他区域'
                END AS 策略区域,
                交易方向,
                COUNT(*) as 总次数,
                AVG(close) as 平均开仓价,
                MIN(close) as 最低开仓价,
                MAX(close) as 最高开仓价
            FROM test 
            GROUP BY 策略区域, 交易方向
            ORDER BY 策略区域, 交易方向
        """)
        
        zone_stats = cursor.fetchall()
        
        print(f"\n📊 按策略区域统计:")
        print("-" * 100)
        print(f"{'策略区域':<12} {'方向':<6} {'次数':<6} {'平均开仓价':<10} {'价格范围':<20}")
        print("-" * 100)
        
        for zone, direction, count, avg_price, min_price, max_price in zone_stats:
            price_range = f"{float(min_price):.2f} - {float(max_price):.2f}"
            print(f"{zone:<12} {direction:<6} {count:<6} {float(avg_price):<10.2f} {price_range:<20}")
        
        # 计算理论止盈止损价格范围
        print(f"\n🎯 理论止盈止损价格范围:")
        print("-" * 80)
        
        for zone, direction, count, avg_price, min_price, max_price in zone_stats:
            if zone == '控股商控制区':
                print(f"{zone} ({direction}): 无止盈止损")
            elif zone == '高值盈利区' and direction == '买涨':
                avg_profit = avg_price * 1.02
                avg_loss = avg_price * 0.99
                print(f"{zone} ({direction}): 止盈{avg_profit:.2f}, 止损{avg_loss:.2f} (平均)")
            elif direction == '买跌':
                if zone == '强亏损区':
                    avg_profit = avg_price * 0.98  # -2%
                    avg_loss = avg_price * 1.01    # +1%
                    print(f"{zone} ({direction}): 止盈{avg_profit:.2f}, 止损{avg_loss:.2f} (平均)")
                elif zone == '其他区域':
                    avg_profit = avg_price * 0.99  # -1%
                    avg_loss = avg_price * 1.02    # +2%
                    print(f"{zone} ({direction}): 止盈{avg_profit:.2f}, 止损{avg_loss:.2f} (平均)")
        
        connection.close()
        print(f"\n🎉 止盈止损价格列表完成!")
        
    except Exception as e:
        print(f"❌ 列出止盈止损价格失败: {e}")

if __name__ == "__main__":
    list_profit_loss_prices()
