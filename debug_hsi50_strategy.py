#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
HSI50策略调试版本
================
分析为什么没有触发交易信号
"""

import mysql.connector
import pandas as pd
import numpy as np
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

def debug_hsi50_strategy():
    """调试HSI50策略"""
    
    # 数据库配置
    db_config = {
        'host': 'localhost',
        'database': 'finance',
        'user': 'root',
        'password': '12345678',
        'charset': 'utf8mb4'
    }
    
    try:
        print("🔍 HSI50策略调试分析")
        print("=" * 50)
        
        # 1. 加载数据
        connection = mysql.connector.connect(**db_config)
        
        query = """
            SELECT Date, Open, High, Low, Close, Volume,
                   ma_20, ma_60, y_probability, 
                   new_midprice, new_controller, new_Full_Y
            FROM hkhsi50 
            WHERE Date >= '2024-01-01'  -- 最近1年数据
            AND y_probability IS NOT NULL
            ORDER BY Date ASC
        """
        
        data = pd.read_sql(query, connection)
        connection.close()
        
        data['Date'] = pd.to_datetime(data['Date'])
        data.set_index('Date', inplace=True)
        
        print(f"📊 加载了 {len(data)} 条数据")
        
        # 2. 计算技术指标
        data['price_change'] = data['Close'].pct_change()
        data['price_vs_ma20'] = data['Close'] / data['ma_20']
        data['price_vs_ma60'] = data['Close'] / data['ma_60']
        data['price_deviation'] = (data['Close'] - data['new_midprice']) / data['new_midprice']
        
        # 3. 分析价格偏离情况
        print(f"\n📈 价格偏离分析:")
        print(f"   • 价格偏离范围: {data['price_deviation'].min():.4f} ~ {data['price_deviation'].max():.4f}")
        print(f"   • 平均偏离: {data['price_deviation'].mean():.4f}")
        print(f"   • 标准差: {data['price_deviation'].std():.4f}")
        
        # 检查极端偏离
        extreme_high = data['price_deviation'] > 0.05
        extreme_low = data['price_deviation'] < -0.05
        
        print(f"   • 极端偏高(>5%): {extreme_high.sum()} 天 ({extreme_high.mean()*100:.1f}%)")
        print(f"   • 极端偏低(<-5%): {extreme_low.sum()} 天 ({extreme_low.mean()*100:.1f}%)")
        
        # 4. 分析y_probability分布
        print(f"\n🎯 y_probability分析:")
        print(f"   • y_probability范围: {data['y_probability'].min():.4f} ~ {data['y_probability'].max():.4f}")
        print(f"   • 平均值: {data['y_probability'].mean():.4f}")
        print(f"   • 标准差: {data['y_probability'].std():.4f}")
        
        # y_probability分布
        high_y = data['y_probability'] > 0.52
        low_y = data['y_probability'] < 0.48
        
        print(f"   • 高y_probability(>0.52): {high_y.sum()} 天 ({high_y.mean()*100:.1f}%)")
        print(f"   • 低y_probability(<0.48): {low_y.sum()} 天 ({low_y.mean()*100:.1f}%)")
        
        # 5. 模拟信号生成
        print(f"\n🎲 信号生成分析:")
        
        signals = []
        for date, row in data.iterrows():
            deviation = row['price_deviation']
            y_prob = row['y_probability']
            
            # 计算期望值和胜率
            if abs(deviation) < 0.03:
                base_win_prob = 0.5
            elif deviation > 0.05:
                base_win_prob = 0.7 + min(0.2, abs(deviation) - 0.05) * 10
            elif deviation < -0.05:
                base_win_prob = 0.7 + min(0.2, abs(deviation) - 0.05) * 10
            else:
                base_win_prob = 0.5 + abs(deviation) * 5
            
            y_adjustment = (y_prob - 0.5) * 0.3
            win_probability = np.clip(base_win_prob + y_adjustment, 0.1, 0.95)
            
            if deviation > 0:
                expected_return = abs(deviation) * 2
            else:
                expected_return = abs(deviation) * 2
            
            expected_value = win_probability * expected_return - (1 - win_probability) * 0.06
            
            # 检查信号条件
            signal = 'HOLD'
            if (expected_value >= 0.05 and win_probability >= 0.6):
                if deviation > 0.05:
                    signal = 'SHORT'
                elif deviation < -0.05:
                    signal = 'LONG'
            
            signals.append({
                'date': date,
                'deviation': deviation,
                'y_probability': y_prob,
                'expected_value': expected_value,
                'win_probability': win_probability,
                'signal': signal
            })
        
        signals_df = pd.DataFrame(signals)
        
        # 统计信号
        total_signals = len(signals_df)
        long_signals = len(signals_df[signals_df['signal'] == 'LONG'])
        short_signals = len(signals_df[signals_df['signal'] == 'SHORT'])
        hold_signals = len(signals_df[signals_df['signal'] == 'HOLD'])
        
        print(f"   • 总信号数: {total_signals}")
        print(f"   • 买涨信号: {long_signals} ({long_signals/total_signals*100:.1f}%)")
        print(f"   • 买跌信号: {short_signals} ({short_signals/total_signals*100:.1f}%)")
        print(f"   • 持有信号: {hold_signals} ({hold_signals/total_signals*100:.1f}%)")
        
        # 6. 显示最有潜力的信号
        print(f"\n🎯 最有潜力的交易机会:")
        
        # 按期望值排序
        top_signals = signals_df.nlargest(10, 'expected_value')
        
        for _, signal in top_signals.iterrows():
            signal_emoji = "📈" if signal['signal'] == 'LONG' else "📉" if signal['signal'] == 'SHORT' else "⏸️"
            print(f"   {signal_emoji} {signal['date'].strftime('%Y-%m-%d')}: {signal['signal']}")
            print(f"      偏离: {signal['deviation']*100:+.2f}%, y_prob: {signal['y_probability']:.4f}")
            print(f"      期望值: {signal['expected_value']*100:.2f}%, 胜率: {signal['win_probability']*100:.1f}%")
        
        # 7. 分析为什么没有交易
        print(f"\n🔍 无交易原因分析:")
        
        # 检查期望值条件
        high_ev = signals_df['expected_value'] >= 0.05
        high_prob = signals_df['win_probability'] >= 0.6
        extreme_dev = (signals_df['deviation'].abs() >= 0.05)
        
        print(f"   • 期望值≥5%: {high_ev.sum()} 天 ({high_ev.mean()*100:.1f}%)")
        print(f"   • 胜率≥60%: {high_prob.sum()} 天 ({high_prob.mean()*100:.1f}%)")
        print(f"   • 极端偏离≥5%: {extreme_dev.sum()} 天 ({extreme_dev.mean()*100:.1f}%)")
        
        # 同时满足所有条件
        all_conditions = high_ev & high_prob & extreme_dev
        print(f"   • 同时满足所有条件: {all_conditions.sum()} 天 ({all_conditions.mean()*100:.1f}%)")
        
        if all_conditions.sum() > 0:
            print(f"\n✅ 找到 {all_conditions.sum()} 个潜在交易机会!")
            potential_trades = signals_df[all_conditions]
            for _, trade in potential_trades.iterrows():
                print(f"   📅 {trade['date'].strftime('%Y-%m-%d')}: {trade['signal']}")
                print(f"      期望值: {trade['expected_value']*100:.2f}%, 胜率: {trade['win_probability']*100:.1f}%")
        else:
            print(f"\n❌ 在当前参数下没有找到符合条件的交易机会")
            print(f"💡 建议调整策略参数:")
            print(f"   • 降低期望值阈值 (当前5%)")
            print(f"   • 降低胜率阈值 (当前60%)")
            print(f"   • 降低偏离阈值 (当前5%)")
        
        # 8. 推荐参数
        print(f"\n💡 推荐策略参数调整:")
        
        # 找到95%分位数的偏离
        deviation_95 = np.percentile(np.abs(signals_df['deviation']), 95)
        ev_75 = np.percentile(signals_df['expected_value'], 75)
        prob_75 = np.percentile(signals_df['win_probability'], 75)
        
        print(f"   • 建议偏离阈值: {deviation_95*100:.2f}% (95%分位数)")
        print(f"   • 建议期望值阈值: {ev_75*100:.2f}% (75%分位数)")
        print(f"   • 建议胜率阈值: {prob_75*100:.1f}% (75%分位数)")
        
        return True
        
    except Exception as e:
        print(f"❌ 调试失败: {e}")
        return False

def main():
    """主函数"""
    debug_hsi50_strategy()

if __name__ == "__main__":
    main()
