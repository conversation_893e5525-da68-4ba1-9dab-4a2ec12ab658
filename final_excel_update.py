#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd
import mysql.connector

def update_excel_final():
    """最终更新Excel表格，添加所有需要的字段"""
    
    excel_file = "交易记录追踪0023HK.xlsx"
    
    print("🔧 最终更新Excel表格...")
    
    # 读取Excel
    try:
        df = pd.read_excel(excel_file)
        print(f"📋 当前记录数: {len(df)}")
        print(f"📊 当前列数: {len(df.columns)}")
    except:
        print("❌ 无法读取Excel文件")
        return False
    
    # 获取数据库数据
    try:
        config = {
            'host': 'localhost',
            'port': 3306,
            'user': 'root',
            'password': '12345678',
            'database': 'finance'
        }
        connection = mysql.connector.connect(**config)
        cursor = connection.cursor()
        query = "SELECT Y_Value, X_Value, E_Value, Full_Y, MoneyFlowRatio FROM eab_0023hk ORDER BY Date DESC LIMIT 1"
        cursor.execute(query)
        result = cursor.fetchone()
        cursor.close()
        connection.close()
        
        if result:
            y_val, x_val, e_val, full_y, mfr = result
            print(f"📈 数据库数据获取成功")
        else:
            y_val, x_val, e_val, full_y, mfr = 0.2069, 0.3211, -0.0525, 0.2069, 0.3211
            print(f"⚠️ 使用默认数据")
    except Exception as e:
        print(f"⚠️ 数据库连接失败: {e}")
        y_val, x_val, e_val, full_y, mfr = 0.2069, 0.3211, -0.0525, 0.2069, 0.3211
    
    # 添加缺失的字段
    fields_added = []
    
    # 1. 添加止盈价
    if '止盈价' not in df.columns:
        price_idx = df.columns.get_loc('交易价格')
        df.insert(price_idx + 1, '止盈价', 0.00)
        fields_added.append('止盈价')
    
    # 2. 添加止损价
    if '止损价' not in df.columns:
        profit_idx = df.columns.get_loc('止盈价')
        df.insert(profit_idx + 1, '止损价', 0.00)
        fields_added.append('止损价')
    
    # 3. 添加Full_Y
    if 'Full_Y' not in df.columns:
        y_idx = df.columns.get_loc('Y值')
        df.insert(y_idx + 1, 'Full_Y', 0.0000)
        fields_added.append('Full_Y')
    
    # 4. 添加MoneyFlowRatio
    if 'MoneyFlowRatio' not in df.columns:
        full_y_idx = df.columns.get_loc('Full_Y')
        df.insert(full_y_idx + 1, 'MoneyFlowRatio', 0.0000)
        fields_added.append('MoneyFlowRatio')
    
    # 5. 添加MyE
    if 'MyE' not in df.columns:
        mfr_idx = df.columns.get_loc('MoneyFlowRatio')
        df.insert(mfr_idx + 1, 'MyE', 0.0000)
        fields_added.append('MyE')
    
    # 计算MyE值
    mye_value = 8 * mfr * full_y - 3 * mfr - 3 * full_y + 1
    
    # 更新最新记录
    latest_idx = -1
    current_price = 12.14
    total_capital = 2500.00
    max_shares = 200
    
    # 计算止盈止损价格 (空头)
    take_profit_price = current_price * 0.988  # -1.2%
    stop_loss_price = current_price * 1.006    # +0.6%
    
    # 计算交易数据
    trade_amount = current_price * max_shares
    commission = trade_amount * 0.001
    account_balance = total_capital - trade_amount - commission
    total_assets = account_balance + trade_amount
    
    # 更新所有字段
    df.iloc[latest_idx, df.columns.get_loc('交易价格')] = current_price
    df.iloc[latest_idx, df.columns.get_loc('止盈价')] = take_profit_price
    df.iloc[latest_idx, df.columns.get_loc('止损价')] = stop_loss_price
    df.iloc[latest_idx, df.columns.get_loc('持仓数量')] = max_shares
    df.iloc[latest_idx, df.columns.get_loc('交易金额')] = trade_amount
    df.iloc[latest_idx, df.columns.get_loc('手续费')] = commission
    df.iloc[latest_idx, df.columns.get_loc('账户余额')] = account_balance
    df.iloc[latest_idx, df.columns.get_loc('当前市值')] = trade_amount
    df.iloc[latest_idx, df.columns.get_loc('总资产')] = total_assets
    df.iloc[latest_idx, df.columns.get_loc('累计收益率')] = (total_assets - total_capital) / total_capital * 100
    df.iloc[latest_idx, df.columns.get_loc('Y值')] = y_val
    df.iloc[latest_idx, df.columns.get_loc('Full_Y')] = full_y
    df.iloc[latest_idx, df.columns.get_loc('X值')] = x_val
    df.iloc[latest_idx, df.columns.get_loc('MoneyFlowRatio')] = mfr
    df.iloc[latest_idx, df.columns.get_loc('E值')] = e_val
    df.iloc[latest_idx, df.columns.get_loc('MyE')] = mye_value
    df.iloc[latest_idx, df.columns.get_loc('备注')] = f'完整XYE系统 总资本{total_capital:,.0f}港元 持仓{max_shares}股 含所有技术指标'
    
    # 保存
    df.to_excel(excel_file, index=False)
    
    print("✅ Excel表格更新完成")
    print(f"📊 新增字段: {', '.join(fields_added) if fields_added else '无新增字段'}")
    print(f"📋 总列数: {len(df.columns)}")
    
    # 显示结果
    print(f"\n📈 技术指标:")
    print(f"   Y值: {y_val:.4f}")
    print(f"   Full_Y: {full_y:.4f}")
    print(f"   X值: {x_val:.4f}")
    print(f"   MoneyFlowRatio: {mfr:.4f}")
    print(f"   E值: {e_val:.4f}")
    print(f"   MyE: {mye_value:.4f}")
    
    print(f"\n🎯 止盈止损:")
    print(f"   交易价格: {current_price:.2f} 港元")
    print(f"   止盈价: {take_profit_price:.2f} 港元")
    print(f"   止损价: {stop_loss_price:.2f} 港元")
    
    print(f"\n💰 资金状况:")
    print(f"   总资本: {total_capital:,.2f} 港元")
    print(f"   持仓: {max_shares} 股")
    print(f"   总资产: {total_assets:,.2f} 港元")
    
    return True

if __name__ == "__main__":
    update_excel_final()
