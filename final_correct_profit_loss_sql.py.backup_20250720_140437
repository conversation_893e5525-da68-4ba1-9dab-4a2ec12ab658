#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终正确计算test表的profit和loss价格
=================================

正确理解：
- profit价格 = 止盈价格 (盈利目标价)
- loss价格 = 止损价格 (亏损限制价)

买涨策略：
- profit价格 = close × (1 + 止盈%) (价格上涨时止盈)
- loss价格 = close × (1 - 止损%) (价格下跌时止损)

买跌策略：
- profit价格 = close × (1 - 止盈%) (价格下跌时止盈)
- loss价格 = close × (1 + 止损%) (价格上涨时止损)

作者: Cosmoon NG
日期: 2025年7月
"""

import pymysql

def final_correct_profit_loss_sql():
    """最终正确计算profit和loss价格"""
    db_config = {
        'host': '************',
        'port': 3306,
        'database': 'finance',
        'user': 'root',
        'password': '',
        'charset': 'utf8mb4'
    }
    
    try:
        connection = pymysql.connect(**db_config)
        cursor = connection.cursor()
        
        print("📊 最终正确计算test表的profit和loss价格")
        print("="*60)
        print("🔍 正确理解:")
        print("   • profit价格 = 止盈价格 (盈利目标价)")
        print("   • loss价格 = 止损价格 (亏损限制价)")
        print("="*60)
        
        # 1. 正确计算高值盈利区买涨的profit和loss价格
        print("\n1️⃣ 正确计算高值盈利区买涨的profit和loss价格...")
        print("   买涨: profit价格=止盈价(close×1.02), loss价格=止损价(close×0.99)")
        cursor.execute("""
            UPDATE test 
            SET `profit价格` = close * 1.02,
                `loss价格` = close * 0.99
            WHERE `控制系数` > 0.43 AND `资金流比例` > 0.43 AND 交易方向 = '买涨'
        """)
        high_profit_rows = cursor.rowcount
        print(f"✅ 更新了 {high_profit_rows} 条高值盈利区买涨记录")
        
        # 2. 正确计算强亏损区买跌的profit和loss价格
        print("\n2️⃣ 正确计算强亏损区买跌的profit和loss价格...")
        print("   买跌: profit价格=止盈价(close×0.98), loss价格=止损价(close×1.01)")
        cursor.execute("""
            UPDATE test 
            SET `profit价格` = close * 0.98,
                `loss价格` = close * 1.01
            WHERE (`控制系数` < 0.25 OR `资金流比例` < 0.25) 
              AND NOT (`控制系数` > 0.333 AND `控制系数` < 0.4)
              AND 交易方向 = '买跌'
        """)
        strong_loss_rows = cursor.rowcount
        print(f"✅ 更新了 {strong_loss_rows} 条强亏损区买跌记录")
        
        # 3. 正确计算其他区域买跌的profit和loss价格
        print("\n3️⃣ 正确计算其他区域买跌的profit和loss价格...")
        print("   买跌: profit价格=止盈价(close×0.99), loss价格=止损价(close×1.02)")
        cursor.execute("""
            UPDATE test 
            SET `profit价格` = close * 0.99,
                `loss价格` = close * 1.02
            WHERE NOT (`控制系数` > 0.43 AND `资金流比例` > 0.43) 
              AND NOT (`控制系数` > 0.333 AND `控制系数` < 0.4) 
              AND NOT (`控制系数` < 0.25 OR `资金流比例` < 0.25)
              AND 交易方向 = '买跌'
        """)
        other_rows = cursor.rowcount
        print(f"✅ 更新了 {other_rows} 条其他区域买跌记录")
        
        # 4. 控股商控制区观望保持不变
        print("\n4️⃣ 控股商控制区观望保持profit和loss价格为0...")
        cursor.execute("""
            SELECT COUNT(*) FROM test 
            WHERE `控制系数` > 0.333 AND `控制系数` < 0.4 AND 交易方向 = '观望'
        """)
        control_count = cursor.fetchone()[0]
        print(f"ℹ️ 控股商控制区观望记录: {control_count} 条 (profit和loss价格保持为0)")
        
        # 提交事务
        connection.commit()
        
        # 5. 验证前10条记录
        print("\n5️⃣ 验证前10条记录的profit和loss价格...")
        cursor.execute("""
            SELECT 交易序号, close, 平仓价格, 交易方向, `profit价格`, `loss价格`,
                   CASE 
                       WHEN `控制系数` > 0.43 AND `资金流比例` > 0.43 THEN '高值盈利区'
                       WHEN `控制系数` > 0.333 AND `控制系数` < 0.4 THEN '控股商控制区'
                       WHEN (`控制系数` < 0.25 OR `资金流比例` < 0.25) AND NOT (`控制系数` > 0.333 AND `控制系数` < 0.4) THEN '强亏损区'
                       ELSE '其他区域'
                   END AS 策略区域
            FROM test 
            ORDER BY 交易序号 
            LIMIT 10
        """)
        
        results = cursor.fetchall()
        
        print(f"\n📊 前10条记录的profit和loss价格验证:")
        print("-" * 100)
        print(f"{'序号':<4} {'策略区域':<12} {'方向':<6} {'开仓价':<8} {'平仓价':<8} {'profit价格':<10} {'loss价格':<9}")
        print("-" * 100)
        
        for record in results:
            trade_id, open_price, close_price, direction, profit_price, loss_price, zone = record
            print(f"{trade_id:<4} {zone:<12} {direction:<6} {float(open_price):<8.2f} {float(close_price):<8.2f} "
                  f"{float(profit_price):<10.2f} {float(loss_price):<9.2f}")
        
        # 6. 详细验证您提到的例子
        print("\n6️⃣ 详细验证您提到的例子...")
        
        # 验证交易1 (买跌)
        cursor.execute("SELECT close, `profit价格`, `loss价格` FROM test WHERE 交易序号 = 1")
        trade1 = cursor.fetchone()
        open1, profit1, loss1 = trade1
        
        print(f"📋 交易1 (强亏损区买跌):")
        print(f"   • 开仓价: {float(open1):.2f}港币")
        print(f"   • profit价格 = {float(open1):.2f} × (1-2%) = {float(open1) * 0.98:.2f}港币 (实际: {float(profit1):.2f})")
        print(f"   • loss价格 = {float(open1):.2f} × (1+1%) = {float(open1) * 1.01:.2f}港币 (实际: {float(loss1):.2f})")
        
        # 验证交易2 (买涨)
        cursor.execute("SELECT close, `profit价格`, `loss价格` FROM test WHERE 交易序号 = 2")
        trade2 = cursor.fetchone()
        open2, profit2, loss2 = trade2
        
        print(f"\n📋 交易2 (高值盈利区买涨):")
        print(f"   • 开仓价: {float(open2):.2f}港币")
        print(f"   • profit价格 = {float(open2):.2f} × (1+2%) = {float(open2) * 1.02:.2f}港币 (实际: {float(profit2):.2f})")
        print(f"   • loss价格 = {float(open2):.2f} × (1-1%) = {float(open2) * 0.99:.2f}港币 (实际: {float(loss2):.2f})")
        
        # 验证您的具体例子
        print(f"\n✅ 验证您的例子:")
        print(f"   交易2买涨: profit = 22.86 × (1+2%) = {22.86 * 1.02:.2f}港币 ✅")
        print(f"   交易2买涨: loss = 22.86 × (1-1%) = {22.86 * 0.99:.2f}港币 ✅")
        
        # 7. 统计各策略区域
        print("\n7️⃣ 统计各策略区域的profit和loss价格...")
        cursor.execute("""
            SELECT 
                CASE 
                    WHEN `控制系数` > 0.43 AND `资金流比例` > 0.43 THEN '高值盈利区'
                    WHEN `控制系数` > 0.333 AND `控制系数` < 0.4 THEN '控股商控制区'
                    WHEN (`控制系数` < 0.25 OR `资金流比例` < 0.25) AND NOT (`控制系数` > 0.333 AND `控制系数` < 0.4) THEN '强亏损区'
                    ELSE '其他区域'
                END AS 策略区域,
                交易方向,
                COUNT(*) as 总次数,
                AVG(`profit价格`) as 平均profit价格,
                AVG(`loss价格`) as 平均loss价格
            FROM test 
            GROUP BY 策略区域, 交易方向
            ORDER BY 策略区域, 交易方向
        """)
        
        summary_results = cursor.fetchall()
        
        print(f"\n📈 各策略区域profit和loss价格统计:")
        print("-" * 80)
        print(f"{'策略区域':<12} {'方向':<6} {'次数':<6} {'平均profit价格':<12} {'平均loss价格':<12}")
        print("-" * 80)
        
        for record in summary_results:
            zone, direction, count, avg_profit, avg_loss = record
            print(f"{zone:<12} {direction:<6} {count:<6} {float(avg_profit):<12.2f} {float(avg_loss):<12.2f}")
        
        connection.close()
        print(f"\n🎉 profit和loss价格最终正确计算完成!")
        print(f"📊 更新记录: {high_profit_rows + strong_loss_rows + other_rows} 条")
        print("💡 现在profit价格=止盈价格, loss价格=止损价格 (完全正确)")
        
    except Exception as e:
        print(f"❌ 计算profit和loss价格失败: {e}")

if __name__ == "__main__":
    final_correct_profit_loss_sql()
