#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
验证两套XYE系统的数据
"""

import mysql.connector
from datetime import datetime

def verify_two_xye_systems():
    """验证两套XYE系统"""
    print("验证两套XYE系统")
    print("=" * 60)
    
    try:
        # 数据库连接
        config = {
            'host': 'localhost',
            'port': 3306,
            'user': 'root',
            'password': '12345678',
            'database': 'finance',
            'charset': 'utf8mb4'
        }
        
        connection = mysql.connector.connect(**config)
        cursor = connection.cursor()
        
        # 获取最新5条记录
        cursor.execute("""
            SELECT Date, Close, MFI, Y_Value, X_Value, E_Value, Full_Y, E, Controller, midprice
            FROM eab_0023hk 
            ORDER BY Date DESC 
            LIMIT 5
        """)
        
        records = cursor.fetchall()
        
        print("最新5条记录对比:")
        print("=" * 120)
        print("日期        收盘价    MFI     第一套XYE系统                    第二套XYE系统                Controller  回归线")
        print("                            X_Value  Y_Value  E_Value        X(MFI/100) Y(Full_Y)    E")
        print("-" * 120)
        
        for record in records:
            date, close, mfi, y_value, x_value, e_value, full_y, e, controller, midprice = record
            
            # 第一套系统
            x1 = x_value if x_value is not None else 0
            y1 = y_value if y_value is not None else 0
            e1 = e_value if e_value is not None else 0
            
            # 第二套系统
            x2 = mfi / 100 if mfi is not None else 0
            y2 = full_y if full_y is not None else 0
            e2 = e if e is not None else 0
            
            controller_str = f"{controller:3d}" if controller is not None else "NULL"
            midprice_str = f"{midprice:8.2f}" if midprice is not None else "    NULL"
            
            print(f"{date}  {close:7.2f}   {mfi:6.2f}   "
                  f"{x1:7.4f}  {y1:7.4f}  {e1:8.4f}     "
                  f"{x2:7.4f}    {y2:7.4f}   {e2:8.4f}   "
                  f"{controller_str}     {midprice_str}")
        
        # 分析最新记录
        if records:
            latest = records[0]
            date, close, mfi, y_value, x_value, e_value, full_y, e, controller, midprice = latest
            
            print(f"\n最新记录详细分析 ({date}):")
            print("=" * 60)
            
            print(f"基础数据:")
            print(f"  收盘价: {close:.2f} 港元")
            print(f"  MFI: {mfi:.2f}")
            print(f"  回归线价格: {midprice:.2f if midprice else 'NULL'}")
            
            print(f"\n第一套XYE系统 (基于价格位置):")
            print(f"  X_Value: {x_value:.6f} (= MFI/100 = {mfi:.2f}/100)")
            print(f"  Y_Value: {y_value:.6f} (价格在20日区间的位置)")
            print(f"  E_Value: {e_value:.6f} (= (8×X-3)×Y - 3×X + 1)")
            
            # 验证第一套E_Value计算
            if x_value and y_value:
                calculated_e1 = (8 * x_value - 3) * y_value - 3 * x_value + 1
                print(f"  验证E_Value: {calculated_e1:.6f} (计算值)")
                diff1 = abs(calculated_e1 - e_value) if e_value else 0
                print(f"  差异: {diff1:.6f} {'✅' if diff1 < 0.001 else '❌'}")
            
            print(f"\n第二套XYE系统 (基于回归线K值):")
            print(f"  X (MFI/100): {mfi/100:.6f}")
            print(f"  Y (Full_Y): {full_y:.6f} (通过sp_averagelineV3计算的K值)")
            print(f"  E: {e:.6f} (基于Full_Y的E值)")
            
            # 验证第二套E计算
            if mfi and full_y:
                x2 = mfi / 100
                calculated_e2 = (8 * x2 - 3) * full_y - 3 * x2 + 1
                print(f"  验证E: {calculated_e2:.6f} (如果使用相同公式)")
                diff2 = abs(calculated_e2 - e) if e else 0
                print(f"  差异: {diff2:.6f} {'✅' if diff2 < 0.001 else '❌'}")
            
            print(f"\nController分析:")
            print(f"  Controller: {controller}")
            if controller == 1:
                print(f"  含义: 买入信号")
            elif controller == -1:
                print(f"  含义: 卖出信号")
            else:
                print(f"  含义: 中性/观望")
            
            # 价格与回归线偏离分析
            if midprice and close:
                deviation = (close - midprice) / midprice * 100
                print(f"\n价格偏离分析:")
                print(f"  当前价格: {close:.2f}")
                print(f"  回归线价格: {midprice:.2f}")
                print(f"  偏离度: {deviation:+.2f}%")
                
                if deviation > 2:
                    print(f"  状态: 价格高于回归线，可能超买")
                elif deviation < -2:
                    print(f"  状态: 价格低于回归线，可能超卖")
                else:
                    print(f"  状态: 价格接近回归线，相对平衡")
        
        cursor.close()
        connection.close()
        
        print(f"\n验证完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        return True
        
    except Exception as e:
        print(f"验证失败: {e}")
        return False

def main():
    """主函数"""
    success = verify_two_xye_systems()
    return 0 if success else 1

if __name__ == "__main__":
    import sys
    sys.exit(main())
