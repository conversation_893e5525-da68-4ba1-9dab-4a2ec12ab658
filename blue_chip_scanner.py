#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
香港蓝筹股票扫描器
================
查找价格最低、风险相对较小的蓝筹股票
"""

import yfinance as yf
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

def scan_hk_blue_chips():
    """扫描香港主要蓝筹股票"""
    print("🔍 扫描香港蓝筹股票...")

    # 香港主要蓝筹股票列表 (包含每手股数)
    blue_chips = {
        '0700.HK': {'name': '腾讯控股', 'lot_size': 100},
        '0005.HK': {'name': '汇丰控股', 'lot_size': 400},
        '0941.HK': {'name': '中国移动', 'lot_size': 500},
        '0939.HK': {'name': '建设银行', 'lot_size': 1000},
        '3988.HK': {'name': '中国银行', 'lot_size': 1000},
        '0388.HK': {'name': '港交所', 'lot_size': 100},
        '0883.HK': {'name': '中国海洋石油', 'lot_size': 500},
        '0001.HK': {'name': '长和', 'lot_size': 500},
        '0002.HK': {'name': '中电控股', 'lot_size': 500},
        '0003.HK': {'name': '香港中华煤气', 'lot_size': 1000},
        '0011.HK': {'name': '恒生银行', 'lot_size': 100},
        '0016.HK': {'name': '新鸿基地产', 'lot_size': 100},
        '0017.HK': {'name': '新世界发展', 'lot_size': 1000},
        '0023.HK': {'name': '东亚银行', 'lot_size': 1000},
        '1299.HK': {'name': '友邦保险', 'lot_size': 200},
        '2318.HK': {'name': '中国平安', 'lot_size': 100},
        '1398.HK': {'name': '工商银行', 'lot_size': 1000},
        '2628.HK': {'name': '中国人寿', 'lot_size': 500},
        '0066.HK': {'name': '港铁公司', 'lot_size': 100},
        '0101.HK': {'name': '恒隆地产', 'lot_size': 500}
    }

    results = []

    for symbol, stock_info in blue_chips.items():
        try:
            name = stock_info['name']
            lot_size = stock_info['lot_size']
            print(f"📊 查询 {symbol} {name} (每手{lot_size}股)...")

            # 获取基本信息
            ticker = yf.Ticker(symbol)
            info = ticker.info

            # 获取历史数据计算风险指标
            hist = ticker.history(period="1y")
            if hist.empty:
                continue

            current_price = hist['Close'].iloc[-1]
            one_lot_cost = current_price * lot_size  # 一手成本

            # 计算风险指标
            returns = hist['Close'].pct_change().dropna()
            volatility = returns.std() * np.sqrt(252)  # 年化波动率
            max_drawdown = ((hist['Close'] / hist['Close'].cummax()) - 1).min()

            # 获取基本面数据
            pe_ratio = info.get('trailingPE', 0)
            pb_ratio = info.get('priceToBook', 0)
            dividend_yield = info.get('dividendYield', 0) * 100 if info.get('dividendYield') else 0
            market_cap = info.get('marketCap', 0) / 1e9  # 转换为十亿

            results.append({
                '代码': symbol,
                '名称': name,
                '每手股数': lot_size,
                '当前价格': round(current_price, 2),
                '一手成本(HKD)': round(one_lot_cost, 2),
                '市值(十亿)': round(market_cap, 1),
                'PE比率': round(pe_ratio, 2) if pe_ratio else 'N/A',
                'PB比率': round(pb_ratio, 2) if pb_ratio else 'N/A',
                '股息率%': round(dividend_yield, 2),
                '年化波动率%': round(volatility * 100, 2),
                '最大回撤%': round(max_drawdown * 100, 2),
                '风险评分': calculate_risk_score(volatility, max_drawdown, pe_ratio, pb_ratio)
            })

        except Exception as e:
            print(f"❌ {symbol} 查询失败: {e}")
            continue

    # 转换为DataFrame并排序
    df = pd.DataFrame(results)

    if df.empty:
        print("❌ 未获取到任何数据")
        return

    # 按一手成本排序
    df_by_lot_cost = df.sort_values('一手成本(HKD)').head(10)

    # 按单股价格排序
    df_by_price = df.sort_values('当前价格').head(10)

    # 按风险评分排序（分数越低风险越小）
    df_by_risk = df.sort_values('风险评分').head(10)

    print("\n" + "="*100)
    print("💰 一手成本最低的10只蓝筹股 (最便宜的一手股):")
    print("="*100)
    print(df_by_lot_cost[['代码', '名称', '每手股数', '当前价格', '一手成本(HKD)', 'PE比率', 'PB比率', '股息率%']].to_string(index=False))

    print("\n" + "="*100)
    print("🏆 单股价格最低的10只蓝筹股:")
    print("="*100)
    print(df_by_price[['代码', '名称', '每手股数', '当前价格', '一手成本(HKD)', 'PE比率', 'PB比率', '股息率%']].to_string(index=False))

    print("\n" + "="*100)
    print("🛡️ 风险最低的10只蓝筹股:")
    print("="*100)
    print(df_by_risk[['代码', '名称', '当前价格', '一手成本(HKD)', '年化波动率%', '最大回撤%', '风险评分']].to_string(index=False))

    # 找出既便宜又安全的股票 (基于一手成本)
    df['综合评分'] = df['一手成本(HKD)'].rank() + df['风险评分'].rank()
    df_best = df.sort_values('综合评分').head(5)

    print("\n" + "="*100)
    print("💎 综合推荐 (一手成本低+风险小):")
    print("="*100)
    print(df_best[['代码', '名称', '每手股数', '当前价格', '一手成本(HKD)', 'PE比率', 'PB比率', '股息率%', '风险评分']].to_string(index=False))

    # 显示最便宜的一手股
    cheapest = df_by_lot_cost.iloc[0]
    print(f"\n🎯 最便宜的一手蓝筹股: {cheapest['代码']} {cheapest['名称']}")
    print(f"   一手成本: HKD {cheapest['一手成本(HKD)']} ({cheapest['每手股数']}股 × HKD {cheapest['当前价格']})")
    print(f"   PE比率: {cheapest['PE比率']}, PB比率: {cheapest['PB比率']}, 股息率: {cheapest['股息率%']}%")

    return df

def calculate_risk_score(volatility, max_drawdown, pe_ratio, pb_ratio):
    """计算风险评分 (分数越低风险越小)"""
    score = 0

    # 波动率评分 (0-30分)
    if volatility > 0.4:
        score += 30
    elif volatility > 0.3:
        score += 20
    elif volatility > 0.2:
        score += 10

    # 最大回撤评分 (0-30分)
    if abs(max_drawdown) > 0.5:
        score += 30
    elif abs(max_drawdown) > 0.3:
        score += 20
    elif abs(max_drawdown) > 0.2:
        score += 10

    # PE比率评分 (0-20分)
    if pe_ratio and pe_ratio > 30:
        score += 20
    elif pe_ratio and pe_ratio > 20:
        score += 10

    # PB比率评分 (0-20分)
    if pb_ratio and pb_ratio > 3:
        score += 20
    elif pb_ratio and pb_ratio > 2:
        score += 10

    return score

if __name__ == "__main__":
    scan_hk_blue_chips()