#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
EAB_0023HK 优化回测系统
初始资本: 2500港币
优化策略参数以提高收益率
"""

import pandas as pd
import numpy as np
import mysql.connector
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

class EABOptimizedBacktest:
    def __init__(self):
        """初始化优化回测系统"""
        self.initial_capital = 2500
        self.transaction_cost = 0.001    # 0.1%
        self.position_ratio = 0.9        # 提高到90%仓位
        self.max_holding_days = 5        # 延长到5天
        
        # 优化的止盈止损
        self.take_profit = 0.05          # 5%止盈
        self.stop_loss = 0.03            # 3%止损
        
        # 当前状态
        self.position = 0
        self.position_size = 0
        self.entry_price = 0
        self.entry_date = None
        self.current_capital = self.initial_capital
        
        # 记录
        self.trades = []
        self.daily_equity = []
        
    def load_data(self):
        """加载数据"""
        print("📊 加载EAB_0023HK数据...")
        
        try:
            config = {
                'host': 'localhost',
                'port': 3306,
                'user': 'root',
                'password': '12345678',
                'database': 'finance',
                'charset': 'utf8mb4'
            }
            
            conn = mysql.connector.connect(**config)
            cursor = conn.cursor()
            
            query = """
            SELECT 
                Date, Close, High, Low,
                Y_Value, X_Value, E_Value, 
                Full_Y, E as E2, Controller,
                RSI, MFI, midprice
            FROM eab_0023hk 
            WHERE Date IS NOT NULL 
            AND Close IS NOT NULL
            ORDER BY Date ASC
            """
            
            cursor.execute(query)
            results = cursor.fetchall()
            
            columns = ['date', 'close', 'high', 'low',
                      'y_value', 'x_value', 'e_value', 
                      'full_y', 'e2', 'controller',
                      'rsi', 'mfi', 'midprice']
            
            self.df = pd.DataFrame(results, columns=columns)
            self.df['date'] = pd.to_datetime(self.df['date'])
            
            # 转换数值列
            numeric_cols = ['close', 'high', 'low', 'y_value', 'x_value', 
                           'e_value', 'full_y', 'e2', 'controller', 'rsi', 'mfi', 'midprice']
            
            for col in numeric_cols:
                if col in self.df.columns:
                    self.df[col] = pd.to_numeric(self.df[col], errors='coerce')
            
            cursor.close()
            conn.close()
            
            print(f"✅ 加载 {len(self.df)} 条数据")
            print(f"📅 {self.df['date'].min().date()} 到 {self.df['date'].max().date()}")
            
            return True
            
        except Exception as e:
            print(f"❌ 数据加载失败: {e}")
            return False
    
    def calculate_position_size(self, capital, price):
        """优化的仓位计算"""
        available = capital * self.position_ratio
        size = int(available / price)
        return max(size, 50)  # 最小50股
    
    def generate_optimized_signal(self, row):
        """优化的信号生成"""
        # 获取技术指标
        y1, x1, e1 = row['y_value'], row['x_value'], row['e_value']
        y2, e2, controller = row['full_y'], row['e2'], row['controller']
        rsi, mfi = row['rsi'], row['mfi']
        
        # 价格偏离
        deviation = 0
        if pd.notna(row['midprice']) and row['midprice'] > 0:
            deviation = (row['close'] - row['midprice']) / row['midprice']
        
        # 多因子信号
        buy_signals = 0
        sell_signals = 0
        
        # 系统1: XYE信号
        if pd.notna(e1) and pd.notna(y1):
            if e1 > 0 and y1 > 0.5:
                buy_signals += 1
            elif e1 < 0 and y1 < 0.5:
                sell_signals += 1
        
        # 系统2: Controller信号
        if pd.notna(controller):
            if controller == 1:
                buy_signals += 1
            elif controller == 0 and deviation > 0.02:
                sell_signals += 1
        
        # RSI信号
        if pd.notna(rsi):
            if rsi < 30:  # 超卖
                buy_signals += 1
            elif rsi > 70:  # 超买
                sell_signals += 1
        
        # MFI信号
        if pd.notna(mfi):
            if mfi < 20:  # 资金流超卖
                buy_signals += 1
            elif mfi > 80:  # 资金流超买
                sell_signals += 1
        
        # 价格偏离信号
        if deviation < -0.05:  # 价格低于回归线5%
            buy_signals += 1
        elif deviation > 0.05:  # 价格高于回归线5%
            sell_signals += 1
        
        # 综合判断
        if buy_signals >= 3:
            return {"signal": "买入", "strength": buy_signals}
        elif sell_signals >= 3:
            return {"signal": "卖出", "strength": sell_signals}
        elif buy_signals >= 2:
            return {"signal": "买入", "strength": buy_signals}
        elif sell_signals >= 2:
            return {"signal": "卖出", "strength": sell_signals}
        else:
            return {"signal": "观望", "strength": 0}
    
    def check_exit_conditions(self, row, signal):
        """优化的平仓条件"""
        if self.position == 0:
            return False, ""
        
        # 时间止损
        if self.entry_date:
            days = (row['date'] - self.entry_date).days
            if days >= self.max_holding_days:
                return True, f"时间止损({days}天)"
        
        # 盈亏止损
        pnl_ratio = 0
        if self.position == 1:
            pnl_ratio = (row['close'] - self.entry_price) / self.entry_price
        else:
            pnl_ratio = (self.entry_price - row['close']) / self.entry_price
        
        if pnl_ratio >= self.take_profit:
            return True, f"止盈({pnl_ratio*100:.1f}%)"
        elif pnl_ratio <= -self.stop_loss:
            return True, f"止损({pnl_ratio*100:.1f}%)"
        
        # 信号反转
        if signal['strength'] >= 3:
            if (self.position == 1 and signal['signal'] == "卖出") or \
               (self.position == -1 and signal['signal'] == "买入"):
                return True, "信号反转"
        
        return False, ""
    
    def execute_trade(self, row, action, reason=""):
        """执行交易"""
        price = row['close']
        
        if action == "开多":
            self.position_size = self.calculate_position_size(self.current_capital, price)
            cost = self.position_size * price * (1 + self.transaction_cost)
            
            if cost <= self.current_capital:  # 资金充足
                self.current_capital -= cost
                self.position = 1
                self.entry_price = price
                self.entry_date = row['date']
                
                self.trades.append({
                    'date': row['date'],
                    'action': '开多',
                    'price': price,
                    'size': self.position_size,
                    'reason': reason
                })
            
        elif action == "开空":
            self.position_size = self.calculate_position_size(self.current_capital, price)
            revenue = self.position_size * price * (1 - self.transaction_cost)
            
            self.current_capital += revenue
            self.position = -1
            self.entry_price = price
            self.entry_date = row['date']
            
            self.trades.append({
                'date': row['date'],
                'action': '开空',
                'price': price,
                'size': self.position_size,
                'reason': reason
            })
            
        elif action == "平仓":
            if self.position == 1:  # 平多
                revenue = self.position_size * price * (1 - self.transaction_cost)
                self.current_capital += revenue
                pnl = (price - self.entry_price) * self.position_size
            else:  # 平空
                cost = self.position_size * price * (1 + self.transaction_cost)
                self.current_capital -= cost
                pnl = (self.entry_price - price) * self.position_size
            
            # 扣除双向手续费
            net_pnl = pnl - self.position_size * self.entry_price * self.transaction_cost * 2
            
            self.trades.append({
                'date': row['date'],
                'action': f'平{"多" if self.position == 1 else "空"}',
                'price': price,
                'pnl': net_pnl,
                'reason': reason
            })
            
            self.position = 0
            self.position_size = 0
            self.entry_price = 0
            self.entry_date = None
    
    def run_optimized_backtest(self):
        """运行优化回测"""
        print("\n🚀 开始优化回测...")
        print("📋 优化参数:")
        print(f"   仓位比例: {self.position_ratio*100:.0f}%")
        print(f"   最大持仓: {self.max_holding_days} 天")
        print(f"   止盈: {self.take_profit*100:.0f}%")
        print(f"   止损: {self.stop_loss*100:.0f}%")
        print()
        
        for i, row in self.df.iterrows():
            # 计算总资产
            total_assets = self.current_capital
            if self.position != 0:
                position_value = self.position_size * row['close']
                if self.position == 1:
                    total_assets += position_value
                else:
                    total_assets += (self.position_size * self.entry_price - position_value)
            
            self.daily_equity.append({
                'date': row['date'],
                'total_assets': total_assets,
                'position': self.position
            })
            
            # 生成信号
            signal = self.generate_optimized_signal(row)
            
            # 检查平仓
            should_exit, reason = self.check_exit_conditions(row, signal)
            if should_exit:
                self.execute_trade(row, "平仓", reason)
            
            # 检查开仓
            if self.position == 0 and signal['strength'] >= 2:
                if signal['signal'] == "买入":
                    self.execute_trade(row, "开多", f"多因子买入(强度{signal['strength']})")
                elif signal['signal'] == "卖出":
                    self.execute_trade(row, "开空", f"多因子卖出(强度{signal['strength']})")
        
        # 强制平仓
        if self.position != 0:
            self.execute_trade(self.df.iloc[-1], "平仓", "回测结束")
        
        print(f"✅ 优化回测完成")
        
    def analyze_optimized_results(self):
        """分析优化结果"""
        print("\n📊 优化回测结果")
        print("=" * 50)
        
        if not self.trades:
            print("无交易记录")
            return
        
        # 统计
        entries = [t for t in self.trades if '开' in t['action']]
        exits = [t for t in self.trades if '平' in t['action']]
        profitable = len([t for t in exits if t.get('pnl', 0) > 0])
        
        print(f"📈 交易统计:")
        print(f"   交易次数: {len(entries)}")
        print(f"   盈利次数: {profitable}")
        print(f"   胜率: {profitable/len(exits)*100:.1f}%" if exits else "0%")
        
        # 收益
        total_return = (self.current_capital / self.initial_capital - 1) * 100
        days = (self.df['date'].max() - self.df['date'].min()).days
        annual_return = (self.current_capital / self.initial_capital) ** (365/days) - 1
        
        print(f"\n💰 收益统计:")
        print(f"   初始: {self.initial_capital:,.0f}")
        print(f"   最终: {self.current_capital:,.0f}")
        print(f"   总收益率: {total_return:+.2f}%")
        print(f"   年化收益率: {annual_return*100:+.2f}%")
        
        # 对比
        buy_hold = (self.df['close'].iloc[-1] / self.df['close'].iloc[0] - 1) * 100
        print(f"   买入持有: {buy_hold:+.2f}%")
        print(f"   超额收益: {total_return - buy_hold:+.2f}%")
        
        # 风险指标
        equity_df = pd.DataFrame(self.daily_equity)
        equity_df['peak'] = equity_df['total_assets'].cummax()
        equity_df['drawdown'] = (equity_df['total_assets'] - equity_df['peak']) / equity_df['peak']
        max_drawdown = equity_df['drawdown'].min() * 100
        
        print(f"\n📉 风险指标:")
        print(f"   最大回撤: {max_drawdown:.2f}%")
        
        # 保存结果
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        trades_df = pd.DataFrame(self.trades)
        trades_df.to_excel(f"EAB_优化回测_{timestamp}.xlsx", index=False)
        
        print(f"\n✅ 结果已保存: EAB_优化回测_{timestamp}.xlsx")

def main():
    """主函数"""
    print("🏦 EAB_0023HK 优化回测系统")
    print("=" * 50)
    print("🔧 优化策略:")
    print("   • 多因子信号系统")
    print("   • 提高仓位比例到90%")
    print("   • 延长持仓时间到5天")
    print("   • 优化止盈止损比例")
    print("   • 降低开仓门槛")
    
    try:
        backtest = EABOptimizedBacktest()
        
        if not backtest.load_data():
            return
        
        backtest.run_optimized_backtest()
        backtest.analyze_optimized_results()
        
    except Exception as e:
        print(f"❌ 回测失败: {e}")
        import traceback
        traceback.print_exc()
    else:
        print("\n🎉 优化回测完成！")

if __name__ == "__main__":
    main()
