#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
增强版持仓追踪器
为Excel交易记录添加详细的持仓状态栏目
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import os
import warnings
warnings.filterwarnings('ignore')

class EnhancedPositionTracker:
    """增强版持仓追踪器"""
    
    def __init__(self, excel_file="交易记录追踪0023HK.xlsx"):
        self.excel_file = excel_file
        self.backup_file = f"交易记录追踪0023HK_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
        
    def backup_original_file(self):
        """备份原始文件"""
        try:
            if os.path.exists(self.excel_file):
                df = pd.read_excel(self.excel_file)
                df.to_excel(self.backup_file, index=False)
                print(f"✅ 原始文件已备份: {self.backup_file}")
                return True
        except Exception as e:
            print(f"❌ 备份失败: {e}")
            return False
    
    def load_existing_data(self):
        """加载现有数据"""
        try:
            if os.path.exists(self.excel_file):
                df = pd.read_excel(self.excel_file)
                print(f"✅ 加载现有数据: {len(df)} 条记录")
                return df
            else:
                print(f"❌ 文件不存在: {self.excel_file}")
                return None
        except Exception as e:
            print(f"❌ 加载数据失败: {e}")
            return None
    
    def calculate_enhanced_position_fields(self, df):
        """计算增强的持仓字段"""
        if df is None or len(df) == 0:
            return df
        
        # 确保日期列是datetime类型
        df['交易日期'] = pd.to_datetime(df['交易日期'])
        
        # 初始化新字段
        enhanced_fields = {
            '持仓状态': [],
            '持仓方向': [],
            '持仓天数': [],
            '持仓比例%': [],
            '盈亏比例%': [],
            '止盈距离%': [],
            '止损距离%': [],
            '风险收益比': [],
            '持仓强度': [],
            '资金利用率%': []
        }
        
        # 逐行计算
        current_position = 0  # 0=空仓, 1=多头, -1=空头
        entry_price = 0
        entry_date = None
        
        for i, row in df.iterrows():
            trade_type = row.get('交易类型', '')
            trade_direction = row.get('交易方向', '')
            current_price = row.get('交易价格', 0)
            position_quantity = row.get('持仓数量', 0)
            position_cost = row.get('持仓成本', 0)
            current_market_value = row.get('当前市值', 0)
            total_assets = row.get('总资产', 10000)
            account_balance = row.get('账户余额', 10000)
            
            # 更新持仓状态
            if trade_type == '开仓':
                if '多头' in str(trade_direction):
                    current_position = 1
                elif '空头' in str(trade_direction):
                    current_position = -1
                entry_price = current_price
                entry_date = row['交易日期']
            elif trade_type == '平仓':
                current_position = 0
                entry_price = 0
                entry_date = None
            
            # 计算持仓状态
            if current_position == 0:
                position_status = '空仓'
                position_direction = '无'
                holding_days = 0
                position_ratio = 0
                pnl_ratio = 0
                take_profit_distance = 0
                stop_loss_distance = 0
                risk_reward_ratio = 0
                position_intensity = '无'
                capital_utilization = 0
            else:
                position_status = '持仓中'
                position_direction = '多头' if current_position == 1 else '空头'
                
                # 持仓天数
                if entry_date:
                    holding_days = (row['交易日期'] - entry_date).days + 1
                else:
                    holding_days = 1
                
                # 持仓比例
                position_ratio = (current_market_value / total_assets * 100) if total_assets > 0 else 0
                
                # 盈亏比例
                if entry_price > 0:
                    if current_position == 1:  # 多头
                        pnl_ratio = (current_price - entry_price) / entry_price * 100
                    else:  # 空头
                        pnl_ratio = (entry_price - current_price) / entry_price * 100
                else:
                    pnl_ratio = 0
                
                # 止盈止损距离（假设止盈2.5%，止损1.5%）
                take_profit_threshold = 2.5
                stop_loss_threshold = 1.5
                
                if current_position == 1:  # 多头
                    take_profit_price = entry_price * (1 + take_profit_threshold / 100)
                    stop_loss_price = entry_price * (1 - stop_loss_threshold / 100)
                    take_profit_distance = (take_profit_price - current_price) / current_price * 100
                    stop_loss_distance = (current_price - stop_loss_price) / current_price * 100
                else:  # 空头
                    take_profit_price = entry_price * (1 - take_profit_threshold / 100)
                    stop_loss_price = entry_price * (1 + stop_loss_threshold / 100)
                    take_profit_distance = (current_price - take_profit_price) / current_price * 100
                    stop_loss_distance = (stop_loss_price - current_price) / current_price * 100
                
                # 确保距离不为负数
                take_profit_distance = max(0, take_profit_distance)
                stop_loss_distance = max(0, stop_loss_distance)
                
                # 风险收益比
                if stop_loss_distance > 0:
                    risk_reward_ratio = take_profit_distance / stop_loss_distance
                else:
                    risk_reward_ratio = 0
                
                # 持仓强度
                if position_ratio >= 80:
                    position_intensity = '重仓'
                elif position_ratio >= 50:
                    position_intensity = '中仓'
                elif position_ratio >= 20:
                    position_intensity = '轻仓'
                else:
                    position_intensity = '试探'
                
                # 资金利用率
                capital_utilization = (current_market_value / (account_balance + current_market_value) * 100) if (account_balance + current_market_value) > 0 else 0
            
            # 添加到字段列表
            enhanced_fields['持仓状态'].append(position_status)
            enhanced_fields['持仓方向'].append(position_direction)
            enhanced_fields['持仓天数'].append(holding_days)
            enhanced_fields['持仓比例%'].append(round(position_ratio, 2))
            enhanced_fields['盈亏比例%'].append(round(pnl_ratio, 2))
            enhanced_fields['止盈距离%'].append(round(take_profit_distance, 2))
            enhanced_fields['止损距离%'].append(round(stop_loss_distance, 2))
            enhanced_fields['风险收益比'].append(round(risk_reward_ratio, 2))
            enhanced_fields['持仓强度'].append(position_intensity)
            enhanced_fields['资金利用率%'].append(round(capital_utilization, 2))
        
        # 添加新字段到DataFrame
        for field_name, field_values in enhanced_fields.items():
            df[field_name] = field_values
        
        return df
    
    def enhance_excel_file(self):
        """增强Excel文件"""
        print("🔧 增强版持仓追踪器")
        print("=" * 50)
        print(f"📅 处理时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        # 备份原始文件
        if not self.backup_original_file():
            print("❌ 备份失败，停止处理")
            return False
        
        # 加载现有数据
        df = self.load_existing_data()
        if df is None:
            return False
        
        print(f"📊 原始字段数: {len(df.columns)}")
        print(f"📋 原始记录数: {len(df)}")
        
        # 计算增强字段
        print("🔄 计算增强持仓字段...")
        enhanced_df = self.calculate_enhanced_position_fields(df)
        
        # 保存增强后的文件
        try:
            enhanced_df.to_excel(self.excel_file, index=False)
            print(f"✅ 增强文件已保存: {self.excel_file}")
            print(f"📊 增强后字段数: {len(enhanced_df.columns)}")
            print(f"📋 记录数: {len(enhanced_df)}")
            
            # 显示新增字段
            new_fields = ['持仓状态', '持仓方向', '持仓天数', '持仓比例%', '盈亏比例%', 
                         '止盈距离%', '止损距离%', '风险收益比', '持仓强度', '资金利用率%']
            print(f"\n📈 新增持仓字段:")
            for field in new_fields:
                print(f"   • {field}")
            
            # 显示最新记录的持仓状态
            if len(enhanced_df) > 0:
                latest = enhanced_df.iloc[-1]
                print(f"\n📋 最新持仓状态:")
                print(f"   持仓状态: {latest['持仓状态']}")
                print(f"   持仓方向: {latest['持仓方向']}")
                print(f"   持仓天数: {latest['持仓天数']}天")
                print(f"   持仓比例: {latest['持仓比例%']:.2f}%")
                print(f"   盈亏比例: {latest['盈亏比例%']:+.2f}%")
                print(f"   持仓强度: {latest['持仓强度']}")
                print(f"   资金利用率: {latest['资金利用率%']:.2f}%")
            
            return True
            
        except Exception as e:
            print(f"❌ 保存文件失败: {e}")
            return False
    
    def create_position_summary(self):
        """创建持仓汇总报告"""
        df = self.load_existing_data()
        if df is None:
            return
        
        print(f"\n📊 持仓汇总报告")
        print("=" * 40)
        
        # 统计持仓状态分布
        if '持仓状态' in df.columns:
            status_counts = df['持仓状态'].value_counts()
            print(f"持仓状态分布:")
            for status, count in status_counts.items():
                print(f"   {status}: {count} 次 ({count/len(df)*100:.1f}%)")
        
        # 统计持仓方向分布
        if '持仓方向' in df.columns:
            direction_counts = df['持仓方向'].value_counts()
            print(f"\n持仓方向分布:")
            for direction, count in direction_counts.items():
                print(f"   {direction}: {count} 次 ({count/len(df)*100:.1f}%)")
        
        # 统计持仓强度分布
        if '持仓强度' in df.columns:
            intensity_counts = df['持仓强度'].value_counts()
            print(f"\n持仓强度分布:")
            for intensity, count in intensity_counts.items():
                print(f"   {intensity}: {count} 次 ({count/len(df)*100:.1f}%)")

def main():
    """主函数"""
    tracker = EnhancedPositionTracker()
    
    print("🎯 选择操作:")
    print("1. 增强Excel文件（添加持仓状态栏目）")
    print("2. 查看持仓汇总报告")
    print("3. 两者都执行")
    
    choice = input("请输入选择 (1/2/3): ").strip()
    
    if choice == "1":
        tracker.enhance_excel_file()
    elif choice == "2":
        tracker.create_position_summary()
    elif choice == "3":
        if tracker.enhance_excel_file():
            tracker.create_position_summary()
    else:
        print("❌ 无效选择")

if __name__ == "__main__":
    main()
