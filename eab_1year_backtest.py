#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
EAB_0023HK 1年回测系统
初始资本: 2500港币
回测期间: 最近1年数据
"""

import pandas as pd
import numpy as np
import mysql.connector
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

class EAB1YearBacktest:
    def __init__(self):
        """初始化1年回测系统"""
        self.initial_capital = 2500      # 初始资本2500港币
        self.transaction_cost = 0.001    # 0.1%交易成本
        self.position_ratio = 0.8        # 80%仓位比例
        self.max_holding_days = 3        # 最大持仓3天
        
        # 止盈止损设置
        self.take_profit = 0.025         # 2.5%止盈
        self.stop_loss = 0.02            # 2%止损
        
        # 当前状态
        self.position = 0                # 0=空仓, 1=多头, -1=空头
        self.position_size = 0
        self.entry_price = 0
        self.entry_date = None
        self.current_capital = self.initial_capital
        
        # 记录
        self.trades = []
        self.daily_equity = []
        
    def load_1year_data(self):
        """加载最近1年数据"""
        print("📊 加载EAB_0023HK 最近1年数据...")
        print(f"💰 初始资本: {self.initial_capital:,} 港币")
        
        try:
            config = {
                'host': 'localhost',
                'port': 3306,
                'user': 'root',
                'password': '12345678',
                'database': 'finance',
                'charset': 'utf8mb4'
            }
            
            conn = mysql.connector.connect(**config)
            cursor = conn.cursor()
            
            # 计算1年前的日期
            one_year_ago = datetime.now() - timedelta(days=365)
            
            # 查询最近1年数据
            query = """
            SELECT 
                Date, Close, High, Low, Volume,
                Y_Value, X_Value, E_Value, 
                Full_Y, E as E2, Controller,
                RSI, MFI, midprice
            FROM eab_0023hk 
            WHERE Date >= %s
            AND Close IS NOT NULL
            ORDER BY Date ASC
            """
            
            cursor.execute(query, (one_year_ago.strftime('%Y-%m-%d'),))
            results = cursor.fetchall()
            
            if not results:
                print("❌ 没有找到最近1年数据")
                return False
            
            columns = ['date', 'close', 'high', 'low', 'volume',
                      'y_value', 'x_value', 'e_value', 
                      'full_y', 'e2', 'controller',
                      'rsi', 'mfi', 'midprice']
            
            self.df = pd.DataFrame(results, columns=columns)
            self.df['date'] = pd.to_datetime(self.df['date'])
            
            # 转换数值列
            numeric_cols = ['close', 'high', 'low', 'volume', 'y_value', 'x_value', 
                           'e_value', 'full_y', 'e2', 'controller', 'rsi', 'mfi', 'midprice']
            
            for col in numeric_cols:
                if col in self.df.columns:
                    self.df[col] = pd.to_numeric(self.df[col], errors='coerce')
            
            cursor.close()
            conn.close()
            
            # 数据统计
            start_date = self.df['date'].min()
            end_date = self.df['date'].max()
            total_days = (end_date - start_date).days
            
            print(f"✅ 成功加载 {len(self.df):,} 条数据")
            print(f"📅 时间范围: {start_date.date()} 到 {end_date.date()}")
            print(f"⏰ 总天数: {total_days:,} 天")
            print(f"💰 价格范围: {self.df['close'].min():.2f} - {self.df['close'].max():.2f} 港币")
            print(f"📈 期间涨幅: {(self.df['close'].iloc[-1]/self.df['close'].iloc[0]-1)*100:+.1f}%")
            
            return True
            
        except Exception as e:
            print(f"❌ 数据加载失败: {e}")
            return False
    
    def calculate_position_size(self, capital, price):
        """计算仓位大小"""
        # 80%资金投入
        available_capital = capital * self.position_ratio
        position_size = int(available_capital / price)
        
        # 确保最小仓位100股
        return max(position_size, 100)
    
    def generate_trading_signal(self, row):
        """生成交易信号"""
        # 双XYE系统
        y1, x1, e1 = row['y_value'], row['x_value'], row['e_value']
        y2, e2, controller = row['full_y'], row['e2'], row['controller']
        
        # 技术指标
        rsi, mfi = row['rsi'], row['mfi']
        
        # 价格偏离回归线
        price_deviation = 0
        if pd.notna(row['midprice']) and row['midprice'] > 0:
            price_deviation = (row['close'] - row['midprice']) / row['midprice']
        
        # 信号计分系统
        buy_score = 0
        sell_score = 0
        
        # 系统1: XYE信号 (权重2)
        if pd.notna(e1) and pd.notna(y1):
            if e1 > 0 and y1 > 0.6:
                buy_score += 2
            elif e1 < -0.05 and y1 < 0.4:
                sell_score += 2
        
        # 系统2: Controller信号 (权重2)
        if pd.notna(controller):
            if controller == 1:
                buy_score += 2
            elif controller == 0 and price_deviation > 0.03:
                sell_score += 2
        
        # RSI信号 (权重1)
        if pd.notna(rsi):
            if rsi < 35:  # 超卖
                buy_score += 1
            elif rsi > 65:  # 超买
                sell_score += 1
        
        # MFI信号 (权重1)
        if pd.notna(mfi):
            if mfi < 25:  # 资金流超卖
                buy_score += 1
            elif mfi > 75:  # 资金流超买
                sell_score += 1
        
        # 价格偏离信号 (权重1)
        if price_deviation < -0.05:  # 价格低于回归线5%
            buy_score += 1
        elif price_deviation > 0.05:  # 价格高于回归线5%
            sell_score += 1
        
        # 综合判断
        if buy_score >= 4:
            return {"signal": "买入", "strength": buy_score}
        elif sell_score >= 4:
            return {"signal": "卖出", "strength": sell_score}
        elif buy_score >= 3:
            return {"signal": "买入", "strength": buy_score}
        elif sell_score >= 3:
            return {"signal": "卖出", "strength": sell_score}
        else:
            return {"signal": "观望", "strength": max(buy_score, sell_score)}
    
    def check_exit_conditions(self, row, signal_info):
        """检查平仓条件"""
        if self.position == 0:
            return False, ""
        
        current_price = row['close']
        
        # 1. 时间止损
        if self.entry_date:
            holding_days = (row['date'] - self.entry_date).days
            if holding_days >= self.max_holding_days:
                return True, f"时间止损({holding_days}天)"
        
        # 2. 盈亏止损
        if self.position == 1:  # 多头
            pnl_ratio = (current_price - self.entry_price) / self.entry_price
        else:  # 空头
            pnl_ratio = (self.entry_price - current_price) / self.entry_price
        
        if pnl_ratio >= self.take_profit:
            return True, f"止盈({pnl_ratio*100:.1f}%)"
        elif pnl_ratio <= -self.stop_loss:
            return True, f"止损({pnl_ratio*100:.1f}%)"
        
        # 3. 信号反转
        if signal_info['strength'] >= 4:
            if (self.position == 1 and signal_info['signal'] == "卖出") or \
               (self.position == -1 and signal_info['signal'] == "买入"):
                return True, "强信号反转"
        
        return False, ""
    
    def execute_trade(self, row, action, reason=""):
        """执行交易"""
        current_price = row['close']
        
        if action == "开多仓":
            self.position_size = self.calculate_position_size(self.current_capital, current_price)
            trade_amount = self.position_size * current_price
            trade_cost = trade_amount * self.transaction_cost
            total_cost = trade_amount + trade_cost
            
            if total_cost <= self.current_capital:
                self.current_capital -= total_cost
                self.position = 1
                self.entry_price = current_price
                self.entry_date = row['date']
                
                self.trades.append({
                    'date': row['date'],
                    'action': '开多仓',
                    'price': current_price,
                    'size': self.position_size,
                    'amount': trade_amount,
                    'cost': trade_cost,
                    'capital': self.current_capital,
                    'reason': reason
                })
        
        elif action == "开空仓":
            self.position_size = self.calculate_position_size(self.current_capital, current_price)
            trade_amount = self.position_size * current_price
            trade_cost = trade_amount * self.transaction_cost
            
            self.current_capital += (trade_amount - trade_cost)
            self.position = -1
            self.entry_price = current_price
            self.entry_date = row['date']
            
            self.trades.append({
                'date': row['date'],
                'action': '开空仓',
                'price': current_price,
                'size': self.position_size,
                'amount': trade_amount,
                'cost': trade_cost,
                'capital': self.current_capital,
                'reason': reason
            })
        
        elif action == "平仓":
            trade_amount = self.position_size * current_price
            trade_cost = trade_amount * self.transaction_cost
            
            if self.position == 1:  # 平多仓
                pnl = (current_price - self.entry_price) * self.position_size
                self.current_capital += (trade_amount - trade_cost)
            else:  # 平空仓
                pnl = (self.entry_price - current_price) * self.position_size
                self.current_capital -= (trade_amount + trade_cost)
            
            # 扣除开仓手续费
            net_pnl = pnl - (self.position_size * self.entry_price * self.transaction_cost)
            
            self.trades.append({
                'date': row['date'],
                'action': f'平{"多" if self.position == 1 else "空"}仓',
                'price': current_price,
                'size': self.position_size,
                'pnl': net_pnl,
                'capital': self.current_capital,
                'reason': reason
            })
            
            # 重置持仓
            self.position = 0
            self.position_size = 0
            self.entry_price = 0
            self.entry_date = None
    
    def run_1year_backtest(self):
        """运行1年回测"""
        print("\n🚀 开始1年回测...")
        print("📋 回测参数:")
        print(f"   初始资本: {self.initial_capital:,} 港币")
        print(f"   仓位比例: {self.position_ratio*100:.0f}%")
        print(f"   止盈: {self.take_profit*100:.1f}%")
        print(f"   止损: {self.stop_loss*100:.1f}%")
        print(f"   最大持仓: {self.max_holding_days} 天")
        print()
        
        for i, row in self.df.iterrows():
            # 计算总资产
            total_assets = self.current_capital
            if self.position != 0:
                position_value = self.position_size * row['close']
                if self.position == 1:
                    total_assets += position_value
                else:
                    total_assets += (self.position_size * self.entry_price - position_value)
            
            # 记录每日权益
            self.daily_equity.append({
                'date': row['date'],
                'capital': self.current_capital,
                'total_assets': total_assets,
                'position': self.position,
                'price': row['close']
            })
            
            # 生成交易信号
            signal_info = self.generate_trading_signal(row)
            
            # 检查平仓条件
            should_exit, exit_reason = self.check_exit_conditions(row, signal_info)
            if should_exit:
                self.execute_trade(row, "平仓", exit_reason)
            
            # 检查开仓条件 (只在空仓且信号强度足够时)
            if self.position == 0 and signal_info['strength'] >= 3:
                if signal_info['signal'] == "买入":
                    self.execute_trade(row, "开多仓", f"买入信号(强度{signal_info['strength']})")
                elif signal_info['signal'] == "卖出":
                    self.execute_trade(row, "开空仓", f"卖出信号(强度{signal_info['strength']})")
        
        # 最后强制平仓
        if self.position != 0:
            last_row = self.df.iloc[-1]
            self.execute_trade(last_row, "平仓", "回测结束强制平仓")
        
        print(f"✅ 1年回测完成！")
    
    def analyze_1year_results(self):
        """分析1年回测结果"""
        print("\n📊 1年回测结果分析")
        print("=" * 50)
        
        if not self.trades:
            print("❌ 没有产生任何交易")
            return
        
        # 交易统计
        entry_trades = [t for t in self.trades if '开' in t['action']]
        exit_trades = [t for t in self.trades if '平' in t['action']]
        
        total_trades = len(entry_trades)
        profitable_trades = len([t for t in exit_trades if t.get('pnl', 0) > 0])
        
        print(f"📈 交易统计:")
        print(f"   总交易次数: {total_trades}")
        print(f"   盈利交易: {profitable_trades}")
        print(f"   亏损交易: {len(exit_trades) - profitable_trades}")
        if len(exit_trades) > 0:
            print(f"   胜率: {profitable_trades/len(exit_trades)*100:.1f}%")
        
        # 收益统计
        final_capital = self.current_capital
        total_return = (final_capital / self.initial_capital - 1) * 100
        
        print(f"\n💰 收益统计:")
        print(f"   初始资金: {self.initial_capital:,.2f} 港币")
        print(f"   最终资金: {final_capital:,.2f} 港币")
        print(f"   绝对收益: {final_capital - self.initial_capital:+,.2f} 港币")
        print(f"   总收益率: {total_return:+.2f}%")
        
        # 年化收益率
        days = (self.df['date'].max() - self.df['date'].min()).days
        if days > 0:
            annual_return = (final_capital / self.initial_capital) ** (365/days) - 1
            print(f"   年化收益率: {annual_return*100:+.2f}%")
        
        # 买入持有对比
        start_price = self.df['close'].iloc[0]
        end_price = self.df['close'].iloc[-1]
        buy_hold_return = (end_price / start_price - 1) * 100
        
        print(f"\n🆚 买入持有对比:")
        print(f"   买入持有收益率: {buy_hold_return:+.2f}%")
        print(f"   策略超额收益: {total_return - buy_hold_return:+.2f}%")
        
        # 风险指标
        equity_df = pd.DataFrame(self.daily_equity)
        equity_df['peak'] = equity_df['total_assets'].cummax()
        equity_df['drawdown'] = (equity_df['total_assets'] - equity_df['peak']) / equity_df['peak']
        max_drawdown = equity_df['drawdown'].min() * 100
        
        print(f"\n📉 风险指标:")
        print(f"   最大回撤: {max_drawdown:.2f}%")
        
        # 月度统计
        equity_df['month'] = equity_df['date'].dt.to_period('M')
        monthly_returns = []
        
        for month in equity_df['month'].unique():
            month_data = equity_df[equity_df['month'] == month]
            if len(month_data) > 1:
                start_value = month_data['total_assets'].iloc[0]
                end_value = month_data['total_assets'].iloc[-1]
                month_return = (end_value / start_value - 1) * 100
                monthly_returns.append(month_return)
        
        if monthly_returns:
            positive_months = len([r for r in monthly_returns if r > 0])
            print(f"   盈利月份: {positive_months}/{len(monthly_returns)} ({positive_months/len(monthly_returns)*100:.0f}%)")
        
        # 保存结果
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        trades_df = pd.DataFrame(self.trades)
        trades_df.to_excel(f"EAB_1年交易记录_{timestamp}.xlsx", index=False)
        
        equity_df.to_excel(f"EAB_1年权益曲线_{timestamp}.xlsx", index=False)
        
        print(f"\n✅ 结果已保存:")
        print(f"   📄 交易记录: EAB_1年交易记录_{timestamp}.xlsx")
        print(f"   📈 权益曲线: EAB_1年权益曲线_{timestamp}.xlsx")

def main():
    """主函数"""
    print("🏦 EAB_0023HK 1年回测系统")
    print("=" * 50)
    print("📋 系统特点:")
    print("   • 初始资本: 2,500 港币")
    print("   • 回测期间: 最近1年")
    print("   • 双XYE + 多技术指标")
    print("   • 智能仓位管理")
    print("   • 严格风险控制")
    
    try:
        backtest = EAB1YearBacktest()
        
        if not backtest.load_1year_data():
            return
        
        backtest.run_1year_backtest()
        backtest.analyze_1year_results()
        
    except Exception as e:
        print(f"\n❌ 回测失败: {e}")
        import traceback
        traceback.print_exc()
    else:
        print(f"\n🎉 EAB_0023HK 1年回测完成！")

if __name__ == "__main__":
    main()
