import pandas as pd

# 读取Excel
df = pd.read_excel('交易记录追踪0023HK.xlsx')
print(f"当前列数: {len(df.columns)}")

# 添加必要字段
if 'Full_Y' not in df.columns:
    y_idx = df.columns.get_loc('Y值')
    df.insert(y_idx + 1, 'Full_Y', 0.0000)
    print("添加 Full_Y 列")

if 'MoneyFlowRatio' not in df.columns:
    full_y_idx = df.columns.get_loc('Full_Y')
    df.insert(full_y_idx + 1, 'MoneyFlowRatio', 0.0000)
    print("添加 MoneyFlowRatio 列")

if 'MyE' not in df.columns:
    mfr_idx = df.columns.get_loc('MoneyFlowRatio')
    df.insert(mfr_idx + 1, 'MyE', 0.0000)
    print("添加 MyE 列")

# 设置技术指标值
y_val = 0.2069
full_y = 0.2069
x_val = 0.3211
mfr = 0.3211
e_val = -0.0525

# 计算MyE (Cosmoon公式)
# MyE = 8 × MoneyFlowRatio × Full_Y - 3 × MoneyFlowRatio - 3 × Full_Y + 1
mye_value = 8 * mfr * full_y - 3 * mfr - 3 * full_y + 1

print(f"MyE计算: 8×{mfr:.4f}×{full_y:.4f} - 3×{mfr:.4f} - 3×{full_y:.4f} + 1 = {mye_value:.4f}")

# 更新最新记录
latest_idx = -1
df.iloc[latest_idx, df.columns.get_loc('Y值')] = y_val
df.iloc[latest_idx, df.columns.get_loc('Full_Y')] = full_y
df.iloc[latest_idx, df.columns.get_loc('MoneyFlowRatio')] = mfr
df.iloc[latest_idx, df.columns.get_loc('MyE')] = mye_value
df.iloc[latest_idx, df.columns.get_loc('X值')] = x_val
df.iloc[latest_idx, df.columns.get_loc('E值')] = e_val

# 保存
df.to_excel('交易记录追踪0023HK.xlsx', index=False)

print("✅ Excel已更新")
print(f"📊 MyE值: {mye_value:.4f}")
print(f"📋 总列数: {len(df.columns)}")

# 显示XYE字段
xye_fields = ['Y值', 'Full_Y', 'X值', 'MoneyFlowRatio', 'E值', 'MyE']
print("\n📈 XYE字段:")
for field in xye_fields:
    if field in df.columns:
        idx = df.columns.get_loc(field) + 1
        print(f"  {idx:2d}. {field}")
