#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
基于完整每日更新系统的1年回测
初始资本: 2500港币
严格按照现有系统逻辑执行
"""

import pandas as pd
import numpy as np
import mysql.connector
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

class CompleteSystemBacktest:
    def __init__(self):
        """初始化完整系统回测"""
        self.initial_capital = 2500.00
        self.current_capital = 2500.00
        self.transaction_cost = 0.001  # 0.1%
        
        # 系统参数 (基于您的完整系统)
        self.position_ratio = 0.8      # 80%仓位
        self.max_holding_days = 3      # 最大持仓3天
        self.take_profit = 0.02        # 2%止盈
        self.stop_loss = 0.015         # 1.5%止损
        
        # 当前状态
        self.position = 0              # 0=空仓, 1=多头, -1=空头
        self.position_size = 0
        self.entry_price = 0
        self.entry_date = None
        self.holding_days = 0
        
        # 记录
        self.trades = []
        self.daily_reports = []
        
    def load_historical_data(self):
        """加载历史数据 (模拟完整系统的数据库更新)"""
        print("📊 步骤1: 加载历史数据 (模拟数据库更新)")
        print("-" * 40)
        
        try:
            config = {
                'host': 'localhost',
                'port': 3306,
                'user': 'root',
                'password': '12345678',
                'database': 'finance',
                'charset': 'utf8mb4'
            }
            
            conn = mysql.connector.connect(**config)
            cursor = conn.cursor()
            
            # 获取最近1年数据
            one_year_ago = datetime.now() - timedelta(days=365)
            
            query = """
            SELECT 
                Date, Close, High, Low,
                Y_Value, X_Value, E_Value, 
                Full_Y, E as E2, Controller,
                RSI, MFI, midprice
            FROM eab_0023hk 
            WHERE Date >= %s
            AND Close IS NOT NULL
            ORDER BY Date ASC
            """
            
            cursor.execute(query, (one_year_ago.strftime('%Y-%m-%d'),))
            results = cursor.fetchall()
            
            columns = ['date', 'close', 'high', 'low',
                      'y_value', 'x_value', 'e_value', 
                      'full_y', 'e2', 'controller',
                      'rsi', 'mfi', 'midprice']
            
            self.df = pd.DataFrame(results, columns=columns)
            self.df['date'] = pd.to_datetime(self.df['date'])
            
            # 转换数值列
            numeric_cols = ['close', 'high', 'low', 'y_value', 'x_value', 
                           'e_value', 'full_y', 'e2', 'controller', 'rsi', 'mfi', 'midprice']
            
            for col in numeric_cols:
                if col in self.df.columns:
                    self.df[col] = pd.to_numeric(self.df[col], errors='coerce')
            
            cursor.close()
            conn.close()
            
            print(f"✅ 数据库更新成功")
            print(f"   加载数据: {len(self.df)} 条")
            print(f"   时间范围: {self.df['date'].min().date()} 到 {self.df['date'].max().date()}")
            
            return True
            
        except Exception as e:
            print(f"❌ 数据库更新失败: {e}")
            return False
    
    def update_full_y_controller(self, row):
        """步骤2: 更新Full_Y和Controller字段 (模拟)"""
        # 在实际系统中，这里会调用simple_full_y_update.py
        # 这里我们直接使用数据库中的值
        return {
            'full_y_updated': True,
            'controller_updated': True
        }
    
    def generate_trading_signal(self, row):
        """模拟position_manager的信号生成逻辑"""
        # 双XYE系统信号生成 (基于您的系统逻辑)
        y1, x1, e1 = row['y_value'], row['x_value'], row['e_value']
        y2, e2, controller = row['full_y'], row['e2'], row['controller']
        
        # 价格偏离回归线
        price_deviation = 0
        if pd.notna(row['midprice']) and row['midprice'] > 0:
            price_deviation = (row['close'] - row['midprice']) / row['midprice']
        
        # 系统1信号
        signal1 = "观望"
        if pd.notna(e1) and pd.notna(y1):
            if e1 > 0 and y1 > 0.6:
                signal1 = "买入"
            elif e1 < -0.05 and y1 < 0.4:
                signal1 = "卖出"
        
        # 系统2信号
        signal2 = "观望"
        if pd.notna(controller):
            if controller == 1 and price_deviation < -0.05:
                signal2 = "买入"
            elif controller == 0 and price_deviation > 0.05:
                signal2 = "卖出"
        
        # 综合信号强度
        strength = 0
        final_signal = "观望"
        
        if signal1 == "买入" and signal2 == "买入":
            final_signal = "买入"
            strength = 4
        elif signal1 == "买入" or signal2 == "买入":
            final_signal = "买入"
            strength = 2
        elif signal1 == "卖出" and signal2 == "卖出":
            final_signal = "卖出"
            strength = 4
        elif signal1 == "卖出" or signal2 == "卖出":
            final_signal = "卖出"
            strength = 2
        
        return {
            'signal': final_signal,
            'strength': strength,
            'system1': signal1,
            'system2': signal2,
            'deviation': price_deviation,
            'reason': f"系统1: {signal1}, 系统2: {signal2}, 偏离={price_deviation*100:+.1f}%"
        }
    
    def run_position_management(self, row, signal):
        """步骤3: 持仓管理 (模拟position_manager逻辑)"""
        current_price = row['close']
        
        # 检查平仓条件
        should_exit, exit_reason = self.check_exit_conditions(row, signal)
        if should_exit:
            self.execute_trade(row, "平仓", exit_reason)
        
        # 检查开仓条件 (尽量不持仓策略 - 只在强信号时开仓)
        if self.position == 0 and signal['strength'] >= 4:
            if signal['signal'] == "买入":
                self.execute_trade(row, "开多仓", f"强买入信号(强度{signal['strength']})")
            elif signal['signal'] == "卖出":
                self.execute_trade(row, "开空仓", f"强卖出信号(强度{signal['strength']})")
    
    def check_exit_conditions(self, row, signal):
        """检查平仓条件"""
        if self.position == 0:
            return False, ""
        
        current_price = row['close']
        
        # 1. 时间止损
        if self.holding_days >= self.max_holding_days:
            return True, f"时间止损({self.holding_days}天)"
        
        # 2. 盈亏止损
        if self.position == 1:  # 多头
            pnl_ratio = (current_price - self.entry_price) / self.entry_price
        else:  # 空头
            pnl_ratio = (self.entry_price - current_price) / self.entry_price
        
        if pnl_ratio >= self.take_profit:
            return True, f"止盈({pnl_ratio*100:.1f}%)"
        elif pnl_ratio <= -self.stop_loss:
            return True, f"止损({pnl_ratio*100:.1f}%)"
        
        # 3. 信号反转
        if signal['strength'] >= 4:
            if (self.position == 1 and signal['signal'] == "卖出") or \
               (self.position == -1 and signal['signal'] == "买入"):
                return True, "强信号反转"
        
        return False, ""
    
    def execute_trade(self, row, action, reason):
        """执行交易 (模拟Excel更新)"""
        current_price = row['close']
        
        if action == "开多仓":
            # 计算仓位 (80%资金)
            available_capital = self.current_capital * self.position_ratio
            self.position_size = int(available_capital / current_price)
            self.position_size = max(self.position_size, 100)  # 最小100股
            
            trade_amount = self.position_size * current_price
            trade_cost = trade_amount * self.transaction_cost
            
            if trade_amount + trade_cost <= self.current_capital:
                self.current_capital -= (trade_amount + trade_cost)
                self.position = 1
                self.entry_price = current_price
                self.entry_date = row['date']
                self.holding_days = 1
                
                self.trades.append({
                    'date': row['date'],
                    'action': '开多仓',
                    'price': current_price,
                    'size': self.position_size,
                    'amount': trade_amount,
                    'cost': trade_cost,
                    'capital': self.current_capital,
                    'reason': reason
                })
        
        elif action == "开空仓":
            available_capital = self.current_capital * self.position_ratio
            self.position_size = int(available_capital / current_price)
            self.position_size = max(self.position_size, 100)
            
            trade_amount = self.position_size * current_price
            trade_cost = trade_amount * self.transaction_cost
            
            self.current_capital += (trade_amount - trade_cost)
            self.position = -1
            self.entry_price = current_price
            self.entry_date = row['date']
            self.holding_days = 1
            
            self.trades.append({
                'date': row['date'],
                'action': '开空仓',
                'price': current_price,
                'size': self.position_size,
                'amount': trade_amount,
                'cost': trade_cost,
                'capital': self.current_capital,
                'reason': reason
            })
        
        elif action == "平仓":
            trade_amount = self.position_size * current_price
            trade_cost = trade_amount * self.transaction_cost
            
            if self.position == 1:  # 平多仓
                pnl = (current_price - self.entry_price) * self.position_size
                self.current_capital += (trade_amount - trade_cost)
            else:  # 平空仓
                pnl = (self.entry_price - current_price) * self.position_size
                self.current_capital -= (trade_amount + trade_cost)
            
            # 扣除开仓手续费
            net_pnl = pnl - (self.position_size * self.entry_price * self.transaction_cost)
            
            self.trades.append({
                'date': row['date'],
                'action': f'平{"多" if self.position == 1 else "空"}仓',
                'price': current_price,
                'pnl': net_pnl,
                'capital': self.current_capital,
                'reason': reason
            })
            
            # 重置持仓
            self.position = 0
            self.position_size = 0
            self.entry_price = 0
            self.entry_date = None
            self.holding_days = 0
    
    def generate_daily_report(self, row, signal):
        """步骤4: 生成每日报告"""
        # 计算总资产
        total_assets = self.current_capital
        if self.position != 0:
            position_value = self.position_size * row['close']
            if self.position == 1:
                total_assets += position_value
            else:
                total_assets += (self.position_size * self.entry_price - position_value)
        
        # 生成报告
        report = {
            'date': row['date'],
            'close': row['close'],
            'y_value': row['y_value'],
            'x_value': row['x_value'],
            'e_value': row['e_value'],
            'full_y': row['full_y'],
            'controller': row['controller'],
            'rsi': row['rsi'],
            'mfi': row['mfi'],
            'signal': signal['signal'],
            'strength': signal['strength'],
            'reason': signal['reason'],
            'position': self.position,
            'position_size': self.position_size,
            'holding_days': self.holding_days,
            'current_capital': self.current_capital,
            'total_assets': total_assets,
            'cumulative_return': (total_assets / self.initial_capital - 1) * 100
        }
        
        self.daily_reports.append(report)
        return report
    
    def run_complete_backtest(self):
        """运行完整系统回测"""
        print("\n🚀 开始完整系统1年回测")
        print("=" * 60)
        print(f"💰 初始资本: {self.initial_capital:,} 港币")
        print(f"📋 系统特点: 尽量不持仓策略")
        print(f"⚖️ 仓位比例: {self.position_ratio*100:.0f}%")
        print(f"📅 最大持仓: {self.max_holding_days} 天")
        print()
        
        success_count = 0
        total_days = len(self.df)
        
        for i, row in self.df.iterrows():
            try:
                # 步骤1: 数据库更新 (已完成)
                
                # 步骤2: 更新Full_Y和Controller
                full_y_result = self.update_full_y_controller(row)
                if full_y_result['full_y_updated']:
                    success_count += 1
                
                # 步骤3: 生成交易信号
                signal = self.generate_trading_signal(row)
                
                # 步骤4: 持仓管理
                self.run_position_management(row, signal)
                
                # 步骤5: 生成每日报告
                daily_report = self.generate_daily_report(row, signal)
                
                # 更新持仓天数
                if self.position != 0:
                    self.holding_days += 1
                
                # 显示进度
                if i % 50 == 0:
                    progress = i / total_days * 100
                    print(f"   进度: {progress:.1f}% - {row['date'].date()} - 资产: {daily_report['total_assets']:,.0f}")
                
            except Exception as e:
                print(f"   ❌ {row['date'].date()} 处理失败: {e}")
        
        # 最后强制平仓
        if self.position != 0:
            last_row = self.df.iloc[-1]
            self.execute_trade(last_row, "平仓", "回测结束强制平仓")
        
        print(f"\n✅ 完整系统回测完成")
        print(f"📊 处理天数: {total_days}")
        print(f"📈 成功率: {success_count/total_days*100:.1f}%")
    
    def analyze_complete_results(self):
        """分析完整系统回测结果"""
        print("\n📊 完整系统回测结果分析")
        print("=" * 60)
        
        # 交易统计
        entry_trades = [t for t in self.trades if '开' in t['action']]
        exit_trades = [t for t in self.trades if '平' in t['action']]
        
        total_trades = len(entry_trades)
        profitable_trades = len([t for t in exit_trades if t.get('pnl', 0) > 0])
        
        print(f"📈 交易统计:")
        print(f"   总交易次数: {total_trades}")
        print(f"   盈利交易: {profitable_trades}")
        print(f"   亏损交易: {len(exit_trades) - profitable_trades}")
        if len(exit_trades) > 0:
            print(f"   胜率: {profitable_trades/len(exit_trades)*100:.1f}%")
        
        # 收益统计
        final_capital = self.current_capital
        total_return = (final_capital / self.initial_capital - 1) * 100
        
        print(f"\n💰 收益统计:")
        print(f"   初始资金: {self.initial_capital:,.2f} 港币")
        print(f"   最终资金: {final_capital:,.2f} 港币")
        print(f"   绝对收益: {final_capital - self.initial_capital:+,.2f} 港币")
        print(f"   总收益率: {total_return:+.2f}%")
        
        # 年化收益率
        days = (self.df['date'].max() - self.df['date'].min()).days
        if days > 0:
            annual_return = (final_capital / self.initial_capital) ** (365/days) - 1
            print(f"   年化收益率: {annual_return*100:+.2f}%")
        
        # 买入持有对比
        start_price = self.df['close'].iloc[0]
        end_price = self.df['close'].iloc[-1]
        buy_hold_return = (end_price / start_price - 1) * 100
        
        print(f"\n🆚 买入持有对比:")
        print(f"   买入持有收益率: {buy_hold_return:+.2f}%")
        print(f"   策略超额收益: {total_return - buy_hold_return:+.2f}%")
        
        # 策略特点验证
        reports_df = pd.DataFrame(self.daily_reports)
        empty_position_days = len(reports_df[reports_df['position'] == 0])
        empty_ratio = empty_position_days / len(reports_df) * 100
        
        print(f"\n🎯 策略特点验证:")
        print(f"   空仓天数: {empty_position_days}/{len(reports_df)} ({empty_ratio:.1f}%)")
        print(f"   策略符合性: {'✅ 符合尽量不持仓' if empty_ratio > 80 else '⚠️ 持仓过多'}")
        
        # 保存结果
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        trades_df = pd.DataFrame(self.trades)
        trades_df.to_excel(f"完整系统1年回测_交易记录_{timestamp}.xlsx", index=False)
        
        reports_df.to_excel(f"完整系统1年回测_每日报告_{timestamp}.xlsx", index=False)
        
        print(f"\n✅ 结果已保存:")
        print(f"   📄 交易记录: 完整系统1年回测_交易记录_{timestamp}.xlsx")
        print(f"   📊 每日报告: 完整系统1年回测_每日报告_{timestamp}.xlsx")

def main():
    """主函数"""
    print("🏦 基于完整每日更新系统的1年回测")
    print("=" * 60)
    print("📋 系统特点:")
    print("   • 严格按照现有系统逻辑")
    print("   • 模拟4步骤每日更新流程")
    print("   • 尽量不持仓策略")
    print("   • 双XYE信号系统")
    print("   • 初始资本: 2,500 港币")
    
    try:
        backtest = CompleteSystemBacktest()
        
        # 步骤1: 加载数据
        if not backtest.load_historical_data():
            return
        
        # 运行完整回测
        backtest.run_complete_backtest()
        
        # 分析结果
        backtest.analyze_complete_results()
        
    except Exception as e:
        print(f"\n❌ 回测失败: {e}")
        import traceback
        traceback.print_exc()
    else:
        print(f"\n🎉 完整系统1年回测完成！")

if __name__ == "__main__":
    main()
