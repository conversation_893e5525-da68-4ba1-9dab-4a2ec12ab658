#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
东亚银行(0023.HK) 实时信号分析器
分析当前市场状态，预测明天可能的买卖信号条件
"""

import yfinance as yf
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

class RealtimeSignalAnalyzer:
    def __init__(self):
        """初始化实时信号分析器"""
        self.ticker = '0023.HK'
        
        # 策略参数（与回测保持一致）
        self.x_threshold_long = 0.45
        self.y_threshold_long = 0.45
        self.x_threshold_short = 0.25
        self.y_threshold_short = 0.25
        
        # 止盈止损参数
        self.take_profit_long = 0.012   # 多头止盈 1.2%
        self.stop_loss_long = 0.008     # 多头止损 0.8%
        self.take_profit_short = 0.008  # 空头止盈 0.8%
        self.stop_loss_short = 0.012    # 空头止损 1.2%
        
    def fetch_latest_data(self, days=100):
        """获取最新数据"""
        try:
            end_date = datetime.now()
            start_date = end_date - timedelta(days=days)
            
            print(f"📊 获取 {self.ticker} 最近{days}天数据...")
            data = yf.download(self.ticker, start=start_date, end=end_date)
            
            if data.empty:
                raise ValueError("未获取到数据")
            
            # 处理多级列名
            if isinstance(data.columns, pd.MultiIndex):
                data.columns = [col[0] for col in data.columns]
            
            # 重命名列
            data = data.rename(columns={
                'Open': 'open', 'High': 'high', 'Low': 'low', 
                'Close': 'close', 'Volume': 'volume'
            })
            
            data = data.reset_index()
            data = data.rename(columns={'Date': 'date'})
            
            print(f"✓ 成功获取 {len(data)} 条数据")
            print(f"数据范围：{data['date'].min().strftime('%Y-%m-%d')} 至 {data['date'].max().strftime('%Y-%m-%d')}")
            
            return data
            
        except Exception as e:
            print(f"❌ 数据获取失败: {e}")
            return None
    
    def calculate_indicators(self, df):
        """计算技术指标"""
        print("🔧 计算技术指标...")
        
        # 计算回归线
        window = 60
        df['regression_line'] = df['close'].rolling(window=window).apply(
            lambda x: np.polyval(np.polyfit(range(len(x)), x, 1), len(x)-1), raw=False
        )
        
        # 计算RSI
        delta = df['close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
        rs = gain / loss
        df['rsi'] = 100 - (100 / (1 + rs))
        
        # 计算资金流比例 (X值)
        price_change = (df['close'] - df['open']) / df['open']
        money_flow = df['volume'] * price_change
        
        def calc_inflow_ratio(flows):
            if len(flows) == 0 or flows.isna().all():
                return 0.5
            flows = flows.dropna()
            if len(flows) == 0:
                return 0.5
            
            inflows = flows[flows > 0].sum()
            outflows = abs(flows[flows < 0].sum())
            total_flow = inflows + outflows
            
            return inflows / total_flow if total_flow > 0 else 0.5
        
        df['money_flow_ratio'] = money_flow.rolling(window=20).apply(calc_inflow_ratio, raw=False)
        df['money_flow_ratio'] = np.clip(df['money_flow_ratio'], 0.1, 0.9).fillna(0.5)
        
        # 计算Full_Y值 (控制系数)
        price_momentum = df['close'].pct_change(10)
        df['full_y'] = (df['rsi'] / 100 + np.tanh(price_momentum * 5) + 1) / 2
        df['full_y'] = np.clip(df['full_y'], 0.1, 0.9)
        
        # 计算E值
        df['e_value'] = 8 * df['money_flow_ratio'] * df['full_y'] - 3 * df['money_flow_ratio'] - 3 * df['full_y'] + 1
        
        # 计算价格相对回归线位置
        df['price_position'] = (df['close'] - df['regression_line']) / df['regression_line']
        
        print("✓ 技术指标计算完成")
        return df
    
    def analyze_current_state(self, df):
        """分析当前市场状态"""
        if len(df) == 0:
            return None
        
        latest = df.iloc[-1]
        
        print("\n" + "="*60)
        print("📈 当前市场状态分析")
        print("="*60)
        
        print(f"📅 最新日期: {latest['date'].strftime('%Y-%m-%d')}")
        print(f"💰 最新价格: {latest['close']:.2f} 港币")
        print(f"📊 回归线价格: {latest['regression_line']:.2f} 港币")
        print(f"📍 价格位置: {latest['price_position']*100:.2f}% ({'高于' if latest['price_position'] > 0 else '低于'}回归线)")
        
        print(f"\n🔢 关键指标:")
        print(f"  • X值 (资金流比例): {latest['money_flow_ratio']:.3f}")
        print(f"  • Y值 (控制系数): {latest['full_y']:.3f}")
        print(f"  • E值 (博弈期望): {latest['e_value']:.3f}")
        print(f"  • RSI: {latest['rsi']:.1f}")
        
        return latest
    
    def predict_signals(self, latest):
        """预测明天可能的信号"""
        print(f"\n🎯 明天信号预测分析")
        print("="*60)
        
        x_val = latest['money_flow_ratio']
        y_val = latest['full_y']
        e_val = latest['e_value']
        price_pos = latest['price_position']
        
        signals = []
        
        # 买涨信号条件
        if (e_val > 0 and x_val > self.x_threshold_long and 
            y_val > self.y_threshold_long and price_pos < 0):
            signals.append({
                'type': '🟢 买涨信号',
                'condition': '满足',
                'reason': f'E值>0({e_val:.3f}) + X值>{self.x_threshold_long}({x_val:.3f}) + Y值>{self.y_threshold_long}({y_val:.3f}) + 价格低于回归线',
                'action': '建议开多仓',
                'stop_loss': latest['close'] * (1 - self.stop_loss_long),
                'take_profit': latest['close'] * (1 + self.take_profit_long)
            })
        
        # 买跌信号条件
        elif ((y_val < self.y_threshold_short or x_val < self.x_threshold_short) and 
              price_pos > 0):
            signals.append({
                'type': '🔴 买跌信号',
                'condition': '满足',
                'reason': f'Y值<{self.y_threshold_short}({y_val:.3f}) 或 X值<{self.x_threshold_short}({x_val:.3f}) + 价格高于回归线',
                'action': '建议开空仓',
                'stop_loss': latest['close'] * (1 + self.stop_loss_short),
                'take_profit': latest['close'] * (1 - self.take_profit_short)
            })
        
        # 观望条件
        else:
            signals.append({
                'type': '⚪ 观望信号',
                'condition': '当前状态',
                'reason': '不满足买涨或买跌条件',
                'action': '建议观望',
                'stop_loss': None,
                'take_profit': None
            })
        
        # 显示信号分析
        for signal in signals:
            print(f"\n{signal['type']}")
            print(f"  状态: {signal['condition']}")
            print(f"  原因: {signal['reason']}")
            print(f"  建议: {signal['action']}")
            
            if signal['stop_loss'] and signal['take_profit']:
                print(f"  止损价: {signal['stop_loss']:.2f} 港币")
                print(f"  止盈价: {signal['take_profit']:.2f} 港币")
        
        return signals
    
    def show_signal_conditions(self):
        """显示信号生成条件"""
        print(f"\n📋 信号生成条件说明")
        print("="*60)
        
        print("🟢 买涨信号条件:")
        print(f"  1. E值 > 0")
        print(f"  2. X值(资金流比例) > {self.x_threshold_long}")
        print(f"  3. Y值(控制系数) > {self.y_threshold_long}")
        print(f"  4. 价格低于回归线 (price_position < 0)")
        
        print(f"\n🔴 买跌信号条件:")
        print(f"  1. Y值 < {self.y_threshold_short} 或 X值 < {self.x_threshold_short}")
        print(f"  2. 价格高于回归线 (price_position > 0)")
        
        print(f"\n⚪ 观望条件:")
        print(f"  不满足以上买涨或买跌条件时")
        
        print(f"\n💡 止盈止损设置:")
        print(f"  多头: 止盈{self.take_profit_long*100:.1f}% / 止损{self.stop_loss_long*100:.1f}%")
        print(f"  空头: 止盈{self.take_profit_short*100:.1f}% / 止损{self.stop_loss_short*100:.1f}%")

def main():
    """主函数"""
    print("🏦 东亚银行(0023.HK) 实时信号分析器")
    print("="*60)
    
    analyzer = RealtimeSignalAnalyzer()
    
    # 获取最新数据
    df = analyzer.fetch_latest_data(days=100)
    if df is None:
        return
    
    # 计算技术指标
    df = analyzer.calculate_indicators(df)
    
    # 分析当前状态
    latest = analyzer.analyze_current_state(df)
    if latest is None:
        return
    
    # 预测信号
    signals = analyzer.predict_signals(latest)
    
    # 显示信号条件
    analyzer.show_signal_conditions()
    
    print(f"\n⚠️  重要提醒:")
    print(f"  • 此分析基于历史数据和当前状态")
    print(f"  • 明天的实际信号需要明天的真实市场数据")
    print(f"  • 请结合实际市场情况谨慎决策")
    print(f"  • 投资有风险，决策需谨慎")

if __name__ == "__main__":
    main()
