#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
持仓策略 - 2500港元1年投资计划
============================

专为小资金投资者设计的稳健持仓策略
适用于：初始资本2500港元，投资期限1年

策略特点：
1. 小资金友好 - 最低100港元开仓
2. 风险可控 - 严格止损机制
3. 资金利用率高 - 分批建仓
4. 适合新手 - 简单易懂的规则

作者: Cosmoon NG
日期: 2025年7月28日
版本: v1.0
"""

import yfinance as yf
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import matplotlib.pyplot as plt
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class SmallCapitalStrategy:
    """小资金持仓策略系统"""
    
    def __init__(self):
        """初始化策略参数"""
        self.symbol = "0023.HK"
        self.company_name = "东亚银行"
        
        # 资金设置
        self.initial_capital = 2500      # 初始资本2500港元
        self.min_trade_amount = 100      # 最小交易金额100港元
        self.max_position_ratio = 0.8    # 最大仓位80%
        self.reserve_ratio = 0.2         # 保留20%现金
        
        # 风险控制参数（适合小资金）
        self.stop_loss = 0.08           # 止损8%（相对宽松）
        self.take_profit = 0.15         # 止盈15%（目标合理）
        self.max_single_position = 0.4  # 单次最大仓位40%
        
        # 分批建仓参数
        self.position_levels = [0.2, 0.3, 0.3]  # 分3批建仓：20%, 30%, 30%
        self.add_position_drop = 0.05    # 下跌5%加仓
        
        # 交易手续费
        self.commission_rate = 0.001     # 0.1%手续费
        
        # 当前状态
        self.current_capital = self.initial_capital
        self.positions = []              # 持仓记录
        self.cash_reserve = self.initial_capital * self.reserve_ratio
        self.available_capital = self.initial_capital - self.cash_reserve
        
        print(f"🎯 小资金持仓策略系统已启动")
        print(f"💰 初始资本: {self.initial_capital:,.0f} 港元")
        print(f"💵 可用资金: {self.available_capital:,.0f} 港元")
        print(f"🏦 现金储备: {self.cash_reserve:,.0f} 港元")
        print(f"📊 投资标的: {self.company_name}({self.symbol})")
    
    def load_market_data(self, period="1y"):
        """加载市场数据"""
        print(f"\n📊 加载{self.symbol}市场数据...")
        
        try:
            ticker = yf.Ticker(self.symbol)
            hist_data = ticker.history(period=period)
            
            if hist_data.empty:
                raise ValueError(f"无法获取{self.symbol}的数据")
            
            self.market_data = pd.DataFrame({
                'date': hist_data.index,
                'open': hist_data['Open'].values,
                'high': hist_data['High'].values,
                'low': hist_data['Low'].values,
                'close': hist_data['Close'].values,
                'volume': hist_data['Volume'].values
            })
            
            # 计算技术指标
            self.calculate_indicators()
            
            print(f"✅ 成功加载 {len(self.market_data)} 条数据")
            print(f"   当前价格: {self.market_data['close'].iloc[-1]:.2f} 港元")
            
            return True
            
        except Exception as e:
            print(f"❌ 数据加载失败: {e}")
            return False
    
    def calculate_indicators(self):
        """计算技术指标"""
        df = self.market_data.copy()
        
        # 移动平均线
        df['ma5'] = df['close'].rolling(window=5).mean()
        df['ma10'] = df['close'].rolling(window=10).mean()
        df['ma20'] = df['close'].rolling(window=20).mean()
        
        # RSI指标
        delta = df['close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
        rs = gain / (loss + 1e-10)
        df['rsi'] = 100 - (100 / (1 + rs))
        
        # 布林带
        df['bb_middle'] = df['close'].rolling(window=20).mean()
        bb_std = df['close'].rolling(window=20).std()
        df['bb_upper'] = df['bb_middle'] + (bb_std * 2)
        df['bb_lower'] = df['bb_middle'] - (bb_std * 2)
        
        # 生成交易信号
        df['signal'] = self.generate_signals(df)
        
        self.market_data = df.fillna(0)
        print("   技术指标计算完成")
    
    def generate_signals(self, df):
        """生成交易信号（适合小资金策略）"""
        signals = []
        
        for i in range(len(df)):
            close = df['close'].iloc[i]
            ma5 = df['ma5'].iloc[i]
            ma10 = df['ma10'].iloc[i]
            rsi = df['rsi'].iloc[i]
            bb_lower = df['bb_lower'].iloc[i]
            bb_upper = df['bb_upper'].iloc[i]
            
            # 买入信号：价格接近布林带下轨 + RSI超卖 + 短期均线向上
            if (close <= bb_lower * 1.02 and rsi < 35 and ma5 > ma10):
                signals.append('强烈买入')
            elif (close < ma10 and rsi < 40 and ma5 > ma10):
                signals.append('买入')
            # 卖出信号：价格接近布林带上轨 + RSI超买
            elif (close >= bb_upper * 0.98 and rsi > 70):
                signals.append('卖出')
            elif (close > ma10 * 1.05 and rsi > 65):
                signals.append('减仓')
            else:
                signals.append('观望')
        
        return signals
    
    def calculate_position_size(self, price, signal_strength):
        """计算仓位大小"""
        if signal_strength == '强烈买入':
            base_ratio = self.position_levels[0]  # 20%
        elif signal_strength == '买入':
            base_ratio = self.position_levels[0] * 0.7  # 14%
        else:
            base_ratio = self.position_levels[0] * 0.5  # 10%
        
        # 计算可投资金额
        investment_amount = self.available_capital * base_ratio
        
        # 确保不低于最小交易金额
        if investment_amount < self.min_trade_amount:
            return 0, 0
        
        # 计算股数（向下取整）
        shares = int(investment_amount / price)
        actual_amount = shares * price
        
        return shares, actual_amount
    
    def execute_strategy(self):
        """执行持仓策略"""
        print(f"\n🎯 开始执行小资金持仓策略...")
        
        strategy_log = []
        current_position = 0
        total_shares = 0
        avg_cost = 0
        
        for i, row in self.market_data.iterrows():
            date = row['date']
            price = row['close']
            signal = row['signal']
            
            # 检查买入信号
            if signal in ['强烈买入', '买入'] and current_position < self.max_position_ratio:
                shares, amount = self.calculate_position_size(price, signal)
                
                if shares > 0 and amount <= self.available_capital:
                    # 执行买入
                    commission = amount * self.commission_rate
                    total_cost = amount + commission
                    
                    if total_cost <= self.available_capital:
                        # 更新持仓
                        if total_shares == 0:
                            avg_cost = price
                        else:
                            avg_cost = (avg_cost * total_shares + amount) / (total_shares + shares)
                        
                        total_shares += shares
                        self.available_capital -= total_cost
                        current_position = (total_shares * price) / self.initial_capital
                        
                        strategy_log.append({
                            'date': date,
                            'action': '买入',
                            'price': price,
                            'shares': shares,
                            'amount': amount,
                            'commission': commission,
                            'total_shares': total_shares,
                            'avg_cost': avg_cost,
                            'position_ratio': current_position,
                            'available_capital': self.available_capital,
                            'signal': signal
                        })
                        
                        print(f"📈 {date.strftime('%Y-%m-%d')}: {signal} - 买入{shares}股，价格{price:.2f}，仓位{current_position:.1%}")
            
            # 检查卖出信号或止盈止损
            elif total_shares > 0:
                should_sell = False
                sell_reason = ""
                sell_ratio = 0
                
                # 止盈检查
                if price >= avg_cost * (1 + self.take_profit):
                    should_sell = True
                    sell_reason = "止盈"
                    sell_ratio = 0.5  # 止盈卖出50%
                
                # 止损检查
                elif price <= avg_cost * (1 - self.stop_loss):
                    should_sell = True
                    sell_reason = "止损"
                    sell_ratio = 1.0  # 止损全部卖出
                
                # 信号卖出
                elif signal in ['卖出', '减仓']:
                    should_sell = True
                    sell_reason = signal
                    sell_ratio = 0.3 if signal == '减仓' else 0.6
                
                if should_sell:
                    sell_shares = int(total_shares * sell_ratio)
                    if sell_shares > 0:
                        sell_amount = sell_shares * price
                        commission = sell_amount * self.commission_rate
                        net_amount = sell_amount - commission
                        
                        # 计算盈亏
                        cost_basis = sell_shares * avg_cost
                        profit_loss = net_amount - cost_basis
                        
                        # 更新持仓
                        total_shares -= sell_shares
                        self.available_capital += net_amount
                        current_position = (total_shares * price) / self.initial_capital if total_shares > 0 else 0
                        
                        strategy_log.append({
                            'date': date,
                            'action': sell_reason,
                            'price': price,
                            'shares': -sell_shares,
                            'amount': sell_amount,
                            'commission': commission,
                            'total_shares': total_shares,
                            'avg_cost': avg_cost,
                            'position_ratio': current_position,
                            'available_capital': self.available_capital,
                            'profit_loss': profit_loss,
                            'signal': signal
                        })
                        
                        print(f"📉 {date.strftime('%Y-%m-%d')}: {sell_reason} - 卖出{sell_shares}股，价格{price:.2f}，盈亏{profit_loss:.2f}")
        
        self.strategy_log = pd.DataFrame(strategy_log)
        print(f"✅ 策略执行完成，共生成 {len(self.strategy_log)} 条记录")
        
        return self.strategy_log
    
    def analyze_performance(self):
        """分析策略表现"""
        if len(self.strategy_log) == 0:
            print("❌ 没有交易记录可分析")
            return
        
        print(f"\n📊 小资金持仓策略表现分析")
        print("=" * 60)
        
        # 计算最终资产
        final_price = self.market_data['close'].iloc[-1]
        current_shares = self.strategy_log['total_shares'].iloc[-1] if len(self.strategy_log) > 0 else 0
        stock_value = current_shares * final_price
        total_assets = self.available_capital + stock_value
        
        # 基本统计
        buy_trades = len(self.strategy_log[self.strategy_log['action'] == '买入'])
        sell_trades = len(self.strategy_log[self.strategy_log['action'].isin(['止盈', '止损', '卖出', '减仓'])])
        
        print(f"💰 资产状况:")
        print(f"   初始资本: {self.initial_capital:,.0f} 港元")
        print(f"   现金余额: {self.available_capital:,.2f} 港元")
        print(f"   持股价值: {stock_value:,.2f} 港元")
        print(f"   总资产: {total_assets:,.2f} 港元")
        print(f"   总收益: {total_assets - self.initial_capital:,.2f} 港元")
        print(f"   收益率: {(total_assets / self.initial_capital - 1) * 100:.2f}%")
        
        print(f"\n📈 交易统计:")
        print(f"   买入次数: {buy_trades}")
        print(f"   卖出次数: {sell_trades}")
        print(f"   当前持股: {current_shares} 股")
        
        if 'profit_loss' in self.strategy_log.columns:
            realized_pnl = self.strategy_log['profit_loss'].sum()
            print(f"   已实现盈亏: {realized_pnl:.2f} 港元")
        
        # 风险分析
        if len(self.strategy_log) > 0:
            max_position = self.strategy_log['position_ratio'].max()
            print(f"\n⚠️ 风险控制:")
            print(f"   最大仓位: {max_position:.1%}")
            print(f"   现金储备: {self.cash_reserve:,.0f} 港元")
    
    def save_strategy_report(self):
        """保存策略报告"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"小资金持仓策略报告_{timestamp}.xlsx"
        
        try:
            with pd.ExcelWriter(filename, engine='openpyxl') as writer:
                # 交易记录
                if len(self.strategy_log) > 0:
                    self.strategy_log.to_excel(writer, sheet_name='交易记录', index=False)
                
                # 策略参数
                params_df = pd.DataFrame({
                    '参数名称': ['初始资本', '最小交易金额', '最大仓位', '止损比例', '止盈比例', '现金储备比例'],
                    '参数值': [self.initial_capital, self.min_trade_amount, f"{self.max_position_ratio:.0%}", 
                              f"{self.stop_loss:.0%}", f"{self.take_profit:.0%}", f"{self.reserve_ratio:.0%}"],
                    '说明': ['投资本金', '单次最小投资额', '最大持仓比例', '止损线', '止盈线', '现金储备比例']
                })
                params_df.to_excel(writer, sheet_name='策略参数', index=False)
            
            print(f"💾 策略报告已保存到: {filename}")
            return filename
            
        except Exception as e:
            print(f"❌ 保存报告失败: {e}")
            return None

def main():
    """主函数"""
    print("🎯 小资金持仓策略系统")
    print("适用于：2500港元初始资本，1年投资期限")
    print("=" * 50)
    
    try:
        # 创建策略实例
        strategy = SmallCapitalStrategy()
        
        # 加载数据
        if not strategy.load_market_data(period="1y"):
            return None
        
        # 执行策略
        strategy.execute_strategy()
        
        # 分析表现
        strategy.analyze_performance()
        
        # 保存报告
        strategy.save_strategy_report()
        
        print(f"\n✅ 小资金持仓策略分析完成")
        return strategy
        
    except Exception as e:
        print(f"\n❌ 策略执行失败: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    strategy = main()
