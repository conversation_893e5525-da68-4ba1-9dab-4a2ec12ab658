#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
验证EAB_0023HK数据库更新
======================

检查今日数据是否成功更新到数据库

作者: Cosmoon NG
日期: 2025年7月24日
"""

import mysql.connector
from datetime import datetime, date

def verify_eab_update():
    """验证EAB数据库更新"""
    
    config = {
        'host': 'localhost',
        'user': 'root',
        'password': '12345678',
        'database': 'finance',
        'charset': 'utf8mb4'
    }
    
    try:
        conn = mysql.connector.connect(**config)
        cursor = conn.cursor()
        
        print("🔍 验证EAB_0023HK数据库更新")
        print("=" * 50)
        
        # 1. 检查今日数据
        today = date.today()
        print(f"\n📅 检查今日数据 ({today})...")
        
        cursor.execute("""
            SELECT 
                Date, Close, Volume, MoneyFlowRatio, MFI, 
                Y_Value, X_Value, E_Value, RSI, TradingSignal, i
            FROM eab_0023hk 
            WHERE Date = %s
        """, (today,))
        
        today_data = cursor.fetchone()
        
        if today_data:
            print("✅ 今日数据已成功更新!")
            print(f"   📊 日期: {today_data[0]}")
            print(f"   💰 收盘价: {today_data[1]:.2f} 港元")
            print(f"   📈 成交量: {today_data[2]:,}")
            print(f"   💹 MoneyFlowRatio: {today_data[3]:.4f}")
            print(f"   📊 MFI: {today_data[4]:.2f}")
            print(f"   🎯 Y值: {today_data[5]:.4f}")
            print(f"   🎯 X值: {today_data[6]:.4f}")
            print(f"   ⚡ E值: {today_data[7]:.4f}")
            print(f"   📈 RSI: {today_data[8]:.2f}")
            
            signal_text = "做多" if today_data[9] == 1 else "做空" if today_data[9] == -1 else "观望"
            print(f"   🚦 交易信号: {signal_text}")
            print(f"   🔢 记录编号: {today_data[10]}")
        else:
            print("❌ 今日数据未找到")
        
        # 2. 检查最近5天数据
        print(f"\n📊 最近5天数据...")
        cursor.execute("""
            SELECT 
                Date, Close, MoneyFlowRatio, Y_Value, X_Value, E_Value, TradingSignal
            FROM eab_0023hk 
            ORDER BY Date DESC 
            LIMIT 5
        """)
        
        recent_data = cursor.fetchall()
        
        print("   日期       | 收盘价  | MFR    | Y值    | X值    | E值    | 信号")
        print("   " + "-" * 65)
        
        for row in recent_data:
            signal_text = "做多" if row[6] == 1 else "做空" if row[6] == -1 else "观望"
            print(f"   {row[0]} | {row[1]:6.2f} | {row[2]:6.4f} | {row[3]:6.4f} | {row[4]:6.4f} | {row[5]:6.4f} | {signal_text}")
        
        # 3. 检查数据统计
        print(f"\n📈 数据统计...")
        cursor.execute("""
            SELECT 
                COUNT(*) as total_records,
                MIN(Date) as earliest_date,
                MAX(Date) as latest_date,
                AVG(Close) as avg_close,
                MAX(Close) as max_close,
                MIN(Close) as min_close
            FROM eab_0023hk
        """)
        
        stats = cursor.fetchone()
        
        print(f"   📊 总记录数: {stats[0]:,}")
        print(f"   📅 数据范围: {stats[1]} 至 {stats[2]}")
        print(f"   💰 平均收盘价: {stats[3]:.2f} 港元")
        print(f"   📈 最高价: {stats[4]:.2f} 港元")
        print(f"   📉 最低价: {stats[5]:.2f} 港元")
        
        # 4. 测试修复后的存储过程
        print(f"\n🔧 测试存储过程...")
        try:
            cursor.execute("CALL sp_combined_stock_analysis()")
            results = cursor.fetchall()
            print(f"   ✅ sp_combined_stock_analysis() 执行成功，返回 {len(results)} 条记录")
            
            if results:
                latest_combined = results[0]
                print(f"   📊 最新综合数据: HSI收盘 {latest_combined[1]:.2f}, EAB收盘 {latest_combined[5]:.2f}")
                
        except mysql.connector.Error as e:
            print(f"   ❌ 存储过程执行失败: {e}")
        
        # 5. 检查交易信号分布
        print(f"\n🚦 交易信号分布 (最近30天)...")
        cursor.execute("""
            SELECT 
                TradingSignal,
                COUNT(*) as count,
                AVG(Close) as avg_price
            FROM eab_0023hk 
            WHERE Date >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
            GROUP BY TradingSignal
            ORDER BY TradingSignal
        """)
        
        signal_stats = cursor.fetchall()
        
        for row in signal_stats:
            signal_name = "做多" if row[0] == 1 else "做空" if row[0] == -1 else "观望"
            print(f"   {signal_name}: {row[1]} 天, 平均价格 {row[2]:.2f} 港元")
        
        cursor.close()
        conn.close()
        
        print(f"\n🎉 EAB_0023HK数据库更新验证完成!")
        return True
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False

def call_fixed_stored_procedure():
    """调用修复后的存储过程"""
    
    config = {
        'host': 'localhost',
        'user': 'root',
        'password': '12345678',
        'database': 'finance',
        'charset': 'utf8mb4'
    }
    
    try:
        conn = mysql.connector.connect(**config)
        cursor = conn.cursor()
        
        print("\n🔧 调用修复后的存储过程...")
        
        # 调用无参数版本的存储过程
        cursor.execute("CALL sp_combined_stock_analysis()")
        results = cursor.fetchall()
        
        print(f"✅ 存储过程执行成功，返回 {len(results)} 条记录")
        
        if results:
            print("\n📊 最新综合分析数据:")
            latest = results[0]
            print(f"   📅 日期: {latest[0]}")
            print(f"   🏦 HSI收盘价: {latest[1]:.2f}")
            print(f"   🏦 HSI MoneyFlowRatio: {latest[2]:.4f}")
            print(f"   🏦 EAB收盘价: {latest[5]:.2f}")
            print(f"   🏦 EAB MoneyFlowRatio: {latest[6]:.4f}")
            print(f"   📊 EAB MFI: {latest[7]:.2f}")
            print(f"   🎯 EAB Y值: {latest[8]:.4f}")
            print(f"   🎯 EAB X值: {latest[9]:.4f}")
            print(f"   ⚡ EAB E值: {latest[10]:.4f}")
            
            signal_text = "做多" if latest[11] == 1 else "做空" if latest[11] == -1 else "观望"
            print(f"   🚦 EAB信号: {signal_text}")
        
        cursor.close()
        conn.close()
        
        return True
        
    except Exception as e:
        print(f"❌ 存储过程调用失败: {e}")
        return False

def main():
    """主函数"""
    print("🔍 EAB_0023HK数据库更新验证")
    print("=" * 40)
    
    # 验证数据库更新
    if verify_eab_update():
        print("\n✅ 数据库更新验证成功")
    else:
        print("\n❌ 数据库更新验证失败")
    
    # 调用存储过程
    if call_fixed_stored_procedure():
        print("\n✅ 存储过程调用成功")
    else:
        print("\n❌ 存储过程调用失败")

if __name__ == "__main__":
    main()
