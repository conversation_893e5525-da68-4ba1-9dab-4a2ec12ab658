#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试数据库XYE信号功能
"""

from Signals import (
    get_database_xye_data, 
    get_xye_signal, 
    get_enhanced_signal_with_kelly,
    format_signal_with_kelly,
    format_xye_signal_with_rules
)

def test_database_connection():
    """测试数据库连接和XYE数据获取"""
    print("🔍 测试数据库XYE信号功能")
    print("=" * 50)
    
    # 1. 测试数据库连接
    print("1. 测试数据库连接...")
    db_data = get_database_xye_data()
    
    print(f"📊 数据来源: {db_data.get('data_source', 'unknown')}")
    print(f"📅 数据日期: {db_data.get('date', 'unknown')}")
    print(f"💰 收盘价: {db_data.get('close', 0):.2f} 港元")
    print(f"📈 XYE值: X={db_data.get('X_Value', 0):.4f}, Y={db_data.get('Y_Value', 0):.4f}, E={db_data.get('E_Value', 0):.4f}")
    print(f"📊 技术指标: RSI={db_data.get('RSI', 0):.1f}, MFI={db_data.get('MFI', 0):.1f}")
    
    # 2. 测试XYE信号（使用数据库数据）
    print("\n2. 测试XYE信号（数据库数据）...")
    xye_signal = get_xye_signal(use_database=True)
    print(format_xye_signal_with_rules(xye_signal))
    
    # 3. 测试增强信号 + 凯利公式
    print("3. 测试增强信号 + 凯利公式（数据库数据）...")
    enhanced_signal = get_enhanced_signal_with_kelly(use_database=True)
    print(format_signal_with_kelly(enhanced_signal))
    
    # 4. 对比默认数据和数据库数据
    print("4. 对比测试...")
    print("使用默认数据的XYE信号:")
    default_signal = get_xye_signal(use_database=False)
    print(f"   信号: {default_signal.get('signal', '未知')}")
    print(f"   数据来源: {default_signal.get('data_source', '未知')}")
    
    print("使用数据库数据的XYE信号:")
    db_signal = get_xye_signal(use_database=True)
    print(f"   信号: {db_signal.get('signal', '未知')}")
    print(f"   数据来源: {db_signal.get('data_source', '未知')}")
    
    # 5. 显示XYE值对比
    print(f"\n📊 XYE值对比:")
    default_xye = default_signal.get('xye_values', {})
    db_xye = db_signal.get('xye_values', {})
    
    print(f"   默认数据: X={default_xye.get('X', 0):.4f}, Y={default_xye.get('Y', 0):.4f}, E={default_xye.get('E', 0):.4f}")
    print(f"   数据库数据: X={db_xye.get('X', 0):.4f}, Y={db_xye.get('Y', 0):.4f}, E={db_xye.get('E', 0):.4f}")
    
    # 6. 结论
    print(f"\n🏆 测试结论:")
    if db_data.get('data_source') == 'database':
        print("✅ 数据库连接成功，XYE信号使用实时数据库数据")
    else:
        print("⚠️ 数据库连接失败，XYE信号使用默认数据")
    
    if default_signal.get('signal') != db_signal.get('signal'):
        print("📊 数据库数据与默认数据产生了不同的交易信号")
    else:
        print("📊 数据库数据与默认数据产生了相同的交易信号")

def test_specific_table():
    """测试指定数据库表"""
    print(f"\n🔍 测试指定数据库表...")
    
    # 测试不同的表
    tables = ['eab_0023hk', 'hkhsi50', 'hk2800']
    
    for table in tables:
        print(f"\n📊 测试表: {table}")
        try:
            data = get_database_xye_data(table)
            if data.get('data_source') == 'database':
                print(f"   ✅ 成功获取数据: X={data.get('X_Value', 0):.4f}, Y={data.get('Y_Value', 0):.4f}, E={data.get('E_Value', 0):.4f}")
            else:
                print(f"   ❌ 获取失败，使用默认数据")
        except Exception as e:
            print(f"   ❌ 错误: {e}")

if __name__ == "__main__":
    test_database_connection()
    test_specific_table()
