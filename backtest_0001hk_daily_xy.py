#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
长江和记实业0001.HK - 使用当天X和Y值的分段策略
===============================================

使用当天数据计算X和Y值，测试您的分段规则:
- 观望: 0.333 < y < 0.4
- 做多: x>=0.5且y>=0.5 或 x<0.25且y<0.25
- 做空: x>0.45且y<0.35 或 x<0.45且y>0.35

作者: Cosmoon NG
"""

import yfinance as yf
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

class DailyXYStrategy0001HK:
    def __init__(self):
        """初始化策略"""
        self.symbol = "0001.HK"
        self.initial_capital = 10000.00
        self.monthly_addition = 3000.00

        # 止盈止损参数
        self.take_profit_long = 0.012
        self.stop_loss_long = 0.006
        self.take_profit_short = 0.012
        self.stop_loss_short = 0.006

        # 交易状态
        self.position = 0
        self.current_price = 0
        self.trades = []
        self.equity_curve = []

    def load_data(self):
        """加载数据"""
        print(f"📊 加载{self.symbol}历史数据...")

        try:
            end_date = datetime.now()
            start_date = end_date - timedelta(days=25*365)

            ticker = yf.Ticker(self.symbol)
            hist_data = ticker.history(start=start_date, end=end_date)

            self.df = pd.DataFrame({
                'date': hist_data.index,
                'open': hist_data['Open'],
                'high': hist_data['High'],
                'low': hist_data['Low'],
                'close': hist_data['Close'],
                'volume': hist_data['Volume']
            })

            self.df = self.df.dropna().sort_values('date').reset_index(drop=True)

            print(f"   ✅ 成功获取 {len(self.df)} 条数据")
            print(f"   📈 价格范围: {self.df['close'].min():.2f} - {self.df['close'].max():.2f} 港元")

            return True

        except Exception as e:
            print(f"   ❌ 数据加载失败: {e}")
            return False

    def calculate_daily_indicators(self):
        """计算当天的X和Y指标"""
        print(f"\n🧮 计算当天X和Y指标...")

        # 计算一些基础指标用于当天计算
        self.df['typical_price'] = (self.df['high'] + self.df['low'] + self.df['close']) / 3
        self.df['volume_ma5'] = self.df['volume'].rolling(5).mean()  # 短期成交量均线用于对比

        # 为每一天计算当天的X和Y值
        daily_x = []
        daily_y = []

        for i in range(len(self.df)):
            row = self.df.iloc[i]

            # === 当天Y值计算 ===
            # Y = 当天价格在当天高低区间的位置
            if row['high'] != row['low']:
                y_today = (row['close'] - row['low']) / (row['high'] - row['low'])
            else:
                y_today = 0.5

            y_today = max(0.1, min(0.9, y_today))

            # === 当天X值计算 (官方公式) ===

            # 1. MFI组成部分 (40%权重) - 当天简化版
            # 当天资金流向 = (收盘-开盘)/开盘 * 成交量
            if row['open'] > 0:
                price_change_ratio = (row['close'] - row['open']) / row['open']
            else:
                price_change_ratio = 0

            # 当天MFI简化: 基于当天价格变化方向
            if price_change_ratio > 0:
                mfi_today = 50 + min(price_change_ratio * 1000, 40)  # 50-90
            else:
                mfi_today = 50 + max(price_change_ratio * 1000, -40)  # 10-50

            mfi_component = (mfi_today / 100) * 0.40

            # 2. Price_vs_VWAP组成部分 (25%权重) - 当天简化版
            # 当天VWAP = 典型价格 (简化)
            vwap_today = row['typical_price']
            price_vs_vwap = row['close'] / vwap_today if vwap_today > 0 else 1.0
            vwap_component = max(0, min(1, (price_vs_vwap - 0.98) / 0.04)) * 0.25

            # 3. Relative_Volume组成部分 (20%权重) - 当天简化版
            # 相对成交量 = 当天成交量 / 近期平均成交量
            if i >= 5 and self.df.iloc[i]['volume_ma5'] > 0:
                relative_volume = row['volume'] / self.df.iloc[i]['volume_ma5']
            else:
                relative_volume = 1.0

            volume_component = max(0, min(1, (relative_volume - 0.5) / 1.5)) * 0.20

            # 4. Price_Percentile组成部分 (15%权重) - 就是Y值
            percentile_component = y_today * 0.15

            # 合成当天X值
            x_today = mfi_component + vwap_component + volume_component + percentile_component
            x_today = max(0.1, min(0.9, x_today))

            daily_x.append(x_today)
            daily_y.append(y_today)

        self.df['x_daily'] = daily_x
        self.df['y_daily'] = daily_y

        # 计算E值 (用于参考)
        self.df['e_daily'] = (8 * self.df['x_daily'] * self.df['y_daily'] -
                             3 * self.df['x_daily'] - 3 * self.df['y_daily'] + 1)

        print(f"   ✅ 当天指标计算完成")
        print(f"   📊 当天X值范围: {self.df['x_daily'].min():.3f} - {self.df['x_daily'].max():.3f}")
        print(f"   📊 当天Y值范围: {self.df['y_daily'].min():.3f} - {self.df['y_daily'].max():.3f}")
        print(f"   📊 当天E值范围: {self.df['e_daily'].min():.3f} - {self.df['e_daily'].max():.3f}")

    def get_trading_signal(self, x_val, y_val):
        """根据您的分段规则获取交易信号"""

        # 观望条件 (优先级最高)
        if 0.333 < y_val < 0.4:
            return "HOLD"   # 观望: 0.333 < y < 0.4

        # 做多条件
        if x_val >= 0.5 and y_val >= 0.5:
            return "LONG"   # x>=0.5 且 y>=0.5, 做多
        elif x_val < 0.25 and y_val < 0.25:
            return "LONG"   # x<0.25 且 y<0.25, 做多

        # 做空条件
        if x_val > 0.45 and y_val < 0.35:
            return "SHORT"  # x>0.45 且 y<0.35, 做空
        elif x_val < 0.45 and y_val > 0.35:
            return "SHORT"  # x<0.45 且 y>0.35, 做空

        return "HOLD"  # 其他情况持有

    def add_monthly_capital(self, date, capital):
        """每月增加资金"""
        last_month = getattr(self, 'last_month', None)
        current_month = date.replace(day=1)

        if last_month is None or current_month > last_month:
            self.last_month = current_month
            return capital + self.monthly_addition

        return capital

    def run_backtest(self):
        """运行回测"""
        print(f"\n💼 开始回测 (当天XY分段策略)...")

        capital = self.initial_capital
        signal_stats = {"LONG": 0, "SHORT": 0, "HOLD": 0}

        for i in range(60, len(self.df)):
            row = self.df.iloc[i]
            date = row['date']
            current_price = row['close']

            # 每月增加资金
            capital = self.add_monthly_capital(date, capital)

            # 获取当天交易信号
            signal = self.get_trading_signal(row['x_daily'], row['y_daily'])
            signal_stats[signal] += 1

            # 记录权益
            self.equity_curve.append({
                'date': date,
                'equity': capital,
                'position': self.position,
                'signal': signal,
                'x_value': row['x_daily'],
                'y_value': row['y_daily'],
                'e_value': row['e_daily']
            })

            # 观望信号 - 强制平仓
            if signal == "HOLD" and self.position != 0:
                if self.position == 1:  # 平多头仓
                    profit = (current_price - self.current_price) / self.current_price * capital
                    capital += profit
                    self.trades.append({
                        'date': date,
                        'type': 'long_exit_hold',
                        'entry_price': self.current_price,
                        'exit_price': current_price,
                        'profit': profit,
                        'capital': capital
                    })
                elif self.position == -1:  # 平空头仓
                    profit = (self.current_price - current_price) / self.current_price * capital
                    capital += profit
                    self.trades.append({
                        'date': date,
                        'type': 'short_exit_hold',
                        'entry_price': self.current_price,
                        'exit_price': current_price,
                        'profit': profit,
                        'capital': capital
                    })

                self.position = 0

            # 检查止盈止损 (基于当天最高最低价)
            if self.position != 0:
                if self.position == 1:  # 多头
                    profit_ratio = (row['high'] - self.current_price) / self.current_price
                    loss_ratio = (self.current_price - row['low']) / self.current_price

                    if profit_ratio >= self.take_profit_long:  # 止盈
                        exit_price = self.current_price * (1 + self.take_profit_long)
                        profit = (exit_price - self.current_price) / self.current_price * capital
                        capital += profit
                        self.position = 0
                        self.trades.append({
                            'date': date,
                            'type': 'long_exit_tp',
                            'entry_price': self.current_price,
                            'exit_price': exit_price,
                            'profit': profit,
                            'capital': capital
                        })
                    elif loss_ratio >= self.stop_loss_long:  # 止损
                        exit_price = self.current_price * (1 - self.stop_loss_long)
                        loss = (exit_price - self.current_price) / self.current_price * capital
                        capital += loss
                        self.position = 0
                        self.trades.append({
                            'date': date,
                            'type': 'long_exit_sl',
                            'entry_price': self.current_price,
                            'exit_price': exit_price,
                            'profit': loss,
                            'capital': capital
                        })

                elif self.position == -1:  # 空头
                    profit_ratio = (self.current_price - row['low']) / self.current_price
                    loss_ratio = (row['high'] - self.current_price) / self.current_price

                    if profit_ratio >= self.take_profit_short:  # 止盈
                        exit_price = self.current_price * (1 - self.take_profit_short)
                        profit = (self.current_price - exit_price) / self.current_price * capital
                        capital += profit
                        self.position = 0
                        self.trades.append({
                            'date': date,
                            'type': 'short_exit_tp',
                            'entry_price': self.current_price,
                            'exit_price': exit_price,
                            'profit': profit,
                            'capital': capital
                        })
                    elif loss_ratio >= self.stop_loss_short:  # 止损
                        exit_price = self.current_price * (1 + self.stop_loss_short)
                        loss = (exit_price - self.current_price) / self.current_price * capital * -1
                        capital += loss
                        self.position = 0
                        self.trades.append({
                            'date': date,
                            'type': 'short_exit_sl',
                            'entry_price': self.current_price,
                            'exit_price': exit_price,
                            'profit': loss,
                            'capital': capital
                        })

            # 开仓信号判断
            if self.position == 0:
                if signal == "LONG":
                    self.position = 1
                    self.current_price = current_price
                    self.trades.append({
                        'date': date,
                        'type': 'long_entry',
                        'price': self.current_price,
                        'capital': capital,
                        'signal': f"X={row['x_daily']:.3f},Y={row['y_daily']:.3f},E={row['e_daily']:.3f}"
                    })

                elif signal == "SHORT":
                    self.position = -1
                    self.current_price = current_price
                    self.trades.append({
                        'date': date,
                        'type': 'short_entry',
                        'price': self.current_price,
                        'capital': capital,
                        'signal': f"X={row['x_daily']:.3f},Y={row['y_daily']:.3f},E={row['e_daily']:.3f}"
                    })

        self.final_capital = capital
        self.signal_stats = signal_stats
        print(f"   ✅ 回测完成！最终资金：{self.final_capital:,.2f}")

    def analyze_results(self):
        """分析回测结果"""
        print(f"\n📈 当天XY分段策略回测结果")
        print("=" * 60)

        # 基础统计
        total_days = (self.df['date'].max() - self.df['date'].min()).days
        total_years = total_days / 365

        print(f"📊 基础信息:")
        print(f"   标的: {self.symbol} (长江和记实业)")
        print(f"   回测期间: {total_years:.1f}年")
        print(f"   策略: 当天XY分段策略")

        # 价格表现
        start_price = self.df['close'].iloc[0]
        end_price = self.df['close'].iloc[-1]
        price_return = (end_price - start_price) / start_price * 100

        print(f"\n💰 价格表现:")
        print(f"   起始价格: {start_price:.2f}港元")
        print(f"   最终价格: {end_price:.2f}港元")
        print(f"   价格涨幅: {price_return:.2f}%")

        # 策略表现
        months_passed = int(total_days / 30)
        total_invested = self.initial_capital + months_passed * self.monthly_addition
        net_profit = self.final_capital - total_invested
        total_return = net_profit / total_invested * 100
        annual_return = ((self.final_capital / total_invested) ** (1/total_years) - 1) * 100

        print(f"\n🚀 策略表现:")
        print(f"   总投入: {total_invested:,.0f}港元")
        print(f"   最终资金: {self.final_capital:,.0f}港元")
        print(f"   净收益: {net_profit:,.0f}港元")
        print(f"   总收益率: {total_return:.2f}%")
        print(f"   年化收益率: {annual_return:.2f}%")

        # 信号分布统计
        total_signals = sum(self.signal_stats.values())
        print(f"\n📊 信号分布:")
        for signal, count in self.signal_stats.items():
            percentage = count / total_signals * 100
            print(f"   {signal}: {count}次 ({percentage:.1f}%)")

        # 交易统计
        trades_df = pd.DataFrame(self.trades)
        if len(trades_df) > 0:
            entry_trades = trades_df[trades_df['type'].str.contains('entry')]
            exit_trades = trades_df[trades_df['type'].str.contains('exit')]
            profit_trades = exit_trades[exit_trades['profit'] > 0]

            print(f"\n📋 交易统计:")
            print(f"   总交易次数: {len(entry_trades)}")
            print(f"   盈利交易: {len(profit_trades)}")
            print(f"   亏损交易: {len(exit_trades) - len(profit_trades)}")
            print(f"   胜率: {len(profit_trades)/len(exit_trades)*100:.2f}%" if len(exit_trades) > 0 else "   胜率: 0%")

            if len(profit_trades) > 0:
                avg_profit = profit_trades['profit'].mean()
                print(f"   平均盈利: {avg_profit:,.0f}港元")

            loss_trades = exit_trades[exit_trades['profit'] <= 0]
            if len(loss_trades) > 0:
                avg_loss = loss_trades['profit'].mean()
                print(f"   平均亏损: {avg_loss:,.0f}港元")

        # 买入持有对比
        buy_hold_shares = int(self.initial_capital / start_price)
        buy_hold_final = buy_hold_shares * end_price
        buy_hold_return = (buy_hold_final - self.initial_capital) / self.initial_capital * 100

        print(f"\n🆚 买入持有对比:")
        print(f"   买入持有收益: {buy_hold_return:.2f}%")
        print(f"   策略收益: {total_return:.2f}%")
        print(f"   策略优势: {total_return - buy_hold_return:.2f}%")

        # 当天指标分析
        print(f"\n🔍 当天指标分析:")
        print(f"   当天X值平均: {self.df['x_daily'].mean():.3f}")
        print(f"   当天Y值平均: {self.df['y_daily'].mean():.3f}")
        print(f"   当天E值平均: {self.df['e_daily'].mean():.3f}")

        # 条件触发分析
        long_entries = trades_df[trades_df['type'] == 'long_entry']
        short_entries = trades_df[trades_df['type'] == 'short_entry']

        if len(long_entries) > 0:
            print(f"   多头开仓: {len(long_entries)}次")
            print(f"   多头示例: {long_entries.iloc[0]['signal'] if len(long_entries) > 0 else 'N/A'}")

        if len(short_entries) > 0:
            print(f"   空头开仓: {len(short_entries)}次")
            print(f"   空头示例: {short_entries.iloc[0]['signal'] if len(short_entries) > 0 else 'N/A'}")

def main():
    """主函数"""
    print("🎯 长江和记实业0001.HK - 当天XY分段策略测试")
    print("=" * 60)
    print("📋 使用当天数据的分段规则:")
    print("   📊 X值 = 当天MFI(40%) + 当天VWAP(25%) + 当天成交量(20%) + 当天价格位置(15%)")
    print("   📊 Y值 = 当天价格在当天高低区间的位置")
    print("   🔄 观望: 0.333 < y < 0.4")
    print("   📈 做多: x>=0.5且y>=0.5 或 x<0.25且y<0.25")
    print("   📉 做空: x>0.45且y<0.35 或 x<0.45且y>0.35")
    print("   ⚠️  观望时强制平仓，不持任何仓位")

    backtest = DailyXYStrategy0001HK()

    if not backtest.load_data():
        return

    backtest.calculate_daily_indicators()
    backtest.run_backtest()
    backtest.analyze_results()

    print(f"\n🎉 当天XY分段策略测试完成!")
    print(f"📊 这验证了您的分段规则在当天数据下的表现")
    print(f"💡 当天计算避免了未来数据泄露，更真实")

if __name__ == "__main__":
    main()
