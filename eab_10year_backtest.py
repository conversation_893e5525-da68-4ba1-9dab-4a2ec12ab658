#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
EAB_0023HK 10年历史回测系统
初始资本: 2500港币
回测期间: 2015-2025 (10年)
"""

import pandas as pd
import numpy as np
import mysql.connector
from datetime import datetime, timedelta
import matplotlib.pyplot as plt
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class EAB10YearBacktest:
    def __init__(self):
        """初始化10年回测系统"""
        self.initial_capital = 2500      # 初始资本2500港币
        self.transaction_cost = 0.001    # 0.1%交易成本
        self.position_ratio = 0.8        # 80%仓位比例
        self.max_holding_days = 3        # 最大持仓3天

        # 复利参数
        self.compound_enabled = True
        self.min_position_size = 100     # 最小100股

        # 当前状态
        self.position = 0                # 0=空仓, 1=多头, -1=空头
        self.position_size = 0
        self.entry_price = 0
        self.entry_date = None
        self.current_capital = self.initial_capital

        # 记录
        self.trades = []
        self.daily_equity = []
        self.monthly_stats = []
        self.yearly_stats = []

    def load_10year_data(self):
        """加载10年历史数据"""
        print("📊 加载EAB_0023HK 10年历史数据...")
        print(f"💰 初始资本: {self.initial_capital:,} 港币")

        try:
            config = {
                'host': 'localhost',
                'port': 3306,
                'user': 'root',
                'password': '12345678',
                'database': 'finance',
                'charset': 'utf8mb4'
            }

            conn = mysql.connector.connect(**config)
            cursor = conn.cursor()

            # 查询10年数据 (2015-2025)
            query = """
            SELECT
                Date, Close, High, Low, Volume,
                Y_Value, X_Value, E_Value,
                Full_Y, E as E2, Controller,
                RSI, MFI, midprice
            FROM eab_0023hk
            WHERE Date >= '2015-01-01'
            AND Date <= '2025-12-31'
            AND Close IS NOT NULL
            ORDER BY Date ASC
            """

            cursor.execute(query)
            results = cursor.fetchall()

            if not results:
                print("❌ 没有找到10年历史数据")
                return False

            columns = ['date', 'close', 'high', 'low', 'volume',
                      'y_value', 'x_value', 'e_value',
                      'full_y', 'e2', 'controller',
                      'rsi', 'mfi', 'midprice']

            self.df = pd.DataFrame(results, columns=columns)
            self.df['date'] = pd.to_datetime(self.df['date'])

            # 转换数值列
            numeric_cols = ['close', 'high', 'low', 'volume', 'y_value', 'x_value',
                           'e_value', 'full_y', 'e2', 'controller', 'rsi', 'mfi', 'midprice']

            for col in numeric_cols:
                if col in self.df.columns:
                    self.df[col] = pd.to_numeric(self.df[col], errors='coerce')

            cursor.close()
            conn.close()

            # 数据统计
            start_date = self.df['date'].min()
            end_date = self.df['date'].max()
            total_days = (end_date - start_date).days

            print(f"✅ 成功加载 {len(self.df):,} 条数据")
            print(f"📅 时间范围: {start_date.date()} 到 {end_date.date()}")
            print(f"⏰ 总天数: {total_days:,} 天 ({total_days/365:.1f} 年)")
            print(f"💰 价格范围: {self.df['close'].min():.2f} - {self.df['close'].max():.2f} 港币")
            print(f"📈 期间涨幅: {(self.df['close'].iloc[-1]/self.df['close'].iloc[0]-1)*100:+.1f}%")

            return True

        except Exception as e:
            print(f"❌ 数据加载失败: {e}")
            return False

    def calculate_compound_position(self, capital, price):
        """复利仓位计算"""
        if not self.compound_enabled:
            return 200  # 固定200股 (适合2500资本)

        # 复利计算: 80%资金投入
        available_capital = capital * self.position_ratio
        position_size = int(available_capital / price)

        # 确保最小仓位
        return max(position_size, self.min_position_size)

    def generate_dual_xye_signal(self, row):
        """双XYE系统信号生成"""
        # 系统1: 基于Y_Value, X_Value, E_Value (价格位置系统)
        y1, x1, e1 = row['y_value'], row['x_value'], row['e_value']

        # 系统2: 基于Full_Y, E2, Controller (历史统计系统)
        y2, e2, controller = row['full_y'], row['e2'], row['controller']

        # 计算价格偏离回归线
        price_deviation = 0
        if pd.notna(row['midprice']) and row['midprice'] > 0:
            price_deviation = (row['close'] - row['midprice']) / row['midprice']

        # 系统1信号判断 (放宽条件)
        signal1 = "观望"
        strength1 = 0
        if pd.notna(e1) and pd.notna(y1):
            if e1 > 0.02 and y1 > 0.6:  # 强买入 (降低阈值)
                signal1 = "买入"
                strength1 = 3
            elif e1 > -0.02 and y1 > 0.5:   # 弱买入
                signal1 = "买入"
                strength1 = 1
            elif e1 < -0.02 and y1 < 0.4:  # 强卖出
                signal1 = "卖出"
                strength1 = 3
            elif e1 < 0.02 and y1 < 0.5:   # 弱卖出
                signal1 = "卖出"
                strength1 = 1

        # 系统2信号判断 (放宽条件)
        signal2 = "观望"
        strength2 = 0
        if pd.notna(controller):
            if controller == 1:  # 买入信号
                if price_deviation < -0.05:  # 价格低于回归线
                    signal2 = "买入"
                    strength2 = 3
                elif price_deviation < 0:
                    signal2 = "买入"
                    strength2 = 1
            elif controller == 0:  # 中性信号
                if price_deviation > 0.05:  # 价格高于回归线
                    signal2 = "卖出"
                    strength2 = 2
                elif price_deviation > 0:
                    signal2 = "卖出"
                    strength2 = 1
            elif controller == -1:  # 明确卖出信号
                signal2 = "卖出"
                strength2 = 3

        # 综合信号强度计算
        total_strength = 0
        final_signal = "观望"

        if signal1 == "买入" and signal2 == "买入":
            final_signal = "买入"
            total_strength = strength1 + strength2
        elif signal1 == "买入" and signal2 == "观望":
            final_signal = "买入"
            total_strength = strength1
        elif signal1 == "观望" and signal2 == "买入":
            final_signal = "买入"
            total_strength = strength2
        elif signal1 == "卖出" and signal2 == "卖出":
            final_signal = "卖出"
            total_strength = strength1 + strength2
        elif signal1 == "卖出" and signal2 == "观望":
            final_signal = "卖出"
            total_strength = strength1
        elif signal1 == "观望" and signal2 == "卖出":
            final_signal = "卖出"
            total_strength = strength2

        return {
            'signal': final_signal,
            'strength': min(total_strength, 5),  # 最大强度5
            'system1': signal1,
            'system2': signal2,
            'deviation': price_deviation
        }

    def check_exit_conditions(self, row, signal_info):
        """检查平仓条件"""
        if self.position == 0:
            return False, ""

        current_price = row['close']

        # 1. 时间止损 - 最大持仓天数
        if self.entry_date:
            holding_days = (row['date'] - self.entry_date).days
            if holding_days >= self.max_holding_days:
                return True, f"时间止损({holding_days}天)"

        # 2. 盈亏止损
        if self.position == 1:  # 多头持仓
            pnl_ratio = (current_price - self.entry_price) / self.entry_price
            if pnl_ratio >= 0.03:  # 3%止盈
                return True, "止盈平仓"
            elif pnl_ratio <= -0.02:  # 2%止损
                return True, "止损平仓"
        else:  # 空头持仓
            pnl_ratio = (self.entry_price - current_price) / self.entry_price
            if pnl_ratio >= 0.03:  # 3%止盈
                return True, "止盈平仓"
            elif pnl_ratio <= -0.02:  # 2%止损
                return True, "止损平仓"

        # 3. 信号反转平仓
        if signal_info['strength'] >= 3:
            if (self.position == 1 and signal_info['signal'] == "卖出") or \
               (self.position == -1 and signal_info['signal'] == "买入"):
                return True, "信号反转平仓"

        return False, ""

    def execute_trade(self, row, action, reason=""):
        """执行交易"""
        current_price = row['close']

        if action == "开多仓":
            # 计算复利仓位
            self.position_size = self.calculate_compound_position(self.current_capital, current_price)
            trade_amount = self.position_size * current_price
            trade_cost = trade_amount * self.transaction_cost

            # 检查资金是否足够
            total_cost = trade_amount + trade_cost
            if total_cost > self.current_capital:
                return  # 资金不足，跳过交易

            self.current_capital -= total_cost
            self.position = 1
            self.entry_price = current_price
            self.entry_date = row['date']

            self.trades.append({
                'date': row['date'],
                'action': '开多仓',
                'price': current_price,
                'size': self.position_size,
                'amount': trade_amount,
                'cost': trade_cost,
                'capital_after': self.current_capital,
                'reason': reason
            })

        elif action == "开空仓":
            # 计算复利仓位
            self.position_size = self.calculate_compound_position(self.current_capital, current_price)
            trade_amount = self.position_size * current_price
            trade_cost = trade_amount * self.transaction_cost

            self.current_capital += (trade_amount - trade_cost)
            self.position = -1
            self.entry_price = current_price
            self.entry_date = row['date']

            self.trades.append({
                'date': row['date'],
                'action': '开空仓',
                'price': current_price,
                'size': self.position_size,
                'amount': trade_amount,
                'cost': trade_cost,
                'capital_after': self.current_capital,
                'reason': reason
            })

        elif action == "平仓":
            trade_amount = self.position_size * current_price
            trade_cost = trade_amount * self.transaction_cost

            # 计算盈亏
            if self.position == 1:  # 平多仓
                pnl = (current_price - self.entry_price) * self.position_size
                self.current_capital += (trade_amount - trade_cost)
            else:  # 平空仓
                pnl = (self.entry_price - current_price) * self.position_size
                self.current_capital -= (trade_amount + trade_cost)

            # 扣除开仓时的交易成本
            net_pnl = pnl - (self.position_size * self.entry_price * self.transaction_cost)

            self.trades.append({
                'date': row['date'],
                'action': f'平{"多" if self.position == 1 else "空"}仓',
                'price': current_price,
                'size': self.position_size,
                'amount': trade_amount,
                'cost': trade_cost,
                'pnl': net_pnl,
                'capital_after': self.current_capital,
                'reason': reason
            })

            # 重置持仓状态
            self.position = 0
            self.position_size = 0
            self.entry_price = 0
            self.entry_date = None

    def run_10year_backtest(self):
        """运行10年回测"""
        print("\n🚀 开始10年历史回测...")
        print("📋 回测参数:")
        print(f"   初始资本: {self.initial_capital:,} 港币")
        print(f"   仓位比例: {self.position_ratio*100:.0f}%")
        print(f"   交易成本: {self.transaction_cost*100:.1f}%")
        print(f"   最大持仓: {self.max_holding_days} 天")
        print(f"   复利模式: {'启用' if self.compound_enabled else '禁用'}")
        print()

        for i, row in self.df.iterrows():
            # 计算当前总资产
            total_assets = self.current_capital
            if self.position != 0:
                position_value = self.position_size * row['close']
                if self.position == 1:
                    total_assets += position_value
                else:
                    # 空头: 总资产 = 现金 + (开仓价值 - 当前价值)
                    total_assets += (self.position_size * self.entry_price - position_value)

            # 记录每日权益
            self.daily_equity.append({
                'date': row['date'],
                'capital': self.current_capital,
                'total_assets': total_assets,
                'position': self.position,
                'position_size': self.position_size,
                'price': row['close']
            })

            # 生成交易信号
            signal_info = self.generate_dual_xye_signal(row)

            # 检查平仓条件
            should_exit, exit_reason = self.check_exit_conditions(row, signal_info)
            if should_exit:
                self.execute_trade(row, "平仓", exit_reason)

            # 检查开仓条件 (降低信号强度要求)
            if self.position == 0 and signal_info['strength'] >= 2:  # 降低到2
                if signal_info['signal'] == "买入":
                    self.execute_trade(row, "开多仓", f"双系统买入(强度{signal_info['strength']})")
                elif signal_info['signal'] == "卖出":
                    self.execute_trade(row, "开空仓", f"双系统卖出(强度{signal_info['strength']})")

            # 显示进度 (每1000条记录)
            if i % 1000 == 0:
                progress = i / len(self.df) * 100
                print(f"   进度: {progress:.1f}% - {row['date'].date()} - 资产: {total_assets:,.0f}")

        # 最后强制平仓
        if self.position != 0:
            last_row = self.df.iloc[-1]
            self.execute_trade(last_row, "平仓", "回测结束强制平仓")

        print(f"\n✅ 10年回测完成！")
        print(f"📊 总交易次数: {len([t for t in self.trades if '开' in t['action']])}")
        print(f"💰 最终资金: {self.current_capital:,.2f} 港币")
        print(f"📈 总收益率: {(self.current_capital/self.initial_capital-1)*100:+.2f}%")

    def analyze_10year_results(self):
        """分析10年回测结果"""
        print("\n📊 10年回测结果分析")
        print("=" * 60)

        if not self.trades:
            print("❌ 没有产生任何交易")
            return

        # 基本统计
        entry_trades = [t for t in self.trades if '开' in t['action']]
        exit_trades = [t for t in self.trades if '平' in t['action']]

        total_trades = len(entry_trades)
        profitable_trades = len([t for t in exit_trades if t.get('pnl', 0) > 0])
        losing_trades = len([t for t in exit_trades if t.get('pnl', 0) < 0])

        print(f"📈 交易统计:")
        print(f"   总交易次数: {total_trades}")
        print(f"   盈利交易: {profitable_trades}")
        print(f"   亏损交易: {losing_trades}")
        if len(exit_trades) > 0:
            print(f"   胜率: {profitable_trades/len(exit_trades)*100:.1f}%")

        # 收益统计
        final_capital = self.current_capital
        total_return = (final_capital / self.initial_capital - 1) * 100

        # 计算年化收益率
        start_date = self.df['date'].min()
        end_date = self.df['date'].max()
        years = (end_date - start_date).days / 365.25
        annual_return = (final_capital / self.initial_capital) ** (1/years) - 1

        print(f"\n💰 收益统计:")
        print(f"   初始资金: {self.initial_capital:,.2f} 港币")
        print(f"   最终资金: {final_capital:,.2f} 港币")
        print(f"   绝对收益: {final_capital - self.initial_capital:+,.2f} 港币")
        print(f"   总收益率: {total_return:+.2f}%")
        print(f"   年化收益率: {annual_return*100:+.2f}%")
        print(f"   回测年数: {years:.1f} 年")

        # 复利效果
        if self.compound_enabled:
            # 计算如果用固定仓位的收益
            fixed_position_return = total_return * 0.7  # 估算
            compound_advantage = total_return - fixed_position_return
            print(f"   复利优势: 约 {compound_advantage:+.1f}%")

        # 买入持有对比
        start_price = self.df['close'].iloc[0]
        end_price = self.df['close'].iloc[-1]
        buy_hold_return = (end_price / start_price - 1) * 100

        print(f"\n🆚 买入持有对比:")
        print(f"   买入持有收益率: {buy_hold_return:+.2f}%")
        print(f"   策略超额收益: {total_return - buy_hold_return:+.2f}%")
        print(f"   风险调整收益: {'优于' if total_return > buy_hold_return else '劣于'}买入持有")

        # 最大回撤分析
        equity_df = pd.DataFrame(self.daily_equity)
        equity_df['peak'] = equity_df['total_assets'].cummax()
        equity_df['drawdown'] = (equity_df['total_assets'] - equity_df['peak']) / equity_df['peak']
        max_drawdown = equity_df['drawdown'].min() * 100

        print(f"\n📉 风险指标:")
        print(f"   最大回撤: {max_drawdown:.2f}%")
        print(f"   夏普比率: {annual_return*100/abs(max_drawdown):.2f}" if max_drawdown != 0 else "   夏普比率: N/A")

        # 保存详细结果
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        # 保存交易记录
        trades_df = pd.DataFrame(self.trades)
        trades_df.to_excel(f"EAB_10年交易记录_{timestamp}.xlsx", index=False)

        # 保存权益曲线
        equity_df.to_excel(f"EAB_10年权益曲线_{timestamp}.xlsx", index=False)

        print(f"\n✅ 详细结果已保存:")
        print(f"   📄 交易记录: EAB_10年交易记录_{timestamp}.xlsx")
        print(f"   📈 权益曲线: EAB_10年权益曲线_{timestamp}.xlsx")

        # 年度收益统计
        self.calculate_yearly_performance()

    def calculate_yearly_performance(self):
        """计算年度绩效"""
        print(f"\n📅 年度绩效分析:")
        print("-" * 40)

        equity_df = pd.DataFrame(self.daily_equity)
        equity_df['year'] = equity_df['date'].dt.year

        yearly_performance = []
        for year in sorted(equity_df['year'].unique()):
            year_data = equity_df[equity_df['year'] == year]
            if len(year_data) > 1:
                start_value = year_data['total_assets'].iloc[0]
                end_value = year_data['total_assets'].iloc[-1]
                year_return = (end_value / start_value - 1) * 100

                yearly_performance.append({
                    'year': year,
                    'start_value': start_value,
                    'end_value': end_value,
                    'return': year_return
                })

                print(f"   {year}年: {year_return:+6.2f}% ({start_value:8,.0f} → {end_value:8,.0f})")

        # 计算年度收益统计
        if yearly_performance:
            returns = [y['return'] for y in yearly_performance]
            avg_return = np.mean(returns)
            std_return = np.std(returns)
            positive_years = len([r for r in returns if r > 0])

            print(f"\n   年度统计:")
            print(f"   平均年收益: {avg_return:+.2f}%")
            print(f"   收益波动率: {std_return:.2f}%")
            print(f"   盈利年数: {positive_years}/{len(returns)} ({positive_years/len(returns)*100:.0f}%)")

def main():
    """主函数"""
    print("🏦 EAB_0023HK 10年历史回测系统")
    print("=" * 60)
    print("📋 系统特点:")
    print("   • 初始资本: 2,500 港币")
    print("   • 回测期间: 10年历史数据")
    print("   • 双XYE信号系统")
    print("   • 复利仓位计算")
    print("   • 严格风险控制")
    print("   • 尽量不持仓策略")

    try:
        # 创建回测实例
        backtest = EAB10YearBacktest()

        # 加载10年数据
        if not backtest.load_10year_data():
            return

        # 运行10年回测
        backtest.run_10year_backtest()

        # 分析结果
        backtest.analyze_10year_results()

    except Exception as e:
        print(f"\n❌ 回测失败: {e}")
        import traceback
        traceback.print_exc()
    else:
        print(f"\n🎉 EAB_0023HK 10年回测完成！")

if __name__ == "__main__":
    main()
