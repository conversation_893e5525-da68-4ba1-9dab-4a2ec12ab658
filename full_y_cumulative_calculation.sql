-- Full_Y累积比例计算的完整解决方案

-- 方法1：使用窗口函数（推荐 - MySQL 8.0+）
UPDATE your_table_name t1
JOIN (
    SELECT 
        id,  -- 假设有id字段作为主键
        SUM(CASE WHEN controller = 1 THEN 1 ELSE 0 END) 
            OVER (ORDER BY date, id ROWS UNBOUNDED PRECEDING) as cumulative_controller_1,
        ROW_NUMBER() OVER (ORDER BY date, id) as cumulative_total,
        ROUND(
            SUM(CASE WHEN controller = 1 THEN 1 ELSE 0 END) 
                OVER (ORDER BY date, id ROWS UNBOUNDED PRECEDING) / 
            ROW_NUMBER() OVER (ORDER BY date, id), 
            3
        ) as calculated_full_y
    FROM your_table_name
    ORDER BY date, id
) t2 ON t1.id = t2.id
SET t1.Full_Y = t2.calculated_full_y;

-- 方法2：使用变量（兼容MySQL 5.7及以下版本）
SET @cumulative_controller_1 = 0;
SET @cumulative_total = 0;

UPDATE your_table_name 
SET 
    @cumulative_controller_1 = @cumulative_controller_1 + (CASE WHEN controller = 1 THEN 1 ELSE 0 END),
    @cumulative_total = @cumulative_total + 1,
    Full_Y = ROUND(@cumulative_controller_1 / @cumulative_total, 3)
ORDER BY date, id;  -- 确保按正确顺序处理

-- 方法3：存储过程实现（最安全的方法）
DELIMITER $$

CREATE PROCEDURE `sp_calculate_full_y`(IN tablename VARCHAR(64))
BEGIN
    DECLARE done INT DEFAULT FALSE;
    DECLARE v_id INT;
    DECLARE v_controller INT;
    DECLARE cumulative_controller_1 INT DEFAULT 0;
    DECLARE cumulative_total INT DEFAULT 0;
    DECLARE calculated_full_y DECIMAL(10,3);
    
    -- 声明游标
    DECLARE cur CURSOR FOR 
        SELECT id, controller 
        FROM your_table_name 
        ORDER BY date, id;  -- 按时间和ID排序
    
    DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;
    
    -- 重置Full_Y字段
    SET @sql = CONCAT('UPDATE `', tablename, '` SET Full_Y = NULL');
    PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;
    
    -- 打开游标
    OPEN cur;
    
    read_loop: LOOP
        FETCH cur INTO v_id, v_controller;
        IF done THEN
            LEAVE read_loop;
        END IF;
        
        -- 累积计算
        SET cumulative_total = cumulative_total + 1;
        IF v_controller = 1 THEN
            SET cumulative_controller_1 = cumulative_controller_1 + 1;
        END IF;
        
        -- 计算Full_Y
        SET calculated_full_y = ROUND(cumulative_controller_1 / cumulative_total, 3);
        
        -- 更新当前记录
        SET @sql = CONCAT(
            'UPDATE `', tablename, '` SET Full_Y = ', calculated_full_y, 
            ' WHERE id = ', v_id
        );
        PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;
        
    END LOOP;
    
    CLOSE cur;
    
    SELECT CONCAT('Full_Y累积计算完成，总处理记录: ', cumulative_total) AS result;
    
END$$

DELIMITER ;

-- 方法4：集成到您现有的存储过程中
-- 在sp_updatecontroller_enhanced中添加以下代码段：

    -- 更新Full_Y字段（累积比例计算）
    SELECT '开始计算Full_Y累积比例' AS full_y_start_status;
    
    -- 检查是否支持窗口函数（MySQL 8.0+）
    SET @mysql_version = (SELECT SUBSTRING_INDEX(VERSION(), '.', 2) + 0);
    
    IF @mysql_version >= 8.0 THEN
        -- 使用窗口函数
        SET @sql = CONCAT(
            'UPDATE `', tablename, '` t1 ',
            'JOIN ( ',
            '    SELECT id, ',
            '        ROUND( ',
            '            SUM(CASE WHEN controller = 1 THEN 1 ELSE 0 END) ',
            '                OVER (ORDER BY date, id ROWS UNBOUNDED PRECEDING) / ',
            '            ROW_NUMBER() OVER (ORDER BY date, id), ',
            '            3 ',
            '        ) as calculated_full_y ',
            '    FROM `', tablename, '` ',
            '    ORDER BY date, id ',
            ') t2 ON t1.id = t2.id ',
            'SET t1.Full_Y = t2.calculated_full_y'
        );
    ELSE
        -- 使用变量方法
        SET @sql = CONCAT(
            'SET @cumulative_controller_1 = 0, @cumulative_total = 0; ',
            'UPDATE `', tablename, '` ',
            'SET ',
            '    @cumulative_controller_1 = @cumulative_controller_1 + (CASE WHEN controller = 1 THEN 1 ELSE 0 END), ',
            '    @cumulative_total = @cumulative_total + 1, ',
            '    Full_Y = ROUND(@cumulative_controller_1 / @cumulative_total, 3) ',
            'ORDER BY date, id'
        );
    END IF;
    
    PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;
    SELECT 'Full_Y累积比例计算完成' AS full_y_update_status;
