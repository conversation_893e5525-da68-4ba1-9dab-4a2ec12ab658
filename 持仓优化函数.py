#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
持仓优化函数
===========

专为2500港元资金设计的智能持仓优化系统
支持多种优化策略和风险控制机制

功能特点：
1. 动态仓位优化
2. 风险收益平衡
3. 资金利用率最大化
4. 多策略组合优化
5. 实时调仓建议

作者: Cosmoon NG
日期: 2025年7月28日
版本: v1.0
"""

import numpy as np
import pandas as pd
import yfinance as yf
from scipy.optimize import minimize
from datetime import datetime, timedelta
import matplotlib.pyplot as plt
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class PositionOptimizer:
    """持仓优化器"""

    def __init__(self, initial_capital=2500, cash_reserve_ratio=0.2):
        """
        初始化持仓优化器

        参数:
        initial_capital: 初始资金
        cash_reserve_ratio: 现金储备比例
        """
        self.initial_capital = initial_capital
        self.cash_reserve_ratio = cash_reserve_ratio
        self.available_capital = initial_capital * (1 - cash_reserve_ratio)
        self.cash_reserve = initial_capital * cash_reserve_ratio

        # 风险参数
        self.max_position_ratio = 0.8  # 最大仓位比例
        self.min_trade_amount = 100    # 最小交易金额
        self.commission_rate = 0.001   # 手续费率

        # 优化参数
        self.risk_free_rate = 0.02     # 无风险利率2%
        self.target_return = 0.12      # 目标年化收益12%
        self.max_volatility = 0.25     # 最大波动率25%

        print(f"🎯 持仓优化器已初始化")
        print(f"💰 总资金: {self.initial_capital:,.0f} 港元")
        print(f"💵 可投资金: {self.available_capital:,.0f} 港元")
        print(f"🏦 现金储备: {self.cash_reserve:,.0f} 港元")

    def load_market_data(self, symbol="0023.HK", period="1y"):
        """加载市场数据"""
        print(f"\n📊 加载{symbol}市场数据...")

        try:
            ticker = yf.Ticker(symbol)
            hist_data = ticker.history(period=period)

            if hist_data.empty:
                raise ValueError(f"无法获取{symbol}的数据")

            self.symbol = symbol
            self.market_data = hist_data
            self.returns = hist_data['Close'].pct_change().dropna()

            # 计算统计指标
            self.mean_return = self.returns.mean() * 252  # 年化收益率
            self.volatility = self.returns.std() * np.sqrt(252)  # 年化波动率
            self.sharpe_ratio = (self.mean_return - self.risk_free_rate) / self.volatility

            print(f"✅ 数据加载完成")
            print(f"   年化收益率: {self.mean_return:.2%}")
            print(f"   年化波动率: {self.volatility:.2%}")
            print(f"   夏普比率: {self.sharpe_ratio:.3f}")

            return True

        except Exception as e:
            print(f"❌ 数据加载失败: {e}")
            return False

    def calculate_optimal_position_kelly(self):
        """
        使用凯利公式计算最优仓位

        凯利公式: f = (bp - q) / b
        其中: b = 赔率, p = 胜率, q = 败率
        """
        print(f"\n🎯 使用凯利公式计算最优仓位...")

        # 计算胜率和平均盈亏
        positive_returns = self.returns[self.returns > 0]
        negative_returns = self.returns[self.returns < 0]

        win_rate = len(positive_returns) / len(self.returns)
        loss_rate = 1 - win_rate

        avg_win = positive_returns.mean() if len(positive_returns) > 0 else 0
        avg_loss = abs(negative_returns.mean()) if len(negative_returns) > 0 else 0

        # 计算赔率
        odds_ratio = avg_win / avg_loss if avg_loss > 0 else 1

        # 凯利公式
        kelly_fraction = (odds_ratio * win_rate - loss_rate) / odds_ratio

        # 限制在合理范围内
        kelly_fraction = max(0, min(kelly_fraction, self.max_position_ratio))

        optimal_amount = self.available_capital * kelly_fraction

        print(f"   胜率: {win_rate:.2%}")
        print(f"   平均盈利: {avg_win:.2%}")
        print(f"   平均亏损: {avg_loss:.2%}")
        print(f"   赔率: {odds_ratio:.2f}")
        print(f"   凯利比例: {kelly_fraction:.2%}")
        print(f"   建议投资金额: {optimal_amount:,.0f} 港元")

        return kelly_fraction, optimal_amount

    def calculate_optimal_position_markowitz(self):
        """
        使用马科维茨理论计算最优仓位
        最大化夏普比率
        """
        print(f"\n📊 使用马科维茨理论计算最优仓位...")

        def negative_sharpe(weight):
            """负夏普比率（用于最小化）"""
            portfolio_return = weight * self.mean_return
            portfolio_volatility = weight * self.volatility

            if portfolio_volatility == 0:
                return -np.inf

            sharpe = (portfolio_return - self.risk_free_rate) / portfolio_volatility
            return -sharpe

        # 约束条件
        constraints = [
            {'type': 'ineq', 'fun': lambda x: self.max_position_ratio - x},  # 最大仓位
            {'type': 'ineq', 'fun': lambda x: x}  # 非负仓位
        ]

        # 初始猜测
        initial_guess = 0.3

        # 优化
        result = minimize(
            negative_sharpe,
            initial_guess,
            method='SLSQP',
            constraints=constraints,
            bounds=[(0, self.max_position_ratio)]
        )

        optimal_weight = result.x[0] if result.success else 0.3
        optimal_amount = self.available_capital * optimal_weight

        # 计算组合指标
        portfolio_return = optimal_weight * self.mean_return
        portfolio_volatility = optimal_weight * self.volatility
        portfolio_sharpe = (portfolio_return - self.risk_free_rate) / portfolio_volatility

        print(f"   最优权重: {optimal_weight:.2%}")
        print(f"   组合收益率: {portfolio_return:.2%}")
        print(f"   组合波动率: {portfolio_volatility:.2%}")
        print(f"   组合夏普比率: {portfolio_sharpe:.3f}")
        print(f"   建议投资金额: {optimal_amount:,.0f} 港元")

        return optimal_weight, optimal_amount

    def calculate_optimal_position_var(self, confidence_level=0.05):
        """
        基于VaR风险控制的仓位优化

        参数:
        confidence_level: 置信水平 (默认5%)
        """
        print(f"\n⚠️ 基于VaR风险控制计算最优仓位...")

        # 计算VaR
        var_percentile = np.percentile(self.returns, confidence_level * 100)

        # 设定最大可接受损失（总资金的5%）
        max_acceptable_loss = self.initial_capital * 0.05

        # 计算最大仓位
        max_position_var = max_acceptable_loss / abs(var_percentile * self.market_data['Close'].iloc[-1])

        # 限制在合理范围内
        optimal_weight = min(max_position_var / self.available_capital, self.max_position_ratio)
        optimal_amount = self.available_capital * optimal_weight

        print(f"   {confidence_level:.0%}置信水平VaR: {var_percentile:.2%}")
        print(f"   最大可接受损失: {max_acceptable_loss:,.0f} 港元")
        print(f"   VaR约束下最优权重: {optimal_weight:.2%}")
        print(f"   建议投资金额: {optimal_amount:,.0f} 港元")

        return optimal_weight, optimal_amount

    def dynamic_position_adjustment(self, current_price, entry_price, current_position):
        """
        动态仓位调整

        参数:
        current_price: 当前价格
        entry_price: 入场价格
        current_position: 当前仓位金额
        """
        print(f"\n🔄 动态仓位调整分析...")

        # 计算当前盈亏比例
        pnl_ratio = (current_price - entry_price) / entry_price

        # 计算当前仓位比例
        current_weight = current_position / self.available_capital

        # 动态调整逻辑
        if pnl_ratio > 0.1:  # 盈利超过10%
            # 获利了结，减仓30%
            adjustment = -0.3
            reason = "获利了结"
        elif pnl_ratio > 0.05:  # 盈利5-10%
            # 小幅减仓，锁定部分利润
            adjustment = -0.1
            reason = "锁定利润"
        elif pnl_ratio < -0.05:  # 亏损超过5%
            # 止损或减仓
            if pnl_ratio < -0.08:  # 亏损超过8%
                adjustment = -1.0  # 全部止损
                reason = "止损出场"
            else:
                adjustment = -0.2  # 减仓20%
                reason = "控制风险"
        else:
            # 持仓不变
            adjustment = 0
            reason = "维持现状"

        new_weight = max(0, current_weight * (1 + adjustment))
        new_amount = new_weight * self.available_capital

        print(f"   当前价格: {current_price:.2f} 港元")
        print(f"   入场价格: {entry_price:.2f} 港元")
        print(f"   盈亏比例: {pnl_ratio:.2%}")
        print(f"   当前仓位: {current_weight:.2%}")
        print(f"   调整建议: {adjustment:.0%}")
        print(f"   调整原因: {reason}")
        print(f"   新仓位比例: {new_weight:.2%}")
        print(f"   新投资金额: {new_amount:,.0f} 港元")

        return new_weight, new_amount, reason

    def comprehensive_position_optimization(self):
        """
        综合持仓优化
        结合多种方法给出最终建议
        """
        print(f"\n🎯 综合持仓优化分析")
        print("=" * 60)

        # 1. 凯利公式
        kelly_weight, kelly_amount = self.calculate_optimal_position_kelly()

        # 2. 马科维茨理论
        markowitz_weight, markowitz_amount = self.calculate_optimal_position_markowitz()

        # 3. VaR风险控制
        var_weight, var_amount = self.calculate_optimal_position_var()

        # 综合权重（加权平均）
        weights = [kelly_weight, markowitz_weight, var_weight]
        method_weights = [0.3, 0.4, 0.3]  # 各方法权重

        final_weight = sum(w * mw for w, mw in zip(weights, method_weights))
        final_amount = final_weight * self.available_capital

        # 计算股数
        current_price = self.market_data['Close'].iloc[-1]
        shares = int(final_amount / current_price)
        actual_amount = shares * current_price

        print(f"\n📊 综合优化结果:")
        print(f"   凯利公式建议: {kelly_weight:.2%} ({kelly_amount:,.0f}港元)")
        print(f"   马科维茨建议: {markowitz_weight:.2%} ({markowitz_amount:,.0f}港元)")
        print(f"   VaR风险控制: {var_weight:.2%} ({var_amount:,.0f}港元)")
        print(f"   综合建议权重: {final_weight:.2%}")
        print(f"   建议投资金额: {final_amount:,.0f} 港元")
        print(f"   建议购买股数: {shares} 股")
        print(f"   实际投资金额: {actual_amount:,.0f} 港元")
        print(f"   剩余现金: {self.available_capital - actual_amount:,.0f} 港元")

        return {
            'final_weight': final_weight,
            'final_amount': final_amount,
            'shares': shares,
            'actual_amount': actual_amount,
            'current_price': current_price,
            'kelly_weight': kelly_weight,
            'markowitz_weight': markowitz_weight,
            'var_weight': var_weight
        }

    def generate_trading_signals(self):
        """生成交易信号"""
        print(f"\n📈 生成交易信号...")

        # 计算技术指标
        close_prices = self.market_data['Close']

        # 移动平均线
        ma5 = close_prices.rolling(window=5).mean()
        ma10 = close_prices.rolling(window=10).mean()
        ma20 = close_prices.rolling(window=20).mean()

        # RSI
        delta = close_prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
        rs = gain / (loss + 1e-10)
        rsi = 100 - (100 / (1 + rs))

        # 布林带
        bb_middle = close_prices.rolling(window=20).mean()
        bb_std = close_prices.rolling(window=20).std()
        bb_upper = bb_middle + (bb_std * 2)
        bb_lower = bb_middle - (bb_std * 2)

        # 当前值
        current_price = close_prices.iloc[-1]
        current_rsi = rsi.iloc[-1]
        current_ma5 = ma5.iloc[-1]
        current_ma10 = ma10.iloc[-1]
        current_bb_lower = bb_lower.iloc[-1]
        current_bb_upper = bb_upper.iloc[-1]

        # 生成信号
        signals = []

        # 买入信号
        if (current_price <= current_bb_lower * 1.02 and
            current_rsi < 35 and
            current_ma5 > current_ma10):
            signals.append("强烈买入")
        elif (current_price < current_ma10 and
              current_rsi < 40):
            signals.append("买入")

        # 卖出信号
        if (current_price >= current_bb_upper * 0.98 and
            current_rsi > 70):
            signals.append("卖出")
        elif current_rsi > 65:
            signals.append("减仓")

        if not signals:
            signals.append("观望")

        print(f"   当前价格: {current_price:.2f} 港元")
        print(f"   RSI: {current_rsi:.1f}")
        print(f"   MA5: {current_ma5:.2f}")
        print(f"   MA10: {current_ma10:.2f}")
        print(f"   布林带下轨: {current_bb_lower:.2f}")
        print(f"   布林带上轨: {current_bb_upper:.2f}")
        print(f"   交易信号: {', '.join(signals)}")

        return signals[0] if signals else "观望"

    def create_optimization_report(self):
        """创建优化报告"""
        print(f"\n📋 生成持仓优化报告...")

        # 执行综合优化
        optimization_result = self.comprehensive_position_optimization()

        # 生成交易信号
        trading_signal = self.generate_trading_signals()

        # 创建报告
        report = {
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'symbol': self.symbol,
            'initial_capital': self.initial_capital,
            'available_capital': self.available_capital,
            'cash_reserve': self.cash_reserve,
            'current_price': optimization_result['current_price'],
            'trading_signal': trading_signal,
            'optimization_result': optimization_result,
            'market_stats': {
                'annual_return': self.mean_return,
                'annual_volatility': self.volatility,
                'sharpe_ratio': self.sharpe_ratio
            }
        }

        # 保存报告
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"持仓优化报告_{timestamp}.txt"

        with open(filename, 'w', encoding='utf-8') as f:
            f.write("持仓优化报告\n")
            f.write("=" * 50 + "\n\n")
            f.write(f"生成时间: {report['timestamp']}\n")
            f.write(f"投资标的: {report['symbol']}\n")
            f.write(f"总资金: {report['initial_capital']:,.0f} 港元\n")
            f.write(f"可投资金: {report['available_capital']:,.0f} 港元\n")
            f.write(f"现金储备: {report['cash_reserve']:,.0f} 港元\n\n")

            f.write("市场统计:\n")
            f.write(f"  年化收益率: {report['market_stats']['annual_return']:.2%}\n")
            f.write(f"  年化波动率: {report['market_stats']['annual_volatility']:.2%}\n")
            f.write(f"  夏普比率: {report['market_stats']['sharpe_ratio']:.3f}\n\n")

            f.write("优化结果:\n")
            f.write(f"  当前价格: {report['current_price']:.2f} 港元\n")
            f.write(f"  交易信号: {report['trading_signal']}\n")
            f.write(f"  建议仓位: {report['optimization_result']['final_weight']:.2%}\n")
            f.write(f"  建议金额: {report['optimization_result']['final_amount']:,.0f} 港元\n")
            f.write(f"  建议股数: {report['optimization_result']['shares']} 股\n")

        print(f"📄 报告已保存到: {filename}")

        return report

def main():
    """主函数"""
    print("🎯 持仓优化函数系统")
    print("适用于2500港元资金的智能持仓优化")
    print("=" * 50)

    try:
        # 创建优化器
        optimizer = PositionOptimizer(initial_capital=2500, cash_reserve_ratio=0.2)

        # 加载数据
        if not optimizer.load_market_data("0023.HK", period="1y"):
            return None

        # 生成优化报告
        report = optimizer.create_optimization_report()

        print(f"\n✅ 持仓优化分析完成")
        print(f"💡 请查看生成的报告文件获取详细建议")

        return optimizer, report

    except Exception as e:
        print(f"\n❌ 优化失败: {e}")
        import traceback
        traceback.print_exc()
        return None, None

def optimize_position_for_user(current_capital=2500, target_stock="0023.HK",
                              current_price=None, entry_price=None, current_shares=0):
    """
    用户专用持仓优化函数

    参数:
    current_capital: 当前总资金
    target_stock: 目标股票代码
    current_price: 当前股价 (如果为None则自动获取)
    entry_price: 入场价格 (如果已持仓)
    current_shares: 当前持股数量

    返回:
    优化建议字典
    """
    print(f"🎯 为用户定制持仓优化")
    print(f"💰 当前资金: {current_capital:,.0f} 港元")
    print(f"📊 目标股票: {target_stock}")
    print(f"📈 当前持股: {current_shares} 股")

    # 创建优化器
    optimizer = PositionOptimizer(initial_capital=current_capital)

    # 加载数据
    if not optimizer.load_market_data(target_stock):
        return None

    # 获取当前价格
    if current_price is None:
        current_price = optimizer.market_data['Close'].iloc[-1]

    # 计算当前持仓价值
    current_position_value = current_shares * current_price
    current_cash = current_capital - current_position_value

    print(f"💵 当前价格: {current_price:.2f} 港元")
    print(f"💎 持仓价值: {current_position_value:,.0f} 港元")
    print(f"💰 现金余额: {current_cash:,.0f} 港元")

    # 执行优化
    optimization_result = optimizer.comprehensive_position_optimization()
    trading_signal = optimizer.generate_trading_signals()

    # 计算调仓建议
    target_amount = optimization_result['final_amount']
    target_shares = optimization_result['shares']

    # 调仓建议
    shares_diff = target_shares - current_shares
    amount_diff = shares_diff * current_price

    if shares_diff > 0:
        action = "买入"
        action_shares = shares_diff
        action_amount = amount_diff
    elif shares_diff < 0:
        action = "卖出"
        action_shares = abs(shares_diff)
        action_amount = abs(amount_diff)
    else:
        action = "持有"
        action_shares = 0
        action_amount = 0

    # 如果已有持仓，计算动态调整建议
    dynamic_suggestion = None
    if current_shares > 0 and entry_price is not None:
        dynamic_weight, dynamic_amount, reason = optimizer.dynamic_position_adjustment(
            current_price, entry_price, current_position_value
        )
        dynamic_suggestion = {
            'reason': reason,
            'new_weight': dynamic_weight,
            'new_amount': dynamic_amount,
            'adjustment': (dynamic_amount - current_position_value) / current_price
        }

    # 生成最终建议
    recommendation = {
        'current_status': {
            'capital': current_capital,
            'price': current_price,
            'shares': current_shares,
            'position_value': current_position_value,
            'cash': current_cash,
            'position_ratio': current_position_value / current_capital
        },
        'optimization_result': {
            'signal': trading_signal,
            'target_weight': optimization_result['final_weight'],
            'target_amount': target_amount,
            'target_shares': target_shares,
            'kelly_weight': optimization_result['kelly_weight'],
            'markowitz_weight': optimization_result['markowitz_weight'],
            'var_weight': optimization_result['var_weight']
        },
        'action_recommendation': {
            'action': action,
            'shares': action_shares,
            'amount': action_amount,
            'urgency': 'high' if trading_signal in ['强烈买入', '强烈卖出'] else 'medium'
        },
        'dynamic_adjustment': dynamic_suggestion,
        'risk_metrics': {
            'annual_return': optimizer.mean_return,
            'annual_volatility': optimizer.volatility,
            'sharpe_ratio': optimizer.sharpe_ratio
        }
    }

    # 打印建议
    print(f"\n📋 持仓优化建议:")
    print(f"   交易信号: {trading_signal}")
    print(f"   建议行动: {action}")
    if action_shares > 0:
        print(f"   操作数量: {action_shares} 股")
        print(f"   操作金额: {action_amount:,.0f} 港元")

    if dynamic_suggestion:
        print(f"\n🔄 动态调整建议:")
        print(f"   调整原因: {dynamic_suggestion['reason']}")
        if dynamic_suggestion['adjustment'] != 0:
            adj_action = "买入" if dynamic_suggestion['adjustment'] > 0 else "卖出"
            adj_shares = abs(int(dynamic_suggestion['adjustment']))
            print(f"   建议{adj_action}: {adj_shares} 股")

    return recommendation

def quick_position_check(capital=2500, shares=0, entry_price=None):
    """
    快速持仓检查函数

    参数:
    capital: 总资金
    shares: 当前持股
    entry_price: 入场价格

    返回:
    快速建议
    """
    print(f"⚡ 快速持仓检查")

    # 获取当前价格
    ticker = yf.Ticker("0023.HK")
    current_price = ticker.history(period="1d")['Close'].iloc[-1]

    position_value = shares * current_price
    cash = capital - position_value
    position_ratio = position_value / capital

    print(f"💰 总资金: {capital:,.0f} 港元")
    print(f"💵 当前价格: {current_price:.2f} 港元")
    print(f"📈 持股: {shares} 股")
    print(f"💎 持仓价值: {position_value:,.0f} 港元")
    print(f"💰 现金: {cash:,.0f} 港元")
    print(f"📊 仓位比例: {position_ratio:.1%}")

    # 简单建议
    if position_ratio < 0.2:
        suggestion = "仓位较轻，可考虑加仓"
    elif position_ratio > 0.8:
        suggestion = "仓位较重，注意风险控制"
    else:
        suggestion = "仓位适中"

    # 盈亏分析
    if shares > 0 and entry_price is not None:
        pnl = (current_price - entry_price) * shares
        pnl_ratio = (current_price - entry_price) / entry_price
        print(f"💹 入场价格: {entry_price:.2f} 港元")
        print(f"💰 浮动盈亏: {pnl:,.0f} 港元 ({pnl_ratio:.1%})")

        if pnl_ratio > 0.15:
            suggestion += "，建议止盈"
        elif pnl_ratio < -0.08:
            suggestion += "，建议止损"

    print(f"💡 建议: {suggestion}")

    return {
        'current_price': current_price,
        'position_value': position_value,
        'cash': cash,
        'position_ratio': position_ratio,
        'suggestion': suggestion
    }

if __name__ == "__main__":
    # 运行完整优化
    optimizer, report = main()

    print(f"\n" + "="*60)
    print(f"🎯 用户专用功能演示")

    # 演示用户专用函数
    user_recommendation = optimize_position_for_user(
        current_capital=2500,
        target_stock="0023.HK",
        current_shares=0,  # 假设当前无持仓
        entry_price=None
    )

    print(f"\n" + "="*60)
    print(f"⚡ 快速检查演示")

    # 演示快速检查
    quick_check = quick_position_check(
        capital=2500,
        shares=50,  # 假设持有50股
        entry_price=12.0  # 假设12港元入场
    )
