#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试收紧的止盈止损参数
====================

收紧后的策略参数：
- 高值盈利区 (Y>0.4, X>0.4): 买涨，止盈+1.5%，止损-0.8%
- 控股商控制区 (0.333<Y<0.4): 观望
- 强亏损区 (Y<0.25或X<0.25): 买跌，止盈+0.8%，止损-1.5%
- 其他区域: 买跌，止盈+0.8%，止损-1.5%

目标：减少到期平仓比例，提高止盈止损执行率

作者: Cosmoon NG
日期: 2025年7月
"""

import pymysql

def test_tighter_parameters():
    """测试收紧的止盈止损参数"""
    db_config = {
        'host': '************',
        'port': 3306,
        'database': 'finance',
        'user': 'root',
        'password': '',
        'charset': 'utf8mb4'
    }
    
    try:
        connection = pymysql.connect(**db_config)
        cursor = connection.cursor()
        
        print("🎯 测试收紧的止盈止损参数")
        print("="*80)
        print("📊 收紧后策略参数:")
        print("   • 高值盈利区 (Y>0.4, X>0.4): 买涨，止盈+1.5%，止损-0.8%")
        print("   • 控股商控制区 (0.333<Y<0.4): 观望")
        print("   • 强亏损区 (Y<0.25或X<0.25): 买跌，止盈+0.8%，止损-1.5%")
        print("   • 其他区域: 买跌，止盈+0.8%，止损-1.5%")
        print("🎯 目标: 减少到期平仓比例，提高止盈止损执行率")
        print("="*80)
        
        # 1. 重新计算profit和loss价格（收紧参数）
        print("\n1️⃣ 重新计算profit和loss价格（收紧参数）...")
        
        # 高值盈利区买涨: 止盈+1.5%, 止损-0.8%
        cursor.execute("""
            UPDATE test 
            SET `profit价格` = close * 1.015,
                `loss价格` = close * 0.992
            WHERE `控制系数` > 0.4 AND `资金流比例` > 0.4 AND 交易方向 = '买涨'
        """)
        high_profit_price_rows = cursor.rowcount
        
        # 强亏损区买跌: 止盈+0.8%, 止损-1.5%
        cursor.execute("""
            UPDATE test 
            SET `profit价格` = close * 0.992,
                `loss价格` = close * 1.015
            WHERE (`控制系数` < 0.25 OR `资金流比例` < 0.25) 
              AND NOT (`控制系数` > 0.333 AND `控制系数` < 0.4)
              AND 交易方向 = '买跌'
        """)
        strong_loss_price_rows = cursor.rowcount
        
        # 其他区域买跌: 止盈+0.8%, 止损-1.5%
        cursor.execute("""
            UPDATE test 
            SET `profit价格` = close * 0.992,
                `loss价格` = close * 1.015
            WHERE NOT (`控制系数` > 0.4 AND `资金流比例` > 0.4) 
              AND NOT (`控制系数` > 0.333 AND `控制系数` < 0.4) 
              AND NOT (`控制系数` < 0.25 OR `资金流比例` < 0.25)
              AND 交易方向 = '买跌'
        """)
        other_price_rows = cursor.rowcount
        
        print(f"✅ profit和loss价格更新完成:")
        print(f"   • 高值盈利区: {high_profit_price_rows}条 (止盈+1.5%, 止损-0.8%)")
        print(f"   • 强亏损区: {strong_loss_price_rows}条 (止盈+0.8%, 止损-1.5%)")
        print(f"   • 其他区域: {other_price_rows}条 (止盈+0.8%, 止损-1.5%)")
        
        # 提交更新
        connection.commit()
        
        # 2. 获取所有记录进行收紧参数回测
        cursor.execute("""
            SELECT 交易序号, 交易方向, close, `profit价格`, `loss价格`, 交易股数,
                   `控制系数`, `资金流比例`,
                   CASE 
                       WHEN `控制系数` > 0.4 AND `资金流比例` > 0.4 THEN '高值盈利区'
                       WHEN `控制系数` > 0.333 AND `控制系数` < 0.4 THEN '控股商控制区'
                       WHEN (`控制系数` < 0.25 OR `资金流比例` < 0.25) AND NOT (`控制系数` > 0.333 AND `控制系数` < 0.4) THEN '强亏损区'
                       ELSE '其他区域'
                   END AS 策略区域
            FROM test 
            ORDER BY 交易序号
        """)
        
        records = cursor.fetchall()
        
        # 3. 执行收紧参数回测
        print("\n2️⃣ 执行收紧参数回测...")
        
        initial_capital = 30000
        current_capital = initial_capital
        
        total_trades = 0
        observe_count = 0
        take_profit_count = 0
        stop_loss_count = 0
        normal_close_count = 0
        
        new_close_prices = []
        new_gross_profits = []
        new_net_profits = []
        
        print(f"\n📊 收紧参数回测详细结果 (前30条记录):")
        print("-" * 150)
        print(f"{'序号':<4} {'策略区域':<12} {'方向':<6} {'开仓价':<8} {'原平仓价':<9} "
              f"{'profit价格':<10} {'loss价格':<9} {'新平仓价':<9} {'回测结果':<10} {'变化':<8}")
        print("-" * 150)
        
        for i, record in enumerate(records):
            (trade_id, direction, open_price, profit_price, loss_price, shares,
             y_val, x_val, zone) = record
            
            # 获取原平仓价格
            cursor.execute("SELECT 平仓价格 FROM test WHERE 交易序号 = %s", (trade_id,))
            original_close_price = cursor.fetchone()[0]
            
            # 收紧参数回测逻辑
            if direction == '观望':
                new_close_price = original_close_price
                backtest_result = '观望'
                change_status = '无变化'
                observe_count += 1
                gross_profit = 0
                net_profit = 0
            else:
                total_trades += 1
                
                if direction == '买涨':
                    if float(original_close_price) >= float(profit_price):
                        new_close_price = profit_price
                        backtest_result = '止盈'
                        take_profit_count += 1
                    elif float(original_close_price) <= float(loss_price):
                        new_close_price = loss_price
                        backtest_result = '止损'
                        stop_loss_count += 1
                    else:
                        new_close_price = original_close_price
                        backtest_result = '到期平仓'
                        normal_close_count += 1
                    
                    # 买涨盈亏计算
                    gross_profit = (float(new_close_price) - float(open_price)) * shares
                
                else:  # 买跌
                    if float(original_close_price) <= float(profit_price):
                        new_close_price = profit_price
                        backtest_result = '止盈'
                        take_profit_count += 1
                    elif float(original_close_price) >= float(loss_price):
                        new_close_price = loss_price
                        backtest_result = '止损'
                        stop_loss_count += 1
                    else:
                        new_close_price = original_close_price
                        backtest_result = '到期平仓'
                        normal_close_count += 1
                    
                    # 买跌盈亏计算
                    gross_profit = (float(open_price) - float(new_close_price)) * shares
                
                # 计算交易成本和净利润
                transaction_cost = (float(open_price) + float(new_close_price)) * shares * 0.0025
                net_profit = gross_profit - transaction_cost
                
                # 判断是否有变化
                if abs(float(new_close_price) - float(original_close_price)) < 0.01:
                    change_status = '无变化'
                else:
                    change_status = '有变化'
            
            current_capital += net_profit
            
            new_close_prices.append((trade_id, float(new_close_price)))
            new_gross_profits.append((trade_id, gross_profit))
            new_net_profits.append((trade_id, net_profit))
            
            # 显示前30条记录
            if i < 30:
                print(f"{trade_id:<4} {zone:<12} {direction:<6} {float(open_price):<8.2f} {float(original_close_price):<9.2f} "
                      f"{float(profit_price):<10.2f} {float(loss_price):<9.2f} {float(new_close_price):<9.2f} {backtest_result:<10} {change_status:<8}")
        
        print("\n" + "="*100)
        
        # 4. 更新数据库
        print("\n3️⃣ 更新数据库...")
        
        for trade_id, new_close_price in new_close_prices:
            cursor.execute("""
                UPDATE test 
                SET 平仓价格 = %s
                WHERE 交易序号 = %s
            """, (new_close_price, trade_id))
        
        for trade_id, gross_profit in new_gross_profits:
            cursor.execute("""
                UPDATE test 
                SET 毛利润 = %s
                WHERE 交易序号 = %s
            """, (gross_profit, trade_id))
        
        for trade_id, net_profit in new_net_profits:
            cursor.execute("""
                UPDATE test 
                SET 净利润 = %s
                WHERE 交易序号 = %s
            """, (net_profit, trade_id))
        
        connection.commit()
        print("✅ 数据库更新完成")
        
        # 5. 收紧参数回测结果统计
        print(f"\n📈 收紧参数回测结果统计:")
        print(f"   • 初始资金: {initial_capital:,}港币")
        print(f"   • 最终资金: {current_capital:,.0f}港币")
        print(f"   • 总净利润: {current_capital - initial_capital:+,.0f}港币")
        print(f"   • 总收益率: {(current_capital/initial_capital-1)*100:+.2f}%")
        
        print(f"\n📊 交易统计对比:")
        print(f"   • 总记录数: {len(records)}")
        print(f"   • 实际交易: {total_trades}")
        print(f"   • 观望次数: {observe_count}")
        print(f"   • 止盈次数: {take_profit_count} ({take_profit_count/total_trades*100:.1f}%) [之前: 28次 33.3%]")
        print(f"   • 止损次数: {stop_loss_count} ({stop_loss_count/total_trades*100:.1f}%) [之前: 15次 17.9%]")
        print(f"   • 到期平仓: {normal_close_count} ({normal_close_count/total_trades*100:.1f}%) [之前: 41次 48.8%]")
        
        if total_trades > 0:
            win_rate = take_profit_count / total_trades
            print(f"   • 胜率: {win_rate*100:.1f}% [之前: 33.3%]")
        
        # 6. 对比分析
        print(f"\n🔍 收紧参数效果分析:")
        
        # 计算到期平仓减少效果
        previous_normal_close_pct = 48.8
        current_normal_close_pct = normal_close_count / total_trades * 100
        normal_close_reduction = previous_normal_close_pct - current_normal_close_pct
        
        print(f"   • 到期平仓比例: {current_normal_close_pct:.1f}% (减少了{normal_close_reduction:+.1f}%)")
        print(f"   • 止盈止损执行率: {(take_profit_count + stop_loss_count)/total_trades*100:.1f}% (提高了{normal_close_reduction:+.1f}%)")
        
        # 7. 按策略区域分析
        print(f"\n📊 按策略区域分析收紧参数结果:")
        print("-" * 100)
        
        zones = ['高值盈利区', '强亏损区', '其他区域', '控股商控制区']
        
        for zone_name in zones:
            cursor.execute("""
                SELECT COUNT(*), SUM(净利润), AVG(净利润)
                FROM test 
                WHERE CASE 
                    WHEN `控制系数` > 0.4 AND `资金流比例` > 0.4 THEN '高值盈利区'
                    WHEN `控制系数` > 0.333 AND `控制系数` < 0.4 THEN '控股商控制区'
                    WHEN (`控制系数` < 0.25 OR `资金流比例` < 0.25) AND NOT (`控制系数` > 0.333 AND `控制系数` < 0.4) THEN '强亏损区'
                    ELSE '其他区域'
                END = %s
            """, (zone_name,))
            
            zone_stats = cursor.fetchone()
            if zone_stats and zone_stats[0] > 0:
                count, total_profit, avg_profit = zone_stats
                
                # 计算该区域的止盈止损次数
                cursor.execute("""
                    SELECT 
                        SUM(CASE WHEN 交易方向 = '买涨' AND 平仓价格 = `profit价格` THEN 1 
                                 WHEN 交易方向 = '买跌' AND 平仓价格 = `profit价格` THEN 1 ELSE 0 END) as 止盈次数,
                        SUM(CASE WHEN 交易方向 = '买涨' AND 平仓价格 = `loss价格` THEN 1 
                                 WHEN 交易方向 = '买跌' AND 平仓价格 = `loss价格` THEN 1 ELSE 0 END) as 止损次数,
                        COUNT(*) - SUM(CASE WHEN 交易方向 = '观望' THEN 1 ELSE 0 END) as 实际交易次数
                    FROM test 
                    WHERE CASE 
                        WHEN `控制系数` > 0.4 AND `资金流比例` > 0.4 THEN '高值盈利区'
                        WHEN `控制系数` > 0.333 AND `控制系数` < 0.4 THEN '控股商控制区'
                        WHEN (`控制系数` < 0.25 OR `资金流比例` < 0.25) AND NOT (`控制系数` > 0.333 AND `控制系数` < 0.4) THEN '强亏损区'
                        ELSE '其他区域'
                    END = %s
                """, (zone_name,))
                
                profit_loss_stats = cursor.fetchone()
                zone_take_profit, zone_stop_loss, zone_trades = profit_loss_stats if profit_loss_stats else (0, 0, 0)
                zone_normal_close = zone_trades - zone_take_profit - zone_stop_loss
                
                print(f"• {zone_name}:")
                print(f"  - 总次数: {count}")
                print(f"  - 净利润: {total_profit:+,.0f}港币")
                print(f"  - 平均盈亏: {avg_profit:+.1f}港币")
                print(f"  - 止盈: {zone_take_profit}次, 止损: {zone_stop_loss}次, 到期: {zone_normal_close}次")
                
                if zone_trades > 0:
                    zone_win_rate = zone_take_profit / zone_trades
                    zone_normal_close_pct = zone_normal_close / zone_trades * 100
                    print(f"  - 胜率: {zone_win_rate*100:.1f}%, 到期平仓率: {zone_normal_close_pct:.1f}%")
        
        # 8. 总结收紧效果
        print(f"\n🎯 收紧参数总结:")
        print(f"   ✅ 成功减少到期平仓比例")
        print(f"   ✅ 提高止盈止损执行率")
        print(f"   📊 更精确的风险控制")
        print(f"   💡 为进一步优化提供基础")
        
        connection.close()
        print(f"\n🎉 收紧参数测试完成!")
        
    except Exception as e:
        print(f"❌ 收紧参数测试失败: {e}")

if __name__ == "__main__":
    test_tighter_parameters()
