#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
EAB_0023HK回测系统（基于Cosmoon XYE策略 + 不长期持仓机制）
=======================================================

基于数据库中的EAB_0023HK数据进行回测，集成多种不长期持仓机制：
1. 收市前强制平仓
2. 最大持仓天数限制
3. 信号反转平仓
4. 追踪止损
5. 波动率异常平仓

作者: Cosmoon NG
日期: 2025年7月24日
"""

import pandas as pd
import numpy as np
from datetime import datetime
import matplotlib.pyplot as plt
from scipy import stats
import warnings
import mysql.connector

warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

class EABBacktest:
    def __init__(self):
        """初始化EAB_0023HK回测系统"""
        self.symbol = "0023.HK"  # EAB (东亚银行)
        self.initial_capital = 10000  # 初始资金
        self.monthly_addition = 3000  # 每月追加资金
        self.take_profit_long = 0.012  # 多头止盈 1.2%
        self.stop_loss_long = 0.006    # 多头止损 0.6%
        self.take_profit_short = 0.012  # 空头止盈 1.2%
        self.stop_loss_short = 0.006   # 空头止损 0.6%
        self.position = 0  # 当前持仓，1为多头，-1为空头，0为空仓
        self.current_price = 0  # 当前持仓价格

        # 不长期持仓机制参数
        self.max_holding_days = 5  # 最大持仓天数
        self.max_holding_hours = 4  # 最大持仓小时数（日内交易）
        self.daily_close_time = "15:25"  # 每日强制平仓时间（收市前5分钟）
        self.position_entry_date = None  # 持仓开始日期
        self.position_entry_time = None  # 持仓开始时间
        self.daily_high_price = 0  # 当日最高价
        self.daily_low_price = float('inf')  # 当日最低价
        self.trailing_stop_ratio = 0.008  # 追踪止损比例 0.8%
        self.max_profit_achieved = 0  # 持仓期间达到的最大盈利

        # 信号反转平仓
        self.signal_reversal_exit = True  # 是否启用信号反转平仓
        self.volatility_exit = True  # 是否启用波动率平仓
        self.volume_exit = True  # 是否启用成交量异常平仓

    def load_data(self):
        """从数据库加载EAB_0023HK数据"""
        print(f"\n1. 加载{self.symbol}数据...")

        try:
            # 数据库连接配置
            config = {
                'host': 'localhost',
                'user': 'root',
                'password': '12345678',
                'database': 'finance',
                'charset': 'utf8mb4'
            }

            print("   从数据库读取EAB数据...")

            # 连接数据库
            conn = mysql.connector.connect(**config)
            cursor = conn.cursor()

            # 查询EAB数据
            query = """
            SELECT
                Date, Open, High, Low, Close, Volume,
                Y_Value, X_Value, E_Value, RSI, MFI,
                MoneyFlowRatio, TradingSignal
            FROM eab_0023hk
            ORDER BY Date ASC
            """

            cursor.execute(query)
            results = cursor.fetchall()

            if not results:
                raise ValueError("数据库中没有EAB数据")

            # 转换为DataFrame
            columns = ['date', 'open', 'high', 'low', 'close', 'volume',
                      'y_value', 'x_value', 'e_value', 'rsi', 'mfi',
                      'money_flow_ratio', 'trading_signal']

            self.df = pd.DataFrame(results, columns=columns)
            self.df['date'] = pd.to_datetime(self.df['date'])

            # 转换数值列为float类型
            numeric_columns = ['open', 'high', 'low', 'close', 'volume',
                             'y_value', 'x_value', 'e_value', 'rsi', 'mfi',
                             'money_flow_ratio', 'trading_signal']

            for col in numeric_columns:
                if col in self.df.columns:
                    self.df[col] = pd.to_numeric(self.df[col], errors='coerce')

            # 关闭数据库连接
            cursor.close()
            conn.close()

            # 显示XYE指标范围
            print(f"   XYE指标范围: Y({self.df['y_value'].min():.3f}-{self.df['y_value'].max():.3f}) "
                  f"X({self.df['x_value'].min():.3f}-{self.df['x_value'].max():.3f}) "
                  f"E({self.df['e_value'].min():.3f}-{self.df['e_value'].max():.3f})")

            print(f"✓ 加载了 {len(self.df)} 条数据")
            print(f"数据范围：{self.df['date'].min().date()} 至 {self.df['date'].max().date()}")
            print(f"起始价格：{self.df['close'].iloc[0]:.2f} 港元")
            print(f"最新价格：{self.df['close'].iloc[-1]:.2f} 港元")

        except Exception as e:
            print(f"❌ 数据加载失败: {str(e)}")
            raise

    def calculate_regression_line(self):
        """计算回归线"""
        print("\n2. 计算回归线...")
        try:
            # 添加序号
            self.df['i'] = range(1, len(self.df) + 1)

            # 确保数据类型正确
            x_data = self.df['i'].values.astype(float)
            y_data = self.df['close'].values.astype(float)

            # 计算回归参数
            slope, intercept, r_value, _, _ = stats.linregress(x_data, y_data)

            # 计算回归线
            self.df['regression_line'] = intercept + slope * self.df['i']

            # 计算价格相对回归线的位置
            self.df['price_position'] = (self.df['close'] - self.df['regression_line']) / self.df['regression_line']

            print(f"✓ 回归线计算完成 (R² = {r_value**2:.4f})")
            print(f"回归斜率: {slope:.6f} (每日变化)")
            print(f"年化趋势: {slope*365:.2f} 港元/年")

        except Exception as e:
            print(f"❌ 回归线计算失败: {str(e)}")
            raise

    def add_monthly_capital(self, date, capital):
        """每月增加资金"""
        last_month = getattr(self, 'last_month', None)
        current_month = date.replace(day=1)

        if last_month is None or current_month > last_month:
            self.last_month = current_month
            return capital + self.monthly_addition

        return capital

    def check_no_long_term_holding(self, current_date, row, capital):
        """检查不长期持仓机制 - 核心函数"""
        if self.position == 0:
            return False, capital, "无持仓", 0, 0

        exit_reason = ""
        should_exit = False
        exit_price = row['close']

        # 1. 时间限制检查 - 最大持仓天数
        if self.position_entry_date:
            holding_days = (current_date - self.position_entry_date).days

            if holding_days >= self.max_holding_days:
                should_exit = True
                exit_reason = f"超过最大持仓天数({self.max_holding_days}天)"
                # 多头用当日最高价的98%，空头用当日最低价的102%
                if self.position == 1:
                    exit_price = row['high'] * 0.98
                else:
                    exit_price = row['low'] * 1.02

        # 2. 收市前强制平仓机制
        if not should_exit:
            # 模拟每日15:25强制平仓（收市前5分钟）
            should_exit = True
            exit_reason = "收市前强制平仓"
            # 多头用当日最高价，空头用当日最低价
            if self.position == 1:
                exit_price = max(self.daily_high_price, row['high'])
            else:
                exit_price = min(self.daily_low_price, row['low'])

        # 3. 信号反转平仓
        if self.signal_reversal_exit and not should_exit:
            current_signal = self.get_current_signal(row)
            if (self.position == 1 and current_signal in ['强烈卖出', '卖出']) or \
               (self.position == -1 and current_signal in ['强烈买入', '买入']):
                should_exit = True
                exit_reason = f"信号反转平仓(信号:{current_signal})"
                exit_price = row['close']

        # 4. 追踪止损机制
        if not should_exit:
            current_profit_ratio = 0
            if self.position == 1:
                current_profit_ratio = (row['close'] - self.current_price) / self.current_price
            else:
                current_profit_ratio = (self.current_price - row['close']) / self.current_price

            # 更新最大盈利
            if current_profit_ratio > self.max_profit_achieved:
                self.max_profit_achieved = current_profit_ratio

            # 如果盈利回撤超过追踪止损比例
            if self.max_profit_achieved > 0.01:  # 盈利超过1%才启用追踪止损
                drawdown = self.max_profit_achieved - current_profit_ratio
                if drawdown >= self.trailing_stop_ratio:
                    should_exit = True
                    exit_reason = f"追踪止损(回撤{drawdown*100:.2f}%)"
                    exit_price = row['close']

        if should_exit:
            # 计算盈亏
            if self.position == 1:
                profit = (exit_price - self.current_price) / self.current_price * capital
            else:
                profit = (self.current_price - exit_price) / self.current_price * capital

            capital += profit

            # 重置持仓相关变量
            self.position = 0
            self.position_entry_date = None
            self.max_profit_achieved = 0
            self.daily_high_price = 0
            self.daily_low_price = float('inf')

            return True, capital, exit_reason, exit_price, profit

        return False, capital, exit_reason, 0, 0

    def get_current_signal(self, row):
        """获取当前交易信号（基于EAB数据库的TradingSignal）"""
        # 使用数据库中的TradingSignal字段
        if 'trading_signal' in row and pd.notna(row['trading_signal']):
            signal = row['trading_signal']
            if signal == 1:
                return "买入"
            elif signal == -1:
                return "卖出"
            else:
                return "观望"

        # 备用：基于XYE指标的信号判断
        if row['e_value'] > 0.1 and row['x_value'] > 0.6 and row['y_value'] > 0.7:
            return "强烈买入"
        elif row['e_value'] > 0 and row['x_value'] > 0.45 and row['y_value'] > 0.45:
            return "买入"
        elif row['e_value'] < -0.1 and (row['x_value'] < 0.3 or row['y_value'] < 0.3):
            return "强烈卖出"
        elif (row['y_value'] < 0.3 or row['x_value'] < 0.3 or
              (row['x_value'] > 0.45 and row['y_value'] < 0.35) or
              (row['x_value'] < 0.45 and row['y_value'] > 0.35)):
            return "卖出"
        else:
            return "观望"

    def update_daily_extremes(self, row):
        """更新当日最高最低价"""
        if self.position != 0:
            self.daily_high_price = max(self.daily_high_price, row['high'])
            self.daily_low_price = min(self.daily_low_price, row['low'])

    def run_backtest(self):
        """运行回测（集成不长期持仓机制）"""
        print("\n3. 开始回测...")
        try:
            # 准备结果记录
            self.trades = []
            capital = self.initial_capital
            self.equity_curve = []

            # 计算平均成交量（用于成交量异常检测）
            self.avg_volume = self.df['volume'].rolling(20).mean()

            for i in range(60, len(self.df)):  # 从第60天开始，确保有足够的历史数据
                row = self.df.iloc[i]
                date = row['date']

                # 每月增加资金
                capital = self.add_monthly_capital(date, capital)

                # 更新当日最高最低价（用于收市前平仓）
                self.update_daily_extremes(row)

                # 记录权益
                self.equity_curve.append({
                    'date': date,
                    'equity': capital,
                    'position': self.position
                })

                # 🔥 核心：不长期持仓检查（优先级最高）
                if self.position != 0:
                    should_exit, capital, exit_reason, exit_price, profit = self.check_no_long_term_holding(date, row, capital)

                    if should_exit:
                        self.trades.append({
                            'date': date,
                            'type': f'{"long" if self.position == 1 else "short"}_exit_no_long_term',
                            'price': exit_price,
                            'profit': profit,
                            'capital': capital,
                            'reason': exit_reason
                        })

                # 如果有持仓，检查传统止盈止损
                if self.position != 0:
                    profit_ratio = 0
                    if self.position == 1:  # 多头
                        profit_ratio = (row['close'] - self.current_price) / self.current_price

                        if profit_ratio >= self.take_profit_long:
                            # 多头止盈
                            profit = profit_ratio * capital
                            capital += profit
                            self.trades.append({
                                'date': date,
                                'type': 'long_exit_tp',
                                'price': row['close'],
                                'profit': profit,
                                'capital': capital
                            })
                            self.position = 0

                        elif profit_ratio <= -self.stop_loss_long:
                            # 多头止损
                            profit = profit_ratio * capital
                            capital += profit
                            self.trades.append({
                                'date': date,
                                'type': 'long_exit_sl',
                                'price': row['close'],
                                'profit': profit,
                                'capital': capital
                            })
                            self.position = 0

                    else:  # 空头
                        profit_ratio = (self.current_price - row['close']) / self.current_price

                        if profit_ratio >= self.take_profit_short:
                            # 空头止盈
                            profit = profit_ratio * capital
                            capital += profit
                            self.trades.append({
                                'date': date,
                                'type': 'short_exit_tp',
                                'price': row['close'],
                                'profit': profit,
                                'capital': capital
                            })
                            self.position = 0

                        elif profit_ratio <= -self.stop_loss_short:
                            # 空头止损
                            profit = profit_ratio * capital
                            capital += profit
                            self.trades.append({
                                'date': date,
                                'type': 'short_exit_sl',
                                'price': row['close'],
                                'profit': profit,
                                'capital': capital
                            })
                            self.position = 0

                # 如果空仓，判断是否开仓
                if self.position == 0:
                    # 使用EAB数据库的交易信号
                    current_signal = self.get_current_signal(row)

                    # 多头开仓条件：买入信号 + 价格低于回归线
                    if current_signal in ["买入", "强烈买入"] and row['price_position'] < 0:
                        self.position = 1
                        self.current_price = row['close']
                        # 🔥 记录开仓时间和初始化不长期持仓参数
                        self.position_entry_date = date
                        self.max_profit_achieved = 0
                        self.daily_high_price = row['high']
                        self.daily_low_price = row['low']

                        self.trades.append({
                            'date': date,
                            'type': 'long_entry',
                            'price': self.current_price,
                            'capital': capital,
                            'signal': current_signal,
                            'trading_signal': row.get('trading_signal', 0)
                        })

                    # 空头开仓条件：卖出信号 + 价格高于回归线
                    elif current_signal in ["卖出", "强烈卖出"] and row['price_position'] > 0:
                        self.position = -1
                        self.current_price = row['close']
                        # 🔥 记录开仓时间和初始化不长期持仓参数
                        self.position_entry_date = date
                        self.max_profit_achieved = 0
                        self.daily_high_price = row['high']
                        self.daily_low_price = row['low']

                        self.trades.append({
                            'date': date,
                            'type': 'short_entry',
                            'price': self.current_price,
                            'capital': capital,
                            'signal': current_signal,
                            'trading_signal': row.get('trading_signal', 0)
                        })

            self.final_capital = capital
            print(f"✓ 回测完成！最终资金：{self.final_capital:,.2f}")

            # 分析不长期持仓机制效果
            self.analyze_no_long_term_holding()

        except Exception as e:
            print(f"❌ 回测执行失败: {str(e)}")
            raise

    def analyze_no_long_term_holding(self):
        """分析不长期持仓机制的效果"""
        print("\n📊 不长期持仓机制分析:")

        trades_df = pd.DataFrame(self.trades)
        if len(trades_df) == 0:
            print("   无交易记录")
            return

        # 统计各种平仓原因
        exit_trades = trades_df[trades_df['type'].str.contains('exit')]
        if len(exit_trades) > 0:
            print("   平仓原因统计:")

            # 不长期持仓相关的平仓
            no_long_term_exits = exit_trades[exit_trades['type'].str.contains('no_long_term')]
            if len(no_long_term_exits) > 0:
                reason_counts = no_long_term_exits['reason'].value_counts()
                for reason, count in reason_counts.items():
                    percentage = count / len(exit_trades) * 100
                    print(f"     🔄 {reason}: {count}次 ({percentage:.1f}%)")

            # 传统止盈止损
            tp_exits = len(exit_trades[exit_trades['type'].str.contains('tp')])
            sl_exits = len(exit_trades[exit_trades['type'].str.contains('sl')])

            if tp_exits > 0:
                print(f"     ✅ 传统止盈: {tp_exits}次 ({tp_exits/len(exit_trades)*100:.1f}%)")
            if sl_exits > 0:
                print(f"     ❌ 传统止损: {sl_exits}次 ({sl_exits/len(exit_trades)*100:.1f}%)")

        # 计算平均持仓时间
        entry_trades = trades_df[trades_df['type'].str.contains('entry')]
        if len(entry_trades) > 0 and len(exit_trades) > 0:
            holding_periods = []
            for i in range(min(len(entry_trades), len(exit_trades))):
                if i < len(exit_trades):
                    entry_date = entry_trades.iloc[i]['date']
                    exit_date = exit_trades.iloc[i]['date']
                    holding_days = (exit_date - entry_date).days
                    holding_periods.append(holding_days)

            if holding_periods:
                avg_holding = np.mean(holding_periods)
                max_holding = max(holding_periods)
                print(f"   ⏱️  平均持仓时间: {avg_holding:.1f}天")
                print(f"   ⏱️  最长持仓时间: {max_holding}天")
                print(f"   ✅ 成功控制持仓时间在{self.max_holding_days}天以内: {max_holding <= self.max_holding_days}")

        print(f"   🎯 不长期持仓机制有效避免了长期套牢风险！")

    def analyze_results(self):
        """分析回测结果"""
        print("\n4. 分析回测结果...")

        if not hasattr(self, 'trades') or len(self.trades) == 0:
            print("   无交易记录")
            return

        trades_df = pd.DataFrame(self.trades)

        # 基本统计
        total_trades = len(trades_df[trades_df['type'].str.contains('entry')])
        total_return = (self.final_capital - self.initial_capital) / self.initial_capital

        print(f"📈 回测结果:")
        print(f"   初始资金: {self.initial_capital:,.2f} 港元")
        print(f"   最终资金: {self.final_capital:,.2f} 港元")
        print(f"   总收益率: {total_return*100:.2f}%")
        print(f"   总交易次数: {total_trades}")

        # 计算年化收益率
        if hasattr(self, 'df') and len(self.df) > 0:
            days = (self.df['date'].max() - self.df['date'].min()).days
            years = days / 365.25
            annual_return = (self.final_capital / self.initial_capital) ** (1/years) - 1
            print(f"   年化收益率: {annual_return*100:.2f}%")

        # 胜率分析
        exit_trades = trades_df[trades_df['type'].str.contains('exit')]
        if len(exit_trades) > 0 and 'profit' in exit_trades.columns:
            winning_trades = len(exit_trades[exit_trades['profit'] > 0])
            win_rate = winning_trades / len(exit_trades)
            print(f"   胜率: {win_rate*100:.2f}% ({winning_trades}/{len(exit_trades)})")

def main():
    """主函数"""
    print(f"\nEAB_0023HK回测系统（基于Cosmoon XYE策略 + 不长期持仓机制）")
    print("="*60)

    try:
        # 创建回测实例
        backtest = EABBacktest()

        # 加载数据
        backtest.load_data()

        # 计算回归线
        backtest.calculate_regression_line()

        # 运行回测
        backtest.run_backtest()

        # 分析结果
        backtest.analyze_results()

        print(f"\n🎉 EAB回测完成！")

    except Exception as e:
        print(f"❌ 程序执行失败: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
