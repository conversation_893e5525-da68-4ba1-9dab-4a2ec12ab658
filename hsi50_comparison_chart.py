#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
HSI50策略对比图表 - 极少持仓+每月3K vs 恒生指数
============================================

对比分析：
1. 极少持仓+每月3K定投策略
2. 恒生指数直接投资
3. 纯定投策略

作者: Cosmoon NG
日期: 2025年7月
"""

import pandas as pd
import yfinance as yf
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

def fetch_hsi_data():
    """获取恒生指数20年历史数据"""
    print("📈 获取恒生指数20年历史数据...")
    
    try:
        # 获取20年数据
        end_date = datetime.now()
        start_date = end_date - timedelta(days=20*365)
        
        ticker = yf.Ticker("^HSI")
        data = ticker.history(start=start_date, end=end_date)
        
        if data.empty:
            print("❌ 数据获取失败：返回空数据")
            return None
        
        data.reset_index(inplace=True)
        data.columns = [col.lower() for col in data.columns]
        
        print(f"✅ 成功获取恒生指数数据:")
        print(f"   • 数据期间: {data['date'].min().strftime('%Y-%m-%d')} 至 {data['date'].max().strftime('%Y-%m-%d')}")
        print(f"   • 总记录数: {len(data):,} 天")
        
        return data
        
    except Exception as e:
        print(f"❌ 获取数据失败: {e}")
        return None

def simulate_strategies(data):
    """模拟三种策略"""
    print("📊 模拟三种投资策略...")
    
    initial_capital = 30000  # 30K港币初始资金
    monthly_investment = 3000  # 每月定投3K港币
    
    results = []
    last_investment_month = None
    
    # 策略1：极少持仓+每月3K（实际就是纯定投，因为没有交易）
    strategy1_capital = initial_capital
    strategy1_total_invested = initial_capital
    
    # 策略2：恒生指数直接投资+每月3K
    strategy2_shares = initial_capital / data['close'].iloc[0]  # 初始买入股数
    strategy2_cash = 0
    strategy2_total_invested = initial_capital
    
    # 策略3：纯定投恒生指数
    strategy3_shares = 0
    strategy3_cash = initial_capital
    strategy3_total_invested = initial_capital
    
    for i, row in data.iterrows():
        date = row['date']
        price = row['close']
        
        # 检查是否需要每月定投
        current_month = date.strftime('%Y-%m')
        if last_investment_month != current_month:
            # 策略1：极少持仓+每月3K（纯现金）
            strategy1_capital += monthly_investment
            strategy1_total_invested += monthly_investment
            
            # 策略2：恒生指数直接投资+每月3K买入
            new_shares = monthly_investment / price
            strategy2_shares += new_shares
            strategy2_total_invested += monthly_investment
            
            # 策略3：纯定投恒生指数
            new_shares = monthly_investment / price
            strategy3_shares += new_shares
            strategy3_total_invested += monthly_investment
            
            last_investment_month = current_month
        
        # 计算各策略价值
        strategy1_value = strategy1_capital  # 纯现金
        strategy2_value = strategy2_shares * price + strategy2_cash
        strategy3_value = strategy3_shares * price
        
        results.append({
            'date': date,
            'hsi_price': price,
            'strategy1_value': strategy1_value,
            'strategy1_invested': strategy1_total_invested,
            'strategy2_value': strategy2_value,
            'strategy2_invested': strategy2_total_invested,
            'strategy3_value': strategy3_value,
            'strategy3_invested': strategy3_total_invested
        })
    
    return pd.DataFrame(results)

def create_comparison_chart(results_df):
    """创建对比图表"""
    print("📊 生成对比图表...")
    
    # 创建图表
    fig = plt.figure(figsize=(20, 16))
    
    # 1. 投资组合价值对比
    ax1 = plt.subplot(2, 2, 1)
    results_df['date_dt'] = pd.to_datetime(results_df['date'])
    
    plt.plot(results_df['date_dt'], results_df['strategy1_value']/10000, 
             linewidth=2, color='blue', label='极少持仓+每月3K (纯现金)')
    plt.plot(results_df['date_dt'], results_df['strategy2_value']/10000, 
             linewidth=2, color='red', label='恒指直投+每月3K')
    plt.plot(results_df['date_dt'], results_df['strategy3_value']/10000, 
             linewidth=2, color='green', label='纯定投恒指')
    
    plt.title('投资组合价值对比 (20年)', fontsize=14, fontweight='bold')
    plt.xlabel('日期')
    plt.ylabel('价值 (万港币)')
    plt.legend()
    plt.grid(True, alpha=0.3)
    ax1.xaxis.set_major_formatter(mdates.DateFormatter('%Y'))
    ax1.xaxis.set_major_locator(mdates.YearLocator(2))
    plt.xticks(rotation=45)
    
    # 2. 恒生指数价格走势
    ax2 = plt.subplot(2, 2, 2)
    plt.plot(results_df['date_dt'], results_df['hsi_price'], 
             linewidth=1, color='red', alpha=0.7)
    plt.title('恒生指数20年走势', fontsize=14, fontweight='bold')
    plt.xlabel('日期')
    plt.ylabel('恒指点数')
    plt.grid(True, alpha=0.3)
    ax2.xaxis.set_major_formatter(mdates.DateFormatter('%Y'))
    ax2.xaxis.set_major_locator(mdates.YearLocator(2))
    plt.xticks(rotation=45)
    
    # 3. 累计投入资金对比
    ax3 = plt.subplot(2, 2, 3)
    plt.plot(results_df['date_dt'], results_df['strategy1_invested']/10000, 
             linewidth=2, color='blue', label='极少持仓策略')
    plt.plot(results_df['date_dt'], results_df['strategy2_invested']/10000, 
             linewidth=2, color='red', label='恒指直投策略')
    plt.plot(results_df['date_dt'], results_df['strategy3_invested']/10000, 
             linewidth=2, color='green', label='纯定投策略')
    
    plt.title('累计投入资金对比', fontsize=14, fontweight='bold')
    plt.xlabel('日期')
    plt.ylabel('投入资金 (万港币)')
    plt.legend()
    plt.grid(True, alpha=0.3)
    ax3.xaxis.set_major_formatter(mdates.DateFormatter('%Y'))
    ax3.xaxis.set_major_locator(mdates.YearLocator(2))
    plt.xticks(rotation=45)
    
    # 4. 收益率对比
    ax4 = plt.subplot(2, 2, 4)
    results_df['strategy1_return'] = (results_df['strategy1_value'] - results_df['strategy1_invested']) / results_df['strategy1_invested'] * 100
    results_df['strategy2_return'] = (results_df['strategy2_value'] - results_df['strategy2_invested']) / results_df['strategy2_invested'] * 100
    results_df['strategy3_return'] = (results_df['strategy3_value'] - results_df['strategy3_invested']) / results_df['strategy3_invested'] * 100
    
    plt.plot(results_df['date_dt'], results_df['strategy1_return'], 
             linewidth=2, color='blue', label='极少持仓+每月3K')
    plt.plot(results_df['date_dt'], results_df['strategy2_return'], 
             linewidth=2, color='red', label='恒指直投+每月3K')
    plt.plot(results_df['date_dt'], results_df['strategy3_return'], 
             linewidth=2, color='green', label='纯定投恒指')
    
    plt.axhline(y=0, color='black', linestyle='-', alpha=0.3)
    plt.title('收益率对比', fontsize=14, fontweight='bold')
    plt.xlabel('日期')
    plt.ylabel('收益率 (%)')
    plt.legend()
    plt.grid(True, alpha=0.3)
    ax4.xaxis.set_major_formatter(mdates.DateFormatter('%Y'))
    ax4.xaxis.set_major_locator(mdates.YearLocator(2))
    plt.xticks(rotation=45)
    
    plt.tight_layout()
    
    # 保存图表
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    chart_filename = f"HSI50策略对比图表_{timestamp}.png"
    plt.savefig(chart_filename, dpi=300, bbox_inches='tight')
    print(f"✅ 图表已保存至: {chart_filename}")
    
    plt.show()
    
    return chart_filename

def analyze_final_results(results_df):
    """分析最终结果"""
    print("\n📊 20年投资策略对比分析")
    print("=" * 60)
    
    final_row = results_df.iloc[-1]
    
    # 策略1：极少持仓+每月3K
    strategy1_invested = final_row['strategy1_invested']
    strategy1_value = final_row['strategy1_value']
    strategy1_return = strategy1_value - strategy1_invested
    strategy1_return_rate = (strategy1_return / strategy1_invested) * 100
    strategy1_annual = ((strategy1_value / strategy1_invested) ** (1/20) - 1) * 100
    
    # 策略2：恒指直投+每月3K
    strategy2_invested = final_row['strategy2_invested']
    strategy2_value = final_row['strategy2_value']
    strategy2_return = strategy2_value - strategy2_invested
    strategy2_return_rate = (strategy2_return / strategy2_invested) * 100
    strategy2_annual = ((strategy2_value / strategy2_invested) ** (1/20) - 1) * 100
    
    # 策略3：纯定投恒指
    strategy3_invested = final_row['strategy3_invested']
    strategy3_value = final_row['strategy3_value']
    strategy3_return = strategy3_value - strategy3_invested
    strategy3_return_rate = (strategy3_return / strategy3_invested) * 100
    strategy3_annual = ((strategy3_value / strategy3_invested) ** (1/20) - 1) * 100
    
    print(f"💰 策略1：极少持仓+每月3K (纯现金)")
    print(f"• 总投入: {strategy1_invested:,.0f} 港元")
    print(f"• 最终价值: {strategy1_value:,.0f} 港元")
    print(f"• 总收益: {strategy1_return:,.0f} 港元")
    print(f"• 总收益率: {strategy1_return_rate:.2f}%")
    print(f"• 年化收益率: {strategy1_annual:.2f}%")
    
    print(f"\n📈 策略2：恒指直投+每月3K")
    print(f"• 总投入: {strategy2_invested:,.0f} 港元")
    print(f"• 最终价值: {strategy2_value:,.0f} 港元")
    print(f"• 总收益: {strategy2_return:,.0f} 港元")
    print(f"• 总收益率: {strategy2_return_rate:.2f}%")
    print(f"• 年化收益率: {strategy2_annual:.2f}%")
    
    print(f"\n📊 策略3：纯定投恒指")
    print(f"• 总投入: {strategy3_invested:,.0f} 港元")
    print(f"• 最终价值: {strategy3_value:,.0f} 港元")
    print(f"• 总收益: {strategy3_return:,.0f} 港元")
    print(f"• 总收益率: {strategy3_return_rate:.2f}%")
    print(f"• 年化收益率: {strategy3_annual:.2f}%")
    
    print(f"\n🏆 策略排名（按总收益率）:")
    strategies = [
        ("极少持仓+每月3K", strategy1_return_rate, strategy1_annual),
        ("恒指直投+每月3K", strategy2_return_rate, strategy2_annual),
        ("纯定投恒指", strategy3_return_rate, strategy3_annual)
    ]
    strategies.sort(key=lambda x: x[1], reverse=True)
    
    for i, (name, total_return, annual_return) in enumerate(strategies, 1):
        print(f"{i}. {name}: {total_return:.2f}% (年化{annual_return:.2f}%)")

def main():
    """主函数"""
    print("🏢 HSI50策略对比分析系统")
    print("=" * 60)
    print("💰 初始资金: 30,000港元")
    print("📅 每月定投: 3,000港元")
    print("📊 分析周期: 20年历史数据")
    print("🎯 对比策略:")
    print("   1. 极少持仓+每月3K (纯现金)")
    print("   2. 恒指直投+每月3K")
    print("   3. 纯定投恒指")
    
    # 获取数据
    data = fetch_hsi_data()
    if data is None:
        return
    
    # 模拟策略
    results_df = simulate_strategies(data)
    
    # 分析结果
    analyze_final_results(results_df)
    
    # 创建图表
    create_comparison_chart(results_df)

if __name__ == "__main__":
    main()
