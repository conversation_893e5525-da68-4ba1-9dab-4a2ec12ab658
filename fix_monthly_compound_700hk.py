#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
修正腾讯700HK每月3000复利计算
============================

确保每月3000港元追加资金的复利效果正确体现

作者: Cosmoon NG
"""

import pandas as pd
import mysql.connector
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

def create_compound_corrected_records():
    """创建包含正确复利计算的交易记录"""
    
    print("💰 修正腾讯700HK每月3000复利计算...")
    print("=" * 60)
    
    # 参数设置
    initial_capital = 20000.00
    monthly_addition = 3000.00
    kelly_fraction = 0.25
    commission_rate = 0.001
    
    # 止盈止损率
    long_take_profit = 0.012
    long_stop_loss = 0.006
    short_take_profit = 0.006
    short_stop_loss = 0.012
    
    print(f"📊 复利参数:")
    print(f"   初始资本: {initial_capital:,.2f}港元")
    print(f"   每月追加: {monthly_addition:,.2f}港元")
    print(f"   凯利比例: {kelly_fraction*100:.0f}%")
    print(f"   复利效果: 每月追加资金参与后续投资")
    
    # 从数据库获取数据
    config = {
        'host': 'localhost',
        'port': 3306,
        'user': 'root',
        'password': '12345678',
        'database': 'finance'
    }
    
    try:
        connection = mysql.connector.connect(**config)
        
        df = pd.read_sql("""
            SELECT Date, Close, Y_Value, X_Value, E_Value, Full_Y, MoneyFlowRatio, MyE
            FROM eab_0700hk
            ORDER BY Date
        """, connection)
        
        connection.close()
        
        print(f"   📊 从数据库读取 {len(df)} 条记录")
        
        # 创建交易记录
        trading_records = []
        
        # 初始状态
        total_capital = initial_capital  # 总资本 (包括追加的)
        available_cash = initial_capital  # 可用现金
        position = 0
        entry_price = 0
        shares = 0
        trade_count = 0
        months_passed = 0
        last_month = 0
        
        for i, row in df.iterrows():
            current_date = row['Date']
            current_price = row['Close']
            y_value = row['Y_Value']
            x_value = row['X_Value']
            e_value = row['E_Value']
            full_y = row['Full_Y']
            mfr = row['MoneyFlowRatio']
            mye = row['MyE']
            
            # 计算月份 (每22个交易日约1个月)
            current_month = i // 22
            
            # 每月追加资金
            if current_month > last_month and current_month > 0:
                total_capital += monthly_addition
                available_cash += monthly_addition
                months_passed += 1
                last_month = current_month
                print(f"   💰 第{months_passed}个月追加 {monthly_addition:,.0f}港元，总资本: {total_capital:,.0f}港元")
            
            # 交易逻辑
            signal = "观望"
            trade_type = "持仓"
            trade_direction = "空仓"
            
            if position == 0:  # 空仓状态
                # 计算当前可投资金额 (基于总资本的凯利比例)
                investment_amount = total_capital * kelly_fraction
                
                if e_value > 0 and x_value > 0.45 and y_value > 0.45:
                    # 多头开仓
                    signal = "强烈买入"
                    trade_type = "开仓"
                    trade_direction = "多头"
                    position = 1
                    entry_price = current_price
                    
                    # 使用凯利公式计算投资金额 (基于总资本)
                    shares = int(investment_amount / current_price) if current_price > 0 else 0
                    if shares > 0:
                        cost = shares * current_price * (1 + commission_rate)
                        if cost <= available_cash:
                            available_cash -= cost
                            trade_count += 1
                        else:
                            shares = 0  # 资金不足
                            
                elif (y_value < 0.3 or x_value < 0.3):
                    # 空头开仓
                    signal = "强烈卖出"
                    trade_type = "开仓"
                    trade_direction = "空头"
                    position = -1
                    entry_price = current_price
                    
                    # 使用凯利公式计算投资金额
                    shares = int(investment_amount / current_price) if current_price > 0 else 0
                    if shares > 0:
                        cost = shares * current_price * commission_rate
                        if cost <= available_cash:
                            available_cash -= cost
                            trade_count += 1
                        else:
                            shares = 0
                            
            else:  # 有持仓
                price_change = (current_price - entry_price) / entry_price if entry_price > 0 else 0
                
                if position == 1:  # 多头持仓
                    trade_direction = "多头"
                    if price_change >= long_take_profit:
                        signal = "止盈平仓"
                        trade_type = "平仓"
                        if shares > 0:
                            proceeds = shares * current_price * (1 - commission_rate)
                            available_cash += proceeds
                            # 更新总资本 (体现盈利的复利效果)
                            profit = proceeds - (shares * entry_price * (1 + commission_rate))
                            total_capital += profit
                        position = 0
                        shares = 0
                    elif price_change <= -long_stop_loss:
                        signal = "止损平仓"
                        trade_type = "平仓"
                        if shares > 0:
                            proceeds = shares * current_price * (1 - commission_rate)
                            available_cash += proceeds
                            # 更新总资本 (体现亏损)
                            loss = proceeds - (shares * entry_price * (1 + commission_rate))
                            total_capital += loss
                        position = 0
                        shares = 0
                    else:
                        signal = "持有多头"
                        
                elif position == -1:  # 空头持仓
                    trade_direction = "空头"
                    if price_change <= -short_take_profit:
                        signal = "止盈平仓"
                        trade_type = "平仓"
                        if shares > 0:
                            profit = shares * (entry_price - current_price) * (1 - commission_rate)
                            available_cash += profit
                            total_capital += profit
                        position = 0
                        shares = 0
                    elif price_change >= short_stop_loss:
                        signal = "止损平仓"
                        trade_type = "平仓"
                        if shares > 0:
                            loss = shares * (entry_price - current_price) * (1 - commission_rate)
                            available_cash += loss
                            total_capital += loss
                        position = 0
                        shares = 0
                    else:
                        signal = "持有空头"
            
            # 计算当前状态
            if position != 0 and shares > 0:
                if position == 1:
                    unrealized_pnl = shares * (current_price - entry_price)
                    current_market_value = shares * current_price
                else:
                    unrealized_pnl = shares * (entry_price - current_price)
                    current_market_value = shares * current_price
                total_assets = available_cash + current_market_value + unrealized_pnl
            else:
                unrealized_pnl = 0
                current_market_value = 0
                total_assets = available_cash
            
            # 计算收益率 (基于总投入资本)
            total_invested = initial_capital + months_passed * monthly_addition
            daily_return = (total_assets - total_invested) / total_invested * 100 if total_invested > 0 else 0
            cumulative_return = (total_assets - initial_capital) / initial_capital * 100
            
            # 止盈止损价格
            if position == 1:
                take_profit_price = entry_price * (1 + long_take_profit) if entry_price > 0 else current_price * (1 + long_take_profit)
                stop_loss_price = entry_price * (1 - long_stop_loss) if entry_price > 0 else current_price * (1 - long_stop_loss)
            elif position == -1:
                take_profit_price = entry_price * (1 + short_take_profit) if entry_price > 0 else current_price * (1 + short_take_profit)
                stop_loss_price = entry_price * (1 - short_stop_loss) if entry_price > 0 else current_price * (1 - short_stop_loss)
            else:
                take_profit_price = current_price * (1 + long_take_profit)
                stop_loss_price = current_price * (1 - long_stop_loss)
            
            # 风险等级
            if abs(e_value) > 0.5:
                risk_level = "高风险"
            elif abs(e_value) > 0.2:
                risk_level = "中风险"
            else:
                risk_level = "低风险"
            
            # 创建记录
            record = {
                '交易日期': current_date.strftime('%Y-%m-%d') if hasattr(current_date, 'strftime') else str(current_date),
                '交易类型': trade_type,
                '交易方向': trade_direction,
                '交易价格': round(current_price, 2),
                '入场价格': round(entry_price, 2) if entry_price > 0 else round(current_price, 2),
                '止盈价': round(take_profit_price, 2),
                '止损价': round(stop_loss_price, 2),
                '持仓数量': shares,
                '交易金额': round(shares * current_price, 2) if shares > 0 else 0,
                '手续费': round(shares * current_price * commission_rate, 2) if shares > 0 else 0,
                '净交易额': round(shares * current_price * (1 - commission_rate), 2) if shares > 0 else 0,
                '持仓成本': round(entry_price, 2) if entry_price > 0 else 0,
                '当前市值': round(current_market_value, 2),
                '浮动盈亏': round(unrealized_pnl, 2),
                '实现盈亏': 0.00,
                '累计盈亏': round(unrealized_pnl, 2),
                '账户余额': round(available_cash, 2),
                '总资产': round(total_assets, 2),
                '收益率': round(daily_return, 2),
                '累计收益率': round(cumulative_return, 2),
                'Y值': round(y_value, 4),
                'Full_Y': round(full_y, 4),
                'X值': round(x_value, 4),
                'MoneyFlowRatio': round(mfr, 4),
                'E值': round(e_value, 4),
                'MyE': round(mye, 4),
                '信号强度': signal,
                '风险等级': risk_level,
                '备注': f'复利版 总资本{total_capital:,.0f} 月数{months_passed} 投资{total_capital*kelly_fraction:,.0f} {signal}'
            }
            
            trading_records.append(record)
        
        # 创建DataFrame
        final_df = pd.DataFrame(trading_records)
        
        # 保存Excel文件
        filename = f'交易记录追踪0700HK_复利修正版_{datetime.now().strftime("%Y%m%d_%H%M%S")}.xlsx'
        final_df.to_excel(filename, index=False)
        
        print(f"\n✅ 复利修正版Excel已导出: {filename}")
        print(f"📊 包含 {len(final_df)} 条记录，29个完整字段")
        
        # 显示复利效果
        final_record = trading_records[-1]
        total_months = months_passed
        total_added = total_months * monthly_addition
        
        print(f"\n💰 复利效果总结:")
        print(f"   初始资本: {initial_capital:,.2f}港元")
        print(f"   追加月数: {total_months}个月")
        print(f"   总追加金额: {total_added:,.2f}港元")
        print(f"   总投入: {initial_capital + total_added:,.2f}港元")
        print(f"   最终资产: {final_record['总资产']:,.2f}港元")
        print(f"   净收益: {final_record['总资产'] - (initial_capital + total_added):,.2f}港元")
        print(f"   总收益率: {(final_record['总资产'] / (initial_capital + total_added) - 1) * 100:.2f}%")
        print(f"   相对初始资本倍数: {final_record['总资产'] / initial_capital:.2f}倍")
        
        # 显示每月投资额变化
        print(f"\n📈 凯利投资额变化:")
        print(f"   第1个月: {initial_capital * kelly_fraction:,.0f}港元")
        print(f"   第12个月: {(initial_capital + 12*monthly_addition) * kelly_fraction:,.0f}港元")
        print(f"   第24个月: {(initial_capital + 24*monthly_addition) * kelly_fraction:,.0f}港元")
        print(f"   最后: {total_capital * kelly_fraction:,.0f}港元")
        
        return filename, final_df
        
    except Exception as e:
        print(f"❌ 复利修正失败: {e}")
        return None, None

def main():
    """主函数"""
    
    print("💰 腾讯700HK每月3000复利修正系统")
    print("=" * 60)
    print("🔄 修正内容:")
    print("   1. 每月追加3000港元到总资本")
    print("   2. 凯利投资额基于总资本计算")
    print("   3. 盈亏影响总资本 (复利效果)")
    print("   4. 正确体现资金增长的复利威力")
    
    filename, df = create_compound_corrected_records()
    
    if filename:
        print(f"\n🎉 复利修正完成!")
        print(f"✅ 文件: {filename}")
        print(f"💰 每月3000港元复利效果已正确体现")
        print(f"📈 投资金额随总资本增长而增长")
        print(f"🔄 完整的复利增长模式")
    else:
        print(f"\n❌ 复利修正失败")

if __name__ == "__main__":
    main()
