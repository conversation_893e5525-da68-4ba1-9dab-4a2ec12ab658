#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Cosmoon数据库策略回测
===================

使用数据库中的Y和X值进行策略回测
选择一个有数据的香港股票进行测试

作者: Cosmoon NG
日期: 2025年7月
"""

import pymysql
import pandas as pd
import numpy as np
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

class CosmoonDBStrategy:
    def __init__(self):
        """初始化数据库策略"""
        self.db_config = {
            'host': 'localhost',
            'port': 3306,
            'database': 'finance',
            'user': 'root',
            'password': '12345678',
            'charset': 'utf8mb4'
        }
        
        # 策略参数
        self.strategy_params = {
            'initial_capital': 8000,
            'kelly_win_rate': 0.6,
            'kelly_win_ratio': 2,
            'kelly_loss_ratio': 1,
            'max_position_ratio': 0.2,
            'max_total_positions': 3,
            
            # 止盈止损
            'long_take_profit': 0.04,
            'long_stop_loss': 0.02,
            'short_take_profit': 0.04,
            'short_stop_loss': 0.02,
        }
        
        self.data = None
        self.trades = []
        self.current_positions = []
    
    def connect_database(self):
        """连接数据库"""
        try:
            self.connection = pymysql.connect(**self.db_config)
            print("✅ 数据库连接成功")
            return True
        except Exception as e:
            print(f"❌ 数据库连接失败: {e}")
            return False
    
    def find_best_table(self):
        """找到最适合的表进行回测"""
        print("🔍 查找最适合的数据表...")
        
        # 候选表列表（香港股票优先）
        candidate_tables = [
            'stock_0005_hk',  # 汇丰控股
            'stock_0700_hk',  # 腾讯控股
            'stock_0001_hk',  # 长和
            'stock_0002_hk',  # 中电控股
            'stock_0003_hk',  # 香港中华煤气
            'hsbc_0005',      # 汇丰控股备用表
        ]
        
        for table in candidate_tables:
            try:
                # 检查表的数据质量
                query = f"""
                    SELECT COUNT(*) as count,
                           MIN(date) as min_date,
                           MAX(date) as max_date,
                           COUNT(CASE WHEN y_probability IS NOT NULL AND inflow_ratio IS NOT NULL THEN 1 END) as valid_count
                    FROM {table}
                """
                
                result = pd.read_sql(query, self.connection)
                
                if result['count'].iloc[0] > 1000 and result['valid_count'].iloc[0] > 1000:
                    print(f"✅ 选择表: {table}")
                    print(f"   • 总记录数: {result['count'].iloc[0]:,}")
                    print(f"   • 有效记录数: {result['valid_count'].iloc[0]:,}")
                    print(f"   • 数据期间: {result['min_date'].iloc[0]} 至 {result['max_date'].iloc[0]}")
                    return table
                    
            except Exception as e:
                print(f"   ❌ 表 {table} 不可用: {e}")
                continue
        
        print("❌ 未找到合适的数据表")
        return None
    
    def load_data(self, table_name):
        """加载数据"""
        print(f"📊 从表 {table_name} 加载数据...")
        
        try:
            query = f"""
                SELECT 
                    date,
                    close,
                    high,
                    low,
                    volume,
                    y_probability,
                    inflow_ratio
                FROM {table_name}
                WHERE date >= DATE_SUB(CURDATE(), INTERVAL 20 YEAR)
                AND y_probability IS NOT NULL 
                AND inflow_ratio IS NOT NULL
                AND close > 0
                ORDER BY date ASC
            """
            
            self.data = pd.read_sql(query, self.connection)
            
            if self.data.empty:
                print("❌ 未找到可用数据")
                return False
            
            # 数据预处理
            self.data['date'] = pd.to_datetime(self.data['date'])
            self.data = self.data.sort_values('date').reset_index(drop=True)
            
            # 计算E值和策略区域
            self.data['e_value'] = (8 * self.data['inflow_ratio'] * self.data['y_probability'] - 
                                   3 * self.data['inflow_ratio'] - 3 * self.data['y_probability'] + 1)
            
            # 标记策略区域
            conditions = [
                (self.data['y_probability'] > 0.5) & (self.data['inflow_ratio'] > 0.5),  # 高值盈利区
                (self.data['y_probability'] > 0.333) & (self.data['y_probability'] < 0.4),  # 控股商控制区
                (self.data['y_probability'] < 0.25) | (self.data['inflow_ratio'] < 0.25),  # 强亏损区
            ]
            
            choices = ['HIGH_PROFIT', 'CONTROL_ZONE', 'STRONG_LOSS']
            self.data['strategy_zone'] = np.select(conditions, choices, default='OTHER')
            
            print(f"✅ 数据加载完成:")
            print(f"   • 数据期间: {self.data['date'].min().strftime('%Y-%m-%d')} 至 {self.data['date'].max().strftime('%Y-%m-%d')}")
            print(f"   • 总记录数: {len(self.data)} 条")
            
            # 统计各区域分布
            zone_counts = self.data['strategy_zone'].value_counts()
            total = len(self.data)
            print(f"📊 策略区域分布:")
            for zone, count in zone_counts.items():
                print(f"   • {zone}: {count} 天 ({count/total*100:.1f}%)")
            
            # 显示Y和X值统计
            print(f"📈 Y和X值统计:")
            print(f"   • Y值范围: {self.data['y_probability'].min():.3f} - {self.data['y_probability'].max():.3f}")
            print(f"   • X值范围: {self.data['inflow_ratio'].min():.3f} - {self.data['inflow_ratio'].max():.3f}")
            print(f"   • E>0天数: {(self.data['e_value'] > 0).sum()} 天 ({(self.data['e_value'] > 0).sum()/total*100:.1f}%)")
            
            return True
            
        except Exception as e:
            print(f"❌ 数据加载失败: {e}")
            return False
    
    def calculate_kelly_position(self, win_rate_modifier=1.0):
        """计算凯利公式仓位"""
        win_rate = self.strategy_params['kelly_win_rate'] * win_rate_modifier
        win_ratio = self.strategy_params['kelly_win_ratio']
        loss_ratio = self.strategy_params['kelly_loss_ratio']
        
        b = win_ratio / loss_ratio
        p = win_rate
        q = 1 - p
        
        kelly_fraction = (b * p - q) / b
        return max(0, min(kelly_fraction, self.strategy_params['max_position_ratio']))
    
    def check_stop_conditions(self, position, current_price):
        """检查止盈止损"""
        entry_price = position['entry_price']
        direction = position['direction']
        
        if direction == 'LONG':
            profit_pct = (current_price - entry_price) / entry_price
            if profit_pct >= self.strategy_params['long_take_profit']:
                return 'TAKE_PROFIT'
            elif profit_pct <= -self.strategy_params['long_stop_loss']:
                return 'STOP_LOSS'
        else:  # SHORT
            profit_pct = (entry_price - current_price) / entry_price
            if profit_pct >= self.strategy_params['short_take_profit']:
                return 'TAKE_PROFIT'
            elif profit_pct <= -self.strategy_params['short_stop_loss']:
                return 'STOP_LOSS'
        
        return None
    
    def execute_trade(self, date, price, direction, zone, kelly_modifier=1.0):
        """执行交易"""
        kelly_fraction = self.calculate_kelly_position(kelly_modifier)
        position_value = self.current_cash * kelly_fraction
        shares = position_value / price
        
        if shares > 0 and position_value > 100:
            self.current_cash -= position_value
            
            new_position = {
                'entry_date': date,
                'entry_price': price,
                'shares': shares,
                'direction': direction,
                'zone': zone,
                'position_value': position_value
            }
            
            self.current_positions.append(new_position)
            return True
        return False
    
    def close_position(self, position_index, date, price, reason):
        """平仓"""
        position = self.current_positions[position_index]
        direction = position['direction']
        entry_price = position['entry_price']
        shares = position['shares']
        
        if direction == 'LONG':
            profit = (price - entry_price) * shares
            exit_value = shares * price
        else:  # SHORT
            profit = (entry_price - price) * shares
            exit_value = shares * price
        
        self.current_cash += exit_value
        
        trade_record = {
            'entry_date': position['entry_date'],
            'exit_date': date,
            'direction': direction,
            'entry_price': entry_price,
            'exit_price': price,
            'shares': shares,
            'profit': profit,
            'profit_pct': profit / (shares * entry_price) * 100,
            'reason': reason,
            'zone': position['zone'],
            'holding_days': (date - position['entry_date']).days
        }
        
        self.trades.append(trade_record)
        del self.current_positions[position_index]
        
        return profit > 0
    
    def run_backtest(self):
        """运行回测"""
        print(f"\n🚀 开始Cosmoon数据库策略回测...")
        print("="*70)
        print(f"💰 初始资金: {self.strategy_params['initial_capital']:,} 港币")
        print(f"🎯 使用数据库中的Y和X值")
        print("="*70)
        
        self.current_cash = self.strategy_params['initial_capital']
        winning_trades = 0
        losing_trades = 0
        
        for i, row in self.data.iterrows():
            date = row['date']
            price = row['close']
            zone = row['strategy_zone']
            y_val = row['y_probability']
            x_val = row['inflow_ratio']
            
            # 检查现有持仓的止盈止损
            positions_to_close = []
            for j, position in enumerate(self.current_positions):
                stop_reason = self.check_stop_conditions(position, price)
                if stop_reason:
                    positions_to_close.append((j, stop_reason))
            
            # 平仓处理
            for j, reason in reversed(positions_to_close):
                is_winning = self.close_position(j, date, price, reason)
                if is_winning:
                    winning_trades += 1
                else:
                    losing_trades += 1
            
            # 开仓逻辑
            current_position_count = len(self.current_positions)
            
            if zone == 'HIGH_PROFIT' and current_position_count < self.strategy_params['max_total_positions']:
                # 高值盈利区：买涨
                self.execute_trade(date, price, 'LONG', zone, kelly_modifier=1.0)
                
            elif zone == 'STRONG_LOSS' and current_position_count < self.strategy_params['max_total_positions']:
                # 强亏损区：买涨（低位反弹）
                self.execute_trade(date, price, 'LONG', zone, kelly_modifier=0.8)
                
            elif zone == 'OTHER' and current_position_count < self.strategy_params['max_total_positions']:
                # 其他区域：买跌
                self.execute_trade(date, price, 'SHORT', zone, kelly_modifier=0.7)
        
        # 最终清仓
        final_price = self.data['close'].iloc[-1]
        final_date = self.data['date'].iloc[-1]
        
        for i in range(len(self.current_positions)):
            is_winning = self.close_position(0, final_date, final_price, 'FINAL_EXIT')
            if is_winning:
                winning_trades += 1
            else:
                losing_trades += 1
        
        # 计算结果
        final_value = self.current_cash
        total_return = (final_value / self.strategy_params['initial_capital'] - 1) * 100
        years = len(self.data) / 252
        annual_return = (final_value / self.strategy_params['initial_capital']) ** (1/years) - 1
        
        total_trades = len(self.trades)
        win_rate = winning_trades / total_trades * 100 if total_trades > 0 else 0
        
        # 统计各类型交易
        long_trades = len([t for t in self.trades if t['direction'] == 'LONG'])
        short_trades = len([t for t in self.trades if t['direction'] == 'SHORT'])
        
        print(f"\n✅ 数据库策略回测完成!")
        print(f"📊 交易统计:")
        print(f"   • 总交易次数: {total_trades}")
        print(f"   • 做多交易: {long_trades} 次")
        print(f"   • 做空交易: {short_trades} 次")
        print(f"   • 盈利交易: {winning_trades}")
        print(f"   • 亏损交易: {losing_trades}")
        print(f"   • 实际胜率: {win_rate:.1f}%")
        
        print(f"\n💰 收益统计:")
        print(f"   • 初始资金: {self.strategy_params['initial_capital']:,} 港币")
        print(f"   • 最终资金: {final_value:,.0f} 港币")
        print(f"   • 总收益: {final_value - self.strategy_params['initial_capital']:+,.0f} 港币")
        print(f"   • 总收益率: {total_return:+.2f}%")
        print(f"   • 年化收益率: {annual_return*100:.2f}%")
        
        # 分析买跌效果
        if short_trades > 0:
            short_profits = [t['profit'] for t in self.trades if t['direction'] == 'SHORT']
            short_total_profit = sum(short_profits)
            short_winning = len([p for p in short_profits if p > 0])
            short_win_rate = short_winning / short_trades * 100
            
            print(f"\n📉 买跌策略分析:")
            print(f"   • 做空交易次数: {short_trades}")
            print(f"   • 做空胜率: {short_win_rate:.1f}%")
            print(f"   • 做空总盈亏: {short_total_profit:+.0f} 港币")
            print(f"   • 做空平均盈亏: {short_total_profit/short_trades:+.0f} 港币/次")

def main():
    """主函数"""
    print("🎯 Cosmoon数据库策略回测")
    print("="*60)
    print("📅 启动时间:", datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
    print("💾 使用数据库中的真实Y和X值")
    
    # 创建策略实例
    strategy = CosmoonDBStrategy()
    
    # 连接数据库
    if not strategy.connect_database():
        return
    
    # 找到最适合的表
    table_name = strategy.find_best_table()
    if not table_name:
        return
    
    # 加载数据
    if not strategy.load_data(table_name):
        return
    
    # 运行回测
    strategy.run_backtest()
    
    print(f"\n🎉 数据库策略回测完成!")
    print(f"💡 使用了数据库中真实的Y和X值！")

if __name__ == "__main__":
    main()
