#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
东亚银行0023.HK每日交易记录追踪系统
每日下午5:00香港交易所结束后自动填充
"""

import pandas as pd
import yfinance as yf
import numpy as np
from datetime import datetime, timedelta
from scipy import stats
import os
import warnings
warnings.filterwarnings('ignore')

class DailyTracker0023HK:
    """每日交易记录追踪器"""

    def __init__(self):
        self.symbol = "0023.HK"
        self.excel_file = "交易记录追踪0023HK.xlsx"

        # 交易参数
        self.take_profit_long = 0.025   # 多头止盈2.5%
        self.stop_loss_long = 0.015     # 多头止损1.5%
        self.take_profit_short = 0.015  # 空头止盈1.5%
        self.stop_loss_short = 0.025    # 空头止损2.5%

        # 当前状态
        self.position = 0  # 0=空仓 1=多头 -1=空头
        self.entry_price = 0
        self.capital = 10000

        print(f"0023.HK每日交易记录追踪系统")
        print(f"填充日期: {datetime.now().strftime('%Y年%m月%d日')}")

    def get_market_data(self):
        """获取市场数据"""
        try:
            ticker = yf.Ticker(self.symbol)
            # 获取最近30天数据用于计算技术指标
            hist = ticker.history(period="30d")

            if hist.empty:
                raise ValueError("无法获取市场数据")

            # 计算技术指标
            self.calculate_indicators(hist)

            # 获取今日数据
            today_data = hist.iloc[-1]
            today_indicators = {
                'y_value': hist['y_value'].iloc[-1],
                'x_value': hist['x_value'].iloc[-1],
                'e_value': hist['e_value'].iloc[-1],
                'price_position': hist['price_position'].iloc[-1]
            }

            return today_data, today_indicators

        except Exception as e:
            print(f"获取数据失败: {e}")
            return None, None

    def calculate_indicators(self, df):
        """计算XYE技术指标"""
        # Y值: 价格在20日区间位置
        window = 20
        df['high_20'] = df['High'].rolling(window).max()
        df['low_20'] = df['Low'].rolling(window).min()
        df['y_value'] = (df['Close'] - df['low_20']) / (df['high_20'] - df['low_20'])
        df['y_value'] = df['y_value'].fillna(0.5).clip(0, 1)

        # X值: 资金流强度
        df['typical_price'] = (df['High'] + df['Low'] + df['Close']) / 3
        df['money_flow'] = df['typical_price'] * df['Volume']
        df['price_change'] = df['typical_price'].diff()

        df['positive_mf'] = np.where(df['price_change'] > 0, df['money_flow'], 0)
        df['negative_mf'] = np.where(df['price_change'] < 0, df['money_flow'], 0)

        period = 14
        df['positive_mf_14'] = df['positive_mf'].rolling(period).sum()
        df['negative_mf_14'] = df['negative_mf'].rolling(period).sum()
        df['money_flow_ratio'] = df['positive_mf_14'] / (df['negative_mf_14'] + 1e-10)

        df['mfi'] = 100 - (100 / (1 + df['money_flow_ratio']))
        df['x_value'] = df['mfi'] / 100

        # E值: Cosmoon公式
        df['e_value'] = (8 * df['x_value'] - 3) * df['y_value'] - 3 * df['x_value'] + 1

        # 回归线
        df['i'] = range(1, len(df) + 1)
        if len(df) > 20:
            slope, intercept, _, _, _ = stats.linregress(df['i'], df['Close'])
            df['regression_line'] = intercept + slope * df['i']
            df['price_position'] = (df['Close'] - df['regression_line']) / df['regression_line']

    def generate_signal(self, indicators):
        """生成交易信号"""
        y = indicators['y_value']
        x = indicators['x_value']
        e = indicators['e_value']
        pos = indicators.get('price_position', 0)

        # 多头信号
        if e > 0 and x > 0.45 and y > 0.45 and pos < 0:
            return 1, "强烈买入"
        # 空头信号
        elif ((y < 0.3 or x < 0.3 or
               (x > 0.45 and y < 0.35) or
               (x < 0.45 and y > 0.35)) and pos > 0):
            return -1, "强烈卖出"
        else:
            return 0, "观望"

    def check_stop_conditions(self, current_price):
        """检查止盈止损"""
        if self.position == 0:
            return False, ""

        if self.position == 1:  # 多头
            profit_ratio = (current_price - self.entry_price) / self.entry_price
            if profit_ratio >= self.take_profit_long:
                return True, "多头止盈"
            elif profit_ratio <= -self.stop_loss_long:
                return True, "多头止损"

        elif self.position == -1:  # 空头
            profit_ratio = (self.entry_price - current_price) / self.entry_price
            if profit_ratio >= self.take_profit_short:
                return True, "空头止盈"
            elif profit_ratio <= -self.stop_loss_short:
                return True, "空头止损"

        return False, ""

    def get_position_status_details(self):
        """获取详细的持仓状态信息"""
        if self.position == 0:
            return {
                'status': '空仓',
                'direction': '无',
                'holding_days': 0,
                'position_ratio': 0.0,
                'pnl_ratio': 0.0,
                'take_profit_distance': 0.0,
                'stop_loss_distance': 0.0
            }

        # 计算持仓天数（这里简化为1天，实际应该从开仓日期计算）
        holding_days = 1  # 可以从Excel记录中获取实际开仓日期

        # 计算持仓比例（当前市值占总资产的比例）
        current_price = self.entry_price  # 这里应该是当前价格
        market_value = self.capital * 0.8  # 假设80%资金用于持仓
        position_ratio = market_value / (self.capital + market_value) * 100

        # 计算盈亏比例
        if self.position == 1:  # 多头
            pnl_ratio = (current_price - self.entry_price) / self.entry_price * 100
            take_profit_distance = (self.entry_price * (1 + self.take_profit_long) - current_price) / current_price * 100
            stop_loss_distance = (current_price - self.entry_price * (1 - self.stop_loss_long)) / current_price * 100
        else:  # 空头
            pnl_ratio = (self.entry_price - current_price) / self.entry_price * 100
            take_profit_distance = (current_price - self.entry_price * (1 - self.take_profit_short)) / current_price * 100
            stop_loss_distance = (self.entry_price * (1 + self.stop_loss_short) - current_price) / current_price * 100

        direction = '多头' if self.position == 1 else '空头'
        status = f'持仓{direction}'

        return {
            'status': status,
            'direction': direction,
            'holding_days': holding_days,
            'position_ratio': round(position_ratio, 2),
            'pnl_ratio': round(pnl_ratio, 2),
            'take_profit_distance': round(max(0, take_profit_distance), 2),
            'stop_loss_distance': round(max(0, stop_loss_distance), 2)
        }

    def load_existing_records(self):
        """加载现有记录"""
        try:
            if os.path.exists(self.excel_file):
                df = pd.read_excel(self.excel_file)
                if len(df) > 0:
                    # 获取最新状态
                    latest = df.iloc[-1]
                    self.capital = latest.get('账户余额', 10000)

                    # 判断持仓状态
                    if latest.get('交易类型') == '开仓':
                        if '多头' in str(latest.get('交易方向', '')):
                            self.position = 1
                        elif '空头' in str(latest.get('交易方向', '')):
                            self.position = -1
                        self.entry_price = latest.get('交易价格', 0)

                return df
            else:
                return pd.DataFrame()
        except:
            return pd.DataFrame()

    def fill_today_record(self):
        """填充今日记录"""
        print("开始填充今日交易记录...")

        # 获取数据
        today_data, indicators = self.get_market_data()
        if today_data is None:
            print("无法获取数据，跳过填充")
            return

        # 加载现有记录
        existing_df = self.load_existing_records()

        current_price = today_data['Close']

        # 检查止盈止损
        should_close, close_reason = self.check_stop_conditions(current_price)

        # 生成信号
        signal, signal_strength = self.generate_signal(indicators)

        # 决定交易动作
        trade_type = "观察"
        trade_direction = "无"
        realized_pnl = 0
        note = f"市场观察收盘价{current_price:.2f}港元"

        if should_close:
            # 平仓
            trade_type = "平仓"
            trade_direction = close_reason
            if self.position == 1:
                profit_ratio = (current_price - self.entry_price) / self.entry_price
            else:
                profit_ratio = (self.entry_price - current_price) / self.entry_price
            realized_pnl = profit_ratio * self.capital * 0.8
            self.capital += realized_pnl
            self.position = 0
            note = f"{close_reason}平仓盈亏{realized_pnl:.2f}港元"

        elif self.position == 0 and signal != 0:
            # 开仓
            trade_type = "开仓"
            trade_direction = "多头" if signal == 1 else "空头"
            self.position = signal
            self.entry_price = current_price
            note = f"{signal_strength}开仓价格{current_price:.2f}港元"

        # 计算持仓相关数据
        if self.position != 0:
            quantity = int(self.capital * 0.8 / current_price)
            trade_amount = current_price * quantity
            current_market_value = trade_amount
            if self.position == 1:
                unrealized_pnl = (current_price - self.entry_price) * quantity
            else:
                unrealized_pnl = (self.entry_price - current_price) * quantity
        else:
            quantity = 0
            trade_amount = 0
            current_market_value = 0
            unrealized_pnl = 0

        commission = trade_amount * 0.001 if trade_type in ["开仓", "平仓"] else 0
        net_amount = trade_amount - commission
        total_assets = self.capital + current_market_value

        # 计算收益率
        if len(existing_df) > 0:
            prev_total = existing_df['总资产'].iloc[-1]
            daily_return = (total_assets - prev_total) / prev_total * 100
        else:
            daily_return = 0

        cumulative_return = (total_assets - 10000) / 10000 * 100

        # 风险等级
        volatility = abs(indicators.get('price_position', 0))
        if volatility < 0.02:
            risk_level = "低风险"
        elif volatility < 0.05:
            risk_level = "中风险"
        elif volatility < 0.1:
            risk_level = "高风险"
        else:
            risk_level = "极高风险"

        # 计算持仓状态详情
        position_status = self.get_position_status_details()

        # 创建今日记录
        record = {
            '交易日期': datetime.now().strftime('%Y-%m-%d'),
            '交易类型': trade_type,
            '交易方向': trade_direction,
            '交易价格': current_price,
            '持仓数量': quantity,
            '交易金额': trade_amount,
            '手续费': commission,
            '净交易额': net_amount,
            '持仓成本': self.entry_price if self.position != 0 else 0,
            '当前市值': current_market_value,
            '浮动盈亏': unrealized_pnl,
            '实现盈亏': realized_pnl,
            '累计盈亏': realized_pnl + unrealized_pnl,
            '账户余额': self.capital,
            '总资产': total_assets,
            '收益率': daily_return,
            '累计收益率': cumulative_return,
            'Y值': indicators['y_value'],
            'X值': indicators['x_value'],
            'E值': indicators['e_value'],
            '信号强度': signal_strength,
            '风险等级': risk_level,
            # 新增持仓状态字段
            '持仓状态': position_status['status'],
            '持仓方向': position_status['direction'],
            '持仓天数': position_status['holding_days'],
            '持仓比例': position_status['position_ratio'],
            '盈亏比例': position_status['pnl_ratio'],
            '止盈距离': position_status['take_profit_distance'],
            '止损距离': position_status['stop_loss_distance'],
            '备注': note
        }

        # 添加到记录
        new_df = pd.concat([existing_df, pd.DataFrame([record])], ignore_index=True)

        # 保存
        new_df.to_excel(self.excel_file, index=False)

        # 显示结果
        print(f"股票代码: {self.symbol} 东亚银行")
        print(f"开盘价: {today_data['Open']:.2f} 港元")
        print(f"最高价: {today_data['High']:.2f} 港元")
        print(f"最低价: {today_data['Low']:.2f} 港元")
        print(f"收盘价: {today_data['Close']:.2f} 港元")
        print(f"成交量: {today_data['Volume']:,.0f}")
        print(f"Y值: {indicators['y_value']:.4f}")
        print(f"X值: {indicators['x_value']:.4f}")
        print(f"E值: {indicators['e_value']:.4f}")
        print(f"交易状态: {trade_type}")
        print(f"信号强度: {signal_strength}")
        print(f"总资产: {total_assets:,.2f} 港元")
        print(f"累计收益率: {cumulative_return:.2f}%")

        # 显示持仓状态详情
        print("\n📊 持仓状态详情:")
        print(f"   持仓状态: {position_status['status']}")
        print(f"   持仓方向: {position_status['direction']}")
        print(f"   持仓天数: {position_status['holding_days']}天")
        print(f"   持仓比例: {position_status['position_ratio']:.2f}%")
        print(f"   盈亏比例: {position_status['pnl_ratio']:+.2f}%")
        print(f"   距离止盈: {position_status['take_profit_distance']:.2f}%")
        print(f"   距离止损: {position_status['stop_loss_distance']:.2f}%")

        print(f"\n记录已保存到: {self.excel_file}")
        print("今日交易记录填充完成")

def main():
    """主函数"""
    print("香港交易所交易时间结束 下午5:00")
    print("=" * 50)

    try:
        tracker = DailyTracker0023HK()
        tracker.fill_today_record()
        print("下次填充: 明日下午5:00")
    except Exception as e:
        print(f"填充失败: {e}")

if __name__ == "__main__":
    main()
