#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
快速每日更新系统 - 简化版
执行顺序：
1. 更新数据库 (跳过，假设已完成)
2. 更新Full_Y和Controller字段
3. 查看持仓状态
"""

import subprocess
import sys
from datetime import datetime

def print_header(title):
    """打印标题"""
    print("\n" + "=" * 50)
    print(f"🎯 {title}")
    print("=" * 50)

def print_step(step_num, description):
    """打印步骤"""
    print(f"\n{step_num}️⃣ {description}")
    print("-" * 30)

def main():
    """主函数"""
    print_header("快速每日更新系统")
    print(f"🕐 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    success_count = 0
    total_steps = 2
    
    # 步骤1: 更新Full_Y和Controller
    print_step(1, "更新Full_Y和Controller字段")
    try:
        result = subprocess.run([sys.executable, "update_full_y_controller.py"], 
                              capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            print("✅ Full_Y和Controller更新成功")
            success_count += 1
        else:
            print("❌ Full_Y和Controller更新失败")
            print(f"错误: {result.stderr}")
    except Exception as e:
        print(f"❌ 更新异常: {e}")
    
    # 步骤2: 查看持仓状态
    print_step(2, "查看持仓状态")
    try:
        result = subprocess.run([sys.executable, "position_status_viewer.py"], 
                              capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            print("✅ 持仓状态查看成功")
            # 显示关键信息
            output_lines = result.stdout.split('\n')
            for line in output_lines:
                if any(keyword in line for keyword in ['持仓状态', '持仓方向', '盈亏', '风险等级']):
                    print(f"   {line}")
            success_count += 1
        else:
            print("❌ 持仓状态查看失败")
            print(f"错误: {result.stderr}")
    except Exception as e:
        print(f"❌ 查看异常: {e}")
    
    # 总结
    print_header("更新完成总结")
    print(f"🕐 完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"📊 成功步骤: {success_count}/{total_steps}")
    print(f"📈 成功率: {success_count/total_steps*100:.1f}%")
    
    if success_count == total_steps:
        print("🎉 快速更新任务完成！")
        return True
    else:
        print("⚠️ 部分任务失败")
        return False

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n⚠️ 用户中断操作")
        sys.exit(1)
    except Exception as e:
        print(f"\n\n❌ 系统异常: {e}")
        sys.exit(1)
