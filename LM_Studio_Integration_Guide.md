# LM Studio 交易计算服务接轨指南

## 🎯 概述

这个服务程序将您的交易计算功能与 LM Studio 进行完美接轨，提供：
- 数据库连接和查询
- 交易数据计算
- LM Studio AI 对话集成
- RESTful API 接口

## 🛠️ 安装步骤

### 1. 安装 Python 依赖
```bash
pip install -r requirements.txt
```

### 2. 配置数据库连接
编辑 `lm_studio_trading_service.py` 中的数据库配置：
```python
DB_CONFIG = {
    'host': 'localhost',
    'port': 3306,
    'user': 'root',
    'password': 'your_actual_password',  # 修改为您的密码
    'database': 'your_actual_database',  # 修改为您的数据库名
    'charset': 'utf8mb4'
}
```

### 3. 配置 LM Studio
确保 LM Studio 正在运行，并修改配置：
```python
LM_STUDIO_CONFIG = {
    'base_url': 'http://localhost:1234/v1',  # LM Studio 默认地址
    'model': 'your-actual-model-name'  # 修改为您使用的模型名
}
```

### 4. 启动服务
```bash
python lm_studio_trading_service.py
```

## 📡 API 接口说明

### 1. 健康检查
```http
GET http://localhost:5000/api/health
```

### 2. 更新交易数据
```http
POST http://localhost:5000/api/update_trading_data
Content-Type: application/json

{
    "table_name": "your_table_name"
}
```

### 3. 获取交易数据
```http
GET http://localhost:5000/api/get_trading_data?table_name=your_table_name&limit=10
```

### 4. 获取统计信息
```http
GET http://localhost:5000/api/get_statistics?table_name=your_table_name
```

### 5. LM Studio 对话
```http
POST http://localhost:5000/api/lm_studio_chat
Content-Type: application/json

{
    "message": "请分析最新的交易数据",
    "table_name": "your_table_name"
}
```

## 🔗 LM Studio 集成方式

### 方式1：直接 API 调用
在 LM Studio 中使用 HTTP 请求调用我们的服务：

```javascript
// 在 LM Studio 的自定义函数中
async function getTradeData(tableName) {
    const response = await fetch('http://localhost:5000/api/get_statistics?table_name=' + tableName);
    const data = await response.json();
    return data;
}
```

### 方式2：通过对话接口
直接与 LM Studio 对话，服务会自动获取相关数据：

```
用户: "请分析 HSI2412 表的交易数据"
AI: [自动获取数据并分析]
```

## 🎨 前端集成示例

### HTML + JavaScript 示例
```html
<!DOCTYPE html>
<html>
<head>
    <title>交易数据分析</title>
</head>
<body>
    <div id="app">
        <h1>交易数据分析</h1>
        
        <div>
            <input type="text" id="tableName" placeholder="输入表名">
            <button onclick="updateData()">更新数据</button>
            <button onclick="getStats()">获取统计</button>
        </div>
        
        <div>
            <textarea id="chatInput" placeholder="输入您的问题"></textarea>
            <button onclick="chatWithAI()">与AI对话</button>
        </div>
        
        <div id="results"></div>
    </div>

    <script>
        const API_BASE = 'http://localhost:5000/api';
        
        async function updateData() {
            const tableName = document.getElementById('tableName').value;
            const response = await fetch(`${API_BASE}/update_trading_data`, {
                method: 'POST',
                headers: {'Content-Type': 'application/json'},
                body: JSON.stringify({table_name: tableName})
            });
            const data = await response.json();
            document.getElementById('results').innerHTML = JSON.stringify(data, null, 2);
        }
        
        async function getStats() {
            const tableName = document.getElementById('tableName').value;
            const response = await fetch(`${API_BASE}/get_statistics?table_name=${tableName}`);
            const data = await response.json();
            document.getElementById('results').innerHTML = JSON.stringify(data, null, 2);
        }
        
        async function chatWithAI() {
            const message = document.getElementById('chatInput').value;
            const tableName = document.getElementById('tableName').value;
            const response = await fetch(`${API_BASE}/lm_studio_chat`, {
                method: 'POST',
                headers: {'Content-Type': 'application/json'},
                body: JSON.stringify({message: message, table_name: tableName})
            });
            const data = await response.json();
            document.getElementById('results').innerHTML = data.response;
        }
    </script>
</body>
</html>
```

## 🔧 高级配置

### 1. 添加认证
```python
from functools import wraps

def require_auth(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        auth_header = request.headers.get('Authorization')
        if not auth_header or auth_header != 'Bearer your-secret-token':
            return jsonify({'error': 'Unauthorized'}), 401
        return f(*args, **kwargs)
    return decorated_function

@app.route('/api/protected_endpoint', methods=['POST'])
@require_auth
def protected_endpoint():
    # 受保护的端点
    pass
```

### 2. 添加缓存
```python
from flask_caching import Cache

cache = Cache(app, config={'CACHE_TYPE': 'simple'})

@app.route('/api/cached_data')
@cache.cached(timeout=300)  # 缓存5分钟
def cached_data():
    # 缓存的数据
    pass
```

### 3. 添加日志记录
```python
import logging
from logging.handlers import RotatingFileHandler

if not app.debug:
    file_handler = RotatingFileHandler('logs/trading_service.log', maxBytes=10240, backupCount=10)
    file_handler.setFormatter(logging.Formatter(
        '%(asctime)s %(levelname)s: %(message)s [in %(pathname)s:%(lineno)d]'
    ))
    file_handler.setLevel(logging.INFO)
    app.logger.addHandler(file_handler)
```

## 🚀 使用示例

### 1. 启动服务后测试
```bash
# 健康检查
curl http://localhost:5000/api/health

# 更新交易数据
curl -X POST http://localhost:5000/api/update_trading_data \
  -H "Content-Type: application/json" \
  -d '{"table_name": "HSI2412"}'

# 获取统计信息
curl "http://localhost:5000/api/get_statistics?table_name=HSI2412"
```

### 2. 与 LM Studio 对话
```bash
curl -X POST http://localhost:5000/api/lm_studio_chat \
  -H "Content-Type: application/json" \
  -d '{
    "message": "请分析最新的交易数据，重点关注k值和Full_Y的趋势",
    "table_name": "HSI2412"
  }'
```

## 🎯 功能特点

- ✅ **完整的数据库集成**：直接调用您的存储过程
- ✅ **LM Studio AI 集成**：智能分析交易数据
- ✅ **RESTful API**：标准化的接口设计
- ✅ **实时数据**：即时获取最新的计算结果
- ✅ **错误处理**：完善的异常处理机制
- ✅ **日志记录**：详细的操作日志
- ✅ **跨域支持**：支持前端集成

现在您的 LM Studio 就可以与交易计算系统完美接轨了！🎉
