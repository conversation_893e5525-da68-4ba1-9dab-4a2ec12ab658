#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
分析Excel文件中的持仓逻辑
"""

import pandas as pd
import os
from datetime import datetime

def analyze_excel_position_logic():
    """分析Excel持仓逻辑"""
    excel_file = "交易记录追踪0023HK.xlsx"
    
    print("📊 交易记录追踪0023HK.xlsx 持仓逻辑分析")
    print("=" * 60)
    
    if not os.path.exists(excel_file):
        print(f"❌ Excel文件不存在: {excel_file}")
        return
    
    try:
        df = pd.read_excel(excel_file)
        print(f"📋 总记录数: {len(df)}")
        print(f"📅 数据时间范围: {df.iloc[0]['交易日期'] if len(df) > 0 else 'N/A'} 到 {df.iloc[-1]['交易日期'] if len(df) > 0 else 'N/A'}")
        print()
        
        # 1. 显示Excel结构
        print("📋 Excel文件结构:")
        print("-" * 40)
        for i, col in enumerate(df.columns, 1):
            print(f"   {i:2d}. {col}")
        print()
        
        # 2. 分析交易类型
        if '交易类型' in df.columns:
            print("🔍 交易类型分析:")
            print("-" * 30)
            trade_types = df['交易类型'].value_counts()
            for trade_type, count in trade_types.items():
                percentage = count / len(df) * 100
                print(f"   {trade_type}: {count}次 ({percentage:.1f}%)")
            print()
        
        # 3. 分析持仓状态变化
        print("📈 持仓状态变化分析:")
        print("-" * 40)
        
        # 检查持仓数量变化
        if '持仓数量' in df.columns:
            position_changes = []
            for i, row in df.iterrows():
                position_qty = row['持仓数量']
                trade_type = row.get('交易类型', 'Unknown')
                trade_direction = row.get('交易方向', 'Unknown')
                
                if position_qty > 0:
                    position_changes.append({
                        'date': row['交易日期'],
                        'type': trade_type,
                        'direction': trade_direction,
                        'quantity': position_qty,
                        'status': '持仓'
                    })
                else:
                    position_changes.append({
                        'date': row['交易日期'],
                        'type': trade_type,
                        'direction': trade_direction,
                        'quantity': position_qty,
                        'status': '空仓'
                    })
            
            # 统计持仓状态
            position_count = sum(1 for p in position_changes if p['status'] == '持仓')
            empty_count = sum(1 for p in position_changes if p['status'] == '空仓')
            
            print(f"   持仓记录: {position_count}次")
            print(f"   空仓记录: {empty_count}次")
            print(f"   持仓比例: {position_count/(position_count+empty_count)*100:.1f}%")
            print()
        
        # 4. 分析资金变化
        print("💰 资金变化分析:")
        print("-" * 30)
        
        if '账户余额' in df.columns and '总资产' in df.columns:
            initial_balance = df['账户余额'].iloc[0]
            current_balance = df['账户余额'].iloc[-1]
            initial_assets = df['总资产'].iloc[0]
            current_assets = df['总资产'].iloc[-1]
            
            balance_change = current_balance - initial_balance
            assets_change = current_assets - initial_assets
            
            print(f"   初始账户余额: {initial_balance:,.2f}")
            print(f"   当前账户余额: {current_balance:,.2f}")
            print(f"   余额变化: {balance_change:+,.2f}")
            print()
            print(f"   初始总资产: {initial_assets:,.2f}")
            print(f"   当前总资产: {current_assets:,.2f}")
            print(f"   资产变化: {assets_change:+,.2f}")
            print(f"   总收益率: {assets_change/initial_assets*100:+.2f}%")
            print()
        
        # 5. 分析持仓逻辑合理性
        print("🎯 持仓逻辑合理性分析:")
        print("-" * 40)
        
        # 检查开仓/平仓逻辑
        open_positions = df[df['交易类型'] == '开仓'] if '交易类型' in df.columns else pd.DataFrame()
        close_positions = df[df['交易类型'] == '平仓'] if '交易类型' in df.columns else pd.DataFrame()
        
        print(f"   开仓次数: {len(open_positions)}")
        print(f"   平仓次数: {len(close_positions)}")
        
        if len(open_positions) > 0 and len(close_positions) > 0:
            print(f"   开平仓匹配: {'✅ 平衡' if len(open_positions) == len(close_positions) else '⚠️ 不平衡'}")
        
        # 检查"尽量不持仓"策略执行情况
        empty_positions = df[df['交易类型'] == '空仓'] if '交易类型' in df.columns else pd.DataFrame()
        if len(empty_positions) > 0:
            empty_ratio = len(empty_positions) / len(df) * 100
            print(f"   空仓比例: {empty_ratio:.1f}%")
            print(f"   策略符合性: {'✅ 符合尽量不持仓' if empty_ratio > 80 else '⚠️ 持仓过多'}")
        
        print()
        
        # 6. 显示最新记录
        print("📅 最新5条记录:")
        print("-" * 80)
        
        # 选择关键列
        key_columns = ['交易日期', '交易类型', '交易方向', '持仓数量', '交易价格', '账户余额', '总资产']
        available_columns = [col for col in key_columns if col in df.columns]
        
        if available_columns:
            latest_records = df[available_columns].tail(5)
            print(latest_records.to_string(index=False))
        else:
            print("无法找到关键列，显示前6列:")
            latest_records = df.iloc[:, :6].tail(5)
            print(latest_records.to_string(index=False))
        
        print()
        
        # 7. 持仓逻辑评估
        print("🏆 持仓逻辑评估:")
        print("-" * 30)
        
        issues = []
        recommendations = []
        
        # 检查是否有异常的持仓数量
        if '持仓数量' in df.columns:
            max_position = df['持仓数量'].max()
            min_position = df['持仓数量'].min()
            
            if max_position > 2000:
                issues.append(f"持仓数量过大: 最大{max_position}股")
            
            if min_position < 0:
                issues.append(f"持仓数量为负: 最小{min_position}股")
        
        # 检查资金使用效率
        if '账户余额' in df.columns and '总资产' in df.columns:
            avg_cash_ratio = (df['账户余额'] / df['总资产']).mean()
            if avg_cash_ratio > 0.9:
                recommendations.append("现金比例过高，可考虑增加投资")
            elif avg_cash_ratio < 0.1:
                recommendations.append("现金比例过低，注意风险控制")
        
        if issues:
            print("   ⚠️ 发现问题:")
            for issue in issues:
                print(f"      - {issue}")
        else:
            print("   ✅ 未发现明显问题")
        
        if recommendations:
            print("   💡 建议:")
            for rec in recommendations:
                print(f"      - {rec}")
        
        print()
        print("✅ 持仓逻辑分析完成")
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")

if __name__ == "__main__":
    analyze_excel_position_logic()
