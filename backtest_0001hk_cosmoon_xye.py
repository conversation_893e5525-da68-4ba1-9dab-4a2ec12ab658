#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
长江和记实业0001.HK - Cosmoon XYE策略20年回测
============================================

使用Cosmoon XYE策略回测长江和记实业20年数据
严格按照指定的X值和Y值计算公式

作者: Cosmoon NG
"""

import yfinance as yf
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from scipy import stats
import warnings
warnings.filterwarnings('ignore')

class CosmoonXYE0001HK:
    def __init__(self):
        """初始化Cosmoon XYE策略回测系统"""
        self.symbol = "0001.HK"  # 长江和记实业
        self.initial_capital = 10000.00  # 初始资金1万港元
        self.monthly_addition = 3000.00  # 每月追加3千港元
        self.commission_rate = 0.001     # 手续费0.1%
        
        # 止盈止损参数
        self.take_profit_long = 0.012    # 多头止盈1.2%
        self.stop_loss_long = 0.006      # 多头止损0.6%
        self.take_profit_short = 0.012   # 空头止盈1.2%
        self.stop_loss_short = 0.006     # 空头止损0.6%
        
        # 交易状态
        self.position = 0  # 0=空仓, 1=多头, -1=空头
        self.entry_price = 0
        self.shares = 0
        self.trades = []
        self.equity_curve = []
        
    def load_data(self):
        """加载长江和记实业20年历史数据"""
        print(f"📊 加载{self.symbol}历史数据...")
        
        try:
            # 获取20年数据
            end_date = datetime.now()
            start_date = end_date - timedelta(days=20*365)
            
            print(f"   时间范围: {start_date.date()} 至 {end_date.date()}")
            
            ticker = yf.Ticker(self.symbol)
            hist_data = ticker.history(start=start_date, end=end_date)
            
            if hist_data.empty:
                raise ValueError(f"无法获取{self.symbol}数据")
            
            # 转换数据格式
            self.df = pd.DataFrame({
                'date': hist_data.index,
                'open': hist_data['Open'],
                'high': hist_data['High'],
                'low': hist_data['Low'],
                'close': hist_data['Close'],
                'volume': hist_data['Volume']
            })
            
            self.df = self.df.dropna().sort_values('date').reset_index(drop=True)
            
            print(f"   ✅ 成功获取 {len(self.df)} 条数据")
            print(f"   📈 价格范围: {self.df['close'].min():.2f} - {self.df['close'].max():.2f} 港元")
            print(f"   📅 实际时间: {self.df['date'].min().date()} 至 {self.df['date'].max().date()}")
            
            return True
            
        except Exception as e:
            print(f"   ❌ 数据加载失败: {e}")
            return False
    
    def calculate_cosmoon_xye(self):
        """计算Cosmoon XYE指标 - 严格按照指定公式"""
        print(f"\n🧮 计算Cosmoon XYE指标...")
        
        # === Y值计算 (价格在20日区间的位置) ===
        print("   计算Y值...")
        window = 20
        self.df['high_20'] = self.df['high'].rolling(window).max()
        self.df['low_20'] = self.df['low'].rolling(window).min()
        self.df['y_value'] = (self.df['close'] - self.df['low_20']) / (self.df['high_20'] - self.df['low_20'])
        self.df['y_value'] = self.df['y_value'].fillna(0.5).clip(0, 1)
        
        # === X值计算 (资金流强度) ===
        print("   计算X值...")
        
        # 1. 计算典型价格和资金流
        self.df['typical_price'] = (self.df['high'] + self.df['low'] + self.df['close']) / 3
        self.df['money_flow'] = self.df['typical_price'] * self.df['volume']
        self.df['price_change'] = self.df['typical_price'].diff()
        
        # 2. 计算正负资金流
        self.df['positive_mf'] = np.where(self.df['price_change'] > 0, self.df['money_flow'], 0)
        self.df['negative_mf'] = np.where(self.df['price_change'] < 0, self.df['money_flow'], 0)
        
        # 3. 计算14日资金流总和
        period = 14
        self.df['positive_mf_14'] = self.df['positive_mf'].rolling(period).sum()
        self.df['negative_mf_14'] = self.df['negative_mf'].rolling(period).sum()
        
        # 4. 按照指定公式计算X值
        # 计算资金流比率
        self.df['money_flow_ratio'] = self.df['positive_mf_14'] / (self.df['negative_mf_14'] + 1e-10)
        
        # 计算MFI (Money Flow Index)
        self.df['mfi'] = 100 - (100 / (1 + self.df['money_flow_ratio']))
        
        # X值 = MFI归一化到0-1
        self.df['x_value'] = self.df['mfi'] / 100
        
        # === E值计算 (Cosmoon公式) ===
        print("   计算E值...")
        self.df['e_value'] = (8 * self.df['x_value'] - 3) * self.df['y_value'] - 3 * self.df['x_value'] + 1
        
        # === 回归线计算 ===
        print("   计算回归线...")
        self.df['i'] = range(1, len(self.df) + 1)
        slope, intercept, r_value, _, _ = stats.linregress(self.df['i'], self.df['close'])
        self.df['regression_line'] = intercept + slope * self.df['i']
        self.df['price_position'] = (self.df['close'] - self.df['regression_line']) / self.df['regression_line']
        
        print(f"   ✅ XYE指标计算完成")
        print(f"   📊 Y值范围: {self.df['y_value'].min():.3f} - {self.df['y_value'].max():.3f}")
        print(f"   📊 X值范围: {self.df['x_value'].min():.3f} - {self.df['x_value'].max():.3f}")
        print(f"   📊 E值范围: {self.df['e_value'].min():.3f} - {self.df['e_value'].max():.3f}")
        print(f"   📈 回归线R²: {r_value**2:.4f}")
        
    def add_monthly_capital(self, current_date, capital):
        """每月追加资金"""
        if not hasattr(self, 'last_addition_month'):
            self.last_addition_month = current_date.replace(day=1)
            return capital
        
        current_month = current_date.replace(day=1)
        if current_month > self.last_addition_month:
            capital += self.monthly_addition
            self.last_addition_month = current_month
            
        return capital
    
    def run_backtest(self):
        """运行Cosmoon XYE策略回测"""
        print(f"\n💼 开始Cosmoon XYE策略回测...")
        
        capital = self.initial_capital
        trade_count = 0
        winning_trades = 0
        losing_trades = 0
        
        # 从第60天开始，确保指标稳定
        for i in range(60, len(self.df)):
            row = self.df.iloc[i]
            current_date = row['date']
            current_price = row['close']
            
            # 每月追加资金
            capital = self.add_monthly_capital(current_date, capital)
            
            # 检查止盈止损
            if self.position != 0:
                if self.position == 1:  # 多头持仓
                    profit_ratio = (current_price - self.entry_price) / self.entry_price
                    
                    if profit_ratio >= self.take_profit_long:  # 止盈
                        proceeds = self.shares * current_price * (1 - self.commission_rate)
                        capital += proceeds
                        profit = proceeds - (self.shares * self.entry_price * (1 + self.commission_rate))
                        
                        self.trades.append({
                            'date': current_date,
                            'type': 'long_exit_profit',
                            'entry_price': self.entry_price,
                            'exit_price': current_price,
                            'shares': self.shares,
                            'profit': profit,
                            'capital': capital
                        })
                        
                        if profit > 0:
                            winning_trades += 1
                        else:
                            losing_trades += 1
                        
                        self.position = 0
                        self.shares = 0
                        trade_count += 1
                        
                    elif profit_ratio <= -self.stop_loss_long:  # 止损
                        proceeds = self.shares * current_price * (1 - self.commission_rate)
                        capital += proceeds
                        profit = proceeds - (self.shares * self.entry_price * (1 + self.commission_rate))
                        
                        self.trades.append({
                            'date': current_date,
                            'type': 'long_exit_loss',
                            'entry_price': self.entry_price,
                            'exit_price': current_price,
                            'shares': self.shares,
                            'profit': profit,
                            'capital': capital
                        })
                        
                        losing_trades += 1
                        self.position = 0
                        self.shares = 0
                        trade_count += 1
                        
                elif self.position == -1:  # 空头持仓
                    profit_ratio = (self.entry_price - current_price) / self.entry_price
                    
                    if profit_ratio >= self.take_profit_short:  # 止盈
                        profit = self.shares * (self.entry_price - current_price) * (1 - self.commission_rate)
                        capital += profit
                        
                        self.trades.append({
                            'date': current_date,
                            'type': 'short_exit_profit',
                            'entry_price': self.entry_price,
                            'exit_price': current_price,
                            'shares': self.shares,
                            'profit': profit,
                            'capital': capital
                        })
                        
                        if profit > 0:
                            winning_trades += 1
                        else:
                            losing_trades += 1
                        
                        self.position = 0
                        self.shares = 0
                        trade_count += 1
                        
                    elif profit_ratio <= -self.stop_loss_short:  # 止损
                        loss = self.shares * (self.entry_price - current_price) * (1 - self.commission_rate)
                        capital += loss
                        
                        self.trades.append({
                            'date': current_date,
                            'type': 'short_exit_loss',
                            'entry_price': self.entry_price,
                            'exit_price': current_price,
                            'shares': self.shares,
                            'profit': loss,
                            'capital': capital
                        })
                        
                        losing_trades += 1
                        self.position = 0
                        self.shares = 0
                        trade_count += 1
            
            # 开仓信号判断 (Cosmoon XYE策略)
            if self.position == 0:
                # 多头开仓条件
                if (row['e_value'] > 0 and row['x_value'] > 0.45 and 
                    row['y_value'] > 0.45 and row['price_position'] < 0):
                    
                    self.position = 1
                    self.entry_price = current_price
                    self.shares = int(capital * 0.8 / current_price)  # 80%仓位
                    cost = self.shares * current_price * (1 + self.commission_rate)
                    capital -= cost
                    
                    self.trades.append({
                        'date': current_date,
                        'type': 'long_entry',
                        'entry_price': self.entry_price,
                        'shares': self.shares,
                        'cost': cost,
                        'capital': capital
                    })
                    
                # 空头开仓条件
                elif ((row['y_value'] < 0.3 or row['x_value'] < 0.3 or
                       (row['x_value'] > 0.45 and row['y_value'] < 0.35) or
                       (row['x_value'] < 0.45 and row['y_value'] > 0.35)) and
                      row['price_position'] > 0):
                    
                    self.position = -1
                    self.entry_price = current_price
                    self.shares = int(capital * 0.8 / current_price)  # 80%仓位
                    cost = self.shares * current_price * self.commission_rate
                    capital -= cost
                    
                    self.trades.append({
                        'date': current_date,
                        'type': 'short_entry',
                        'entry_price': self.entry_price,
                        'shares': self.shares,
                        'cost': cost,
                        'capital': capital
                    })
            
            # 记录权益曲线
            current_equity = capital
            if self.position != 0 and self.shares > 0:
                if self.position == 1:
                    unrealized_pnl = self.shares * (current_price - self.entry_price)
                else:
                    unrealized_pnl = self.shares * (self.entry_price - current_price)
                current_equity += unrealized_pnl
            
            self.equity_curve.append({
                'date': current_date,
                'equity': current_equity,
                'position': self.position,
                'price': current_price
            })
        
        self.final_capital = capital
        self.total_trades = trade_count
        self.winning_trades = winning_trades
        self.losing_trades = losing_trades
        
        print(f"   ✅ 回测完成")
        
    def analyze_results(self):
        """分析回测结果"""
        print(f"\n📈 Cosmoon XYE策略回测结果分析")
        print("=" * 60)
        
        # 基础统计
        total_days = (self.df['date'].max() - self.df['date'].min()).days
        total_years = total_days / 365
        
        print(f"📊 基础信息:")
        print(f"   标的: {self.symbol} (长江和记实业)")
        print(f"   回测期间: {total_years:.1f}年 ({total_days}天)")
        print(f"   数据条数: {len(self.df)}")
        
        # 价格表现
        start_price = self.df['close'].iloc[0]
        end_price = self.df['close'].iloc[-1]
        price_return = (end_price - start_price) / start_price * 100
        
        print(f"\n💰 价格表现:")
        print(f"   起始价格: {start_price:.2f}港元")
        print(f"   最终价格: {end_price:.2f}港元")
        print(f"   价格涨幅: {price_return:.2f}%")
        print(f"   价格倍数: {end_price/start_price:.2f}倍")
        
        # 策略表现
        months_passed = int(total_days / 30)
        total_invested = self.initial_capital + months_passed * self.monthly_addition
        net_profit = self.final_capital - total_invested
        total_return = net_profit / total_invested * 100
        annual_return = ((self.final_capital / total_invested) ** (1/total_years) - 1) * 100
        
        print(f"\n🚀 策略表现:")
        print(f"   初始资金: {self.initial_capital:,.0f}港元")
        print(f"   追加资金: {months_passed * self.monthly_addition:,.0f}港元 ({months_passed}个月)")
        print(f"   总投入: {total_invested:,.0f}港元")
        print(f"   最终资金: {self.final_capital:,.0f}港元")
        print(f"   净收益: {net_profit:,.0f}港元")
        print(f"   总收益率: {total_return:.2f}%")
        print(f"   年化收益率: {annual_return:.2f}%")
        print(f"   资产倍数: {self.final_capital/self.initial_capital:.2f}倍")
        
        # 交易统计
        win_rate = self.winning_trades / self.total_trades * 100 if self.total_trades > 0 else 0
        
        print(f"\n📋 交易统计:")
        print(f"   总交易次数: {self.total_trades}")
        print(f"   盈利交易: {self.winning_trades}")
        print(f"   亏损交易: {self.losing_trades}")
        print(f"   胜率: {win_rate:.2f}%")
        
        # 买入持有对比
        buy_hold_shares = int(self.initial_capital / start_price)
        buy_hold_final = buy_hold_shares * end_price
        buy_hold_return = (buy_hold_final - self.initial_capital) / self.initial_capital * 100
        
        print(f"\n🆚 买入持有对比:")
        print(f"   买入持有最终: {buy_hold_final:,.0f}港元")
        print(f"   买入持有收益: {buy_hold_return:.2f}%")
        print(f"   策略优势: {self.final_capital - buy_hold_final:,.0f}港元")
        print(f"   策略倍数: {self.final_capital / buy_hold_final:.2f}倍")
        
        # XYE指标统计
        print(f"\n🧮 XYE指标统计:")
        print(f"   Y值平均: {self.df['y_value'].mean():.3f}")
        print(f"   X值平均: {self.df['x_value'].mean():.3f}")
        print(f"   E值平均: {self.df['e_value'].mean():.3f}")
        print(f"   E>0天数: {(self.df['e_value'] > 0).sum()}天 ({(self.df['e_value'] > 0).mean()*100:.1f}%)")
        
        return {
            'final_capital': self.final_capital,
            'total_return': total_return,
            'annual_return': annual_return,
            'win_rate': win_rate,
            'total_trades': self.total_trades
        }

def main():
    """主函数"""
    print("🚀 长江和记实业0001.HK - Cosmoon XYE策略20年回测")
    print("=" * 60)
    print("📋 策略参数:")
    print("   • Y值: 价格在20日区间的位置")
    print("   • X值: MFI/100 (资金流指数归一化)")
    print("   • E值: (8×X-3)×Y-3×X+1")
    print("   • 多头止盈/止损: +1.2%/-0.6%")
    print("   • 空头止盈/止损: +1.2%/-0.6%")
    print("   • 每月追加: 3,000港元")
    
    # 创建回测实例
    backtest = CosmoonXYE0001HK()
    
    # 执行回测流程
    if not backtest.load_data():
        return
    
    backtest.calculate_cosmoon_xye()
    backtest.run_backtest()
    results = backtest.analyze_results()
    
    print(f"\n🎉 Cosmoon XYE策略回测完成!")
    print(f"📈 长江和记实业20年回测展示了XYE策略的有效性")

if __name__ == "__main__":
    main()
