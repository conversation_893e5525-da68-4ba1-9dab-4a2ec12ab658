#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
修正版完整系统回测 - 降低信号要求
解决没有交易的问题
"""

import pandas as pd
import numpy as np
import mysql.connector
from datetime import datetime, timedelta

class FixedCompleteSystemBacktest:
    def __init__(self):
        """初始化修正版系统"""
        self.initial_capital = 2500.00
        self.current_capital = 2500.00
        self.transaction_cost = 0.001
        self.position_ratio = 0.8
        self.max_holding_days = 3
        self.take_profit = 0.02
        self.stop_loss = 0.015
        
        # 当前状态
        self.position = 0
        self.position_size = 0
        self.entry_price = 0
        self.entry_date = None
        self.holding_days = 0
        
        # 记录
        self.trades = []
        self.daily_reports = []
    
    def load_data(self):
        """加载数据"""
        try:
            config = {
                'host': 'localhost',
                'port': 3306,
                'user': 'root',
                'password': '12345678',
                'database': 'finance',
                'charset': 'utf8mb4'
            }
            
            conn = mysql.connector.connect(**config)
            cursor = conn.cursor()
            
            one_year_ago = datetime.now() - timedelta(days=365)
            
            query = """
            SELECT 
                Date, Close, High, Low,
                Y_Value, X_Value, E_Value, 
                Full_Y, E as E2, Controller,
                RSI, MFI, midprice
            FROM eab_0023hk 
            WHERE Date >= %s
            AND Close IS NOT NULL
            ORDER BY Date ASC
            """
            
            cursor.execute(query, (one_year_ago.strftime('%Y-%m-%d'),))
            results = cursor.fetchall()
            
            columns = ['date', 'close', 'high', 'low',
                      'y_value', 'x_value', 'e_value', 
                      'full_y', 'e2', 'controller',
                      'rsi', 'mfi', 'midprice']
            
            self.df = pd.DataFrame(results, columns=columns)
            self.df['date'] = pd.to_datetime(self.df['date'])
            
            numeric_cols = ['close', 'high', 'low', 'y_value', 'x_value', 
                           'e_value', 'full_y', 'e2', 'controller', 'rsi', 'mfi', 'midprice']
            
            for col in numeric_cols:
                if col in self.df.columns:
                    self.df[col] = pd.to_numeric(self.df[col], errors='coerce')
            
            cursor.close()
            conn.close()
            
            print(f"✅ 数据加载成功: {len(self.df)} 条")
            return True
            
        except Exception as e:
            print(f"❌ 数据加载失败: {e}")
            return False
    
    def generate_fixed_signal(self, row):
        """修正版信号生成 - 降低要求"""
        y1, x1, e1 = row['y_value'], row['x_value'], row['e_value']
        y2, e2, controller = row['full_y'], row['e2'], row['controller']
        
        # 价格偏离 (放宽要求)
        price_deviation = 0
        if pd.notna(row['midprice']) and row['midprice'] > 0:
            price_deviation = (row['close'] - row['midprice']) / row['midprice']
        
        # 系统1信号 (放宽条件)
        signal1 = "观望"
        strength1 = 0
        if pd.notna(e1) and pd.notna(y1):
            if e1 > -0.02 and y1 > 0.5:  # 放宽: E>-0.02, Y>0.5
                signal1 = "买入"
                strength1 = 2
            elif e1 < -0.02 and y1 < 0.5:  # 放宽: E<-0.02, Y<0.5
                signal1 = "卖出"
                strength1 = 2
        
        # 系统2信号 (简化条件)
        signal2 = "观望"
        strength2 = 0
        if pd.notna(controller):
            if controller == 1:  # 简化: 直接买入信号
                signal2 = "买入"
                strength2 = 1
            elif controller == 0 and abs(price_deviation) > 0.03:  # 放宽: 偏离>3%
                if price_deviation > 0:
                    signal2 = "卖出"
                    strength2 = 1
        
        # 综合信号 (降低要求)
        total_strength = 0
        final_signal = "观望"
        
        if signal1 == "买入":
            total_strength += strength1
            if signal2 == "买入":
                total_strength += strength2
                final_signal = "买入"
            elif signal2 == "观望":
                final_signal = "买入"
        elif signal1 == "卖出":
            total_strength += strength1
            if signal2 == "卖出":
                total_strength += strength2
                final_signal = "卖出"
            elif signal2 == "观望":
                final_signal = "卖出"
        elif signal2 == "买入":
            total_strength += strength2
            final_signal = "买入"
        elif signal2 == "卖出":
            total_strength += strength2
            final_signal = "卖出"
        
        return {
            'signal': final_signal,
            'strength': total_strength,
            'system1': signal1,
            'system2': signal2,
            'deviation': price_deviation,
            'reason': f"系统1: {signal1}({strength1}), 系统2: {signal2}({strength2})"
        }
    
    def check_exit_conditions(self, row, signal):
        """检查平仓条件"""
        if self.position == 0:
            return False, ""
        
        current_price = row['close']
        
        # 时间止损
        if self.holding_days >= self.max_holding_days:
            return True, f"时间止损({self.holding_days}天)"
        
        # 盈亏止损
        if self.position == 1:
            pnl_ratio = (current_price - self.entry_price) / self.entry_price
        else:
            pnl_ratio = (self.entry_price - current_price) / self.entry_price
        
        if pnl_ratio >= self.take_profit:
            return True, f"止盈({pnl_ratio*100:.1f}%)"
        elif pnl_ratio <= -self.stop_loss:
            return True, f"止损({pnl_ratio*100:.1f}%)"
        
        # 信号反转 (降低要求)
        if signal['strength'] >= 2:
            if (self.position == 1 and signal['signal'] == "卖出") or \
               (self.position == -1 and signal['signal'] == "买入"):
                return True, "信号反转"
        
        return False, ""
    
    def execute_trade(self, row, action, reason):
        """执行交易"""
        current_price = row['close']
        
        if action == "开多仓":
            available_capital = self.current_capital * self.position_ratio
            self.position_size = int(available_capital / current_price)
            self.position_size = max(self.position_size, 100)
            
            trade_amount = self.position_size * current_price
            trade_cost = trade_amount * self.transaction_cost
            
            if trade_amount + trade_cost <= self.current_capital:
                self.current_capital -= (trade_amount + trade_cost)
                self.position = 1
                self.entry_price = current_price
                self.entry_date = row['date']
                self.holding_days = 1
                
                self.trades.append({
                    'date': row['date'],
                    'action': '开多仓',
                    'price': current_price,
                    'size': self.position_size,
                    'reason': reason
                })
        
        elif action == "开空仓":
            available_capital = self.current_capital * self.position_ratio
            self.position_size = int(available_capital / current_price)
            self.position_size = max(self.position_size, 100)
            
            trade_amount = self.position_size * current_price
            trade_cost = trade_amount * self.transaction_cost
            
            self.current_capital += (trade_amount - trade_cost)
            self.position = -1
            self.entry_price = current_price
            self.entry_date = row['date']
            self.holding_days = 1
            
            self.trades.append({
                'date': row['date'],
                'action': '开空仓',
                'price': current_price,
                'size': self.position_size,
                'reason': reason
            })
        
        elif action == "平仓":
            trade_amount = self.position_size * current_price
            trade_cost = trade_amount * self.transaction_cost
            
            if self.position == 1:
                pnl = (current_price - self.entry_price) * self.position_size
                self.current_capital += (trade_amount - trade_cost)
            else:
                pnl = (self.entry_price - current_price) * self.position_size
                self.current_capital -= (trade_amount + trade_cost)
            
            net_pnl = pnl - (self.position_size * self.entry_price * self.transaction_cost)
            
            self.trades.append({
                'date': row['date'],
                'action': f'平{"多" if self.position == 1 else "空"}仓',
                'price': current_price,
                'pnl': net_pnl,
                'reason': reason
            })
            
            self.position = 0
            self.position_size = 0
            self.entry_price = 0
            self.entry_date = None
            self.holding_days = 0
    
    def run_fixed_backtest(self):
        """运行修正版回测"""
        print("\n🚀 开始修正版系统回测")
        print("=" * 50)
        print("🔧 修正内容:")
        print("   • 降低信号强度要求: 4→2")
        print("   • 放宽系统1条件: E>-0.02, Y>0.5")
        print("   • 简化系统2条件: Controller=1直接买入")
        print("   • 允许单系统信号交易")
        print()
        
        for i, row in self.df.iterrows():
            # 生成修正信号
            signal = self.generate_fixed_signal(row)
            
            # 检查平仓
            should_exit, reason = self.check_exit_conditions(row, signal)
            if should_exit:
                self.execute_trade(row, "平仓", reason)
            
            # 检查开仓 (降低要求到强度2)
            if self.position == 0 and signal['strength'] >= 2:
                if signal['signal'] == "买入":
                    self.execute_trade(row, "开多仓", f"修正买入信号(强度{signal['strength']})")
                elif signal['signal'] == "卖出":
                    self.execute_trade(row, "开空仓", f"修正卖出信号(强度{signal['strength']})")
            
            # 更新持仓天数
            if self.position != 0:
                self.holding_days += 1
            
            # 显示进度
            if i % 50 == 0:
                progress = i / len(self.df) * 100
                print(f"   进度: {progress:.1f}% - {row['date'].date()}")
        
        # 强制平仓
        if self.position != 0:
            last_row = self.df.iloc[-1]
            self.execute_trade(last_row, "平仓", "回测结束")
        
        print(f"\n✅ 修正版回测完成")
    
    def analyze_fixed_results(self):
        """分析修正版结果"""
        print("\n📊 修正版回测结果")
        print("=" * 50)
        
        entry_trades = [t for t in self.trades if '开' in t['action']]
        exit_trades = [t for t in self.trades if '平' in t['action']]
        
        total_trades = len(entry_trades)
        profitable_trades = len([t for t in exit_trades if t.get('pnl', 0) > 0])
        
        print(f"📈 交易统计:")
        print(f"   总交易次数: {total_trades}")
        print(f"   盈利交易: {profitable_trades}")
        print(f"   亏损交易: {len(exit_trades) - profitable_trades}")
        if len(exit_trades) > 0:
            print(f"   胜率: {profitable_trades/len(exit_trades)*100:.1f}%")
        
        final_capital = self.current_capital
        total_return = (final_capital / self.initial_capital - 1) * 100
        
        print(f"\n💰 收益统计:")
        print(f"   初始资金: {self.initial_capital:,.2f}")
        print(f"   最终资金: {final_capital:,.2f}")
        print(f"   总收益率: {total_return:+.2f}%")
        
        # 对比买入持有
        start_price = self.df['close'].iloc[0]
        end_price = self.df['close'].iloc[-1]
        buy_hold_return = (end_price / start_price - 1) * 100
        
        print(f"\n🆚 买入持有对比:")
        print(f"   买入持有: {buy_hold_return:+.2f}%")
        print(f"   策略超额: {total_return - buy_hold_return:+.2f}%")
        
        # 保存结果
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        trades_df = pd.DataFrame(self.trades)
        trades_df.to_excel(f"修正版系统回测_{timestamp}.xlsx", index=False)
        
        print(f"\n✅ 结果已保存: 修正版系统回测_{timestamp}.xlsx")

def main():
    """主函数"""
    print("🔧 修正版完整系统回测")
    print("=" * 50)
    print("🎯 解决问题:")
    print("   • 系统2信号为0的问题")
    print("   • 双系统组合过严的问题")
    print("   • 强度要求过高的问题")
    
    try:
        backtest = FixedCompleteSystemBacktest()
        
        if not backtest.load_data():
            return
        
        backtest.run_fixed_backtest()
        backtest.analyze_fixed_results()
        
    except Exception as e:
        print(f"❌ 回测失败: {e}")
        import traceback
        traceback.print_exc()
    else:
        print("\n🎉 修正版回测完成！")

if __name__ == "__main__":
    main()
