#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
查看sp_averagelineV3存储过程
==========================

查看回归线计算过程

作者: Cosmoon NG
日期: 2025年7月24日
"""

import mysql.connector

def check_avgline_procedure():
    """查看sp_averagelineV3存储过程"""
    
    config = {
        'host': 'localhost',
        'user': 'root',
        'password': '12345678',
        'database': 'finance',
        'charset': 'utf8mb4'
    }
    
    try:
        conn = mysql.connector.connect(**config)
        cursor = conn.cursor()
        
        print("🔍 查看sp_averagelineV3存储过程:")
        print("=" * 60)
        
        # 查看存储过程定义
        cursor.execute("SHOW CREATE PROCEDURE sp_averagelineV3")
        result = cursor.fetchone()
        
        if result:
            proc_name = result[0]
            proc_definition = result[2]
            
            print(f"存储过程名称: {proc_name}")
            print(f"存储过程定义:")
            print("-" * 60)
            print(proc_definition)
            print("-" * 60)
        else:
            print("❌ 找不到sp_averagelineV3存储过程")
        
        # 查看是否有avglineV3表
        print(f"\n🔍 查看avglineV3表结构:")
        try:
            cursor.execute("DESCRIBE avglineV3")
            columns = cursor.fetchall()
            
            print("表结构:")
            for col in columns:
                print(f"   {col[0]} - {col[1]} - {col[2]}")
                
        except mysql.connector.Error as e:
            print(f"❌ avglineV3表不存在或无法访问: {e}")
        
        # 查看是否有相关的回归线数据
        print(f"\n🔍 查看回归线相关数据:")
        try:
            cursor.execute("SELECT * FROM avglineV3 LIMIT 5")
            data = cursor.fetchall()
            
            if data:
                print("前5条记录:")
                for row in data:
                    print(f"   {row}")
            else:
                print("表中无数据")
                
        except mysql.connector.Error as e:
            print(f"❌ 无法查询avglineV3数据: {e}")
        
        cursor.close()
        conn.close()
        
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")

if __name__ == "__main__":
    check_avgline_procedure()
