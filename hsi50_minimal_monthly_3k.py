#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
HSI50最少持仓回测系统 - 30K初始+每月3K资金20年历史
=================================================

策略特点：
- 很少持仓，极其保守
- 严格回归中线策略
- 凯利公式精确计算
- Cosmoon博弈论方法
- 复利计算
- 每月定投3K港币
- 20年历史数据

作者: Cosmoon NG
日期: 2025年7月
"""

import pandas as pd
import yfinance as yf
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

class HSI50MinimalMonthly3K:
    def __init__(self):
        """初始化HSI50最少持仓+每月3K回测系统"""
        self.symbol = "^HSI"  # 恒生指数
        self.initial_capital = 30000  # 30K港币初始资金
        self.monthly_investment = 3000  # 每月定投3K港币
        self.data = None
        
        # 极其保守的策略参数
        self.strategy_params = {
            # 极严格的Cosmoon博弈论参数
            'ultra_high_y': 0.85,           # 超高Y值阈值
            'ultra_high_x': 0.85,           # 超高X值阈值
            'ultra_low_y': 0.1,             # 超低Y值阈值
            'ultra_low_x': 0.1,             # 超低X值阈值
            'ultra_e_high': 0.8,            # 超高E值阈值
            'ultra_e_low': -0.8,            # 超低E值阈值
            
            # 严格回归中线参数
            'median_period': 60,            # 回归中线计算周期60天
            'deviation_threshold': 0.05,    # 偏离阈值5%
            'extreme_deviation': 0.1,      # 极端偏离阈值10%
            
            # 凯利公式参数 - 极保守
            'kelly_win_rate': 0.7,          # 高胜率要求
            'kelly_odds_ratio': 3.0,        # 1:3赔率要求
            'max_position_ratio': 0.1,      # 最大仓位10%
            'max_positions': 1,             # 最多1个仓位
            
            # 持仓参数 - 很少持仓
            'min_holding_days': 60,         # 最少持有60天
            'max_holding_days': 180,        # 最多持有180天
            'position_cooldown': 30,        # 平仓后30天冷却期
            
            # 止盈止损参数 - 宽松设置
            'take_profit': 0.2,             # 止盈20%
            'stop_loss': 0.1,               # 止损10%
            
            # 交易参数
            'transaction_cost': 30,         # 每笔交易成本30港币
            'compound_interest': True,      # 复利计算
        }
        
        self.trades = []
        self.monthly_investments = []
        self.daily_portfolio = []
        self.current_positions = []
        self.current_capital = self.initial_capital
        self.last_trade_date = None
        self.total_invested = self.initial_capital
    
    def fetch_hsi_data(self):
        """获取恒生指数20年历史数据"""
        print("📈 获取恒生指数20年历史数据...")
        
        try:
            # 获取20年数据
            end_date = datetime.now()
            start_date = end_date - timedelta(days=20*365)
            
            ticker = yf.Ticker(self.symbol)
            self.data = ticker.history(start=start_date, end=end_date)
            
            if self.data.empty:
                print("❌ 数据获取失败：返回空数据")
                return False
            
            self.data.reset_index(inplace=True)
            self.data.columns = [col.lower() for col in self.data.columns]
            
            print(f"✅ 成功获取恒生指数数据:")
            print(f"   • 数据期间: {self.data['date'].min().strftime('%Y-%m-%d')} 至 {self.data['date'].max().strftime('%Y-%m-%d')}")
            print(f"   • 总记录数: {len(self.data):,} 天")
            
            return True
            
        except Exception as e:
            print(f"❌ 获取数据失败: {e}")
            return False
    
    def calculate_indicators(self):
        """计算技术指标和策略参数"""
        print("📊 计算技术指标和极保守策略参数...")
        
        # 移动平均线
        self.data['ma_20'] = self.data['close'].rolling(window=20).mean()
        self.data['ma_60'] = self.data['close'].rolling(window=60).mean()
        self.data['ma_120'] = self.data['close'].rolling(window=120).mean()
        
        # 严格回归中线计算
        self.data['median_price'] = self.data['close'].rolling(window=self.strategy_params['median_period']).median()
        self.data['price_deviation'] = (self.data['close'] - self.data['median_price']) / self.data['median_price']
        
        # RSI
        delta = self.data['close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
        rs = gain / loss
        self.data['rsi'] = 100 - (100 / (1 + rs))
        
        # 成交量指标
        self.data['volume_ma'] = self.data['volume'].rolling(window=20).mean()
        self.data['volume_ratio'] = self.data['volume'] / self.data['volume_ma']
        
        # Y值计算 (控制系数) - 更保守
        price_vs_ma20 = self.data['close'] / self.data['ma_20']
        base_y = np.where(price_vs_ma20 >= 1, 
                         0.5 + 0.3 * np.tanh((price_vs_ma20 - 1) * 2),
                         0.5 - 0.3 * np.tanh((1 - price_vs_ma20) * 2))
        
        # 趋势调整 - 更保守
        ma_trend = (self.data['ma_20'] / self.data['ma_60']).fillna(1)
        trend_adjustment = 0.05 * np.tanh((ma_trend - 1) * 1.5)
        
        # 成交量调整 - 更保守
        volume_adjustment = 0.03 * np.tanh((self.data['volume_ratio'] - 1))
        
        self.data['y_probability'] = base_y + trend_adjustment + volume_adjustment
        self.data['y_probability'] = np.clip(self.data['y_probability'], 0.1, 0.9)
        self.data['y_probability'].fillna(0.5, inplace=True)
        
        # X值计算 (资金流比例) - 更保守
        price_change = (self.data['close'] - self.data['open']) / self.data['open']
        money_flow = self.data['volume'] * price_change
        
        def calc_inflow_ratio(flows):
            if len(flows) == 0 or flows.isna().all():
                return 0.5
            flows = flows.dropna()
            if len(flows) == 0:
                return 0.5
            
            inflows = flows[flows > 0].sum()
            outflows = abs(flows[flows < 0].sum())
            total_flow = inflows + outflows
            
            return inflows / total_flow if total_flow > 0 else 0.5
        
        base_x = money_flow.rolling(window=30).apply(calc_inflow_ratio, raw=False)  # 更长周期
        rsi_adjustment = 0.2 * (self.data['rsi'] / 100 - 0.5)  # 更小影响
        
        self.data['inflow_ratio'] = base_x + rsi_adjustment
        self.data['inflow_ratio'] = np.clip(self.data['inflow_ratio'], 0.1, 0.9)
        self.data['inflow_ratio'].fillna(0.5, inplace=True)
        
        # E值计算 (Cosmoon博弈论核心公式)
        self.data['e_value'] = (8 * self.data['inflow_ratio'] * self.data['y_probability'] - 
                               3 * self.data['inflow_ratio'] - 3 * self.data['y_probability'] + 1)
        
        # 长期趋势判断 - 三重确认
        self.data['strong_uptrend'] = (
            (self.data['ma_20'] > self.data['ma_60']) & 
            (self.data['ma_60'] > self.data['ma_120']) &
            (self.data['close'] > self.data['ma_20'])
        )
        self.data['strong_downtrend'] = (
            (self.data['ma_20'] < self.data['ma_60']) & 
            (self.data['ma_60'] < self.data['ma_120']) &
            (self.data['close'] < self.data['ma_20'])
        )
        
        print("✅ 极保守指标计算完成")
    
    def calculate_kelly_position(self, win_rate, odds_ratio):
        """计算凯利公式仓位"""
        # 凯利公式: f = (bp - q) / b
        b = odds_ratio
        p = win_rate
        q = 1 - p
        
        kelly_fraction = (b * p - q) / b
        
        # 极保守：只有凯利系数>0.3才考虑交易
        if kelly_fraction < 0.3:
            return 0
        
        return max(0, min(kelly_fraction, self.strategy_params['max_position_ratio']))
    
    def get_ultra_conservative_signal(self, row):
        """获取极保守交易信号"""
        y_val = row['y_probability']
        x_val = row['inflow_ratio']
        e_val = row['e_value']
        deviation = row['price_deviation']
        strong_uptrend = row['strong_uptrend']
        strong_downtrend = row['strong_downtrend']
        
        # 检查冷却期
        if self.last_trade_date:
            days_since_last_trade = (row['date'] - self.last_trade_date).days
            if days_since_last_trade < self.strategy_params['position_cooldown']:
                return 'COOLDOWN'
        
        # 极端买涨信号：所有条件都必须满足
        if (y_val > self.strategy_params['ultra_high_y'] and 
            x_val > self.strategy_params['ultra_high_x'] and
            e_val > self.strategy_params['ultra_e_high'] and
            deviation < -self.strategy_params['extreme_deviation'] and  # 价格远低于中线
            strong_uptrend):
            return 'ULTRA_BUY'
        
        # 极端买跌信号：所有条件都必须满足
        elif (y_val < self.strategy_params['ultra_low_y'] and 
              x_val < self.strategy_params['ultra_low_x'] and
              e_val < self.strategy_params['ultra_e_low'] and
              deviation > self.strategy_params['extreme_deviation'] and  # 价格远高于中线
              strong_downtrend):
            return 'ULTRA_SELL'
        
        # 其他情况：很少持仓
        else:
            return 'MINIMAL_HOLD'

    def check_exit_conditions(self, position, current_price, current_date):
        """检查退出条件"""
        entry_price = position['entry_price']
        direction = position['direction']
        entry_date = position['entry_date']

        # 计算持仓天数
        holding_days = (current_date - entry_date).days

        # 计算收益率
        if direction == 'LONG':
            profit_pct = (current_price - entry_price) / entry_price
        else:  # SHORT
            profit_pct = (entry_price - current_price) / entry_price

        # 时间限制
        if holding_days >= self.strategy_params['max_holding_days']:
            return True, '时间止损', profit_pct

        # 最少持有期内不退出（除非巨亏）
        if holding_days < self.strategy_params['min_holding_days']:
            if profit_pct < -self.strategy_params['stop_loss'] * 1.5:  # 巨亏才退出
                return True, '巨亏止损', profit_pct
            else:
                return False, '', profit_pct

        # 止盈止损
        if profit_pct >= self.strategy_params['take_profit']:
            return True, '止盈', profit_pct
        elif profit_pct <= -self.strategy_params['stop_loss']:
            return True, '止损', profit_pct

        return False, '', profit_pct

    def backtest_minimal_strategy(self):
        """执行极少持仓+每月3K策略回测"""
        print("\n🚀 开始极少持仓+每月3K策略回测...")
        print("="*60)
        print(f"💰 初始资金: {self.initial_capital:,} 港币")
        print(f"📅 每月定投: {self.monthly_investment:,} 港币")
        print(f"📊 策略: 极少持仓 + 严格回归中线 + Cosmoon + 凯利公式")
        print(f"📈 极端买涨: Y>{self.strategy_params['ultra_high_y']}, X>{self.strategy_params['ultra_high_x']}, E>{self.strategy_params['ultra_e_high']} + 价格远低于中线 + 强上升趋势")
        print(f"📉 极端买跌: Y<{self.strategy_params['ultra_low_y']}, X<{self.strategy_params['ultra_low_x']}, E<{self.strategy_params['ultra_e_low']} + 价格远高于中线 + 强下降趋势")
        print(f"⏰ 持仓时间: {self.strategy_params['min_holding_days']}-{self.strategy_params['max_holding_days']}天")
        print(f"🎯 止盈: {self.strategy_params['take_profit']*100}%, 止损: {self.strategy_params['stop_loss']*100}%")
        print(f"🔄 复利计算: {self.strategy_params['compound_interest']}")
        print("="*60)

        # 统计变量
        total_trades = 0
        winning_trades = 0
        losing_trades = 0
        minimal_hold_days = 0

        # 记录每月定投
        last_investment_month = None

        # 跳过前120天用于指标计算
        for i in range(120, len(self.data)):
            row = self.data.iloc[i]
            date = row['date']
            price = row['close']

            # 检查是否需要每月定投
            current_month = date.strftime('%Y-%m')
            if last_investment_month != current_month:
                self.current_capital += self.monthly_investment
                self.total_invested += self.monthly_investment
                self.monthly_investments.append({
                    'date': date.strftime('%Y-%m-%d'),
                    'amount': self.monthly_investment,
                    'total_invested': self.total_invested,
                    'current_capital': self.current_capital
                })
                last_investment_month = current_month

            # 获取极保守交易信号
            signal = self.get_ultra_conservative_signal(row)

            # 检查现有持仓的退出条件
            positions_to_close = []
            for j, position in enumerate(self.current_positions):
                should_exit, exit_reason, profit_pct = self.check_exit_conditions(position, price, date)

                if should_exit:
                    # 计算实际盈亏
                    if position['direction'] == 'LONG':
                        price_diff = price - position['entry_price']
                    else:  # SHORT
                        price_diff = position['entry_price'] - price

                    gross_profit = price_diff * position['shares']
                    net_profit = gross_profit - self.strategy_params['transaction_cost']

                    # 复利计算
                    if self.strategy_params['compound_interest']:
                        self.current_capital += net_profit

                    # 记录交易
                    trade_record = {
                        'entry_date': position['entry_date'].strftime('%Y-%m-%d'),
                        'exit_date': date.strftime('%Y-%m-%d'),
                        'direction': position['direction'],
                        'entry_price': round(position['entry_price'], 2),
                        'exit_price': round(price, 2),
                        'shares': round(position['shares'], 2),
                        'investment': round(position['investment'], 2),
                        'gross_profit': round(gross_profit, 2),
                        'transaction_cost': self.strategy_params['transaction_cost'],
                        'net_profit': round(net_profit, 2),
                        'profit_pct': round(profit_pct * 100, 2),
                        'holding_days': (date - position['entry_date']).days,
                        'capital_after': round(self.current_capital, 2),
                        'exit_reason': exit_reason
                    }

                    self.trades.append(trade_record)

                    if net_profit > 0:
                        winning_trades += 1
                    else:
                        losing_trades += 1

                    total_trades += 1
                    self.last_trade_date = date
                    positions_to_close.append(j)

            # 移除已平仓的持仓
            for j in sorted(positions_to_close, reverse=True):
                del self.current_positions[j]

            # 根据极保守信号决定开仓
            if signal in ['MINIMAL_HOLD', 'COOLDOWN']:
                minimal_hold_days += 1
                action = '极少持仓' if signal == 'MINIMAL_HOLD' else '冷却期'
            elif len(self.current_positions) < self.strategy_params['max_positions']:
                # 计算凯利公式仓位
                kelly_position = self.calculate_kelly_position(
                    self.strategy_params['kelly_win_rate'],
                    self.strategy_params['kelly_odds_ratio']
                )

                if kelly_position > 0:
                    # 计算投资金额
                    investment_amount = self.current_capital * kelly_position

                    if investment_amount >= self.strategy_params['transaction_cost'] * 2:
                        # 计算股数
                        shares = (investment_amount - self.strategy_params['transaction_cost']) / price

                        # 创建持仓记录
                        if signal == 'ULTRA_BUY':
                            direction = 'LONG'
                            action = '极端买涨'
                        else:  # ULTRA_SELL
                            direction = 'SHORT'
                            action = '极端买跌'

                        position = {
                            'entry_date': date,
                            'entry_price': price,
                            'shares': shares,
                            'direction': direction,
                            'investment': investment_amount
                        }

                        self.current_positions.append(position)
                        self.last_trade_date = date
                    else:
                        action = '资金不足'
                        minimal_hold_days += 1
                else:
                    action = '凯利系数不足'
                    minimal_hold_days += 1
            else:
                action = '仓位已满'
                minimal_hold_days += 1

            # 记录每日组合价值
            position_value = 0
            for position in self.current_positions:
                if position['direction'] == 'LONG':
                    unrealized_profit = (price - position['entry_price']) * position['shares']
                else:  # SHORT
                    unrealized_profit = (position['entry_price'] - price) * position['shares']
                position_value += position['investment'] + unrealized_profit

            total_value = self.current_capital + position_value

            self.daily_portfolio.append({
                'date': date.strftime('%Y-%m-%d'),
                'price': price,
                'capital': self.current_capital,
                'position_value': position_value,
                'total_value': total_value,
                'total_invested': self.total_invested,
                'action': action,
                'signal': signal,
                'y_value': row['y_probability'],
                'x_value': row['inflow_ratio'],
                'e_value': row['e_value'],
                'deviation': row['price_deviation'],
                'median_price': row['median_price'],
                'positions_count': len(self.current_positions)
            })

        print(f"\n✅ 极少持仓+每月3K策略回测完成!")
        print(f"📊 总交易次数: {total_trades}")
        print(f"📈 盈利次数: {winning_trades}")
        print(f"📉 亏损次数: {losing_trades}")
        print(f"⏸️ 极少持仓天数: {minimal_hold_days}")
        print(f"💰 总投入资金: {self.total_invested:,} 港币")
        if total_trades > 0:
            print(f"🎯 胜率: {winning_trades/total_trades*100:.1f}%")

        return pd.DataFrame(self.trades), pd.DataFrame(self.monthly_investments), pd.DataFrame(self.daily_portfolio)

    def analyze_results(self, trades_df, monthly_df, daily_df):
        """分析回测结果"""
        print("\n📊 极少持仓+每月3K策略回测结果分析")
        print("=" * 60)

        # 基本统计
        total_trades = len(trades_df)
        final_capital = self.current_capital
        final_total_value = daily_df['total_value'].iloc[-1] if len(daily_df) > 0 else self.initial_capital
        total_invested = self.total_invested
        net_return = final_total_value - total_invested
        net_return_rate = (net_return / total_invested) * 100

        # 年化收益率
        years = 20
        annual_return_rate = ((final_total_value / total_invested) ** (1/years) - 1) * 100

        # 定投统计
        total_monthly_investments = len(monthly_df)
        monthly_investment_total = monthly_df['amount'].sum() if len(monthly_df) > 0 else 0

        # 持仓统计
        total_days = len(daily_df)
        minimal_hold_count = len(daily_df[daily_df['action'] == '极少持仓'])
        minimal_hold_rate = minimal_hold_count / total_days * 100

        if total_trades > 0:
            winning_trades = len(trades_df[trades_df['net_profit'] > 0])
            win_rate = winning_trades / total_trades * 100
            max_profit = trades_df['net_profit'].max()
            max_loss = trades_df['net_profit'].min()
            avg_profit = trades_df['net_profit'].mean()
            avg_holding_days = trades_df['holding_days'].mean()
            total_trading_profit = trades_df['net_profit'].sum()

            # 按方向分析
            direction_stats = trades_df.groupby('direction').agg({
                'net_profit': ['count', 'sum', 'mean'],
                'profit_pct': 'mean',
                'holding_days': 'mean'
            }).round(2)

            # 按退出原因分析
            exit_reason_stats = trades_df.groupby('exit_reason').agg({
                'net_profit': ['count', 'sum', 'mean'],
                'profit_pct': 'mean'
            }).round(2)
        else:
            winning_trades = 0
            win_rate = 0
            max_profit = 0
            max_loss = 0
            avg_profit = 0
            avg_holding_days = 0
            total_trading_profit = 0
            direction_stats = pd.DataFrame()
            exit_reason_stats = pd.DataFrame()

        # 输出结果
        print(f"💰 资金统计:")
        print(f"• 初始资金: {self.initial_capital:,} 港元")
        print(f"• 总投入资金: {total_invested:,} 港元")
        print(f"• 最终现金: {final_capital:,.0f} 港元")
        print(f"• 最终总价值: {final_total_value:,.0f} 港元")
        print(f"• 净收益: {net_return:,.0f} 港元")
        print(f"• 净收益率: {net_return_rate:.2f}%")
        print(f"• 年化收益率: {annual_return_rate:.2f}%")

        print(f"\n📅 定投统计:")
        print(f"• 定投次数: {total_monthly_investments}")
        print(f"• 定投总额: {monthly_investment_total:,} 港元")
        print(f"• 月均定投: {self.monthly_investment:,} 港元")

        print(f"\n⏸️ 持仓统计:")
        print(f"• 总天数: {total_days}")
        print(f"• 极少持仓天数: {minimal_hold_count}")
        print(f"• 极少持仓比例: {minimal_hold_rate:.1f}%")

        if total_trades > 0:
            print(f"\n📈 交易统计:")
            print(f"• 总交易次数: {total_trades}")
            print(f"• 盈利次数: {winning_trades}")
            print(f"• 胜率: {win_rate:.1f}%")
            print(f"• 最大单笔盈利: {max_profit:,.0f} 港元")
            print(f"• 最大单笔亏损: {max_loss:,.0f} 港元")
            print(f"• 平均每笔盈亏: {avg_profit:,.0f} 港元")
            print(f"• 平均持仓天数: {avg_holding_days:.1f} 天")
            print(f"• 交易总盈亏: {total_trading_profit:,.0f} 港元")

            print(f"\n📊 方向分析:")
            print(direction_stats)

            print(f"\n📊 退出原因分析:")
            print(exit_reason_stats)

        # 凯利公式验证
        if total_trades > 0:
            actual_win_rate = win_rate / 100
            actual_odds_ratio = abs(max_profit / max_loss) if max_loss != 0 else 3

            print(f"\n🎲 凯利公式验证:")
            print(f"• 实际胜率: {actual_win_rate:.3f}")
            print(f"• 实际赔率: 1:{actual_odds_ratio:.2f}")
            print(f"• 预设胜率: {self.strategy_params['kelly_win_rate']:.3f}")
            print(f"• 预设赔率: 1:{self.strategy_params['kelly_odds_ratio']:.2f}")

        # 保存到Excel
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"HSI50极少持仓每月3K回测结果_{timestamp}.xlsx"

        with pd.ExcelWriter(filename, engine='openpyxl') as writer:
            if len(trades_df) > 0:
                trades_df.to_excel(writer, sheet_name='交易记录', index=False)
            if len(monthly_df) > 0:
                monthly_df.to_excel(writer, sheet_name='每月定投', index=False)
            daily_df.to_excel(writer, sheet_name='每日数据', index=False)

            # 汇总统计
            summary_data = {
                '项目': ['初始资金(港元)', '总投入资金(港元)', '最终现金(港元)', '最终总价值(港元)',
                        '净收益(港元)', '净收益率(%)', '年化收益率(%)',
                        '总交易次数', '盈利次数', '胜率(%)',
                        '极少持仓天数', '极少持仓比例(%)', '平均持仓天数',
                        '定投次数', '定投总额(港元)'],
                '数值': [self.initial_capital, total_invested, round(final_capital, 0), round(final_total_value, 0),
                        round(net_return, 0), round(net_return_rate, 2), round(annual_return_rate, 2),
                        total_trades, winning_trades, round(win_rate, 1),
                        minimal_hold_count, round(minimal_hold_rate, 1),
                        round(avg_holding_days, 1) if total_trades > 0 else 0,
                        total_monthly_investments, monthly_investment_total]
            }
            pd.DataFrame(summary_data).to_excel(writer, sheet_name='汇总统计', index=False)

            if len(direction_stats) > 0:
                direction_stats.to_excel(writer, sheet_name='方向分析')
            if len(exit_reason_stats) > 0:
                exit_reason_stats.to_excel(writer, sheet_name='退出原因分析')

        print(f"\n✅ 详细结果已保存至: {filename}")
        return filename

def main():
    """主函数"""
    print("🏢 HSI50极少持仓+每月3K策略回测系统")
    print("=" * 60)
    print("💰 初始资金: 30,000港元")
    print("📅 每月定投: 3,000港元")
    print("📊 分析周期: 20年历史数据")
    print("🎯 策略: 极少持仓 + 严格回归中线 + Cosmoon + 凯利公式")
    print("📈 极端买涨: Y>0.85, X>0.85, E>0.8 + 价格远低于中线 + 强上升趋势")
    print("📉 极端买跌: Y<0.1, X<0.1, E<-0.8 + 价格远高于中线 + 强下降趋势")
    print("⏸️ 其他情况: 极少持仓")
    print("⏰ 持仓时间: 60-180天")
    print("🎯 止盈: 20%, 止损: 10%")
    print("🔄 复利计算: 启用")
    print("📊 最大仓位: 10%")
    print("❄️ 冷却期: 30天")

    # 创建回测器
    backtester = HSI50MinimalMonthly3K()

    # 获取数据
    if not backtester.fetch_hsi_data():
        return

    # 计算指标
    backtester.calculate_indicators()

    # 执行回测
    trades_df, monthly_df, daily_df = backtester.backtest_minimal_strategy()

    # 分析结果
    backtester.analyze_results(trades_df, monthly_df, daily_df)

if __name__ == "__main__":
    main()
