#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
列出test表的正确止盈止损价格
==========================

根据策略区域确定买涨/买跌方向，然后计算正确的止盈止损价格：

高值盈利区 (Y>0.43, X>0.43): 买涨，止盈+2%，止损-1%
控股商控制区 (0.333<Y<0.4): 观望
强亏损区 (Y<0.25或X<0.25): 买跌，止盈+2%，止损-1%
其他区域: 买跌，止盈+1%，止损-2%

作者: Cosmoon NG
日期: 2025年7月
"""

import pymysql

class CorrectStopProfitLossLister:
    def __init__(self):
        """初始化止盈止损价格列表器"""
        self.db_config = {
            'host': '************',
            'port': 3306,
            'database': 'finance',
            'user': 'root',
            'password': '',
            'charset': 'utf8mb4'
        }
        self.connection = None
        
    def connect_database(self):
        """连接数据库"""
        try:
            self.connection = pymysql.connect(**self.db_config)
            print(f"✅ 成功连接MySQL数据库: {self.db_config['host']}")
            return True
        except Exception as e:
            print(f"❌ 连接数据库失败: {e}")
            return False
    
    def list_all_stop_profit_loss_prices(self):
        """列出所有记录的止盈止损价格"""
        try:
            cursor = self.connection.cursor()
            
            # 获取所有记录
            cursor.execute("""
                SELECT 交易序号, 开仓日期, close, 平仓价格, `控制系数`, `资金流比例`, 
                       E值, 净利润, `收益率%`
                FROM test 
                ORDER BY 交易序号
            """)
            
            records = cursor.fetchall()
            
            print("📊 test表所有记录的正确止盈止损价格")
            print("="*160)
            print("🔍 策略规则:")
            print("   • 高值盈利区 (Y>0.43, X>0.43): 买涨，止盈+2%，止损-1%")
            print("   • 控股商控制区 (0.333<Y<0.4): 观望")
            print("   • 强亏损区 (Y<0.25或X<0.25): 买跌，止盈+2%，止损-1%")
            print("   • 其他区域: 买跌，止盈+1%，止损-2%")
            print("="*160)
            
            print(f"{'序号':<4} {'日期':<12} {'策略区域':<12} {'方向':<6} {'开仓价':<8} {'平仓价':<8} "
                  f"{'止盈价':<8} {'止损价':<8} {'实际结果':<10} {'应得盈亏':<8} {'实际盈亏':<8}")
            print("-" * 160)
            
            total_should_profit = 0
            total_actual_profit = 0
            
            for record in records:
                (trade_id, open_date, open_price, close_price, y_val, x_val, 
                 e_val, actual_profit, actual_return) = record
                
                # 分类策略区域和方向
                if y_val > 0.43 and x_val > 0.43:
                    zone = '高值盈利区'
                    direction = '买涨'
                    take_profit_pct = 2.0  # +2%
                    stop_loss_pct = 1.0    # -1%
                elif 0.333 < y_val < 0.4:
                    zone = '控股商控制区'
                    direction = '观望'
                    take_profit_pct = 0
                    stop_loss_pct = 0
                elif y_val < 0.25 or x_val < 0.25:
                    zone = '强亏损区'
                    direction = '买跌'
                    take_profit_pct = 2.0  # +2%
                    stop_loss_pct = 1.0    # -1%
                else:
                    zone = '其他区域'
                    direction = '买跌'
                    take_profit_pct = 1.0  # +1%
                    stop_loss_pct = 2.0    # -2%
                
                if direction == '观望':
                    take_profit_price = 0
                    stop_loss_price = 0
                    result = '观望'
                    should_profit = 0
                elif direction == '买涨':
                    # 买涨策略
                    take_profit_price = open_price * (1 + take_profit_pct / 100)
                    stop_loss_price = open_price * (1 - stop_loss_pct / 100)
                    
                    if close_price >= take_profit_price:
                        result = '止盈'
                        should_profit = (take_profit_price - open_price) * 100
                    elif close_price <= stop_loss_price:
                        result = '止损'
                        should_profit = (stop_loss_price - open_price) * 100
                    else:
                        result = '到期平仓'
                        should_profit = (close_price - open_price) * 100
                else:  # 买跌策略
                    # 买跌策略：止盈价格下跌，止损价格上涨
                    take_profit_price = open_price * (1 - take_profit_pct / 100)
                    stop_loss_price = open_price * (1 + stop_loss_pct / 100)
                    
                    if close_price <= take_profit_price:
                        result = '止盈'
                        should_profit = (open_price - take_profit_price) * 100
                    elif close_price >= stop_loss_price:
                        result = '止损'
                        should_profit = (open_price - stop_loss_price) * 100
                    else:
                        result = '到期平仓'
                        should_profit = (open_price - close_price) * 100
                
                total_should_profit += should_profit
                total_actual_profit += actual_profit
                
                print(f"{trade_id:<4} {open_date:<12} {zone:<12} {direction:<6} {open_price:<8.2f} {close_price:<8.2f} "
                      f"{take_profit_price:<8.2f} {stop_loss_price:<8.2f} {result:<10} {should_profit:<8.0f} {int(actual_profit):<8}")
            
            print("\n" + "="*100)
            print(f"📊 汇总统计:")
            print(f"   • 应得总盈亏: {total_should_profit:+,.0f}港币")
            print(f"   • 实际总盈亏: {total_actual_profit:+,.0f}港币")
            print(f"   • 差异: {total_actual_profit - total_should_profit:+,.0f}港币 (交易成本等)")
            
            return True
            
        except Exception as e:
            print(f"❌ 列出止盈止损价格失败: {e}")
            return False
    
    def analyze_by_strategy_zones(self):
        """按策略区域分析止盈止损效果"""
        try:
            cursor = self.connection.cursor()
            
            print(f"\n📊 按策略区域分析止盈止损效果:")
            print("="*120)
            
            zones = [
                ('高值盈利区', '买涨', 2.0, 1.0, "`控制系数` > 0.43 AND `资金流比例` > 0.43"),
                ('强亏损区', '买跌', 2.0, 1.0, "(`控制系数` < 0.25 OR `资金流比例` < 0.25) AND NOT (`控制系数` > 0.333 AND `控制系数` < 0.4)"),
                ('其他区域', '买跌', 1.0, 2.0, """NOT (`控制系数` > 0.43 AND `资金流比例` > 0.43) 
                                                   AND NOT (`控制系数` > 0.333 AND `控制系数` < 0.4) 
                                                   AND NOT (`控制系数` < 0.25 OR `资金流比例` < 0.25)"""),
                ('控股商控制区', '观望', 0, 0, "`控制系数` > 0.333 AND `控制系数` < 0.4")
            ]
            
            for zone_name, direction, take_profit_pct, stop_loss_pct, where_clause in zones:
                print(f"\n🎯 {zone_name} - {direction}策略:")
                if direction != '观望':
                    print(f"   止盈: {take_profit_pct}%, 止损: {stop_loss_pct}%")
                
                cursor.execute(f"""
                    SELECT COUNT(*) as count,
                           AVG(close) as avg_open_price,
                           AVG(平仓价格) as avg_close_price
                    FROM test 
                    WHERE {where_clause}
                """)
                
                result = cursor.fetchone()
                count, avg_open_price, avg_close_price = result
                
                if count > 0:
                    print(f"   • 交易次数: {count}")
                    print(f"   • 平均开仓价: {float(avg_open_price):.2f}港币")
                    print(f"   • 平均平仓价: {float(avg_close_price):.2f}港币")
                    
                    if direction != '观望':
                        # 计算理论止盈止损价格
                        if direction == '买涨':
                            avg_take_profit = avg_open_price * (1 + take_profit_pct / 100)
                            avg_stop_loss = avg_open_price * (1 - stop_loss_pct / 100)
                        else:  # 买跌
                            avg_take_profit = avg_open_price * (1 - take_profit_pct / 100)
                            avg_stop_loss = avg_open_price * (1 + stop_loss_pct / 100)
                        
                        print(f"   • 平均止盈价: {avg_take_profit:.2f}港币")
                        print(f"   • 平均止损价: {avg_stop_loss:.2f}港币")
                        
                        # 分析触发情况
                        if direction == '买涨':
                            cursor.execute(f"""
                                SELECT 
                                    SUM(CASE WHEN 平仓价格 >= close * (1 + {take_profit_pct}/100) THEN 1 ELSE 0 END) as take_profits,
                                    SUM(CASE WHEN 平仓价格 <= close * (1 - {stop_loss_pct}/100) THEN 1 ELSE 0 END) as stop_losses
                                FROM test 
                                WHERE {where_clause}
                            """)
                        else:  # 买跌
                            cursor.execute(f"""
                                SELECT 
                                    SUM(CASE WHEN 平仓价格 <= close * (1 - {take_profit_pct}/100) THEN 1 ELSE 0 END) as take_profits,
                                    SUM(CASE WHEN 平仓价格 >= close * (1 + {stop_loss_pct}/100) THEN 1 ELSE 0 END) as stop_losses
                                FROM test 
                                WHERE {where_clause}
                            """)
                        
                        tp_sl_result = cursor.fetchone()
                        take_profits, stop_losses = tp_sl_result
                        normal_closes = count - take_profits - stop_losses
                        
                        print(f"   • 止盈次数: {take_profits} ({take_profits/count*100:.1f}%)")
                        print(f"   • 止损次数: {stop_losses} ({stop_losses/count*100:.1f}%)")
                        print(f"   • 到期平仓: {normal_closes} ({normal_closes/count*100:.1f}%)")
                        
                        if take_profits > 0 and stop_losses > 0:
                            win_rate = take_profits / (take_profits + stop_losses)
                            odds = take_profit_pct / stop_loss_pct
                            kelly_f = (odds * win_rate - (1 - win_rate)) / odds
                            print(f"   • 止盈止损胜率: {win_rate:.3f}")
                            print(f"   • 凯利系数: {kelly_f:.6f}")
                else:
                    print("   无交易记录")
            
            return True
            
        except Exception as e:
            print(f"❌ 按策略区域分析失败: {e}")
            return False
    
    def close_connection(self):
        """关闭数据库连接"""
        if self.connection:
            self.connection.close()
            print("\n🔒 数据库连接已关闭")

def main():
    """主函数"""
    print("📊 列出test表的正确止盈止损价格")
    print("="*80)
    print("🔍 买涨策略: 止盈价 = 开仓价 × (1+止盈%), 止损价 = 开仓价 × (1-止损%)")
    print("🔍 买跌策略: 止盈价 = 开仓价 × (1-止盈%), 止损价 = 开仓价 × (1+止损%)")
    
    # 创建列表器
    lister = CorrectStopProfitLossLister()
    
    # 连接数据库
    if not lister.connect_database():
        return
    
    # 列出所有止盈止损价格
    lister.list_all_stop_profit_loss_prices()
    
    # 按策略区域分析
    lister.analyze_by_strategy_zones()
    
    # 关闭连接
    lister.close_connection()
    
    print(f"\n🎯 止盈止损价格列表完成！")

if __name__ == "__main__":
    main()
