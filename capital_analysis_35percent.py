#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
35%仓位策略资金分析
==================
详细分析35%仓位策略的资金构成和使用情况
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

def analyze_capital_structure():
    """分析35%仓位策略的资金结构"""
    
    print("💰 35%仓位策略资金详细分析")
    print("=" * 60)
    print(f"📅 分析时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 基础数据
    initial_capital = 30000      # 初始资金
    monthly_addition = 1000      # 每月追加
    investment_period = 5.5      # 投资年数
    months = int(investment_period * 12)  # 总月数
    max_position_ratio = 0.35    # 最大仓位35%
    
    final_capital = 99350.08     # 最终资金
    total_invested = 97466.67    # 总投入
    net_profit = 1883.42         # 净收益
    
    print(f"\n📊 资金构成分析:")
    print(f"   • 初始资金: {initial_capital:,}港元")
    print(f"   • 每月追加: {monthly_addition:,}港元")
    print(f"   • 投资期间: {investment_period}年 ({months}个月)")
    print(f"   • 总投入: {total_invested:,.2f}港元")
    print(f"   • 最终资金: {final_capital:,.2f}港元")
    print(f"   • 净收益: {net_profit:,.2f}港元")
    
    # 资金使用分析
    print(f"\n💼 资金使用分析:")
    
    # 平均资金水平
    avg_capital = (initial_capital + final_capital) / 2
    print(f"   • 平均资金水平: {avg_capital:,.2f}港元")
    
    # 35%仓位的实际金额
    avg_position_size = avg_capital * max_position_ratio
    print(f"   • 35%仓位金额: {avg_position_size:,.2f}港元")
    
    # 剩余现金
    avg_cash = avg_capital * (1 - max_position_ratio)
    print(f"   • 剩余现金(65%): {avg_cash:,.2f}港元")
    
    # 资金增长轨迹
    print(f"\n📈 资金增长轨迹:")
    
    milestones = [
        (0, initial_capital, "初始资金"),
        (12, initial_capital + 12 * monthly_addition, "第1年末"),
        (24, initial_capital + 24 * monthly_addition, "第2年末"),
        (36, initial_capital + 36 * monthly_addition, "第3年末"),
        (48, initial_capital + 48 * monthly_addition, "第4年末"),
        (60, initial_capital + 60 * monthly_addition, "第5年末"),
        (66, total_invested, "投资结束(实际)"),
        (66, final_capital, "最终资金(含收益)")
    ]
    
    for month, capital, description in milestones:
        position_35 = capital * 0.35
        cash_65 = capital * 0.65
        if month <= 66:
            print(f"   • {description:12s}: 总资金{capital:8,.0f} = 仓位{position_35:8,.0f} + 现金{cash_65:8,.0f}")
    
    # 仓位使用效率分析
    print(f"\n🎯 仓位使用效率分析:")
    
    total_trades = 531
    avg_trade_amount = avg_position_size
    total_trade_volume = total_trades * avg_trade_amount
    
    print(f"   • 总交易次数: {total_trades}笔")
    print(f"   • 平均每笔交易金额: {avg_trade_amount:,.2f}港元")
    print(f"   • 总交易金额: {total_trade_volume:,.0f}港元")
    print(f"   • 资金周转率: {total_trade_volume/avg_capital:.1f}倍")
    
    # 收益来源分析
    print(f"\n💰 收益来源分析:")
    
    avg_profit_per_trade = net_profit / total_trades
    monthly_profit = net_profit / months
    annual_profit = net_profit / investment_period
    
    print(f"   • 总净收益: {net_profit:,.2f}港元")
    print(f"   • 平均每笔交易收益: {avg_profit_per_trade:.2f}港元")
    print(f"   • 平均月收益: {monthly_profit:.2f}港元")
    print(f"   • 平均年收益: {annual_profit:.2f}港元")
    
    # 风险暴露分析
    print(f"\n⚠️ 风险暴露分析:")
    
    max_loss_per_trade = avg_position_size * 0.008  # 0.8%止损
    max_profit_per_trade = avg_position_size * 0.016  # 1.6%止盈
    max_daily_risk = max_loss_per_trade  # 假设每日最多1笔交易
    
    print(f"   • 单笔最大风险: {max_loss_per_trade:,.2f}港元 (0.8%止损)")
    print(f"   • 单笔最大收益: {max_profit_per_trade:,.2f}港元 (1.6%止盈)")
    print(f"   • 日最大风险暴露: {max_daily_risk:,.2f}港元")
    print(f"   • 风险占总资金比例: {max_daily_risk/avg_capital*100:.2f}%")
    
    # 现金管理分析
    print(f"\n💵 现金管理分析:")
    
    print(f"   • 现金比例: 65% (保守)")
    print(f"   • 平均现金余额: {avg_cash:,.2f}港元")
    print(f"   • 现金作用:")
    print(f"     - 应对日常开支")
    print(f"     - 提供安全缓冲")
    print(f"     - 支持每月定投")
    print(f"     - 应对紧急情况")
    
    # 不同资金水平下的仓位分析
    print(f"\n📊 不同资金水平下的35%仓位:")
    
    capital_levels = [50000, 100000, 200000, 500000, 1000000]
    
    print(f"   总资金 | 35%仓位 | 单笔止损 | 单笔止盈 | 日风险比例")
    print(f"   " + "-" * 55)
    
    for capital in capital_levels:
        position_35 = capital * 0.35
        stop_loss = position_35 * 0.008
        take_profit = position_35 * 0.016
        daily_risk_ratio = stop_loss / capital * 100
        
        print(f"   {capital:6,d} | {position_35:7,.0f} | {stop_loss:8,.0f} | {take_profit:8,.0f} | {daily_risk_ratio:8.2f}%")
    
    # 资金配置建议
    print(f"\n💡 资金配置建议:")
    
    print(f"\n   🎯 当前配置 (35%仓位):")
    print(f"   • 交易仓位: 35% (主动投资)")
    print(f"   • 现金储备: 65% (安全缓冲)")
    print(f"   • 风险等级: 中低风险")
    print(f"   • 适合人群: 稳健型投资者")
    
    print(f"\n   📊 其他配置选项:")
    configurations = [
        (20, 80, "保守型", "新手投资者"),
        (30, 70, "稳健型", "有经验投资者"),
        (35, 65, "平衡型", "当前配置 ⭐"),
        (40, 60, "积极型", "风险承受能力强"),
        (50, 50, "激进型", "专业投资者")
    ]
    
    for position, cash, risk_type, suitable_for in configurations:
        print(f"   • {position:2d}%仓位 + {cash:2d}%现金 = {risk_type} ({suitable_for})")
    
    # 实盘资金管理建议
    print(f"\n🚀 实盘资金管理建议:")
    
    print(f"\n   💰 资金准备:")
    print(f"   • 最低启动资金: 5万港元")
    print(f"   • 推荐启动资金: 10万港元")
    print(f"   • 理想启动资金: 20万港元以上")
    print(f"   • 每月定投能力: 1000-3000港元")
    
    print(f"\n   🎯 仓位管理:")
    print(f"   • 严格执行35%仓位限制")
    print(f"   • 单笔交易不超过总资金的0.8%风险")
    print(f"   • 保持65%现金缓冲")
    print(f"   • 定期评估和调整")
    
    print(f"\n   📊 风险控制:")
    print(f"   • 日最大亏损: 不超过总资金的1%")
    print(f"   • 月最大亏损: 不超过总资金的3%")
    print(f"   • 年最大回撤: 不超过总资金的10%")
    print(f"   • 连续亏损保护: 3次止损后暂停")
    
    # 收益预期分析
    print(f"\n📈 收益预期分析:")
    
    annual_return_rate = 0.0035  # 0.35%年化收益率
    
    print(f"\n   基于0.35%年化收益率的预期:")
    
    projection_years = [1, 3, 5, 10]
    starting_capital = 100000  # 假设10万起始资金
    monthly_add = 1000
    
    print(f"   年数 | 总投入 | 预期资金 | 累计收益 | 年化收益率")
    print(f"   " + "-" * 50)
    
    for years in projection_years:
        total_invested = starting_capital + monthly_add * 12 * years
        expected_capital = total_invested * (1 + annual_return_rate * years)
        cumulative_profit = expected_capital - total_invested
        
        print(f"   {years:2d}年 | {total_invested:8,d} | {expected_capital:10,.0f} | {cumulative_profit:8,.0f} | {annual_return_rate*100:9.2f}%")
    
    # 总结
    print(f"\n🎉 总结:")
    print(f"   35%仓位策略的资金特点:")
    print(f"   ✅ 总资金: 约10万港元水平 (5.5年积累)")
    print(f"   ✅ 交易仓位: 约3.5万港元 (35%)")
    print(f"   ✅ 现金储备: 约6.5万港元 (65%)")
    print(f"   ✅ 单笔风险: 约280港元 (0.8%止损)")
    print(f"   ✅ 单笔收益: 约560港元 (1.6%止盈)")
    print(f"   ✅ 风险可控: 日风险仅占总资金0.28%")
    
    print(f"\n   这是一个:")
    print(f"   🎯 风险可控的稳健配置")
    print(f"   🎯 适合大多数投资者的选择")
    print(f"   🎯 平衡收益和安全的方案")
    print(f"   🎯 可持续的长期策略")

def calculate_position_examples():
    """计算不同资金水平下的仓位示例"""
    
    print(f"\n💼 实际仓位计算示例:")
    print(f"=" * 40)
    
    # 实际案例
    scenarios = [
        ("小资金", 50000, 1000),
        ("中等资金", 100000, 2000),
        ("较大资金", 200000, 3000),
        ("大资金", 500000, 5000)
    ]
    
    print(f"   情况 | 总资金 | 月投入 | 35%仓位 | 单笔风险 | 单笔收益")
    print(f"   " + "-" * 60)
    
    for scenario, capital, monthly in scenarios:
        position_35 = capital * 0.35
        risk_per_trade = position_35 * 0.008
        profit_per_trade = position_35 * 0.016
        
        print(f"   {scenario:6s} | {capital:6,d} | {monthly:6,d} | {position_35:7,.0f} | {risk_per_trade:8,.0f} | {profit_per_trade:8,.0f}")

def main():
    """主函数"""
    analyze_capital_structure()
    calculate_position_examples()
    
    print(f"\n🎯 关键要点:")
    print(f"   • 35%仓位 ≈ 3.5万港元交易资金")
    print(f"   • 65%现金 ≈ 6.5万港元安全缓冲")
    print(f"   • 单笔风险 ≈ 280港元 (可承受)")
    print(f"   • 单笔收益 ≈ 560港元 (有吸引力)")

if __name__ == "__main__":
    main()
