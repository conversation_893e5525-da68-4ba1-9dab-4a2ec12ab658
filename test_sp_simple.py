#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单测试存储过程
===============

直接测试存储过程 sp_stock_analysis_with_row_coefficients_v2

作者: Cosmoon NG
日期: 2025年7月
"""

import pymysql

# 数据库配置
db_config = {
    'host': 'localhost',
    'user': 'root',
    'password': '12345678',
    'database': 'finance',
    'charset': 'utf8mb4'
}

def test_stored_procedure():
    """测试存储过程"""
    print("🧪 测试存储过程 sp_stock_analysis_with_row_coefficients_v2...")
    
    try:
        conn = pymysql.connect(**db_config)
        cursor = conn.cursor()
        
        # 检查存储过程是否存在
        cursor.execute("""
            SELECT ROUTINE_NAME FROM information_schema.ROUTINES 
            WHERE ROUTINE_SCHEMA = 'finance' 
            AND ROUTINE_NAME = 'sp_stock_analysis_with_row_coefficients_v2'
        """)
        
        if not cursor.fetchone():
            print("❌ 存储过程不存在")
            return False
        
        print("✅ 存储过程存在，开始调用...")
        
        # 调用存储过程
        cursor.execute("CALL sp_stock_analysis_with_row_coefficients_v2('hkhsi50')")
        
        # 获取结果
        rows = cursor.fetchall()
        
        if rows:
            print(f"📊 存储过程返回 {len(rows)} 条结果:")
            
            # 显示结果
            for i, row in enumerate(rows[:5]):  # 只显示前5行
                print(f"   {i+1}. {row}")
        else:
            print("⚠️  存储过程没有返回结果")
        
        # 检查更新后的数据
        cursor.execute("""
            SELECT COUNT(*) as 总数,
                   COUNT(CASE WHEN Full_Y IS NOT NULL THEN 1 END) as FullY非空,
                   COUNT(CASE WHEN E IS NOT NULL THEN 1 END) as E非空,
                   MIN(Full_Y) as FullY最小值,
                   MAX(Full_Y) as FullY最大值,
                   AVG(Full_Y) as FullY平均值
            FROM hkhsi50
        """)
        
        stats = cursor.fetchone()
        print(f"\n📈 数据统计:")
        print(f"   • 总记录数: {stats[0]}")
        print(f"   • Full_Y非空: {stats[1]}")
        print(f"   • E非空: {stats[2]}")
        print(f"   • Full_Y范围: {stats[3]:.4f} - {stats[4]:.4f}")
        print(f"   • Full_Y平均: {stats[5]:.4f}")
        
        # 显示最新几条记录
        cursor.execute("""
            SELECT Date, Close, Midprice, Controller, MoneyFlowRatio, Full_Y, E
            FROM hkhsi50 
            ORDER BY Date DESC 
            LIMIT 5
        """)
        
        recent_data = cursor.fetchall()
        print(f"\n📋 最新5条记录:")
        print(f"{'日期':<12} {'收盘价':<10} {'中位价':<10} {'控股商':<6} {'资金流':<8} {'FullY':<8} {'E值':<8}")
        print("-" * 70)
        
        for row in recent_data:
            controller = str(row[3]) if row[3] is not None else "NULL"
            money_flow = f"{row[4]:.3f}" if row[4] is not None else "NULL"
            full_y = f"{row[5]:.3f}" if row[5] is not None else "NULL"
            e_value = f"{row[6]:.3f}" if row[6] is not None else "NULL"
            
            print(f"{str(row[0]):<12} {row[1]:<10.2f} {row[2]:<10.2f} {controller:<6} {money_flow:<8} {full_y:<8} {e_value:<8}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🎯 简单测试存储过程")
    print("="*40)
    
    if test_stored_procedure():
        print("\n🎉 存储过程测试成功！")
        print("💡 存储过程已正常工作，可以使用:")
        print("   CALL sp_stock_analysis_with_row_coefficients_v2('hkhsi50');")
    else:
        print("\n❌ 存储过程测试失败")

if __name__ == "__main__":
    main()
