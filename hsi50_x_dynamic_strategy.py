#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
HSI50 X指标动态策略
==================
基于X指标的相对变化和分位数，而不是绝对阈值

特点：
1. X = 1 - Full_Y (弱势比例指标)
2. 使用动态分位数作为交易信号
3. 结合y_probability和价格偏离
4. 更灵活的交易条件

基于reference.py改进
"""

import mysql.connector
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import matplotlib.pyplot as plt
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

class HSI50XDynamicStrategy:
    def __init__(self):
        """初始化回测系统"""
        # 数据库配置
        self.db_config = {
            'host': 'localhost',
            'database': 'finance',
            'user': 'root',
            'password': '12345678',
            'charset': 'utf8mb4'
        }
        
        # 策略参数
        self.initial_capital = 30000      # 初始资金
        self.monthly_addition = 3000      # 每月追加资金
        self.take_profit = 0.05           # 止盈 5%
        self.stop_loss = 0.025            # 止损 2.5%
        self.max_position_ratio = 0.2     # 最大仓位20%
        
        # X指标动态策略参数
        self.lookback_window = 60         # 回望窗口
        self.x_high_percentile = 80       # X高位分位数
        self.x_low_percentile = 20        # X低位分位数
        
        # 状态变量
        self.position = 0                 # 当前持仓
        self.current_price = 0            # 当前持仓价格
        self.trades = []
        self.equity_curve = []
        
    def load_data(self):
        """从新数据库加载数据"""
        print("\n1. 加载HSI50数据 (X动态策略)...")
        try:
            connection = mysql.connector.connect(**self.db_config)
            
            query = """
                SELECT Date, Open, High, Low, Close, Volume,
                       ma_20, ma_60, y_probability, 
                       new_midprice, new_controller, new_Full_Y
                FROM hkhsi50 
                WHERE Date >= '2020-01-01'
                AND new_Full_Y IS NOT NULL
                ORDER BY Date ASC
            """
            
            self.df = pd.read_sql(query, connection)
            connection.close()
            
            # 数据预处理
            self.df['date'] = pd.to_datetime(self.df['Date'])
            self.df.rename(columns={
                'Open': 'open', 'High': 'high', 'Low': 'low', 
                'Close': 'close', 'Volume': 'volume',
                'new_Full_Y': 'full_y'
            }, inplace=True)
            
            # 计算X指标和动态阈值
            self.calculate_dynamic_indicators()
            
            print(f"✓ 成功加载 {len(self.df)} 条数据")
            print(f"数据范围：{self.df['date'].min().date()} 至 {self.df['date'].max().date()}")
            print(f"X指标范围：{self.df['X'].min():.6f} ~ {self.df['X'].max():.6f}")
            
            return True
            
        except Exception as e:
            print(f"❌ 数据加载失败: {e}")
            return False
    
    def calculate_dynamic_indicators(self):
        """计算X指标和动态阈值"""
        print("   计算X指标和动态阈值...")
        
        # 核心X指标：X = 1 - Full_Y
        self.df['X'] = 1 - self.df['full_y']
        
        # 计算动态分位数阈值
        self.df['X_high_threshold'] = self.df['X'].rolling(self.lookback_window).quantile(self.x_high_percentile/100)
        self.df['X_low_threshold'] = self.df['X'].rolling(self.lookback_window).quantile(self.x_low_percentile/100)
        
        # X指标的技术分析
        self.df['X_change'] = self.df['X'].diff()
        self.df['X_ma5'] = self.df['X'].rolling(5).mean()
        self.df['X_ma20'] = self.df['X'].rolling(20).mean()
        
        # X相对于动态阈值的位置
        self.df['X_above_high'] = self.df['X'] > self.df['X_high_threshold']
        self.df['X_below_low'] = self.df['X'] < self.df['X_low_threshold']
        
        # X的动量
        self.df['X_momentum'] = self.df['X'] - self.df['X_ma5']
        
        # 价格技术指标
        self.df['price_deviation'] = (self.df['close'] - self.df['new_midprice']) / self.df['new_midprice']
        self.df['price_vs_ma20'] = self.df['close'] / self.df['ma_20']
        self.df['volatility'] = self.df['close'].pct_change().rolling(window=20).std()
        
        # RSI
        delta = self.df['close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
        rs = gain / loss
        self.df['rsi'] = 100 - (100 / (1 + rs))
        
        # 成交量指标
        self.df['volume_ma20'] = self.df['volume'].rolling(20).mean()
        self.df['volume_ratio'] = self.df['volume'] / self.df['volume_ma20']
        
        print("✓ 动态指标计算完成")
    
    def add_monthly_capital(self, date, capital):
        """每月增加资金"""
        last_month = getattr(self, 'last_month', None)
        current_month = date.replace(day=1)
        
        if last_month is None or current_month > last_month:
            self.last_month = current_month
            return capital + self.monthly_addition
        
        return capital
    
    def check_entry_conditions(self, row):
        """检查入场条件 (基于动态X指标)"""
        # 基本数据检查
        required_fields = ['X', 'X_high_threshold', 'X_low_threshold', 'X_change', 
                          'y_probability', 'price_deviation', 'rsi', 'volume_ratio']
        
        for field in required_fields:
            if pd.isna(row[field]):
                return 0
        
        X = row['X']
        X_high_thresh = row['X_high_threshold']
        X_low_thresh = row['X_low_threshold']
        X_change = row['X_change']
        X_momentum = row['X_momentum']
        y_prob = row['y_probability']
        price_deviation = row['price_deviation']
        rsi = row['rsi']
        volume_ratio = row['volume_ratio']
        
        # 多头条件：X突破高位阈值 (弱势比例高，适合抄底)
        long_condition = (
            X > X_high_thresh and  # X突破高位阈值
            X_change > 0 and  # X正在上升 (弱势加剧)
            X_momentum > 0 and  # X动量向上
            y_prob > 0.49 and  # y_probability不太悲观
            price_deviation < 0.015 and  # 价格不太高
            rsi < 70 and  # RSI不超买
            volume_ratio > 0.8  # 成交量不太低
        )
        
        # 空头条件：X跌破低位阈值 (弱势比例低，市场过度强势)
        short_condition = (
            X < X_low_thresh and  # X跌破低位阈值
            X_change < 0 and  # X正在下降 (强势加剧)
            X_momentum < 0 and  # X动量向下
            y_prob < 0.51 and  # y_probability不太乐观
            price_deviation > -0.015 and  # 价格不太低
            rsi > 30 and  # RSI不超卖
            volume_ratio > 0.8  # 成交量不太低
        )
        
        if long_condition:
            return 1
        elif short_condition:
            return -1
        else:
            return 0
    
    def run_backtest(self):
        """运行回测"""
        print("\n2. 开始X动态策略回测...")
        
        capital = self.initial_capital
        last_trade_date = None
        min_trade_interval = timedelta(days=1)  # 允许更频繁交易
        
        for i in range(self.lookback_window, len(self.df)):  # 从回望窗口后开始
            row = self.df.iloc[i]
            date = pd.to_datetime(row['date'])
            
            # 每月增加资金
            capital = self.add_monthly_capital(date, capital)
            
            # 记录权益
            current_equity = capital
            if self.position != 0:
                # 计算当前持仓的浮动盈亏
                if self.position == 1:  # 多头
                    unrealized_pnl = (row['close'] - self.current_price) / self.current_price * capital * self.max_position_ratio
                else:  # 空头
                    unrealized_pnl = (self.current_price - row['close']) / self.current_price * capital * self.max_position_ratio
                current_equity += unrealized_pnl
            
            self.equity_curve.append({
                'date': date,
                'equity': current_equity,
                'position': self.position,
                'X': row['X'],
                'X_high_threshold': row['X_high_threshold'],
                'X_low_threshold': row['X_low_threshold']
            })
            
            # 检查交易间隔
            if last_trade_date and (date - last_trade_date) < min_trade_interval:
                continue
            
            # 检查止盈止损
            if self.position != 0:
                profit_ratio = 0
                
                if self.position == 1:  # 多头
                    profit_ratio = (row['close'] - self.current_price) / self.current_price
                else:  # 空头
                    profit_ratio = (self.current_price - row['close']) / self.current_price
                
                should_exit = False
                exit_reason = ""
                
                if profit_ratio >= self.take_profit:  # 止盈
                    should_exit = True
                    exit_reason = "止盈"
                elif profit_ratio <= -self.stop_loss:  # 止损
                    should_exit = True
                    exit_reason = "止损"
                
                if should_exit:
                    profit = profit_ratio * capital * self.max_position_ratio
                    capital += profit
                    
                    self.trades.append({
                        'date': date,
                        'type': f'{["short", "long"][self.position == 1]}_exit_{exit_reason}',
                        'price': row['close'],
                        'profit': profit,
                        'profit_ratio': profit_ratio,
                        'capital': capital,
                        'X': row['X'],
                        'exit_reason': exit_reason
                    })
                    
                    self.position = 0
            
            # 检查开仓条件
            if self.position == 0:
                position_signal = self.check_entry_conditions(row)
                
                if position_signal != 0:
                    position_size = capital * self.max_position_ratio
                    
                    if position_size > 1000:  # 最小交易金额限制
                        self.position = position_signal
                        self.current_price = row['close']
                        last_trade_date = date
                        
                        self.trades.append({
                            'date': date,
                            'type': 'long_entry' if position_signal == 1 else 'short_entry',
                            'price': self.current_price,
                            'size': position_size,
                            'capital': capital,
                            'X': row['X'],
                            'X_threshold': row['X_high_threshold'] if position_signal == 1 else row['X_low_threshold'],
                            'y_probability': row['y_probability']
                        })
        
        self.final_capital = capital
        print(f"✓ 回测完成！最终资金：{self.final_capital:,.2f}")
    
    def analyze_results(self):
        """分析回测结果"""
        print("\n=== X动态策略回测分析 ===")
        
        trades_df = pd.DataFrame(self.trades)
        if len(trades_df) == 0:
            print("没有产生任何交易")
            return
        
        # 基本统计
        entry_trades = trades_df[trades_df['type'].str.contains('entry')]
        exit_trades = trades_df[trades_df['type'].str.contains('exit')]
        
        total_trades = len(entry_trades)
        winning_trades = len(exit_trades[exit_trades['profit'] > 0]) if 'profit' in exit_trades.columns else 0
        
        print(f"\n📊 交易统计：")
        print(f"总交易次数：{total_trades}")
        print(f"盈利交易：{winning_trades}")
        print(f"亏损交易：{total_trades - winning_trades}")
        if total_trades > 0:
            print(f"胜率：{winning_trades/total_trades*100:.2f}%")
        
        # 盈亏分析
        if 'profit' in exit_trades.columns and len(exit_trades) > 0:
            total_profit = exit_trades['profit'].sum()
            profit_trades = exit_trades[exit_trades['profit'] > 0]
            loss_trades = exit_trades[exit_trades['profit'] < 0]
            
            print(f"总交易盈亏：{total_profit:,.2f}")
            
            if len(profit_trades) > 0:
                print(f"平均盈利：{profit_trades['profit'].mean():.2f}")
                print(f"最大盈利：{profit_trades['profit'].max():.2f}")
            
            if len(loss_trades) > 0:
                print(f"平均亏损：{loss_trades['profit'].mean():.2f}")
                print(f"最大亏损：{loss_trades['profit'].min():.2f}")
            
            # 止盈止损分析
            tp_trades = exit_trades[exit_trades['exit_reason'] == '止盈']
            sl_trades = exit_trades[exit_trades['exit_reason'] == '止损']
            
            print(f"止盈次数：{len(tp_trades)} ({len(tp_trades)/len(exit_trades)*100:.1f}%)")
            print(f"止损次数：{len(sl_trades)} ({len(sl_trades)/len(exit_trades)*100:.1f}%)")
        
        # 收益率分析
        initial_equity = self.initial_capital
        final_equity = self.final_capital
        total_days = (self.df['date'].max() - self.df['date'].min()).days
        total_years = total_days / 365
        
        # 计算总投入
        months = total_days / 30
        total_invested = initial_equity + months * self.monthly_addition
        
        net_profit = final_equity - total_invested
        total_return = net_profit / total_invested if total_invested > 0 else 0
        annual_return = (final_equity / total_invested) ** (1/total_years) - 1 if total_years > 0 else 0
        
        print(f"\n💰 收益统计：")
        print(f"初始资金：{initial_equity:,.2f}")
        print(f"总投入：{total_invested:,.2f}")
        print(f"最终资金：{final_equity:,.2f}")
        print(f"净收益：{net_profit:,.2f}")
        print(f"总收益率：{total_return*100:.2f}%")
        print(f"年化收益率：{annual_return*100:.2f}%")
        
        # X指标分析
        if 'X' in entry_trades.columns:
            print(f"\n🎯 X指标分析：")
            avg_entry_X = entry_trades['X'].mean()
            print(f"平均入场X值：{avg_entry_X:.6f}")
            
            long_entries = entry_trades[entry_trades['type'] == 'long_entry']
            short_entries = entry_trades[entry_trades['type'] == 'short_entry']
            
            if len(long_entries) > 0:
                print(f"多头入场平均X值：{long_entries['X'].mean():.6f}")
                print(f"多头入场平均阈值：{long_entries['X_threshold'].mean():.6f}")
            if len(short_entries) > 0:
                print(f"空头入场平均X值：{short_entries['X'].mean():.6f}")
                print(f"空头入场平均阈值：{short_entries['X_threshold'].mean():.6f}")
        
        # 与买入持有比较
        hsi_start = self.df['close'].iloc[self.lookback_window]
        hsi_end = self.df['close'].iloc[-1]
        buy_hold_return = (hsi_end - hsi_start) / hsi_start
        buy_hold_annual = (buy_hold_return + 1) ** (1/total_years) - 1
        
        print(f"\n📊 策略对比：")
        print(f"X动态策略年化收益：{annual_return*100:.2f}%")
        print(f"买入持有年化收益：{buy_hold_annual*100:.2f}%")
        print(f"超额收益：{(annual_return - buy_hold_annual)*100:+.2f}%")
        
        # 显示最近的交易
        if len(trades_df) > 0:
            print(f"\n📋 最近5笔交易:")
            recent_trades = trades_df.tail(5)
            for _, trade in recent_trades.iterrows():
                trade_type = "📈" if "long" in trade['type'] else "📉"
                if 'profit' in trade:
                    pnl_emoji = "💰" if trade['profit'] > 0 else "💔"
                    print(f"   {trade_type} {trade['date'].strftime('%Y-%m-%d')}: {trade['type']}")
                    print(f"      价格: {trade['price']:.2f}, X值: {trade['X']:.6f}")
                    if 'profit' in trade:
                        print(f"      {pnl_emoji} 盈亏: {trade['profit']:,.2f} ({trade['profit_ratio']*100:+.2f}%)")
                else:
                    print(f"   {trade_type} {trade['date'].strftime('%Y-%m-%d')}: {trade['type']}")
                    print(f"      价格: {trade['price']:.2f}, X值: {trade['X']:.6f}")
        
        # 保存结果
        try:
            if len(trades_df) > 0:
                trades_df.to_excel('x_dynamic_strategy_trades.xlsx', index=False)
                print("\n📄 交易记录已保存到 x_dynamic_strategy_trades.xlsx")
            
        except Exception as e:
            print(f"保存文件时出错: {e}")

def main():
    """主函数"""
    print("🎯 HSI50 X指标动态策略")
    print("基于动态分位数的X指标交易")
    print("="*50)
    
    try:
        strategy = HSI50XDynamicStrategy()
        
        if not strategy.load_data():
            return
        
        strategy.run_backtest()
        strategy.analyze_results()
        
        print("\n🎉 X动态策略测试完成！")
        print("💡 使用动态阈值，更灵活的交易条件")
        
    except Exception as e:
        print(f"❌ 运行失败: {e}")

if __name__ == "__main__":
    main()
