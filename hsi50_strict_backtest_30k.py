#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
HSI50严格回测系统 - 30K资金20年历史
==================================

严格策略要求：
- 不持仓策略（每日开平仓）
- 回归中线策略
- 凯利公式严格实现
- Cosmoon博弈论方法
- 复利计算
- 总资金30K港币
- 20年历史数据

作者: Cosmoon NG
日期: 2025年7月
"""

import pandas as pd
import yfinance as yf
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

class StrictHSI50Backtest:
    def __init__(self):
        """初始化严格HSI50回测系统"""
        self.symbol = "^HSI"  # 恒生指数
        self.initial_capital = 30000  # 30K港币
        self.data = None
        
        # 严格策略参数
        self.strategy_params = {
            # Cosmoon博弈论参数
            'high_profit_y': 0.5,           # 高值盈利区Y阈值
            'high_profit_x': 0.5,           # 高值盈利区X阈值
            'control_zone_min': 0.333,      # 控股商控制区下限
            'control_zone_max': 0.4,        # 控股商控制区上限
            'strong_loss_y': 0.25,          # 强亏损区Y阈值
            'strong_loss_x': 0.25,          # 强亏损区X阈值
            
            # 凯利公式参数 (严格1:2赔率)
            'kelly_win_rate': 0.6,          # 胜率预期
            'kelly_odds_ratio': 2.0,        # 赔率1:2
            'max_position_ratio': 0.25,     # 最大仓位25%
            
            # 回归中线参数
            'lookback_period': 20,          # 回归中线计算周期
            'deviation_threshold': 0.02,    # 偏离阈值2%
            
            # 交易参数
            'transaction_cost': 30,         # 每笔交易成本30港币
            'point_value': 50,              # 每个恒指点子价值50港元
            'compound_interest': True,      # 复利计算
        }
        
        self.trades = []
        self.daily_portfolio = []
        self.current_capital = self.initial_capital
    
    def fetch_hsi_data(self):
        """获取恒生指数20年历史数据"""
        print("📈 获取恒生指数20年历史数据...")
        
        try:
            # 获取20年数据
            end_date = datetime.now()
            start_date = end_date - timedelta(days=20*365)
            
            ticker = yf.Ticker(self.symbol)
            self.data = ticker.history(start=start_date, end=end_date)
            
            if self.data.empty:
                print("❌ 数据获取失败：返回空数据")
                return False
            
            self.data.reset_index(inplace=True)
            self.data.columns = [col.lower() for col in self.data.columns]
            
            print(f"✅ 成功获取恒生指数数据:")
            print(f"   • 数据期间: {self.data['date'].min().strftime('%Y-%m-%d')} 至 {self.data['date'].max().strftime('%Y-%m-%d')}")
            print(f"   • 总记录数: {len(self.data):,} 天")
            
            return True
            
        except Exception as e:
            print(f"❌ 获取数据失败: {e}")
            return False
    
    def calculate_indicators(self):
        """计算技术指标和策略参数"""
        print("📊 计算技术指标和策略参数...")
        
        # 移动平均线
        self.data['ma_20'] = self.data['close'].rolling(window=20).mean()
        self.data['ma_60'] = self.data['close'].rolling(window=60).mean()
        
        # 回归中线计算
        self.data['median_price'] = self.data['close'].rolling(window=self.strategy_params['lookback_period']).median()
        self.data['price_deviation'] = (self.data['close'] - self.data['median_price']) / self.data['median_price']
        
        # RSI
        delta = self.data['close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
        rs = gain / loss
        self.data['rsi'] = 100 - (100 / (1 + rs))
        
        # 成交量指标
        self.data['volume_ma'] = self.data['volume'].rolling(window=20).mean()
        self.data['volume_ratio'] = self.data['volume'] / self.data['volume_ma']
        
        # Y值计算 (控制系数)
        price_vs_ma20 = self.data['close'] / self.data['ma_20']
        base_y = np.where(price_vs_ma20 >= 1, 
                         0.5 + 0.4 * np.tanh((price_vs_ma20 - 1) * 3),
                         0.5 - 0.4 * np.tanh((1 - price_vs_ma20) * 3))
        
        # 趋势调整
        ma_trend = (self.data['ma_20'] / self.data['ma_60']).fillna(1)
        trend_adjustment = 0.1 * np.tanh((ma_trend - 1) * 2)
        
        # 成交量调整
        volume_adjustment = 0.05 * np.tanh((self.data['volume_ratio'] - 1))
        
        self.data['y_probability'] = base_y + trend_adjustment + volume_adjustment
        self.data['y_probability'] = np.clip(self.data['y_probability'], 0.1, 0.9)
        self.data['y_probability'].fillna(0.5, inplace=True)
        
        # X值计算 (资金流比例)
        price_change = (self.data['close'] - self.data['open']) / self.data['open']
        money_flow = self.data['volume'] * price_change
        
        def calc_inflow_ratio(flows):
            if len(flows) == 0 or flows.isna().all():
                return 0.5
            flows = flows.dropna()
            if len(flows) == 0:
                return 0.5
            
            inflows = flows[flows > 0].sum()
            outflows = abs(flows[flows < 0].sum())
            total_flow = inflows + outflows
            
            return inflows / total_flow if total_flow > 0 else 0.5
        
        base_x = money_flow.rolling(window=20).apply(calc_inflow_ratio, raw=False)
        rsi_adjustment = 0.3 * (self.data['rsi'] / 100 - 0.5)
        
        self.data['inflow_ratio'] = base_x + rsi_adjustment
        self.data['inflow_ratio'] = np.clip(self.data['inflow_ratio'], 0.1, 0.9)
        self.data['inflow_ratio'].fillna(0.5, inplace=True)
        
        # E值计算 (Cosmoon博弈论核心公式)
        self.data['e_value'] = (8 * self.data['inflow_ratio'] * self.data['y_probability'] - 
                               3 * self.data['inflow_ratio'] - 3 * self.data['y_probability'] + 1)
        
        print("✅ 指标计算完成")
    
    def calculate_kelly_position(self, win_rate, odds_ratio):
        """计算凯利公式仓位"""
        # 凯利公式: f = (bp - q) / b
        # 其中 b = 赔率, p = 胜率, q = 败率
        b = odds_ratio
        p = win_rate
        q = 1 - p
        
        kelly_fraction = (b * p - q) / b
        
        # 限制最大仓位
        return max(0, min(kelly_fraction, self.strategy_params['max_position_ratio']))
    
    def classify_strategy_zone(self, y_val, x_val, e_val):
        """分类策略区域"""
        if (y_val > self.strategy_params['high_profit_y'] and 
            x_val > self.strategy_params['high_profit_x']):
            return 'HIGH_PROFIT'
        elif (self.strategy_params['control_zone_min'] < y_val < self.strategy_params['control_zone_max']):
            return 'CONTROL'
        elif (y_val < self.strategy_params['strong_loss_y'] or 
              x_val < self.strategy_params['strong_loss_x']):
            return 'STRONG_LOSS'
        else:
            return 'OTHER'
    
    def get_trading_signal(self, row):
        """获取交易信号"""
        y_val = row['y_probability']
        x_val = row['inflow_ratio']
        e_val = row['e_value']
        price = row['close']
        median_price = row['median_price']
        deviation = row['price_deviation']
        
        zone = self.classify_strategy_zone(y_val, x_val, e_val)
        
        # 回归中线策略
        if pd.isna(median_price):
            return 'HOLD', zone, 0
        
        # 严格的回归中线策略
        if zone == 'HIGH_PROFIT' and deviation < -self.strategy_params['deviation_threshold']:
            # 高值盈利区且价格低于中线：买涨
            return 'BUY', zone, self.calculate_kelly_position(0.65, 2.0)
        elif zone == 'STRONG_LOSS' and deviation > self.strategy_params['deviation_threshold']:
            # 强亏损区且价格高于中线：买跌
            return 'SELL', zone, self.calculate_kelly_position(0.55, 2.0)
        elif zone == 'CONTROL':
            # 控股商控制区：观望
            return 'HOLD', zone, 0
        else:
            # 其他情况：根据偏离程度决定
            if abs(deviation) > self.strategy_params['deviation_threshold']:
                if deviation > 0:
                    # 价格高于中线：买跌
                    return 'SELL', zone, self.calculate_kelly_position(0.5, 2.0)
                else:
                    # 价格低于中线：买涨
                    return 'BUY', zone, self.calculate_kelly_position(0.5, 2.0)
            else:
                return 'HOLD', zone, 0

    def backtest_strategy(self):
        """执行严格回测策略"""
        print("\n🚀 开始严格HSI50回测...")
        print("="*60)
        print(f"💰 初始资金: {self.initial_capital:,} 港币")
        print(f"📊 策略: 不持仓 + 回归中线 + Cosmoon博弈论 + 凯利公式")
        print(f"🔄 复利计算: {self.strategy_params['compound_interest']}")
        print(f"📈 高值盈利区: Y > {self.strategy_params['high_profit_y']}, X > {self.strategy_params['high_profit_x']}")
        print(f"📉 强亏损区: Y < {self.strategy_params['strong_loss_y']} 或 X < {self.strategy_params['strong_loss_x']}")
        print(f"⏸️ 控股商控制区: {self.strategy_params['control_zone_min']} < Y < {self.strategy_params['control_zone_max']}")
        print(f"📊 回归中线周期: {self.strategy_params['lookback_period']} 天")
        print(f"🎲 凯利公式: 1:{self.strategy_params['kelly_odds_ratio']} 赔率")
        print("="*60)

        # 统计变量
        total_trades = 0
        winning_trades = 0
        losing_trades = 0
        hold_days = 0

        # 跳过前60天用于指标计算
        for i in range(60, len(self.data)):
            row = self.data.iloc[i]
            date = row['date']
            price = row['close']

            # 获取交易信号
            signal, zone, position_size = self.get_trading_signal(row)

            if signal == 'HOLD':
                hold_days += 1
                action = '观望'
                net_profit = 0
            else:
                # 计算投资金额
                investment_amount = self.current_capital * position_size

                if investment_amount < self.strategy_params['transaction_cost'] * 2:
                    # 资金不足，观望
                    hold_days += 1
                    action = '资金不足'
                    net_profit = 0
                else:
                    # 模拟当日开平仓交易
                    shares = (investment_amount - self.strategy_params['transaction_cost']) / price

                    # 假设当日收盘价格变动（简化模拟）
                    if i < len(self.data) - 1:
                        next_price = self.data.iloc[i + 1]['open']  # 使用次日开盘价
                    else:
                        next_price = price * (1 + np.random.normal(0, 0.01))  # 最后一天随机变动

                    # 计算盈亏
                    if signal == 'BUY':
                        price_diff = next_price - price
                        action = '买涨'
                    else:  # SELL
                        price_diff = price - next_price
                        action = '买跌'

                    gross_profit = price_diff * shares
                    net_profit = gross_profit - self.strategy_params['transaction_cost']

                    # 复利计算
                    if self.strategy_params['compound_interest']:
                        self.current_capital += net_profit

                    # 记录交易
                    trade_record = {
                        'date': date.strftime('%Y-%m-%d'),
                        'action': action,
                        'zone': zone,
                        'price': round(price, 2),
                        'next_price': round(next_price, 2),
                        'shares': round(shares, 2),
                        'investment': round(investment_amount, 2),
                        'position_size': round(position_size * 100, 1),
                        'gross_profit': round(gross_profit, 2),
                        'transaction_cost': self.strategy_params['transaction_cost'],
                        'net_profit': round(net_profit, 2),
                        'capital_after': round(self.current_capital, 2),
                        'y_value': round(row['y_probability'], 3),
                        'x_value': round(row['inflow_ratio'], 3),
                        'e_value': round(row['e_value'], 3),
                        'median_price': round(row['median_price'], 2),
                        'deviation': round(row['price_deviation'] * 100, 2)
                    }

                    self.trades.append(trade_record)

                    if net_profit > 0:
                        winning_trades += 1
                    else:
                        losing_trades += 1

                    total_trades += 1

            # 记录每日组合价值
            self.daily_portfolio.append({
                'date': date.strftime('%Y-%m-%d'),
                'price': price,
                'capital': self.current_capital,
                'action': action,
                'zone': zone,
                'y_value': row['y_probability'],
                'x_value': row['inflow_ratio'],
                'e_value': row['e_value'],
                'median_price': row['median_price'],
                'deviation': row['price_deviation']
            })

        print(f"\n✅ 严格回测完成!")
        print(f"📊 总交易次数: {total_trades}")
        print(f"📈 盈利次数: {winning_trades}")
        print(f"📉 亏损次数: {losing_trades}")
        print(f"⏸️ 观望天数: {hold_days}")
        if total_trades > 0:
            print(f"🎯 胜率: {winning_trades/total_trades*100:.1f}%")

        return pd.DataFrame(self.trades), pd.DataFrame(self.daily_portfolio)

    def analyze_results(self, trades_df, daily_df):
        """分析回测结果"""
        print("\n📊 严格HSI50回测结果分析")
        print("=" * 60)

        # 基本统计
        total_trades = len(trades_df)
        final_capital = self.current_capital
        total_return = final_capital - self.initial_capital
        total_return_rate = (total_return / self.initial_capital) * 100

        # 年化收益率
        years = 20
        annual_return_rate = ((final_capital / self.initial_capital) ** (1/years) - 1) * 100

        if total_trades > 0:
            winning_trades = len(trades_df[trades_df['net_profit'] > 0])
            win_rate = winning_trades / total_trades * 100
            max_profit = trades_df['net_profit'].max()
            max_loss = trades_df['net_profit'].min()
            avg_profit = trades_df['net_profit'].mean()
            total_trading_profit = trades_df['net_profit'].sum()

            # 按区域分析
            zone_stats = trades_df.groupby('zone').agg({
                'net_profit': ['count', 'sum', 'mean'],
                'position_size': 'mean'
            }).round(2)

            # 按动作分析
            action_stats = trades_df.groupby('action').agg({
                'net_profit': ['count', 'sum', 'mean'],
                'position_size': 'mean'
            }).round(2)
        else:
            winning_trades = 0
            win_rate = 0
            max_profit = 0
            max_loss = 0
            avg_profit = 0
            total_trading_profit = 0
            zone_stats = pd.DataFrame()
            action_stats = pd.DataFrame()

        # 观望统计
        hold_count = len(daily_df[daily_df['action'] == '观望'])
        hold_rate = hold_count / len(daily_df) * 100

        # 输出结果
        print(f"💰 资金统计:")
        print(f"• 初始资金: {self.initial_capital:,} 港元")
        print(f"• 最终资金: {final_capital:,.0f} 港元")
        print(f"• 总收益: {total_return:,.0f} 港元")
        print(f"• 总收益率: {total_return_rate:.2f}%")
        print(f"• 年化收益率: {annual_return_rate:.2f}%")

        if total_trades > 0:
            print(f"\n📈 交易统计:")
            print(f"• 总交易次数: {total_trades}")
            print(f"• 盈利次数: {winning_trades}")
            print(f"• 胜率: {win_rate:.1f}%")
            print(f"• 最大单笔盈利: {max_profit:,.0f} 港元")
            print(f"• 最大单笔亏损: {max_loss:,.0f} 港元")
            print(f"• 平均每笔盈亏: {avg_profit:,.0f} 港元")
            print(f"• 交易总盈亏: {total_trading_profit:,.0f} 港元")

            print(f"\n📊 区域分析:")
            print(zone_stats)

            print(f"\n📊 动作分析:")
            print(action_stats)

        print(f"\n⏸️ 持仓不动统计:")
        print(f"• 观望天数: {hold_count}")
        print(f"• 观望比例: {hold_rate:.1f}%")

        # 凯利公式验证
        if total_trades > 0:
            actual_win_rate = win_rate / 100
            actual_odds_ratio = abs(max_profit / max_loss) if max_loss != 0 else 2

            print(f"\n🎲 凯利公式验证:")
            print(f"• 实际胜率: {actual_win_rate:.3f}")
            print(f"• 实际赔率: 1:{actual_odds_ratio:.2f}")
            print(f"• 预设胜率: {self.strategy_params['kelly_win_rate']:.3f}")
            print(f"• 预设赔率: 1:{self.strategy_params['kelly_odds_ratio']:.2f}")

        # 保存到Excel
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"HSI50严格回测结果_{timestamp}.xlsx"

        with pd.ExcelWriter(filename, engine='openpyxl') as writer:
            if len(trades_df) > 0:
                trades_df.to_excel(writer, sheet_name='交易记录', index=False)
            daily_df.to_excel(writer, sheet_name='每日数据', index=False)

            # 汇总统计
            summary_data = {
                '项目': ['初始资金(港元)', '最终资金(港元)', '总收益(港元)',
                        '总收益率(%)', '年化收益率(%)', '总交易次数',
                        '盈利次数', '胜率(%)', '观望天数', '观望比例(%)',
                        '最大单笔盈利(港元)', '最大单笔亏损(港元)', '平均每笔盈亏(港元)'],
                '数值': [self.initial_capital, round(final_capital, 0), round(total_return, 0),
                        round(total_return_rate, 2), round(annual_return_rate, 2), total_trades,
                        winning_trades, round(win_rate, 1), hold_count, round(hold_rate, 1),
                        round(max_profit, 0) if total_trades > 0 else 0,
                        round(max_loss, 0) if total_trades > 0 else 0,
                        round(avg_profit, 0) if total_trades > 0 else 0]
            }
            pd.DataFrame(summary_data).to_excel(writer, sheet_name='汇总统计', index=False)

            if len(zone_stats) > 0:
                zone_stats.to_excel(writer, sheet_name='区域分析')
            if len(action_stats) > 0:
                action_stats.to_excel(writer, sheet_name='动作分析')

        print(f"\n✅ 详细结果已保存至: {filename}")
        return filename

    def create_charts(self, trades_df, daily_df):
        """创建图表"""
        print("📊 生成图表...")

        # 创建图表
        fig = plt.figure(figsize=(20, 16))

        # 1. 资金增长曲线
        ax1 = plt.subplot(3, 2, 1)
        daily_df['date_dt'] = pd.to_datetime(daily_df['date'])
        daily_df['capital_万'] = daily_df['capital'] / 10000

        plt.plot(daily_df['date_dt'], daily_df['capital_万'], linewidth=2, color='blue', label='资金曲线')
        plt.axhline(y=self.initial_capital/10000, color='red', linestyle='--', alpha=0.7, label='初始资金')
        plt.title('资金增长曲线 (严格回测)', fontsize=14, fontweight='bold')
        plt.xlabel('日期')
        plt.ylabel('资金 (万港币)')
        plt.legend()
        plt.grid(True, alpha=0.3)
        ax1.xaxis.set_major_formatter(mdates.DateFormatter('%Y'))
        ax1.xaxis.set_major_locator(mdates.YearLocator(2))
        plt.xticks(rotation=45)

        # 2. 恒生指数价格走势 vs 回归中线
        ax2 = plt.subplot(3, 2, 2)
        plt.plot(daily_df['date_dt'], daily_df['price'], linewidth=1, color='red', alpha=0.7, label='恒指价格')
        plt.plot(daily_df['date_dt'], daily_df['median_price'], linewidth=1, color='green', alpha=0.7, label='回归中线')
        plt.title('恒生指数价格 vs 回归中线', fontsize=14, fontweight='bold')
        plt.xlabel('日期')
        plt.ylabel('恒指点数')
        plt.legend()
        plt.grid(True, alpha=0.3)
        ax2.xaxis.set_major_formatter(mdates.DateFormatter('%Y'))
        ax2.xaxis.set_major_locator(mdates.YearLocator(2))
        plt.xticks(rotation=45)

        # 3. 策略区域分布
        ax3 = plt.subplot(3, 2, 3)
        zone_counts = daily_df['zone'].value_counts()
        colors = ['#ff9999', '#66b3ff', '#99ff99', '#ffcc99']
        plt.pie(zone_counts.values, labels=zone_counts.index, autopct='%1.1f%%',
                colors=colors, startangle=90)
        plt.title('策略区域时间分布', fontsize=14, fontweight='bold')

        # 4. 交易动作分布
        ax4 = plt.subplot(3, 2, 4)
        action_counts = daily_df['action'].value_counts()
        colors = ['#ff9999', '#66b3ff', '#99ff99', '#ffcc99', '#ff99cc']
        plt.pie(action_counts.values, labels=action_counts.index, autopct='%1.1f%%',
                colors=colors, startangle=90)
        plt.title('交易动作分布', fontsize=14, fontweight='bold')

        # 5. X、Y值散点图
        ax5 = plt.subplot(3, 2, 5)
        colors_map = {'HIGH_PROFIT': 'red', 'STRONG_LOSS': 'blue', 'OTHER': 'green', 'CONTROL': 'orange'}
        for zone in daily_df['zone'].unique():
            zone_data = daily_df[daily_df['zone'] == zone]
            plt.scatter(zone_data['x_value'], zone_data['y_value'],
                       c=colors_map.get(zone, 'gray'), label=zone, alpha=0.6, s=1)

        plt.axhline(y=0.5, color='red', linestyle='--', alpha=0.5)
        plt.axvline(x=0.5, color='red', linestyle='--', alpha=0.5)
        plt.axhline(y=0.25, color='blue', linestyle='--', alpha=0.5)
        plt.axvline(x=0.25, color='blue', linestyle='--', alpha=0.5)
        plt.axhline(y=0.333, color='orange', linestyle='--', alpha=0.5)
        plt.axhline(y=0.4, color='orange', linestyle='--', alpha=0.5)

        plt.title('X、Y值分布与策略区域', fontsize=14, fontweight='bold')
        plt.xlabel('X值 (资金流比例)')
        plt.ylabel('Y值 (控制系数)')
        plt.legend()
        plt.grid(True, alpha=0.3)

        # 6. 价格偏离度分布
        ax6 = plt.subplot(3, 2, 6)
        deviation_data = daily_df['deviation'].dropna()
        plt.hist(deviation_data, bins=50, alpha=0.7, color='skyblue', edgecolor='black')
        plt.axvline(self.strategy_params['deviation_threshold'] * 100, color='red', linestyle='--',
                   label=f'阈值: ±{self.strategy_params["deviation_threshold"]*100}%')
        plt.axvline(-self.strategy_params['deviation_threshold'] * 100, color='red', linestyle='--')
        plt.axvline(0, color='black', linestyle='-', alpha=0.3)
        plt.title('价格偏离回归中线分布', fontsize=14, fontweight='bold')
        plt.xlabel('偏离度 (%)')
        plt.ylabel('天数')
        plt.legend()
        plt.grid(True, alpha=0.3)

        plt.tight_layout()

        # 保存图表
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        chart_filename = f"HSI50严格回测图表_{timestamp}.png"
        plt.savefig(chart_filename, dpi=300, bbox_inches='tight')
        print(f"✅ 图表已保存至: {chart_filename}")

        plt.show()

        return chart_filename

def main():
    """主函数"""
    print("🏢 HSI50严格回测系统")
    print("=" * 60)
    print("💰 总资金: 30,000港元")
    print("📊 分析周期: 20年历史数据")
    print("🎯 策略: 不持仓 + 回归中线 + Cosmoon博弈论 + 凯利公式")
    print("📈 高值盈利区: Y>0.5, X>0.5 且价格低于中线 (买涨)")
    print("📉 强亏损区: Y<0.25或X<0.25 且价格高于中线 (买跌)")
    print("⏸️ 控股商控制区: 0.333<Y<0.4 (观望)")
    print("📊 回归中线: 20日中位数，偏离阈值±2%")
    print("🎲 凯利公式: 1:2赔率，最大仓位25%")
    print("🔄 复利计算: 启用")
    print("📅 交易模式: 每日开平仓，不持仓过夜")

    # 创建回测器
    backtester = StrictHSI50Backtest()

    # 获取数据
    if not backtester.fetch_hsi_data():
        return

    # 计算指标
    backtester.calculate_indicators()

    # 执行回测
    trades_df, daily_df = backtester.backtest_strategy()

    # 分析结果
    backtester.analyze_results(trades_df, daily_df)

    # 创建图表
    backtester.create_charts(trades_df, daily_df)

if __name__ == "__main__":
    main()
