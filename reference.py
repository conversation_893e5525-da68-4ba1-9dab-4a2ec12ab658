#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
HSI50指数20年回测系统
===================

特点：
1. 使用回归中线作为趋势判断
2. 使用凯利公式优化仓位
3. 采用Cosmoon XYE方法
4. 每月复利加入1000

作者: Cosmoon NG
日期: 2025年7月
"""

import sqlite3
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats


import matplotlib.pyplot as plt
plt.rcParams['font.sans-serif'] = ['SimHei']  # Use SimHei for Chinese characters
plt.rcParams['axes.unicode_minus'] = False  # This is to display minus signs correctly


class HSI50Backtest:
    def __init__(self):
        """初始化回测系统"""
        self.initial_capital = 1000  # 初始资金
        self.monthly_addition = 1000  # 每月追加资金
        self.take_profit_long = 0.016  # 多头止盈 1.6%
        self.stop_loss_long = 0.008    # 多头止损 0.8%
        self.take_profit_short = 0.016  # 空头止盈 1.6%
        self.stop_loss_short = 0.016   # 空头止损 1.6%
        self.position = 0  # 当前持仓，1为多头，-1为空头，0为空仓
        self.current_price = 0  # 当前持仓价格
        self.kelly_fraction = 0.5  # 凯利公式分数，用于控制风险
        
    def load_data(self):
        """从数据库加载数据"""
        print("\n1. 加载数据...")
        try:
            # 首先尝试连接 hsi_25years.db
            try:
                conn = sqlite3.connect('hsi_25years.db')
                # 只加载最近10年的数据
                ten_years_ago = (datetime.now() - timedelta(days=365*10)).strftime('%Y-%m-%d')
                self.df = pd.read_sql(f"""
                    SELECT date, open, high, low, close, volume,
                           ma_20, ma_60, rsi, volume_ratio, inflow_ratio
                    FROM hsi_data 
                    WHERE date >= '{ten_years_ago}'
                    ORDER BY date
                """, conn)
                print("✓ 从 hsi_25years.db 加载数据成功")
            except:
                # 如果失败，尝试连接 finance.db
                conn = sqlite3.connect('finance.db')
                self.df = pd.read_sql("""
                    SELECT date, open, high, low, close, volume,
                           ma20 as ma_20, ma60 as ma_60, rsi,
                           COALESCE(money_flow, 0) as inflow_ratio,
                           1 as volume_ratio
                    FROM hkhsi50 
                    ORDER BY date
                """, conn)
                print("✓ 从 finance.db 加载数据成功")
            
            # 转换日期
            self.df['date'] = pd.to_datetime(self.df['date'])
            
            # 如果缺少某些列，使用合理的默认值
            if 'volume_ratio' not in self.df.columns:
                self.df['volume_ratio'] = 1.0
            if 'inflow_ratio' not in self.df.columns:
                self.df['inflow_ratio'] = 0.5
                
            print(f"✓ 加载了 {len(self.df)} 条数据")
            print(f"数据范围：{self.df['date'].min().date()} 至 {self.df['date'].max().date()}")
            
            # 检查数据是否为空
            if len(self.df) == 0:
                raise Exception("没有数据加载")
                
        except Exception as e:
            print(f"❌ 数据加载失败: {str(e)}")
            raise
            
        finally:
            if 'conn' in locals():
                conn.close()
        
    def calculate_regression_line(self):
        """计算回归线和技术指标"""
        print("\n2. 计算技术指标...")
        # 添加序号用于回归计算
        self.df['i'] = range(1, len(self.df) + 1)
        
        # 计算60日回归线
        window = 60
        self.df['regression_line'] = self.df['close'].rolling(window=window).apply(
            lambda x: stats.linregress(range(len(x)), x)[0] * (window-1) + stats.linregress(range(len(x)), x)[1]
        )
        
        # 计算价格相对回归线的位置（百分比）
        self.df['price_position'] = (self.df['close'] - self.df['regression_line']) / self.df['regression_line']
        
        # 计算趋势强度（回归线斜率）
        self.df['trend_strength'] = self.df['regression_line'].diff() / self.df['regression_line'].shift(1)
        
        # 计算波动率（20日）
        self.df['volatility'] = self.df['close'].pct_change().rolling(window=20).std()
        
        # 计算RSI的变化率
        self.df['rsi_change'] = self.df['rsi'].diff()
        
        print("✓ 技术指标计算完成")
        
    def calculate_kelly(self, win_rate, profit_ratio, loss_ratio):
        """计算凯利公式建议仓位"""
        if win_rate <= 0 or profit_ratio <= 0:
            return 0
        q = 1 - win_rate
        kelly = (win_rate/q * profit_ratio - 1) / profit_ratio
        kelly *= self.kelly_fraction  # 使用保守的凯利分数
        return max(0, min(kelly, 0.5))  # 最大仓位限制在50%
    
    def get_position_size(self, price, capital, volatility):
        """计算仓位大小"""
        # 基于历史数据计算胜率和盈亏比
        if len(self.trades) < 10:
            win_rate = 0.5
        else:
            trades_df = pd.DataFrame(self.trades)
            win_rate = len(trades_df[trades_df['profit'] > 0]) / len(trades_df)
        
        # 根据波动率调整盈亏比
        if self.position == 1:  # 多头
            profit_ratio = self.take_profit_long
            loss_ratio = self.stop_loss_long
        else:  # 空头
            profit_ratio = self.take_profit_short
            loss_ratio = self.stop_loss_short
            
        # 使用凯利公式计算仓位比例
        kelly = self.calculate_kelly(win_rate, profit_ratio, loss_ratio)
        
        # 根据波动率调整仓位
        volatility_factor = 1 - min(volatility * 100, 0.5)  # 波动率越大，仓位越小
        
        return capital * kelly * volatility_factor
    
    def add_monthly_capital(self, date, capital):
        """每月增加资金"""
        # 获取上一次操作的月份
        last_month = getattr(self, 'last_month', None)
        current_month = date.replace(day=1)
        
        # 如果是新的月份，增加资金
        if last_month is None or current_month > last_month:
            self.last_month = current_month
            return capital + self.monthly_addition
        
        return capital
    
    def check_entry_conditions(self, row):
        """检查入场条件"""
        # 检查趋势强度
        trend_is_strong = abs(row['trend_strength']) > 0.0005
        
        # 检查RSI条件
        rsi_condition = (row['rsi'] < 30 and row['rsi_change'] > 0) or (row['rsi'] > 70 and row['rsi_change'] < 0)
        
        # 检查成交量条件
        volume_active = row['volume_ratio'] > 1.2
        
        # 多头条件
        if (row['price_position'] < -0.0005 and  # 价格显著低于回归线
            row['rsi'] < 30 and  # RSI超卖
            row['rsi_change'] > 0 and  # RSI开始上升
            trend_is_strong and  # 趋势强
            volume_active and  # 成交量活跃
            row['inflow_ratio'] > 0.6):  # 资金流入强势
            return 1
        
        # 空头条件
        elif (row['price_position'] > 0.0005 and  # 价格显著高于回归线
              row['rsi'] > 70 and  # RSI超买
              row['rsi_change'] < 0 and  # RSI开始下降
              trend_is_strong and  # 趋势强
              volume_active and  # 成交量活跃
              row['inflow_ratio'] < 0.4):  # 资金流出强势
            return -1
        
        return 0
    
    def run_backtest(self):
        """运行回测"""
        print("\n3. 开始回测...")
        
        # 准备结果记录
        self.trades = []
        capital = self.initial_capital
        self.equity_curve = []
        last_trade_date = None
        
        # 设置最小交易间隔（避免过度交易）
        min_trade_interval = timedelta(days=5)
        
        for i in range(60, len(self.df)):  # 从第60天开始，确保有足够的历史数据
            row = self.df.iloc[i]
            date = pd.to_datetime(row['date'])
            
            # 每月增加资金
            capital = self.add_monthly_capital(date, capital)
            
            # 记录权益
            self.equity_curve.append({
                'date': date,
                'equity': capital,
                'position': self.position
            })
            
            # 检查是否可以交易（避免过度交易）
            if last_trade_date and (date - last_trade_date) < min_trade_interval:
                continue
            
            # 如果有持仓，检查止盈止损
            if self.position != 0:
                if self.position == 1:  # 多头
                    profit_ratio = (row['high'] - self.current_price) / self.current_price
                    loss_ratio = (self.current_price - row['low']) / self.current_price
                    
                    if profit_ratio >= self.take_profit_long:  # 止盈
                        exit_price = self.current_price * (1 + self.take_profit_long)
                        profit = (exit_price - self.current_price) / self.current_price * capital
                        capital += profit
                        self.position = 0
                        self.trades.append({
                            'date': date,
                            'type': 'long_exit_tp',
                            'price': exit_price,
                            'profit': profit,
                            'capital': capital
                        })
                    elif loss_ratio >= self.stop_loss_long:  # 止损
                        exit_price = self.current_price * (1 - self.stop_loss_long)
                        loss = (exit_price - self.current_price) / self.current_price * capital
                        capital += loss
                        self.position = 0
                        self.trades.append({
                            'date': date,
                            'type': 'long_exit_sl',
                            'price': exit_price,
                            'profit': loss,
                            'capital': capital
                        })
                
                elif self.position == -1:  # 空头
                    profit_ratio = (self.current_price - row['low']) / self.current_price
                    loss_ratio = (row['high'] - self.current_price) / self.current_price
                    
                    if profit_ratio >= self.take_profit_short:  # 止盈
                        exit_price = self.current_price * (1 - self.take_profit_short)
                        profit = (self.current_price - exit_price) / self.current_price * capital
                        capital += profit
                        self.position = 0
                        self.trades.append({
                            'date': date,
                            'type': 'short_exit_tp',
                            'price': exit_price,
                            'profit': profit,
                            'capital': capital
                        })
                    elif loss_ratio >= self.stop_loss_short:  # 止损
                        exit_price = self.current_price * (1 + self.stop_loss_short)
                        loss = (exit_price - self.current_price) / self.current_price * capital * -1
                        capital += loss
                        self.position = 0
                        self.trades.append({
                            'date': date,
                            'type': 'short_exit_sl',
                            'price': exit_price,
                            'profit': loss,
                            'capital': capital
                        })
            
            # 如果空仓，判断是否开仓
            if self.position == 0:
                position_signal = self.check_entry_conditions(row)
                
                if position_signal != 0:
                    # 计算仓位大小
                    position_size = self.get_position_size(row['close'], capital, row['volatility'])
                    
                    if position_size > 1000:  # 最小交易金额限制
                        self.position = position_signal
                        self.current_price = row['close']
                        last_trade_date = date
                        
                        self.trades.append({
                            'date': date,
                            'type': 'long_entry' if position_signal == 1 else 'short_entry',
                            'price': self.current_price,
                            'size': position_size,
                            'capital': capital
                        })
        
        self.final_capital = capital
        print(f"✓ 回测完成！最终资金：{self.final_capital:,.2f}")
    
    def analyze_results(self):
        """分析回测结果"""
        print("\n=== 回测分析 ===")
        
        # 转换交易记录为DataFrame
        trades_df = pd.DataFrame(self.trades)
        if len(trades_df) == 0:
            print("没有产生任何交易")
            return
        
        # 计算基本统计数据
        total_trades = len(trades_df[trades_df['type'].str.contains('entry')])
        winning_trades = len(trades_df[trades_df['profit'] > 0]) if 'profit' in trades_df.columns else 0
        profit_trades = trades_df[trades_df['profit'] > 0] if 'profit' in trades_df.columns else pd.DataFrame()
        loss_trades = trades_df[trades_df['profit'] < 0] if 'profit' in trades_df.columns else pd.DataFrame()
        
        print(f"\n交易统计：")
        print(f"总交易次数：{total_trades}")
        print(f"盈利交易：{winning_trades}")
        print(f"亏损交易：{total_trades - winning_trades}")
        print(f"胜率：{winning_trades/total_trades*100:.2f}%")
        
        if len(profit_trades) > 0:
            print(f"平均盈利：{profit_trades['profit'].mean():.2f}")
            print(f"最大盈利：{profit_trades['profit'].max():.2f}")
        
        if len(loss_trades) > 0:
            print(f"平均亏损：{loss_trades['profit'].mean():.2f}")
            print(f"最大亏损：{loss_trades['profit'].min():.2f}")
        
        # 计算收益率
        initial_equity = self.initial_capital
        final_equity = self.final_capital
        total_days = (self.df['date'].max() - self.df['date'].min()).days
        total_years = total_days / 365
        
        total_return = (final_equity - initial_equity) / initial_equity
        annual_return = (1 + total_return) ** (1/total_years) - 1
        
        print(f"\n收益统计：")
        print(f"初始资金：{initial_equity:.2f}")
        print(f"最终资金：{final_equity:.2f}")
        print(f"总收益率：{total_return*100:.2f}%")
        print(f"年化收益率：{annual_return*100:.2f}%")
        
        # 绘制权益曲线
        equity_df = pd.DataFrame(self.equity_curve)
        plt.figure(figsize=(15, 7))
        plt.plot(equity_df['date'], equity_df['equity'])
        plt.title('权益曲线')
        plt.xlabel('日期')
        plt.ylabel('资金')
        plt.grid(True)
        plt.savefig('equity_curve.png')
        plt.close()
        
        # 保存交易记录
        if len(trades_df) > 0:
            trades_df.to_excel('trade_records.xlsx', index=False)
            print("\n交易记录已保存到 trade_records.xlsx")
        
        print("权益曲线已保存到 equity_curve.png")

def main():
    """主函数"""
    print("HSI50指数20年回测系统")
    print("="*50)
    
    try:
        # 创建回测实例
        backtest = HSI50Backtest()
        
        # 加载数据
        backtest.load_data()
        
        # 计算回归线
        backtest.calculate_regression_line()
        
        # 运行回测
        backtest.run_backtest()
        
        # 分析结果
        backtest.analyze_results()
        
    except Exception as e:
        print(f"❌ 运行失败: {str(e)}")

if __name__ == "__main__":
    main()
