#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
添加技术指标字段到Excel表格
==========================

添加字段:
- Full_Y (行控制系数)
- MoneyFlowRatio (资金流比率)
- 止盈价
- 止损价

并修正资金数据为实际情况

作者: Cosmoon NG
"""

import pandas as pd
import mysql.connector
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

def get_database_technical_data():
    """从数据库获取技术指标数据"""
    try:
        config = {
            'host': 'localhost',
            'port': 3306,
            'user': 'root',
            'password': '12345678',
            'database': 'finance'
        }
        
        connection = mysql.connector.connect(**config)
        cursor = connection.cursor()
        
        query = """
        SELECT Y_Value, X_Value, E_Value, Full_Y, MoneyFlowRatio, MFI, RSI
        FROM eab_0023hk 
        ORDER BY Date DESC 
        LIMIT 1
        """
        
        cursor.execute(query)
        result = cursor.fetchone()
        
        cursor.close()
        connection.close()
        
        if result:
            return {
                'y_value': result[0],
                'x_value': result[1], 
                'e_value': result[2],
                'full_y': result[3],
                'money_flow_ratio': result[4],
                'mfi': result[5],
                'rsi': result[6]
            }
    except Exception as e:
        print(f"⚠️ 数据库查询失败: {e}")
    
    # 默认值
    return {
        'y_value': 0.2069,
        'x_value': 0.3211,
        'e_value': -0.0525,
        'full_y': 0.2069,  # Full_Y通常等于Y_Value
        'money_flow_ratio': 0.3211,  # MoneyFlowRatio通常等于X_Value
        'mfi': 32.11,
        'rsi': 20.69
    }

def add_technical_fields():
    """添加技术指标字段到Excel表格"""
    
    excel_file = "交易记录追踪0023HK.xlsx"
    
    print("📊 添加技术指标字段到Excel表格")
    print("=" * 50)
    
    try:
        # 读取Excel文件
        df = pd.read_excel(excel_file)
        print(f"📋 当前记录数: {len(df)}")
        print(f"📊 当前列数: {len(df.columns)}")
        
        # 获取数据库技术指标
        tech_data = get_database_technical_data()
        print(f"📈 获取技术指标数据: {'成功' if tech_data else '失败'}")
        
        # 找到Y值列的位置
        y_col_idx = df.columns.get_loc('Y值') if 'Y值' in df.columns else len(df.columns)
        
        # 添加新字段 (在Y值附近)
        new_fields = []
        
        if 'Full_Y' not in df.columns:
            df.insert(y_col_idx + 1, 'Full_Y', 0.0000)
            new_fields.append('Full_Y')
            print("➕ 添加 Full_Y 列")
        
        if 'MoneyFlowRatio' not in df.columns:
            df.insert(y_col_idx + 2, 'MoneyFlowRatio', 0.0000)
            new_fields.append('MoneyFlowRatio')
            print("➕ 添加 MoneyFlowRatio 列")
        
        # 添加止盈止损价格字段
        if '止盈价' not in df.columns:
            price_col_idx = df.columns.get_loc('交易价格') if '交易价格' in df.columns else 4
            df.insert(price_col_idx + 1, '止盈价', 0.00)
            new_fields.append('止盈价')
            print("➕ 添加 止盈价 列")
        
        if '止损价' not in df.columns:
            profit_col_idx = df.columns.get_loc('止盈价') if '止盈价' in df.columns else 5
            df.insert(profit_col_idx + 1, '止损价', 0.00)
            new_fields.append('止损价')
            print("➕ 添加 止损价 列")
        
        # 更新最新记录的数据
        latest_idx = len(df) - 1
        
        # 用户实际参数
        total_capital = 2500.00
        max_shares = 200
        current_price = 12.18
        
        # 计算交易数据
        trade_amount = current_price * max_shares  # 2436港元
        commission = trade_amount * 0.001          # 2.44港元
        account_balance = total_capital - trade_amount - commission  # 61.56港元
        current_market_value = trade_amount
        total_assets = account_balance + current_market_value
        
        # 计算止盈止损价格 (空头)
        take_profit_rate = 0.012  # 1.2%
        stop_loss_rate = 0.006    # 0.6%
        take_profit_price = current_price * (1 - take_profit_rate)  # 12.03
        stop_loss_price = current_price * (1 + stop_loss_rate)      # 12.25
        
        # 更新所有字段
        updates = {
            '交易日期': datetime.now().strftime('%Y-%m-%d'),
            '交易类型': '开仓',
            '交易方向': '空头',
            '交易价格': current_price,
            '止盈价': take_profit_price,
            '止损价': stop_loss_price,
            '持仓数量': max_shares,
            '交易金额': trade_amount,
            '手续费': commission,
            '净交易额': trade_amount - commission,
            '持仓成本': current_price,
            '当前市值': current_market_value,
            '浮动盈亏': 0.00,
            '实现盈亏': 0.00,
            '累计盈亏': 0.00,
            '账户余额': account_balance,
            '总资产': total_assets,
            '收益率': 0.00,
            '累计收益率': (total_assets - total_capital) / total_capital * 100,
            'Y值': tech_data['y_value'],
            'Full_Y': tech_data['full_y'],
            'MoneyFlowRatio': tech_data['money_flow_ratio'],
            'X值': tech_data['x_value'],
            'E值': tech_data['e_value'],
            '信号强度': '强烈卖出',
            '风险等级': '高风险',
            '备注': f'完整技术指标 总资本{total_capital:,.0f}港元 持仓{max_shares}股 含Full_Y和MFR'
        }
        
        # 更新最新记录
        for field, value in updates.items():
            if field in df.columns:
                df.iloc[latest_idx, df.columns.get_loc(field)] = value
        
        # 保存Excel文件
        df.to_excel(excel_file, index=False)
        
        print(f"✅ Excel文件已更新: {excel_file}")
        print(f"📊 新增字段: {', '.join(new_fields)}")
        print(f"📋 总列数: {len(df.columns)}")
        
        # 显示技术指标详情
        print(f"\n📈 技术指标详情:")
        print(f"   Y值: {tech_data['y_value']:.4f}")
        print(f"   Full_Y: {tech_data['full_y']:.4f}")
        print(f"   X值: {tech_data['x_value']:.4f}")
        print(f"   MoneyFlowRatio: {tech_data['money_flow_ratio']:.4f}")
        print(f"   E值: {tech_data['e_value']:.4f}")
        print(f"   MFI: {tech_data['mfi']:.2f}")
        print(f"   RSI: {tech_data['rsi']:.2f}")
        
        # 显示止盈止损详情
        print(f"\n🎯 止盈止损设置:")
        print(f"   交易价格: {current_price:.2f} 港元")
        print(f"   止盈价: {take_profit_price:.2f} 港元 (-1.2%)")
        print(f"   止损价: {stop_loss_price:.2f} 港元 (+0.6%)")
        
        # 显示资金状况
        print(f"\n💰 资金状况:")
        print(f"   总资本: {total_capital:,.2f} 港元")
        print(f"   持仓: {max_shares} 股")
        print(f"   交易金额: {trade_amount:,.2f} 港元")
        print(f"   账户余额: {account_balance:,.2f} 港元")
        print(f"   总资产: {total_assets:,.2f} 港元")
        print(f"   累计收益率: {updates['累计收益率']:.2f}%")
        
        # 显示字段列表
        print(f"\n📋 完整字段列表:")
        for i, col in enumerate(df.columns, 1):
            marker = "🆕" if col in new_fields else "📊"
            print(f"   {i:2d}. {marker} {col}")
        
        return True
        
    except Exception as e:
        print(f"❌ 操作失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    try:
        success = add_technical_fields()
        
        if success:
            print(f"\n🎯 Excel表格更新完成！")
            print(f"✅ 已添加 Full_Y 和 MoneyFlowRatio 字段")
            print(f"✅ 已添加止盈价和止损价字段")
            print(f"💰 资金数据已修正为实际情况")
            print(f"📊 技术指标数据已从数据库获取")
        else:
            print(f"\n❌ Excel表格更新失败")
            
    except Exception as e:
        print(f"❌ 系统错误: {e}")

if __name__ == "__main__":
    main()
