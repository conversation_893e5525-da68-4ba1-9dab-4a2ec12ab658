#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试集成后的sp_updatecontroller函数
==================================
正确调用存储过程并显示结果
"""

import mysql.connector
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

class ControllerTester:
    def __init__(self):
        """初始化数据库连接"""
        self.db_config = {
            'host': '************',
            'database': 'finance',
            'user': 'root',
            'password': '',
            'charset': 'utf8mb4',
            'autocommit': True
        }
        self.connection = None
        self.cursor = None
        
    def connect_database(self):
        """连接数据库"""
        try:
            print("🔗 连接MySQL数据库...")
            self.connection = mysql.connector.connect(**self.db_config)
            self.cursor = self.connection.cursor()
            print(f"✅ 成功连接到数据库: {self.db_config['host']}/{self.db_config['database']}")
            return True
        except mysql.connector.Error as e:
            print(f"❌ 数据库连接失败: {e}")
            return False
    
    def test_table_exists(self, table_name):
        """检查表是否存在"""
        try:
            self.cursor.execute(f"SHOW TABLES LIKE '{table_name}'")
            result = self.cursor.fetchone()
            if result:
                print(f"✅ 表 {table_name} 存在")
                
                # 查看表结构
                self.cursor.execute(f"DESCRIBE {table_name}")
                columns = self.cursor.fetchall()
                print(f"📋 {table_name} 表结构:")
                for col in columns:
                    print(f"   • {col[0]} - {col[1]}")
                
                # 查看数据量
                self.cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
                count = self.cursor.fetchone()[0]
                print(f"📊 {table_name} 数据量: {count} 条记录")
                
                return True
            else:
                print(f"❌ 表 {table_name} 不存在")
                return False
        except mysql.connector.Error as e:
            print(f"❌ 检查表失败: {e}")
            return False
    
    def test_sp_averageline(self, table_name):
        """测试sp_averagelineV3存储过程"""
        try:
            print(f"\n🧪 测试sp_averagelineV3存储过程 - 表: {table_name}")
            
            # 调用存储过程
            self.cursor.callproc('sp_averagelineV3', [table_name])
            
            # 获取结果
            for result_set in self.cursor.stored_results():
                rows = result_set.fetchall()
                if rows:
                    print(f"📊 sp_averagelineV3返回结果:")
                    for row in rows:
                        print(f"   {row}")
            
            # 检查midprice是否更新
            self.cursor.execute(f"""
                SELECT 
                    COUNT(*) as total,
                    COUNT(midprice) as midprice_count,
                    MIN(midprice) as min_midprice,
                    MAX(midprice) as max_midprice,
                    AVG(midprice) as avg_midprice
                FROM {table_name}
            """)
            
            result = self.cursor.fetchone()
            print(f"📊 midprice更新结果:")
            print(f"   • 总记录数: {result[0]}")
            print(f"   • midprice记录数: {result[1]}")
            print(f"   • midprice覆盖率: {result[1]/result[0]*100:.1f}%")
            if result[2] is not None:
                print(f"   • midprice范围: {float(result[2]):.4f} ~ {float(result[3]):.4f}")
                print(f"   • midprice平均值: {float(result[4]):.4f}")
            
            return True
            
        except mysql.connector.Error as e:
            print(f"❌ 测试sp_averagelineV3失败: {e}")
            return False
    
    def test_sp_updatecontroller(self, table_name):
        """测试集成后的sp_updatecontroller存储过程"""
        try:
            print(f"\n🧪 测试集成后的sp_updatecontroller - 表: {table_name}")
            
            # 调用存储过程
            args = [table_name, 0]  # 第二个参数是OUT参数的占位符
            result = self.cursor.callproc('sp_updatecontroller', args)
            
            # 获取结果集
            print("📊 sp_updatecontroller执行过程:")
            for result_set in self.cursor.stored_results():
                rows = result_set.fetchall()
                for row in rows:
                    print(f"   {row}")
            
            # 显示OUT参数结果
            k_value = result[1]  # OUT参数result_k的值
            print(f"📊 k值结果: {k_value}")
            
            # 验证controller字段
            self.cursor.execute(f"""
                SELECT 
                    COUNT(*) as total,
                    COUNT(controller) as controller_count,
                    COUNT(midprice) as midprice_count
                FROM {table_name}
            """)
            
            result = self.cursor.fetchone()
            print(f"\n📊 字段验证结果:")
            print(f"   • 总记录数: {result[0]}")
            print(f"   • controller记录数: {result[1]}")
            print(f"   • midprice记录数: {result[2]}")
            print(f"   • controller覆盖率: {result[1]/result[0]*100:.1f}%")
            print(f"   • midprice覆盖率: {result[2]/result[0]*100:.1f}%")
            
            # 查看controller分布
            self.cursor.execute(f"""
                SELECT 
                    controller,
                    COUNT(*) as count,
                    ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM {table_name}), 2) as percentage
                FROM {table_name} 
                WHERE controller IS NOT NULL
                GROUP BY controller
                ORDER BY controller
            """)
            
            controller_stats = self.cursor.fetchall()
            print(f"\n📊 controller字段分布:")
            print("值 | 数量   | 百分比 | 含义")
            print("-" * 35)
            for row in controller_stats:
                meaning = ""
                if row[0] == 0:
                    meaning = "收盘价 < midprice"
                elif row[0] == 1:
                    meaning = "收盘价 > midprice"
                elif row[0] == 3:
                    meaning = "收盘价 = midprice"
                print(f"{row[0]}  | {row[1]:6d} | {row[2]:6.2f}% | {meaning}")
            
            # 显示最新几条数据
            self.cursor.execute(f"""
                SELECT date, close, midprice, controller,
                       ROUND((close - midprice) / midprice * 100, 2) as deviation_pct
                FROM {table_name} 
                WHERE midprice IS NOT NULL AND controller IS NOT NULL
                ORDER BY date DESC 
                LIMIT 10
            """)
            
            latest_data = self.cursor.fetchall()
            print(f"\n📊 最新10条数据样本:")
            print("日期          | 收盘价  | midprice | controller | 偏差%")
            print("-" * 60)
            for row in latest_data:
                date_str = str(row[0])
                close_val = float(row[1]) if row[1] is not None else 0.0
                mid_val = float(row[2]) if row[2] is not None else 0.0
                controller_val = row[3] if row[3] is not None else 'N/A'
                deviation = float(row[4]) if row[4] is not None else 0.0
                print(f"{date_str} | {close_val:7.2f} | {mid_val:8.4f} | {controller_val:10} | {deviation:6.2f}%")
            
            return True
            
        except mysql.connector.Error as e:
            print(f"❌ 测试sp_updatecontroller失败: {e}")
            return False
    
    def generate_sql_examples(self, table_name):
        """生成正确的SQL调用示例"""
        print(f"\n📝 正确的SQL调用示例 (表: {table_name}):")
        print("=" * 60)
        
        print("1️⃣ 只更新midprice:")
        print(f"   CALL sp_averagelineV3('{table_name}');")
        
        print("\n2️⃣ 完整更新 (midprice + controller + k值):")
        print(f"   CALL sp_updatecontroller('{table_name}', @k_value);")
        print("   SELECT @k_value AS k_result;")
        
        print("\n3️⃣ 查看controller分布:")
        print(f"""   SELECT 
       controller,
       COUNT(*) as count,
       ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM {table_name}), 2) as percentage
   FROM {table_name} 
   WHERE controller IS NOT NULL
   GROUP BY controller;""")
        
        print("\n4️⃣ 查看最新数据:")
        print(f"""   SELECT date, close, midprice, controller,
          ROUND((close - midprice) / midprice * 100, 2) as deviation_pct
   FROM {table_name} 
   WHERE midprice IS NOT NULL 
   ORDER BY date DESC 
   LIMIT 10;""")
        
        print("\n⚠️ 注意事项:")
        print("   • 使用单引号包围表名")
        print("   • 必须使用CALL关键字调用存储过程")
        print("   • OUT参数需要用@变量接收")
    
    def close_connection(self):
        """关闭数据库连接"""
        if self.cursor:
            self.cursor.close()
        if self.connection:
            self.connection.close()
        print("🔒 数据库连接已关闭")
    
    def run_test(self, table_name):
        """运行完整测试"""
        print("🎯 测试集成后的sp_updatecontroller函数")
        print("=" * 60)
        print(f"📅 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"🏠 数据库: {self.db_config['host']}/{self.db_config['database']}")
        print(f"📊 测试表: {table_name}")
        
        try:
            # 1. 连接数据库
            if not self.connect_database():
                return False
            
            # 2. 检查表是否存在
            if not self.test_table_exists(table_name):
                return False
            
            # 3. 测试sp_averagelineV3
            if not self.test_sp_averageline(table_name):
                return False
            
            # 4. 测试集成后的sp_updatecontroller
            if not self.test_sp_updatecontroller(table_name):
                return False
            
            # 5. 生成SQL示例
            self.generate_sql_examples(table_name)
            
            print("\n🎉 所有测试通过!")
            print("💡 您可以直接使用上面的SQL示例在查询窗口中执行")
            
            return True
            
        except Exception as e:
            print(f"❌ 测试失败: {e}")
            return False
        
        finally:
            self.close_connection()

def main():
    """主函数"""
    # 测试不同的表
    tables_to_test = [
        'stock_600036_ss',  # 招商银行
        'stock_600887_ss'   # 伊利股份
    ]
    
    tester = ControllerTester()
    
    for table_name in tables_to_test:
        print(f"\n{'='*80}")
        print(f"🧪 测试表: {table_name}")
        print(f"{'='*80}")
        
        success = tester.run_test(table_name)
        
        if success:
            print(f"✅ {table_name} 测试成功")
        else:
            print(f"❌ {table_name} 测试失败")
        
        print("\n" + "="*80)

if __name__ == "__main__":
    main()
