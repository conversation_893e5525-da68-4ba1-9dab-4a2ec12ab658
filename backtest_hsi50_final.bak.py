#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
00023.HK (恒生银行) 20年回测系统
==========================

特点：
1. 使用回归中线作为趋势判断
2. 使用凯利公式优化仓位
3. 采用Cosmoon XYE方法
4. 每月复利加入3000

作者: Cosmoon NG
日期: 2025年7月
"""
import sys
import os
import io
import locale

# 设置标准输出编码为UTF-8
if sys.stdout.encoding != 'utf-8':
    sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8', errors='replace')

# 设置控制台编码
if os.name == 'nt':  # Windows
    if sys.stdout.encoding != 'utf-8':
        import codecs
        sys.stdout = codecs.getwriter('utf-8')(sys.stdout.buffer, 'strict')
    # 设置控制台代码页为UTF-8
    os.system('chcp 65001 > nul')

import sqlite3
import pandas as pd
import numpy as np
import os
import yfinance as yf
from datetime import datetime, timedelta
import matplotlib.pyplot as plt
from scipy import stats
import matplotlib.pyplot as plt
plt.rcParams['font.sans-serif'] = ['SimHei']  # Use SimHei for Chinese characters
plt.rcParams['axes.unicode_minus'] = False  # This is to display minus signs correctly



class HSI50Backtest:
    def __init__(self):
        """初始化回测系统"""
        # 基础参数
        self.ticker = '0011.HK'  # 恒生银行
        self.initial_capital = 50000  # 初始资金 (提高初始资金以匹配股票价格)
        self.monthly_addition = 10000  # 每月追加资金
        
        # 止盈止损参数
        self.take_profit_long = 0.016  # 多头止盈 1.6%
        self.stop_loss_long = 0.008    # 多头止损 0.8%
        self.take_profit_short = 0.008  # 空头止盈 0.8%
        self.stop_loss_short = 0.016   # 空头止损 1.6%
        
        # 策略参数
        self.position = 0  # 当前持仓，1为多头，-1为空头，0为空仓
        self.current_price = 0  # 当前持仓价格
        
        # 市场状态参数 (0:震荡, 1:上升趋势, -1:下降趋势)
        self.market_state = 0
        
        # 动态XY阈值
        self.x_threshold_long = 0.45  # 多头X阈值
        self.y_threshold_long = 0.45  # 多头Y阈值
        self.x_threshold_short = 0.25  # 空头X阈值
        self.y_threshold_short = 0.25  # 空头Y阈值
        
        # 交易统计
        self.win_count = 0
        self.loss_count = 0
        self.total_trades = 0
        self.win_rate = 0.0
        
    def fetch_yf_data(self, ticker='0011.HK', start_date=None, end_date=None):
        """使用yfinance获取数据"""
        if end_date is None:
            end_date = datetime.now().strftime('%Y-%m-%d')
        if start_date is None:
            # 获取20年历史数据
            start_date = (datetime.now() - timedelta(days=365*20)).strftime('%Y-%m-%d')
            
        print(f"\n1. 从Yahoo Finance获取数据 ({start_date} 至 {end_date})...")
        try:
            # 获取数据
            data = yf.download(ticker, start=start_date, end=end_date)
            
            if data.empty:
                raise ValueError("未获取到数据，请检查股票代码和日期范围")
                
            # 重命名列以匹配现有代码
            data = data.rename(columns={
                'Open': 'open',
                'High': 'high',
                'Low': 'low',
                'Close': 'close',
                'Volume': 'volume'
            })
            
            # 重置索引并将日期转换为列
            data = data.reset_index()
            data = data.rename(columns={'Date': 'date'})
            
            # 计算成交量比率 (x_value)
            data['x_value'] = data['volume'] / data['volume'].rolling(20).mean()
            data['x_value'] = data['x_value'].fillna(0.5).clip(0.1, 2.0)  # 限制范围
            
            # 计算资金流比率 (y_value)
            # 简化计算：使用价格变化与成交量的关系
            price_change = data['close'].pct_change()
            # 使用简单的成交量比率作为y_value的代理
            volume_ma = data['volume'].rolling(20).mean()
            volume_ma = volume_ma.replace(0, 1)  # 避免除零
            data['y_value'] = data['volume'] / volume_ma
            data['y_value'] = data['y_value'].fillna(0.5).clip(0.1, 2.0)  # 限制范围
            
            # 能量指标 (e_value) - 使用RSI的变体
            delta = data['close'].diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
            rs = gain / loss
            data['e_value'] = 1 - (1 / (1 + rs))
            data['e_value'] = data['e_value'].fillna(0.5).clip(0.1, 0.9)  # 限制范围
            
            # 确保日期格式正确
            data['date'] = pd.to_datetime(data['date'])
            
            print(f"✓ 成功获取 {len(data)} 条数据")
            print(f"数据范围：{data['date'].min().date()} 至 {data['date'].max().date()}")
            
            return data
            
        except Exception as e:
            print(f"❌ 从Yahoo Finance获取数据失败: {str(e)}")
            raise
    
    def load_data(self, use_yf=True):
        """加载数据"""
        if use_yf:
            # 使用yfinance获取最新数据
            self.df = self.fetch_yf_data()
        else:
            # 使用原有数据库方式
            print("\n1. 从数据库加载数据...")
            try:
                conn = sqlite3.connect('hsi_25years.db')
                three_years_ago = (datetime.now() - timedelta(days=20*365)).strftime('%Y-%m-%d')
                self.df = pd.read_sql(f"""
                    SELECT date, open, high, low, close, volume, 
                           volume_ratio as x_value, 
                           inflow_ratio as y_value, 
                           0.5 as e_value 
                    FROM hsi_data 
                    WHERE date >= '{three_years_ago}'
                    ORDER BY date
                """, conn)
                conn.close()
                
                # 转换日期
                self.df['date'] = pd.to_datetime(self.df['date'])
                print(f"✓ 从数据库加载了 {len(self.df)} 条数据")
                print(f"数据范围：{self.df['date'].min().date()} 至 {self.df['date'].max().date()}")
                
            except Exception as e:
                print(f"❌ 数据库加载失败: {str(e)}")
                print("尝试从Yahoo Finance获取数据...")
                self.df = self.fetch_yf_data()
        
    def calculate_regression_line(self, window=60):
        """计算回归线
        
        使用numpy的polynomial.polyfit进行滚动窗口回归计算
        """
        print("\n2. 计算回归线...")
        try:
            # 确保数据按日期排序
            self.df = self.df.sort_values('date').reset_index(drop=True)
            
            # 初始化新列
            close_prices = self.df['close'].values
            n = len(close_prices)
            
            # 初始化结果数组
            regression_line = np.full(n, np.nan)
            price_position = np.zeros(n)
            upper_band = np.full(n, np.nan)
            lower_band = np.full(n, np.nan)
            
            # 使用numpy的polyfit进行滚动回归
            for i in range(window, n):
                # 获取窗口数据
                window_prices = close_prices[i-window:i+1]
                x = np.arange(len(window_prices))
                
                try:
                    # 确保输入是1D数组且没有NaN值
                    x_clean = x.astype(float)
                    y_clean = window_prices.astype(float)
                    
                    # 检查是否有NaN值
                    mask = ~(np.isnan(x_clean) | np.isnan(y_clean))
                    if np.sum(mask) < 2:  # 至少需要2个点
                        continue
                        
                    x_clean = x_clean[mask]
                    y_clean = y_clean[mask]
                    
                    # 使用numpy的polyfit进行线性拟合
                    coef = np.polyfit(x_clean, y_clean, 1)
                    poly = np.poly1d(coef)
                    
                    # 计算当前点的回归值（窗口内最后一个点）
                    current_x = x_clean[-1] if len(x_clean) > 0 else 0
                    current_regression = poly(current_x)
                    regression_line[i] = current_regression
                except Exception as e:
                    # 如果拟合失败，使用前一个有效值或默认值
                    regression_line[i] = regression_line[i-1] if i > 0 else np.nan
                
                # 计算价格相对回归线的位置
                current_price = close_prices[i]
                if current_regression != 0:
                    price_position[i] = (current_price - current_regression) / current_regression
                
                # 计算残差和标准差
                if len(window_prices) > 1:  # 需要至少2个点计算标准差
                    residuals = window_prices - poly(x)
                    std_dev = np.std(residuals, ddof=1)  # 使用样本标准差
                    upper_band[i] = current_regression + 2 * std_dev
                    lower_band[i] = current_regression - 2 * std_dev
            
            # 将结果存入DataFrame
            self.df['regression_line'] = regression_line
            self.df['price_position'] = price_position
            self.df['upper_band'] = upper_band
            self.df['lower_band'] = lower_band
            
            # 向前填充NaN值
            self.df['regression_line'] = self.df['regression_line'].fillna(method='bfill')
            self.df['upper_band'] = self.df['upper_band'].fillna(method='bfill')
            self.df['lower_band'] = self.df['lower_band'].fillna(method='bfill')
            
            # 计算R²
            valid_regression = self.df['regression_line'].notna()
            if valid_regression.sum() > 1:
                r_value = np.corrcoef(
                    self.df.loc[valid_regression, 'close'],
                    self.df.loc[valid_regression, 'regression_line']
                )[0, 1]
                r_squared = r_value ** 2
            else:
                r_squared = 0
            
            print(f"✓ 回归线计算完成 (R² = {r_squared:.4f}, 滚动窗口: {window}天)")
            
        except Exception as e:
            print(f"❌ 回归线计算失败: {str(e)}")
            import traceback
            traceback.print_exc()
            raise
    
    def calculate_kelly(self, win_rate=None, profit_ratio=None):
        """
        计算凯利公式建议仓位
        
        参数:
            win_rate: 胜率(0-1)
            profit_ratio: 盈亏比(平均盈利/平均亏损)
            
        返回:
            建议仓位比例(0-1)
        """
        if win_rate is None:
            if self.total_trades == 0:
                return 0.1  # 默认10%仓位
            win_rate = self.win_rate
            
        if profit_ratio is None:
            # 如果没有提供盈亏比，使用固定值2:1
            profit_ratio = 2.0
            
        if win_rate <= 0 or profit_ratio <= 0:
            return 0.1  # 最小10%仓位
            
        q = 1 - win_rate
        kelly = (win_rate * profit_ratio - q) / profit_ratio
        
        # 限制在10%-50%之间，降低风险
        return max(0.1, min(kelly, 0.5))
    
    def add_monthly_capital(self, date, capital):
        """
        每月增加资金
        
        参数:
            date: 当前日期
            capital: 当前资金
            
        返回:
            更新后的资金
        """
        # 获取当前月份的第一天
        current_month = date.replace(day=1)
        
        # 检查是否已经处理过这个月
        if not hasattr(self, 'last_month') or current_month > self.last_month:
            self.last_month = current_month
            # 只在月初增加资金
            if date.day <= 5:  # 允许在月初5天内增加资金，避免错过月初第一个交易日
                print(f"{date.date()}: 增加资金 {self.monthly_addition:.2f}")
                return capital + self.monthly_addition
        
        return capital
    
    def detect_market_trend(self, df, current_idx, window=60):
        """
        检测市场趋势
        
        参数:
            df: 数据DataFrame
            current_idx: 当前索引
            window: 观察窗口大小
            
        返回:
            1: 上升趋势
            -1: 下降趋势
            0: 震荡市
        """
        if current_idx < window:
            return 0
            
        # 获取窗口数据
        window_data = df.iloc[current_idx-window:current_idx]
        
        # 计算均线
        ma20 = window_data['close'].mean()
        ma60 = window_data['close'].iloc[-60:].mean() if len(window_data) >= 60 else ma20
        
        # 计算ATR
        high_low = window_data['high'] - window_data['low']
        high_close = (window_data['high'] - window_data['close'].shift()).abs()
        low_close = (window_data['low'] - window_data['close'].shift()).abs()
        tr = pd.concat([high_low, high_close, low_close], axis=1).max(axis=1)
        atr = tr.mean()
        
        # 趋势判断
        price_change = (window_data['close'].iloc[-1] - window_data['close'].iloc[0]) / window_data['close'].iloc[0]
        
        if price_change > 0.05 and ma20 > ma60 and (ma20 - ma60) > atr * 0.5:
            return 1  # 上升趋势
        elif price_change < -0.05 and ma20 < ma60 and (ma60 - ma20) > atr * 0.5:
            return -1  # 下降趋势
        else:
            return 0  # 震荡市
            
    def adjust_xy_thresholds(self):
        """根据市场状态调整XY阈值"""
        if self.market_state == 1:  # 上升趋势
            self.x_threshold_long = 0.4  # 降低多头入场门槛
            self.y_threshold_long = 0.4
            self.x_threshold_short = 0.3  # 提高空头入场门槛
            self.y_threshold_short = 0.3
        elif self.market_state == -1:  # 下降趋势
            self.x_threshold_long = 0.5  # 提高多头入场门槛
            self.y_threshold_long = 0.5
            self.x_threshold_short = 0.2  # 降低空头入场门槛
            self.y_threshold_short = 0.2
        else:  # 震荡市
            self.x_threshold_long = 0.45
            self.y_threshold_long = 0.45
            self.x_threshold_short = 0.25
            self.y_threshold_short = 0.25

    def generate_daily_signals(self, row, current_idx, capital):
        """
        生成每日交易信号
        
        参数:
            row: 当前行情数据
            current_idx: 当前索引
            capital: 当前资金
            
        返回:
            dict: 包含信号详细信息的字典
        """
        signal = {
            'date': row['date'],
            'close': row['close'],
            'x_value': row['x_value'],
            'y_value': row['y_value'],
            'market_state': ['震荡', '上升', '下降'][self.market_state + 1],
            'signal': '持有',
            'position': self.position,
            'suggested_action': '无',
            'stop_loss': None,
            'take_profit': None,
            'kelly_fraction': 0.0
        }
        
        # 计算凯利仓位
        if self.total_trades > 0:
            win_rate = self.win_count / self.total_trades
            profit_ratio = 2.0  # 默认盈亏比
            signal['kelly_fraction'] = self.calculate_kelly(win_rate, profit_ratio)
        
        # 生成信号
        if self.position == 0:  # 空仓状态
            if (row['x_value'] > self.x_threshold_long and 
                row['y_value'] > self.y_threshold_long and 
                row['price_position'] < 0):
                signal['signal'] = '买入信号'
                signal['suggested_action'] = f'开多仓 (仓位: {signal["kelly_fraction"]*100:.1f}%)'
                signal['stop_loss'] = row['close'] * (1 - self.stop_loss_long)
                signal['take_profit'] = row['close'] * (1 + self.take_profit_long)
                
            elif (row['x_value'] < self.x_threshold_short and 
                  row['y_value'] < self.y_threshold_short and 
                  row['price_position'] > 0):
                signal['signal'] = '卖出信号'
                signal['suggested_action'] = f'开空仓 (仓位: {signal["kelly_fraction"]*100:.1f}%)'
                signal['stop_loss'] = row['close'] * (1 + self.stop_loss_short)
                signal['take_profit'] = row['close'] * (1 - self.take_profit_short)
                
        else:  # 持仓状态
            if self.position == 1:  # 持有多仓
                signal['stop_loss'] = self.current_price * (1 - self.stop_loss_long)
                signal['take_profit'] = self.current_price * (1 + self.take_profit_long)
                signal['suggested_action'] = f'持有 (止盈: {signal["take_profit"]:.2f}, 止损: {signal["stop_loss"]:.2f})'
            else:  # 持有空仓
                signal['stop_loss'] = self.current_price * (1 + self.stop_loss_short)
                signal['take_profit'] = self.current_price * (1 - self.take_profit_short)
                signal['suggested_action'] = f'持有 (止盈: {signal["take_profit"]:.2f}, 止损: {signal["stop_loss"]:.2f})'
        
        return signal

    def run_backtest(self):
        """运行回测"""
        print("\n3. 开始回测...")
        try:
            # 初始化信号记录
            self.daily_signals = []
            # 准备结果记录
            self.trades = []
            capital = self.initial_capital
            self.equity_curve = []
            
            # 初始化交易统计
            self.win_count = 0
            self.loss_count = 0
            self.total_trades = 0
            self.win_rate = 0.0
            
            # 计算ATR用于仓位管理
            self.df['high_low'] = self.df['high'] - self.df['low']
            self.df['high_close'] = (self.df['high'] - self.df['close'].shift()).abs()
            self.df['low_close'] = (self.df['low'] - self.df['close'].shift()).abs()
            self.df['tr'] = self.df[['high_low', 'high_close', 'low_close']].max(axis=1)
            self.df['atr'] = self.df['tr'].rolling(window=14).mean()
            
            for i in range(60, len(self.df)):  # 从第60天开始，确保有足够的历史数据
                row = self.df.iloc[i]
                date = row['date']
                
                # 每月增加资金
                capital = self.add_monthly_capital(date, capital)
                
                # 生成每日信号
                signal = self.generate_daily_signals(row, i, capital)
                self.daily_signals.append(signal)
                
                # 记录权益
                self.equity_curve.append({
                    'date': date,
                    'equity': capital,
                    'position': self.position
                })
                
                # 如果有持仓，检查止盈止损
                if self.position != 0:
                    if self.position == 1:  # 多头
                        profit_ratio = (row['high'] - self.current_price) / self.current_price
                        loss_ratio = (self.current_price - row['low']) / self.current_price
                        
                        if profit_ratio >= self.take_profit_long:  # 止盈
                            exit_price = self.current_price * (1 + self.take_profit_long)
                            position_size = self.trades[-1]['position_size']  # 获取开仓时的仓位大小
                            profit = (exit_price - self.current_price) / self.current_price * position_size
                            capital += profit
                            self.position = 0
                            self.win_count += 1
                            self.trades.append({
                                'date': date,
                                'type': 'long_exit_tp',
                                'price': exit_price,
                                'profit': profit,
                                'capital': capital,
                                'position_size': position_size,
                                'kelly': self.trades[-1]['kelly'],
                                'market_state': self.market_state
                            })
                        elif loss_ratio >= self.stop_loss_long:  # 止损
                            exit_price = self.current_price * (1 - self.stop_loss_long)
                            position_size = self.trades[-1]['position_size']  # 获取开仓时的仓位大小
                            loss = (exit_price - self.current_price) / self.current_price * position_size
                            capital += loss
                            self.position = 0
                            self.loss_count += 1
                            self.trades.append({
                                'date': date,
                                'type': 'long_exit_sl',
                                'price': exit_price,
                                'profit': loss,
                                'capital': capital,
                                'position_size': position_size,
                                'kelly': self.trades[-1]['kelly'],
                                'market_state': self.market_state
                            })
                    
                    elif self.position == -1:  # 空头
                        profit_ratio = (self.current_price - row['low']) / self.current_price
                        loss_ratio = (row['high'] - self.current_price) / self.current_price
                        
                        if profit_ratio >= self.take_profit_short:  # 止盈
                            exit_price = self.current_price * (1 - self.take_profit_short)
                            position_size = self.trades[-1]['position_size']  # 获取开仓时的仓位大小
                            profit = (self.current_price - exit_price) / self.current_price * position_size
                            capital += profit
                            self.position = 0
                            self.win_count += 1
                            self.trades.append({
                                'date': date,
                                'type': 'short_exit_tp',
                                'price': exit_price,
                                'profit': profit,
                                'capital': capital,
                                'position_size': position_size,
                                'kelly': self.trades[-1]['kelly'],
                                'market_state': self.market_state
                            })
                        elif loss_ratio >= self.stop_loss_short:  # 止损
                            exit_price = self.current_price * (1 + self.stop_loss_short)
                            position_size = self.trades[-1]['position_size']  # 获取开仓时的仓位大小
                            loss = (self.current_price - exit_price) / self.current_price * position_size
                            capital += loss
                            self.position = 0
                            self.loss_count += 1
                            self.trades.append({
                                'date': date,
                                'type': 'short_exit_sl',
                                'price': exit_price,
                                'profit': loss,
                                'capital': capital,
                                'position_size': position_size,
                                'kelly': self.trades[-1]['kelly'],
                                'market_state': self.market_state
                            })
                
                # 检测市场状态
                if i >= 60:  # 确保有足够的数据点
                    self.market_state = self.detect_market_trend(self.df, i)
                    self.adjust_xy_thresholds()
                
                # 更新胜率
                if self.total_trades > 0:
                    self.win_rate = self.win_count / self.total_trades
                
                # 如果空仓，判断是否开仓
                if self.position == 0:
                    # 计算凯利仓位
                    kelly_position = self.calculate_kelly()
                    position_size = capital * kelly_position
                    
                    # 使用动态阈值的Cosmoon策略判断
                    long_condition = (row['e_value'] > 0 and 
                                    row['x_value'] > self.x_threshold_long and 
                                    row['y_value'] > self.y_threshold_long)
                    
                    short_condition = (row['y_value'] < self.y_threshold_short or 
                                     row['x_value'] < self.x_threshold_short)
                    
                    # 多头信号
                    if long_condition and row['price_position'] < 0:  # 价格低于回归线
                        self.position = 1
                        self.current_price = row['close']
                        self.trades.append({
                            'date': date,
                            'type': 'long_entry',
                            'price': self.current_price,
                            'capital': capital,
                            'position_size': position_size,
                            'kelly': kelly_position,
                            'market_state': self.market_state
                        })
                        self.total_trades += 1
                    
                    # 空头信号
                    elif short_condition and row['price_position'] > 0:  # 价格高于回归线
                        self.position = -1
                        self.current_price = row['close']
                        self.trades.append({
                            'date': date,
                            'type': 'short_entry',
                            'price': self.current_price,
                            'capital': capital,
                            'position_size': position_size,
                            'kelly': kelly_position,
                            'market_state': self.market_state
                        })
                        self.total_trades += 1
            
            self.final_capital = capital
            print(f"✓ 回测完成！最终资金：{self.final_capital:,.2f}")
            
        except Exception as e:
            print(f"❌ 回测执行失败: {str(e)}")
            raise
    
    def save_daily_signals(self):
        """保存每日信号到Excel文件"""
        try:
            signals_df = pd.DataFrame(self.daily_signals)
            output_file = 'daily_signals.xlsx'
            signals_df.to_excel(output_file, index=False)
            print(f"✓ 每日信号已保存到 {output_file}")
            return output_file
        except Exception as e:
            print(f"❌ 保存每日信号失败: {str(e)}")
            return None
    
    def analyze_results(self):
        """分析回测结果"""
        print("\n4. 回测分析...")
        
        # 保存每日信号
        output_file = self.save_daily_signals()
        return output_file
        try:
            # 转换交易记录为DataFrame
            trades_df = pd.DataFrame(self.trades)
            if len(trades_df) == 0:
                print("没有产生任何交易")
                return
            
            # 计算基本统计数据
            total_trades = len(trades_df[trades_df['type'].str.contains('entry')])
            winning_trades = len(trades_df[trades_df['profit'] > 0]) if 'profit' in trades_df.columns else 0
            profit_trades = trades_df[trades_df['profit'] > 0] if 'profit' in trades_df.columns else pd.DataFrame()
            loss_trades = trades_df[trades_df['profit'] < 0] if 'profit' in trades_df.columns else pd.DataFrame()
            
            print(f"\n交易统计：")
            print(f"总交易次数：{total_trades}")
            print(f"盈利交易：{winning_trades}")
            print(f"亏损交易：{total_trades - winning_trades}")
            print(f"胜率：{winning_trades/total_trades*100:.2f}%")
            
            if len(profit_trades) > 0:
                print(f"平均盈利：{profit_trades['profit'].mean():,.2f}")
                print(f"最大盈利：{profit_trades['profit'].max():,.2f}")
            
            if len(loss_trades) > 0:
                print(f"平均亏损：{loss_trades['profit'].mean():,.2f}")
                print(f"最大亏损：{loss_trades['profit'].min():,.2f}")
            
            # 计算收益率
            initial_equity = self.initial_capital
            final_equity = self.final_capital
            total_days = (self.df['date'].max() - self.df['date'].min()).days
            total_years = total_days / 365
            
            total_return = (final_equity - initial_equity) / initial_equity
            annual_return = (1 + total_return) ** (1/total_years) - 1
            
            print(f"\n收益统计：")
            print(f"初始资金：{initial_equity:,.2f}")
            print(f"最终资金：{final_equity:,.2f}")
            print(f"总收益率：{total_return*100:.2f}%")
            print(f"年化收益率：{annual_return*100:.2f}%")
            
            # 计算复利考虑每月追加资金
            total_additions = (total_days // 30) * self.monthly_addition
            actual_return = (final_equity - initial_equity - total_additions) / (initial_equity + total_additions)
            print(f"考虑每月追加后的实际收益率：{actual_return*100:.2f}%")
            
            # 创建子图
            fig = plt.figure(figsize=(20, 15))
            
            # 1. 权益曲线（带持仓标记）
            ax1 = plt.subplot(221)
            equity_df = pd.DataFrame(self.equity_curve)
            equity_df['date'] = pd.to_datetime(equity_df['date'])
            
            # 绘制基础权益曲线
            ax1.plot(equity_df['date'], equity_df['equity'], 'b-', label='权益曲线', linewidth=1.5)
            
            # 标记多空持仓
            long_periods = equity_df[equity_df['position'] == 1]
            short_periods = equity_df[equity_df['position'] == -1]
            
            if len(long_periods) > 0:
                ax1.plot(long_periods['date'], long_periods['equity'], 'g.', label='多头', markersize=3)
            if len(short_periods) > 0:
                ax1.plot(short_periods['date'], short_periods['equity'], 'r.', label='空头', markersize=3)
            
            ax1.set_title('权益曲线与持仓分析')
            ax1.set_xlabel('日期')
            ax1.set_ylabel('资金')
            ax1.grid(True)
            ax1.legend()
            
            # 2. 月度收益分布
            ax2 = plt.subplot(222)
            monthly_returns = []
            
            for year in equity_df['date'].dt.year.unique():
                for month in range(1, 13):
                    month_data = equity_df[
                        (equity_df['date'].dt.year == year) & 
                        (equity_df['date'].dt.month == month)
                    ]
                    if len(month_data) > 0:
                        start_equity = month_data['equity'].iloc[0]
                        end_equity = month_data['equity'].iloc[-1]
                        monthly_return = (end_equity - start_equity) / start_equity * 100
                        monthly_returns.append(monthly_return)
            
            ax2.hist(monthly_returns, bins=50, color='blue', alpha=0.7)
            ax2.axvline(x=0, color='r', linestyle='--')
            ax2.set_title('月度收益分布')
            ax2.set_xlabel('收益率 (%)')
            ax2.set_ylabel('频次')
            
            # 3. 累计收益与回撤分析
            ax3 = plt.subplot(223)
            equity_df['return'] = equity_df['equity'].pct_change()
            equity_df['cum_return'] = (1 + equity_df['return']).cumprod()
            equity_df['cum_roll_max'] = equity_df['cum_return'].cummax()
            equity_df['drawdown'] = equity_df['cum_roll_max'] - equity_df['cum_return']
            
            ax3.plot(equity_df['date'], equity_df['cum_return'], 'b-', label='累计收益')
            ax3.plot(equity_df['date'], equity_df['cum_roll_max'], 'g--', label='历史新高')
            ax3.fill_between(equity_df['date'], 
                           equity_df['cum_return'], 
                           equity_df['cum_roll_max'], 
                           alpha=0.3, 
                           color='red', 
                           label='回撤')
            ax3.set_title('累计收益与回撤分析')
            ax3.set_xlabel('日期')
            ax3.set_ylabel('累计收益倍数')
            ax3.grid(True)
            ax3.legend()
            
            # 4. 交易盈亏分布
            ax4 = plt.subplot(224)
            trades_df['profit'].hist(bins=50, ax=ax4, color='blue', alpha=0.7)
            ax4.axvline(x=0, color='r', linestyle='--')
            ax4.set_title('交易盈亏分布')
            ax4.set_xlabel('盈亏金额')
            ax4.set_ylabel('频次')
            
            # 调整布局并保存
            plt.tight_layout()
            plt.savefig('trading_analysis.png', dpi=300, bbox_inches='tight')
            plt.close()
            
            # 保存交易记录
            trades_df.to_excel('trade_records.xlsx', index=False)
            print("\n✓ 分析完成")
            print("• 交易记录已保存到 trade_records.xlsx")
            print("• 详细分析图表已保存到 trading_analysis.png")
            
        except Exception as e:
            print(f"❌ 分析失败: {str(e)}")
            raise

def main():
    """主函数"""
    print("\n" + "="*50)
    print("00023.HK (恒生银行) 20年回测系统")
    print("="*50 + "\n")
    
    try:
        # 创建回测实例
        backtest = HSI50Backtest()
        
        # 加载数据
        backtest.load_data()
        
        # 计算回归线
        backtest.calculate_regression_line()
        
        # 运行回测
        backtest.run_backtest()
        
        # 分析结果
        output_file = backtest.analyze_results()
        
        # 如果成功生成Excel文件，则尝试打开
        if output_file and os.path.exists(output_file):
            try:
                os.startfile(os.path.abspath(output_file))
                print(f"✓ 已自动打开: {os.path.abspath(output_file)}")
            except Exception as e:
                print(f"❌ 无法自动打开Excel文件，请手动打开: {os.path.abspath(output_file)}")
        
    except Exception as e:
        print(f"\n❌ 程序运行失败: {str(e)}")
    else:
        print("\n✅ 程序运行完成")

if __name__ == "__main__":
    main()
