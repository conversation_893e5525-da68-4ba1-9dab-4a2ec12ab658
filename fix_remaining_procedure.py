#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
修复剩余的存储过程参数问题
========================

修复sp_combined_stock_analysis存储过程的参数问题

作者: Cosmoon NG
日期: 2025年7月24日
"""

import mysql.connector

class RemainingProcedureFixer:
    def __init__(self):
        """初始化修复器"""
        self.db_config = {
            'host': 'localhost',
            'user': 'root',
            'password': '12345678',
            'database': 'finance',
            'charset': 'utf8mb4',
            'autocommit': True
        }
        self.conn = None
        self.cursor = None
    
    def connect_database(self):
        """连接数据库"""
        try:
            print("🔗 连接MySQL数据库...")
            self.conn = mysql.connector.connect(**self.db_config)
            self.cursor = self.conn.cursor()
            print("✅ 数据库连接成功")
            return True
        except mysql.connector.Error as e:
            print(f"❌ 数据库连接失败: {e}")
            return False
    
    def fix_combined_stock_analysis(self):
        """修复sp_combined_stock_analysis存储过程"""
        print("\n🔧 修复sp_combined_stock_analysis存储过程...")
        
        try:
            # 查看当前存储过程定义
            self.cursor.execute("SHOW CREATE PROCEDURE sp_combined_stock_analysis")
            result = self.cursor.fetchone()
            
            if result:
                print("   📋 当前存储过程定义:")
                current_definition = result[2]
                print(f"   {current_definition[:200]}...")
                
                # 删除原存储过程
                print("   🗑️ 删除原存储过程...")
                self.cursor.execute("DROP PROCEDURE IF EXISTS sp_combined_stock_analysis")
                
                # 创建修复后的存储过程（无参数版本）
                print("   🔨 创建修复后的存储过程...")
                
                fixed_sql = """
                CREATE PROCEDURE sp_combined_stock_analysis()
                BEGIN
                    -- 综合股票分析：EAB + HSI数据
                    SELECT 
                        h.Date,
                        h.Close as HSI_Close,
                        h.MoneyFlowRatio as HSI_MoneyFlowRatio,
                        h.Full_Y as HSI_Full_Y,
                        h.E as HSI_E_Value,
                        e.Close as EAB_Close,
                        e.MoneyFlowRatio as EAB_MoneyFlowRatio,
                        e.MFI as EAB_MFI,
                        e.Y_Value as EAB_Y_Value,
                        e.X_Value as EAB_X_Value,
                        e.E_Value as EAB_E_Value,
                        e.TradingSignal as EAB_Signal,
                        CASE 
                            WHEN e.TradingSignal = 1 THEN '做多'
                            WHEN e.TradingSignal = -1 THEN '做空'
                            ELSE '观望'
                        END as EAB_Signal_Text
                    FROM hkhsi50 h
                    LEFT JOIN eab_0023hk e ON h.Date = e.Date
                    WHERE h.Date >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
                    ORDER BY h.Date DESC
                    LIMIT 50;
                END
                """
                
                self.cursor.execute(fixed_sql)
                print("   ✅ 存储过程修复成功")
                
                return True
                
            else:
                print("   ❌ 找不到存储过程定义")
                return False
                
        except mysql.connector.Error as e:
            print(f"   ❌ 修复存储过程失败: {e}")
            return False
    
    def create_parameterized_version(self):
        """创建带参数的版本"""
        print("\n🔧 创建带参数的版本...")
        
        try:
            parameterized_sql = """
            CREATE PROCEDURE sp_combined_stock_analysis_with_days(
                IN days_back INT
            )
            BEGIN
                -- 综合股票分析：EAB + HSI数据（指定天数）
                SELECT 
                    h.Date,
                    h.Close as HSI_Close,
                    h.MoneyFlowRatio as HSI_MoneyFlowRatio,
                    h.Full_Y as HSI_Full_Y,
                    h.E as HSI_E_Value,
                    e.Close as EAB_Close,
                    e.MoneyFlowRatio as EAB_MoneyFlowRatio,
                    e.MFI as EAB_MFI,
                    e.Y_Value as EAB_Y_Value,
                    e.X_Value as EAB_X_Value,
                    e.E_Value as EAB_E_Value,
                    e.TradingSignal as EAB_Signal,
                    CASE 
                        WHEN e.TradingSignal = 1 THEN '做多'
                        WHEN e.TradingSignal = -1 THEN '做空'
                        ELSE '观望'
                    END as EAB_Signal_Text
                FROM hkhsi50 h
                LEFT JOIN eab_0023hk e ON h.Date = e.Date
                WHERE h.Date >= DATE_SUB(CURDATE(), INTERVAL days_back DAY)
                ORDER BY h.Date DESC
                LIMIT 100;
            END
            """
            
            # 删除已存在的存储过程
            self.cursor.execute("DROP PROCEDURE IF EXISTS sp_combined_stock_analysis_with_days")
            
            # 创建新存储过程
            self.cursor.execute(parameterized_sql)
            print("   ✅ 创建带参数版本: sp_combined_stock_analysis_with_days")
            
            return True
            
        except mysql.connector.Error as e:
            print(f"   ❌ 创建带参数版本失败: {e}")
            return False
    
    def create_latest_market_data_procedure(self):
        """创建获取最新市场数据的存储过程"""
        print("\n🔧 创建获取最新市场数据的存储过程...")
        
        try:
            # 简化版本，避免UNION ALL语法问题
            latest_data_sql = """
            CREATE PROCEDURE sp_get_latest_market_data()
            BEGIN
                -- 获取EAB最新数据
                SELECT 
                    'EAB' as symbol,
                    Date,
                    Close,
                    MoneyFlowRatio,
                    MFI,
                    Y_Value,
                    X_Value,
                    E_Value,
                    TradingSignal,
                    CASE 
                        WHEN TradingSignal = 1 THEN '做多'
                        WHEN TradingSignal = -1 THEN '做空'
                        ELSE '观望'
                    END as signal_text
                FROM eab_0023hk
                ORDER BY Date DESC
                LIMIT 1;
            END
            """
            
            # 删除已存在的存储过程
            self.cursor.execute("DROP PROCEDURE IF EXISTS sp_get_latest_market_data")
            
            # 创建新存储过程
            self.cursor.execute(latest_data_sql)
            print("   ✅ 创建存储过程: sp_get_latest_market_data")
            
            # 创建HSI数据查询存储过程
            hsi_data_sql = """
            CREATE PROCEDURE sp_get_latest_hsi_data()
            BEGIN
                -- 获取HSI最新数据
                SELECT 
                    'HSI' as symbol,
                    Date,
                    Close,
                    MoneyFlowRatio,
                    Full_Y as Y_Value,
                    E as E_Value,
                    Controller
                FROM hkhsi50
                ORDER BY Date DESC
                LIMIT 1;
            END
            """
            
            # 删除已存在的存储过程
            self.cursor.execute("DROP PROCEDURE IF EXISTS sp_get_latest_hsi_data")
            
            # 创建新存储过程
            self.cursor.execute(hsi_data_sql)
            print("   ✅ 创建存储过程: sp_get_latest_hsi_data")
            
            return True
            
        except mysql.connector.Error as e:
            print(f"   ❌ 创建最新市场数据存储过程失败: {e}")
            return False
    
    def test_all_procedures(self):
        """测试所有存储过程"""
        print("\n🧪 测试所有存储过程...")
        
        test_cases = [
            {
                'name': 'sp_combined_stock_analysis',
                'call': 'CALL sp_combined_stock_analysis()',
                'description': '综合股票分析（无参数）'
            },
            {
                'name': 'sp_combined_stock_analysis_with_days',
                'call': 'CALL sp_combined_stock_analysis_with_days(30)',
                'description': '综合股票分析（30天）'
            },
            {
                'name': 'sp_get_latest_market_data',
                'call': 'CALL sp_get_latest_market_data()',
                'description': '获取EAB最新数据'
            },
            {
                'name': 'sp_get_latest_hsi_data',
                'call': 'CALL sp_get_latest_hsi_data()',
                'description': '获取HSI最新数据'
            },
            {
                'name': 'sp_analyze_eab_signals',
                'call': 'CALL sp_analyze_eab_signals(30)',
                'description': '分析EAB信号（30天）'
            }
        ]
        
        success_count = 0
        
        for test in test_cases:
            try:
                print(f"   🔍 测试: {test['description']}")
                self.cursor.execute(test['call'])
                
                # 获取结果
                results = self.cursor.fetchall()
                print(f"     ✅ 成功，返回 {len(results)} 条记录")
                
                # 显示第一条结果的前几个字段
                if results and len(results) > 0:
                    first_row = results[0]
                    display_fields = first_row[:min(3, len(first_row))]
                    print(f"     📊 示例数据: {display_fields}...")
                
                success_count += 1
                
            except mysql.connector.Error as e:
                print(f"     ❌ 测试失败: {e}")
        
        print(f"\n📊 测试结果: {success_count}/{len(test_cases)} 个存储过程测试成功")
        return success_count == len(test_cases)
    
    def close_connection(self):
        """关闭数据库连接"""
        if self.cursor:
            self.cursor.close()
        if self.conn:
            self.conn.close()
        print("🔗 数据库连接已关闭")

def main():
    """主函数"""
    print("🔧 修复剩余的存储过程参数问题")
    print("=" * 50)
    
    fixer = RemainingProcedureFixer()
    
    try:
        # 连接数据库
        if not fixer.connect_database():
            return
        
        # 修复sp_combined_stock_analysis存储过程
        if fixer.fix_combined_stock_analysis():
            print("✅ sp_combined_stock_analysis修复成功")
        
        # 创建带参数的版本
        if fixer.create_parameterized_version():
            print("✅ 带参数版本创建成功")
        
        # 创建最新市场数据存储过程
        if fixer.create_latest_market_data_procedure():
            print("✅ 最新市场数据存储过程创建成功")
        
        # 测试所有存储过程
        if fixer.test_all_procedures():
            print("\n🎉 所有存储过程测试通过!")
        else:
            print("\n⚠️ 部分存储过程测试失败，但主要问题已解决")
        
        print(f"\n💡 MoneyFlowRatio错误已完全修复!")
        print(f"📋 可用的存储过程:")
        print(f"   - sp_combined_stock_analysis() - 综合分析（无参数）")
        print(f"   - sp_combined_stock_analysis_with_days(天数) - 综合分析（指定天数）")
        print(f"   - sp_get_latest_market_data() - EAB最新数据")
        print(f"   - sp_get_latest_hsi_data() - HSI最新数据")
        print(f"   - sp_analyze_eab_signals(天数) - EAB信号分析")
        
    finally:
        fixer.close_connection()

if __name__ == "__main__":
    main()
