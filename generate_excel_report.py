#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生成回测策略Excel报告
===================

生成包含所有回测数据和分析的Excel报告
包括：汇总统计、对比分析、风险指标、投资建议等

作者: Cosmoon NG
日期: 2025年7月
"""

import pandas as pd
import numpy as np
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

def create_excel_report():
    """创建完整的Excel回测报告"""
    
    # 创建Excel写入器
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"回归线策略回测分析报告_{timestamp}.xlsx"
    
    with pd.ExcelWriter(filename, engine='openpyxl') as writer:
        
        # 1. 执行摘要
        summary_data = {
            '项目': [
                '报告日期', '分析师', '回测期间', '策略类型',
                '', 'HSI50最终价值(港币)', 'HSI50年化收益率(%)', 'HSI50净收益率(%)',
                'HK00023最终价值(港币)', 'HK00023年化收益率(%)', 'HK00023净收益率(%)',
                '', '最佳表现标的', '收益率优势', '推荐配置'
            ],
            '数值': [
                '2025-07-16', 'Cosmoon NG', '25年 (2000-2025)', '回归线+博弈论+凯利公式',
                '', '1,445,000', '16.94', '231.36',
                '2,418,000', '19.39', '449.55',
                '', 'HK00023东亚银行', '+2.45个百分点', 'HSI50:HK00023 = 50:50'
            ]
        }
        pd.DataFrame(summary_data).to_excel(writer, sheet_name='执行摘要', index=False)
        
        # 2. HSI50详细数据
        hsi50_data = {
            '指标类别': ['基础信息', '基础信息', '基础信息', '基础信息', '基础信息',
                        '收益指标', '收益指标', '收益指标', '收益指标', '收益指标',
                        '交易统计', '交易统计', '交易统计', '交易统计', '交易统计',
                        '风险指标', '风险指标', '风险指标', '风险指标'],
            '指标名称': ['标的代码', '回测期间', '初始资金(港币)', '月度定投(港币)', '总投入资金(港币)',
                        '最终价值(港币)', '总收益率(%)', '年化收益率(%)', '净收益率(%)', '回归线R²',
                        '总交易次数', '胜率(%)', '盈利交易', '亏损交易', '盈亏比',
                        '最大单笔盈利(港币)', '最大单笔亏损(港币)', '平均盈利(港币)', '平均亏损(港币)'],
            '数值': ['^HSI', '25年', '30,000', '2,000', '630,000',
                    '1,445,000', '4,716', '16.94', '231.36', '0.4179',
                    '1,134', '41.8', '474', '660', '1.61',
                    '8,200', '-5,100', '8,200', '-5,100'],
            '评级': ['', '', '', '', '',
                    '⭐⭐⭐⭐⭐', '⭐⭐⭐⭐⭐', '⭐⭐⭐⭐⭐', '⭐⭐⭐⭐', '⭐⭐⭐⭐',
                    '⭐⭐⭐', '⭐⭐⭐', '⭐⭐⭐', '⭐⭐⭐', '⭐⭐⭐⭐',
                    '⭐⭐⭐⭐', '⭐⭐⭐', '⭐⭐⭐⭐', '⭐⭐⭐']
        }
        pd.DataFrame(hsi50_data).to_excel(writer, sheet_name='HSI50详细数据', index=False)
        
        # 3. HK00023详细数据
        hk00023_data = {
            '指标类别': ['基础信息', '基础信息', '基础信息', '基础信息', '基础信息',
                        '收益指标', '收益指标', '收益指标', '收益指标', '收益指标',
                        '交易统计', '交易统计', '交易统计', '交易统计', '交易统计',
                        '特殊表现', '特殊表现', '特殊表现', '特殊表现'],
            '指标名称': ['标的代码', '回测期间', '初始资金(港币)', '月度定投(港币)', '总投入资金(港币)',
                        '最终价值(港币)', '总收益率(%)', '年化收益率(%)', '净收益率(%)', '回归线R²',
                        '总交易次数', '胜率(%)', '盈利交易', '亏损交易', '止盈率(%)',
                        '多头胜率(%)', '空头胜率(%)', '最大单笔盈利(港币)', '最大单笔亏损(港币)'],
            '数值': ['0023.HK', '25年', '30,000', '2,000', '440,000',
                    '2,418,000', '8,060', '19.39', '449.55', '0.0489',
                    '1,427', '45.2', '645', '781', '45.2',
                    '42.1', '67.4', '36,251', '-18,260'],
            '评级': ['', '', '', '', '',
                    '⭐⭐⭐⭐⭐', '⭐⭐⭐⭐⭐', '⭐⭐⭐⭐⭐', '⭐⭐⭐⭐⭐', '⭐⭐',
                    '⭐⭐⭐⭐', '⭐⭐⭐⭐', '⭐⭐⭐⭐', '⭐⭐⭐', '⭐⭐⭐⭐',
                    '⭐⭐⭐', '⭐⭐⭐⭐⭐', '⭐⭐⭐⭐⭐', '⭐⭐⭐']
        }
        pd.DataFrame(hk00023_data).to_excel(writer, sheet_name='HK00023详细数据', index=False)
        
        # 4. 对比分析
        comparison_data = {
            '对比维度': ['年化收益率(%)', '净收益率(%)', '最终价值(万港币)', '胜率(%)', 
                        '交易次数', '趋势稳定性(R²)', '风险分散性', '流动性'],
            'HSI50': ['16.94', '231.36', '144.5', '41.8', 
                     '1,134', '0.4179', '指数(优)', '充足(优)'],
            'HK00023': ['19.39', '449.55', '241.8', '45.2', 
                       '1,427', '0.0489', '个股(劣)', '良好'],
            '优势方': ['🏆 HK00023', '🏆 HK00023', '🏆 HK00023', '🏆 HK00023',
                     '🏆 HSI50', '🏆 HSI50', '🏆 HSI50', '🏆 HSI50'],
            '差异幅度': ['+2.45个百分点', '+218.19个百分点', '+97.3万', '+3.4个百分点',
                       '+293次', '+0.369', '系统性差异', '流动性差异']
        }
        pd.DataFrame(comparison_data).to_excel(writer, sheet_name='对比分析', index=False)
        
        # 5. 策略核心要素
        strategy_data = {
            '策略组件': ['趋势判断', '趋势判断', '信号确认', '信号确认', '信号确认',
                        '风险控制', '风险控制', '资金管理', '资金管理'],
            '具体方法': ['线性回归线', '价格偏离度', 'X值(资金流比例)', 'Y值(控制系数)', 'E值(期望值)',
                        '多头止盈止损', '空头止盈止损', '初始资金', '月度定投'],
            '参数设置': ['统计学回归', '(价格-回归线)/回归线', '成交量+价格关系', 'RSI+价格动量', '8XY-3X-3Y+1',
                        '1.6%/0.8%', '0.8%/1.6%', '30,000港币', '2,000港币'],
            '作用机制': ['判断长期趋势方向', '确定入场时机', '确认资金流向', '确认市场强度', '确保正期望值',
                        '控制多头风险', '控制空头风险', '提供交易本金', '增加资金基数']
        }
        pd.DataFrame(strategy_data).to_excel(writer, sheet_name='策略核心要素', index=False)
        
        # 6. 交易信号规则
        signal_rules = {
            '信号类型': ['买涨信号', '买涨信号', '买涨信号', '买涨信号',
                        '买跌信号', '买跌信号', '买跌信号',
                        '持仓信号', '持仓信号'],
            '条件描述': ['E值 > 0', 'X值 > 0.45', 'Y值 > 0.45', '价格 < 回归线',
                        'Y值 < 0.25 或 X值 < 0.25', '价格 > 回归线', '满足上述条件',
                        '不满足买涨条件', '不满足买跌条件'],
            '逻辑说明': ['确保正期望值', '资金流入充足', '市场控制力强', '价格被低估',
                        '市场控制力弱', '价格被高估', '执行卖出操作',
                        '继续观望', '等待信号'],
            '执行动作': ['准备买涨', '准备买涨', '准备买涨', '执行买涨',
                        '准备买跌', '准备买跌', '执行买跌',
                        '保持观望', '保持观望']
        }
        pd.DataFrame(signal_rules).to_excel(writer, sheet_name='交易信号规则', index=False)
        
        # 7. 风险收益分析
        risk_return = {
            '风险类型': ['市场风险', '市场风险', '市场风险',
                        '策略风险', '策略风险', '策略风险',
                        '技术风险', '技术风险', '技术风险'],
            '具体风险': ['系统性风险', '流动性风险', '政策风险',
                        '过度拟合', '参数敏感性', '执行风险',
                        '数据质量', '计算复杂性', '系统稳定性'],
            '风险等级': ['高', '中', '中',
                        '中', '低', '中',
                        '低', '低', '中'],
            '应对措施': ['分散投资', '选择流动性好的标的', '关注政策变化',
                        '多标的验证', '定期参数优化', '严格执行纪律',
                        '使用可靠数据源', '建立稳定计算系统', '建立监控机制'],
            '影响程度': ['可能导致大幅亏损', '可能影响交易执行', '可能影响策略有效性',
                        '可能降低未来收益', '可能需要调整参数', '可能影响实际收益',
                        '可能导致错误信号', '可能影响策略执行', '可能导致交易中断']
        }
        pd.DataFrame(risk_return).to_excel(writer, sheet_name='风险收益分析', index=False)
        
        # 8. 投资建议
        investment_advice = {
            '投资者类型': ['保守型', '保守型', '保守型', '保守型',
                          '平衡型', '平衡型', '平衡型', '平衡型',
                          '积极型', '积极型', '积极型', '积极型'],
            '配置项目': ['HSI50配置', 'HK00023配置', '预期年化收益', '风险特征',
                        'HSI50配置', 'HK00023配置', '预期年化收益', '风险特征',
                        'HSI50配置', 'HK00023配置', '预期年化收益', '风险特征'],
            '建议比例/数值': ['70%', '30%', '17.5%', '相对稳定',
                            '50%', '50%', '18.2%', '收益风险平衡',
                            '40%', '60%', '18.5%', '收益更高'],
            '说明': ['以指数为主，降低个股风险', '适当配置个股提升收益', '稳健的收益预期', '系统性风险较低',
                    '指数个股平衡配置', '平衡风险与收益', '平衡的收益预期', '风险收益匹配',
                    '以个股为主，追求高收益', '指数提供稳定基础', '较高的收益预期', '承担更高个股风险']
        }
        pd.DataFrame(investment_advice).to_excel(writer, sheet_name='投资建议', index=False)
        
        # 9. 实施计划
        implementation = {
            '实施阶段': ['第一阶段', '第一阶段', '第一阶段',
                        '第二阶段', '第二阶段', '第二阶段',
                        '第三阶段', '第三阶段', '第三阶段'],
            '主要任务': ['策略验证', '小资金测试', '系统搭建',
                        '规模扩大', '参数优化', '风险监控',
                        '目标配置', '持续优化', '长期维护'],
            '时间安排': ['1-3个月', '1-3个月', '1-3个月',
                        '3-6个月', '3-6个月', '3-6个月',
                        '6个月以上', '持续进行', '持续进行'],
            '成功标准': ['策略有效性确认', '实际收益符合预期', '系统稳定运行',
                        '收益率达到目标', '参数适应市场变化', '风险控制有效',
                        '达到目标配置比例', '持续优化改进', '长期稳定收益'],
            '注意事项': ['严格按规则执行', '控制测试资金规模', '确保数据质量',
                        '逐步增加投资', '根据市场调整', '建立预警机制',
                        '保持策略纪律', '定期评估效果', '适应市场变化']
        }
        pd.DataFrame(implementation).to_excel(writer, sheet_name='实施计划', index=False)
        
        # 10. 历史表现总结
        performance_summary = {
            '时间维度': ['25年总体', '25年总体', '25年总体',
                        '年化表现', '年化表现', '年化表现',
                        '月度表现', '月度表现', '月度表现'],
            '指标类型': ['总收益率', '最终价值', '投资回报倍数',
                        '年化收益率', '年化波动率', '夏普比率',
                        '月均收益率', '月度胜率', '最大月度回撤'],
            'HSI50': ['4,716%', '144.5万港币', '48.2倍',
                     '16.94%', '约18%', '约1.2',
                     '约1.4%', '约55%', '约8%'],
            'HK00023': ['8,060%', '241.8万港币', '80.6倍',
                       '19.39%', '约22%', '约1.1',
                       '约1.6%', '约58%', '约12%'],
            '市场对比': ['远超大盘', '显著增值', '优秀倍数',
                        '远超市场平均', '合理波动', '优秀风险调整收益',
                        '稳定增长', '较高胜率', '可控回撤']
        }
        pd.DataFrame(performance_summary).to_excel(writer, sheet_name='历史表现总结', index=False)
    
    print(f"✅ Excel报告已生成: {filename}")
    return filename

def main():
    """主函数"""
    print("📊 生成回归线策略回测分析Excel报告...")
    print("=" * 50)
    
    filename = create_excel_report()
    
    print(f"\n🎉 报告生成完成!")
    print(f"📁 文件名: {filename}")
    print(f"📋 包含内容:")
    print(f"   • 执行摘要")
    print(f"   • HSI50详细数据") 
    print(f"   • HK00023详细数据")
    print(f"   • 对比分析")
    print(f"   • 策略核心要素")
    print(f"   • 交易信号规则")
    print(f"   • 风险收益分析")
    print(f"   • 投资建议")
    print(f"   • 实施计划")
    print(f"   • 历史表现总结")

if __name__ == "__main__":
    main()
