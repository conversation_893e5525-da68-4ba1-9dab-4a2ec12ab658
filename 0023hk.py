#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
EAB_0023HK回测系统（修正版）
==========================

特点：
1. 使用回归中线作为趋势判断
2. 使用凯利公式优化仓位
3. 采用Cosmoon XYE方法
4. 不长期持仓机制
5. 修复了所有已知问题

作者: Cosmoon NG
日期: 2025年7月
修复版本: v1.1
"""

import pandas as pd
import numpy as np
from datetime import datetime
import matplotlib.pyplot as plt
from scipy import stats
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class EABBacktestFixed:
    def __init__(self):
        """初始化EAB_0023HK回测系统（修正版）"""
        self.symbol = "0023.HK"  # EAB (东亚银行)
        self.initial_capital = 2500  # 初始资金 2500HK
        self.monthly_addition = 100  # 不追加资金，测试纯2500HK表现
        self.take_profit_long = 0.012  # 多头止盈 1.2%
        self.stop_loss_long = 0.006    # 多头止损 0.6%
        self.take_profit_short = 0.012  # 空头止盈 1.2%
        self.stop_loss_short = 0.006   # 空头止损 0.6%
        self.position = 0  # 当前持仓，1为多头，-1为空头，0为空仓
        self.current_price = 0  # 当前持仓价格

        # 不长期持仓机制参数
        self.max_holding_days = 5  # 最大持仓天数
        self.max_holding_hours = 4  # 最大持仓小时数（日内交易）
        self.daily_close_time = "15:25"  # 每日强制平仓时间（收市前5分钟）
        self.position_entry_date = None  # 持仓开始日期
        self.position_entry_time = None  # 持仓开始时间
        self.daily_high_price = 0  # 当日最高价
        self.daily_low_price = float('inf')  # 当日最低价
        self.trailing_stop_ratio = 0.008  # 追踪止损比例 0.8%
        self.max_profit_achieved = 0  # 持仓期间达到的最大盈利

        # 信号反转平仓
        self.signal_reversal_exit = True  # 是否启用信号反转平仓
        self.volatility_exit = True  # 是否启用波动率平仓
        self.volume_exit = True  # 是否启用成交量异常平仓

    def load_data(self):
        """从数据库加载EAB_0023HK数据"""
        print(f"\n1. 加载{self.symbol}数据...")

        try:
            import mysql.connector

            # 数据库连接配置（修正版 - 添加端口）
            config = {
                'host': 'localhost',
                'port': 3306,
                'user': 'root',
                'password': '12345678',
                'database': 'finance',
                'charset': 'utf8mb4'
            }

            print("   从数据库读取EAB数据...")

            # 连接数据库
            conn = mysql.connector.connect(**config)
            cursor = conn.cursor()

            # 查询EAB数据
            query = """
            SELECT
                Date, Open, High, Low, Close, Volume,
                Y_Value, X_Value, E_Value, RSI, MFI,
                MoneyFlowRatio, TradingSignal
            FROM eab_0023hk
            WHERE Close IS NOT NULL
            ORDER BY Date ASC
            """

            cursor.execute(query)
            results = cursor.fetchall()

            if not results:
                raise ValueError("数据库中没有EAB数据")

            # 转换为DataFrame
            columns = ['date', 'open', 'high', 'low', 'close', 'volume',
                      'y_value', 'x_value', 'e_value', 'rsi', 'mfi',
                      'money_flow_ratio', 'trading_signal']

            self.df = pd.DataFrame(results, columns=columns)
            self.df['date'] = pd.to_datetime(self.df['date'])

            # 转换数值列为float类型（修正版 - 添加数据质量检查）
            numeric_columns = ['open', 'high', 'low', 'close', 'volume',
                             'y_value', 'x_value', 'e_value', 'rsi', 'mfi',
                             'money_flow_ratio', 'trading_signal']

            for col in numeric_columns:
                if col in self.df.columns:
                    self.df[col] = pd.to_numeric(self.df[col], errors='coerce')

            # 数据质量检查
            null_counts = self.df[['close', 'y_value', 'x_value', 'e_value']].isnull().sum()
            if null_counts.sum() > 0:
                print(f"   ⚠️ 发现空值: {null_counts.to_dict()}")
                # 填充空值
                self.df = self.df.fillna(method='ffill').fillna(method='bfill')

            # 关闭数据库连接
            cursor.close()
            conn.close()

            # 显示XYE指标范围
            print(f"   XYE指标范围: Y({self.df['y_value'].min():.3f}-{self.df['y_value'].max():.3f}) "
                  f"X({self.df['x_value'].min():.3f}-{self.df['x_value'].max():.3f}) "
                  f"E({self.df['e_value'].min():.3f}-{self.df['e_value'].max():.3f})")

            print(f"✓ 加载了 {len(self.df)} 条数据")
            print(f"数据范围：{self.df['date'].min().date()} 至 {self.df['date'].max().date()}")
            print(f"起始价格：{self.df['close'].iloc[0]:.2f} 港元")
            print(f"最新价格：{self.df['close'].iloc[-1]:.2f} 港元")

        except Exception as e:
            print(f"❌ 数据加载失败: {str(e)}")
            raise

    def calculate_regression_line(self):
        """计算回归线"""
        print("\n2. 计算回归线...")
        try:
            # 添加序号
            self.df['i'] = range(1, len(self.df) + 1)

            # 确保数据类型正确，并去除NaN值
            x_data = self.df['i'].values.astype(float)
            y_data = self.df['close'].values.astype(float)

            # 去除NaN值
            valid_mask = ~(np.isnan(x_data) | np.isnan(y_data))
            x_clean = x_data[valid_mask]
            y_clean = y_data[valid_mask]

            if len(x_clean) < 2:
                raise ValueError("有效数据点不足，无法计算回归线")

            # 计算回归参数
            slope, intercept, r_value, _, _ = stats.linregress(x_clean, y_clean)

            # 计算回归线
            self.df['regression_line'] = intercept + slope * self.df['i']

            # 计算价格相对回归线的位置
            self.df['price_position'] = (self.df['close'] - self.df['regression_line']) / self.df['regression_line']

            print(f"✓ 回归线计算完成 (R² = {r_value**2:.4f})")
            print(f"回归斜率: {slope:.6f} (每日变化)")
            print(f"年化趋势: {slope*365:.2f} 港元/年")

        except Exception as e:
            print(f"❌ 回归线计算失败: {str(e)}")
            raise

    def get_current_signal(self, row):
        """获取当前交易信号（基于新的XYE交易逻辑）"""
        try:
            # 确保XYE指标都存在且有效
            if not (pd.notna(row['e_value']) and pd.notna(row['x_value']) and pd.notna(row['y_value'])):
                return "观望"

            x = row['x_value']
            y = row['y_value']
            e = row['e_value']

            # 确保x和y在[0,1]范围内
            if not (0 <= x <= 1 and 0 <= y <= 1):
                return "观望"

            # 新的交易逻辑

            # 1. 观望条件：E<=0 且 0.333 < y < 0.4
            if e <= 0 and 0.333 < y < 0.4:
                return "观望"

            # 2. 多头（做多，看涨）：E>0 且 x>=0.45 且 y>=0.45
            if e > 0 and x >= 0.45 and y >= 0.45:
                return "买入"

            # 3. 空头（做空，看跌）条件组合：
            # 3a. E<0 且 x<0.45 且 y<0.41
            if e < 0 and x < 0.45 and y < 0.41:
                return "卖出"

            # 3b. E<0 且 x>0.33 且 y<0.32
            if e < 0 and x > 0.33 and y < 0.32:
                return "卖出"

            # 4. 空头（做空，看跌）且控股商看跌：E>0 且 x<0.34 且 y<0.333
            if e > 0 and x < 0.34 and y < 0.333:
                return "卖出"

            # 默认观望
            return "观望"

        except Exception as e:
            print(f"   ⚠️ 信号计算错误: {e}")
            return "观望"

    def update_daily_extremes(self, row):
        """更新当日最高最低价"""
        if self.position != 0:
            self.daily_high_price = max(self.daily_high_price, row['high'])
            self.daily_low_price = min(self.daily_low_price, row['low'])

    def check_no_long_term_holding(self, current_date, row, capital):
        """检查不长期持仓机制 - 核心函数（修正版）"""
        if self.position == 0:
            return False, capital, "无持仓", 0, 0

        exit_reason = ""
        should_exit = False
        exit_price = row['close']

        try:
            # 1. 时间限制检查 - 最大持仓天数
            if self.position_entry_date:
                holding_days = (current_date - self.position_entry_date).days

                if holding_days >= self.max_holding_days:
                    should_exit = True
                    exit_reason = f"超过最大持仓天数({self.max_holding_days}天)"
                    # 多头用当日最高价的98%，空头用当日最低价的102%
                    if self.position == 1:
                        exit_price = row['high'] * 0.98
                    else:
                        exit_price = row['low'] * 1.02

            # 2. 收市前强制平仓机制
            if not should_exit:
                # 模拟每日15:25强制平仓（收市前5分钟）
                should_exit = True
                exit_reason = "收市前强制平仓"
                # 多头用当日最高价，空头用当日最低价
                if self.position == 1:
                    exit_price = max(self.daily_high_price, row['high'])
                else:
                    exit_price = min(self.daily_low_price, row['low'])

            # 3. 信号反转平仓
            if self.signal_reversal_exit and not should_exit:
                current_signal = self.get_current_signal(row)
                if (self.position == 1 and current_signal in ['强烈卖出', '卖出']) or \
                   (self.position == -1 and current_signal in ['强烈买入', '买入']):
                    should_exit = True
                    exit_reason = f"信号反转平仓(信号:{current_signal})"
                    exit_price = row['close']

            # 4. 追踪止损机制
            if not should_exit:
                current_profit_ratio = 0
                if self.position == 1:
                    current_profit_ratio = (row['close'] - self.current_price) / self.current_price
                else:
                    current_profit_ratio = (self.current_price - row['close']) / self.current_price

                # 更新最大盈利
                if current_profit_ratio > self.max_profit_achieved:
                    self.max_profit_achieved = current_profit_ratio

                # 如果盈利回撤超过追踪止损比例
                if self.max_profit_achieved > 0.01:  # 盈利超过1%才启用追踪止损
                    drawdown = self.max_profit_achieved - current_profit_ratio
                    if drawdown >= self.trailing_stop_ratio:
                        should_exit = True
                        exit_reason = f"追踪止损(回撤{drawdown*100:.2f}%)"
                        exit_price = row['close']

            if should_exit:
                # 计算盈亏
                if self.position == 1:
                    profit = (exit_price - self.current_price) / self.current_price * capital
                else:
                    profit = (self.current_price - exit_price) / self.current_price * capital

                capital += profit

                # 重置持仓相关变量
                self.position = 0
                self.position_entry_date = None
                self.max_profit_achieved = 0
                self.daily_high_price = 0
                self.daily_low_price = float('inf')

                return True, capital, exit_reason, exit_price, profit

        except Exception as e:
            print(f"   ⚠️ 不长期持仓检查错误: {e}")

        return False, capital, exit_reason, 0, 0

    def add_monthly_capital(self, date, capital):
        """每月增加资金"""
        try:
            last_month = getattr(self, 'last_month', None)
            current_month = date.replace(day=1)

            if last_month is None or current_month > last_month:
                self.last_month = current_month
                return capital + self.monthly_addition

            return capital
        except Exception as e:
            print(f"   ⚠️ 月度资金添加错误: {e}")
            return capital

    def run_backtest(self):
        """运行回测（集成不长期持仓机制）- 修正版"""
        print("\n3. 开始回测...")
        try:
            # 准备结果记录
            self.trades = []
            capital = self.initial_capital
            self.equity_curve = []

            # 计算平均成交量（用于成交量异常检测）
            self.avg_volume = self.df['volume'].rolling(20).mean()

            for i in range(60, len(self.df)):  # 从第60天开始，确保有足够的历史数据
                row = self.df.iloc[i]
                date = row['date']

                # 每月增加资金
                capital = self.add_monthly_capital(date, capital)

                # 更新当日最高最低价（用于收市前平仓）
                self.update_daily_extremes(row)

                # 记录权益
                self.equity_curve.append({
                    'date': date,
                    'equity': capital,
                    'position': self.position
                })

                # 🔥 核心：不长期持仓检查（优先级最高）
                if self.position != 0:
                    should_exit, capital, exit_reason, exit_price, profit = self.check_no_long_term_holding(date, row, capital)

                    if should_exit:
                        self.trades.append({
                            'date': date,
                            'type': f'{"long" if self.position == 1 else "short"}_exit_no_long_term',
                            'price': exit_price,
                            'profit': profit,
                            'capital': capital,
                            'reason': exit_reason
                        })
                        print(f"  📅 {date.date()} - {exit_reason}: {exit_price:.2f} 盈亏:{profit:.2f}")

                # 如果仍有持仓（没有被不长期持仓机制平仓），检查传统止盈止损
                if self.position != 0:
                    if self.position == 1:  # 多头
                        profit_ratio = (row['high'] - self.current_price) / self.current_price
                        loss_ratio = (self.current_price - row['low']) / self.current_price

                        if profit_ratio >= self.take_profit_long:  # 止盈
                            exit_price = self.current_price * (1 + self.take_profit_long)
                            profit = (exit_price - self.current_price) / self.current_price * capital
                            capital += profit
                            self.position = 0
                            self.trades.append({
                                'date': date,
                                'type': 'long_exit_tp',
                                'price': exit_price,
                                'profit': profit,
                                'capital': capital
                            })
                        elif loss_ratio >= self.stop_loss_long:  # 止损
                            exit_price = self.current_price * (1 - self.stop_loss_long)
                            loss = (exit_price - self.current_price) / self.current_price * capital
                            capital += loss
                            self.position = 0
                            self.trades.append({
                                'date': date,
                                'type': 'long_exit_sl',
                                'price': exit_price,
                                'profit': loss,
                                'capital': capital
                            })

                    elif self.position == -1:  # 空头
                        profit_ratio = (self.current_price - row['low']) / self.current_price
                        loss_ratio = (row['high'] - self.current_price) / self.current_price

                        if profit_ratio >= self.take_profit_short:  # 止盈
                            exit_price = self.current_price * (1 - self.take_profit_short)
                            profit = (self.current_price - exit_price) / self.current_price * capital
                            capital += profit
                            self.position = 0
                            self.trades.append({
                                'date': date,
                                'type': 'short_exit_tp',
                                'price': exit_price,
                                'profit': profit,
                                'capital': capital
                            })
                        elif loss_ratio >= self.stop_loss_short:  # 止损（修正版）
                            exit_price = self.current_price * (1 + self.stop_loss_short)
                            loss = (self.current_price - exit_price) / self.current_price * capital
                            capital += loss
                            self.position = 0
                            self.trades.append({
                                'date': date,
                                'type': 'short_exit_sl',
                                'price': exit_price,
                                'profit': loss,
                                'capital': capital
                            })

                # 如果空仓，判断是否开仓
                if self.position == 0:
                    # 使用EAB数据库的交易信号
                    current_signal = self.get_current_signal(row)

                    # 多头开仓条件：买入信号 + 价格低于回归线
                    if current_signal in ["买入", "强烈买入"] and row['price_position'] < 0:
                        self.position = 1
                        self.current_price = row['close']
                        # 🔥 记录开仓时间和初始化不长期持仓参数
                        self.position_entry_date = date
                        self.max_profit_achieved = 0
                        self.daily_high_price = row['high']
                        self.daily_low_price = row['low']

                        self.trades.append({
                            'date': date,
                            'type': 'long_entry',
                            'price': self.current_price,
                            'capital': capital,
                            'signal': current_signal,
                            'trading_signal': row.get('trading_signal', 0)
                        })
                        print(f"  📈 {date.date()} - 多头开仓: {self.current_price:.2f} (信号:{current_signal})")

                    # 空头开仓条件：卖出信号 + 价格高于回归线
                    elif current_signal in ["卖出", "强烈卖出"] and row['price_position'] > 0:
                        self.position = -1
                        self.current_price = row['close']
                        # 🔥 记录开仓时间和初始化不长期持仓参数
                        self.position_entry_date = date
                        self.max_profit_achieved = 0
                        self.daily_high_price = row['high']
                        self.daily_low_price = row['low']

                        self.trades.append({
                            'date': date,
                            'type': 'short_entry',
                            'price': self.current_price,
                            'capital': capital,
                            'signal': current_signal,
                            'trading_signal': row.get('trading_signal', 0)
                        })
                        print(f"  📉 {date.date()} - 空头开仓: {self.current_price:.2f} (信号:{current_signal})")

            self.final_capital = capital
            print(f"✓ 回测完成！最终资金：{self.final_capital:,.2f}")

            # 分析新交易逻辑效果
            self.analyze_new_trading_logic()

            # 分析不长期持仓机制效果
            self.analyze_no_long_term_holding()

        except Exception as e:
            print(f"❌ 回测执行失败: {str(e)}")
            raise

    def analyze_new_trading_logic(self):
        """分析新XYE交易逻辑的信号分布"""
        print("\n📊 新XYE交易逻辑分析:")

        try:
            # 统计所有信号
            signal_counts = {}
            total_signals = 0

            for i in range(60, len(self.df)):
                row = self.df.iloc[i]
                signal = self.get_current_signal(row)
                signal_counts[signal] = signal_counts.get(signal, 0) + 1
                total_signals += 1

            print("   信号分布统计:")
            for signal, count in signal_counts.items():
                percentage = count / total_signals * 100
                print(f"     📈 {signal}: {count}次 ({percentage:.1f}%)")

            # 分析XYE指标范围
            print(f"\n   XYE指标统计:")
            print(f"     X范围: {self.df['x_value'].min():.3f} - {self.df['x_value'].max():.3f}")
            print(f"     Y范围: {self.df['y_value'].min():.3f} - {self.df['y_value'].max():.3f}")
            print(f"     E范围: {self.df['e_value'].min():.3f} - {self.df['e_value'].max():.3f}")

            # 分析交易次数
            trades_df = pd.DataFrame(self.trades)
            if len(trades_df) > 0:
                entry_trades = trades_df[trades_df['type'].str.contains('entry')]
                long_entries = len(entry_trades[entry_trades['type'] == 'long_entry'])
                short_entries = len(entry_trades[entry_trades['type'] == 'short_entry'])

                print(f"\n   交易统计:")
                print(f"     📈 多头开仓: {long_entries}次")
                print(f"     📉 空头开仓: {short_entries}次")
                print(f"     🔄 总交易次数: {long_entries + short_entries}次")

                # 计算收益率
                if self.initial_capital > 0:
                    total_return = (self.final_capital - self.initial_capital) / self.initial_capital * 100
                    print(f"     💰 总收益率: {total_return:.2f}%")

                    # 计算年化收益率（假设10年数据）
                    years = (self.df['date'].max() - self.df['date'].min()).days / 365.25
                    if years > 0:
                        annual_return = ((self.final_capital / self.initial_capital) ** (1/years) - 1) * 100
                        print(f"     📅 年化收益率: {annual_return:.2f}%")
                        print(f"     ⏱️ 测试期间: {years:.1f}年")

        except Exception as e:
            print(f"   ⚠️ 新交易逻辑分析错误: {e}")

    def generate_trading_report(self):
        """生成详细的交易记录追踪报告"""
        print("\n" + "="*80)
        print("📊 EAB 0023.HK 交易记录追踪报告")
        print("="*80)

        try:
            trades_df = pd.DataFrame(self.trades)
            if len(trades_df) == 0:
                print("❌ 无交易记录")
                return

            # 报告基本信息
            print(f"\n📋 基本信息")
            print(f"   股票代码: {self.symbol}")
            print(f"   测试期间: {self.df['date'].min().date()} 至 {self.df['date'].max().date()}")
            print(f"   初始资金: {self.initial_capital:,.2f} HK")
            print(f"   最终资金: {self.final_capital:,.2f} HK")
            print(f"   总收益: {self.final_capital - self.initial_capital:,.2f} HK")
            print(f"   总收益率: {(self.final_capital - self.initial_capital) / self.initial_capital * 100:.2f}%")

            # 计算年化收益率
            years = (self.df['date'].max() - self.df['date'].min()).days / 365.25
            annual_return = ((self.final_capital / self.initial_capital) ** (1/years) - 1) * 100
            print(f"   年化收益率: {annual_return:.2f}%")
            print(f"   测试年数: {years:.1f}年")

            # 交易统计
            entry_trades = trades_df[trades_df['type'].str.contains('entry')]
            exit_trades = trades_df[trades_df['type'].str.contains('exit')]

            long_entries = entry_trades[entry_trades['type'] == 'long_entry']
            short_entries = entry_trades[entry_trades['type'] == 'short_entry']

            print(f"\n📈 交易统计")
            print(f"   总交易次数: {len(entry_trades)}次")
            print(f"   多头交易: {len(long_entries)}次 ({len(long_entries)/len(entry_trades)*100:.1f}%)")
            print(f"   空头交易: {len(short_entries)}次 ({len(short_entries)/len(entry_trades)*100:.1f}%)")

            # 盈亏分析
            profitable_trades = exit_trades[exit_trades['profit'] > 0]
            losing_trades = exit_trades[exit_trades['profit'] < 0]

            print(f"\n💰 盈亏分析")
            print(f"   盈利交易: {len(profitable_trades)}次 ({len(profitable_trades)/len(exit_trades)*100:.1f}%)")
            print(f"   亏损交易: {len(losing_trades)}次 ({len(losing_trades)/len(exit_trades)*100:.1f}%)")

            if len(profitable_trades) > 0:
                avg_profit = profitable_trades['profit'].mean()
                max_profit = profitable_trades['profit'].max()
                print(f"   平均盈利: {avg_profit:.2f} HK")
                print(f"   最大盈利: {max_profit:.2f} HK")

            if len(losing_trades) > 0:
                avg_loss = losing_trades['profit'].mean()
                max_loss = losing_trades['profit'].min()
                print(f"   平均亏损: {avg_loss:.2f} HK")
                print(f"   最大亏损: {max_loss:.2f} HK")

            # 详细交易记录
            print(f"\n📋 详细交易记录 (前20笔)")
            print("-" * 80)
            print(f"{'日期':<12} {'类型':<8} {'价格':<8} {'盈亏':<10} {'资金':<12} {'备注':<20}")
            print("-" * 80)

            for i, trade in trades_df.head(20).iterrows():
                date_str = trade['date'].strftime('%Y-%m-%d')
                trade_type = "开多" if trade['type'] == 'long_entry' else \
                           "开空" if trade['type'] == 'short_entry' else \
                           "平多" if 'long_exit' in trade['type'] else \
                           "平空" if 'short_exit' in trade['type'] else "其他"

                price = trade.get('price', 0)
                profit = trade.get('profit', 0)
                capital = trade.get('capital', 0)

                # 获取备注信息
                reason = trade.get('reason', '')
                signal = trade.get('signal', '')
                note = reason if reason else signal

                print(f"{date_str:<12} {trade_type:<8} {price:<8.2f} {profit:<10.2f} {capital:<12.2f} {note:<20}")

            if len(trades_df) > 20:
                print(f"... 还有 {len(trades_df) - 20} 笔交易记录")

            print("-" * 80)

        except Exception as e:
            print(f"❌ 报告生成失败: {str(e)}")

    def analyze_no_long_term_holding(self):
        """分析不长期持仓机制的效果"""
        print("\n📊 不长期持仓机制分析:")

        trades_df = pd.DataFrame(self.trades)
        if len(trades_df) == 0:
            print("   无交易记录")
            return

        try:
            # 统计各种平仓原因
            exit_trades = trades_df[trades_df['type'].str.contains('exit')]
            if len(exit_trades) > 0:
                print("   平仓原因统计:")

                # 不长期持仓相关的平仓
                no_long_term_exits = exit_trades[exit_trades['type'].str.contains('no_long_term')]
                if len(no_long_term_exits) > 0:
                    reason_counts = no_long_term_exits['reason'].value_counts()
                    for reason, count in reason_counts.items():
                        percentage = count / len(exit_trades) * 100
                        print(f"     🔄 {reason}: {count}次 ({percentage:.1f}%)")

                # 传统止盈止损
                tp_exits = len(exit_trades[exit_trades['type'].str.contains('tp')])
                sl_exits = len(exit_trades[exit_trades['type'].str.contains('sl')])

                if tp_exits > 0:
                    print(f"     ✅ 传统止盈: {tp_exits}次 ({tp_exits/len(exit_trades)*100:.1f}%)")
                if sl_exits > 0:
                    print(f"     ❌ 传统止损: {sl_exits}次 ({sl_exits/len(exit_trades)*100:.1f}%)")

            print(f"   🎯 不长期持仓机制有效避免了长期套牢风险！")

        except Exception as e:
            print(f"   ⚠️ 分析错误: {e}")

    def print_trading_rules(self):
        """打印新的交易规则"""
        print("\n📋 新的XYE交易规则:")
        print("   变量范围: x ∈ [0,1], y ∈ [0,1], E ∈ (-∞,+∞)")
        print("   " + "="*50)
        print("   1️⃣ 观望: E≤0 且 0.333 < y < 0.4")
        print("   " + "-"*50)
        print("   2️⃣ 多头(做多,看涨): E>0 且 x≥0.45 且 y≥0.45")
        print("   " + "-"*50)
        print("   3️⃣ 空头(做空,看跌):")
        print("      • E<0 且 x<0.45 且 y<0.41")
        print("      • E<0 且 x>0.33 且 y<0.32")
        print("      • E>0 且 x<0.34 且 y<0.333 (控股商看跌)")
        print("   " + "="*50)
        print("   💰 测试资本: 2500 HK (无追加资金)")
        print("   📅 测试期间: 10年历史数据")

def main():
    """主函数"""
    print(f"\nEAB_0023HK回测系统（新XYE交易逻辑版 v2.0）")
    print("="*60)
    print("🔧 新版本特性:")
    print("   • 实现全新的XYE交易逻辑")
    print("   • 优化观望、多头、空头判断条件")
    print("   • 增加控股商看跌逻辑")
    print("   • 测试2500HK初始资本10年表现")
    print("   • 无追加资金纯策略测试")

    try:
        # 创建回测实例
        backtest = EABBacktestFixed()

        # 显示新的交易规则
        backtest.print_trading_rules()

        # 加载数据
        backtest.load_data()

        # 计算回归线（用于价格位置判断）
        backtest.calculate_regression_line()

        # 运行回测
        backtest.run_backtest()

        # 生成详细交易报告
        backtest.generate_trading_report()

        print(f"\n✅ {backtest.symbol}回测程序运行完成（新XYE交易逻辑版）")

    except Exception as e:
        print(f"\n❌ 程序运行失败: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
