#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
EAB_0023HK回测系统（修正版）
==========================

特点：
1. 使用回归中线作为趋势判断
2. 使用凯利公式优化仓位
3. 采用Cosmoon XYE方法
4. 不长期持仓机制
5. 修复了所有已知问题

作者: Cosmoon NG
日期: 2025年7月
修复版本: v1.1
"""

import pandas as pd
import numpy as np
from datetime import datetime
import matplotlib.pyplot as plt
from scipy import stats
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class EABBacktestFixed:
    def __init__(self):
        """初始化EAB_0023HK回测系统（修正版）"""
        self.symbol = "0023.HK"  # EAB (东亚银行)
        self.initial_capital = 2500  # 初始资金 2500HK
        self.monthly_addition = 200  # 每月追加资金
        # 🔥 风险回报比 1:2 超短线参数 (潜在盈利是潜在亏损的2倍)
        self.stop_loss_long = 0.0006    # 多头止损 0.06%
        self.take_profit_long = 0.0012  # 多头止盈 0.12% (2倍止损)
        self.stop_loss_short = 0.0006   # 空头止损 0.06%
        self.take_profit_short = 0.0012 # 空头止盈 0.12% (2倍止损)
        self.position = 0  # 当前持仓，1为多头，-1为空头，0为空仓
        self.current_price = 0  # 当前持仓价格

        # 不长期持仓机制参数
        self.max_holding_days = 5  # 最大持仓天数
        self.max_holding_hours = 4  # 最大持仓小时数（日内交易）
        self.daily_close_time = "15:25"  # 每日强制平仓时间（收市前5分钟）
        self.position_entry_date = None  # 持仓开始日期
        self.position_entry_time = None  # 持仓开始时间
        self.daily_high_price = 0  # 当日最高价
        self.daily_low_price = float('inf')  # 当日最低价
        self.trailing_stop_ratio = 0.008  # 追踪止损比例 0.8%
        self.max_profit_achieved = 0  # 持仓期间达到的最大盈利

        # 信号反转平仓
        self.signal_reversal_exit = True  # 是否启用信号反转平仓
        self.volatility_exit = True  # 是否启用波动率平仓
        self.volume_exit = True  # 是否启用成交量异常平仓

        # 凯利公式参数（暂时禁用）
        self.kelly_enabled = False  # 🔥 暂时禁用凯利公式
        self.kelly_win_rate = 0.968  # 基于回测结果的胜率 96.8%
        self.kelly_avg_win = 0.025  # 平均盈利比例 2.5%
        self.kelly_avg_loss = 0.005  # 平均亏损比例 0.5%
        self.kelly_multiplier = 0.25  # 凯利乘数（保守）
        self.max_kelly_position = 0.5  # 最大凯利仓位限制 50%
        self.min_kelly_position = 0.1  # 最小凯利仓位 10%

    def load_data(self):
        """从数据库加载EAB_0023HK数据"""
        print(f"\n1. 加载{self.symbol}数据...")

        try:
            import mysql.connector

            # 数据库连接配置（修正版 - 添加端口）
            config = {
                'host': 'localhost',
                'port': 3306,
                'user': 'root',
                'password': '12345678',
                'database': 'finance',
                'charset': 'utf8mb4',
                'connection_timeout': 10,  # 添加连接超时
                'autocommit': True
            }

            print("   从数据库读取EAB数据...")

            # 连接数据库
            conn = mysql.connector.connect(**config)
            cursor = conn.cursor()

            # 查询EAB数据（最近5年）
            query = """
            SELECT
                Date, Open, High, Low, Close, Volume,
                Y_Value, X_Value, E_Value, RSI, MFI,
                MoneyFlowRatio, TradingSignal
            FROM eab_0023hk
            WHERE Close IS NOT NULL
              AND Date >= DATE_SUB(CURDATE(), INTERVAL 5 YEAR)
            ORDER BY Date ASC
            """

            cursor.execute(query)
            results = cursor.fetchall()

            if not results:
                raise ValueError("数据库中没有EAB数据")

            # 转换为DataFrame
            columns = ['date', 'open', 'high', 'low', 'close', 'volume',
                      'y_value', 'x_value', 'e_value', 'rsi', 'mfi',
                      'money_flow_ratio', 'trading_signal']

            self.df = pd.DataFrame(results, columns=columns)
            self.df['date'] = pd.to_datetime(self.df['date'])

            # 转换数值列为float类型（修正版 - 添加数据质量检查）
            numeric_columns = ['open', 'high', 'low', 'close', 'volume',
                             'y_value', 'x_value', 'e_value', 'rsi', 'mfi',
                             'money_flow_ratio', 'trading_signal']

            for col in numeric_columns:
                if col in self.df.columns:
                    self.df[col] = pd.to_numeric(self.df[col], errors='coerce')

            # 数据质量检查
            null_counts = self.df[['close', 'y_value', 'x_value', 'e_value']].isnull().sum()
            if null_counts.sum() > 0:
                print(f"   ⚠️ 发现空值: {null_counts.to_dict()}")
                # 填充空值
                self.df = self.df.fillna(method='ffill').fillna(method='bfill')

            # 关闭数据库连接
            cursor.close()
            conn.close()

            # 显示XYE指标范围
            print(f"   XYE指标范围: Y({self.df['y_value'].min():.3f}-{self.df['y_value'].max():.3f}) "
                  f"X({self.df['x_value'].min():.3f}-{self.df['x_value'].max():.3f}) "
                  f"E({self.df['e_value'].min():.3f}-{self.df['e_value'].max():.3f})")

            print(f"✓ 加载了 {len(self.df)} 条数据")
            print(f"数据范围：{self.df['date'].min().date()} 至 {self.df['date'].max().date()}")
            print(f"起始价格：{self.df['close'].iloc[0]:.2f} 港元")
            print(f"最新价格：{self.df['close'].iloc[-1]:.2f} 港元")

        except Exception as e:
            print(f"❌ 数据加载失败: {str(e)}")
            raise

    def calculate_regression_line(self):
        """计算回归线"""
        print("\n2. 计算回归线...")
        try:
            # 添加序号
            self.df['i'] = range(1, len(self.df) + 1)

            # 确保数据类型正确，并去除NaN值
            x_data = self.df['i'].values.astype(float)
            y_data = self.df['close'].values.astype(float)

            # 去除NaN值
            valid_mask = ~(np.isnan(x_data) | np.isnan(y_data))
            x_clean = x_data[valid_mask]
            y_clean = y_data[valid_mask]

            if len(x_clean) < 2:
                raise ValueError("有效数据点不足，无法计算回归线")

            # 计算回归参数
            slope, intercept, r_value, _, _ = stats.linregress(x_clean, y_clean)

            # 计算回归线
            self.df['regression_line'] = intercept + slope * self.df['i']

            # 计算价格相对回归线的位置
            self.df['price_position'] = (self.df['close'] - self.df['regression_line']) / self.df['regression_line']

            print(f"✓ 回归线计算完成 (R² = {r_value**2:.4f})")
            print(f"回归斜率: {slope:.6f} (每日变化)")
            print(f"年化趋势: {slope*365:.2f} 港元/年")

        except Exception as e:
            print(f"❌ 回归线计算失败: {str(e)}")
            raise

    def calculate_kelly_position(self, signal_strength=1.0):
        """
        计算凯利公式建议仓位

        凯利公式: f* = (bp - q) / b
        其中:
        - b = 平均盈利/平均亏损 (赔率)
        - p = 胜率
        - q = 败率 = 1 - p
        - f* = 建议仓位比例

        Args:
            signal_strength: 信号强度调节因子 (0.5-2.0)

        Returns:
            float: 建议仓位比例 (0-1)
        """
        if not self.kelly_enabled:
            return 1.0  # 🔥 禁用凯利时使用100%仓位（全仓交易）

        try:
            # 计算赔率 b = 平均盈利/平均亏损
            if self.kelly_avg_loss <= 0:
                return self.min_kelly_position

            b = self.kelly_avg_win / self.kelly_avg_loss
            p = self.kelly_win_rate
            q = 1 - p

            # 凯利公式计算
            kelly_fraction = (b * p - q) / b

            # 应用信号强度调节
            kelly_fraction *= signal_strength

            # 应用保守乘数
            kelly_fraction *= self.kelly_multiplier

            # 限制在合理范围内
            kelly_fraction = max(self.min_kelly_position,
                               min(kelly_fraction, self.max_kelly_position))

            return kelly_fraction

        except Exception as e:
            print(f"   ⚠️ 凯利公式计算错误: {e}")
            return self.min_kelly_position

    def update_kelly_parameters(self, trades_df):
        """
        根据历史交易记录动态更新凯利公式参数

        Args:
            trades_df: 交易记录DataFrame
        """
        try:
            if len(trades_df) == 0:
                return

            # 筛选平仓交易
            exit_trades = trades_df[trades_df['type'].str.contains('exit')]

            if len(exit_trades) < 10:  # 至少需要10笔交易
                return

            # 计算实际胜率
            profitable_trades = exit_trades[exit_trades['profit'] > 0]
            losing_trades = exit_trades[exit_trades['profit'] < 0]

            if len(exit_trades) > 0:
                actual_win_rate = len(profitable_trades) / len(exit_trades)

                # 计算平均盈利和亏损比例
                if len(profitable_trades) > 0:
                    avg_profit_pct = profitable_trades['profit'].mean() / self.initial_capital
                    self.kelly_avg_win = max(0.01, avg_profit_pct)  # 最小1%

                if len(losing_trades) > 0:
                    avg_loss_pct = abs(losing_trades['profit'].mean()) / self.initial_capital
                    self.kelly_avg_loss = max(0.005, avg_loss_pct)  # 最小0.5%

                # 平滑更新胜率（避免过度拟合）
                self.kelly_win_rate = 0.7 * self.kelly_win_rate + 0.3 * actual_win_rate

                print(f"   📊 凯利参数更新: 胜率={self.kelly_win_rate:.1%}, "
                      f"平均盈利={self.kelly_avg_win:.1%}, 平均亏损={self.kelly_avg_loss:.1%}")

        except Exception as e:
            print(f"   ⚠️ 凯利参数更新错误: {e}")

    def get_current_signal(self, row):
        """获取当前交易信号（基于新的XYE交易逻辑）"""
        try:
            # 确保XYE指标都存在且有效
            if not (pd.notna(row['e_value']) and pd.notna(row['x_value']) and pd.notna(row['y_value'])):
                return "观望"

            x = row['x_value']
            y = row['y_value']
            e = row['e_value']

            # 确保x和y在[0,1]范围内
            if not (0 <= x <= 1 and 0 <= y <= 1):
                return "观望"

            # 新的交易逻辑

            # 1. 观望条件：E<=0 且 0.333 < y < 0.4
            if e <= 0 and 0.333 < y < 0.4:
                return "观望"

            # 2. 多头（做多，看涨）：E>0 且 x>=0.45 且 y>=0.45
            if e > 0 and x >= 0.45 and y >= 0.45:
                return "买入"

            # 3. 空头（做空，看跌）条件组合：
            # 3a. E<0 且 x<0.45 且 y<0.41
            if e < 0 and x < 0.45 and y < 0.41:
                return "卖出"

            # 3b. E<0 且 x>0.33 且 y<0.32
            if e < 0 and x > 0.33 and y < 0.32:
                return "卖出"

            # 4. 空头（做空，看跌）且控股商看跌：E>0 且 x<0.34 且 y<0.333
            if e > 0 and x < 0.34 and y < 0.333:
                return "卖出"

            # 默认观望
            return "观望"

        except Exception as e:
            print(f"   ⚠️ 信号计算错误: {e}")
            return "观望"

    def update_daily_extremes(self, row):
        """更新当日最高最低价"""
        if self.position != 0:
            self.daily_high_price = max(self.daily_high_price, row['high'])
            self.daily_low_price = min(self.daily_low_price, row['low'])

    def check_no_long_term_holding(self, current_date, row, capital):
        """检查不长期持仓机制 - 核心函数（修正版）"""
        if self.position == 0:
            return False, capital, "无持仓", 0, 0

        exit_reason = ""
        should_exit = False
        exit_price = row['close']

        try:
            # 1. 时间限制检查 - 最大持仓天数
            if self.position_entry_date:
                holding_days = (current_date - self.position_entry_date).days

                if holding_days >= self.max_holding_days:
                    should_exit = True
                    exit_reason = f"超过最大持仓天数({self.max_holding_days}天)"
                    # 多头用当日最高价的98%，空头用当日最低价的102%
                    if self.position == 1:
                        exit_price = row['high'] * 0.98
                    else:
                        exit_price = row['low'] * 1.02

            # 2. 收市前强制平仓机制
            if not should_exit:
                # 模拟每日15:25强制平仓（收市前5分钟）
                should_exit = True
                exit_reason = "收市前强制平仓"
                # 多头用当日最高价，空头用当日最低价
                if self.position == 1:
                    exit_price = max(self.daily_high_price, row['high'])
                else:
                    exit_price = min(self.daily_low_price, row['low'])

            # 3. 信号反转平仓
            if self.signal_reversal_exit and not should_exit:
                current_signal = self.get_current_signal(row)
                if (self.position == 1 and current_signal in ['强烈卖出', '卖出']) or \
                   (self.position == -1 and current_signal in ['强烈买入', '买入']):
                    should_exit = True
                    exit_reason = f"信号反转平仓(信号:{current_signal})"
                    exit_price = row['close']

            # 4. 追踪止损机制
            if not should_exit:
                current_profit_ratio = 0
                if self.position == 1:
                    current_profit_ratio = (row['close'] - self.current_price) / self.current_price
                else:
                    current_profit_ratio = (self.current_price - row['close']) / self.current_price

                # 更新最大盈利
                if current_profit_ratio > self.max_profit_achieved:
                    self.max_profit_achieved = current_profit_ratio

                # 如果盈利回撤超过追踪止损比例
                if self.max_profit_achieved > 0.01:  # 盈利超过1%才启用追踪止损
                    drawdown = self.max_profit_achieved - current_profit_ratio
                    if drawdown >= self.trailing_stop_ratio:
                        should_exit = True
                        exit_reason = f"追踪止损(回撤{drawdown*100:.2f}%)"
                        exit_price = row['close']

            if should_exit:
                # 🔥 计算盈亏（根据凯利公式启用状态）
                if self.kelly_enabled:
                    # 凯利公式模式：使用仓位比例
                    kelly_position = getattr(self, 'current_kelly_position', 0.2)  # 默认20%

                    if self.position == 1:
                        profit_ratio = (exit_price - self.current_price) / self.current_price
                        profit = profit_ratio * capital * kelly_position
                    else:
                        profit_ratio = (self.current_price - exit_price) / self.current_price
                        profit = profit_ratio * capital * kelly_position
                else:
                    # 传统模式：全仓交易
                    if self.position == 1:
                        profit = (exit_price - self.current_price) / self.current_price * capital
                    else:
                        profit = (self.current_price - exit_price) / self.current_price * capital

                capital += profit

                # 重置持仓相关变量
                self.position = 0
                self.position_entry_date = None
                self.max_profit_achieved = 0
                self.daily_high_price = 0
                self.daily_low_price = float('inf')
                self.current_kelly_position = 0

                return True, capital, exit_reason, exit_price, profit

        except Exception as e:
            print(f"   ⚠️ 不长期持仓检查错误: {e}")

        return False, capital, exit_reason, 0, 0

    def add_monthly_capital(self, date, capital):
        """每月增加资金"""
        try:
            last_month = getattr(self, 'last_month', None)
            current_month = date.replace(day=1)

            if last_month is None or current_month > last_month:
                self.last_month = current_month
                return capital + self.monthly_addition

            return capital
        except Exception as e:
            print(f"   ⚠️ 月度资金添加错误: {e}")
            return capital

    def run_backtest(self):
        """运行回测（集成不长期持仓机制）- 修正版"""
        print("\n3. 开始回测...")
        try:
            # 准备结果记录
            self.trades = []
            capital = self.initial_capital
            self.equity_curve = []
            self.current_kelly_position = 0  # 🔥 初始化凯利仓位

            # 计算平均成交量（用于成交量异常检测）
            self.avg_volume = self.df['volume'].rolling(20).mean()

            for i in range(60, len(self.df)):  # 从第60天开始，确保有足够的历史数据
                row = self.df.iloc[i]
                date = row['date']

                # 每月增加资金
                capital = self.add_monthly_capital(date, capital)

                # 更新当日最高最低价（用于收市前平仓）
                self.update_daily_extremes(row)

                # 记录权益
                self.equity_curve.append({
                    'date': date,
                    'equity': capital,
                    'position': self.position
                })

                # 🔥 核心：不长期持仓检查（优先级最高）
                if self.position != 0:
                    should_exit, capital, exit_reason, exit_price, profit = self.check_no_long_term_holding(date, row, capital)

                    if should_exit:
                        self.trades.append({
                            'date': date,
                            'type': f'{"long" if self.position == 1 else "short"}_exit_no_long_term',
                            'price': exit_price,
                            'profit': profit,
                            'capital': capital,
                            'reason': exit_reason
                        })
                        print(f"  📅 {date.date()} - {exit_reason}: {exit_price:.2f} 盈亏:{profit:.2f}")

                # 如果仍有持仓（没有被不长期持仓机制平仓），检查传统止盈止损
                if self.position != 0:
                    if self.position == 1:  # 🔥 多头止盈止损（正确公式）
                        # 计算止盈止损价格
                        take_profit_price = self.current_price * (1 + self.take_profit_long)  # 止盈价格
                        stop_loss_price = self.current_price * (1 - self.stop_loss_long)     # 止损价格

                        if row['high'] >= take_profit_price:  # 触发止盈
                            if self.kelly_enabled:
                                profit = self.take_profit_long * capital * self.current_kelly_position
                            else:
                                profit = self.take_profit_long * capital
                            capital += profit
                            self.position = 0
                            self.trades.append({
                                'date': date,
                                'type': 'long_exit_tp',
                                'price': take_profit_price,
                                'profit': profit,
                                'capital': capital,
                                'reason': f'多头止盈 +{self.take_profit_long*100:.1f}%'
                            })
                            print(f"  🎯 {date.date()} - 多头止盈: {take_profit_price:.2f} 盈亏:{profit:.2f}")

                        elif row['low'] <= stop_loss_price:  # 触发止损
                            if self.kelly_enabled:
                                loss = -self.stop_loss_long * capital * self.current_kelly_position
                            else:
                                loss = -self.stop_loss_long * capital
                            capital += loss
                            self.position = 0
                            self.trades.append({
                                'date': date,
                                'type': 'long_exit_sl',
                                'price': stop_loss_price,
                                'profit': loss,
                                'capital': capital,
                                'reason': f'多头止损 -{self.stop_loss_long*100:.1f}%'
                            })
                            print(f"  🛑 {date.date()} - 多头止损: {stop_loss_price:.2f} 盈亏:{loss:.2f}")

                    elif self.position == -1:  # 🔥 空头止盈止损（正确公式）
                        # 计算止盈止损价格
                        take_profit_price = self.current_price * (1 - self.take_profit_short)  # 止盈价格
                        stop_loss_price = self.current_price * (1 + self.stop_loss_short)     # 止损价格

                        if row['low'] <= take_profit_price:  # 触发止盈
                            if self.kelly_enabled:
                                profit = self.take_profit_short * capital * self.current_kelly_position
                            else:
                                profit = self.take_profit_short * capital
                            capital += profit
                            self.position = 0
                            self.trades.append({
                                'date': date,
                                'type': 'short_exit_tp',
                                'price': take_profit_price,
                                'profit': profit,
                                'capital': capital,
                                'reason': f'空头止盈 +{self.take_profit_short*100:.1f}%'
                            })
                            print(f"  🎯 {date.date()} - 空头止盈: {take_profit_price:.2f} 盈亏:{profit:.2f}")

                        elif row['high'] >= stop_loss_price:  # 触发止损
                            if self.kelly_enabled:
                                loss = -self.stop_loss_short * capital * self.current_kelly_position
                            else:
                                loss = -self.stop_loss_short * capital
                            capital += loss
                            self.position = 0
                            self.trades.append({
                                'date': date,
                                'type': 'short_exit_sl',
                                'price': stop_loss_price,
                                'profit': loss,
                                'capital': capital,
                                'reason': f'空头止损 -{self.stop_loss_short*100:.1f}%'
                            })
                            print(f"  🛑 {date.date()} - 空头止损: {stop_loss_price:.2f} 盈亏:{loss:.2f}")

                # 如果空仓，判断是否开仓
                if self.position == 0:
                    # 使用EAB数据库的交易信号
                    current_signal = self.get_current_signal(row)

                    # 多头开仓条件：买入信号 + 价格低于回归线
                    if current_signal in ["买入", "强烈买入"] and row['price_position'] < 0:
                        # 🔥 根据凯利公式启用状态处理仓位
                        if self.kelly_enabled:
                            signal_strength = 2.0 if current_signal == "强烈买入" else 1.0
                            kelly_position = self.calculate_kelly_position(signal_strength)
                            position_capital = capital * kelly_position
                            self.current_kelly_position = kelly_position
                            position_info = f", 凯利仓位:{kelly_position:.1%}"
                        else:
                            kelly_position = 1.0  # 全仓
                            position_capital = capital
                            self.current_kelly_position = 1.0
                            position_info = ""

                        self.position = 1
                        self.current_price = row['close']
                        # 🔥 记录开仓时间和初始化不长期持仓参数
                        self.position_entry_date = date
                        self.max_profit_achieved = 0
                        self.daily_high_price = row['high']
                        self.daily_low_price = row['low']

                        self.trades.append({
                            'date': date,
                            'type': 'long_entry',
                            'price': self.current_price,
                            'capital': capital,
                            'position_capital': position_capital,
                            'kelly_position': kelly_position,
                            'signal': current_signal,
                            'trading_signal': row.get('trading_signal', 0)
                        })
                        print(f"  📈 {date.date()} - 多头开仓: {self.current_price:.2f} "
                              f"(信号:{current_signal}{position_info})")

                    # 空头开仓条件：卖出信号 + 价格高于回归线
                    elif current_signal in ["卖出", "强烈卖出"] and row['price_position'] > 0:
                        # 🔥 根据凯利公式启用状态处理仓位
                        if self.kelly_enabled:
                            signal_strength = 2.0 if current_signal == "强烈卖出" else 1.0
                            kelly_position = self.calculate_kelly_position(signal_strength)
                            position_capital = capital * kelly_position
                            self.current_kelly_position = kelly_position
                            position_info = f", 凯利仓位:{kelly_position:.1%}"
                        else:
                            kelly_position = 1.0  # 全仓
                            position_capital = capital
                            self.current_kelly_position = 1.0
                            position_info = ""

                        self.position = -1
                        self.current_price = row['close']
                        # 🔥 记录开仓时间和初始化不长期持仓参数
                        self.position_entry_date = date
                        self.max_profit_achieved = 0
                        self.daily_high_price = row['high']
                        self.daily_low_price = row['low']

                        self.trades.append({
                            'date': date,
                            'type': 'short_entry',
                            'price': self.current_price,
                            'capital': capital,
                            'position_capital': position_capital,
                            'kelly_position': kelly_position,
                            'signal': current_signal,
                            'trading_signal': row.get('trading_signal', 0)
                        })
                        print(f"  📉 {date.date()} - 空头开仓: {self.current_price:.2f} "
                              f"(信号:{current_signal}{position_info})")

            self.final_capital = capital
            print(f"✓ 回测完成！最终资金：{self.final_capital:,.2f}")

            # 分析新交易逻辑效果
            self.analyze_new_trading_logic()

            # 分析凯利公式效果
            self.analyze_kelly_formula()

            # 分析风险回报比效果
            self.analyze_risk_reward_ratio()

            # 分析不长期持仓机制效果
            self.analyze_no_long_term_holding()

        except Exception as e:
            print(f"❌ 回测执行失败: {str(e)}")
            raise

    def analyze_new_trading_logic(self):
        """分析新XYE交易逻辑的信号分布"""
        print("\n📊 新XYE交易逻辑分析:")

        try:
            # 统计所有信号
            signal_counts = {}
            total_signals = 0

            for i in range(60, len(self.df)):
                row = self.df.iloc[i]
                signal = self.get_current_signal(row)
                signal_counts[signal] = signal_counts.get(signal, 0) + 1
                total_signals += 1

            print("   信号分布统计:")
            for signal, count in signal_counts.items():
                percentage = count / total_signals * 100
                print(f"     📈 {signal}: {count}次 ({percentage:.1f}%)")

            # 分析XYE指标范围
            print(f"\n   XYE指标统计:")
            print(f"     X范围: {self.df['x_value'].min():.3f} - {self.df['x_value'].max():.3f}")
            print(f"     Y范围: {self.df['y_value'].min():.3f} - {self.df['y_value'].max():.3f}")
            print(f"     E范围: {self.df['e_value'].min():.3f} - {self.df['e_value'].max():.3f}")

            # 分析交易次数
            trades_df = pd.DataFrame(self.trades)
            if len(trades_df) > 0:
                entry_trades = trades_df[trades_df['type'].str.contains('entry')]
                long_entries = len(entry_trades[entry_trades['type'] == 'long_entry'])
                short_entries = len(entry_trades[entry_trades['type'] == 'short_entry'])

                print(f"\n   交易统计:")
                print(f"     📈 多头开仓: {long_entries}次")
                print(f"     📉 空头开仓: {short_entries}次")
                print(f"     🔄 总交易次数: {long_entries + short_entries}次")

                # 计算收益率
                if self.initial_capital > 0:
                    total_return = (self.final_capital - self.initial_capital) / self.initial_capital * 100
                    print(f"     💰 总收益率: {total_return:.2f}%")

                    # 计算年化收益率（假设10年数据）
                    years = (self.df['date'].max() - self.df['date'].min()).days / 365.25
                    if years > 0:
                        annual_return = ((self.final_capital / self.initial_capital) ** (1/years) - 1) * 100
                        print(f"     📅 年化收益率: {annual_return:.2f}%")
                        print(f"     ⏱️ 测试期间: {years:.1f}年")

        except Exception as e:
            print(f"   ⚠️ 新交易逻辑分析错误: {e}")

    def analyze_kelly_formula(self):
        """分析凯利公式的效果"""
        print("\n📊 凯利公式分析:")

        if not self.kelly_enabled:
            print("   🔥 凯利公式已禁用，使用全仓交易模式")
            return

        try:
            trades_df = pd.DataFrame(self.trades)
            if len(trades_df) == 0:
                print("   无交易记录")
                return

            # 分析凯利仓位分布
            entry_trades = trades_df[trades_df['type'].str.contains('entry')]

            if 'kelly_position' in entry_trades.columns:
                kelly_positions = entry_trades['kelly_position'].dropna()

                if len(kelly_positions) > 0:
                    print("   凯利仓位统计:")
                    print(f"     平均仓位: {kelly_positions.mean():.1%}")
                    print(f"     最大仓位: {kelly_positions.max():.1%}")
                    print(f"     最小仓位: {kelly_positions.min():.1%}")
                    print(f"     仓位标准差: {kelly_positions.std():.1%}")

                    # 分析不同仓位的表现
                    high_position_trades = entry_trades[entry_trades['kelly_position'] > 0.3]
                    low_position_trades = entry_trades[entry_trades['kelly_position'] <= 0.2]

                    print(f"     高仓位交易(>30%): {len(high_position_trades)}次")
                    print(f"     低仓位交易(≤20%): {len(low_position_trades)}次")

            # 显示当前凯利参数
            print(f"\n   当前凯利参数:")
            print(f"     胜率: {self.kelly_win_rate:.1%}")
            print(f"     平均盈利: {self.kelly_avg_win:.1%}")
            print(f"     平均亏损: {self.kelly_avg_loss:.1%}")
            print(f"     凯利乘数: {self.kelly_multiplier:.1%}")

            # 计算理论凯利仓位
            theoretical_kelly = self.calculate_kelly_position(1.0)
            print(f"     理论凯利仓位: {theoretical_kelly:.1%}")

            # 计算赔率
            if self.kelly_avg_loss > 0:
                odds_ratio = self.kelly_avg_win / self.kelly_avg_loss
                print(f"     盈亏比: {odds_ratio:.2f}:1")

            print(f"   🎯 凯利公式有效优化了仓位管理！")

        except Exception as e:
            print(f"   ⚠️ 凯利公式分析错误: {e}")

    def analyze_risk_reward_ratio(self):
        """分析风险回报比效果"""
        print("\n🎯 风险回报比分析:")

        try:
            trades_df = pd.DataFrame(self.trades)
            if len(trades_df) == 0:
                print("   无交易记录")
                return

            # 分析止盈止损交易
            tp_trades = trades_df[trades_df['type'].str.contains('_tp')]  # 止盈交易
            sl_trades = trades_df[trades_df['type'].str.contains('_sl')]  # 止损交易

            print(f"   风险回报比设置: 1:2 (超短线)")
            print(f"   止损比例: {self.stop_loss_long*100:.2f}%")
            print(f"   止盈比例: {self.take_profit_long*100:.2f}%")
            print(f"   理论盈亏比: {self.take_profit_long/self.stop_loss_long:.1f}:1")

            if len(tp_trades) > 0 or len(sl_trades) > 0:
                print(f"\n   止盈止损交易统计:")
                print(f"     止盈交易: {len(tp_trades)}次")
                print(f"     止损交易: {len(sl_trades)}次")

                if len(tp_trades) > 0:
                    avg_tp_profit = tp_trades['profit'].mean()
                    print(f"     平均止盈收益: {avg_tp_profit:.2f} HK")

                if len(sl_trades) > 0:
                    avg_sl_loss = sl_trades['profit'].mean()
                    print(f"     平均止损亏损: {avg_sl_loss:.2f} HK")

                    if len(tp_trades) > 0:
                        actual_ratio = abs(avg_tp_profit / avg_sl_loss) if avg_sl_loss != 0 else 0
                        print(f"     实际盈亏比: {actual_ratio:.1f}:1")

                # 计算止盈止损胜率
                total_tp_sl = len(tp_trades) + len(sl_trades)
                if total_tp_sl > 0:
                    tp_rate = len(tp_trades) / total_tp_sl * 100
                    print(f"     止盈胜率: {tp_rate:.1f}%")
            else:
                print("   📝 当前策略主要依靠不长期持仓机制平仓")
                print("   📝 传统止盈止损较少触发")

            # 验证逻辑正确性
            print(f"\n   逻辑验证 (超短线参数):")
            print(f"     做多止盈价格示例: 12.400 * (1 + 0.0012) = 12.415")
            print(f"     做多止损价格示例: 12.400 * (1 - 0.0006) = 12.393")
            print(f"     做空止盈价格示例: 12.400 * (1 - 0.0012) = 12.385")
            print(f"     做空止损价格示例: 12.400 * (1 + 0.0006) = 12.407")
            print(f"     ✅ 超短线风险回报比逻辑正确")

        except Exception as e:
            print(f"   ⚠️ 风险回报比分析错误: {e}")

    def generate_trading_report(self):
        """生成详细的交易记录追踪报告"""
        print("\n" + "="*80)
        print("📊 EAB 0023.HK 交易记录追踪报告")
        print("="*80)

        try:
            trades_df = pd.DataFrame(self.trades)
            if len(trades_df) == 0:
                print("❌ 无交易记录")
                return

            # 报告基本信息
            print(f"\n📋 基本信息")
            print(f"   股票代码: {self.symbol}")
            print(f"   测试期间: {self.df['date'].min().date()} 至 {self.df['date'].max().date()}")
            print(f"   初始资金: {self.initial_capital:,.2f} HK")
            print(f"   最终资金: {self.final_capital:,.2f} HK")
            print(f"   总收益: {self.final_capital - self.initial_capital:,.2f} HK")
            print(f"   总收益率: {(self.final_capital - self.initial_capital) / self.initial_capital * 100:.2f}%")

            # 计算年化收益率
            years = (self.df['date'].max() - self.df['date'].min()).days / 365.25
            annual_return = ((self.final_capital / self.initial_capital) ** (1/years) - 1) * 100
            print(f"   年化收益率: {annual_return:.2f}%")
            print(f"   测试年数: {years:.1f}年")

            # 交易统计
            entry_trades = trades_df[trades_df['type'].str.contains('entry')]
            exit_trades = trades_df[trades_df['type'].str.contains('exit')]

            long_entries = entry_trades[entry_trades['type'] == 'long_entry']
            short_entries = entry_trades[entry_trades['type'] == 'short_entry']

            print(f"\n📈 交易统计")
            print(f"   总交易次数: {len(entry_trades)}次")
            print(f"   多头交易: {len(long_entries)}次 ({len(long_entries)/len(entry_trades)*100:.1f}%)")
            print(f"   空头交易: {len(short_entries)}次 ({len(short_entries)/len(entry_trades)*100:.1f}%)")

            # 盈亏分析
            profitable_trades = exit_trades[exit_trades['profit'] > 0]
            losing_trades = exit_trades[exit_trades['profit'] < 0]

            print(f"\n💰 盈亏分析")
            print(f"   盈利交易: {len(profitable_trades)}次 ({len(profitable_trades)/len(exit_trades)*100:.1f}%)")
            print(f"   亏损交易: {len(losing_trades)}次 ({len(losing_trades)/len(exit_trades)*100:.1f}%)")

            if len(profitable_trades) > 0:
                avg_profit = profitable_trades['profit'].mean()
                max_profit = profitable_trades['profit'].max()
                print(f"   平均盈利: {avg_profit:.2f} HK")
                print(f"   最大盈利: {max_profit:.2f} HK")

            if len(losing_trades) > 0:
                avg_loss = losing_trades['profit'].mean()
                max_loss = losing_trades['profit'].min()
                print(f"   平均亏损: {avg_loss:.2f} HK")
                print(f"   最大亏损: {max_loss:.2f} HK")

            # 详细交易记录
            print(f"\n📋 详细交易记录 (前20笔)")
            print("-" * 80)
            print(f"{'日期':<12} {'类型':<8} {'价格':<8} {'盈亏':<10} {'资金':<12} {'备注':<20}")
            print("-" * 80)

            for i, trade in trades_df.head(20).iterrows():
                date_str = trade['date'].strftime('%Y-%m-%d')
                trade_type = "开多" if trade['type'] == 'long_entry' else \
                           "开空" if trade['type'] == 'short_entry' else \
                           "平多" if 'long_exit' in trade['type'] else \
                           "平空" if 'short_exit' in trade['type'] else "其他"

                price = trade.get('price', 0)
                profit = trade.get('profit', 0)
                capital = trade.get('capital', 0)

                # 获取备注信息
                reason = trade.get('reason', '')
                signal = trade.get('signal', '')
                note = reason if reason else signal

                print(f"{date_str:<12} {trade_type:<8} {price:<8.2f} {profit:<10.2f} {capital:<12.2f} {note:<20}")

            if len(trades_df) > 20:
                print(f"... 还有 {len(trades_df) - 20} 笔交易记录")

            print("-" * 80)

        except Exception as e:
            print(f"❌ 报告生成失败: {str(e)}")

    def analyze_no_long_term_holding(self):
        """分析不长期持仓机制的效果"""
        print("\n📊 不长期持仓机制分析:")

        trades_df = pd.DataFrame(self.trades)
        if len(trades_df) == 0:
            print("   无交易记录")
            return

        try:
            # 统计各种平仓原因
            exit_trades = trades_df[trades_df['type'].str.contains('exit')]
            if len(exit_trades) > 0:
                print("   平仓原因统计:")

                # 不长期持仓相关的平仓
                no_long_term_exits = exit_trades[exit_trades['type'].str.contains('no_long_term')]
                if len(no_long_term_exits) > 0:
                    reason_counts = no_long_term_exits['reason'].value_counts()
                    for reason, count in reason_counts.items():
                        percentage = count / len(exit_trades) * 100
                        print(f"     🔄 {reason}: {count}次 ({percentage:.1f}%)")

                # 传统止盈止损
                tp_exits = len(exit_trades[exit_trades['type'].str.contains('tp')])
                sl_exits = len(exit_trades[exit_trades['type'].str.contains('sl')])

                if tp_exits > 0:
                    print(f"     ✅ 传统止盈: {tp_exits}次 ({tp_exits/len(exit_trades)*100:.1f}%)")
                if sl_exits > 0:
                    print(f"     ❌ 传统止损: {sl_exits}次 ({sl_exits/len(exit_trades)*100:.1f}%)")

            print(f"   🎯 不长期持仓机制有效避免了长期套牢风险！")

        except Exception as e:
            print(f"   ⚠️ 分析错误: {e}")

    def print_trading_rules(self):
        """打印新的交易规则"""
        print("\n📋 新的XYE交易规则:")
        print("   变量范围: x ∈ [0,1], y ∈ [0,1], E ∈ (-∞,+∞)")
        print("   " + "="*50)
        print("   1️⃣ 观望: E≤0 且 0.333 < y < 0.4")
        print("   " + "-"*50)
        print("   2️⃣ 多头(做多,看涨): E>0 且 x≥0.45 且 y≥0.45")
        print("   " + "-"*50)
        print("   3️⃣ 空头(做空,看跌):")
        print("      • E<0 且 x<0.45 且 y<0.41")
        print("      • E<0 且 x>0.33 且 y<0.32")
        print("      • E>0 且 x<0.34 且 y<0.333 (控股商看跌)")
        print("   " + "="*50)
        print("   💰 测试资本: 2500 HK (无追加资金)")
        print("   📅 测试期间: 5年历史数据")
        print("   🎯 风险回报比: 1:2 超短线 (止损0.06%, 止盈0.12%)")

def main():
    """主函数"""
    print(f"\nEAB_0023HK回测系统（新XYE交易逻辑版 v2.4 - 超短线风险回报比）")
    print("="*80)
    print("🔧 新版本特性:")
    print("   • 实现全新的XYE交易逻辑")
    print("   • 🔥 凯利公式已禁用，使用全仓交易")
    print("   • 🎯 超短线风险回报比1:2 (止损0.06%, 止盈0.12%)")
    print("   • 优化观望、多头、空头判断条件")
    print("   • 增加控股商看跌逻辑")
    print("   • 🕐 扩展到5年历史数据回测")
    print("   • 测试2500HK初始资本5年表现")
    print("   • 无追加资金纯策略测试")

    try:
        # 创建回测实例
        backtest = EABBacktestFixed()

        # 显示新的交易规则
        backtest.print_trading_rules()

        # 加载数据
        backtest.load_data()

        # 计算回归线（用于价格位置判断）
        backtest.calculate_regression_line()

        # 运行回测
        backtest.run_backtest()

        # 生成详细交易报告
        backtest.generate_trading_report()

        print(f"\n✅ {backtest.symbol}回测程序运行完成（新XYE交易逻辑版）")

    except Exception as e:
        print(f"\n❌ 程序运行失败: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
