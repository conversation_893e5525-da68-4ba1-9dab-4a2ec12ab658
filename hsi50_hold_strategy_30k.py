#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
HSI50持仓不动策略回测系统 - 30K资金20年历史
=========================================

基于用户偏好的持仓不动策略：
- 优先持仓不动，减少频繁交易
- 只在极端情况下交易
- 严格的信号过滤
- 长期持有为主
- 复利计算

作者: Cosmoon NG
日期: 2025年7月
"""

import pandas as pd
import yfinance as yf
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

class HSI50HoldStrategy:
    def __init__(self):
        """初始化HSI50持仓不动策略回测系统"""
        self.symbol = "^HSI"  # 恒生指数
        self.initial_capital = 30000  # 30K港币
        self.data = None
        
        # 持仓不动策略参数
        self.strategy_params = {
            # 极端信号阈值 - 只在极端情况下交易
            'extreme_high_y': 0.8,          # 极高Y值阈值
            'extreme_high_x': 0.8,          # 极高X值阈值
            'extreme_low_y': 0.15,          # 极低Y值阈值
            'extreme_low_x': 0.15,          # 极低X值阈值
            'extreme_e_high': 0.5,          # 极高E值阈值
            'extreme_e_low': -0.5,          # 极低E值阈值
            
            # 持仓参数
            'max_positions': 1,             # 最多持有1个仓位
            'position_size': 0.8,           # 单次仓位80%
            'min_holding_days': 30,         # 最少持有30天
            'max_holding_days': 365,        # 最多持有1年
            
            # 止盈止损参数 - 更宽松
            'take_profit': 0.15,            # 止盈15%
            'stop_loss': 0.08,              # 止损8%
            
            # 交易参数
            'transaction_cost': 30,         # 每笔交易成本30港币
            'compound_interest': True,      # 复利计算
        }
        
        self.trades = []
        self.daily_portfolio = []
        self.current_positions = []
        self.current_capital = self.initial_capital
        self.hold_days = 0
    
    def fetch_hsi_data(self):
        """获取恒生指数20年历史数据"""
        print("📈 获取恒生指数20年历史数据...")
        
        try:
            # 获取20年数据
            end_date = datetime.now()
            start_date = end_date - timedelta(days=20*365)
            
            ticker = yf.Ticker(self.symbol)
            self.data = ticker.history(start=start_date, end=end_date)
            
            if self.data.empty:
                print("❌ 数据获取失败：返回空数据")
                return False
            
            self.data.reset_index(inplace=True)
            self.data.columns = [col.lower() for col in self.data.columns]
            
            print(f"✅ 成功获取恒生指数数据:")
            print(f"   • 数据期间: {self.data['date'].min().strftime('%Y-%m-%d')} 至 {self.data['date'].max().strftime('%Y-%m-%d')}")
            print(f"   • 总记录数: {len(self.data):,} 天")
            
            return True
            
        except Exception as e:
            print(f"❌ 获取数据失败: {e}")
            return False
    
    def calculate_indicators(self):
        """计算技术指标和策略参数"""
        print("📊 计算技术指标和持仓不动策略参数...")
        
        # 移动平均线
        self.data['ma_20'] = self.data['close'].rolling(window=20).mean()
        self.data['ma_60'] = self.data['close'].rolling(window=60).mean()
        self.data['ma_120'] = self.data['close'].rolling(window=120).mean()
        
        # RSI
        delta = self.data['close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
        rs = gain / loss
        self.data['rsi'] = 100 - (100 / (1 + rs))
        
        # 成交量指标
        self.data['volume_ma'] = self.data['volume'].rolling(window=20).mean()
        self.data['volume_ratio'] = self.data['volume'] / self.data['volume_ma']
        
        # Y值计算 (控制系数)
        price_vs_ma20 = self.data['close'] / self.data['ma_20']
        base_y = np.where(price_vs_ma20 >= 1, 
                         0.5 + 0.4 * np.tanh((price_vs_ma20 - 1) * 3),
                         0.5 - 0.4 * np.tanh((1 - price_vs_ma20) * 3))
        
        # 趋势调整
        ma_trend = (self.data['ma_20'] / self.data['ma_60']).fillna(1)
        trend_adjustment = 0.1 * np.tanh((ma_trend - 1) * 2)
        
        # 成交量调整
        volume_adjustment = 0.05 * np.tanh((self.data['volume_ratio'] - 1))
        
        self.data['y_probability'] = base_y + trend_adjustment + volume_adjustment
        self.data['y_probability'] = np.clip(self.data['y_probability'], 0.1, 0.9)
        self.data['y_probability'].fillna(0.5, inplace=True)
        
        # X值计算 (资金流比例)
        price_change = (self.data['close'] - self.data['open']) / self.data['open']
        money_flow = self.data['volume'] * price_change
        
        def calc_inflow_ratio(flows):
            if len(flows) == 0 or flows.isna().all():
                return 0.5
            flows = flows.dropna()
            if len(flows) == 0:
                return 0.5
            
            inflows = flows[flows > 0].sum()
            outflows = abs(flows[flows < 0].sum())
            total_flow = inflows + outflows
            
            return inflows / total_flow if total_flow > 0 else 0.5
        
        base_x = money_flow.rolling(window=20).apply(calc_inflow_ratio, raw=False)
        rsi_adjustment = 0.3 * (self.data['rsi'] / 100 - 0.5)
        
        self.data['inflow_ratio'] = base_x + rsi_adjustment
        self.data['inflow_ratio'] = np.clip(self.data['inflow_ratio'], 0.1, 0.9)
        self.data['inflow_ratio'].fillna(0.5, inplace=True)
        
        # E值计算 (Cosmoon博弈论核心公式)
        self.data['e_value'] = (8 * self.data['inflow_ratio'] * self.data['y_probability'] - 
                               3 * self.data['inflow_ratio'] - 3 * self.data['y_probability'] + 1)
        
        # 长期趋势判断
        self.data['long_trend_up'] = (self.data['ma_20'] > self.data['ma_60']) & (self.data['ma_60'] > self.data['ma_120'])
        self.data['long_trend_down'] = (self.data['ma_20'] < self.data['ma_60']) & (self.data['ma_60'] < self.data['ma_120'])
        
        print("✅ 指标计算完成")
    
    def get_extreme_signal(self, row):
        """获取极端交易信号 - 只在极端情况下交易"""
        y_val = row['y_probability']
        x_val = row['inflow_ratio']
        e_val = row['e_value']
        long_trend_up = row['long_trend_up']
        long_trend_down = row['long_trend_down']
        
        # 极端买涨信号：所有指标都极度乐观 + 长期上升趋势
        if (y_val > self.strategy_params['extreme_high_y'] and 
            x_val > self.strategy_params['extreme_high_x'] and
            e_val > self.strategy_params['extreme_e_high'] and
            long_trend_up):
            return 'EXTREME_BUY'
        
        # 极端买跌信号：所有指标都极度悲观 + 长期下降趋势
        elif (y_val < self.strategy_params['extreme_low_y'] and 
              x_val < self.strategy_params['extreme_low_x'] and
              e_val < self.strategy_params['extreme_e_low'] and
              long_trend_down):
            return 'EXTREME_SELL'
        
        # 其他情况：持仓不动
        else:
            return 'HOLD'
    
    def check_exit_conditions(self, position, current_price, current_date):
        """检查退出条件"""
        entry_price = position['entry_price']
        direction = position['direction']
        entry_date = position['entry_date']
        
        # 计算持仓天数
        holding_days = (current_date - entry_date).days
        
        # 计算收益率
        if direction == 'LONG':
            profit_pct = (current_price - entry_price) / entry_price
        else:  # SHORT
            profit_pct = (entry_price - current_price) / entry_price
        
        # 时间限制
        if holding_days >= self.strategy_params['max_holding_days']:
            return True, '时间止损', profit_pct
        
        # 最少持有期内不退出（除非巨亏）
        if holding_days < self.strategy_params['min_holding_days']:
            if profit_pct < -self.strategy_params['stop_loss'] * 2:  # 巨亏才退出
                return True, '巨亏止损', profit_pct
            else:
                return False, '', profit_pct
        
        # 止盈止损
        if profit_pct >= self.strategy_params['take_profit']:
            return True, '止盈', profit_pct
        elif profit_pct <= -self.strategy_params['stop_loss']:
            return True, '止损', profit_pct
        
        return False, '', profit_pct

    def backtest_hold_strategy(self):
        """执行持仓不动策略回测"""
        print("\n🚀 开始持仓不动策略回测...")
        print("="*60)
        print(f"💰 初始资金: {self.initial_capital:,} 港币")
        print(f"📊 策略: 持仓不动优先 + 极端信号交易")
        print(f"📈 极端买涨: Y>{self.strategy_params['extreme_high_y']}, X>{self.strategy_params['extreme_high_x']}, E>{self.strategy_params['extreme_e_high']} + 长期上升趋势")
        print(f"📉 极端买跌: Y<{self.strategy_params['extreme_low_y']}, X<{self.strategy_params['extreme_low_x']}, E<{self.strategy_params['extreme_e_low']} + 长期下降趋势")
        print(f"⏰ 持仓时间: {self.strategy_params['min_holding_days']}-{self.strategy_params['max_holding_days']}天")
        print(f"🎯 止盈: {self.strategy_params['take_profit']*100}%, 止损: {self.strategy_params['stop_loss']*100}%")
        print("="*60)

        # 统计变量
        total_trades = 0
        winning_trades = 0
        losing_trades = 0

        # 跳过前120天用于指标计算
        for i in range(120, len(self.data)):
            row = self.data.iloc[i]
            date = row['date']
            price = row['close']

            # 获取极端交易信号
            signal = self.get_extreme_signal(row)

            # 检查现有持仓的退出条件
            positions_to_close = []
            for j, position in enumerate(self.current_positions):
                should_exit, exit_reason, profit_pct = self.check_exit_conditions(position, price, date)

                if should_exit:
                    # 计算实际盈亏
                    if position['direction'] == 'LONG':
                        price_diff = price - position['entry_price']
                    else:  # SHORT
                        price_diff = position['entry_price'] - price

                    gross_profit = price_diff * position['shares']
                    net_profit = gross_profit - self.strategy_params['transaction_cost']

                    # 复利计算
                    if self.strategy_params['compound_interest']:
                        self.current_capital += net_profit

                    # 记录交易
                    trade_record = {
                        'entry_date': position['entry_date'].strftime('%Y-%m-%d'),
                        'exit_date': date.strftime('%Y-%m-%d'),
                        'direction': position['direction'],
                        'entry_price': round(position['entry_price'], 2),
                        'exit_price': round(price, 2),
                        'shares': round(position['shares'], 2),
                        'investment': round(position['investment'], 2),
                        'gross_profit': round(gross_profit, 2),
                        'transaction_cost': self.strategy_params['transaction_cost'],
                        'net_profit': round(net_profit, 2),
                        'profit_pct': round(profit_pct * 100, 2),
                        'holding_days': (date - position['entry_date']).days,
                        'capital_after': round(self.current_capital, 2),
                        'exit_reason': exit_reason
                    }

                    self.trades.append(trade_record)

                    if net_profit > 0:
                        winning_trades += 1
                    else:
                        losing_trades += 1

                    total_trades += 1
                    positions_to_close.append(j)

            # 移除已平仓的持仓
            for j in sorted(positions_to_close, reverse=True):
                del self.current_positions[j]

            # 根据极端信号决定开仓
            if signal == 'HOLD':
                self.hold_days += 1
                action = '持仓不动'
            elif len(self.current_positions) < self.strategy_params['max_positions']:
                # 计算投资金额
                investment_amount = self.current_capital * self.strategy_params['position_size']

                if investment_amount >= self.strategy_params['transaction_cost'] * 2:
                    # 计算股数
                    shares = (investment_amount - self.strategy_params['transaction_cost']) / price

                    # 创建持仓记录
                    if signal == 'EXTREME_BUY':
                        direction = 'LONG'
                        action = '极端买涨'
                    else:  # EXTREME_SELL
                        direction = 'SHORT'
                        action = '极端买跌'

                    position = {
                        'entry_date': date,
                        'entry_price': price,
                        'shares': shares,
                        'direction': direction,
                        'investment': investment_amount
                    }

                    self.current_positions.append(position)
                else:
                    action = '资金不足'
                    self.hold_days += 1
            else:
                action = '仓位已满'
                self.hold_days += 1

            # 记录每日组合价值
            position_value = 0
            for position in self.current_positions:
                if position['direction'] == 'LONG':
                    unrealized_profit = (price - position['entry_price']) * position['shares']
                else:  # SHORT
                    unrealized_profit = (position['entry_price'] - price) * position['shares']
                position_value += position['investment'] + unrealized_profit

            total_value = self.current_capital + position_value

            self.daily_portfolio.append({
                'date': date.strftime('%Y-%m-%d'),
                'price': price,
                'capital': self.current_capital,
                'position_value': position_value,
                'total_value': total_value,
                'action': action,
                'signal': signal,
                'y_value': row['y_probability'],
                'x_value': row['inflow_ratio'],
                'e_value': row['e_value'],
                'positions_count': len(self.current_positions)
            })

        print(f"\n✅ 持仓不动策略回测完成!")
        print(f"📊 总交易次数: {total_trades}")
        print(f"📈 盈利次数: {winning_trades}")
        print(f"📉 亏损次数: {losing_trades}")
        print(f"⏸️ 持仓不动天数: {self.hold_days}")
        if total_trades > 0:
            print(f"🎯 胜率: {winning_trades/total_trades*100:.1f}%")

        return pd.DataFrame(self.trades), pd.DataFrame(self.daily_portfolio)

    def analyze_results(self, trades_df, daily_df):
        """分析回测结果"""
        print("\n📊 持仓不动策略回测结果分析")
        print("=" * 60)

        # 基本统计
        total_trades = len(trades_df)
        final_capital = self.current_capital
        final_total_value = daily_df['total_value'].iloc[-1] if len(daily_df) > 0 else self.initial_capital
        total_return = final_capital - self.initial_capital
        total_return_rate = (total_return / self.initial_capital) * 100

        # 年化收益率
        years = 20
        annual_return_rate = ((final_capital / self.initial_capital) ** (1/years) - 1) * 100

        # 持仓不动统计
        total_days = len(daily_df)
        hold_rate = self.hold_days / total_days * 100

        if total_trades > 0:
            winning_trades = len(trades_df[trades_df['net_profit'] > 0])
            win_rate = winning_trades / total_trades * 100
            max_profit = trades_df['net_profit'].max()
            max_loss = trades_df['net_profit'].min()
            avg_profit = trades_df['net_profit'].mean()
            avg_holding_days = trades_df['holding_days'].mean()

            # 按方向分析
            direction_stats = trades_df.groupby('direction').agg({
                'net_profit': ['count', 'sum', 'mean'],
                'profit_pct': 'mean',
                'holding_days': 'mean'
            }).round(2)

            # 按退出原因分析
            exit_reason_stats = trades_df.groupby('exit_reason').agg({
                'net_profit': ['count', 'sum', 'mean'],
                'profit_pct': 'mean'
            }).round(2)
        else:
            winning_trades = 0
            win_rate = 0
            max_profit = 0
            max_loss = 0
            avg_profit = 0
            avg_holding_days = 0
            direction_stats = pd.DataFrame()
            exit_reason_stats = pd.DataFrame()

        # 输出结果
        print(f"💰 资金统计:")
        print(f"• 初始资金: {self.initial_capital:,} 港元")
        print(f"• 最终资金: {final_capital:,.0f} 港元")
        print(f"• 最终总价值: {final_total_value:,.0f} 港元")
        print(f"• 总收益: {total_return:,.0f} 港元")
        print(f"• 总收益率: {total_return_rate:.2f}%")
        print(f"• 年化收益率: {annual_return_rate:.2f}%")

        print(f"\n⏸️ 持仓不动统计:")
        print(f"• 总天数: {total_days}")
        print(f"• 持仓不动天数: {self.hold_days}")
        print(f"• 持仓不动比例: {hold_rate:.1f}%")

        if total_trades > 0:
            print(f"\n📈 交易统计:")
            print(f"• 总交易次数: {total_trades}")
            print(f"• 盈利次数: {winning_trades}")
            print(f"• 胜率: {win_rate:.1f}%")
            print(f"• 最大单笔盈利: {max_profit:,.0f} 港元")
            print(f"• 最大单笔亏损: {max_loss:,.0f} 港元")
            print(f"• 平均每笔盈亏: {avg_profit:,.0f} 港元")
            print(f"• 平均持仓天数: {avg_holding_days:.1f} 天")

            print(f"\n📊 方向分析:")
            print(direction_stats)

            print(f"\n📊 退出原因分析:")
            print(exit_reason_stats)

        # 保存到Excel
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"HSI50持仓不动策略回测结果_{timestamp}.xlsx"

        with pd.ExcelWriter(filename, engine='openpyxl') as writer:
            if len(trades_df) > 0:
                trades_df.to_excel(writer, sheet_name='交易记录', index=False)
            daily_df.to_excel(writer, sheet_name='每日数据', index=False)

            # 汇总统计
            summary_data = {
                '项目': ['初始资金(港元)', '最终资金(港元)', '最终总价值(港元)',
                        '总收益(港元)', '总收益率(%)', '年化收益率(%)',
                        '总交易次数', '盈利次数', '胜率(%)',
                        '持仓不动天数', '持仓不动比例(%)', '平均持仓天数'],
                '数值': [self.initial_capital, round(final_capital, 0), round(final_total_value, 0),
                        round(total_return, 0), round(total_return_rate, 2), round(annual_return_rate, 2),
                        total_trades, winning_trades, round(win_rate, 1),
                        self.hold_days, round(hold_rate, 1),
                        round(avg_holding_days, 1) if total_trades > 0 else 0]
            }
            pd.DataFrame(summary_data).to_excel(writer, sheet_name='汇总统计', index=False)

            if len(direction_stats) > 0:
                direction_stats.to_excel(writer, sheet_name='方向分析')
            if len(exit_reason_stats) > 0:
                exit_reason_stats.to_excel(writer, sheet_name='退出原因分析')

        print(f"\n✅ 详细结果已保存至: {filename}")
        return filename

def main():
    """主函数"""
    print("🏢 HSI50持仓不动策略回测系统")
    print("=" * 60)
    print("💰 总资金: 30,000港元")
    print("📊 分析周期: 20年历史数据")
    print("🎯 策略: 持仓不动优先 + 极端信号交易")
    print("📈 极端买涨: Y>0.8, X>0.8, E>0.5 + 长期上升趋势")
    print("📉 极端买跌: Y<0.15, X<0.15, E<-0.5 + 长期下降趋势")
    print("⏸️ 其他情况: 持仓不动")
    print("⏰ 持仓时间: 30-365天")
    print("🎯 止盈: 15%, 止损: 8%")
    print("🔄 复利计算: 启用")
    print("📊 单次仓位: 80%")

    # 创建回测器
    backtester = HSI50HoldStrategy()

    # 获取数据
    if not backtester.fetch_hsi_data():
        return

    # 计算指标
    backtester.calculate_indicators()

    # 执行回测
    trades_df, daily_df = backtester.backtest_hold_strategy()

    # 分析结果
    backtester.analyze_results(trades_df, daily_df)

if __name__ == "__main__":
    main()
