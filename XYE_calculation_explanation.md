# 数据库 eab_0023hk 表中 X_Value 和 Y_Value 计算方法详解

## 📊 概述

数据库 `finance.eab_0023hk` 表中的 `X_Value` 和 `Y_Value` 是基于技术分析的核心指标，用于生成 Cosmoon 交易信号系统。

## 🎯 Y_Value 计算方法

### 定义
Y_Value 表示**价格在20日区间的相对位置**，反映当前价格在近期价格区间中的强弱程度。

### 计算公式
```python
# 1. 计算20日最高价和最低价
window = 20
high_20 = high.rolling(window).max()    # 20日内最高价
low_20 = low.rolling(window).min()      # 20日内最低价

# 2. 计算Y值
Y_Value = (close - low_20) / (high_20 - low_20)

# 3. 数据处理
Y_Value = Y_Value.fillna(0.5).clip(0, 1)  # 填充NaN为0.5，限制在0-1范围
```

### 含义解释
- **Y_Value = 0**: 当前价格等于20日最低价（极度弱势）
- **Y_Value = 0.5**: 当前价格处于20日区间中位（中性）
- **Y_Value = 1**: 当前价格等于20日最高价（极度强势）

### 实际应用
- **Y > 0.7**: 价格处于20日区间上方，强势
- **0.333 < Y < 0.4**: 观望区间
- **Y < 0.3**: 价格处于20日区间下方，弱势

## 💰 X_Value 计算方法

### 定义
X_Value 表示**资金流强度**，基于 MFI（Money Flow Index）指标，反映资金流入流出的强度。

### 计算步骤

#### 1. 计算典型价格和资金流
```python
# 典型价格
typical_price = (high + low + close) / 3

# 资金流
money_flow = typical_price * volume

# 价格变化
price_change = typical_price.diff()
```

#### 2. 计算正负资金流
```python
# 正资金流（价格上涨时的资金流）
positive_mf = np.where(price_change > 0, money_flow, 0)

# 负资金流（价格下跌时的资金流）
negative_mf = np.where(price_change < 0, money_flow, 0)
```

#### 3. 计算14日资金流比率
```python
period = 14

# 14日正负资金流总和
positive_mf_14 = positive_mf.rolling(period).sum()
negative_mf_14 = negative_mf.rolling(period).sum()

# 资金流比率
money_flow_ratio = positive_mf_14 / (negative_mf_14 + 1e-10)  # 避免除零
```

#### 4. 计算MFI和X值
```python
# MFI指标
MFI = 100 - (100 / (1 + money_flow_ratio))

# X值（归一化到0-1）
X_Value = MFI / 100
```

### 含义解释
- **X_Value = 0**: 资金大量流出（极度弱势）
- **X_Value = 0.5**: 资金流入流出平衡（中性）
- **X_Value = 1**: 资金大量流入（极度强势）

### 实际应用
- **X > 0.8**: 资金大量流入，超买状态
- **X > 0.5**: 资金净流入，偏强势
- **X < 0.2**: 资金大量流出，超卖状态

## ⚡ E_Value 计算方法

### 定义
E_Value 是基于 X 和 Y 值的**综合能量指标**，使用 Cosmoon 公式计算。

### 计算公式
```python
E_Value = (8 * X_Value - 3) * Y_Value - 3 * X_Value + 1
```

### 简化理解
```python
# 可以重写为：
E_Value = 8 * X_Value * Y_Value - 3 * X_Value - 3 * Y_Value + 1
```

### 含义解释
- **E > 0.1**: 强势能量，买入信号
- **-0.1 < E < 0.1**: 中性能量，观望
- **E < -0.1**: 弱势能量，卖出信号

## 📈 数据更新流程

### 1. 数据获取
```python
# 从 yfinance 获取最新的 OHLCV 数据
ticker = yf.Ticker("0023.HK")
hist = ticker.history(period="100d")  # 获取100天数据用于计算
```

### 2. 指标计算
```python
# 按照上述公式计算 Y_Value, X_Value, E_Value
# 同时计算 RSI, MFI 等辅助指标
```

### 3. 数据库更新
```python
# 更新或插入到 eab_0023hk 表
UPDATE eab_0023hk SET
    Y_Value = %s, X_Value = %s, E_Value = %s,
    MFI = %s, RSI = %s, ...
WHERE Date = %s
```

## 🔍 实际数据示例

### 当前数据（2025-07-24）
```
收盘价: 12.22 港元
Y_Value: 0.3793  # 价格在20日区间的37.93%位置
X_Value: 0.3125  # MFI为31.25，资金流入偏弱
E_Value: -0.1272 # 综合能量为负，偏弱势
```

### 信号解读
- **Y = 0.3793**: 处于观望区间（0.333-0.4），价格中性偏弱
- **X = 0.3125**: 资金流入不足，偏弱势
- **E = -0.1272**: 综合能量为负，建议谨慎或卖出

## 🎯 交易信号规则

### XYE 组合信号
```python
# 观望区间
if 0.333 < Y < 0.4:
    signal = "观望"

# 做多条件
elif (X >= 0.5 and Y >= 0.5) or (X < 0.25 and Y < 0.25):
    if E > 0.1:
        signal = "强烈买入"
    else:
        signal = "买入"

# 做空条件
elif (X > 0.45 and Y < 0.35) or (X < 0.45 and Y > 0.35):
    if E < -0.1:
        signal = "强烈卖出"
    else:
        signal = "卖出"
```

## 📝 技术特点

### 优势
1. **实时性**: 基于最新市场数据计算
2. **综合性**: 结合价格位置和资金流向
3. **标准化**: 所有指标都归一化到0-1范围
4. **稳定性**: 使用移动窗口平滑数据

### 注意事项
1. **滞后性**: 基于历史数据，存在一定滞后
2. **市场环境**: 在震荡市场中效果更好
3. **参数调整**: 窗口期（20日、14日）可根据市场调整

## 🔧 代码实现位置

主要计算代码位于：
- `daily_update_eab_table.py` - 主要计算逻辑
- `daily_update_system.py` - 系统更新流程
- `Signals.py` - 信号生成逻辑

数据库表结构：
- 表名: `finance.eab_0023hk`
- 字段: `Y_Value`, `X_Value`, `E_Value`, `MFI`, `RSI`
