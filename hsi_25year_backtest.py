#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
恒生指数25年历史回测 - 博弈论策略
================================

使用博弈论方法回测恒生指数25年历史数据
核心策略：
- 买入条件: X>0.4 且 Y>0.4 (资金流入比例>0.4 且 博弈论概率>0.4)
- 卖出条件: X<0.4 (资金流入比例<0.4)

作者: 博弈论投资策略团队
日期: 2025年7月
"""

import pymysql
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

class HSI25YearBacktest:
    def __init__(self):
        """初始化回测系统"""
        self.db_config = {
            'host': 'localhost',
            'port': 3306,
            'database': 'finance',
            'user': 'root',
            'password': '12345678',
            'charset': 'utf8mb4'
        }
        
        # 策略参数
        self.strategy_params = {
            'y_buy_threshold': 0.4,      # Y买入阈值
            'x_buy_threshold': 0.4,      # X买入阈值
            'x_sell_threshold': 0.4,     # X卖出阈值
            'initial_capital': 100000,   # 初始资金10万港币
            'position_size': 0.8,        # 仓位比例80%
            'transaction_cost': 0.001,   # 交易成本0.1%
            'min_holding_days': 1,       # 最少持有天数
        }
        
        self.data = None
        self.trades = []
        self.portfolio_value = []
        self.positions = []
        
    def connect_database(self):
        """连接数据库"""
        try:
            self.connection = pymysql.connect(**self.db_config)
            print("✅ 数据库连接成功")
            return True
        except Exception as e:
            print(f"❌ 数据库连接失败: {e}")
            return False
    
    def load_hsi_data(self):
        """加载恒生指数历史数据"""
        print("📊 加载恒生指数25年历史数据...")
        
        try:
            # 查询25年的数据
            query = """
                SELECT 
                    date,
                    close as price,
                    volume,
                    y_probability,
                    inflow_ratio as x_ratio,
                    median_price,
                    high,
                    low,
                    open
                FROM hk00023 
                WHERE date >= DATE_SUB(CURDATE(), INTERVAL 25 YEAR)
                AND y_probability IS NOT NULL 
                AND inflow_ratio IS NOT NULL
                ORDER BY date ASC
            """
            
            self.data = pd.read_sql(query, self.connection)
            
            if self.data.empty:
                print("❌ 没有找到恒生指数数据，尝试查找其他表...")
                
                # 尝试查找hk2800表
                query_hk2800 = """
                    SELECT 
                        date,
                        close as price,
                        volume,
                        y_probability,
                        inflow_ratio as x_ratio,
                        median_price,
                        high,
                        low,
                        open
                    FROM hk2800 
                    WHERE date >= DATE_SUB(CURDATE(), INTERVAL 25 YEAR)
                    AND y_probability IS NOT NULL 
                    AND inflow_ratio IS NOT NULL
                    ORDER BY date ASC
                """
                
                self.data = pd.read_sql(query_hk2800, self.connection)
                
                if not self.data.empty:
                    print("✅ 使用HK2800(盈富基金)数据进行回测")
                else:
                    print("❌ 未找到可用的历史数据")
                    return False
            else:
                print("✅ 使用HK00023(恒生指数)数据进行回测")
            
            # 数据预处理
            self.data['date'] = pd.to_datetime(self.data['date'])
            self.data = self.data.sort_values('date').reset_index(drop=True)
            
            # 计算技术指标
            self.calculate_technical_indicators()
            
            print(f"📈 数据加载完成:")
            print(f"   • 数据期间: {self.data['date'].min()} 至 {self.data['date'].max()}")
            print(f"   • 总交易日: {len(self.data)} 天")
            print(f"   • 数据完整性: Y值 {self.data['y_probability'].notna().sum()}/{len(self.data)}")
            print(f"   • 数据完整性: X值 {self.data['x_ratio'].notna().sum()}/{len(self.data)}")
            
            return True
            
        except Exception as e:
            print(f"❌ 数据加载失败: {e}")
            return False
    
    def calculate_technical_indicators(self):
        """计算技术指标"""
        print("🔧 计算技术指标...")
        
        # 移动平均线
        self.data['ma_5'] = self.data['price'].rolling(window=5).mean()
        self.data['ma_20'] = self.data['price'].rolling(window=20).mean()
        self.data['ma_60'] = self.data['price'].rolling(window=60).mean()
        
        # 价格相对位置
        self.data['price_vs_median'] = self.data['price'] / self.data['median_price']
        
        # 波动率
        self.data['volatility'] = self.data['price'].pct_change().rolling(window=20).std() * np.sqrt(252)
        
        # RSI
        delta = self.data['price'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
        rs = gain / loss
        self.data['rsi'] = 100 - (100 / (1 + rs))
        
        print("✅ 技术指标计算完成")
    
    def run_backtest(self):
        """运行回测"""
        print("🚀 开始25年博弈论策略回测...")
        print("="*60)
        
        capital = self.strategy_params['initial_capital']
        position = 0  # 持仓数量
        position_value = 0  # 持仓价值
        cash = capital  # 现金
        last_trade_date = None
        
        buy_signals = 0
        sell_signals = 0
        total_trades = 0
        
        for i, row in self.data.iterrows():
            date = row['date']
            price = row['price']
            y_prob = row['y_probability']
            x_ratio = row['x_ratio']
            
            # 跳过缺失数据
            if pd.isna(y_prob) or pd.isna(x_ratio) or pd.isna(price):
                continue
            
            # 当前组合价值
            current_position_value = position * price if position > 0 else 0
            total_value = cash + current_position_value
            
            # 交易信号判断
            buy_signal = (y_prob > self.strategy_params['y_buy_threshold'] and 
                         x_ratio > self.strategy_params['x_buy_threshold'])
            
            sell_signal = x_ratio < self.strategy_params['x_sell_threshold']
            
            # 检查最少持有期
            can_trade = True
            if last_trade_date:
                days_since_last_trade = (date - last_trade_date).days
                if days_since_last_trade < self.strategy_params['min_holding_days']:
                    can_trade = False
            
            # 执行交易逻辑
            if buy_signal and position == 0 and can_trade:
                # 买入信号且无持仓
                buy_amount = cash * self.strategy_params['position_size']
                transaction_cost = buy_amount * self.strategy_params['transaction_cost']
                net_buy_amount = buy_amount - transaction_cost
                
                if net_buy_amount > 0:
                    position = net_buy_amount / price
                    cash -= buy_amount
                    
                    # 记录交易
                    trade = {
                        'date': date,
                        'action': 'BUY',
                        'price': price,
                        'quantity': position,
                        'value': buy_amount,
                        'cost': transaction_cost,
                        'y_value': y_prob,
                        'x_value': x_ratio,
                        'cash_after': cash,
                        'total_value': total_value
                    }
                    self.trades.append(trade)
                    
                    buy_signals += 1
                    total_trades += 1
                    last_trade_date = date
                    
            elif sell_signal and position > 0 and can_trade:
                # 卖出信号且有持仓
                sell_value = position * price
                transaction_cost = sell_value * self.strategy_params['transaction_cost']
                net_sell_value = sell_value - transaction_cost
                
                cash += net_sell_value
                
                # 记录交易
                trade = {
                    'date': date,
                    'action': 'SELL',
                    'price': price,
                    'quantity': position,
                    'value': sell_value,
                    'cost': transaction_cost,
                    'y_value': y_prob,
                    'x_value': x_ratio,
                    'cash_after': cash,
                    'total_value': cash  # 卖出后全部为现金
                }
                self.trades.append(trade)
                
                position = 0
                sell_signals += 1
                total_trades += 1
                last_trade_date = date
            
            # 记录组合价值
            current_total_value = cash + (position * price if position > 0 else 0)
            self.portfolio_value.append({
                'date': date,
                'total_value': current_total_value,
                'cash': cash,
                'position_value': position * price if position > 0 else 0,
                'position': position,
                'price': price
            })
        
        # 最终清仓（如果有持仓）
        if position > 0:
            final_row = self.data.iloc[-1]
            final_price = final_row['price']
            final_value = position * final_price
            cash += final_value * (1 - self.strategy_params['transaction_cost'])
            
            # 记录最终卖出
            final_trade = {
                'date': final_row['date'],
                'action': 'FINAL_SELL',
                'price': final_price,
                'quantity': position,
                'value': final_value,
                'cost': final_value * self.strategy_params['transaction_cost'],
                'y_value': final_row['y_probability'],
                'x_value': final_row['x_ratio'],
                'cash_after': cash,
                'total_value': cash
            }
            self.trades.append(final_trade)
        
        print(f"✅ 回测完成!")
        print(f"📊 交易统计:")
        print(f"   • 买入信号: {buy_signals} 次")
        print(f"   • 卖出信号: {sell_signals} 次") 
        print(f"   • 总交易次数: {total_trades} 次")
        print(f"   • 最终资金: {cash:,.2f} 港币")
        print(f"   • 总收益: {cash - capital:,.2f} 港币")
        print(f"   • 总收益率: {(cash/capital - 1)*100:.2f}%")
        
        return True
    
    def analyze_performance(self):
        """分析回测性能"""
        print("\n📈 性能分析...")
        print("="*60)
        
        if not self.portfolio_value or not self.trades:
            print("❌ 没有回测数据可分析")
            return
        
        # 转换为DataFrame
        portfolio_df = pd.DataFrame(self.portfolio_value)
        trades_df = pd.DataFrame(self.trades)
        
        # 基本统计
        initial_value = self.strategy_params['initial_capital']
        final_value = portfolio_df['total_value'].iloc[-1]
        total_return = (final_value / initial_value - 1) * 100
        
        # 计算年化收益率
        start_date = portfolio_df['date'].iloc[0]
        end_date = portfolio_df['date'].iloc[-1]
        years = (end_date - start_date).days / 365.25
        annual_return = (final_value / initial_value) ** (1/years) - 1
        
        # 计算最大回撤
        portfolio_df['cumulative_return'] = portfolio_df['total_value'] / initial_value
        portfolio_df['running_max'] = portfolio_df['cumulative_return'].expanding().max()
        portfolio_df['drawdown'] = (portfolio_df['cumulative_return'] - portfolio_df['running_max']) / portfolio_df['running_max']
        max_drawdown = portfolio_df['drawdown'].min() * 100
        
        # 交易分析
        buy_trades = trades_df[trades_df['action'] == 'BUY']
        sell_trades = trades_df[trades_df['action'].isin(['SELL', 'FINAL_SELL'])]
        
        if len(buy_trades) > 0 and len(sell_trades) > 0:
            # 配对交易分析
            paired_trades = []
            for i in range(min(len(buy_trades), len(sell_trades))):
                buy_trade = buy_trades.iloc[i]
                sell_trade = sell_trades.iloc[i]
                
                profit = sell_trade['value'] - buy_trade['value'] - buy_trade['cost'] - sell_trade['cost']
                profit_pct = (profit / buy_trade['value']) * 100
                holding_days = (sell_trade['date'] - buy_trade['date']).days
                
                paired_trades.append({
                    'buy_date': buy_trade['date'],
                    'sell_date': sell_trade['date'],
                    'buy_price': buy_trade['price'],
                    'sell_price': sell_trade['price'],
                    'profit': profit,
                    'profit_pct': profit_pct,
                    'holding_days': holding_days
                })
            
            paired_df = pd.DataFrame(paired_trades)
            
            # 胜率统计
            winning_trades = len(paired_df[paired_df['profit'] > 0])
            total_paired_trades = len(paired_df)
            win_rate = (winning_trades / total_paired_trades * 100) if total_paired_trades > 0 else 0
            
            avg_profit = paired_df['profit'].mean()
            avg_profit_pct = paired_df['profit_pct'].mean()
            avg_holding_days = paired_df['holding_days'].mean()
            
            # 盈亏比
            winning_avg = paired_df[paired_df['profit'] > 0]['profit'].mean() if winning_trades > 0 else 0
            losing_avg = abs(paired_df[paired_df['profit'] < 0]['profit'].mean()) if (total_paired_trades - winning_trades) > 0 else 0
            profit_loss_ratio = winning_avg / losing_avg if losing_avg > 0 else float('inf')
        
        # 打印详细分析结果
        print(f"📊 总体表现:")
        print(f"   • 回测期间: {start_date.strftime('%Y-%m-%d')} 至 {end_date.strftime('%Y-%m-%d')}")
        print(f"   • 回测年数: {years:.1f} 年")
        print(f"   • 初始资金: {initial_value:,.0f} 港币")
        print(f"   • 最终资金: {final_value:,.0f} 港币")
        print(f"   • 总收益: {final_value - initial_value:,.0f} 港币")
        print(f"   • 总收益率: {total_return:.2f}%")
        print(f"   • 年化收益率: {annual_return*100:.2f}%")
        print(f"   • 最大回撤: {max_drawdown:.2f}%")
        
        if len(buy_trades) > 0 and len(sell_trades) > 0:
            print(f"\n📈 交易表现:")
            print(f"   • 总交易对数: {total_paired_trades} 对")
            print(f"   • 获利交易: {winning_trades} 次")
            print(f"   • 亏损交易: {total_paired_trades - winning_trades} 次")
            print(f"   • 胜率: {win_rate:.1f}%")
            print(f"   • 平均收益: {avg_profit:,.0f} 港币 ({avg_profit_pct:.2f}%)")
            print(f"   • 平均持有天数: {avg_holding_days:.1f} 天")
            print(f"   • 盈亏比: {profit_loss_ratio:.2f}")
        
        # 与买入持有策略对比
        buy_hold_return = (portfolio_df['price'].iloc[-1] / portfolio_df['price'].iloc[0] - 1) * 100
        buy_hold_annual = (portfolio_df['price'].iloc[-1] / portfolio_df['price'].iloc[0]) ** (1/years) - 1
        
        print(f"\n📊 策略对比:")
        print(f"   • 买入持有总收益率: {buy_hold_return:.2f}%")
        print(f"   • 买入持有年化收益率: {buy_hold_annual*100:.2f}%")
        print(f"   • 策略超额收益: {total_return - buy_hold_return:.2f}%")
        print(f"   • 年化超额收益: {(annual_return - buy_hold_annual)*100:.2f}%")
        
        # 保存结果到文件
        self.save_results(portfolio_df, trades_df, {
            'total_return': total_return,
            'annual_return': annual_return * 100,
            'max_drawdown': max_drawdown,
            'win_rate': win_rate if 'win_rate' in locals() else 0,
            'total_trades': len(trades_df),
            'years': years
        })
    
    def create_visualizations(self, portfolio_df, trades_df):
        """创建可视化图表"""
        print("\n📊 生成可视化图表...")

        # 设置图表样式
        plt.style.use('seaborn-v0_8')
        fig, axes = plt.subplots(2, 2, figsize=(16, 12))
        fig.suptitle('恒生指数25年博弈论策略回测分析', fontsize=16, fontweight='bold')

        # 1. 组合价值变化
        ax1 = axes[0, 0]
        ax1.plot(portfolio_df['date'], portfolio_df['total_value'],
                label='策略组合价值', linewidth=2, color='blue')
        ax1.plot(portfolio_df['date'],
                portfolio_df['price'] * self.strategy_params['initial_capital'] / portfolio_df['price'].iloc[0],
                label='买入持有', linewidth=2, color='red', alpha=0.7)
        ax1.set_title('组合价值变化对比')
        ax1.set_ylabel('组合价值 (港币)')
        ax1.legend()
        ax1.grid(True, alpha=0.3)

        # 2. 回撤分析
        ax2 = axes[0, 1]
        portfolio_df['cumulative_return'] = portfolio_df['total_value'] / self.strategy_params['initial_capital']
        portfolio_df['running_max'] = portfolio_df['cumulative_return'].expanding().max()
        portfolio_df['drawdown'] = (portfolio_df['cumulative_return'] - portfolio_df['running_max']) / portfolio_df['running_max'] * 100

        ax2.fill_between(portfolio_df['date'], portfolio_df['drawdown'], 0,
                        color='red', alpha=0.3, label='回撤')
        ax2.plot(portfolio_df['date'], portfolio_df['drawdown'], color='red', linewidth=1)
        ax2.set_title('策略回撤分析')
        ax2.set_ylabel('回撤 (%)')
        ax2.legend()
        ax2.grid(True, alpha=0.3)

        # 3. Y和X指标分布
        ax3 = axes[1, 0]
        ax3.scatter(self.data['x_ratio'], self.data['y_probability'],
                   alpha=0.5, s=1, color='blue')
        ax3.axhline(y=0.4, color='red', linestyle='--', label='Y=0.4阈值')
        ax3.axvline(x=0.4, color='red', linestyle='--', label='X=0.4阈值')
        ax3.set_xlabel('X值 (资金流入比例)')
        ax3.set_ylabel('Y值 (博弈论概率)')
        ax3.set_title('Y-X指标分布图')
        ax3.legend()
        ax3.grid(True, alpha=0.3)

        # 4. 月度收益分布
        ax4 = axes[1, 1]
        portfolio_df['month'] = portfolio_df['date'].dt.to_period('M')
        monthly_returns = portfolio_df.groupby('month')['total_value'].last().pct_change().dropna() * 100

        ax4.hist(monthly_returns, bins=30, alpha=0.7, color='green', edgecolor='black')
        ax4.axvline(monthly_returns.mean(), color='red', linestyle='--',
                   label=f'平均: {monthly_returns.mean():.2f}%')
        ax4.set_xlabel('月度收益率 (%)')
        ax4.set_ylabel('频次')
        ax4.set_title('月度收益率分布')
        ax4.legend()
        ax4.grid(True, alpha=0.3)

        plt.tight_layout()

        # 保存图表
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        chart_filename = f"hsi_25year_analysis_{timestamp}.png"
        plt.savefig(chart_filename, dpi=300, bbox_inches='tight')
        plt.show()

        print(f"✅ 图表已保存: {chart_filename}")
        return chart_filename

    def save_results(self, portfolio_df, trades_df, summary):
        """保存回测结果"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        # 保存交易记录
        trades_filename = f"hsi_25year_trades_{timestamp}.csv"
        trades_df.to_csv(trades_filename, index=False, encoding='utf-8-sig')

        # 保存组合价值变化
        portfolio_filename = f"hsi_25year_portfolio_{timestamp}.csv"
        portfolio_df.to_csv(portfolio_filename, index=False, encoding='utf-8-sig')

        # 创建可视化图表
        chart_filename = self.create_visualizations(portfolio_df, trades_df)

        # 生成详细报告
        report_filename = f"hsi_25year_report_{timestamp}.txt"
        with open(report_filename, 'w', encoding='utf-8') as f:
            f.write("恒生指数25年博弈论策略回测报告\n")
            f.write("="*50 + "\n\n")
            f.write(f"回测时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"数据期间: {portfolio_df['date'].min()} 至 {portfolio_df['date'].max()}\n")
            f.write(f"回测年数: {summary['years']:.1f} 年\n\n")

            f.write("策略参数:\n")
            for key, value in self.strategy_params.items():
                f.write(f"  {key}: {value}\n")
            f.write("\n")

            f.write("回测结果:\n")
            f.write(f"  总收益率: {summary['total_return']:.2f}%\n")
            f.write(f"  年化收益率: {summary['annual_return']:.2f}%\n")
            f.write(f"  最大回撤: {summary['max_drawdown']:.2f}%\n")
            f.write(f"  胜率: {summary['win_rate']:.1f}%\n")
            f.write(f"  总交易次数: {summary['total_trades']}\n")

        print(f"\n💾 结果已保存:")
        print(f"   • 交易记录: {trades_filename}")
        print(f"   • 组合价值: {portfolio_filename}")
        print(f"   • 分析图表: {chart_filename}")
        print(f"   • 详细报告: {report_filename}")

        return trades_filename, portfolio_filename, chart_filename, report_filename

def main():
    """主函数"""
    print("🎯 恒生指数25年博弈论策略回测")
    print("="*60)
    print("📅 启动时间:", datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
    
    # 创建回测实例
    backtest = HSI25YearBacktest()
    
    # 连接数据库
    if not backtest.connect_database():
        return
    
    # 加载数据
    if not backtest.load_hsi_data():
        return
    
    # 运行回测
    if not backtest.run_backtest():
        return
    
    # 分析性能
    backtest.analyze_performance()
    
    print("\n🎉 25年恒生指数博弈论策略回测完成!")
    print("💡 投资有风险，回测结果仅供参考")

if __name__ == "__main__":
    main()
