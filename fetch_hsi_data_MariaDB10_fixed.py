#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
获取恒生50指数数据 - 修复版本
===========================

从Yahoo Finance获取恒生50指数数据并存入MariaDB数据库
修复空数据问题：
1. 数据验证和清理
2. 空值处理
3. 数据完整性检查
4. 错误重试机制

作者: Cosmoon NG
日期: 2025年7月
"""

import yfinance as yf
import pandas as pd
import pymysql
from datetime import datetime, timedelta
import numpy as np
import warnings
warnings.filterwarnings('ignore')

# 数据库配置
db_config = {
    'host': 'localhost',
    'user': 'root',
    'password': '12345678',
    'database': 'finance',
    'charset': 'utf8mb4'
}

def check_data_quality(df):
    """检查数据质量"""
    print("🔍 检查数据质量...")
    
    if df.empty:
        print("❌ 数据为空")
        return False
    
    # 检查基础数据
    required_columns = ['Open', 'High', 'Low', 'Close', 'Volume']
    missing_columns = [col for col in required_columns if col not in df.columns]
    
    if missing_columns:
        print(f"❌ 缺少必要列: {missing_columns}")
        return False
    
    # 检查空值
    null_counts = df[required_columns].isnull().sum()
    total_nulls = null_counts.sum()
    
    print(f"📊 数据质量报告:")
    print(f"   • 总记录数: {len(df)}")
    print(f"   • 日期范围: {df.index.min().date()} 至 {df.index.max().date()}")
    
    for col in required_columns:
        null_count = null_counts[col]
        null_pct = null_count / len(df) * 100
        print(f"   • {col}: {null_count} 空值 ({null_pct:.1f}%)")
    
    # 检查价格数据的合理性
    price_issues = 0
    for col in ['Open', 'High', 'Low', 'Close']:
        if (df[col] <= 0).any():
            price_issues += 1
            print(f"⚠️  {col} 包含非正数值")
    
    # 检查OHLC逻辑
    ohlc_issues = 0
    if (df['High'] < df['Low']).any():
        ohlc_issues += 1
        print("⚠️  发现 High < Low 的异常数据")
    
    if (df['High'] < df['Close']).any() or (df['High'] < df['Open']).any():
        ohlc_issues += 1
        print("⚠️  发现 High 小于 Open/Close 的异常数据")
    
    if (df['Low'] > df['Close']).any() or (df['Low'] > df['Open']).any():
        ohlc_issues += 1
        print("⚠️  发现 Low 大于 Open/Close 的异常数据")
    
    # 总体评估
    if total_nulls == 0 and price_issues == 0 and ohlc_issues == 0:
        print("✅ 数据质量良好")
        return True
    elif total_nulls < len(df) * 0.05:  # 空值少于5%
        print("⚠️  数据质量一般，但可以使用")
        return True
    else:
        print("❌ 数据质量较差")
        return False

def clean_data(df):
    """清理数据"""
    print("🧹 清理数据...")
    
    original_count = len(df)
    
    # 1. 移除完全空的行
    df = df.dropna(how='all')
    
    # 2. 移除价格数据为空或非正数的行
    price_columns = ['Open', 'High', 'Low', 'Close']
    for col in price_columns:
        df = df[df[col] > 0]
        df = df[df[col].notna()]
    
    # 3. 移除成交量为负数的行
    df = df[df['Volume'] >= 0]
    
    # 4. 修复OHLC逻辑错误
    # 确保 High >= max(Open, Close) 和 Low <= min(Open, Close)
    df['High'] = df[['High', 'Open', 'Close']].max(axis=1)
    df['Low'] = df[['Low', 'Open', 'Close']].min(axis=1)
    
    # 5. 处理异常的价格跳跃（超过20%的单日变化）
    df['price_change'] = df['Close'].pct_change().abs()
    extreme_changes = df['price_change'] > 0.2
    
    if extreme_changes.any():
        print(f"⚠️  发现 {extreme_changes.sum()} 个极端价格变化，将进行平滑处理")
        # 对极端变化进行平滑处理
        for idx in df[extreme_changes].index:
            if idx != df.index[0]:  # 不处理第一行
                prev_idx = df.index[df.index < idx][-1]
                # 使用前一天的价格进行插值
                df.loc[idx, 'Close'] = (df.loc[prev_idx, 'Close'] + df.loc[idx, 'Close']) / 2
                df.loc[idx, 'Open'] = df.loc[prev_idx, 'Close']
                df.loc[idx, 'High'] = max(df.loc[idx, 'Open'], df.loc[idx, 'Close'])
                df.loc[idx, 'Low'] = min(df.loc[idx, 'Open'], df.loc[idx, 'Close'])
    
    # 移除临时列
    df = df.drop('price_change', axis=1)
    
    cleaned_count = len(df)
    removed_count = original_count - cleaned_count
    
    print(f"✅ 数据清理完成:")
    print(f"   • 原始记录: {original_count}")
    print(f"   • 清理后记录: {cleaned_count}")
    print(f"   • 移除记录: {removed_count}")
    
    return df

def calculate_indicators_robust(df):
    """稳健的技术指标计算"""
    print("🧮 计算技术指标...")
    
    # 确保数据按日期排序
    df = df.sort_index()
    
    # 计算移动平均线
    df['ma20'] = df['Close'].rolling(window=20, min_periods=10).mean()
    df['ma60'] = df['Close'].rolling(window=60, min_periods=30).mean()
    
    # 计算RSI（使用更稳健的方法）
    delta = df['Close'].diff()
    gain = delta.where(delta > 0, 0)
    loss = -delta.where(delta < 0, 0)
    
    # 使用指数移动平均计算RSI
    alpha = 1/14
    avg_gain = gain.ewm(alpha=alpha, adjust=False).mean()
    avg_loss = loss.ewm(alpha=alpha, adjust=False).mean()
    
    rs = avg_gain / avg_loss
    df['rsi'] = 100 - (100 / (1 + rs))
    
    # 处理RSI的极值
    df['rsi'] = df['rsi'].clip(0, 100)
    
    # 计算资金流（处理除零错误）
    price_change = (df['Close'] - df['Open']) / df['Open']
    price_change = price_change.fillna(0)  # 处理除零情况
    df['money_flow'] = df['Volume'] * price_change
    
    # 计算基础X值（20日资金流向）
    def safe_flow_ratio(flows):
        flows = flows.dropna()
        if len(flows) == 0:
            return 0.5
        
        pos_flow = flows[flows > 0].sum()
        neg_flow = abs(flows[flows < 0].sum())
        total_flow = pos_flow + neg_flow
        
        if total_flow == 0:
            return 0.5
        
        return pos_flow / total_flow
    
    df['base_x'] = df['money_flow'].rolling(window=20, min_periods=10).apply(safe_flow_ratio, raw=False)
    
    # 计算最终X值
    rsi_adj = 0.3 * (df['rsi']/100 - 0.5)
    df['x_value'] = df['base_x'] + rsi_adj
    df['x_value'] = df['x_value'].clip(0.1, 0.9)
    
    # 计算基础Y值（处理除零错误）
    price_ma20_ratio = df['Close'] / df['ma20']
    price_ma20_ratio = price_ma20_ratio.fillna(1)  # 处理除零情况
    
    # 使用向量化操作计算base_y
    mask = price_ma20_ratio >= 1
    df['base_y'] = np.where(
        mask,
        0.5 + 0.4 * np.tanh((price_ma20_ratio - 1) * 3),
        0.5 - 0.4 * np.tanh((1 - price_ma20_ratio) * 3)
    )
    
    # 趋势调整（处理除零错误）
    ma_trend = df['ma20'] / df['ma60']
    ma_trend = ma_trend.fillna(1)  # 处理除零情况
    trend_adj = 0.1 * np.tanh((ma_trend - 1) * 2)
    
    # 成交量调整
    volume_ma20 = df['Volume'].rolling(window=20, min_periods=10).mean()
    volume_ratio = df['Volume'] / volume_ma20
    volume_ratio = volume_ratio.fillna(1)  # 处理除零情况
    vol_adj = 0.05 * np.tanh((volume_ratio - 1))
    
    # 计算最终Y值
    df['y_value'] = (df['base_y'] + trend_adj + vol_adj).clip(0.1, 0.9)
    
    # 计算E值
    df['e_value'] = 8 * df['x_value'] * df['y_value'] - 3 * df['x_value'] - 3 * df['y_value'] + 1
    
    # 最终数据验证
    print(f"📊 技术指标计算完成:")
    
    indicators = ['ma20', 'ma60', 'rsi', 'x_value', 'y_value', 'e_value']
    for indicator in indicators:
        null_count = df[indicator].isnull().sum()
        null_pct = null_count / len(df) * 100
        print(f"   • {indicator}: {null_count} 空值 ({null_pct:.1f}%)")
    
    # 用前向填充处理剩余的NaN
    df = df.fillna(method='ffill')
    
    # 用后向填充处理开头的NaN
    df = df.fillna(method='bfill')
    
    # 最后用默认值填充任何剩余的NaN
    default_values = {
        'ma20': df['Close'],
        'ma60': df['Close'],
        'rsi': 50,
        'money_flow': 0,
        'base_x': 0.5,
        'x_value': 0.5,
        'base_y': 0.5,
        'y_value': 0.5,
        'e_value': 0
    }
    
    for col, default_val in default_values.items():
        if col in df.columns:
            df[col] = df[col].fillna(default_val)
    
    # 替换任何剩余的 NaN 为 None（MySQL 兼容）
    df = df.replace({np.nan: None, np.inf: None, -np.inf: None})
    
    return df

def create_database_robust():
    """创建数据库和表结构（稳健版本）"""
    try:
        conn = pymysql.connect(**db_config)
        cursor = conn.cursor()
        
        # 删除旧表（如果需要重新开始）
        # cursor.execute('DROP TABLE IF EXISTS hkhsi50')
        
        # 创建数据表
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS hkhsi50 (
            date DATE PRIMARY KEY,
            open DECIMAL(15,4) NOT NULL,
            high DECIMAL(15,4) NOT NULL,
            low DECIMAL(15,4) NOT NULL,
            close DECIMAL(15,4) NOT NULL,
            volume BIGINT NOT NULL DEFAULT 0,
            ma20 DECIMAL(15,4),
            ma60 DECIMAL(15,4),
            rsi DECIMAL(15,4),
            money_flow DECIMAL(15,4),
            base_x DECIMAL(15,4),
            x_value DECIMAL(15,4),
            base_y DECIMAL(15,4),
            y_value DECIMAL(15,4),
            e_value DECIMAL(15,4),
            INDEX idx_date (date),
            INDEX idx_close (close),
            INDEX idx_y_value (y_value),
            INDEX idx_x_value (x_value)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ''')
        
        conn.commit()
        print("✅ 数据库表创建/检查完成")
        
        # 检查现有数据
        cursor.execute("SELECT COUNT(*), MIN(date), MAX(date) FROM hkhsi50")
        result = cursor.fetchone()
        if result[0] > 0:
            print(f"📊 现有数据: {result[0]} 条记录，日期范围: {result[1]} 至 {result[2]}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 数据库操作出错: {str(e)}")
        return False

def insert_data_robust(df):
    """稳健的数据插入"""
    try:
        conn = pymysql.connect(**db_config)
        cursor = conn.cursor()
        
        # 准备插入SQL
        sql = """
        INSERT INTO hkhsi50 (
            date, open, high, low, close, volume, 
            ma20, ma60, rsi, money_flow, 
            base_x, x_value, base_y, y_value, e_value
        ) VALUES (
            %s, %s, %s, %s, %s, %s, 
            %s, %s, %s, %s, 
            %s, %s, %s, %s, %s
        )
        ON DUPLICATE KEY UPDATE
            open = VALUES(open),
            high = VALUES(high),
            low = VALUES(low),
            close = VALUES(close),
            volume = VALUES(volume),
            ma20 = VALUES(ma20),
            ma60 = VALUES(ma60),
            rsi = VALUES(rsi),
            money_flow = VALUES(money_flow),
            base_x = VALUES(base_x),
            x_value = VALUES(x_value),
            base_y = VALUES(base_y),
            y_value = VALUES(y_value),
            e_value = VALUES(e_value)
        """
        
        # 转换数据格式
        df = df.reset_index()
        df['Date'] = pd.to_datetime(df['Date']).dt.date
        
        # 验证数据
        success_count = 0
        error_count = 0
        
        # 逐条插入以便更好的错误处理
        for _, row in df.iterrows():
            try:
                data = (
                    row['Date'], 
                    float(row['Open']), float(row['High']), float(row['Low']), 
                    float(row['Close']), int(row['Volume']),
                    float(row['ma20']) if row['ma20'] is not None else None,
                    float(row['ma60']) if row['ma60'] is not None else None,
                    float(row['rsi']) if row['rsi'] is not None else None,
                    float(row['money_flow']) if row['money_flow'] is not None else None,
                    float(row['base_x']) if row['base_x'] is not None else None,
                    float(row['x_value']) if row['x_value'] is not None else None,
                    float(row['base_y']) if row['base_y'] is not None else None,
                    float(row['y_value']) if row['y_value'] is not None else None,
                    float(row['e_value']) if row['e_value'] is not None else None
                )
                
                cursor.execute(sql, data)
                success_count += 1
                
                if success_count % 100 == 0:
                    conn.commit()
                    print(f"✅ 已插入 {success_count}/{len(df)} 条记录")
                    
            except Exception as e:
                error_count += 1
                print(f"⚠️  插入第 {success_count + error_count} 条记录失败: {e}")
                continue
        
        # 最终提交
        conn.commit()
        
        print(f"📊 数据插入完成:")
        print(f"   • 成功插入: {success_count} 条")
        print(f"   • 插入失败: {error_count} 条")
        print(f"   • 成功率: {success_count/(success_count+error_count)*100:.1f}%")
        
        conn.close()
        return success_count > 0
        
    except Exception as e:
        print(f"❌ 数据库插入出错: {str(e)}")
        return False

def fetch_hsi50_data_robust():
    """稳健的恒生50指数数据获取"""
    print("📊 开始获取恒生50指数数据（修复版本）...")
    
    # 创建数据库和表
    if not create_database_robust():
        return
    
    try:
        # 获取恒生指数数据
        symbol = "^HSI"
        ticker = yf.Ticker(symbol)
        
        # 获取从1990年到现在的数据
        end_date = datetime.now()
        start_date = datetime(1990, 1, 1)
        
        print(f"📅 获取日期范围: {start_date.date()} 至 {end_date.date()}")
        
        # 尝试获取数据，如果失败则尝试更短的时间范围
        df = None

        # 首先尝试获取从1990年开始的完整数据
        try:
            print(f"🔄 尝试获取从1990年至今的完整数据...")
            df = ticker.history(start=start_date, end=end_date)
            if not df.empty:
                years_span = (end_date - start_date).days / 365.25
                print(f"✅ 成功获取 {years_span:.1f} 年完整数据")
            else:
                print("⚠️  获取完整数据失败，尝试分段获取...")
        except Exception as e:
            print(f"⚠️  获取完整数据失败: {e}，尝试分段获取...")

        # 如果完整获取失败，则尝试分段获取
        if df is None or df.empty:
            for years in [35, 30, 25, 20, 15, 10]:
                try:
                    temp_start = end_date - timedelta(days=years*365)
                    print(f"🔄 尝试获取最近 {years} 年数据...")
                    df = ticker.history(start=temp_start, end=end_date)
                    if not df.empty:
                        print(f"✅ 成功获取最近 {years} 年数据")
                        break
                except Exception as e:
                    print(f"⚠️  获取 {years} 年数据失败: {e}")
                    continue
        
        if df is None or df.empty:
            print("❌ 未能获取到任何数据，请检查网络连接")
            return
        
        print(f"📊 原始数据: {len(df)} 条记录")
        
        # 检查数据质量
        if not check_data_quality(df):
            print("⚠️  数据质量检查未通过，尝试清理数据...")
        
        # 清理数据
        df = clean_data(df)
        
        if df.empty:
            print("❌ 数据清理后为空，无法继续")
            return
        
        # 计算技术指标
        df = calculate_indicators_robust(df)
        
        # 最终数据质量检查
        final_nulls = df.isnull().sum().sum()
        print(f"📊 最终数据质量: {len(df)} 条记录，{final_nulls} 个空值")
        
        # 将数据写入数据库
        if insert_data_robust(df):
            print(f"\n🎉 数据获取和存储成功完成！")
            print("📊 数据摘要：")
            print(f"   • 时间范围：{df.index.min().date()} 至 {df.index.max().date()}")
            print(f"   • 总记录数：{len(df)}")
            
            # 显示最新数据
            latest = df.iloc[-1]
            print(f"\n📈 最新数据 ({latest.name.date()}):")
            print(f"   • 收盘价：{latest['Close']:.2f}")
            print(f"   • X值：{latest['x_value']:.3f}")
            print(f"   • Y值：{latest['y_value']:.3f}")
            print(f"   • E值：{latest['e_value']:.3f}")
            
            # 显示数据分布
            print(f"\n📊 数据分布:")
            print(f"   • Y值范围: {df['y_value'].min():.3f} - {df['y_value'].max():.3f}")
            print(f"   • X值范围: {df['x_value'].min():.3f} - {df['x_value'].max():.3f}")
            print(f"   • E值范围: {df['e_value'].min():.3f} - {df['e_value'].max():.3f}")
        else:
            print("❌ 数据存储失败")
        
    except Exception as e:
        print(f"❌ 出现错误：{str(e)}")

if __name__ == "__main__":
    print("📈 恒生50指数数据获取工具 - 修复版本 (1990-至今)")
    print("="*70)
    print("🔧 修复功能:")
    print("   • 数据质量检查")
    print("   • 空值处理")
    print("   • 异常数据清理")
    print("   • 稳健的指标计算")
    print("   • 错误重试机制")
    print("📅 数据范围: 1990年1月1日 至 现在")
    print("="*70)
    
    fetch_hsi50_data_robust()
