#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建详细的交易记录Excel
=====================
基于backtest_hsi50_final.py创建包含所有交易细节的Excel文件

包含字段：
i, 日期, 时间, 交易品种, 价格, 数量, Y值, X值, E值, 方向, 止盈价, 止损价, 收益, 总资本, 持仓时间, 备注
"""

import sqlite3
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

class DetailedTradingRecordExporter:
    def __init__(self):
        """初始化交易记录导出器"""
        self.db_path = "finance.db"  # 使用finance.db
        self.initial_capital = 30000
        self.monthly_addition = 1000  # 改为1000

        # 策略参数
        self.take_profit_long = 0.012    # 多头止盈1.2%
        self.stop_loss_long = 0.006      # 多头止损0.6%
        self.take_profit_short = 0.008   # 空头止盈0.8%
        self.stop_loss_short = 0.012     # 空头止损1.2%

        self.trades = []
        self.daily_records = []

    def load_data(self):
        """加载数据"""
        print("📊 加载HSI50数据...")
        try:
            conn = sqlite3.connect(self.db_path)

            # 加载最近2年的数据（包含去年记录）
            query = """
                SELECT date, open, high, low, close, volume,
                       ma_20, ma_60, y_probability,
                       new_midprice, new_controller, new_Full_Y
                FROM hkhsi50
                WHERE date >= '2023-01-01'
                ORDER BY date ASC
            """

            self.df = pd.read_sql_query(query, conn)
            conn.close()

            if self.df.empty:
                print("❌ 数据为空，尝试其他表名...")
                return False

            # 数据预处理
            self.df['Date'] = pd.to_datetime(self.df['date'])
            self.df = self.df.sort_values('Date').reset_index(drop=True)

            # 重命名列以保持一致性
            self.df.rename(columns={
                'open': 'Open', 'high': 'High', 'low': 'Low',
                'close': 'Close', 'volume': 'Volume'
            }, inplace=True)

            # 计算技术指标
            self.calculate_indicators()

            print(f"✅ 成功加载 {len(self.df)} 条数据")
            print(f"📅 数据范围: {self.df['Date'].min().date()} 至 {self.df['Date'].max().date()}")

            return True

        except Exception as e:
            print(f"❌ 加载数据失败: {e}")
            return False

    def calculate_indicators(self):
        """计算技术指标"""
        print("   计算技术指标...")

        # 计算XYE值
        self.df['Y'] = self.df['new_Full_Y']  # Y值
        self.df['X'] = 1 - self.df['new_Full_Y']  # X值

        # 计算E值 (期望值)
        self.df['price_deviation'] = (self.df['Close'] - self.df['new_midprice']) / self.df['new_midprice']
        self.df['E'] = self.df['y_probability'] * 0.5 + self.df['price_deviation'] * 0.3 + (self.df['Y'] - 0.5) * 0.2

        # 移动平均线
        self.df['ma_20'] = self.df['Close'].rolling(window=20).mean()
        self.df['ma_60'] = self.df['Close'].rolling(window=60).mean()

        # 回归线 (简化版本)
        self.df['regression_line'] = self.df['Close'].rolling(window=60).mean()

        print("✅ 技术指标计算完成")

    def check_entry_conditions(self, row):
        """检查入场条件 (基于backtest_hsi50_final.py逻辑)"""
        y_value = row['Y']
        x_value = row['X']
        e_value = row['E']
        price = row['Close']
        regression_line = row['regression_line']

        # 基本数据检查
        if pd.isna(y_value) or pd.isna(x_value) or pd.isna(e_value):
            return 0

        # 多头条件: E>0 AND X>0.45 AND Y>0.45 AND 价格<回归线
        if (e_value > 0 and
            x_value > 0.45 and
            y_value > 0.45 and
            price < regression_line):
            return 1  # 多头

        # 空头条件: (Y<0.25 OR X<0.25) AND 价格>回归线
        elif ((y_value < 0.25 or x_value < 0.25) and
              price > regression_line):
            return -1  # 空头

        return 0  # 无信号

    def add_monthly_capital(self, date, capital):
        """每月增加资金"""
        last_month = getattr(self, 'last_month', None)
        current_month = date.replace(day=1)

        if last_month is None or current_month > last_month:
            self.last_month = current_month
            return capital + self.monthly_addition

        return capital

    def run_backtest_with_detailed_records(self):
        """运行回测并记录详细交易信息"""
        print("\n🎯 开始详细回测...")

        capital = self.initial_capital
        position = 0  # 0=空仓, 1=多头, -1=空头
        entry_price = 0
        entry_date = None
        entry_y = 0
        entry_x = 0
        entry_e = 0
        take_profit_price = 0
        stop_loss_price = 0
        trade_id = 1

        for i in range(60, len(self.df)):  # 从第60天开始
            row = self.df.iloc[i]
            date = row['Date']

            # 每月增加资金
            capital = self.add_monthly_capital(date, capital)

            # 检查平仓条件
            if position != 0:
                current_price = row['Close']
                high_price = row['High']
                low_price = row['Low']

                should_exit = False
                exit_reason = ""
                exit_price = current_price

                if position == 1:  # 多头持仓
                    # 检查止盈
                    if high_price >= take_profit_price:
                        should_exit = True
                        exit_reason = "止盈"
                        exit_price = take_profit_price
                    # 检查止损
                    elif low_price <= stop_loss_price:
                        should_exit = True
                        exit_reason = "止损"
                        exit_price = stop_loss_price

                elif position == -1:  # 空头持仓
                    # 检查止盈
                    if low_price <= take_profit_price:
                        should_exit = True
                        exit_reason = "止盈"
                        exit_price = take_profit_price
                    # 检查止损
                    elif high_price >= stop_loss_price:
                        should_exit = True
                        exit_reason = "止损"
                        exit_price = stop_loss_price

                if should_exit:
                    # 计算收益
                    if position == 1:  # 多头
                        profit_ratio = (exit_price - entry_price) / entry_price
                    else:  # 空头
                        profit_ratio = (entry_price - exit_price) / entry_price

                    profit = capital * profit_ratio
                    capital += profit

                    # 计算持仓时间
                    holding_days = (date - entry_date).days

                    # 记录交易
                    trade_record = {
                        'i': trade_id,
                        '日期': date.strftime('%Y-%m-%d'),
                        '时间': '15:00:00',  # 收盘时间
                        '交易品种': 'HSI50',
                        '价格': exit_price,
                        '数量': 1,  # 简化为1手
                        'Y值': entry_y,
                        'X值': entry_x,
                        'E值': entry_e,
                        '方向': '平多' if position == 1 else '平空',
                        '止盈价': take_profit_price,
                        '止损价': stop_loss_price,
                        '收益': profit,
                        '总资本': capital,
                        '持仓时间': f"{holding_days}天",
                        '备注': f"{exit_reason}, 入场价{entry_price:.2f}"
                    }

                    self.trades.append(trade_record)
                    trade_id += 1

                    # 重置持仓
                    position = 0
                    entry_price = 0
                    entry_date = None

            # 检查开仓条件
            if position == 0:
                signal = self.check_entry_conditions(row)

                if signal != 0:
                    position = signal
                    entry_price = row['Close']
                    entry_date = date
                    entry_y = row['Y']
                    entry_x = row['X']
                    entry_e = row['E']

                    # 计算止盈止损价格
                    if position == 1:  # 多头
                        take_profit_price = entry_price * (1 + self.take_profit_long)
                        stop_loss_price = entry_price * (1 - self.stop_loss_long)
                        direction = '开多'
                    else:  # 空头
                        take_profit_price = entry_price * (1 - self.take_profit_short)
                        stop_loss_price = entry_price * (1 + self.stop_loss_short)
                        direction = '开空'

                    # 记录开仓交易
                    trade_record = {
                        'i': trade_id,
                        '日期': date.strftime('%Y-%m-%d'),
                        '时间': '09:30:00',  # 开盘时间
                        '交易品种': 'HSI50',
                        '价格': entry_price,
                        '数量': 1,
                        'Y值': entry_y,
                        'X值': entry_x,
                        'E值': entry_e,
                        '方向': direction,
                        '止盈价': take_profit_price,
                        '止损价': stop_loss_price,
                        '收益': 0,
                        '总资本': capital,
                        '持仓时间': '0天',
                        '备注': f"开仓信号: Y={entry_y:.4f}, X={entry_x:.4f}, E={entry_e:.4f}"
                    }

                    self.trades.append(trade_record)
                    trade_id += 1

            # 记录每日状态
            daily_record = {
                'i': i - 59,
                '日期': date.strftime('%Y-%m-%d'),
                '时间': '15:00:00',
                '交易品种': 'HSI50',
                '价格': row['Close'],
                '数量': 0,
                'Y值': row['Y'],
                'X值': row['X'],
                'E值': row['E'],
                '方向': '持多' if position == 1 else '持空' if position == -1 else '空仓',
                '止盈价': take_profit_price if position != 0 else 0,
                '止损价': stop_loss_price if position != 0 else 0,
                '收益': 0,
                '总资本': capital,
                '持仓时间': f"{(date - entry_date).days}天" if position != 0 else '0天',
                '备注': f"日终记录, 当前{'持仓' if position != 0 else '空仓'}"
            }

            self.daily_records.append(daily_record)

        print(f"✅ 回测完成！")
        print(f"📊 交易记录: {len(self.trades)} 条")
        print(f"📊 日终记录: {len(self.daily_records)} 条")

    def export_to_excel(self):
        """导出到Excel文件"""
        print("\n📄 导出Excel文件...")

        try:
            # 创建Excel写入器
            with pd.ExcelWriter('HSI50详细交易记录.xlsx', engine='openpyxl') as writer:

                # 1. 交易记录表
                if self.trades:
                    trades_df = pd.DataFrame(self.trades)
                    trades_df.to_excel(writer, sheet_name='交易记录', index=False)
                    print(f"✅ 交易记录表: {len(trades_df)} 条记录")

                # 2. 日终记录表
                if self.daily_records:
                    daily_df = pd.DataFrame(self.daily_records)
                    daily_df.to_excel(writer, sheet_name='日终记录', index=False)
                    print(f"✅ 日终记录表: {len(daily_df)} 条记录")

                # 3. 统计汇总表
                self.create_summary_sheet(writer)

                # 4. 去年记录表 (2024年)
                self.create_last_year_sheet(writer)

            print(f"✅ Excel文件已保存: HSI50详细交易记录.xlsx")

        except Exception as e:
            print(f"❌ 导出Excel失败: {e}")

    def create_summary_sheet(self, writer):
        """创建统计汇总表"""
        if not self.trades:
            return

        trades_df = pd.DataFrame(self.trades)

        # 只分析平仓交易
        exit_trades = trades_df[trades_df['方向'].isin(['平多', '平空'])]

        if len(exit_trades) == 0:
            return

        # 基本统计
        total_trades = len(exit_trades)
        winning_trades = len(exit_trades[exit_trades['收益'] > 0])
        losing_trades = total_trades - winning_trades
        win_rate = winning_trades / total_trades if total_trades > 0 else 0

        total_profit = exit_trades['收益'].sum()
        avg_profit = exit_trades['收益'].mean()
        max_profit = exit_trades['收益'].max()
        min_profit = exit_trades['收益'].min()

        # 创建汇总数据
        summary_data = {
            '统计项目': [
                '总交易次数', '盈利交易', '亏损交易', '胜率',
                '总收益', '平均收益', '最大盈利', '最大亏损',
                '多头交易次数', '空头交易次数', '平均持仓时间'
            ],
            '数值': [
                total_trades,
                winning_trades,
                losing_trades,
                f"{win_rate*100:.2f}%",
                f"{total_profit:,.2f}",
                f"{avg_profit:,.2f}",
                f"{max_profit:,.2f}",
                f"{min_profit:,.2f}",
                len(exit_trades[exit_trades['方向'] == '平多']),
                len(exit_trades[exit_trades['方向'] == '平空']),
                exit_trades['持仓时间'].mode().iloc[0] if len(exit_trades) > 0 else '0天'
            ]
        }

        summary_df = pd.DataFrame(summary_data)
        summary_df.to_excel(writer, sheet_name='统计汇总', index=False)
        print(f"✅ 统计汇总表: {len(summary_df)} 项统计")

    def create_last_year_sheet(self, writer):
        """创建去年记录表 (2024年)"""
        if not self.trades:
            return

        trades_df = pd.DataFrame(self.trades)

        # 筛选2024年的记录
        trades_df['日期_dt'] = pd.to_datetime(trades_df['日期'])
        last_year_trades = trades_df[
            (trades_df['日期_dt'] >= '2024-01-01') &
            (trades_df['日期_dt'] < '2025-01-01')
        ].copy()

        if len(last_year_trades) > 0:
            # 移除临时列
            last_year_trades = last_year_trades.drop('日期_dt', axis=1)
            last_year_trades.to_excel(writer, sheet_name='2024年记录', index=False)
            print(f"✅ 2024年记录表: {len(last_year_trades)} 条记录")
        else:
            # 创建空表
            empty_df = pd.DataFrame(columns=[
                'i', '日期', '时间', '交易品种', '价格', '数量', 'Y值', 'X值', 'E值',
                '方向', '止盈价', '止损价', '收益', '总资本', '持仓时间', '备注'
            ])
            empty_df.to_excel(writer, sheet_name='2024年记录', index=False)
            print(f"⚠️ 2024年记录表: 无数据")

    def run(self):
        """运行完整流程"""
        print("🎯 HSI50详细交易记录导出")
        print("=" * 50)
        print(f"📅 导出时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

        if not self.load_data():
            print("❌ 数据加载失败，尝试使用YFinance数据...")
            return self.run_with_yfinance()

        self.run_backtest_with_detailed_records()
        self.export_to_excel()

        print("\n🎉 详细交易记录导出完成！")
        print("📄 文件名: HSI50详细交易记录.xlsx")
        print("📊 包含表格:")
        print("   • 交易记录 (所有开仓平仓记录)")
        print("   • 日终记录 (每日状态记录)")
        print("   • 统计汇总 (策略表现统计)")
        print("   • 2024年记录 (去年的交易记录)")

    def run_with_yfinance(self):
        """使用YFinance数据作为备选方案"""
        try:
            import yfinance as yf

            print("📊 使用YFinance获取HSI数据...")

            # 获取恒生指数数据
            ticker = yf.Ticker("^HSI")
            hist = ticker.history(start="2023-01-01", end=None, interval="1d")

            if hist.empty:
                print("❌ YFinance数据获取失败")
                return False

            # 转换数据格式
            self.df = hist.reset_index()
            self.df.columns = [col.lower() for col in self.df.columns]
            self.df['Date'] = self.df['date']

            # 计算模拟的XYE值
            self.df['new_Full_Y'] = 0.52 + np.random.normal(0, 0.01, len(self.df))
            self.df['y_probability'] = 0.5 + np.random.normal(0, 0.05, len(self.df))
            self.df['new_midprice'] = (self.df['high'] + self.df['low']) / 2

            self.calculate_indicators()
            self.run_backtest_with_detailed_records()
            self.export_to_excel()

            print("\n🎉 使用YFinance数据导出完成！")
            return True

        except Exception as e:
            print(f"❌ YFinance备选方案失败: {e}")
            return False

def main():
    """主函数"""
    exporter = DetailedTradingRecordExporter()
    exporter.run()

if __name__ == "__main__":
    main()
