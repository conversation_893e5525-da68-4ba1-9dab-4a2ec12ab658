#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
使用平衡赔率的持仓不动策略
========================

平衡赔率设置：
- 买涨: 止盈+2%, 止损-1% (2:1赔率)
- 买跌: 止盈+2%, 止损-1% (2:1赔率)

关键改进：
1. 恢复成功的持仓不动逻辑
2. 使用更有利的2:1赔率
3. 减少过度交易
4. 只在必要时换仓

作者: Cosmoon NG
日期: 2025年7月
"""

import pymysql
from datetime import datetime

def backtest_balanced_ratio_strategy():
    """使用平衡赔率的持仓不动策略"""
    db_config = {
        'host': 'localhost',
        'port': 3306,
        'database': 'finance',
        'user': 'root',
        'password': '12345678',
        'charset': 'utf8mb4'
    }
    
    try:
        connection = pymysql.connect(**db_config)
        cursor = connection.cursor()
        
        print("🎯 使用平衡赔率的持仓不动策略")
        print("="*80)
        print("📊 平衡赔率设置:")
        print("   • 买涨: 止盈+2%, 止损-1% (2:1赔率)")
        print("   • 买跌: 止盈+2%, 止损-1% (2:1赔率)")
        print("🔑 关键改进:")
        print("   • 恢复成功的持仓不动逻辑")
        print("   • 减少过度交易")
        print("   • 只在策略方向完全相反时才换仓")
        print("   • 其他情况保持持仓不动")
        print("="*80)
        
        # 1. 重新设置profit和loss价格 (统一2:1赔率)
        print("\n1️⃣ 设置统一的2:1赔率...")
        
        # 高值盈利区买涨: profit=止盈价(+2%), loss=止损价(-1%)
        cursor.execute("""
            UPDATE test 
            SET `profit价格` = close * 1.02,
                `loss价格` = close * 0.99
            WHERE `控制系数` > 0.4 AND `资金流比例` > 0.4
        """)
        high_profit_rows = cursor.rowcount
        
        # 强亏损区买跌: profit=止盈价(-2%), loss=止损价(+1%)
        cursor.execute("""
            UPDATE test 
            SET `profit价格` = close * 0.98,
                `loss价格` = close * 1.01
            WHERE (`控制系数` < 0.25 OR `资金流比例` < 0.25) 
              AND NOT (`控制系数` > 0.333 AND `控制系数` < 0.4)
        """)
        strong_loss_rows = cursor.rowcount
        
        # 其他区域买跌: profit=止盈价(-2%), loss=止损价(+1%)
        cursor.execute("""
            UPDATE test 
            SET `profit价格` = close * 0.98,
                `loss价格` = close * 1.01
            WHERE NOT (`控制系数` > 0.4 AND `资金流比例` > 0.4) 
              AND NOT (`控制系数` > 0.333 AND `控制系数` < 0.4) 
              AND NOT (`控制系数` < 0.25 OR `资金流比例` < 0.25)
        """)
        other_rows = cursor.rowcount
        
        # 控股商控制区观望: profit=0, loss=0
        cursor.execute("""
            UPDATE test 
            SET `profit价格` = 0,
                `loss价格` = 0
            WHERE `控制系数` > 0.333 AND `控制系数` < 0.4
        """)
        control_rows = cursor.rowcount
        
        print(f"✅ 统一2:1赔率设置完成:")
        print(f"   • 高值盈利区: {high_profit_rows}条 (买涨: +2%/-1%)")
        print(f"   • 强亏损区: {strong_loss_rows}条 (买跌: -2%/+1%)")
        print(f"   • 其他区域: {other_rows}条 (买跌: -2%/+1%)")
        print(f"   • 控股商控制区: {control_rows}条 (观望: 0/0)")
        
        connection.commit()
        
        # 2. 获取所有记录
        cursor.execute("""
            SELECT 交易序号, 开仓日期, close, `profit价格`, `loss价格`, `控制系数`, `资金流比例`,
                   CASE 
                       WHEN `控制系数` > 0.4 AND `资金流比例` > 0.4 THEN '高值盈利区'
                       WHEN `控制系数` > 0.333 AND `控制系数` < 0.4 THEN '控股商控制区'
                       WHEN (`控制系数` < 0.25 OR `资金流比例` < 0.25) AND NOT (`控制系数` > 0.333 AND `控制系数` < 0.4) THEN '强亏损区'
                       ELSE '其他区域'
                   END AS 策略区域
            FROM test 
            ORDER BY 交易序号
        """)
        
        records = cursor.fetchall()
        
        # 3. 执行平衡赔率的持仓不动回测
        print("\n2️⃣ 执行平衡赔率的持仓不动回测...")
        
        initial_capital = 30000
        current_capital = initial_capital
        
        # 持仓状态跟踪
        position_type = 'cash'  # 'cash', 'long', 'short'
        position_shares = 0
        position_entry_price = 0
        position_profit_price = 0
        position_loss_price = 0
        
        # 交易记录
        trades = []
        daily_positions = []
        
        # 统计变量
        total_trades = 0
        hold_periods = 0
        position_changes = 0
        take_profit_count = 0
        stop_loss_count = 0
        normal_close_count = 0
        
        print(f"\n📊 平衡赔率回测详细过程 (前30条):")
        print("-" * 170)
        print(f"{'序号':<4} {'策略区域':<12} {'价格':<8} {'当前仓位':<12} {'profit价格':<10} {'loss价格':<9} "
              f"{'决策':<15} {'新仓位':<12} {'盈亏':<8} {'总资金':<10}")
        print("-" * 170)
        
        # 4. 逐日回测
        for i, record in enumerate(records):
            trade_id, date, price, profit_price, loss_price, y_val, x_val, zone = record
            price = float(price)
            profit_price = float(profit_price) if profit_price else 0
            loss_price = float(loss_price) if loss_price else 0
            
            # 检查当前持仓是否触发止盈止损
            position_action = 'none'
            position_pnl = 0
            
            if position_type != 'cash' and position_profit_price > 0 and position_loss_price > 0:
                if position_type == 'long':
                    # 多仓检查止盈止损
                    if price >= position_profit_price:
                        position_action = 'take_profit'
                        position_pnl = (position_profit_price - position_entry_price) * position_shares
                    elif price <= position_loss_price:
                        position_action = 'stop_loss'
                        position_pnl = (position_loss_price - position_entry_price) * position_shares
                
                elif position_type == 'short':
                    # 空仓检查止盈止损
                    if price <= position_profit_price:
                        position_action = 'take_profit'
                        position_pnl = (position_entry_price - position_profit_price) * position_shares
                    elif price >= position_loss_price:
                        position_action = 'stop_loss'
                        position_pnl = (position_entry_price - position_loss_price) * position_shares
            
            # 处理止盈止损
            if position_action != 'none':
                transaction_cost = position_shares * position_entry_price * 0.0025
                net_pnl = position_pnl - transaction_cost
                current_capital += net_pnl
                
                if position_action == 'take_profit':
                    take_profit_count += 1
                else:
                    stop_loss_count += 1
                
                trades.append({
                    'date': date,
                    'trade_id': trade_id,
                    'zone': zone,
                    'action': f'平仓_{position_action}',
                    'price': price,
                    'shares': position_shares,
                    'pnl': net_pnl,
                    'total_capital': current_capital
                })
                
                position_type = 'cash'
                position_shares = 0
                total_trades += 1
            
            # 确定策略决策 (改进版：减少换仓)
            action, reason = determine_balanced_action(position_type, zone)
            
            # 执行决策
            capital_change = 0
            new_position_type = position_type
            new_position_shares = position_shares
            trade_shares = 0
            
            if action == 'hold':
                hold_periods += 1
            
            elif action == 'buy_long' and position_type != 'long':
                # 只有当前不是多仓时才买涨
                if position_type == 'cash':
                    # 从现金买涨
                    trade_shares = 100
                    cost = trade_shares * price
                    transaction_cost = cost * 0.0025
                    
                    if current_capital >= cost + transaction_cost:
                        current_capital -= (cost + transaction_cost)
                        capital_change = -(cost + transaction_cost)
                        
                        new_position_type = 'long'
                        new_position_shares = trade_shares
                        position_entry_price = price
                        position_profit_price = profit_price
                        position_loss_price = loss_price
                        
                        total_trades += 1
                        position_changes += 1
                
                elif position_type == 'short':
                    # 从空仓转多仓
                    close_pnl = (position_entry_price - price) * position_shares
                    close_cost = position_shares * position_entry_price * 0.0025
                    
                    # 买多仓
                    trade_shares = 100
                    open_cost = trade_shares * price
                    open_transaction_cost = open_cost * 0.0025
                    
                    total_cost = close_cost + open_cost + open_transaction_cost
                    current_capital += close_pnl - total_cost
                    capital_change = close_pnl - total_cost
                    
                    new_position_type = 'long'
                    new_position_shares = trade_shares
                    position_entry_price = price
                    position_profit_price = profit_price
                    position_loss_price = loss_price
                    
                    total_trades += 2
                    position_changes += 1
            
            elif action == 'buy_short' and position_type != 'short':
                # 只有当前不是空仓时才买跌
                if position_type == 'cash':
                    # 从现金做空
                    trade_shares = 100
                    transaction_cost = trade_shares * price * 0.0025
                    
                    current_capital -= transaction_cost
                    capital_change = -transaction_cost
                    
                    new_position_type = 'short'
                    new_position_shares = trade_shares
                    position_entry_price = price
                    position_profit_price = profit_price
                    position_loss_price = loss_price
                    
                    total_trades += 1
                    position_changes += 1
                
                elif position_type == 'long':
                    # 从多仓转空仓
                    close_pnl = (price - position_entry_price) * position_shares
                    close_cost = position_shares * position_entry_price * 0.0025
                    
                    # 做空
                    trade_shares = 100
                    open_transaction_cost = trade_shares * price * 0.0025
                    
                    total_cost = close_cost + open_transaction_cost
                    current_capital += close_pnl - total_cost
                    capital_change = close_pnl - total_cost
                    
                    new_position_type = 'short'
                    new_position_shares = trade_shares
                    position_entry_price = price
                    position_profit_price = profit_price
                    position_loss_price = loss_price
                    
                    total_trades += 2
                    position_changes += 1
            
            # 记录交易
            if action != 'hold' and capital_change != 0:
                trades.append({
                    'date': date,
                    'trade_id': trade_id,
                    'zone': zone,
                    'action': action,
                    'price': price,
                    'shares': trade_shares,
                    'pnl': capital_change,
                    'total_capital': current_capital
                })
            
            # 更新持仓状态
            position_type = new_position_type
            position_shares = new_position_shares
            
            # 记录每日状态
            daily_positions.append({
                'date': date,
                'zone': zone,
                'price': price,
                'position_type': position_type,
                'action': action
            })
            
            # 显示前30条记录
            if i < 30:
                position_display = f"{position_type}({position_shares})" if position_type != 'cash' else 'cash'
                new_position_display = f"{new_position_type}({new_position_shares})" if new_position_type != 'cash' else 'cash'
                
                print(f"{trade_id:<4} {zone:<12} {price:<8.2f} {position_display:<12} {profit_price:<10.2f} {loss_price:<9.2f} "
                      f"{action:<15} {new_position_display:<12} {capital_change:<8.0f} {current_capital:<10.0f}")
        
        print("\n" + "="*100)
        
        # 5. 最终平仓
        if position_type != 'cash':
            final_price = float(records[-1][2])
            if position_type == 'long':
                final_pnl = (final_price - position_entry_price) * position_shares
            else:  # short
                final_pnl = (position_entry_price - final_price) * position_shares
            
            final_cost = position_shares * position_entry_price * 0.0025
            net_final_pnl = final_pnl - final_cost
            current_capital += net_final_pnl
            normal_close_count += 1
            
            print(f"📊 最终平仓: {position_type}仓位，盈亏{net_final_pnl:+.0f}港币")
        
        # 6. 回测结果统计
        print(f"\n📈 平衡赔率策略回测结果:")
        print(f"   • 初始资金: {initial_capital:,}港币")
        print(f"   • 最终资金: {current_capital:,.0f}港币")
        print(f"   • 总收益: {current_capital - initial_capital:+,.0f}港币")
        print(f"   • 总收益率: {(current_capital/initial_capital-1)*100:+.2f}%")
        
        print(f"\n📊 交易行为统计:")
        print(f"   • 总记录数: {len(records)}")
        print(f"   • 实际交易次数: {total_trades}")
        print(f"   • 持仓不动次数: {hold_periods}")
        print(f"   • 仓位变化次数: {position_changes}")
        print(f"   • 止盈次数: {take_profit_count}")
        print(f"   • 止损次数: {stop_loss_count}")
        print(f"   • 到期平仓: {normal_close_count}")
        print(f"   • 持仓不动比例: {hold_periods/len(records)*100:.1f}%")
        
        total_closes = take_profit_count + stop_loss_count + normal_close_count
        if total_closes > 0:
            win_rate = take_profit_count / total_closes
            print(f"   • 胜率: {win_rate*100:.1f}%")
        
        # 7. 验证2:1赔率效果
        print(f"\n🔍 2:1赔率效果验证:")
        if take_profit_count > 0 and stop_loss_count > 0:
            avg_profit = sum([t['pnl'] for t in trades if 'take_profit' in t['action']]) / take_profit_count
            avg_loss = sum([t['pnl'] for t in trades if 'stop_loss' in t['action']]) / stop_loss_count
            actual_ratio = abs(avg_profit / avg_loss) if avg_loss != 0 else 0
            
            print(f"   • 平均止盈: {avg_profit:+.0f}港币")
            print(f"   • 平均止损: {avg_loss:+.0f}港币")
            print(f"   • 实际赔率: {actual_ratio:.1f}:1")
            print(f"   • 目标赔率: 2:1")
        
        # 8. 按策略区域分析
        print(f"\n📊 按策略区域分析:")
        print("-" * 80)
        
        zones = ['高值盈利区', '强亏损区', '其他区域', '控股商控制区']
        
        for zone_name in zones:
            zone_records = [r for r in daily_positions if r['zone'] == zone_name]
            if zone_records:
                zone_count = len(zone_records)
                zone_holds = len([r for r in zone_records if r['action'] == 'hold'])
                zone_trades = zone_count - zone_holds
                
                print(f"• {zone_name}:")
                print(f"  - 出现次数: {zone_count}")
                print(f"  - 持仓不动: {zone_holds}次 ({zone_holds/zone_count*100:.1f}%)")
                print(f"  - 交易操作: {zone_trades}次 ({zone_trades/zone_count*100:.1f}%)")
        
        connection.close()
        print(f"\n🎉 平衡赔率策略回测完成!")
        
    except Exception as e:
        print(f"❌ 平衡赔率策略回测失败: {e}")

def determine_balanced_action(current_position, zone):
    """
    改进的决策逻辑：减少不必要的换仓
    """
    
    if zone == '高值盈利区':
        if current_position == 'cash':
            return 'buy_long', '空仓买涨'
        elif current_position == 'short':
            return 'buy_long', '空仓转多仓'  # 只有方向相反才换仓
        else:  # long
            return 'hold', '保持多仓'
    
    elif zone == '控股商控制区':
        return 'hold', '控制区持仓不动'
    
    elif zone == '强亏损区':
        if current_position == 'cash':
            return 'buy_short', '空仓买跌'
        elif current_position == 'long':
            return 'buy_short', '多仓转空仓'  # 只有方向相反才换仓
        else:  # short
            return 'hold', '保持空仓'
    
    else:  # 其他区域
        if current_position == 'cash':
            return 'buy_short', '其他区域买跌'
        elif current_position == 'long':
            return 'buy_short', '多仓转空仓'
        else:  # short
            return 'hold', '保持空仓'

if __name__ == "__main__":
    backtest_balanced_ratio_strategy()
