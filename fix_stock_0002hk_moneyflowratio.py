#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
修复stock_0002_hk表缺少MoneyFlowRatio字段的问题
============================================

1. 检查现有表结构
2. 添加缺失的MoneyFlowRatio字段
3. 重新计算并更新所有技术指标
4. 确保数据完整性

作者: Cosmoon NG
日期: 2025年7月24日
"""

import yfinance as yf
import pandas as pd
import numpy as np
import mysql.connector
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

class Stock0002HKMoneyFlowRatioFixer:
    def __init__(self):
        """初始化修复器"""
        self.symbol = "0002.HK"
        self.db_config = {
            'host': 'localhost',
            'user': 'root',
            'password': '12345678',
            'database': 'finance',
            'charset': 'utf8mb4',
            'autocommit': True
        }
        self.connection = None
        self.cursor = None
        
    def connect_database(self):
        """连接数据库"""
        try:
            print(f"🔗 连接MySQL数据库...")
            self.connection = mysql.connector.connect(**self.db_config)
            self.cursor = self.connection.cursor()
            print(f"✅ 成功连接到数据库: {self.db_config['host']}/{self.db_config['database']}")
            return True
        except mysql.connector.Error as e:
            print(f"❌ 数据库连接失败: {e}")
            return False
    
    def check_current_structure(self):
        """检查当前表结构"""
        try:
            print(f"\n🔍 检查stock_0002_hk表结构...")
            
            self.cursor.execute("DESCRIBE stock_0002_hk")
            columns = self.cursor.fetchall()
            
            self.existing_columns = {}
            print("📋 当前表结构:")
            for col in columns:
                print(f"   {col[0]} - {col[1]}")
                self.existing_columns[col[0]] = col[1]
            
            # 检查是否有MoneyFlowRatio字段
            has_money_flow_ratio = 'MoneyFlowRatio' in self.existing_columns
            print(f"\n🔍 MoneyFlowRatio字段: {'✅ 存在' if has_money_flow_ratio else '❌ 缺失'}")
            
            return has_money_flow_ratio
            
        except mysql.connector.Error as e:
            print(f"❌ 检查表结构失败: {e}")
            return False
    
    def add_missing_columns(self):
        """添加缺失的字段"""
        try:
            print(f"\n🔧 添加缺失的字段...")
            
            # 需要添加的字段列表
            columns_to_add = [
                ("MoneyFlowRatio", "DECIMAL(8,6) DEFAULT NULL COMMENT '资金流比率'"),
                ("MFI", "DECIMAL(8,4) DEFAULT NULL COMMENT '资金流指数'"),
                ("RSI", "DECIMAL(8,4) DEFAULT NULL COMMENT '相对强弱指数'"),
                ("MA20", "DECIMAL(10,4) DEFAULT NULL COMMENT '20日移动平均'"),
                ("MA60", "DECIMAL(10,4) DEFAULT NULL COMMENT '60日移动平均'"),
                ("TypicalPrice", "DECIMAL(10,4) DEFAULT NULL COMMENT '典型价格'"),
                ("MoneyFlow", "DECIMAL(20,4) DEFAULT NULL COMMENT '资金流'"),
                ("PriceMomentum", "DECIMAL(8,6) DEFAULT NULL COMMENT '价格动量'")
            ]
            
            added_count = 0
            for col_name, col_definition in columns_to_add:
                if col_name not in self.existing_columns:
                    try:
                        alter_sql = f"ALTER TABLE stock_0002_hk ADD COLUMN {col_name} {col_definition}"
                        self.cursor.execute(alter_sql)
                        print(f"   ✅ 添加字段: {col_name}")
                        added_count += 1
                    except mysql.connector.Error as e:
                        if "Duplicate column name" in str(e):
                            print(f"   ⚠️ 字段已存在: {col_name}")
                        else:
                            print(f"   ❌ 添加字段失败 {col_name}: {e}")
                else:
                    print(f"   ✅ 字段已存在: {col_name}")
            
            print(f"   📊 新添加字段数: {added_count}")
            return True
            
        except mysql.connector.Error as e:
            print(f"❌ 添加字段失败: {e}")
            return False
    
    def fetch_and_calculate_all_data(self):
        """获取数据并计算所有技术指标"""
        print(f"\n📊 获取{self.symbol}完整历史数据...")
        
        try:
            # 获取股票数据
            ticker = yf.Ticker(self.symbol)
            end_date = datetime.now()
            start_date = end_date - timedelta(days=25*365)
            
            hist_data = ticker.history(start=start_date, end=end_date, auto_adjust=True, prepost=True)
            
            if hist_data.empty:
                hist_data = yf.download(self.symbol, start=start_date, end=end_date, progress=False)
            
            if hist_data.empty:
                print(f"   ❌ 无法获取数据")
                return False
            
            # 数据预处理
            self.data = pd.DataFrame({
                'date': hist_data.index.date,
                'open': hist_data['Open'],
                'high': hist_data['High'],
                'low': hist_data['Low'],
                'close': hist_data['Close'],
                'volume': hist_data['Volume']
            })
            
            self.data = self.data.dropna().reset_index(drop=True)
            
            print(f"   ✅ 获取 {len(self.data)} 条数据")
            print(f"   📅 范围: {self.data['date'].min()} 至 {self.data['date'].max()}")
            
            # 计算所有技术指标
            self.calculate_all_indicators()
            
            return True
            
        except Exception as e:
            print(f"   ❌ 数据获取失败: {e}")
            return False
    
    def calculate_all_indicators(self):
        """计算所有技术指标"""
        print(f"\n🧮 计算完整技术指标...")
        
        # 基础字段
        self.data['symbol'] = '0002.HK'
        self.data['name'] = '中电控股'
        self.data['market'] = 'HK'
        
        # === 1. 典型价格和资金流 ===
        self.data['TypicalPrice'] = (self.data['high'] + self.data['low'] + self.data['close']) / 3
        self.data['MoneyFlow'] = self.data['TypicalPrice'] * self.data['volume']
        self.data['price_change'] = self.data['TypicalPrice'].diff()
        
        # === 2. MFI计算 ===
        print("   计算MFI和MoneyFlowRatio...")
        
        # 分离正负资金流
        positive_mf = np.where(self.data['price_change'] > 0, self.data['MoneyFlow'], 0)
        negative_mf = np.where(self.data['price_change'] < 0, self.data['MoneyFlow'], 0)
        
        # 14日滚动和
        positive_mf_14 = pd.Series(positive_mf).rolling(14).sum()
        negative_mf_14 = pd.Series(negative_mf).rolling(14).sum()
        
        # MoneyFlowRatio
        self.data['MoneyFlowRatio'] = positive_mf_14 / (negative_mf_14 + 1e-10)
        
        # MFI
        self.data['MFI'] = 100 - (100 / (1 + self.data['MoneyFlowRatio']))
        
        # inflow_ratio (归一化)
        total_mf_14 = positive_mf_14 + negative_mf_14
        self.data['inflow_ratio'] = positive_mf_14 / (total_mf_14 + 1e-10)
        self.data['inflow_ratio'] = self.data['inflow_ratio'].fillna(0.5).clip(0, 1)
        
        # === 3. RSI计算 ===
        print("   计算RSI...")
        delta = self.data['close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
        rs = gain / loss
        self.data['RSI'] = 100 - (100 / (1 + rs))
        self.data['RSI'] = self.data['RSI'].fillna(50)
        
        # === 4. 移动平均线 ===
        print("   计算移动平均线...")
        self.data['MA20'] = self.data['close'].rolling(20).mean()
        self.data['MA60'] = self.data['close'].rolling(60).mean()
        
        # === 5. 价格动量 ===
        self.data['PriceMomentum'] = self.data['close'].pct_change(10).fillna(0)
        
        # === 6. Y值计算 ===
        print("   计算Y值...")
        self.data['y_probability'] = (self.data['RSI'] / 100 + np.tanh(self.data['PriceMomentum'] * 5) + 1) / 2
        self.data['y_probability'] = np.clip(self.data['y_probability'], 0.1, 0.9)
        
        # === 7. 中位价格和控制系数 ===
        print("   计算控制系数...")
        self.data['midprice'] = self.data['close'].rolling(22).median()
        self.data['Controller'] = np.where(self.data['close'] > self.data['midprice'], 1, 0)
        
        # Full_Y (累积控制系数)
        cumulative_controller = self.data['Controller'].cumsum()
        row_numbers = np.arange(1, len(self.data) + 1)
        self.data['Full_Y'] = cumulative_controller / row_numbers
        self.data['i'] = row_numbers
        
        # === 8. E值 ===
        print("   计算E值...")
        self.data['E'] = (8 * self.data['inflow_ratio'] - 3) * self.data['y_probability'] - 3 * self.data['inflow_ratio'] + 1
        
        print(f"   ✅ 所有技术指标计算完成")
        print(f"   📊 MoneyFlowRatio范围: {self.data['MoneyFlowRatio'].min():.3f} - {self.data['MoneyFlowRatio'].max():.3f}")
        print(f"   📊 MFI范围: {self.data['MFI'].min():.1f} - {self.data['MFI'].max():.1f}")
        print(f"   📊 RSI范围: {self.data['RSI'].min():.1f} - {self.data['RSI'].max():.1f}")
    
    def update_all_records(self):
        """更新所有记录"""
        print(f"\n💾 更新所有记录的技术指标...")
        
        try:
            # 清空表并重新插入所有数据
            print("   🗑️ 清空现有数据...")
            self.cursor.execute("DELETE FROM stock_0002_hk")
            
            # 准备插入SQL
            insert_sql = """
            INSERT INTO stock_0002_hk (
                date, open, high, low, close, volume,
                symbol, name, market,
                y_probability, inflow_ratio, Full_Y,
                i, midprice, Controller, E,
                MoneyFlowRatio, MFI, RSI, MA20, MA60,
                TypicalPrice, MoneyFlow, PriceMomentum
            ) VALUES (
                %s, %s, %s, %s, %s, %s,
                %s, %s, %s,
                %s, %s, %s,
                %s, %s, %s, %s,
                %s, %s, %s, %s, %s,
                %s, %s, %s
            )
            """
            
            # 批量插入
            insert_count = 0
            for _, row in self.data.iterrows():
                try:
                    values = (
                        row['date'],
                        float(row['open']), float(row['high']), float(row['low']),
                        float(row['close']), int(row['volume']),
                        row['symbol'], row['name'], row['market'],
                        float(row['y_probability']), float(row['inflow_ratio']), float(row['Full_Y']),
                        int(row['i']),
                        float(row['midprice']) if pd.notna(row['midprice']) else None,
                        int(row['Controller']), float(row['E']),
                        float(row['MoneyFlowRatio']) if pd.notna(row['MoneyFlowRatio']) else None,
                        float(row['MFI']) if pd.notna(row['MFI']) else None,
                        float(row['RSI']) if pd.notna(row['RSI']) else None,
                        float(row['MA20']) if pd.notna(row['MA20']) else None,
                        float(row['MA60']) if pd.notna(row['MA60']) else None,
                        float(row['TypicalPrice']), float(row['MoneyFlow']), float(row['PriceMomentum'])
                    )
                    
                    self.cursor.execute(insert_sql, values)
                    insert_count += 1
                    
                    if insert_count % 1000 == 0:
                        print(f"   📊 已插入 {insert_count} 条记录...")
                        
                except mysql.connector.Error as e:
                    print(f"   ⚠️ 插入失败 {row['date']}: {e}")
                    continue
            
            print(f"   ✅ 成功插入 {insert_count} 条记录")
            return True
            
        except mysql.connector.Error as e:
            print(f"❌ 更新记录失败: {e}")
            return False
    
    def verify_fix(self):
        """验证修复结果"""
        print(f"\n🔍 验证修复结果...")
        
        try:
            # 检查MoneyFlowRatio字段
            self.cursor.execute("SELECT COUNT(*) FROM stock_0002_hk WHERE MoneyFlowRatio IS NOT NULL")
            mfr_count = self.cursor.fetchone()[0]
            
            self.cursor.execute("SELECT COUNT(*) FROM stock_0002_hk")
            total_count = self.cursor.fetchone()[0]
            
            print(f"   📊 总记录数: {total_count}")
            print(f"   📊 MoneyFlowRatio非空: {mfr_count} ({mfr_count/total_count*100:.1f}%)")
            
            # 显示最新记录
            self.cursor.execute("""
                SELECT date, close, MoneyFlowRatio, MFI, y_probability, inflow_ratio, E
                FROM stock_0002_hk 
                ORDER BY date DESC 
                LIMIT 5
            """)
            
            latest_records = self.cursor.fetchall()
            print(f"\n📈 最新5条记录:")
            print("日期        | 收盘  | MFR    | MFI   | Y概率 | 资金流 | E值")
            print("-" * 65)
            for record in latest_records:
                mfr = record[2] if record[2] is not None else 0.0
                mfi = record[3] if record[3] is not None else 0.0
                print(f"{record[0]} | {record[1]:5.2f} | {mfr:6.3f} | {mfi:5.1f} | {record[4]:5.3f} | {record[5]:5.3f} | {record[6]:6.3f}")
            
            return True
            
        except mysql.connector.Error as e:
            print(f"❌ 验证失败: {e}")
            return False
    
    def close_connection(self):
        """关闭连接"""
        if self.cursor:
            self.cursor.close()
        if self.connection:
            self.connection.close()
        print("🔌 数据库连接已关闭")

def main():
    """主函数"""
    print("🔧 修复stock_0002_hk表MoneyFlowRatio字段")
    print("=" * 50)
    
    fixer = Stock0002HKMoneyFlowRatioFixer()
    
    try:
        # 连接数据库
        if not fixer.connect_database():
            return
        
        # 检查当前结构
        has_mfr = fixer.check_current_structure()
        
        # 添加缺失字段
        if not fixer.add_missing_columns():
            return
        
        # 获取数据并计算指标
        if not fixer.fetch_and_calculate_all_data():
            return
        
        # 更新所有记录
        if not fixer.update_all_records():
            return
        
        # 验证结果
        if fixer.verify_fix():
            print(f"\n🎉 修复完成！")
            print(f"💡 stock_0002_hk表现在包含完整的MoneyFlowRatio等技术指标")
        
    finally:
        fixer.close_connection()

if __name__ == "__main__":
    main()
