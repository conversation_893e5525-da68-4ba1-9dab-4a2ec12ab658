#!/usr/bin/env python
# coding: utf-8 -*-
"""
获取恒生50指数数据
=================

从Yahoo Finance获取恒生50指数数据并存入MariaDB数据库
- 数据库：stock_hsi50
- 表名：hkhsi50

作者: Cosmoon NG
日期: 2025年7月
"""

import yfinance as yf
import pandas as pd
import pymysql
from datetime import datetime, timedelta
import numpy as np

# 数据库配置
db_config = {
    'host': 'localhost',
    'user': 'root',
    'password': '12345678',
    'database': 'finance',
    'charset': 'utf8mb4'
}

def create_database():
    """创建数据库和表结构"""
    try:
        conn = pymysql.connect(**db_config)
        cursor = conn.cursor()
        
        # 创建数据表
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS hkhsi50 (
            date DATE PRIMARY KEY,
            open DECIMAL(15,4),
            high DECIMAL(15,4),
            low DECIMAL(15,4),
            close DECIMAL(15,4),
            volume BIGINT,
            ma20 DECIMAL(15,4),
            ma60 DECIMAL(15,4),
            rsi DECIMAL(15,4),
            money_flow DECIMAL(15,4),
            base_x DECIMAL(15,4),
            x_value DECIMAL(15,4),
            base_y DECIMAL(15,4),
            y_value DECIMAL(15,4),
            e_value DECIMAL(15,4)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ''')
        
        conn.commit()
        print("✅ 数据库表创建/检查完成")
    except Exception as e:
        print(f"❌ 数据库操作出错: {str(e)}")
    finally:
        if conn:
            conn.close()

def calculate_indicators(df):
    """计算技术指标"""
    # 计算移动平均线
    df['ma20'] = df['Close'].rolling(window=20).mean()
    df['ma60'] = df['Close'].rolling(window=60).mean()
    
    # 计算RSI
    delta = df['Close'].diff()
    gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
    rs = gain / loss
    df['rsi'] = 100 - (100 / (1 + rs))
    
    # 计算资金流
    df['money_flow'] = df['Volume'] * (df['Close'] - df['Open']) / df['Open']
    
    # 计算基础X值（20日资金流向）
    pos_flow = df['money_flow'].where(df['money_flow'] > 0, 0).rolling(window=20).sum()
    neg_flow = (-df['money_flow'].where(df['money_flow'] < 0, 0)).rolling(window=20).sum()
    df['base_x'] = pos_flow / (pos_flow + neg_flow)
    
    # 计算最终X值
    rsi_adj = 0.3 * (df['rsi']/100 - 0.5)
    df['x_value'] = df['base_x'] + rsi_adj
    df['x_value'] = df['x_value'].clip(0.1, 0.9)
    
    # 计算基础Y值和最终Y值
    price_ma20_ratio = df['Close'] / df['ma20']
    df['base_y'] = pd.Series(index=df.index)
    mask = price_ma20_ratio >= 1
    df.loc[mask, 'base_y'] = 0.5 + 0.4 * np.tanh((price_ma20_ratio[mask] - 1) * 3)
    df.loc[~mask, 'base_y'] = 0.5 - 0.4 * np.tanh((1 - price_ma20_ratio[~mask]) * 3)
    
    # 趋势调整
    trend_adj = 0.1 * np.tanh((df['ma20'] / df['ma60'] - 1) * 2)
    
    # 成交量调整
    volume_ma20 = df['Volume'].rolling(window=20).mean()
    vol_adj = 0.05 * np.tanh((df['Volume'] / volume_ma20 - 1))
    
    df['y_value'] = (df['base_y'] + trend_adj + vol_adj).clip(0.1, 0.9)
    
    # 计算E值
    df['e_value'] = 8 * df['x_value'] * df['y_value'] - 3 * df['x_value'] - 3 * df['y_value'] + 1
    
    # 替换 NaN 为 None（MySQL 不接受 NaN）
    df = df.replace({np.nan: None})
    
    return df

def insert_data_to_mysql(df):
    """将数据插入到MariaDB数据库"""
    try:
        conn = pymysql.connect(**db_config)
        cursor = conn.cursor()
        
        # 准备插入SQL
        sql = """
        INSERT INTO hkhsi50 (
            date, open, high, low, close, volume, 
            ma20, ma60, rsi, money_flow, 
            base_x, x_value, base_y, y_value, e_value
        ) VALUES (
            %s, %s, %s, %s, %s, %s, 
            %s, %s, %s, %s, 
            %s, %s, %s, %s, %s
        )
        ON DUPLICATE KEY UPDATE
            open = VALUES(open),
            high = VALUES(high),
            low = VALUES(low),
            close = VALUES(close),
            volume = VALUES(volume),
            ma20 = VALUES(ma20),
            ma60 = VALUES(ma60),
            rsi = VALUES(rsi),
            money_flow = VALUES(money_flow),
            base_x = VALUES(base_x),
            x_value = VALUES(x_value),
            base_y = VALUES(base_y),
            y_value = VALUES(y_value),
            e_value = VALUES(e_value)
        """
        
        # 转换数据格式（确保日期是 datetime.date 对象）
        df = df.reset_index()
        df['Date'] = pd.to_datetime(df['Date']).dt.date
        
        # 批量插入数据
        batch_size = 100
        for i in range(0, len(df), batch_size):
            batch = df.iloc[i:i+batch_size]
            data = [
                (
                    row['Date'], 
                    row['Open'], row['High'], row['Low'], row['Close'], row['Volume'],
                    row['ma20'], row['ma60'], row['rsi'], row['money_flow'],
                    row['base_x'], row['x_value'], row['base_y'], row['y_value'], row['e_value']
                )
                for _, row in batch.iterrows()
            ]
            cursor.executemany(sql, data)
            conn.commit()
            print(f"✅ 已插入 {min(i+batch_size, len(df))}/{len(df)} 条记录")
            
    except Exception as e:
        print(f"❌ 数据库插入出错: {str(e)}")
    finally:
        if conn:
            conn.close()

def fetch_hsi50_data():
    """获取恒生50指数数据"""
    print("📊 开始获取恒生50指数数据...")
    
    # 创建数据库和表
    create_database()
    
    try:
        # 获取恒生50指数数据
        symbol = "^HSI"  # 恒生指数的Yahoo Finance代码
        ticker = yf.Ticker(symbol)
        
        # 获取过去25年的数据（修改这里）
        end_date = datetime.now()
        start_date = end_date - timedelta(days=25*365)  # 从5年改为25年
        
        df = ticker.history(start=start_date, end=end_date)
        
        if df.empty:
            print("❌ 未能获取到数据，请检查网络连接和股票代码")
            return
        
        # 计算技术指标
        df = calculate_indicators(df)
        
        # 将数据写入数据库
        insert_data_to_mysql(df)
        
        print(f"\n✅ 成功获取并存储了 {len(df)} 条数据")
        print("📊 数据摘要：")
        print(f"时间范围：{df.index.min().date()} 至 {df.index.max().date()}")
        print("\n最新数据：")
        latest = df.iloc[-1]
        print(f"日期：{latest.name.date()}")
        print(f"收盘价：{latest['Close']:.2f}")
        print(f"X值：{latest['x_value']:.3f}")
        print(f"Y值：{latest['y_value']:.3f}")
        print(f"E值：{latest['e_value']:.3f}")
        
    except Exception as e:
        print(f"❌ 出现错误：{str(e)}")

if __name__ == "__main__":
    print("📈 恒生50指数数据获取工具")
    print("="*50)
    
    fetch_hsi50_data()