#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
腾讯700HK 改进版25年回测系统
============================

优化交易逻辑和风险管理
修复回测中的问题

作者: Cosmoon NG
"""

import yfinance as yf
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

class ImprovedTencentBacktest:
    """改进版腾讯700HK回测系统"""
    
    def __init__(self):
        self.symbol = "0700.HK"
        self.initial_capital = 100000  # 初始资金10万港元
        self.commission_rate = 0.001   # 手续费率0.1%
        
        # 优化交易参数
        self.position_size = 0.5       # 降低仓位比例到50%
        self.take_profit = 0.05        # 提高止盈到5%
        self.stop_loss = 0.03          # 提高止损到3%
        self.min_holding_days = 5      # 最少持仓天数
        
        print(f"🎯 腾讯700HK 改进版25年回测系统")
        print(f"💰 初始资金: {self.initial_capital:,} 港元")
        print(f"📊 仓位比例: {self.position_size*100}%")
        print(f"🎯 止盈: {self.take_profit*100}%")
        print(f"🛑 止损: {self.stop_loss*100}%")

    def get_historical_data(self):
        """获取历史数据"""
        try:
            end_date = datetime.now()
            start_date = end_date - timedelta(days=25*365)
            
            print(f"\n📅 获取历史数据...")
            print(f"   开始日期: {start_date.strftime('%Y-%m-%d')}")
            print(f"   结束日期: {end_date.strftime('%Y-%m-%d')}")
            
            ticker = yf.Ticker(self.symbol)
            data = ticker.history(start=start_date, end=end_date)
            
            if data.empty:
                print("❌ 无法获取历史数据")
                return None
            
            print(f"✅ 成功获取 {len(data)} 天历史数据")
            print(f"📊 数据范围: {data.index[0].strftime('%Y-%m-%d')} 至 {data.index[-1].strftime('%Y-%m-%d')}")
            
            return data
            
        except Exception as e:
            print(f"❌ 获取数据失败: {e}")
            return None

    def calculate_indicators(self, data):
        """计算技术指标"""
        df = data.copy()
        
        print("📊 计算技术指标...")
        
        # 基础指标
        df['Returns'] = df['Close'].pct_change()
        df['TypicalPrice'] = (df['High'] + df['Low'] + df['Close']) / 3
        
        # RSI
        delta = df['Close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
        rs = gain / (loss + 1e-10)
        df['RSI'] = 100 - (100 / (1 + rs))
        
        # 移动平均线
        df['MA5'] = df['Close'].rolling(window=5).mean()
        df['MA10'] = df['Close'].rolling(window=10).mean()
        df['MA20'] = df['Close'].rolling(window=20).mean()
        df['MA50'] = df['Close'].rolling(window=50).mean()
        
        # 布林带
        df['BB_Middle'] = df['Close'].rolling(window=20).mean()
        bb_std = df['Close'].rolling(window=20).std()
        df['BB_Upper'] = df['BB_Middle'] + (bb_std * 2)
        df['BB_Lower'] = df['BB_Middle'] - (bb_std * 2)
        
        # 成交量指标
        df['Volume_MA'] = df['Volume'].rolling(window=20).mean()
        df['Volume_Ratio'] = df['Volume'] / df['Volume_MA']
        
        # 资金流指标
        df['MoneyFlow'] = df['TypicalPrice'] * df['Volume']
        df['Price_Change'] = df['TypicalPrice'].diff()
        df['PositiveMoneyFlow'] = np.where(df['Price_Change'] > 0, df['MoneyFlow'], 0)
        df['NegativeMoneyFlow'] = np.where(df['Price_Change'] < 0, df['MoneyFlow'], 0)
        
        df['PositiveMoneyFlow_14'] = df['PositiveMoneyFlow'].rolling(window=14).sum()
        df['NegativeMoneyFlow_14'] = df['NegativeMoneyFlow'].rolling(window=14).sum()
        df['MoneyFlowRatio'] = df['PositiveMoneyFlow_14'] / (df['NegativeMoneyFlow_14'] + 1e-10)
        df['MFI'] = 100 - (100 / (1 + df['MoneyFlowRatio']))
        
        return df

    def calculate_cosmoon_signals(self, df):
        """计算Cosmoon交易信号"""
        
        print("🧮 计算Cosmoon交易信号...")
        
        # 价格强度 (Y值)
        price_min = df['Close'].rolling(window=50, min_periods=20).min()
        price_max = df['Close'].rolling(window=50, min_periods=20).max()
        df['Y_Value'] = (df['Close'] - price_min) / (price_max - price_min + 1e-10)
        
        # 资金流强度 (X值)
        mfr_mean = df['MoneyFlowRatio'].rolling(window=50, min_periods=20).mean()
        mfr_std = df['MoneyFlowRatio'].rolling(window=50, min_periods=20).std()
        df['X_Value'] = (df['MoneyFlowRatio'] - mfr_mean) / (mfr_std + 1e-10)
        df['X_Value'] = (df['X_Value'] + 3) / 6
        df['X_Value'] = df['X_Value'].clip(0, 1)
        
        # 趋势强度 (Full_Y)
        df['Price_Trend'] = df['Close'].rolling(window=10, min_periods=5).apply(
            lambda x: np.polyfit(range(len(x)), x, 1)[0] if len(x) >= 5 else 0
        )
        trend_mean = df['Price_Trend'].rolling(window=50, min_periods=20).mean()
        trend_std = df['Price_Trend'].rolling(window=50, min_periods=20).std()
        df['Full_Y'] = (df['Price_Trend'] - trend_mean) / (trend_std + 1e-10)
        df['Full_Y'] = (df['Full_Y'] + 3) / 6
        df['Full_Y'] = df['Full_Y'].clip(0, 1)
        
        # MyE指标
        df['MyE'] = 8 * df['MoneyFlowRatio'] * df['Full_Y'] - 3 * df['MoneyFlowRatio'] - 3 * df['Full_Y'] + 1
        
        return df

    def generate_signals(self, df):
        """生成交易信号"""
        
        print("🎯 生成交易信号...")
        
        signals = []
        
        for i in range(len(df)):
            row = df.iloc[i]
            
            # 基本条件检查
            if i < 50:  # 需要足够的历史数据
                signals.append(0)
                continue
            
            # 获取指标
            close = row['Close']
            ma5 = row['MA5']
            ma10 = row['MA10']
            ma20 = row['MA20']
            rsi = row['RSI']
            mfi = row['MFI']
            y_val = row['Y_Value']
            x_val = row['X_Value']
            mye = row['MyE']
            volume_ratio = row['Volume_Ratio']
            
            # 检查数据有效性
            if pd.isna(mye) or pd.isna(y_val) or pd.isna(x_val) or pd.isna(rsi):
                signals.append(0)
                continue
            
            # 买入信号条件
            buy_conditions = [
                close > ma5 > ma10,  # 价格在均线之上
                rsi < 70,  # RSI不超买
                mfi < 70,  # MFI不超买
                y_val > 0.3,  # 价格强度
                x_val > 0.3,  # 资金流强度
                mye > -0.5,  # MyE不过度负值
                volume_ratio > 0.8  # 成交量支持
            ]
            
            # 强买入信号
            strong_buy_conditions = [
                close > ma5 > ma10 > ma20,  # 强势趋势
                rsi > 30 and rsi < 60,  # RSI在合理区间
                y_val > 0.6,  # 强价格强度
                x_val > 0.6,  # 强资金流
                mye > 0.5,  # 强MyE信号
                volume_ratio > 1.2  # 放量
            ]
            
            # 卖出信号条件
            sell_conditions = [
                close < ma5 < ma10,  # 价格跌破均线
                rsi > 30,  # RSI不超卖
                mfi > 30,  # MFI不超卖
                y_val < 0.7,  # 价格强度下降
                x_val < 0.7,  # 资金流减弱
                mye < 0.5  # MyE信号减弱
            ]
            
            # 强卖出信号
            strong_sell_conditions = [
                close < ma5 < ma10 < ma20,  # 弱势趋势
                rsi > 70 or rsi < 40,  # RSI超买或偏弱
                y_val < 0.4,  # 弱价格强度
                x_val < 0.4,  # 弱资金流
                mye < -0.5,  # 负MyE信号
                volume_ratio > 1.0  # 有成交量确认
            ]
            
            # 信号判断
            if sum(strong_buy_conditions) >= 5:
                signal = 2  # 强买入
            elif sum(buy_conditions) >= 5:
                signal = 1  # 买入
            elif sum(strong_sell_conditions) >= 5:
                signal = -2  # 强卖出
            elif sum(sell_conditions) >= 4:
                signal = -1  # 卖出
            else:
                signal = 0  # 观望
            
            signals.append(signal)
        
        df['TradingSignal'] = signals
        return df

    def run_backtest(self, df):
        """运行回测"""
        
        print("🔄 运行回测...")
        
        # 初始化
        cash = self.initial_capital
        position = 0  # 持仓数量
        entry_price = 0
        entry_date = None
        holding_days = 0
        
        # 记录
        trades = []
        portfolio_values = []
        
        for i in range(len(df)):
            row = df.iloc[i]
            current_date = row.name
            current_price = row['Close']
            signal = row['TradingSignal']
            
            # 计算组合价值
            if position == 0:
                portfolio_value = cash
            else:
                portfolio_value = cash + position * current_price
            
            portfolio_values.append(portfolio_value)
            
            # 更新持仓天数
            if position != 0:
                holding_days += 1
            
            # 检查平仓条件
            if position > 0:  # 持有多头
                price_change = (current_price - entry_price) / entry_price
                
                should_exit = False
                exit_reason = ""
                
                # 止盈止损
                if price_change >= self.take_profit:
                    should_exit = True
                    exit_reason = "Take Profit"
                elif price_change <= -self.stop_loss:
                    should_exit = True
                    exit_reason = "Stop Loss"
                # 信号反转且持仓时间足够
                elif signal <= -1 and holding_days >= self.min_holding_days:
                    should_exit = True
                    exit_reason = "Signal Reversal"
                
                if should_exit:
                    # 卖出
                    sell_value = position * current_price * (1 - self.commission_rate)
                    cash += sell_value
                    
                    pnl = sell_value - (position * entry_price * (1 + self.commission_rate))
                    
                    trades.append({
                        'entry_date': entry_date,
                        'exit_date': current_date,
                        'direction': 'Long',
                        'entry_price': entry_price,
                        'exit_price': current_price,
                        'shares': position,
                        'pnl': pnl,
                        'return': price_change,
                        'holding_days': holding_days,
                        'reason': exit_reason
                    })
                    
                    position = 0
                    holding_days = 0
                    entry_price = 0
                    entry_date = None
            
            # 开仓信号
            if position == 0 and signal > 0 and cash > 1000:  # 确保有足够资金
                # 计算买入数量
                max_investment = cash * self.position_size
                shares_to_buy = int(max_investment / (current_price * (1 + self.commission_rate)))
                
                if shares_to_buy > 0:
                    cost = shares_to_buy * current_price * (1 + self.commission_rate)
                    if cost <= cash:
                        cash -= cost
                        position = shares_to_buy
                        entry_price = current_price
                        entry_date = current_date
                        holding_days = 0
        
        # 最后平仓
        if position > 0:
            final_value = position * df['Close'].iloc[-1] * (1 - self.commission_rate)
            cash += final_value
            
            pnl = final_value - (position * entry_price * (1 + self.commission_rate))
            price_change = (df['Close'].iloc[-1] - entry_price) / entry_price
            
            trades.append({
                'entry_date': entry_date,
                'exit_date': df.index[-1],
                'direction': 'Long',
                'entry_price': entry_price,
                'exit_price': df['Close'].iloc[-1],
                'shares': position,
                'pnl': pnl,
                'return': price_change,
                'holding_days': holding_days,
                'reason': 'Final Exit'
            })
        
        df['PortfolioValue'] = portfolio_values
        
        return df, trades, cash

    def analyze_results(self, df, trades, final_cash):
        """分析回测结果"""
        
        print("\n📊 改进版回测结果分析")
        print("=" * 60)
        
        # 基本统计
        total_return = (final_cash - self.initial_capital) / self.initial_capital * 100
        total_trades = len(trades)
        
        if total_trades > 0:
            winning_trades = len([t for t in trades if t['pnl'] > 0])
            losing_trades = len([t for t in trades if t['pnl'] <= 0])
            win_rate = winning_trades / total_trades * 100
            
            total_profit = sum([t['pnl'] for t in trades if t['pnl'] > 0])
            total_loss = sum([t['pnl'] for t in trades if t['pnl'] <= 0])
            
            avg_win = total_profit / winning_trades if winning_trades > 0 else 0
            avg_loss = total_loss / losing_trades if losing_trades > 0 else 0
            
            profit_factor = abs(total_profit / total_loss) if total_loss != 0 else float('inf')
            
            avg_holding_days = np.mean([t['holding_days'] for t in trades])
        else:
            win_rate = 0
            avg_win = 0
            avg_loss = 0
            profit_factor = 0
            avg_holding_days = 0
        
        # 年化收益率
        years = len(df) / 252
        annual_return = (final_cash / self.initial_capital) ** (1/years) - 1 if years > 0 else 0
        
        # 最大回撤
        portfolio_values = df['PortfolioValue'].values
        peak = np.maximum.accumulate(portfolio_values)
        drawdown = (portfolio_values - peak) / peak
        max_drawdown = np.min(drawdown) * 100
        
        # 买入持有策略
        buy_hold_return = (df['Close'].iloc[-1] - df['Close'].iloc[0]) / df['Close'].iloc[0] * 100
        
        print(f"💰 最终资金: {final_cash:,.0f} 港元")
        print(f"💰 总收益率: {total_return:.2f}%")
        print(f"📈 年化收益率: {annual_return*100:.2f}%")
        print(f"📊 买入持有收益率: {buy_hold_return:.2f}%")
        print(f"🎯 超额收益: {total_return - buy_hold_return:.2f}%")
        print(f"📉 最大回撤: {max_drawdown:.2f}%")
        print(f"🔢 总交易次数: {total_trades}")
        print(f"✅ 胜率: {win_rate:.1f}%")
        print(f"💎 盈亏比: {profit_factor:.2f}")
        print(f"💰 平均盈利: {avg_win:,.0f} 港元")
        print(f"💸 平均亏损: {avg_loss:,.0f} 港元")
        print(f"📅 平均持仓天数: {avg_holding_days:.1f} 天")
        
        # 保存结果
        if trades:
            trades_df = pd.DataFrame(trades)
            trades_df.to_csv(f'腾讯700HK_改进版回测交易记录_{datetime.now().strftime("%Y%m%d")}.csv', index=False)
            print(f"\n💾 交易记录已保存")
        
        df.to_csv(f'腾讯700HK_改进版回测数据_{datetime.now().strftime("%Y%m%d")}.csv')
        print(f"💾 回测数据已保存")

def main():
    """主函数"""
    backtest = ImprovedTencentBacktest()
    
    # 获取历史数据
    data = backtest.get_historical_data()
    if data is None:
        return
    
    # 计算指标和信号
    df = backtest.calculate_indicators(data)
    df = backtest.calculate_cosmoon_signals(df)
    df = backtest.generate_signals(df)
    
    # 运行回测
    df, trades, final_cash = backtest.run_backtest(df)
    
    # 分析结果
    backtest.analyze_results(df, trades, final_cash)
    
    print(f"\n🎯 腾讯700HK 改进版25年回测完成！")

if __name__ == "__main__":
    main()
