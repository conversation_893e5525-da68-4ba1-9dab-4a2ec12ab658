#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
修复版Full_Y和Controller字段更新脚本 - 无Unicode字符
"""

import mysql.connector
from datetime import datetime
import sys

def update_full_y_controller():
    """更新Full_Y和Controller字段"""
    print("Full_Y和Controller字段更新工具")
    print("=" * 50)
    print("说明: 数据库中有两套XYE系统:")
    print("  第一套: X_Value, Y_Value, E_Value (基于MFI和价格位置)")
    print("  第二套: X(MFI/100), Y(Full_Y), E (基于回归线K值)")
    print(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

    try:
        # 数据库连接配置
        config = {
            'host': 'localhost',
            'port': 3306,
            'user': 'root',
            'password': '12345678',
            'database': 'finance',
            'charset': 'utf8mb4'
        }

        print("连接数据库...")
        connection = mysql.connector.connect(**config)
        cursor = connection.cursor()
        print("数据库连接成功")

        # 获取更新前的状态
        cursor.execute("""
            SELECT COUNT(*) as total_records,
                   COUNT(CASE WHEN Full_Y IS NOT NULL THEN 1 END) as full_y_count,
                   COUNT(CASE WHEN Controller IS NOT NULL THEN 1 END) as controller_count
            FROM eab_0023hk
        """)

        before_stats = cursor.fetchone()
        total_records, full_y_before, controller_before = before_stats

        print(f"更新前状态:")
        print(f"  总记录数: {total_records}")
        print(f"  Full_Y非空: {full_y_before}")
        print(f"  Controller非空: {controller_before}")

        # 计算Full_Y = 每行之前Controller=1的累积数目 / 每行之前Controller的累积总数
        print("计算Full_Y (每行的累积Controller=1比例)...")

        try:
            # 首先调用sp_averagelineV3更新回归线
            print("调用sp_averagelineV3更新回归线...")
            cursor.callproc('sp_averagelineV3', ['eab_0023hk'])

            # 清理存储过程结果 - 修复弃用警告
            try:
                # 使用更安全的方式清理结果
                while cursor.nextset():
                    pass
            except Exception:
                # 如果没有更多结果集，会抛出异常，这是正常的
                pass

            print("回归线更新完成")

            # 获取所有记录，按日期排序
            cursor.execute("""
                SELECT id, Date, Controller
                FROM eab_0023hk
                WHERE Controller IS NOT NULL
                ORDER BY Date ASC, id ASC
            """)

            records = cursor.fetchall()
            print(f"处理 {len(records)} 条记录的累积Full_Y计算...")

            # 逐行计算累积Full_Y
            cumulative_controller_1 = 0
            cumulative_total = 0
            updates = []

            for i, (record_id, date, controller) in enumerate(records):
                # 计算到当前行之前的累积统计
                if i == 0:
                    # 第一行，没有之前的数据
                    full_y = 0.5  # 默认值
                else:
                    # 基于之前所有记录计算
                    full_y = cumulative_controller_1 / cumulative_total if cumulative_total > 0 else 0.5

                # 更新累积计数
                cumulative_total += 1
                if controller == 1:
                    cumulative_controller_1 += 1

                # 记录更新
                updates.append((full_y, record_id))

                # 显示前10条的计算过程
                if i < 10:
                    print(f"   记录{i+1} ({date}): Controller={controller}, "
                          f"累积1={cumulative_controller_1-1 if controller==1 else cumulative_controller_1}/"
                          f"累积总数={cumulative_total-1}, Full_Y={full_y:.6f}")

            # 批量更新Full_Y
            print(f"批量更新Full_Y字段...")
            update_sql = "UPDATE eab_0023hk SET Full_Y = %s WHERE id = %s"
            cursor.executemany(update_sql, updates)

            full_y_updated = len(updates)
            print(f"Full_Y字段更新: {full_y_updated} 条记录 (累积Controller=1比例)")

            # 显示最新几条记录的Full_Y
            cursor.execute("""
                SELECT Date, Controller, Full_Y
                FROM eab_0023hk
                ORDER BY Date DESC
                LIMIT 5
            """)

            latest_records = cursor.fetchall()
            print(f"最新5条记录的Full_Y:")
            for date, controller, full_y in latest_records:
                print(f"   {date}: Controller={controller}, Full_Y={full_y:.6f}")

            # 更新第二套系统的E字段 (基于MFI/100和Full_Y比例)
            update_e_sql = """
            UPDATE eab_0023hk
            SET E = CASE
                WHEN Full_Y IS NOT NULL AND MFI IS NOT NULL THEN
                    -- 第二套E公式: 基于Full_Y比例
                    (8 * (MFI/100) - 3) * Full_Y - 3 * (MFI/100) + 1
                ELSE NULL
            END
            WHERE Full_Y IS NOT NULL AND MFI IS NOT NULL
            """

            cursor.execute(update_e_sql)
            e_updated = cursor.rowcount
            print(f"E字段更新: {e_updated} 条记录 (基于Full_Y比例)")

        except Exception as e:
            print(f"Full_Y计算失败: {e}")
            # 备用方法：设置默认值
            print("使用备用方法设置Full_Y...")
            update_full_y_sql = """
            UPDATE eab_0023hk
            SET Full_Y = 0.5
            WHERE Full_Y IS NULL
            """
            cursor.execute(update_full_y_sql)
            full_y_updated = cursor.rowcount
            print(f"Full_Y字段更新: {full_y_updated} 条记录 (备用方法: 默认值0.5)")

        # 更新Controller字段 (基于Full_Y的K值逻辑)
        print("更新Controller字段...")

        # Controller逻辑：基于Full_Y (K值) 和价格偏离度
        update_controller_sql = """
        UPDATE eab_0023hk
        SET Controller = CASE
            WHEN Full_Y > 0.6 THEN 1
            WHEN Full_Y < 0.4 THEN -1
            WHEN Full_Y >= 0.45 AND Full_Y <= 0.55 THEN 0
            WHEN Close > Midprice * 1.02 THEN -1  -- 价格高于回归线2%，卖出信号
            WHEN Close < Midprice * 0.98 THEN 1   -- 价格低于回归线2%，买入信号
            ELSE 0
        END
        WHERE Full_Y IS NOT NULL AND Midprice IS NOT NULL
        """

        cursor.execute(update_controller_sql)
        controller_updated = cursor.rowcount
        print(f"Controller字段更新: {controller_updated} 条记录 (基于K值和回归线偏离)")

        connection.commit()
        print(f"字段更新完成")

        # 获取更新后的状态
        cursor.execute("""
            SELECT COUNT(*) as total_records,
                   COUNT(CASE WHEN Full_Y IS NOT NULL THEN 1 END) as full_y_count,
                   COUNT(CASE WHEN Controller IS NOT NULL THEN 1 END) as controller_count
            FROM eab_0023hk
        """)

        after_stats = cursor.fetchone()
        total_records, full_y_after, controller_after = after_stats

        print(f"\n更新后状态:")
        print(f"  总记录数: {total_records}")
        print(f"  Full_Y非空: {full_y_after}")
        print(f"  Controller非空: {controller_after}")

        print(f"\n更新统计:")
        print(f"  Full_Y新增: {full_y_after - full_y_before}")
        print(f"  Controller新增: {controller_after - controller_before}")

        # 显示最新记录 (检查E和E_Value字段是否都存在)
        try:
            cursor.execute("SHOW COLUMNS FROM eab_0023hk LIKE 'E%'")
            e_columns = [row[0] for row in cursor.fetchall()]
        except Exception as e:
            print(f"获取列信息失败: {e}")
            e_columns = ['E_Value', 'E']  # 使用默认列名

        # 构建查询语句
        base_columns = "Date, Close, Y_Value, Full_Y, Controller"
        e_columns_str = ""
        if 'E_Value' in e_columns:
            e_columns_str += ", E_Value"
        if 'E' in e_columns:
            e_columns_str += ", E"
        if 'Midprice' in [col[0] for col in cursor.execute("SHOW COLUMNS FROM eab_0023hk LIKE 'Midprice'") or []]:
            e_columns_str += ", Midprice"

        query = f"SELECT {base_columns}{e_columns_str} FROM eab_0023hk ORDER BY Date DESC LIMIT 5"
        cursor.execute(query)

        latest_records = cursor.fetchall()
        print(f"\n最新5条记录:")

        # 动态构建表头
        header = "日期        收盘价    Y_Value   Full_Y    Controller"
        if 'E_Value' in e_columns:
            header += "  E_Value "
        if 'E' in e_columns:
            header += "     E    "
        if 'Midprice' in e_columns_str:
            header += "  回归线值"

        print(header)
        print("-" * len(header))

        for record in latest_records:
            date, close, y_value, full_y, controller = record[:5]
            full_y_str = f"{full_y:7.4f}" if full_y is not None else "   NULL"
            controller_str = f"{controller:3d}" if controller is not None else "NULL"

            line = f"{date}  {close:7.2f}   {y_value:7.4f}   {full_y_str}   {controller_str:>4}"

            # 添加E相关字段
            col_idx = 5
            if 'E_Value' in e_columns:
                e_value = record[col_idx] if len(record) > col_idx else None
                e_value_str = f"{e_value:8.4f}" if e_value is not None else "    NULL"
                line += f"  {e_value_str}"
                col_idx += 1

            if 'E' in e_columns:
                e = record[col_idx] if len(record) > col_idx else None
                e_str = f"{e:8.4f}" if e is not None else "    NULL"
                line += f"  {e_str}"
                col_idx += 1

            if 'Midprice' in e_columns_str:
                midprice = record[col_idx] if len(record) > col_idx else None
                midprice_str = f"{midprice:8.2f}" if midprice is not None else "    NULL"
                line += f"  {midprice_str}"

            print(line)

        # 检查最新记录的完整性 - 两套XYE系统
        latest_record = latest_records[0] if latest_records else None
        if latest_record:
            date, close, y_value, full_y, controller = latest_record[:5]
            print(f"\n两套XYE系统完整性检查 ({date}):")

            # 获取完整的数据
            cursor.execute("""
                SELECT MFI, E_Value, E
                FROM eab_0023hk
                WHERE Date = %s
            """, (date,))

            extra_data = cursor.fetchone()
            if extra_data:
                mfi, e_value, e = extra_data
                x_value = mfi / 100 if mfi else None

                print(f"  第一套系统 (X_Value, Y_Value, E_Value):")
                print(f"    X_Value (MFI/100): {'OK' if x_value is not None else 'ERROR'} {x_value:.4f if x_value else 'NULL'}")
                print(f"    Y_Value: {'OK' if y_value is not None else 'ERROR'} {y_value}")
                print(f"    E_Value: {'OK' if e_value is not None else 'ERROR'} {e_value}")

                print(f"  第二套系统 (X=MFI/100, Y=Full_Y, E):")
                print(f"    X (MFI/100): {'OK' if x_value is not None else 'ERROR'} {x_value:.4f if x_value else 'NULL'}")
                print(f"    Y (Full_Y): {'OK' if full_y is not None else 'ERROR'} {full_y}")
                print(f"    E: {'OK' if e is not None else 'ERROR'} {e}")
                print(f"    Controller: {'OK' if controller is not None else 'ERROR'} {controller}")

        cursor.close()
        connection.close()

        print(f"\nFull_Y和Controller更新完成！")
        print(f"完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

        return True

    except mysql.connector.Error as e:
        print(f"数据库错误: {e}")
        return False
    except Exception as e:
        print(f"系统错误: {e}")
        return False

def main():
    """主函数"""
    success = update_full_y_controller()
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
