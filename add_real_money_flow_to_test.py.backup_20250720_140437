#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
为test表补全真实资金流数据
========================

功能：
1. 连接到MySQL数据库的test表
2. 添加真实资金流相关列
3. 基于开仓日期从HK00023数据获取真实资金流数据
4. 更新test表的资金流信息

作者: Cosmoon NG
日期: 2025年7月
"""

import pymysql
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

class TestTableRealMoneyFlowUpdater:
    def __init__(self):
        """初始化test表真实资金流更新器"""
        self.db_config = {
            'host': '************',
            'port': 3306,
            'database': 'finance',
            'user': 'root',
            'password': '',
            'charset': 'utf8mb4'
        }
        self.connection = None
        
    def connect_database(self):
        """连接数据库"""
        try:
            self.connection = pymysql.connect(**self.db_config)
            print(f"✅ 成功连接MySQL数据库: {self.db_config['host']}")
            return True
        except Exception as e:
            print(f"❌ 连接数据库失败: {e}")
            return False
    
    def check_test_table(self):
        """检查test表结构"""
        try:
            cursor = self.connection.cursor()
            
            # 检查test表是否存在
            cursor.execute("SHOW TABLES LIKE 'test'")
            if not cursor.fetchone():
                print("❌ test表不存在")
                return False
            
            # 检查表结构
            cursor.execute("DESCRIBE test")
            columns = cursor.fetchall()
            
            print("📊 test表当前结构:")
            for col in columns:
                print(f"   • {col[0]} ({col[1]})")
            
            # 检查数据量
            cursor.execute("SELECT COUNT(*) FROM test")
            count = cursor.fetchone()[0]
            print(f"\n📈 test表记录数: {count:,}")
            
            # 检查开仓日期范围
            cursor.execute("SELECT MIN(开仓日期), MAX(开仓日期) FROM test")
            date_range = cursor.fetchone()
            print(f"📅 开仓日期范围: {date_range[0]} 至 {date_range[1]}")
            
            return True
            
        except Exception as e:
            print(f"❌ 检查test表失败: {e}")
            return False
    
    def add_real_money_flow_columns(self):
        """为test表添加真实资金流列"""
        try:
            cursor = self.connection.cursor()
            
            # 检查是否已有真实资金流列
            cursor.execute("SHOW COLUMNS FROM test LIKE '%流%'")
            existing_flow_columns = cursor.fetchall()
            
            if existing_flow_columns:
                print("⚠️ test表已存在资金流相关列:")
                for col in existing_flow_columns:
                    print(f"   • {col[0]}")
                
                # 删除现有资金流列
                flow_columns = ['真实流入', '真实流出', '净资金流', '资金流比例', '资金流强度']
                for col in flow_columns:
                    try:
                        cursor.execute(f"ALTER TABLE test DROP COLUMN `{col}`")
                    except:
                        pass
            
            # 添加真实资金流列
            alter_statements = [
                "ALTER TABLE test ADD COLUMN `真实流入` DECIMAL(15,2) DEFAULT 0 COMMENT '开仓日真实资金流入'",
                "ALTER TABLE test ADD COLUMN `真实流出` DECIMAL(15,2) DEFAULT 0 COMMENT '开仓日真实资金流出'",
                "ALTER TABLE test ADD COLUMN `净资金流` DECIMAL(15,2) DEFAULT 0 COMMENT '开仓日净资金流'",
                "ALTER TABLE test ADD COLUMN `资金流比例` DECIMAL(8,6) DEFAULT 0.5 COMMENT '开仓日资金流入比例'",
                "ALTER TABLE test ADD COLUMN `资金流强度` DECIMAL(8,4) DEFAULT 1.0 COMMENT '开仓日资金流强度'"
            ]
            
            for statement in alter_statements:
                cursor.execute(statement)
            
            self.connection.commit()
            print("✅ 成功添加真实资金流列到test表")
            return True
            
        except Exception as e:
            print(f"❌ 添加真实资金流列失败: {e}")
            return False
    
    def get_hk00023_money_flow_data(self):
        """获取HK00023的资金流数据"""
        try:
            cursor = self.connection.cursor()
            
            # 检查HK00023表是否存在
            cursor.execute("SHOW TABLES LIKE 'hk00023'")
            if not cursor.fetchone():
                print("❌ hk00023表不存在，无法获取真实资金流数据")
                return None
            
            # 获取HK00023的资金流数据
            cursor.execute("""
                SELECT date, close, volume, y_probability, inflow_ratio
                FROM hk00023 
                ORDER BY date
            """)
            
            hk_data = cursor.fetchall()
            if not hk_data:
                print("❌ hk00023表中没有数据")
                return None
            
            # 转换为DataFrame
            hk_df = pd.DataFrame(hk_data, columns=['date', 'close', 'volume', 'y_probability', 'inflow_ratio'])
            hk_df['date'] = pd.to_datetime(hk_df['date'])
            
            # 计算真实资金流
            self.calculate_real_money_flow(hk_df)
            
            print(f"✅ 成功获取HK00023资金流数据: {len(hk_df)} 条记录")
            return hk_df
            
        except Exception as e:
            print(f"❌ 获取HK00023资金流数据失败: {e}")
            return None
    
    def calculate_real_money_flow(self, df):
        """计算真实资金流数据"""
        print("🧮 计算HK00023真实资金流数据...")
        
        # 基于价格变化和成交量计算资金流
        df['price_change'] = df['close'].pct_change().fillna(0)
        
        # 方法1: 基于价格变化的资金流
        flow_in_1 = np.where(df['price_change'] > 0, df['volume'] * df['price_change'], 0)
        flow_out_1 = np.where(df['price_change'] < 0, df['volume'] * abs(df['price_change']), 0)
        
        # 方法2: 基于inflow_ratio的资金流
        total_flow_base = df['volume'] * df['close']
        flow_in_2 = total_flow_base * df['inflow_ratio']
        flow_out_2 = total_flow_base * (1 - df['inflow_ratio'])
        
        # 综合计算
        df['real_flow_in'] = (flow_in_1 * 0.4 + flow_in_2 * 0.6)
        df['real_flow_out'] = (flow_out_1 * 0.4 + flow_out_2 * 0.6)
        df['net_flow'] = df['real_flow_in'] - df['real_flow_out']
        df['flow_ratio'] = df['inflow_ratio']  # 直接使用已有的inflow_ratio
        
        # 计算资金流强度
        volume_ma = df['volume'].rolling(window=20, min_periods=1).mean()
        df['flow_intensity'] = (df['real_flow_in'] + df['real_flow_out']) / (volume_ma * df['close'])
        df['flow_intensity'] = df['flow_intensity'].fillna(1.0)
        
        # 处理异常值
        for col in ['real_flow_in', 'real_flow_out', 'net_flow', 'flow_ratio', 'flow_intensity']:
            df[col] = df[col].replace([np.inf, -np.inf], 0)
            df[col] = df[col].fillna(0)
        
        print("✅ 真实资金流计算完成")
    
    def update_test_table_with_real_flow(self, hk_df):
        """更新test表的真实资金流数据"""
        try:
            print("💾 更新test表真实资金流数据...")
            
            cursor = self.connection.cursor()
            
            # 获取test表的交易记录
            cursor.execute("""
                SELECT 交易序号, 开仓日期, 股票代码, 交易方向, 策略区域
                FROM test 
                ORDER BY 交易序号
            """)
            
            test_records = cursor.fetchall()
            
            # 创建日期到资金流的映射
            hk_df['date_str'] = hk_df['date'].dt.strftime('%Y-%m-%d')
            flow_dict = {}
            for _, row in hk_df.iterrows():
                flow_dict[row['date_str']] = {
                    'real_flow_in': row['real_flow_in'],
                    'real_flow_out': row['real_flow_out'],
                    'net_flow': row['net_flow'],
                    'flow_ratio': row['flow_ratio'],
                    'flow_intensity': row['flow_intensity']
                }
            
            # 更新每条交易记录
            update_count = 0
            for record in test_records:
                trade_id, open_date, stock_code, direction, zone = record
                
                # 只处理HK00023的记录
                if stock_code != 'HK00023':
                    continue
                
                # 查找对应日期的资金流数据
                if open_date in flow_dict:
                    flow_data = flow_dict[open_date]
                    
                    # 更新数据库
                    update_sql = """
                        UPDATE test 
                        SET `真实流入` = %s,
                            `真实流出` = %s,
                            `净资金流` = %s,
                            `资金流比例` = %s,
                            `资金流强度` = %s
                        WHERE 交易序号 = %s
                    """
                    
                    cursor.execute(update_sql, (
                        float(flow_data['real_flow_in']),
                        float(flow_data['real_flow_out']),
                        float(flow_data['net_flow']),
                        float(flow_data['flow_ratio']),
                        float(flow_data['flow_intensity']),
                        trade_id
                    ))
                    
                    update_count += 1
            
            self.connection.commit()
            print(f"✅ 成功更新 {update_count} 条记录的真实资金流数据")
            return True
            
        except Exception as e:
            print(f"❌ 更新test表真实资金流失败: {e}")
            return False
    
    def verify_test_table_update(self):
        """验证test表更新结果"""
        try:
            cursor = self.connection.cursor()
            
            # 检查更新后的数据
            cursor.execute("""
                SELECT 交易序号, 开仓日期, 交易方向, 策略区域, 净利润,
                       `真实流入`, `真实流出`, `净资金流`, `资金流比例`, `资金流强度`
                FROM test 
                WHERE `真实流入` > 0 OR `真实流出` > 0
                ORDER BY 交易序号 
                LIMIT 20
            """)
            
            updated_data = cursor.fetchall()
            
            print("\n📊 test表前20条真实资金流数据:")
            print("="*150)
            print(f"{'序号':<4} {'开仓日期':<12} {'方向':<4} {'策略区域':<12} {'净利润':<8} {'真实流入':<12} {'真实流出':<12} {'净资金流':<12} {'流比例':<8} {'强度':<8}")
            print("-" * 150)
            
            for row in updated_data:
                trade_id, date, direction, zone, profit, flow_in, flow_out, net_flow, ratio, intensity = row
                print(f"{trade_id:<4} {date:<12} {direction:<4} {zone:<12} {int(profit):<8} "
                      f"{float(flow_in):<12,.0f} {float(flow_out):<12,.0f} {float(net_flow):<12,.0f} "
                      f"{float(ratio):<8.3f} {float(intensity):<8.2f}")
            
            # 统计信息
            cursor.execute("""
                SELECT 
                    COUNT(*) as total_records,
                    COUNT(CASE WHEN `真实流入` > 0 OR `真实流出` > 0 THEN 1 END) as flow_records,
                    AVG(`真实流入`) as avg_in,
                    AVG(`真实流出`) as avg_out,
                    AVG(`资金流比例`) as avg_ratio
                FROM test
            """)
            
            stats = cursor.fetchone()
            total, flow_records, avg_in, avg_out, avg_ratio = stats
            
            print(f"\n📈 test表真实资金流数据统计:")
            print(f"   • 总记录数: {total:,}")
            print(f"   • 有真实资金流数据的记录: {flow_records:,}")
            print(f"   • 数据完整率: {flow_records/total*100:.1f}%")
            if avg_in:
                print(f"   • 平均真实流入: {float(avg_in):,.0f}")
                print(f"   • 平均真实流出: {float(avg_out):,.0f}")
                print(f"   • 平均资金流比例: {float(avg_ratio):.3f}")
            
            # 按策略区域分析
            cursor.execute("""
                SELECT 策略区域, 
                       COUNT(*) as count,
                       AVG(`资金流比例`) as avg_ratio,
                       AVG(`真实流入`) as avg_in,
                       AVG(`真实流出`) as avg_out,
                       SUM(净利润) as total_profit
                FROM test 
                WHERE `真实流入` > 0 OR `真实流出` > 0
                GROUP BY 策略区域
            """)
            
            zone_stats = cursor.fetchall()
            
            print(f"\n📊 按策略区域的真实资金流分析:")
            print("-" * 80)
            for zone, count, avg_ratio, avg_in, avg_out, total_profit in zone_stats:
                print(f"• {zone}: {count}次, 平均流比例{float(avg_ratio):.3f}, "
                      f"平均流入{float(avg_in):,.0f}, 平均流出{float(avg_out):,.0f}, "
                      f"总盈亏{int(total_profit):+,}港币")
            
            return True
            
        except Exception as e:
            print(f"❌ 验证test表更新失败: {e}")
            return False
    
    def close_connection(self):
        """关闭数据库连接"""
        if self.connection:
            self.connection.close()
            print("\n🔒 数据库连接已关闭")

def main():
    """主函数"""
    print("🏦 为test表补全真实资金流数据")
    print("="*60)
    
    # 创建更新器
    updater = TestTableRealMoneyFlowUpdater()
    
    # 连接数据库
    if not updater.connect_database():
        return
    
    # 检查test表
    if not updater.check_test_table():
        updater.close_connection()
        return
    
    # 添加真实资金流列
    if not updater.add_real_money_flow_columns():
        updater.close_connection()
        return
    
    # 获取HK00023资金流数据
    hk_df = updater.get_hk00023_money_flow_data()
    if hk_df is None:
        updater.close_connection()
        return
    
    # 更新test表的真实资金流数据
    if not updater.update_test_table_with_real_flow(hk_df):
        updater.close_connection()
        return
    
    # 验证更新结果
    updater.verify_test_table_update()
    
    # 关闭连接
    updater.close_connection()
    
    print("\n🎉 test表真实资金流数据补全完成!")
    print("📊 新增列:")
    print("   • 真实流入: 开仓日真实资金流入")
    print("   • 真实流出: 开仓日真实资金流出")
    print("   • 净资金流: 开仓日净资金流")
    print("   • 资金流比例: 开仓日资金流入比例")
    print("   • 资金流强度: 开仓日资金流强度")

if __name__ == "__main__":
    main()
