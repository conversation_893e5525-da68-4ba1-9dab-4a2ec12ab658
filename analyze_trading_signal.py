#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析交易信号逻辑
==============
分析为什么Y=0.4098, X=0.5902会产生看涨信号
"""

def analyze_xy_signal():
    """分析XY分区信号逻辑"""
    
    print("🔍 交易信号分析")
    print("=" * 50)
    
    # 实际数据
    y_value = 0.409836
    x_value = 0.590164
    e_value = 0.178357
    
    print(f"📊 实际数据:")
    print(f"   Y值: {y_value:.6f}")
    print(f"   X值: {x_value:.6f}")
    print(f"   E值: {e_value:.6f}")
    print(f"   验证: Y + X = {y_value + x_value:.6f} (应该=1.0)")
    
    print(f"\n🎯 XY分区策略规则:")
    print(f"   1. 高值盈利区 (Y>0.4 AND X>0.4): 看涨")
    print(f"   2. 控股商控制区 (0.333<Y<0.4): 观望")
    print(f"   3. 强亏损区 (Y<0.25 OR X<0.25): 看跌")
    print(f"   4. 其他区域: 看跌")
    
    print(f"\n🔍 条件检查:")
    
    # 检查各个条件
    condition1_y = y_value > 0.4
    condition1_x = x_value > 0.4
    condition1 = condition1_y and condition1_x
    
    condition2 = 0.333 < y_value < 0.4
    
    condition3_y = y_value < 0.25
    condition3_x = x_value < 0.25
    condition3 = condition3_y or condition3_x
    
    print(f"   条件1 - 高值盈利区:")
    print(f"     Y > 0.4: {y_value:.6f} > 0.4 = {condition1_y} ✅")
    print(f"     X > 0.4: {x_value:.6f} > 0.4 = {condition1_x} ✅")
    print(f"     结果: {condition1_y} AND {condition1_x} = {condition1} → 看涨 ✅")
    
    print(f"\n   条件2 - 控股商控制区:")
    print(f"     0.333 < Y < 0.4: 0.333 < {y_value:.6f} < 0.4 = {condition2}")
    
    print(f"\n   条件3 - 强亏损区:")
    print(f"     Y < 0.25: {y_value:.6f} < 0.25 = {condition3_y}")
    print(f"     X < 0.25: {x_value:.6f} < 0.25 = {condition3_x}")
    print(f"     结果: {condition3_y} OR {condition3_x} = {condition3}")
    
    print(f"\n🎯 最终判断:")
    if condition1:
        print(f"   ✅ 满足条件1 (高值盈利区) → 看涨")
        print(f"   📈 策略逻辑: Y和X都>0.4，表示市场处于相对均衡的强势状态")
    elif condition2:
        print(f"   ⏸️ 满足条件2 (控股商控制区) → 观望")
    elif condition3:
        print(f"   📉 满足条件3 (强亏损区) → 看跌")
    else:
        print(f"   📉 其他区域 → 看跌")
    
    print(f"\n💡 为什么这个组合是看涨？")
    print(f"   🎯 Y=0.4098 (>0.4): 累积强势比例略高于40%")
    print(f"      - 表示历史上有40.98%的时间收盘价高于中位价")
    print(f"      - 显示市场整体偏向强势")
    
    print(f"\n   🎯 X=0.5902 (>0.4): 弱势比例为59.02%")
    print(f"      - X = 1 - Y，表示59.02%的时间收盘价低于中位价")
    print(f"      - 虽然弱势时间更多，但仍在可接受范围内")
    
    print(f"\n   🎯 关键洞察:")
    print(f"      - Y接近0.4的临界值，但仍然>0.4")
    print(f"      - X虽然较高(0.59)，但仍然>0.4")
    print(f"      - 两个条件都满足，触发高值盈利区的看涨信号")
    
    print(f"\n⚠️ 这个信号的风险:")
    print(f"   📊 Y值刚好超过0.4临界值 (0.4098)")
    print(f"   📊 X值较高 (0.5902)，显示弱势时间较多")
    print(f"   📊 E值较低 (0.1784)，期望值不高")
    print(f"   💡 这是一个边缘信号，成功概率可能不高")

def analyze_strategy_logic():
    """分析策略逻辑的合理性"""
    
    print(f"\n🧮 策略逻辑分析:")
    print(f"=" * 30)
    
    print(f"\n📊 XY分区的含义:")
    print(f"   Y值 (Full_Y): 累积强势比例")
    print(f"   - Y>0.5: 强势时间多于弱势时间")
    print(f"   - Y<0.5: 弱势时间多于强势时间")
    print(f"   - Y≈0.5: 强弱势时间基本平衡")
    
    print(f"\n   X值 (1-Full_Y): 累积弱势比例")
    print(f"   - X>0.5: 弱势时间多于强势时间")
    print(f"   - X<0.5: 强势时间多于弱势时间")
    print(f"   - X≈0.5: 强弱势时间基本平衡")
    
    print(f"\n🎯 高值盈利区 (Y>0.4, X>0.4) 的逻辑:")
    print(f"   条件: Y>0.4 AND X>0.4")
    print(f"   含义: 0.4 < Y < 0.6 (因为Y+X=1)")
    print(f"   解释: 强势比例在40%-60%之间，市场相对均衡")
    print(f"   策略: 在均衡状态下看涨，期待突破上行")
    
    print(f"\n💡 为什么在这种情况下看涨？")
    print(f"   1. 市场不是极端弱势 (Y>0.4)")
    print(f"   2. 市场不是极端强势 (X>0.4，即Y<0.6)")
    print(f"   3. 处于相对均衡状态，有上涨潜力")
    print(f"   4. 避免了极端情况，风险相对可控")

def check_boundary_cases():
    """检查边界情况"""
    
    print(f"\n🔍 边界情况分析:")
    print(f"=" * 30)
    
    test_cases = [
        (0.4000, 0.6000, "刚好达到临界值"),
        (0.3999, 0.6001, "刚好不满足条件"),
        (0.4098, 0.5902, "实际案例"),
        (0.5000, 0.5000, "完全均衡"),
        (0.4500, 0.5500, "高值盈利区中心"),
        (0.6000, 0.4000, "边界情况")
    ]
    
    print(f"   Y值    | X值    | 条件检查 | 信号")
    print(f"   " + "-" * 40)
    
    for y, x, description in test_cases:
        condition1 = (y > 0.4) and (x > 0.4)
        condition2 = 0.333 < y < 0.4
        condition3 = (y < 0.25) or (x < 0.25)
        
        if condition1:
            signal = "看涨"
        elif condition2:
            signal = "观望"
        elif condition3:
            signal = "看跌"
        else:
            signal = "看跌"
        
        check_result = "✅" if condition1 else "❌"
        print(f"   {y:.4f} | {x:.4f} | {check_result:8s} | {signal:4s} ({description})")

def main():
    """主函数"""
    analyze_xy_signal()
    analyze_strategy_logic()
    check_boundary_cases()
    
    print(f"\n🎉 总结:")
    print(f"   这条记录被标记为'看涨'是因为:")
    print(f"   ✅ Y=0.4098 > 0.4 (满足强势条件)")
    print(f"   ✅ X=0.5902 > 0.4 (满足均衡条件)")
    print(f"   ✅ 同时满足高值盈利区的两个条件")
    print(f"   📊 虽然是边缘信号，但符合策略逻辑")

if __name__ == "__main__":
    main()
