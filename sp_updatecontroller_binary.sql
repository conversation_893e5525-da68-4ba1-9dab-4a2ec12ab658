
CREATE PROCEDURE sp_updatecontroller(
    IN tablename VARCHAR(64),
    OUT result_k DECIMAL(20,6)
)
BEGIN
    DECLARE col_exists INT DEFAULT 0;
    DECLARE v_start_time DATETIME DEFAULT NOW();
    
    -- 1. 首先更新midprice (调用sp_averagelineV3)
    SELECT CONCAT('开始更新midprice - 表: ', tablename, ' 时间: ', v_start_time) AS start_message;
    CALL sp_averagelineV3(tablename);
    SELECT 'midprice更新完成' AS midprice_status;
    
    -- 2. 检查controller列是否存在
    SET @sql = CONCAT(
        'SELECT COUNT(*) INTO @col_exists FROM information_schema.COLUMNS ',
        'WHERE TABLE_SCHEMA=DATABASE() AND TABLE_NAME=''', tablename, ''' AND COLUMN_NAME=''controller'''
    );
    PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;
    SET col_exists = @col_exists;

    IF col_exists = 0 THEN
        SET @sql = CONCAT('ALTER TABLE `', tablename, '` ADD COLUMN `controller` INT DEFAULT NULL');
        PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;
        SELECT 'controller列已添加' AS controller_status;
    END IF;
    
    -- 3. 检查Full_Y列是否存在
    SET @sql = CONCAT(
        'SELECT COUNT(*) INTO @col_exists FROM information_schema.COLUMNS ',
        'WHERE TABLE_SCHEMA=DATABASE() AND TABLE_NAME=''', tablename, ''' AND COLUMN_NAME=''Full_Y'''
    );
    PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;
    SET col_exists = @col_exists;

    IF col_exists = 0 THEN
        SET @sql = CONCAT('ALTER TABLE `', tablename, '` ADD COLUMN `Full_Y` DECIMAL(20,10) DEFAULT NULL');
        PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;
        SELECT 'Full_Y列已添加' AS full_y_status;
    END IF;

    -- 4. 更新controller字段 (二元逻辑)
    SELECT '开始计算controller (二元逻辑)...' AS calc_status;
    
    -- controller: if (close - midprice > 0) then 1 else 0
    SET @sql = CONCAT(
        'UPDATE `', tablename, '` ',
        'SET controller = CASE WHEN (close - midprice) > 0 THEN 1 ELSE 0 END ',
        'WHERE midprice IS NOT NULL'
    );
    PREPARE stmt FROM @sql;
    EXECUTE stmt;
    DEALLOCATE PREPARE stmt;
    
    SELECT 'controller二元逻辑计算完成' AS controller_complete_status;

    -- 5. 计算Full_Y字段 (累积controller计数 / 行号)
    SELECT '开始计算Full_Y (累积比例)...' AS full_y_calc_status;
    
    -- Full_Y: 累积controller=1的数量 / 行号
    SET @sql = CONCAT(
        'UPDATE `', tablename, '` t1 ',
        'JOIN (',
        '  SELECT date, ',
        '    SUM(controller) OVER (ORDER BY date ASC ROWS UNBOUNDED PRECEDING) as cumulative_count, ',
        '    ROW_NUMBER() OVER (ORDER BY date ASC) as row_num ',
        '  FROM `', tablename, '` ',
        '  WHERE controller IS NOT NULL ',
        '  ORDER BY date ASC',
        ') t2 ON t1.date = t2.date ',
        'SET t1.Full_Y = t2.cumulative_count / t2.row_num'
    );
    PREPARE stmt FROM @sql;
    EXECUTE stmt;
    DEALLOCATE PREPARE stmt;
    
    SELECT 'Full_Y累积比例计算完成' AS full_y_complete_status;

    -- 6. 计算k值 (controller=1的比例)
    SET @sql = CONCAT(
        'SELECT SUM(controller) / COUNT(*) INTO @k_value FROM `', tablename, '` WHERE controller IS NOT NULL'
    );
    PREPARE stmt FROM @sql;
    EXECUTE stmt;
    DEALLOCATE PREPARE stmt;
    
    SET result_k = @k_value;
    
    -- 7. 返回统计信息
    SELECT 
        CONCAT('更新完成 - 表: ', tablename) AS completion_message,
        CONCAT('k值: ', IFNULL(result_k, 0)) AS k_value_message,
        CONCAT('耗时: ', TIMESTAMPDIFF(SECOND, v_start_time, NOW()), ' 秒') AS duration;
        
END
            