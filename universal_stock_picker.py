#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Cosmoon XYE选股神器 (Cosmoon XYE Stock Picker)
============================================

基于Cosmoon XYE策略的智能选股和回测系统
支持多种数据源：Yahoo Finance、本地数据库、CSV文件等

特点：
1. 支持任意股票代码
2. 自动获取数据或使用本地数据
3. Cosmoon XYE策略分析
4. 完整的回测系统
5. 智能选股推荐
6. 风险评估和资金管理

作者: Cosmoon NG (基于backtest_hsi50_final.py改造)
日期: 2025年7月
"""

import yfinance as yf
import sqlite3
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import matplotlib.pyplot as plt
from scipy import stats
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class CosmoonXYEStockPicker:
    """Cosmoon XYE选股神器"""

    def __init__(self, initial_capital=10000, monthly_addition=3000):
        """初始化选股系统"""
        self.initial_capital = initial_capital
        self.monthly_addition = monthly_addition

        # 策略参数
        self.take_profit_long = 0.025   # 多头止盈2.5%
        self.stop_loss_long = 0.015     # 多头止损1.5%
        self.take_profit_short = 0.015  # 空头止盈1.5%
        self.stop_loss_short = 0.025    # 空头止损2.5%

        # 选股参数
        self.min_volume = 1000000       # 最小成交量
        self.min_price = 1.0           # 最小价格
        self.max_price = 1000.0        # 最大价格

        # 当前状态
        self.position = 0
        self.current_price = 0

    def get_stock_data(self, symbol, period="2y", source="yahoo"):
        """获取股票数据"""
        print(f"\n📊 获取 {symbol} 数据...")

        try:
            if source == "yahoo":
                # 从Yahoo Finance获取数据
                stock = yf.Ticker(symbol)
                hist = stock.history(period=period)

                if hist.empty:
                    print(f"❌ 无法获取 {symbol} 的数据")
                    return None

                # 转换为标准格式
                df = pd.DataFrame({
                    'date': hist.index,
                    'open': hist['Open'],
                    'high': hist['High'],
                    'low': hist['Low'],
                    'close': hist['Close'],
                    'volume': hist['Volume']
                })

                # 获取股票信息
                info = stock.info
                company_name = info.get('longName', symbol)
                print(f"✅ 成功获取 {company_name} ({symbol}) 数据")
                print(f"   数据期间: {df['date'].min().date()} 至 {df['date'].max().date()}")
                print(f"   总计: {len(df)} 条记录")

            elif source == "database":
                # 从本地数据库获取数据
                conn = sqlite3.connect('stock_data.db')
                df = pd.read_sql(f"""
                    SELECT date, open, high, low, close, volume
                    FROM {symbol.replace('.', '_').replace('-', '_')}
                    ORDER BY date
                """, conn)
                conn.close()
                df['date'] = pd.to_datetime(df['date'])

            elif source == "csv":
                # 从CSV文件获取数据
                df = pd.read_csv(f"{symbol}.csv")
                df['date'] = pd.to_datetime(df['date'])

            else:
                raise ValueError(f"不支持的数据源: {source}")

            # 数据清理
            df = df.dropna()
            df = df.sort_values('date').reset_index(drop=True)

            return df

        except Exception as e:
            print(f"❌ 获取数据失败: {e}")
            return None

    def calculate_xye_indicators(self, df):
        """计算Cosmoon XYE指标"""
        print("📈 计算XYE技术指标...")

        df = df.copy()

        # 1. 计算Y指标 (价格在区间的位置)
        window = 20
        df['high_20'] = df['high'].rolling(window).max()
        df['low_20'] = df['low'].rolling(window).min()
        df['y_value'] = (df['close'] - df['low_20']) / (df['high_20'] - df['low_20'])
        df['y_value'] = df['y_value'].fillna(0.5).clip(0, 1)

        # 2. 计算X指标 (资金流强度)
        df['typical_price'] = (df['high'] + df['low'] + df['close']) / 3
        df['money_flow'] = df['typical_price'] * df['volume']
        df['price_change'] = df['typical_price'].diff()

        # 正负资金流
        df['positive_mf'] = np.where(df['price_change'] > 0, df['money_flow'], 0)
        df['negative_mf'] = np.where(df['price_change'] < 0, df['money_flow'], 0)

        # 14日资金流比率
        period = 14
        df['positive_mf_14'] = df['positive_mf'].rolling(period).sum()
        df['negative_mf_14'] = df['negative_mf'].rolling(period).sum()
        df['money_flow_ratio'] = df['positive_mf_14'] / (df['negative_mf_14'] + 1e-10)

        # MFI和X值
        df['mfi'] = 100 - (100 / (1 + df['money_flow_ratio']))
        df['x_value'] = df['mfi'] / 100  # 归一化到0-1

        # 3. 计算E指标 (Cosmoon公式)
        df['e_value'] = (8 * df['x_value'] - 3) * df['y_value'] - 3 * df['x_value'] + 1

        # 4. 计算回归线
        df['i'] = range(1, len(df) + 1)
        if len(df) > 60:
            slope, intercept, r_value, p_value, std_err = stats.linregress(
                df['i'], df['close']
            )
            df['regression_line'] = intercept + slope * df['i']
            df['price_position'] = (df['close'] - df['regression_line']) / df['regression_line']
        else:
            df['regression_line'] = df['close'].rolling(20).mean()
            df['price_position'] = (df['close'] - df['regression_line']) / df['regression_line']

        # 5. 其他技术指标
        df['rsi'] = self.calculate_rsi(df['close'])
        df['ma20'] = df['close'].rolling(20).mean()
        df['ma50'] = df['close'].rolling(50).mean()

        print("✅ XYE指标计算完成")
        return df

    def calculate_rsi(self, prices, period=14):
        """计算RSI指标"""
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / (loss + 1e-10)
        rsi = 100 - (100 / (1 + rs))
        return rsi

    def generate_signals(self, df):
        """生成交易信号"""
        print("🎯 生成交易信号...")

        df = df.copy()
        signals = []

        for i, row in df.iterrows():
            if i < 60:  # 需要足够的历史数据
                signals.append(0)
                continue

            # 检查数据完整性
            if (pd.isna(row['y_value']) or pd.isna(row['x_value']) or
                pd.isna(row['e_value']) or pd.isna(row['price_position'])):
                signals.append(0)
                continue

            # Cosmoon XYE策略
            # 多头信号
            if (row['e_value'] > 0 and
                row['x_value'] > 0.45 and
                row['y_value'] > 0.45 and
                row['price_position'] < 0):  # 价格低于回归线
                signals.append(1)

            # 空头信号
            elif ((row['y_value'] < 0.3 or row['x_value'] < 0.3 or
                   (row['x_value'] > 0.45 and row['y_value'] < 0.35) or
                   (row['x_value'] < 0.45 and row['y_value'] > 0.35)) and
                  row['price_position'] > 0):  # 价格高于回归线
                signals.append(-1)

            else:
                signals.append(0)

        df['signal'] = signals

        # 统计信号
        signal_counts = df['signal'].value_counts()
        print(f"📊 信号统计:")
        for signal, count in signal_counts.items():
            signal_name = "🟢 做多" if signal == 1 else "🔴 做空" if signal == -1 else "⚪ 观望"
            print(f"   {signal_name}: {count}次 ({count/len(df)*100:.1f}%)")

        return df

    def analyze_stock(self, symbol, period="2y", source="yahoo"):
        """分析单只股票"""
        print(f"\n🔍 分析股票: {symbol}")
        print("=" * 60)

        # 获取数据
        df = self.get_stock_data(symbol, period, source)
        if df is None:
            return None

        # 计算指标
        df = self.calculate_xye_indicators(df)
        df = self.generate_signals(df)

        # 当前状态分析
        latest = df.iloc[-1]

        analysis = {
            'symbol': symbol,
            'date': latest['date'],
            'price': latest['close'],
            'y_value': latest['y_value'],
            'x_value': latest['x_value'],
            'e_value': latest['e_value'],
            'signal': latest['signal'],
            'rsi': latest['rsi'],
            'price_position': latest['price_position'],
            'volume': latest['volume'],
            'data': df
        }

        # 评分系统
        score = self.calculate_stock_score(analysis)
        analysis['score'] = score

        # 显示分析结果
        self.display_analysis(analysis)

        return analysis

    def calculate_stock_score(self, analysis):
        """计算股票评分 (0-100分)"""
        score = 50  # 基础分

        # Y值评分 (0-20分)
        y_val = analysis['y_value']
        if y_val > 0.7:
            score += 20
        elif y_val > 0.5:
            score += 15
        elif y_val > 0.3:
            score += 10
        elif y_val < 0.2:
            score -= 10

        # X值评分 (0-20分)
        x_val = analysis['x_value']
        if x_val > 0.6:
            score += 20
        elif x_val > 0.4:
            score += 15
        elif x_val > 0.3:
            score += 10
        elif x_val < 0.2:
            score -= 10

        # E值评分 (0-20分)
        e_val = analysis['e_value']
        if e_val > 0.5:
            score += 20
        elif e_val > 0:
            score += 15
        elif e_val > -0.5:
            score += 5
        else:
            score -= 15

        # 信号评分 (0-20分)
        signal = analysis['signal']
        if signal == 1:
            score += 20
        elif signal == -1:
            score -= 20

        # RSI评分 (0-10分)
        rsi = analysis['rsi']
        if 30 < rsi < 70:
            score += 10
        elif rsi < 20 or rsi > 80:
            score -= 5

        # 趋势评分 (0-10分)
        price_pos = analysis['price_position']
        if -0.05 < price_pos < 0.05:
            score += 10
        elif price_pos < -0.1:
            score += 5  # 价格低于趋势，可能是机会

        return max(0, min(100, score))

    def display_analysis(self, analysis):
        """显示分析结果"""
        print(f"\n📊 {analysis['symbol']} 分析结果:")
        print(f"   日期: {analysis['date'].strftime('%Y-%m-%d')}")
        print(f"   价格: {analysis['price']:.2f}")
        print(f"   Y值: {analysis['y_value']:.4f}")
        print(f"   X值: {analysis['x_value']:.4f}")
        print(f"   E值: {analysis['e_value']:.4f}")
        print(f"   RSI: {analysis['rsi']:.1f}")
        print(f"   价格位置: {analysis['price_position']:.4f}")

        # 信号解读
        signal = analysis['signal']
        if signal == 1:
            print(f"   🟢 交易信号: 强烈买入")
        elif signal == -1:
            print(f"   🔴 交易信号: 强烈卖出")
        else:
            print(f"   ⚪ 交易信号: 观望")

        # 评分
        score = analysis['score']
        if score >= 80:
            grade = "🌟 优秀"
        elif score >= 70:
            grade = "👍 良好"
        elif score >= 60:
            grade = "👌 一般"
        elif score >= 40:
            grade = "⚠️ 较差"
        else:
            grade = "❌ 很差"

        print(f"   📈 综合评分: {score:.0f}分 ({grade})")

    def screen_stocks(self, stock_list, period="1y"):
        """批量筛选股票"""
        print(f"\n🔍 批量筛选 {len(stock_list)} 只股票")
        print("=" * 80)

        results = []

        for i, symbol in enumerate(stock_list, 1):
            print(f"\n进度: {i}/{len(stock_list)} - 分析 {symbol}")

            try:
                analysis = self.analyze_stock(symbol, period)
                if analysis:
                    results.append(analysis)
            except Exception as e:
                print(f"❌ {symbol} 分析失败: {e}")
                continue

        # 按评分排序
        results.sort(key=lambda x: x['score'], reverse=True)

        # 显示排行榜
        self.display_ranking(results)

        return results

    def display_ranking(self, results):
        """显示股票排行榜"""
        print(f"\n🏆 股票排行榜 (共{len(results)}只)")
        print("=" * 100)
        print(f"{'排名':<4} {'代码':<12} {'价格':<8} {'Y值':<8} {'X值':<8} {'E值':<8} {'信号':<6} {'评分':<6}")
        print("-" * 100)

        for i, result in enumerate(results[:20], 1):  # 显示前20名
            signal_text = "买入" if result['signal'] == 1 else "卖出" if result['signal'] == -1 else "观望"

            print(f"{i:<4} {result['symbol']:<12} {result['price']:<8.2f} "
                  f"{result['y_value']:<8.4f} {result['x_value']:<8.4f} "
                  f"{result['e_value']:<8.4f} {signal_text:<6} {result['score']:<6.0f}")

        # 推荐股票
        buy_signals = [r for r in results if r['signal'] == 1 and r['score'] >= 70]
        if buy_signals:
            print(f"\n🎯 推荐买入股票:")
            for stock in buy_signals[:5]:
                print(f"   • {stock['symbol']}: {stock['score']:.0f}分 - {stock['price']:.2f}")

    def run_backtest(self, symbol, period="2y", source="yahoo"):
        """运行回测"""
        print(f"\n💰 回测 {symbol}")
        print("=" * 60)

        # 获取和处理数据
        df = self.get_stock_data(symbol, period, source)
        if df is None:
            return None

        df = self.calculate_xye_indicators(df)
        df = self.generate_signals(df)

        # 回测逻辑
        capital = self.initial_capital
        position = 0
        entry_price = 0
        trades = []
        equity_curve = []

        for i, row in df.iterrows():
            if i < 60:
                continue

            date = row['date']
            price = row['close']
            signal = row['signal']

            # 每月增加资金
            capital = self.add_monthly_capital(date, capital)

            # 记录权益
            current_equity = capital
            if position != 0:
                if position == 1:
                    current_equity += (price - entry_price) / entry_price * capital * 0.8
                else:
                    current_equity += (entry_price - price) / entry_price * capital * 0.8

            equity_curve.append({
                'date': date,
                'equity': current_equity,
                'price': price,
                'position': position
            })

            # 交易逻辑
            if position == 0 and signal != 0:
                # 开仓
                position = signal
                entry_price = price
                trades.append({
                    'date': date,
                    'type': 'entry',
                    'direction': 'long' if signal == 1 else 'short',
                    'price': price,
                    'capital': capital
                })

            elif position != 0:
                # 检查平仓条件
                should_exit = False
                exit_reason = ""

                if position == 1:  # 多头
                    profit_ratio = (price - entry_price) / entry_price
                    if profit_ratio >= self.take_profit_long:
                        should_exit = True
                        exit_reason = "止盈"
                    elif profit_ratio <= -self.stop_loss_long:
                        should_exit = True
                        exit_reason = "止损"
                    elif signal == -1:
                        should_exit = True
                        exit_reason = "反向信号"

                elif position == -1:  # 空头
                    profit_ratio = (entry_price - price) / entry_price
                    if profit_ratio >= self.take_profit_short:
                        should_exit = True
                        exit_reason = "止盈"
                    elif profit_ratio <= -self.stop_loss_short:
                        should_exit = True
                        exit_reason = "止损"
                    elif signal == 1:
                        should_exit = True
                        exit_reason = "反向信号"

                if should_exit:
                    # 平仓
                    if position == 1:
                        profit = (price - entry_price) / entry_price * capital * 0.8
                    else:
                        profit = (entry_price - price) / entry_price * capital * 0.8

                    capital += profit

                    trades.append({
                        'date': date,
                        'type': 'exit',
                        'direction': 'long' if position == 1 else 'short',
                        'price': price,
                        'profit': profit,
                        'reason': exit_reason,
                        'capital': capital
                    })

                    position = 0

        # 分析回测结果
        self.analyze_backtest_results(trades, equity_curve, symbol)

        return {
            'trades': trades,
            'equity_curve': equity_curve,
            'final_capital': capital,
            'data': df
        }

    def add_monthly_capital(self, date, capital):
        """每月增加资金"""
        last_month = getattr(self, 'last_month', None)
        current_month = date.replace(day=1)

        if last_month is None or current_month > last_month:
            self.last_month = current_month
            return capital + self.monthly_addition

        return capital

    def analyze_backtest_results(self, trades, equity_curve, symbol):
        """分析回测结果"""
        if not trades:
            print("❌ 没有交易记录")
            return

        # 计算统计数据
        entry_trades = [t for t in trades if t['type'] == 'entry']
        exit_trades = [t for t in trades if t['type'] == 'exit']

        total_trades = len(entry_trades)
        profitable_trades = len([t for t in exit_trades if t.get('profit', 0) > 0])

        if total_trades > 0:
            win_rate = profitable_trades / len(exit_trades) * 100 if exit_trades else 0

            print(f"\n📊 {symbol} 回测结果:")
            print(f"   总交易次数: {total_trades}")
            print(f"   盈利交易: {profitable_trades}")
            print(f"   胜率: {win_rate:.1f}%")

            if exit_trades:
                profits = [t.get('profit', 0) for t in exit_trades]
                print(f"   平均盈利: {np.mean(profits):.2f}")
                print(f"   最大盈利: {max(profits):.2f}")
                print(f"   最大亏损: {min(profits):.2f}")

            # 最终资金
            if equity_curve:
                final_equity = equity_curve[-1]['equity']
                total_return = (final_equity - self.initial_capital) / self.initial_capital * 100
                print(f"   最终资金: {final_equity:.2f}")
                print(f"   总收益率: {total_return:.2f}%")

    def save_analysis_report(self, results, filename="stock_analysis_report.xlsx"):
        """保存分析报告到Excel"""
        print(f"\n💾 保存分析报告到 {filename}")

        try:
            # 创建DataFrame
            report_data = []
            for result in results:
                report_data.append({
                    '股票代码': result['symbol'],
                    '分析日期': result['date'].strftime('%Y-%m-%d'),
                    '当前价格': result['price'],
                    'Y值': result['y_value'],
                    'X值': result['x_value'],
                    'E值': result['e_value'],
                    'RSI': result['rsi'],
                    '价格位置': result['price_position'],
                    '交易信号': '买入' if result['signal'] == 1 else '卖出' if result['signal'] == -1 else '观望',
                    '综合评分': result['score'],
                    '成交量': result['volume']
                })

            df_report = pd.DataFrame(report_data)
            df_report.to_excel(filename, index=False)
            print(f"✅ 报告已保存到 {filename}")

        except Exception as e:
            print(f"❌ 保存报告失败: {e}")

    def create_portfolio(self, results, max_stocks=10, min_score=70):
        """创建投资组合"""
        print(f"\n📋 创建投资组合 (最多{max_stocks}只股票，最低{min_score}分)")

        # 筛选高分股票
        qualified_stocks = [r for r in results if r['score'] >= min_score]

        if not qualified_stocks:
            print("❌ 没有符合条件的股票")
            return None

        # 按评分排序，选择前N只
        portfolio = sorted(qualified_stocks, key=lambda x: x['score'], reverse=True)[:max_stocks]

        print(f"\n🎯 推荐投资组合 ({len(portfolio)}只股票):")
        print("-" * 80)

        total_score = sum(stock['score'] for stock in portfolio)

        for i, stock in enumerate(portfolio, 1):
            weight = stock['score'] / total_score * 100
            signal_text = "买入" if stock['signal'] == 1 else "卖出" if stock['signal'] == -1 else "观望"

            print(f"{i:2d}. {stock['symbol']:<12} "
                  f"评分:{stock['score']:3.0f} "
                  f"权重:{weight:5.1f}% "
                  f"价格:{stock['price']:8.2f} "
                  f"信号:{signal_text}")

        return portfolio

def main():
    """主函数 - 演示用法"""
    print("🎯 Cosmoon XYE选股神器 (Cosmoon XYE Stock Picker)")
    print("基于Cosmoon XYE策略的智能选股系统")
    print("=" * 80)

    # 创建选股器实例
    picker = CosmoonXYEStockPicker(initial_capital=10000, monthly_addition=1000)

    while True:
        print("\n🔧 功能菜单:")
        print("1. 📊 分析单只股票")
        print("2. 🔍 批量筛选股票")
        print("3. 💰 运行回测")
        print("4. 🏆 港股推荐列表")
        print("5. 🌍 美股推荐列表")
        print("6. 📋 创建投资组合")
        print("7. 💾 保存分析报告")
        print("0. 🚪 退出程序")

        choice = input("\n请输入选择 (0-7): ").strip()

        if choice == "0":
            print("👋 感谢使用Cosmoon XYE选股神器！")
            break

        elif choice == "1":
            symbol = input("请输入股票代码 (如: 0700.HK, AAPL, TSLA): ").strip().upper()
            if symbol:
                result = picker.analyze_stock(symbol)

                if result and input("\n是否运行回测? (y/n): ").lower() == 'y':
                    picker.run_backtest(symbol)

        elif choice == "2":
            print("\n选择股票列表:")
            print("1. 自定义列表")
            print("2. 港股蓝筹")
            print("3. 美股科技")

            list_choice = input("请选择 (1-3): ").strip()

            if list_choice == "1":
                custom_list = input("输入股票代码 (用逗号分隔): ").strip()
                if custom_list:
                    stock_list = [s.strip().upper() for s in custom_list.split(',')]
                    results = picker.screen_stocks(stock_list)

                    if results and input("\n是否创建投资组合? (y/n): ").lower() == 'y':
                        picker.create_portfolio(results)

            elif list_choice == "2":
                hk_stocks = [
                    "0700.HK", "0941.HK", "1299.HK", "2318.HK", "0005.HK",
                    "0388.HK", "1398.HK", "0939.HK", "1810.HK", "0883.HK",
                    "0023.HK", "0011.HK", "0016.HK", "0002.HK", "0003.HK"
                ]
                results = picker.screen_stocks(hk_stocks)

                if results and input("\n是否创建投资组合? (y/n): ").lower() == 'y':
                    picker.create_portfolio(results)

            elif list_choice == "3":
                us_stocks = [
                    "AAPL", "MSFT", "GOOGL", "AMZN", "TSLA",
                    "META", "NVDA", "NFLX", "AMD", "CRM",
                    "ORCL", "ADBE", "INTC", "CSCO", "IBM"
                ]
                results = picker.screen_stocks(us_stocks)

                if results and input("\n是否创建投资组合? (y/n): ").lower() == 'y':
                    picker.create_portfolio(results)

        elif choice == "3":
            symbol = input("请输入要回测的股票代码: ").strip().upper()
            if symbol:
                period = input("回测周期 (1y/2y/5y，默认2y): ").strip() or "2y"
                picker.run_backtest(symbol, period)

        elif choice == "4":
            print("\n🏆 分析港股蓝筹...")
            hk_stocks = [
                "0700.HK", "0941.HK", "1299.HK", "2318.HK", "0005.HK",
                "0388.HK", "1398.HK", "0939.HK", "1810.HK", "0883.HK",
                "0023.HK", "0011.HK", "0016.HK", "0002.HK", "0003.HK",
                "0001.HK", "0175.HK", "0267.HK", "0288.HK", "0386.HK"
            ]
            results = picker.screen_stocks(hk_stocks)

            if results:
                if input("\n是否保存报告? (y/n): ").lower() == 'y':
                    picker.save_analysis_report(results, "港股分析报告.xlsx")

                if input("是否创建投资组合? (y/n): ").lower() == 'y':
                    picker.create_portfolio(results)

        elif choice == "5":
            print("\n🌍 分析美股科技...")
            us_stocks = [
                "AAPL", "MSFT", "GOOGL", "AMZN", "TSLA",
                "META", "NVDA", "NFLX", "AMD", "CRM",
                "ORCL", "ADBE", "INTC", "CSCO", "IBM",
                "PYPL", "UBER", "ZOOM", "SHOP", "SQ"
            ]
            results = picker.screen_stocks(us_stocks)

            if results:
                if input("\n是否保存报告? (y/n): ").lower() == 'y':
                    picker.save_analysis_report(results, "美股分析报告.xlsx")

                if input("是否创建投资组合? (y/n): ").lower() == 'y':
                    picker.create_portfolio(results)

        elif choice == "6":
            print("请先运行批量筛选功能")

        elif choice == "7":
            print("请先运行批量筛选功能")

        else:
            print("❌ 无效选择，请重新输入")

if __name__ == "__main__":
    main()
