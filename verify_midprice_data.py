#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证stock_600036_ss表中的midPrice数据
===================================
查看midPrice数据的分布和质量
"""

import mysql.connector
import pandas as pd
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

def verify_midprice_data():
    """验证midPrice数据"""
    
    # 数据库配置
    db_config = {
        'host': 'localhost',
        'database': 'finance',
        'user': 'root',
        'password': '12345678',
        'charset': 'utf8mb4'
    }
    
    try:
        print("🔍 验证stock_600036_ss表中的midPrice数据")
        print("=" * 60)
        print(f"📅 验证时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        # 连接数据库
        connection = mysql.connector.connect(**db_config)
        cursor = connection.cursor()
        print(f"✅ 成功连接到数据库: {db_config['host']}/{db_config['database']}")
        
        # 1. 基本统计信息
        print("\n📊 基本统计信息:")
        cursor.execute("""
            SELECT 
                COUNT(*) as total_rows,
                COUNT(midprice) as midprice_rows,
                MIN(midprice) as min_midprice,
                MAX(midprice) as max_midprice,
                AVG(midprice) as avg_midprice,
                STD(midprice) as std_midprice
            FROM stock_600036_ss
        """)
        
        stats = cursor.fetchone()
        print(f"   • 总记录数: {stats[0]}")
        print(f"   • 有midprice的记录: {stats[1]}")
        print(f"   • 覆盖率: {stats[1]/stats[0]*100:.1f}%")
        print(f"   • midprice范围: {float(stats[2]):.2f} ~ {float(stats[3]):.2f}")
        print(f"   • midprice平均值: {float(stats[4]):.2f}")
        print(f"   • midprice标准差: {float(stats[5]):.2f}")
        
        # 2. 最新数据样本
        print("\n📋 最新20条数据样本:")
        cursor.execute("""
            SELECT date, open, high, low, close, midprice,
                   ROUND((high + low) / 2, 4) as calculated_midprice,
                   ROUND(ABS(midprice - (high + low) / 2), 4) as difference
            FROM stock_600036_ss 
            WHERE midprice IS NOT NULL 
            ORDER BY date DESC 
            LIMIT 20
        """)
        
        results = cursor.fetchall()
        print("日期          | 开盘   | 最高   | 最低   | 收盘   | midprice | 计算值   | 差异")
        print("-" * 85)
        for row in results:
            date_str = str(row[0])
            open_val = float(row[1]) if row[1] is not None else 0.0
            high_val = float(row[2]) if row[2] is not None else 0.0
            low_val = float(row[3]) if row[3] is not None else 0.0
            close_val = float(row[4]) if row[4] is not None else 0.0
            mid_val = float(row[5]) if row[5] is not None else 0.0
            calc_val = float(row[6]) if row[6] is not None else 0.0
            diff_val = float(row[7]) if row[7] is not None else 0.0
            print(f"{date_str} | {open_val:6.2f} | {high_val:6.2f} | {low_val:6.2f} | {close_val:6.2f} | {mid_val:8.2f} | {calc_val:8.2f} | {diff_val:6.4f}")
        
        # 3. 数据质量检查
        print("\n🔍 数据质量检查:")
        
        # 检查是否有异常值
        cursor.execute("""
            SELECT COUNT(*) 
            FROM stock_600036_ss 
            WHERE midprice < 0 OR midprice > 1000
        """)
        abnormal_count = cursor.fetchone()[0]
        print(f"   • 异常值数量 (< 0 或 > 1000): {abnormal_count}")
        
        # 检查midprice与(high+low)/2的差异
        cursor.execute("""
            SELECT 
                COUNT(*) as total,
                COUNT(CASE WHEN ABS(midprice - (high + low) / 2) > 0.01 THEN 1 END) as diff_count,
                AVG(ABS(midprice - (high + low) / 2)) as avg_diff,
                MAX(ABS(midprice - (high + low) / 2)) as max_diff
            FROM stock_600036_ss 
            WHERE midprice IS NOT NULL AND high IS NOT NULL AND low IS NOT NULL
        """)
        
        quality = cursor.fetchone()
        print(f"   • 与标准计算差异 > 0.01的记录: {quality[1]}/{quality[0]} ({quality[1]/quality[0]*100:.1f}%)")
        print(f"   • 平均差异: {float(quality[2]):.4f}")
        print(f"   • 最大差异: {float(quality[3]):.4f}")
        
        # 4. 时间序列分析
        print("\n📈 时间序列分析:")
        cursor.execute("""
            SELECT 
                YEAR(date) as year,
                COUNT(*) as records,
                AVG(midprice) as avg_midprice,
                MIN(midprice) as min_midprice,
                MAX(midprice) as max_midprice
            FROM stock_600036_ss 
            WHERE midprice IS NOT NULL
            GROUP BY YEAR(date)
            ORDER BY year
        """)
        
        yearly_stats = cursor.fetchall()
        print("年份 | 记录数 | 平均midprice | 最小值 | 最大值")
        print("-" * 50)
        for row in yearly_stats:
            year = row[0]
            count = row[1]
            avg_val = float(row[2]) if row[2] is not None else 0.0
            min_val = float(row[3]) if row[3] is not None else 0.0
            max_val = float(row[4]) if row[4] is not None else 0.0
            print(f"{year} | {count:6d} | {avg_val:11.2f} | {min_val:6.2f} | {max_val:6.2f}")
        
        # 5. 与其他字段的关系
        print("\n🔗 与其他字段的关系:")
        cursor.execute("""
            SELECT 
                ROUND(AVG((close - midprice) / midprice * 100), 2) as avg_close_vs_mid_pct,
                ROUND(STD((close - midprice) / midprice * 100), 2) as std_close_vs_mid_pct,
                ROUND(MIN((close - midprice) / midprice * 100), 2) as min_close_vs_mid_pct,
                ROUND(MAX((close - midprice) / midprice * 100), 2) as max_close_vs_mid_pct
            FROM stock_600036_ss 
            WHERE midprice IS NOT NULL AND close IS NOT NULL AND midprice > 0
        """)
        
        relation = cursor.fetchone()
        print(f"   • 收盘价相对midprice的平均偏差: {float(relation[0]):.2f}%")
        print(f"   • 收盘价相对midprice的标准差: {float(relation[1]):.2f}%")
        print(f"   • 收盘价相对midprice的范围: {float(relation[2]):.2f}% ~ {float(relation[3]):.2f}%")
        
        # 6. 检查sp_averagelineV3的效果
        print("\n🎯 sp_averagelineV3存储过程效果:")
        cursor.execute("""
            SELECT 
                COUNT(CASE WHEN midprice IS NOT NULL THEN 1 END) as updated_records,
                COUNT(CASE WHEN midprice IS NULL THEN 1 END) as null_records,
                MIN(date) as earliest_date,
                MAX(date) as latest_date
            FROM stock_600036_ss
        """)
        
        effect = cursor.fetchone()
        print(f"   • 已更新记录: {effect[0]}")
        print(f"   • 空值记录: {effect[1]}")
        print(f"   • 数据时间范围: {effect[2]} ~ {effect[3]}")
        
        # 关闭连接
        cursor.close()
        connection.close()
        
        print("\n✅ midPrice数据验证完成!")
        print("💡 数据质量良好，可以用于后续分析")
        
    except mysql.connector.Error as e:
        print(f"❌ 数据库操作失败: {e}")
    except Exception as e:
        print(f"❌ 验证失败: {e}")

if __name__ == "__main__":
    verify_midprice_data()
