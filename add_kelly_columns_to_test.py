#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
为test表添加凯利公式相关列
========================

功能：
1. 为test表添加凯利公式相关列
2. 计算凯利公式参数并更新每条记录
3. 严格按照1:2赔率和止盈止损规则

作者: Cosmoon NG
日期: 2025年7月
"""

import pymysql
import pandas as pd
import numpy as np

class TestTableKellyUpdater:
    def __init__(self):
        """初始化test表凯利更新器"""
        self.db_config = {
            'host': 'localhost',
            'port': 3306,
            'database': 'finance',
            'user': 'root',
            'password': '12345678',
            'charset': 'utf8mb4'
        }
        self.connection = None
        self.initial_capital = 30000
        
        # 凯利公式策略参数
        self.strategy_params = {
            'take_profit': 0.02,    # 2%止盈
            'stop_loss': 0.01,      # 1%止损
            'odds_ratio': 2.0,      # 赔率1:2
            'high_profit_y': 0.43,
            'high_profit_x': 0.43,
            'control_zone_min': 0.333,
            'control_zone_max': 0.4,
            'strong_loss_y': 0.25,
            'strong_loss_x': 0.25,
            'transaction_cost': 0.0025,
            'max_position_ratio': 0.25,
        }
        
    def connect_database(self):
        """连接数据库"""
        try:
            self.connection = pymysql.connect(**self.db_config)
            print(f"✅ 成功连接MySQL数据库: {self.db_config['host']}")
            return True
        except Exception as e:
            print(f"❌ 连接数据库失败: {e}")
            return False
    
    def add_kelly_columns_to_test(self):
        """为test表添加凯利公式相关列"""
        try:
            cursor = self.connection.cursor()
            
            # 检查是否已有凯利相关列
            cursor.execute("SHOW COLUMNS FROM test LIKE '%凯利%'")
            existing_kelly_columns = cursor.fetchall()
            
            if existing_kelly_columns:
                print("⚠️ test表已存在凯利相关列，将删除重建")
                # 删除现有凯利列
                kelly_columns = ['凯利策略区域', '凯利交易方向', '凯利仓位比例', '凯利交易金额', 
                               '凯利收益率', '凯利净利润', '凯利平仓原因', '凯利账户余额', 
                               '凯利交易理由', '凯利胜率', '凯利系数', '凯利赔率']
                for col in kelly_columns:
                    try:
                        cursor.execute(f"ALTER TABLE test DROP COLUMN `{col}`")
                    except:
                        pass
            
            # 添加凯利公式相关列
            alter_statements = [
                "ALTER TABLE test ADD COLUMN `凯利策略区域` VARCHAR(20) DEFAULT '' COMMENT '凯利公式策略区域'",
                "ALTER TABLE test ADD COLUMN `凯利交易方向` VARCHAR(10) DEFAULT '' COMMENT '凯利公式交易方向'",
                "ALTER TABLE test ADD COLUMN `凯利仓位比例` DECIMAL(8,4) DEFAULT 0 COMMENT '凯利公式仓位比例'",
                "ALTER TABLE test ADD COLUMN `凯利交易金额` DECIMAL(15,2) DEFAULT 0 COMMENT '凯利公式交易金额'",
                "ALTER TABLE test ADD COLUMN `凯利收益率` DECIMAL(8,4) DEFAULT 0 COMMENT '凯利公式收益率%'",
                "ALTER TABLE test ADD COLUMN `凯利净利润` DECIMAL(15,2) DEFAULT 0 COMMENT '凯利公式净利润'",
                "ALTER TABLE test ADD COLUMN `凯利平仓原因` VARCHAR(20) DEFAULT '' COMMENT '凯利公式平仓原因'",
                "ALTER TABLE test ADD COLUMN `凯利账户余额` DECIMAL(15,2) DEFAULT 0 COMMENT '凯利公式账户余额'",
                "ALTER TABLE test ADD COLUMN `凯利交易理由` TEXT COMMENT '凯利公式交易理由'",
                "ALTER TABLE test ADD COLUMN `凯利胜率` DECIMAL(8,6) DEFAULT 0 COMMENT '凯利公式历史胜率'",
                "ALTER TABLE test ADD COLUMN `凯利系数` DECIMAL(8,6) DEFAULT 0 COMMENT '凯利公式系数f'",
                "ALTER TABLE test ADD COLUMN `凯利赔率` VARCHAR(10) DEFAULT '1:2' COMMENT '凯利公式赔率'"
            ]
            
            for statement in alter_statements:
                cursor.execute(statement)
            
            self.connection.commit()
            print("✅ 成功为test表添加凯利公式相关列")
            return True
            
        except Exception as e:
            print(f"❌ 添加凯利列失败: {e}")
            return False
    
    def calculate_kelly_parameters(self):
        """计算凯利公式参数"""
        try:
            cursor = self.connection.cursor()
            
            # 获取历史数据计算胜率
            cursor.execute("SELECT 净利润 FROM test WHERE 净利润 != 0 ORDER BY 交易序号")
            profits = cursor.fetchall()
            
            if not profits:
                print("❌ 没有历史交易数据")
                return None
            
            total_trades = len(profits)
            winning_trades = len([p for p in profits if p[0] > 0])
            
            p = winning_trades / total_trades  # 胜率
            q = 1 - p  # 败率
            b = self.strategy_params['odds_ratio']  # 赔率
            
            # 凯利公式: f = (bp - q) / b
            kelly_f = (b * p - q) / b
            optimal_position = min(kelly_f, self.strategy_params['max_position_ratio']) if kelly_f > 0 else 0
            
            kelly_params = {
                'win_rate': p,
                'lose_rate': q,
                'kelly_f': kelly_f,
                'optimal_position': optimal_position
            }
            
            print(f"📊 凯利公式参数计算:")
            print(f"   • 历史总交易: {total_trades}")
            print(f"   • 历史盈利: {winning_trades}")
            print(f"   • 历史胜率 p: {p:.6f}")
            print(f"   • 历史败率 q: {q:.6f}")
            print(f"   • 赔率 b: {b}")
            print(f"   • 凯利系数 f: {kelly_f:.6f}")
            print(f"   • 最优仓位: {optimal_position*100:.2f}%")
            
            return kelly_params
            
        except Exception as e:
            print(f"❌ 计算凯利参数失败: {e}")
            return None
    
    def update_kelly_data_in_test(self, kelly_params):
        """更新test表的凯利数据"""
        try:
            print("🧮 计算并更新test表的凯利公式数据...")
            
            if kelly_params['optimal_position'] <= 0:
                print("❌ 凯利系数为负，不进行交易计算")
                return False
            
            cursor = self.connection.cursor()
            
            # 获取所有记录
            cursor.execute("""
                SELECT 交易序号, 开仓日期, close, 平仓价格, `控制系数`, `资金流比例`, 
                       E值, 净利润, `真实流入`, `真实流出`
                FROM test 
                ORDER BY 交易序号
            """)
            
            records = cursor.fetchall()
            current_cash = self.initial_capital
            
            for record in records:
                (trade_id, open_date, open_price, close_price, y_val, x_val, 
                 e_val, original_profit, real_flow_in, real_flow_out) = record
                
                # 分类策略区域
                if (y_val > self.strategy_params['high_profit_y'] and 
                    x_val > self.strategy_params['high_profit_x']):
                    zone = '高值盈利区'
                    direction = '做多'
                    reason = f'高值盈利区：控制系数Y={y_val:.3f}>0.43且资金流比例X={x_val:.3f}>0.43，凯利公式建议做多'
                elif (y_val > self.strategy_params['control_zone_min'] and 
                      y_val < self.strategy_params['control_zone_max']):
                    zone = '控股商控制区'
                    direction = '观望'
                    reason = f'控股商控制区：控制系数Y={y_val:.3f}在0.333-0.4之间，凯利公式建议观望'
                elif (y_val < self.strategy_params['strong_loss_y'] or 
                      x_val < self.strategy_params['strong_loss_x']):
                    zone = '强亏损区'
                    direction = '做空'
                    reason = f'强亏损区：控制系数Y={y_val:.3f}<0.25或资金流比例X={x_val:.3f}<0.25，凯利公式建议做空'
                else:
                    zone = '其他区域'
                    direction = '做空'
                    reason = f'其他区域：控制系数Y={y_val:.3f}，资金流比例X={x_val:.3f}，凯利公式建议做空'
                
                # 计算交易结果
                if direction == '观望':
                    kelly_position = 0
                    kelly_amount = 0
                    kelly_return = 0
                    kelly_profit = 0
                    exit_reason = '观望'
                else:
                    kelly_position = kelly_params['optimal_position']
                    position_value = current_cash * kelly_position
                    shares = int(position_value / open_price / 100) * 100
                    kelly_amount = shares * open_price
                    
                    if shares >= 100 and current_cash > kelly_amount:
                        # 严格按照止盈止损计算
                        if direction == '做多':
                            profit_pct = (close_price - open_price) / open_price
                            if profit_pct >= self.strategy_params['take_profit']:
                                exit_reason = '止盈'
                                profit_pct = self.strategy_params['take_profit']
                            elif profit_pct <= -self.strategy_params['stop_loss']:
                                exit_reason = '止损'
                                profit_pct = -self.strategy_params['stop_loss']
                            else:
                                exit_reason = '到期平仓'
                        else:  # 做空
                            profit_pct = (open_price - close_price) / open_price
                            if profit_pct >= self.strategy_params['take_profit']:
                                exit_reason = '止盈'
                                profit_pct = self.strategy_params['take_profit']
                            elif profit_pct <= -self.strategy_params['stop_loss']:
                                exit_reason = '止损'
                                profit_pct = -self.strategy_params['stop_loss']
                            else:
                                exit_reason = '到期平仓'
                        
                        kelly_return = profit_pct * 100
                        gross_profit = profit_pct * kelly_amount
                        transaction_cost = kelly_amount * self.strategy_params['transaction_cost'] * 2
                        kelly_profit = gross_profit - transaction_cost
                        current_cash += kelly_profit
                    else:
                        kelly_return = 0
                        kelly_profit = 0
                        exit_reason = '资金不足'
                        kelly_amount = 0
                        kelly_position = 0
                
                # 更新数据库
                update_sql = """
                    UPDATE test 
                    SET `凯利策略区域` = %s,
                        `凯利交易方向` = %s,
                        `凯利仓位比例` = %s,
                        `凯利交易金额` = %s,
                        `凯利收益率` = %s,
                        `凯利净利润` = %s,
                        `凯利平仓原因` = %s,
                        `凯利账户余额` = %s,
                        `凯利交易理由` = %s,
                        `凯利胜率` = %s,
                        `凯利系数` = %s,
                        `凯利赔率` = %s
                    WHERE 交易序号 = %s
                """
                
                cursor.execute(update_sql, (
                    zone, direction, kelly_position, kelly_amount, kelly_return, kelly_profit,
                    exit_reason, current_cash, reason, kelly_params['win_rate'], 
                    kelly_params['kelly_f'], f"1:{self.strategy_params['odds_ratio']}", trade_id
                ))
            
            self.connection.commit()
            print(f"✅ 成功更新100条记录的凯利公式数据")
            print(f"💰 凯利策略最终资金: {current_cash:,.0f}港币")
            print(f"📈 凯利策略总收益: {current_cash - self.initial_capital:+,.0f}港币")
            print(f"📊 凯利策略收益率: {(current_cash / self.initial_capital - 1) * 100:+.2f}%")
            
            return True
            
        except Exception as e:
            print(f"❌ 更新凯利数据失败: {e}")
            return False
    
    def verify_kelly_data_in_test(self):
        """验证test表中的凯利数据"""
        try:
            cursor = self.connection.cursor()
            
            # 显示前20条记录
            cursor.execute("""
                SELECT 交易序号, 开仓日期, `凯利策略区域`, `凯利交易方向`, 
                       `控制系数`, `资金流比例`, E值, `凯利仓位比例`,
                       `凯利交易金额`, `凯利收益率`, `凯利净利润`, `凯利平仓原因`, `凯利账户余额`
                FROM test 
                ORDER BY 交易序号
                LIMIT 20
            """)
            
            records = cursor.fetchall()
            
            print(f"\n📊 test表凯利公式数据验证 (前20条记录):")
            print("="*160)
            print(f"{'序号':<4} {'日期':<12} {'凯利区域':<12} {'凯利方向':<8} {'Y值':<8} {'X值':<8} {'E值':<10} "
                  f"{'凯利仓位%':<10} {'凯利金额':<10} {'凯利收益%':<10} {'凯利利润':<10} {'凯利平仓':<10} {'凯利余额':<10}")
            print("-" * 160)
            
            for record in records:
                (trade_no, date, zone, direction, y_val, x_val, e_val, position, 
                 amount, return_pct, profit, exit_reason, balance) = record
                
                print(f"{trade_no:<4} {date:<12} {zone:<12} {direction:<8} {float(y_val):<8.3f} "
                      f"{float(x_val):<8.3f} {float(e_val):<10.3f} {float(position)*100:<9.1f}% "
                      f"{int(amount):<10,} {float(return_pct):<9.2f}% {int(profit):<10,} "
                      f"{exit_reason:<10} {int(balance):<10,}")
            
            # 统计分析
            cursor.execute("""
                SELECT 
                    COUNT(*) as total,
                    SUM(CASE WHEN `凯利交易方向` != '观望' THEN 1 ELSE 0 END) as actual_trades,
                    SUM(CASE WHEN `凯利净利润` > 0 THEN 1 ELSE 0 END) as winning,
                    SUM(`凯利净利润`) as total_profit,
                    AVG(`凯利胜率`) as kelly_win_rate,
                    AVG(`凯利系数`) as kelly_f,
                    MAX(`凯利账户余额`) as final_balance
                FROM test
            """)
            
            stats = cursor.fetchone()
            total, actual_trades, winning, total_profit, kelly_win_rate, kelly_f, final_balance = stats
            
            print(f"\n📈 test表凯利公式数据统计:")
            print("-" * 60)
            print(f"• 总记录数: {total}")
            print(f"• 凯利实际交易: {actual_trades}")
            print(f"• 凯利观望次数: {total - actual_trades}")
            print(f"• 凯利盈利次数: {winning}")
            print(f"• 凯利胜率: {winning/actual_trades*100:.1f}%" if actual_trades > 0 else "• 凯利胜率: N/A")
            print(f"• 凯利总盈亏: {int(total_profit):+,}港币")
            print(f"• 凯利历史胜率: {float(kelly_win_rate):.3f}")
            print(f"• 凯利系数: {float(kelly_f):.6f}")
            print(f"• 凯利最终资金: {int(final_balance):,}港币")
            print(f"• 凯利总收益率: {(final_balance/self.initial_capital-1)*100:+.2f}%")
            
            # 按凯利策略区域统计
            cursor.execute("""
                SELECT `凯利策略区域`, 
                       COUNT(*) as count,
                       SUM(`凯利净利润`) as total_profit,
                       AVG(`凯利净利润`) as avg_profit,
                       SUM(CASE WHEN `凯利净利润` > 0 THEN 1 ELSE 0 END) as wins
                FROM test 
                GROUP BY `凯利策略区域`
                ORDER BY total_profit DESC
            """)
            
            zone_stats = cursor.fetchall()
            
            print(f"\n📊 test表按凯利策略区域统计:")
            print("-" * 70)
            for zone, count, total_profit, avg_profit, wins in zone_stats:
                win_rate = wins/count*100 if count > 0 else 0
                print(f"• {zone}: {count}次, 总盈亏{int(total_profit):+,}港币, "
                      f"平均{int(avg_profit):+}港币, 胜率{win_rate:.1f}%")
            
            return True
            
        except Exception as e:
            print(f"❌ 验证凯利数据失败: {e}")
            return False
    
    def close_connection(self):
        """关闭数据库连接"""
        if self.connection:
            self.connection.close()
            print("\n🔒 数据库连接已关闭")

def main():
    """主函数"""
    print("🏦 为test表添加凯利公式相关列")
    print("="*60)
    print("📊 凯利公式: f = (bp - q) / b")
    print("🎲 赔率: 1:2, 止盈: +2%, 止损: -1%")
    
    # 创建更新器
    updater = TestTableKellyUpdater()
    
    # 连接数据库
    if not updater.connect_database():
        return
    
    # 添加凯利列
    if not updater.add_kelly_columns_to_test():
        updater.close_connection()
        return
    
    # 计算凯利参数
    kelly_params = updater.calculate_kelly_parameters()
    if kelly_params is None:
        updater.close_connection()
        return
    
    # 更新凯利数据
    if not updater.update_kelly_data_in_test(kelly_params):
        updater.close_connection()
        return
    
    # 验证凯利数据
    updater.verify_kelly_data_in_test()
    
    # 关闭连接
    updater.close_connection()
    
    print(f"\n🎉 test表凯利公式列添加完成!")
    print("📊 新增12个凯利公式相关列")
    print("💡 现在可以直接在test表中查看凯利公式策略结果")

if __name__ == "__main__":
    main()
