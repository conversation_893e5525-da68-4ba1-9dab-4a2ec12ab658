#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
东亚银行(0023.HK)每日交易记录填充系统
===================================

每日在香港交易所交易时间结束后（下午5:00）自动填充交易记录表格
交易时间：
- 正常交易日：上午9:30-12:00，下午1:00-4:00
- 半日交易日：上午9:30-12:00

作者: Cosmoon NG
日期: 2025年7月22日
"""

import yfinance as yf
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import os
from scipy import stats
import warnings
warnings.filterwarnings('ignore')

class DailyTradingTracker:
    """每日交易记录填充器"""
    
    def __init__(self):
        self.symbol = "0023.HK"
        self.excel_file = "交易记录追踪0023.HK .xlsx"
        
        # 交易参数
        self.take_profit_long = 0.025   # 多头止盈 2.5%
        self.stop_loss_long = 0.015     # 多头止损 1.5%
        self.take_profit_short = 0.015  # 空头止盈 1.5%
        self.stop_loss_short = 0.025    # 空头止损 2.5%
        
        # 当前状态（从Excel文件读取）
        self.current_position = 0
        self.current_price = 0
        self.current_capital = 10000
        
        print(f"🎯 {self.symbol}每日交易记录填充系统")
        print(f"📅 填充日期: {datetime.now().strftime('%Y年%m月%d日')}")
        print(f"⏰ 交易时间结束后填充 (下午5:00)")

    def get_today_market_data(self):
        """获取今日市场数据"""
        print(f"\n📊 获取{self.symbol}今日市场数据...")
        
        try:
            # 获取最近5天数据（确保包含今天）
            end_date = datetime.now()
            start_date = end_date - timedelta(days=5)
            
            ticker = yf.Ticker(self.symbol)
            hist_data = ticker.history(start=start_date, end=end_date)
            
            if hist_data.empty:
                raise ValueError(f"无法获取{self.symbol}的数据")
            
            # 获取今日数据
            today = datetime.now().date()
            today_data = None
            
            for date, row in hist_data.iterrows():
                if date.date() == today:
                    today_data = row
                    break
            
            if today_data is None:
                # 如果没有今日数据，使用最新数据
                today_data = hist_data.iloc[-1]
                print(f"⚠️ 未找到今日数据，使用最新数据: {hist_data.index[-1].date()}")
            else:
                print(f"✅ 成功获取今日数据: {today}")
            
            # 转换为标准格式
            market_data = pd.DataFrame({
                'date': hist_data.index,
                'open': hist_data['Open'],
                'high': hist_data['High'],
                'low': hist_data['Low'],
                'close': hist_data['Close'],
                'volume': hist_data['Volume']
            })
            
            # 计算技术指标
            self.calculate_xye_indicators(market_data)
            
            # 获取今日指标
            today_indicators = market_data.iloc[-1]
            
            print(f"📈 今日交易数据:")
            print(f"   开盘价: {today_data['Open']:.2f} 港元")
            print(f"   最高价: {today_data['High']:.2f} 港元")
            print(f"   最低价: {today_data['Low']:.2f} 港元")
            print(f"   收盘价: {today_data['Close']:.2f} 港元")
            print(f"   成交量: {today_data['Volume']:,.0f}")
            print(f"   Y值: {today_indicators['y_value']:.4f}")
            print(f"   X值: {today_indicators['x_value']:.4f}")
            print(f"   E值: {today_indicators['e_value']:.4f}")
            
            return today_data, today_indicators, market_data
            
        except Exception as e:
            print(f"❌ 获取市场数据失败: {e}")
            return None, None, None

    def calculate_xye_indicators(self, df):
        """计算XYE技术指标"""
        # Y值：价格在20日区间的位置
        window = 20
        df['high_20'] = df['high'].rolling(window).max()
        df['low_20'] = df['low'].rolling(window).min()
        df['y_value'] = (df['close'] - df['low_20']) / (df['high_20'] - df['low_20'])
        df['y_value'] = df['y_value'].fillna(0.5).clip(0, 1)
        
        # X值：资金流强度指标
        df['typical_price'] = (df['high'] + df['low'] + df['close']) / 3
        df['money_flow'] = df['typical_price'] * df['volume']
        df['price_change'] = df['typical_price'].diff()
        
        # 正负资金流
        df['positive_mf'] = np.where(df['price_change'] > 0, df['money_flow'], 0)
        df['negative_mf'] = np.where(df['price_change'] < 0, df['money_flow'], 0)
        
        # 14日资金流比率
        period = 14
        df['positive_mf_14'] = df['positive_mf'].rolling(period).sum()
        df['negative_mf_14'] = df['negative_mf'].rolling(period).sum()
        df['money_flow_ratio'] = df['positive_mf_14'] / (df['negative_mf_14'] + 1e-10)
        
        # MFI和X值
        df['mfi'] = 100 - (100 / (1 + df['money_flow_ratio']))
        df['x_value'] = df['mfi'] / 100
        
        # E值：Cosmoon核心公式
        df['e_value'] = (8 * df['x_value'] - 3) * df['y_value'] - 3 * df['x_value'] + 1
        
        # 计算回归线
        df['i'] = range(1, len(df) + 1)
        if len(df) > 20:
            slope, intercept, r_value, _, _ = stats.linregress(df['i'], df['close'])
            df['regression_line'] = intercept + slope * df['i']
            df['price_position'] = (df['close'] - df['regression_line']) / df['regression_line']

    def load_existing_records(self):
        """加载现有的交易记录"""
        try:
            if os.path.exists(self.excel_file):
                df = pd.read_excel(self.excel_file)
                print(f"📋 加载现有记录: {len(df)}条")
                
                # 获取最新状态
                if len(df) > 0:
                    latest = df.iloc[-1]
                    self.current_capital = latest.get('账户余额', 10000)
                    # 从备注中判断当前持仓
                    if '开仓' in str(latest.get('备注', '')):
                        if '多头' in str(latest.get('交易方向', '')):
                            self.current_position = 1
                        elif '空头' in str(latest.get('交易方向', '')):
                            self.current_position = -1
                        self.current_price = latest.get('交易价格', 0)
                    
                return df
            else:
                print(f"📋 创建新的交易记录表格")
                return pd.DataFrame()
                
        except Exception as e:
            print(f"❌ 加载记录失败: {e}")
            return pd.DataFrame()

    def generate_trading_signal(self, indicators):
        """生成交易信号"""
        y_val = indicators['y_value']
        x_val = indicators['x_value']
        e_val = indicators['e_value']
        price_pos = indicators.get('price_position', 0)
        
        # 多头信号
        if (e_val > 0 and x_val > 0.45 and y_val > 0.45 and price_pos < 0):
            return 1, "强烈买入", "XYE多头信号"
        
        # 空头信号
        elif ((y_val < 0.3 or x_val < 0.3 or
               (x_val > 0.45 and y_val < 0.35) or
               (x_val < 0.45 and y_val > 0.35)) and price_pos > 0):
            return -1, "强烈卖出", "XYE空头信号"
        
        else:
            return 0, "观望", "无明确信号"

    def calculate_risk_level(self, indicators):
        """计算风险等级"""
        price_pos = abs(indicators.get('price_position', 0))
        
        if price_pos < 0.02:
            return "低风险"
        elif price_pos < 0.05:
            return "中风险"
        elif price_pos < 0.1:
            return "高风险"
        else:
            return "极高风险"

    def fill_daily_record(self):
        """填充今日交易记录"""
        print(f"\n📝 填充今日交易记录...")
        
        # 获取市场数据
        today_data, today_indicators, market_data = self.get_today_market_data()
        if today_data is None:
            print("❌ 无法获取市场数据，跳过今日记录")
            return
        
        # 加载现有记录
        existing_records = self.load_existing_records()
        
        # 生成交易信号
        signal, signal_strength, signal_desc = self.generate_trading_signal(today_indicators)
        risk_level = self.calculate_risk_level(today_indicators)
        
        # 当前价格
        current_price = today_data['Close']
        
        # 检查是否需要交易
        trade_executed = False
        trade_type = ""
        trade_direction = ""
        realized_pnl = 0
        note = f"市场观察 - {signal_desc}"
        
        # 检查止盈止损
        if self.current_position != 0:
            if self.current_position == 1:  # 多头持仓
                profit_ratio = (current_price - self.current_price) / self.current_price
                if profit_ratio >= self.take_profit_long:
                    trade_executed = True
                    trade_type = "平仓"
                    trade_direction = "多头止盈"
                    realized_pnl = profit_ratio * self.current_capital * 0.8
                    self.current_position = 0
                    note = f"多头止盈平仓: +{profit_ratio*100:.2f}%"
                elif profit_ratio <= -self.stop_loss_long:
                    trade_executed = True
                    trade_type = "平仓"
                    trade_direction = "多头止损"
                    realized_pnl = profit_ratio * self.current_capital * 0.8
                    self.current_position = 0
                    note = f"多头止损平仓: {profit_ratio*100:.2f}%"
            
            elif self.current_position == -1:  # 空头持仓
                profit_ratio = (self.current_price - current_price) / self.current_price
                if profit_ratio >= self.take_profit_short:
                    trade_executed = True
                    trade_type = "平仓"
                    trade_direction = "空头止盈"
                    realized_pnl = profit_ratio * self.current_capital * 0.8
                    self.current_position = 0
                    note = f"空头止盈平仓: +{profit_ratio*100:.2f}%"
                elif profit_ratio <= -self.stop_loss_short:
                    trade_executed = True
                    trade_type = "平仓"
                    trade_direction = "空头止损"
                    realized_pnl = profit_ratio * self.current_capital * 0.8
                    self.current_position = 0
                    note = f"空头止损平仓: {profit_ratio*100:.2f}%"
        
        # 检查开仓信号
        if not trade_executed and self.current_position == 0:
            if signal == 1:  # 多头信号
                trade_executed = True
                trade_type = "开仓"
                trade_direction = "多头"
                self.current_position = 1
                self.current_price = current_price
                note = f"多头开仓 - {signal_desc}"
            elif signal == -1:  # 空头信号
                trade_executed = True
                trade_type = "开仓"
                trade_direction = "空头"
                self.current_position = -1
                self.current_price = current_price
                note = f"空头开仓 - {signal_desc}"
        
        # 计算持仓相关数据
        if self.current_position != 0:
            quantity = int(self.current_capital * 0.8 / current_price)
            trade_amount = current_price * quantity
            current_market_value = current_price * quantity
            if self.current_position == 1:
                unrealized_pnl = (current_price - self.current_price) * quantity
            else:
                unrealized_pnl = (self.current_price - current_price) * quantity
        else:
            quantity = 0
            trade_amount = 0
            current_market_value = 0
            unrealized_pnl = 0
        
        # 计算费用和资金
        commission = trade_amount * 0.001 if trade_executed else 0
        net_amount = trade_amount - commission
        
        if trade_executed:
            if trade_type == "开仓":
                self.current_capital -= net_amount
            else:  # 平仓
                self.current_capital += net_amount + realized_pnl
        
        total_assets = self.current_capital + current_market_value
        
        # 计算收益率
        if len(existing_records) > 0:
            prev_total = existing_records['总资产'].iloc[-1] if '总资产' in existing_records.columns else 10000
            daily_return = (total_assets - prev_total) / prev_total * 100
        else:
            daily_return = 0
        
        cumulative_return = (total_assets - 10000) / 10000 * 100
        
        # 创建今日记录
        today_record = {
            '交易日期': datetime.now().strftime('%Y-%m-%d'),
            '交易类型': trade_type if trade_executed else "观察",
            '交易方向': trade_direction if trade_executed else "无",
            '交易价格': current_price,
            '持仓数量': quantity,
            '交易金额': trade_amount,
            '手续费': commission,
            '净交易额': net_amount,
            '持仓成本': self.current_price if self.current_position != 0 else 0,
            '当前市值': current_market_value,
            '浮动盈亏': unrealized_pnl,
            '实现盈亏': realized_pnl,
            '累计盈亏': realized_pnl + unrealized_pnl,
            '账户余额': self.current_capital,
            '总资产': total_assets,
            '收益率': daily_return,
            '累计收益率': cumulative_return,
            'Y值': today_indicators['y_value'],
            'X值': today_indicators['x_value'],
            'E值': today_indicators['e_value'],
            '信号强度': signal_strength,
            '风险等级': risk_level,
            '备注': note
        }
        
        # 添加到记录中
        new_records = pd.concat([existing_records, pd.DataFrame([today_record])], ignore_index=True)
        
        # 保存到Excel
        new_records.to_excel(self.excel_file, index=False)
        
        print(f"✅ 今日记录已填充完成")
        print(f"📊 交易状态: {trade_type if trade_executed else '观察'}")
        print(f"💰 总资产: {total_assets:,.2f} 港元")
        print(f"📈 累计收益率: {cumulative_return:.2f}%")
        print(f"💾 记录已保存到: {self.excel_file}")
        
        return today_record

def main():
    """主函数 - 每日下午5:00执行"""
    print("🕔 香港交易所交易时间结束 (下午5:00)")
    print("📊 开始填充今日交易记录...")
    print("=" * 60)
    
    try:
        tracker = DailyTradingTracker()
        record = tracker.fill_daily_record()
        
        if record:
            print(f"\n✅ 今日交易记录填充完成")
            print(f"📅 下次填充时间: 明日下午5:00")
        
    except Exception as e:
        print(f"❌ 填充失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
