#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
东亚银行(0023.HK) 入场价格分析器
根据策略逻辑确定看涨/看跌方向和具体入场价位、止盈止损
"""

import yfinance as yf
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

class EntryPriceAnalyzer:
    def __init__(self):
        """初始化入场价格分析器"""
        self.ticker = '0023.HK'
        
        # 策略参数
        self.x_threshold_long = 0.45    # 买涨X阈值
        self.y_threshold_long = 0.45    # 买涨Y阈值
        self.x_threshold_short = 0.25   # 买跌X阈值
        self.y_threshold_short = 0.25   # 买跌Y阈值
        
        # 止盈止损参数（不对称设计）
        self.take_profit_long = 0.012   # 多头止盈 1.2%
        self.stop_loss_long = 0.008     # 多头止损 0.8%
        self.take_profit_short = 0.008  # 空头止盈 0.8%
        self.stop_loss_short = 0.012    # 空头止损 1.2%
        
    def get_latest_market_data(self):
        """获取最新市场数据"""
        try:
            print("📊 获取最新市场数据...")
            
            # 获取最近100天数据用于计算指标
            end_date = datetime.now()
            start_date = end_date - timedelta(days=100)
            
            data = yf.download(self.ticker, start=start_date, end=end_date)
            
            if data.empty:
                raise ValueError("未获取到数据")
            
            # 处理多级列名
            if isinstance(data.columns, pd.MultiIndex):
                data.columns = [col[0] for col in data.columns]
            
            # 重命名列
            data = data.rename(columns={
                'Open': 'open', 'High': 'high', 'Low': 'low', 
                'Close': 'close', 'Volume': 'volume'
            })
            
            data = data.reset_index()
            data = data.rename(columns={'Date': 'date'})
            
            # 计算技术指标
            data = self.calculate_indicators(data)
            
            print(f"✓ 成功获取数据，最新日期: {data['date'].iloc[-1].strftime('%Y-%m-%d')}")
            return data
            
        except Exception as e:
            print(f"❌ 数据获取失败: {e}")
            return None
    
    def calculate_indicators(self, df):
        """计算技术指标"""
        # 计算回归线
        window = 60
        df['regression_line'] = df['close'].rolling(window=window).apply(
            lambda x: np.polyval(np.polyfit(range(len(x)), x, 1), len(x)-1), raw=False
        )
        
        # 计算RSI
        delta = df['close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
        rs = gain / loss
        df['rsi'] = 100 - (100 / (1 + rs))
        
        # 计算资金流比例 (X值)
        price_change = (df['close'] - df['open']) / df['open']
        money_flow = df['volume'] * price_change
        
        def calc_inflow_ratio(flows):
            if len(flows) == 0 or flows.isna().all():
                return 0.5
            flows = flows.dropna()
            if len(flows) == 0:
                return 0.5
            
            inflows = flows[flows > 0].sum()
            outflows = abs(flows[flows < 0].sum())
            total_flow = inflows + outflows
            
            return inflows / total_flow if total_flow > 0 else 0.5
        
        df['money_flow_ratio'] = money_flow.rolling(window=20).apply(calc_inflow_ratio, raw=False)
        df['money_flow_ratio'] = np.clip(df['money_flow_ratio'], 0.1, 0.9).fillna(0.5)
        
        # 计算Full_Y值 (控制系数)
        price_momentum = df['close'].pct_change(10)
        df['full_y'] = (df['rsi'] / 100 + np.tanh(price_momentum * 5) + 1) / 2
        df['full_y'] = np.clip(df['full_y'], 0.1, 0.9)
        
        # 计算E值
        df['e_value'] = 8 * df['money_flow_ratio'] * df['full_y'] - 3 * df['money_flow_ratio'] - 3 * df['full_y'] + 1
        
        # 计算价格相对回归线位置
        df['price_position'] = (df['close'] - df['regression_line']) / df['regression_line']
        
        return df
    
    def analyze_entry_signals(self, current_price=None):
        """分析入场信号和价格"""
        data = self.get_latest_market_data()
        if data is None:
            return None
        
        latest = data.iloc[-1]
        
        # 如果没有提供当前价格，使用最新收盘价
        if current_price is None:
            current_price = latest['close']
        
        print("\n" + "="*70)
        print("🎯 入场价格分析报告")
        print("="*70)
        
        print(f"📅 分析时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"💰 当前价格: {current_price:.2f} 港币")
        print(f"📊 回归线价格: {latest['regression_line']:.2f} 港币")
        
        # 获取关键指标
        x_val = latest['money_flow_ratio']
        y_val = latest['full_y']
        e_val = latest['e_value']
        price_pos = latest['price_position']
        
        print(f"\n🔢 关键指标:")
        print(f"  • X值 (资金流比例): {x_val:.3f}")
        print(f"  • Y值 (控制系数): {y_val:.3f}")
        print(f"  • E值 (博弈期望): {e_val:.3f}")
        print(f"  • 价格位置: {price_pos*100:.2f}% ({'高于' if price_pos > 0 else '低于'}回归线)")
        
        # 分析信号
        signal_analysis = self.determine_signal(x_val, y_val, e_val, price_pos, current_price)
        
        return signal_analysis
    
    def determine_signal(self, x_val, y_val, e_val, price_pos, current_price):
        """确定交易信号和价格"""
        
        print(f"\n📈 信号分析:")
        print("-" * 50)
        
        # 检查买涨条件
        long_condition = (e_val > 0 and 
                         x_val > self.x_threshold_long and 
                         y_val > self.y_threshold_long and 
                         price_pos < 0)
        
        # 检查买跌条件
        short_condition = ((y_val < self.y_threshold_short or x_val < self.x_threshold_short) and 
                          price_pos > 0)
        
        if long_condition:
            return self.generate_long_signal(current_price, x_val, y_val, e_val, price_pos)
        elif short_condition:
            return self.generate_short_signal(current_price, x_val, y_val, e_val, price_pos)
        else:
            return self.generate_wait_signal(current_price, x_val, y_val, e_val, price_pos)
    
    def generate_long_signal(self, current_price, x_val, y_val, e_val, price_pos):
        """生成看涨信号"""
        print("🟢 看涨信号 - 建议买入做多")
        print(f"✓ 满足条件: E值>0({e_val:.3f}) + X值>{self.x_threshold_long}({x_val:.3f}) + Y值>{self.y_threshold_long}({y_val:.3f}) + 价格低于回归线")
        
        # 计算入场价格和止盈止损
        entry_price = current_price
        stop_loss_price = entry_price * (1 - self.stop_loss_long)
        take_profit_price = entry_price * (1 + self.take_profit_long)
        
        print(f"\n💰 价格设置:")
        print(f"  📍 建议入场价: {entry_price:.2f} 港币")
        print(f"  🛑 止损价位: {stop_loss_price:.2f} 港币 (下跌{self.stop_loss_long*100:.1f}%)")
        print(f"  🎯 止盈价位: {take_profit_price:.2f} 港币 (上涨{self.take_profit_long*100:.1f}%)")
        
        # 计算风险收益比
        risk = entry_price - stop_loss_price
        reward = take_profit_price - entry_price
        risk_reward_ratio = reward / risk if risk > 0 else 0
        
        print(f"\n📊 风险收益分析:")
        print(f"  • 风险金额: {risk:.2f} 港币")
        print(f"  • 收益金额: {reward:.2f} 港币")
        print(f"  • 风险收益比: 1:{risk_reward_ratio:.2f}")
        
        return {
            'signal': 'LONG',
            'direction': '看涨',
            'action': '买入做多',
            'entry_price': entry_price,
            'stop_loss': stop_loss_price,
            'take_profit': take_profit_price,
            'risk_reward_ratio': risk_reward_ratio,
            'confidence': self.calculate_confidence(x_val, y_val, e_val, 'long')
        }
    
    def generate_short_signal(self, current_price, x_val, y_val, e_val, price_pos):
        """生成看跌信号"""
        print("🔴 看跌信号 - 建议买入做空")
        
        if y_val < self.y_threshold_short and x_val < self.x_threshold_short:
            condition_text = f"Y值<{self.y_threshold_short}({y_val:.3f}) 且 X值<{self.x_threshold_short}({x_val:.3f})"
        elif y_val < self.y_threshold_short:
            condition_text = f"Y值<{self.y_threshold_short}({y_val:.3f})"
        else:
            condition_text = f"X值<{self.x_threshold_short}({x_val:.3f})"
        
        print(f"✓ 满足条件: {condition_text} + 价格高于回归线")
        
        # 计算入场价格和止盈止损
        entry_price = current_price
        stop_loss_price = entry_price * (1 + self.stop_loss_short)
        take_profit_price = entry_price * (1 - self.take_profit_short)
        
        print(f"\n💰 价格设置:")
        print(f"  📍 建议入场价: {entry_price:.2f} 港币")
        print(f"  🛑 止损价位: {stop_loss_price:.2f} 港币 (上涨{self.stop_loss_short*100:.1f}%)")
        print(f"  🎯 止盈价位: {take_profit_price:.2f} 港币 (下跌{self.take_profit_short*100:.1f}%)")
        
        # 计算风险收益比
        risk = stop_loss_price - entry_price
        reward = entry_price - take_profit_price
        risk_reward_ratio = reward / risk if risk > 0 else 0
        
        print(f"\n📊 风险收益分析:")
        print(f"  • 风险金额: {risk:.2f} 港币")
        print(f"  • 收益金额: {reward:.2f} 港币")
        print(f"  • 风险收益比: 1:{risk_reward_ratio:.2f}")
        
        return {
            'signal': 'SHORT',
            'direction': '看跌',
            'action': '买入做空',
            'entry_price': entry_price,
            'stop_loss': stop_loss_price,
            'take_profit': take_profit_price,
            'risk_reward_ratio': risk_reward_ratio,
            'confidence': self.calculate_confidence(x_val, y_val, e_val, 'short')
        }
    
    def generate_wait_signal(self, current_price, x_val, y_val, e_val, price_pos):
        """生成观望信号"""
        print("⚪ 观望信号 - 建议等待")
        print("❌ 当前不满足买涨或买跌条件")
        
        print(f"\n📋 条件检查:")
        print(f"  买涨条件:")
        print(f"    • E值>0: {'✓' if e_val > 0 else '✗'} ({e_val:.3f})")
        print(f"    • X值>{self.x_threshold_long}: {'✓' if x_val > self.x_threshold_long else '✗'} ({x_val:.3f})")
        print(f"    • Y值>{self.y_threshold_long}: {'✓' if y_val > self.y_threshold_long else '✗'} ({y_val:.3f})")
        print(f"    • 价格低于回归线: {'✓' if price_pos < 0 else '✗'} ({price_pos*100:.2f}%)")
        
        print(f"  买跌条件:")
        print(f"    • Y值<{self.y_threshold_short} 或 X值<{self.x_threshold_short}: {'✓' if (y_val < self.y_threshold_short or x_val < self.x_threshold_short) else '✗'}")
        print(f"    • 价格高于回归线: {'✓' if price_pos > 0 else '✗'} ({price_pos*100:.2f}%)")
        
        # 给出接近条件的提示
        self.suggest_wait_strategy(x_val, y_val, e_val, price_pos)
        
        return {
            'signal': 'WAIT',
            'direction': '观望',
            'action': '等待机会',
            'entry_price': None,
            'stop_loss': None,
            'take_profit': None,
            'risk_reward_ratio': None,
            'confidence': 0
        }
    
    def suggest_wait_strategy(self, x_val, y_val, e_val, price_pos):
        """观望时的策略建议"""
        print(f"\n💡 等待策略建议:")
        
        # 接近买涨条件
        if (e_val > -0.2 and x_val > 0.35 and y_val > 0.35):
            print("  • 接近买涨条件，关注价格是否回落至回归线下方")
        
        # 接近买跌条件
        elif (y_val < 0.35 or x_val < 0.35) and price_pos > -0.02:
            print("  • 接近买跌条件，关注价格是否突破回归线上方")
        
        else:
            print("  • 当前指标偏中性，建议耐心等待明确信号")
        
        print("  • 可设置价格提醒，关注关键价位突破")
    
    def calculate_confidence(self, x_val, y_val, e_val, signal_type):
        """计算信号置信度"""
        if signal_type == 'long':
            # 买涨信号置信度
            confidence = 0
            if e_val > 0.3: confidence += 30
            elif e_val > 0: confidence += 20
            
            if x_val > 0.6: confidence += 25
            elif x_val > 0.5: confidence += 20
            elif x_val > 0.45: confidence += 15
            
            if y_val > 0.6: confidence += 25
            elif y_val > 0.5: confidence += 20
            elif y_val > 0.45: confidence += 15
            
            return min(confidence, 100)
        
        elif signal_type == 'short':
            # 买跌信号置信度
            confidence = 0
            if y_val < 0.2 or x_val < 0.2: confidence += 40
            elif y_val < 0.25 or x_val < 0.25: confidence += 30
            
            if e_val < -0.3: confidence += 30
            elif e_val < 0: confidence += 20
            
            return min(confidence, 100)
        
        return 0

def main():
    """主函数"""
    print("🏦 东亚银行(0023.HK) 入场价格分析器")
    print("="*70)
    
    analyzer = EntryPriceAnalyzer()
    
    # 分析当前入场信号
    result = analyzer.analyze_entry_signals()
    
    if result:
        print(f"\n🎯 最终建议:")
        print(f"  方向: {result['direction']}")
        print(f"  操作: {result['action']}")
        
        if result['signal'] != 'WAIT':
            print(f"  置信度: {result['confidence']:.0f}%")
        
        print(f"\n⚠️  风险提醒:")
        print(f"  • 请根据自身风险承受能力调整仓位")
        print(f"  • 严格执行止盈止损，控制风险")
        print(f"  • 市场有风险，投资需谨慎")

if __name__ == "__main__":
    main()
