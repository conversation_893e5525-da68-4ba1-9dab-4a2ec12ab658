#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
为test表添加毛利润(Profit and Loss)列
==================================

计算毛利润：
- 买涨: 毛利润 = (平仓价 - 开仓价) × 股数
- 买跌: 毛利润 = (开仓价 - 平仓价) × 股数

作者: Cosmoon NG
日期: 2025年7月
"""

import pymysql

class GrossProfitLossCalculator:
    def __init__(self):
        """初始化毛利润计算器"""
        self.db_config = {
            'host': 'localhost',
            'port': 3306,
            'database': 'finance',
            'user': 'root',
            'password': '12345678',
            'charset': 'utf8mb4'
        }
        self.connection = None
        
    def connect_database(self):
        """连接数据库"""
        try:
            self.connection = pymysql.connect(**self.db_config)
            print(f"✅ 成功连接MySQL数据库: {self.db_config['host']}")
            return True
        except Exception as e:
            print(f"❌ 连接数据库失败: {e}")
            return False
    
    def add_gross_profit_loss_column(self):
        """为test表添加毛利润列"""
        try:
            cursor = self.connection.cursor()
            
            # 检查是否已有毛利润列
            cursor.execute("SHOW COLUMNS FROM test LIKE '毛利润'")
            existing_column = cursor.fetchone()
            
            if existing_column:
                print("⚠️ test表已存在毛利润列，将重新计算")
            else:
                # 添加毛利润列
                cursor.execute("""
                    ALTER TABLE test 
                    ADD COLUMN `毛利润` DECIMAL(15,2) DEFAULT 0 
                    COMMENT '毛利润(不含交易成本)'
                """)
                print("✅ 成功为test表添加毛利润列")
            
            self.connection.commit()
            return True
            
        except Exception as e:
            print(f"❌ 添加毛利润列失败: {e}")
            return False
    
    def calculate_gross_profit_loss(self):
        """计算毛利润"""
        try:
            cursor = self.connection.cursor()
            
            print("🧮 计算毛利润...")
            print("📊 公式:")
            print("   • 买涨: 毛利润 = (平仓价 - 开仓价) × 股数")
            print("   • 买跌: 毛利润 = (开仓价 - 平仓价) × 股数")
            
            # 获取所有记录
            cursor.execute("""
                SELECT 交易序号, close, 平仓价格, 交易股数, `控制系数`, `资金流比例`
                FROM test 
                ORDER BY 交易序号
            """)
            
            records = cursor.fetchall()
            
            print(f"\n📊 计算毛利润 (前20条记录):")
            print("-" * 100)
            print(f"{'序号':<4} {'策略区域':<12} {'方向':<6} {'开仓价':<8} {'平仓价':<8} {'股数':<6} {'毛利润':<8}")
            print("-" * 100)
            
            updates = []
            
            for i, record in enumerate(records):
                trade_id, open_price, close_price, shares, y_val, x_val = record
                
                # 确定策略方向
                if y_val > 0.43 and x_val > 0.43:
                    zone = '高值盈利区'
                    direction = '买涨'
                elif 0.333 < y_val < 0.4:
                    zone = '控股商控制区'
                    direction = '观望'
                elif y_val < 0.25 or x_val < 0.25:
                    zone = '强亏损区'
                    direction = '买跌'
                else:
                    zone = '其他区域'
                    direction = '买跌'
                
                # 计算毛利润
                if direction == '观望':
                    gross_profit = 0
                elif direction == '买涨':
                    # 买涨: 毛利润 = (平仓价 - 开仓价) × 股数
                    gross_profit = (close_price - open_price) * shares
                else:  # 买跌
                    # 买跌: 毛利润 = (开仓价 - 平仓价) × 股数
                    gross_profit = (open_price - close_price) * shares
                
                updates.append((gross_profit, trade_id))
                
                # 显示前20条
                if i < 20:
                    print(f"{trade_id:<4} {zone:<12} {direction:<6} {open_price:<8.2f} {close_price:<8.2f} "
                          f"{shares:<6} {gross_profit:<8.0f}")
            
            # 批量更新毛利润
            update_sql = "UPDATE test SET `毛利润` = %s WHERE 交易序号 = %s"
            cursor.executemany(update_sql, updates)
            self.connection.commit()
            
            print(f"\n✅ 成功计算并更新 {len(updates)} 条记录的毛利润")
            return True
            
        except Exception as e:
            print(f"❌ 计算毛利润失败: {e}")
            return False
    
    def verify_gross_profit_calculation(self):
        """验证毛利润计算"""
        try:
            cursor = self.connection.cursor()
            
            # 获取前10条记录验证
            cursor.execute("""
                SELECT 交易序号, close, 平仓价格, 交易股数, `毛利润`, 净利润,
                       `控制系数`, `资金流比例`
                FROM test 
                ORDER BY 交易序号 
                LIMIT 10
            """)
            
            verification_data = cursor.fetchall()
            
            print(f"\n📊 毛利润计算验证 (前10条记录):")
            print("-" * 120)
            print(f"{'序号':<4} {'方向':<6} {'开仓价':<8} {'平仓价':<8} {'股数':<6} {'计算毛利润':<10} "
                  f"{'数据库毛利润':<12} {'净利润':<8} {'差异':<8}")
            print("-" * 120)
            
            for record in verification_data:
                trade_id, open_price, close_price, shares, db_gross_profit, net_profit, y_val, x_val = record
                
                # 确定方向
                if y_val > 0.43 and x_val > 0.43:
                    direction = '买涨'
                elif 0.333 < y_val < 0.4:
                    direction = '观望'
                elif y_val < 0.25 or x_val < 0.25:
                    direction = '买跌'
                else:
                    direction = '买跌'
                
                # 手工计算毛利润
                if direction == '观望':
                    manual_gross_profit = 0
                elif direction == '买涨':
                    manual_gross_profit = (close_price - open_price) * shares
                else:  # 买跌
                    manual_gross_profit = (open_price - close_price) * shares
                
                difference = abs(float(db_gross_profit) - manual_gross_profit)
                
                print(f"{trade_id:<4} {direction:<6} {open_price:<8.2f} {close_price:<8.2f} {shares:<6} "
                      f"{manual_gross_profit:<10.0f} {float(db_gross_profit):<12.0f} {int(net_profit):<8} {difference:<8.2f}")
            
            # 统计信息
            cursor.execute("""
                SELECT 
                    COUNT(*) as total_records,
                    SUM(`毛利润`) as total_gross_profit,
                    SUM(净利润) as total_net_profit,
                    AVG(`毛利润`) as avg_gross_profit,
                    MIN(`毛利润`) as min_gross_profit,
                    MAX(`毛利润`) as max_gross_profit
                FROM test
            """)
            
            stats = cursor.fetchone()
            total, total_gross, total_net, avg_gross, min_gross, max_gross = stats
            
            print(f"\n📈 毛利润统计信息:")
            print(f"   • 总记录数: {total}")
            print(f"   • 总毛利润: {float(total_gross):+,.0f}港币")
            print(f"   • 总净利润: {float(total_net):+,.0f}港币")
            print(f"   • 交易成本: {float(total_gross) - float(total_net):+,.0f}港币")
            print(f"   • 平均毛利润: {float(avg_gross):+.0f}港币")
            print(f"   • 毛利润范围: {float(min_gross):+.0f} 至 {float(max_gross):+.0f}港币")
            
            return True
            
        except Exception as e:
            print(f"❌ 验证毛利润计算失败: {e}")
            return False
    
    def analyze_gross_profit_by_strategy(self):
        """按策略分析毛利润"""
        try:
            cursor = self.connection.cursor()
            
            print(f"\n📊 按策略区域分析毛利润:")
            print("-" * 80)
            
            # 按策略区域分析
            zones = [
                ('高值盈利区', '买涨', "`控制系数` > 0.43 AND `资金流比例` > 0.43"),
                ('强亏损区', '买跌', "(`控制系数` < 0.25 OR `资金流比例` < 0.25) AND NOT (`控制系数` > 0.333 AND `控制系数` < 0.4)"),
                ('其他区域', '买跌', """NOT (`控制系数` > 0.43 AND `资金流比例` > 0.43) 
                                       AND NOT (`控制系数` > 0.333 AND `控制系数` < 0.4) 
                                       AND NOT (`控制系数` < 0.25 OR `资金流比例` < 0.25)"""),
                ('控股商控制区', '观望', "`控制系数` > 0.333 AND `控制系数` < 0.4")
            ]
            
            for zone_name, direction, where_clause in zones:
                cursor.execute(f"""
                    SELECT 
                        COUNT(*) as count,
                        SUM(`毛利润`) as total_gross_profit,
                        SUM(净利润) as total_net_profit,
                        AVG(`毛利润`) as avg_gross_profit,
                        SUM(CASE WHEN `毛利润` > 0 THEN 1 ELSE 0 END) as gross_wins
                    FROM test 
                    WHERE {where_clause}
                """)
                
                result = cursor.fetchone()
                count, total_gross, total_net, avg_gross, gross_wins = result
                
                if count > 0:
                    gross_win_rate = gross_wins / count * 100
                    transaction_cost = float(total_gross) - float(total_net)
                    
                    print(f"\n🎯 {zone_name} - {direction}:")
                    print(f"   • 交易次数: {count}")
                    print(f"   • 总毛利润: {float(total_gross):+,.0f}港币")
                    print(f"   • 总净利润: {float(total_net):+,.0f}港币")
                    print(f"   • 交易成本: {transaction_cost:+,.0f}港币")
                    print(f"   • 平均毛利润: {float(avg_gross):+.0f}港币")
                    print(f"   • 毛利润胜率: {gross_win_rate:.1f}%")
            
            return True
            
        except Exception as e:
            print(f"❌ 按策略分析毛利润失败: {e}")
            return False
    
    def close_connection(self):
        """关闭数据库连接"""
        if self.connection:
            self.connection.close()
            print("\n🔒 数据库连接已关闭")

def main():
    """主函数"""
    print("📊 为test表添加并计算毛利润(Profit and Loss)")
    print("="*60)
    print("🔍 毛利润计算公式:")
    print("   • 买涨: 毛利润 = (平仓价 - 开仓价) × 股数")
    print("   • 买跌: 毛利润 = (开仓价 - 平仓价) × 股数")
    print("   • 观望: 毛利润 = 0")
    
    # 创建计算器
    calculator = GrossProfitLossCalculator()
    
    # 连接数据库
    if not calculator.connect_database():
        return
    
    # 添加毛利润列
    if not calculator.add_gross_profit_loss_column():
        calculator.close_connection()
        return
    
    # 计算毛利润
    if not calculator.calculate_gross_profit_loss():
        calculator.close_connection()
        return
    
    # 验证计算结果
    calculator.verify_gross_profit_calculation()
    
    # 按策略分析毛利润
    calculator.analyze_gross_profit_by_strategy()
    
    # 关闭连接
    calculator.close_connection()
    
    print(f"\n🎉 毛利润计算完成!")
    print("💡 现在test表包含毛利润列，可以分析交易成本影响")

if __name__ == "__main__":
    main()
