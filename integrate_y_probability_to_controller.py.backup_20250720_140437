#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
将y_probability集成到sp_updatecontroller
=====================================
保留并集成y_probability的计算逻辑到数据库存储过程中
"""

import mysql.connector
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

class YProbabilityIntegrator:
    def __init__(self):
        """初始化数据库连接"""
        self.db_config = {
            'host': '************',
            'database': 'finance',
            'user': 'root',
            'password': '',
            'charset': 'utf8mb4',
            'autocommit': True
        }
        self.connection = None
        self.cursor = None

    def connect_database(self):
        """连接数据库"""
        try:
            print("🔗 连接MySQL数据库...")
            self.connection = mysql.connector.connect(**self.db_config)
            self.cursor = self.connection.cursor()
            print(f"✅ 成功连接到数据库: {self.db_config['host']}/{self.db_config['database']}")
            return True
        except mysql.connector.Error as e:
            print(f"❌ 数据库连接失败: {e}")
            return False

    def analyze_y_probability_formula(self):
        """分析y_probability的计算公式"""
        print("\n📊 分析y_probability计算公式...")
        print("=" * 60)

        print("🧮 y_probability计算公式 (基于多个策略文件分析):")
        print("")
        print("1️⃣ **基础Y值计算**:")
        print("   price_vs_ma20 = close / ma_20")
        print("   base_y = if price_vs_ma20 >= 1:")
        print("              0.5 + 0.4 * tanh((price_vs_ma20 - 1) * 3)")
        print("            else:")
        print("              0.5 - 0.4 * tanh((1 - price_vs_ma20) * 3)")
        print("")
        print("2️⃣ **趋势调整**:")
        print("   ma_trend = ma_20 / ma_60")
        print("   trend_adjustment = 0.1 * tanh((ma_trend - 1) * 2)")
        print("")
        print("3️⃣ **成交量调整**:")
        print("   volume_ratio = volume / avg_volume_20")
        print("   volume_adjustment = 0.05 * tanh((volume_ratio - 1))")
        print("")
        print("4️⃣ **最终y_probability**:")
        print("   y_probability = base_y + trend_adjustment + volume_adjustment")
        print("   y_probability = clip(y_probability, 0.1, 0.9)")
        print("")
        print("💡 **含义**:")
        print("   • y_probability表示控股商托价的概率")
        print("   • 范围: 0.1 ~ 0.9")
        print("   • >0.5: 倾向于托价 (看涨)")
        print("   • <0.5: 倾向于压价 (看跌)")
        print("   • =0.5: 中性")

        return True

    def backup_current_controller(self):
        """备份当前的sp_updatecontroller"""
        try:
            print("\n🔄 备份当前sp_updatecontroller...")

            # 检查存储过程是否存在
            self.cursor.execute("""
                SELECT COUNT(*) FROM information_schema.ROUTINES
                WHERE ROUTINE_SCHEMA = 'finance'
                AND ROUTINE_NAME = 'sp_updatecontroller'
            """)

            exists = self.cursor.fetchone()[0]

            if exists > 0:
                # 获取当前存储过程定义
                self.cursor.execute("SHOW CREATE PROCEDURE sp_updatecontroller")
                result = self.cursor.fetchone()

                if result:
                    # 创建备份
                    backup_name = f"sp_updatecontroller_backup_y_prob_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
                    backup_sql = result[2].replace(
                        'CREATE DEFINER=`root`@`%` PROCEDURE `sp_updatecontroller`',
                        f'CREATE PROCEDURE `{backup_name}`'
                    ).replace(
                        'CREATE PROCEDURE `sp_updatecontroller`',
                        f'CREATE PROCEDURE `{backup_name}`'
                    )

                    self.cursor.execute(backup_sql)
                    print(f"✅ 已备份为: {backup_name}")

                    return True
                else:
                    print("❌ 无法获取当前存储过程定义")
                    return False
            else:
                print("⚠️ sp_updatecontroller不存在，将直接创建新的")
                return True

        except mysql.connector.Error as e:
            print(f"❌ 备份检查失败: {e}")
            return False

    def create_enhanced_controller_with_y_probability(self):
        """创建集成y_probability的增强版sp_updatecontroller"""
        try:
            print("\n🔧 创建集成y_probability的sp_updatecontroller...")

            # 删除现有的存储过程
            self.cursor.execute("DROP PROCEDURE IF EXISTS sp_updatecontroller")

            # 创建集成y_probability的存储过程
            enhanced_procedure = """
CREATE PROCEDURE sp_updatecontroller(
    IN tablename VARCHAR(64),
    OUT result_k DECIMAL(20,6)
)
BEGIN
    DECLARE col_exists INT DEFAULT 0;
    DECLARE v_start_time DATETIME DEFAULT NOW();

    -- 1. 首先更新midprice (调用sp_averagelineV3)
    SELECT CONCAT('开始更新midprice - 表: ', tablename, ' 时间: ', v_start_time) AS start_message;
    CALL sp_averagelineV3(tablename);
    SELECT 'midprice更新完成' AS midprice_status;

    -- 2. 检查并添加必要的列
    -- 检查controller列
    SET @sql = CONCAT(
        'SELECT COUNT(*) INTO @col_exists FROM information_schema.COLUMNS ',
        'WHERE TABLE_SCHEMA=DATABASE() AND TABLE_NAME=''', tablename, ''' AND COLUMN_NAME=''controller'''
    );
    PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;
    SET col_exists = @col_exists;

    IF col_exists = 0 THEN
        SET @sql = CONCAT('ALTER TABLE `', tablename, '` ADD COLUMN `controller` INT DEFAULT NULL');
        PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;
        SELECT 'controller列已添加' AS controller_status;
    END IF;

    -- 检查Full_Y列
    SET @sql = CONCAT(
        'SELECT COUNT(*) INTO @col_exists FROM information_schema.COLUMNS ',
        'WHERE TABLE_SCHEMA=DATABASE() AND TABLE_NAME=''', tablename, ''' AND COLUMN_NAME=''Full_Y'''
    );
    PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;
    SET col_exists = @col_exists;

    IF col_exists = 0 THEN
        SET @sql = CONCAT('ALTER TABLE `', tablename, '` ADD COLUMN `Full_Y` DECIMAL(20,10) DEFAULT NULL');
        PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;
        SELECT 'Full_Y列已添加' AS full_y_status;
    END IF;

    -- 检查y_probability列
    SET @sql = CONCAT(
        'SELECT COUNT(*) INTO @col_exists FROM information_schema.COLUMNS ',
        'WHERE TABLE_SCHEMA=DATABASE() AND TABLE_NAME=''', tablename, ''' AND COLUMN_NAME=''y_probability'''
    );
    PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;
    SET col_exists = @col_exists;

    IF col_exists = 0 THEN
        SET @sql = CONCAT('ALTER TABLE `', tablename, '` ADD COLUMN `y_probability` DECIMAL(10,6) DEFAULT NULL');
        PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;
        SELECT 'y_probability列已添加' AS y_prob_status;
    END IF;

    -- 检查ma_20列
    SET @sql = CONCAT(
        'SELECT COUNT(*) INTO @col_exists FROM information_schema.COLUMNS ',
        'WHERE TABLE_SCHEMA=DATABASE() AND TABLE_NAME=''', tablename, ''' AND COLUMN_NAME=''ma_20'''
    );
    PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;
    SET col_exists = @col_exists;

    IF col_exists = 0 THEN
        SET @sql = CONCAT('ALTER TABLE `', tablename, '` ADD COLUMN `ma_20` DECIMAL(20,6) DEFAULT NULL');
        PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;
        SELECT 'ma_20列已添加' AS ma20_status;
    END IF;

    -- 检查ma_60列
    SET @sql = CONCAT(
        'SELECT COUNT(*) INTO @col_exists FROM information_schema.COLUMNS ',
        'WHERE TABLE_SCHEMA=DATABASE() AND TABLE_NAME=''', tablename, ''' AND COLUMN_NAME=''ma_60'''
    );
    PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;
    SET col_exists = @col_exists;

    IF col_exists = 0 THEN
        SET @sql = CONCAT('ALTER TABLE `', tablename, '` ADD COLUMN `ma_60` DECIMAL(20,6) DEFAULT NULL');
        PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;
        SELECT 'ma_60列已添加' AS ma60_status;
    END IF;

    -- 3. 计算移动平均线
    SELECT '开始计算移动平均线...' AS ma_calc_status;

    -- 计算MA20
    SET @sql = CONCAT(
        'UPDATE `', tablename, '` t1 ',
        'JOIN (',
        '  SELECT date, ',
        '    AVG(close) OVER (ORDER BY date ASC ROWS 19 PRECEDING) as ma20_value ',
        '  FROM `', tablename, '` ',
        '  ORDER BY date ASC',
        ') t2 ON t1.date = t2.date ',
        'SET t1.ma_20 = t2.ma20_value'
    );
    PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;

    -- 计算MA60
    SET @sql = CONCAT(
        'UPDATE `', tablename, '` t1 ',
        'JOIN (',
        '  SELECT date, ',
        '    AVG(close) OVER (ORDER BY date ASC ROWS 59 PRECEDING) as ma60_value ',
        '  FROM `', tablename, '` ',
        '  ORDER BY date ASC',
        ') t2 ON t1.date = t2.date ',
        'SET t1.ma_60 = t2.ma60_value'
    );
    PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;

    SELECT '移动平均线计算完成' AS ma_complete_status;

    -- 4. 计算y_probability (简化版本)
    SELECT '开始计算y_probability...' AS y_prob_calc_status;

    -- 先计算基础y_probability (简化版本，避免复杂的子查询)
    SET @sql = CONCAT(
        'UPDATE `', tablename, '` ',
        'SET y_probability = CASE ',
        '  WHEN ma_20 > 0 THEN ',
        '    GREATEST(0.1, LEAST(0.9, ',
        '      CASE WHEN (close / ma_20) >= 1 THEN ',
        '        0.5 + 0.4 * ((close / ma_20 - 1) / (1 + ABS(close / ma_20 - 1))) ',
        '      ELSE ',
        '        0.5 - 0.4 * ((1 - close / ma_20) / (1 + ABS(1 - close / ma_20))) ',
        '      END ',
        '    )) ',
        '  ELSE 0.5 ',
        'END ',
        'WHERE ma_20 IS NOT NULL AND close IS NOT NULL'
    );
    PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;

    -- 添加趋势调整
    SET @sql = CONCAT(
        'UPDATE `', tablename, '` ',
        'SET y_probability = GREATEST(0.1, LEAST(0.9, ',
        '  y_probability + CASE ',
        '    WHEN ma_20 > 0 AND ma_60 > 0 THEN ',
        '      0.1 * ((ma_20 / ma_60 - 1) / (1 + ABS(ma_20 / ma_60 - 1))) ',
        '    ELSE 0 ',
        '  END ',
        ')) ',
        'WHERE ma_20 IS NOT NULL AND ma_60 IS NOT NULL AND y_probability IS NOT NULL'
    );
    PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;

    SELECT 'y_probability计算完成' AS y_prob_complete_status;

    -- 5. 更新controller字段 (二元逻辑)
    SELECT '开始计算controller (二元逻辑)...' AS controller_calc_status;

    SET @sql = CONCAT(
        'UPDATE `', tablename, '` ',
        'SET controller = CASE WHEN (close - midprice) > 0 THEN 1 ELSE 0 END ',
        'WHERE midprice IS NOT NULL'
    );
    PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;

    SELECT 'controller二元逻辑计算完成' AS controller_complete_status;

    -- 6. 计算Full_Y字段 (累积controller计数 / 行号)
    SELECT '开始计算Full_Y (累积比例)...' AS full_y_calc_status;

    SET @sql = CONCAT(
        'UPDATE `', tablename, '` t1 ',
        'JOIN (',
        '  SELECT date, ',
        '    SUM(controller) OVER (ORDER BY date ASC ROWS UNBOUNDED PRECEDING) as cumulative_count, ',
        '    ROW_NUMBER() OVER (ORDER BY date ASC) as row_num ',
        '  FROM `', tablename, '` ',
        '  WHERE controller IS NOT NULL ',
        '  ORDER BY date ASC',
        ') t2 ON t1.date = t2.date ',
        'SET t1.Full_Y = t2.cumulative_count / t2.row_num'
    );
    PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;

    SELECT 'Full_Y累积比例计算完成' AS full_y_complete_status;

    -- 7. 计算k值 (controller=1的比例)
    SET @sql = CONCAT(
        'SELECT SUM(controller) / COUNT(*) INTO @k_value FROM `', tablename, '` WHERE controller IS NOT NULL'
    );
    PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;

    SET result_k = @k_value;

    -- 8. 返回统计信息
    SELECT
        CONCAT('更新完成 - 表: ', tablename) AS completion_message,
        CONCAT('k值: ', IFNULL(result_k, 0)) AS k_value_message,
        CONCAT('耗时: ', TIMESTAMPDIFF(SECOND, v_start_time, NOW()), ' 秒') AS duration;

END
            """

            self.cursor.execute(enhanced_procedure)
            print("✅ 成功创建集成y_probability的sp_updatecontroller")

            # 保存新的存储过程定义到文件
            with open('sp_updatecontroller_with_y_probability.sql', 'w', encoding='utf-8') as f:
                f.write(enhanced_procedure)
            print("📄 集成y_probability的定义已保存到 sp_updatecontroller_with_y_probability.sql")

            return True

        except mysql.connector.Error as e:
            print(f"❌ 创建集成y_probability的存储过程失败: {e}")
            return False

    def test_enhanced_controller(self, table_name):
        """测试集成y_probability的控制器"""
        try:
            print(f"\n🧪 测试集成y_probability的sp_updatecontroller - 表: {table_name}")

            # 调用存储过程
            args = [table_name, 0]  # 第二个参数是OUT参数的占位符
            result = self.cursor.callproc('sp_updatecontroller', args)

            # 获取结果集
            print("📊 sp_updatecontroller执行过程:")
            for result_set in self.cursor.stored_results():
                rows = result_set.fetchall()
                for row in rows:
                    print(f"   {row}")

            # 显示OUT参数结果
            k_value = result[1]  # OUT参数result_k的值
            print(f"📊 k值结果: {k_value}")

            return True

        except mysql.connector.Error as e:
            print(f"❌ 测试集成y_probability的控制器失败: {e}")
            return False

    def verify_y_probability_integration(self, table_name):
        """验证y_probability集成结果"""
        try:
            print(f"\n🔍 验证y_probability集成结果 - 表: {table_name}")

            # 检查数据完整性
            self.cursor.execute(f"""
                SELECT
                    COUNT(*) as total_rows,
                    COUNT(y_probability) as y_prob_count,
                    COUNT(Full_Y) as full_y_count,
                    COUNT(controller) as controller_count,
                    COUNT(ma_20) as ma20_count,
                    COUNT(ma_60) as ma60_count,
                    MIN(y_probability) as min_y_prob,
                    MAX(y_probability) as max_y_prob,
                    AVG(y_probability) as avg_y_prob
                FROM {table_name}
            """)

            stats = self.cursor.fetchone()
            total_rows = stats[0]
            y_prob_count = stats[1]
            full_y_count = stats[2]
            controller_count = stats[3]
            ma20_count = stats[4]
            ma60_count = stats[5]
            min_y_prob = float(stats[6]) if stats[6] is not None else 0.0
            max_y_prob = float(stats[7]) if stats[7] is not None else 0.0
            avg_y_prob = float(stats[8]) if stats[8] is not None else 0.0

            print(f"📊 集成验证统计:")
            print(f"   • 总记录数: {total_rows}")
            print(f"   • y_probability记录数: {y_prob_count}")
            print(f"   • Full_Y记录数: {full_y_count}")
            print(f"   • controller记录数: {controller_count}")
            print(f"   • ma_20记录数: {ma20_count}")
            print(f"   • ma_60记录数: {ma60_count}")
            print(f"   • y_probability范围: {min_y_prob:.6f} ~ {max_y_prob:.6f}")
            print(f"   • y_probability平均值: {avg_y_prob:.6f}")

            # 显示最新20行数据
            self.cursor.execute(f"""
                SELECT
                    date,
                    close,
                    ma_20,
                    ma_60,
                    midprice,
                    y_probability,
                    controller,
                    Full_Y
                FROM {table_name}
                WHERE y_probability IS NOT NULL
                ORDER BY date DESC
                LIMIT 20
            """)

            latest_results = self.cursor.fetchall()
            print(f"\n📊 最新20条数据 (包含y_probability):")
            print("日期          | 收盘价  | MA20    | MA60    | y_prob  | controller | Full_Y")
            print("-" * 85)
            for row in latest_results:
                date_str = str(row[0])
                close_val = float(row[1]) if row[1] is not None else 0.0
                ma20_val = float(row[2]) if row[2] is not None else 0.0
                ma60_val = float(row[3]) if row[3] is not None else 0.0
                y_prob_val = float(row[5]) if row[5] is not None else 0.0
                controller_val = row[6] if row[6] is not None else 0
                full_y_val = float(row[7]) if row[7] is not None else 0.0
                print(f"{date_str} | {close_val:7.2f} | {ma20_val:7.2f} | {ma60_val:7.2f} | {y_prob_val:7.4f} | {controller_val:10d} | {full_y_val:10.6f}")

            # 分析y_probability分布
            self.cursor.execute(f"""
                SELECT
                    CASE
                        WHEN y_probability < 0.3 THEN '看跌(<0.3)'
                        WHEN y_probability < 0.4 THEN '偏跌(0.3-0.4)'
                        WHEN y_probability < 0.6 THEN '中性(0.4-0.6)'
                        WHEN y_probability < 0.7 THEN '偏涨(0.6-0.7)'
                        ELSE '看涨(>0.7)'
                    END as y_prob_range,
                    COUNT(*) as count,
                    ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM {table_name} WHERE y_probability IS NOT NULL), 2) as percentage
                FROM {table_name}
                WHERE y_probability IS NOT NULL
                GROUP BY 1
                ORDER BY MIN(y_probability)
            """)

            y_prob_distribution = self.cursor.fetchall()
            print(f"\n📊 y_probability分布分析:")
            print("范围           | 数量   | 百分比 | 含义")
            print("-" * 45)
            for row in y_prob_distribution:
                range_name = row[0]
                count = row[1]
                percentage = float(row[2])
                print(f"{range_name:15s} | {count:6d} | {percentage:6.2f}% | 控股商倾向")

            # 验证y_probability与controller的关系
            self.cursor.execute(f"""
                SELECT
                    controller,
                    COUNT(*) as count,
                    AVG(y_probability) as avg_y_prob,
                    MIN(y_probability) as min_y_prob,
                    MAX(y_probability) as max_y_prob
                FROM {table_name}
                WHERE y_probability IS NOT NULL AND controller IS NOT NULL
                GROUP BY controller
                ORDER BY controller
            """)

            controller_y_prob = self.cursor.fetchall()
            print(f"\n📊 controller与y_probability关系:")
            print("controller | 数量   | 平均y_prob | 最小y_prob | 最大y_prob")
            print("-" * 60)
            for row in controller_y_prob:
                controller_val = row[0]
                count = row[1]
                avg_y_prob = float(row[2]) if row[2] is not None else 0.0
                min_y_prob = float(row[3]) if row[3] is not None else 0.0
                max_y_prob = float(row[4]) if row[4] is not None else 0.0
                print(f"{controller_val:10d} | {count:6d} | {avg_y_prob:10.6f} | {min_y_prob:10.6f} | {max_y_prob:10.6f}")

            if y_prob_count > 0:
                print("\n✅ y_probability集成验证成功!")
                return True
            else:
                print("\n❌ y_probability集成验证失败!")
                return False

        except mysql.connector.Error as e:
            print(f"❌ 验证y_probability集成失败: {e}")
            return False

    def close_connection(self):
        """关闭数据库连接"""
        if self.cursor:
            self.cursor.close()
        if self.connection:
            self.connection.close()
        print("🔒 数据库连接已关闭")

    def run(self, table_name='stock_600887_ss'):
        """执行主流程"""
        print("🎯 将y_probability集成到sp_updatecontroller")
        print("=" * 60)
        print(f"📅 执行时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"🏠 数据库: {self.db_config['host']}/{self.db_config['database']}")
        print(f"📊 测试表: {table_name}")

        try:
            # 1. 连接数据库
            if not self.connect_database():
                return False

            # 2. 分析y_probability公式
            self.analyze_y_probability_formula()

            # 3. 备份当前存储过程
            if not self.backup_current_controller():
                return False

            # 4. 创建集成y_probability的存储过程
            if not self.create_enhanced_controller_with_y_probability():
                return False

            # 5. 测试集成后的控制器
            if not self.test_enhanced_controller(table_name):
                return False

            # 6. 验证y_probability集成
            if not self.verify_y_probability_integration(table_name):
                return False

            print("\n🎉 y_probability成功集成到sp_updatecontroller!")
            print("💡 现在sp_updatecontroller包含完整的博弈论分析指标:")
            print("   • midprice: 中值价格")
            print("   • controller: 二元强弱判断")
            print("   • Full_Y: 累积强势比例")
            print("   • y_probability: 控股商托价概率")
            print("   • ma_20, ma_60: 移动平均线")
            print("📝 使用方法: CALL sp_updatecontroller('table_name', @k_value);")

            return True

        except Exception as e:
            print(f"❌ 执行失败: {e}")
            return False

        finally:
            self.close_connection()

def main():
    """主函数"""
    integrator = YProbabilityIntegrator()
    success = integrator.run('stock_600887_ss')

    if success:
        print("\n✅ y_probability集成完成!")
        print("📝 现在拥有完整的博弈论交易分析工具")
    else:
        print("\n❌ y_probability集成失败!")

if __name__ == "__main__":
    main()
