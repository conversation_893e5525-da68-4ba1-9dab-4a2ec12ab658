#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
为hkhsi50表添加y_probability字段
==============================
为hkhsi50表添加y_probability及相关技术指标
"""

import mysql.connector
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

class HKHsi50YProbabilityAdder:
    def __init__(self):
        """初始化数据库连接"""
        self.db_config = {
            'host': '************',
            'database': 'finance',
            'user': 'root',
            'password': '',
            'charset': 'utf8mb4',
            'autocommit': True
        }
        self.connection = None
        self.cursor = None
        self.table_name = 'hkhsi50'
        
    def connect_database(self):
        """连接数据库"""
        try:
            print("🔗 连接MySQL数据库...")
            self.connection = mysql.connector.connect(**self.db_config)
            self.cursor = self.connection.cursor()
            print(f"✅ 成功连接到数据库: {self.db_config['host']}/{self.db_config['database']}")
            return True
        except mysql.connector.Error as e:
            print(f"❌ 数据库连接失败: {e}")
            return False
    
    def check_table_status(self):
        """检查hkhsi50表的当前状态"""
        try:
            print(f"\n📊 检查{self.table_name}表状态...")
            
            # 检查表是否存在
            self.cursor.execute(f"""
                SELECT COUNT(*) FROM information_schema.tables 
                WHERE table_schema = 'finance' AND table_name = '{self.table_name}'
            """)
            
            if self.cursor.fetchone()[0] == 0:
                print(f"❌ 表 {self.table_name} 不存在")
                return False
            
            # 检查表结构
            self.cursor.execute(f"DESCRIBE {self.table_name}")
            columns = self.cursor.fetchall()
            
            existing_columns = {}
            print(f"📋 {self.table_name} 表结构:")
            for col in columns:
                col_name = col[0]
                col_type = col[1]
                existing_columns[col_name] = col_type
                print(f"   • {col_name} - {col_type}")
            
            # 检查数据量
            self.cursor.execute(f"""
                SELECT 
                    COUNT(*) as total_rows,
                    MIN(date) as min_date,
                    MAX(date) as max_date
                FROM {self.table_name}
            """)
            
            stats = self.cursor.fetchone()
            total_rows = stats[0]
            min_date = stats[1]
            max_date = stats[2]
            
            print(f"\n📊 数据统计:")
            print(f"   • 总记录数: {total_rows}")
            print(f"   • 日期范围: {min_date} ~ {max_date}")
            
            # 检查现有的技术指标字段
            required_fields = ['y_probability', 'ma_20', 'ma_60', 'controller', 'Full_Y', 'midprice']
            missing_fields = []
            existing_fields = []
            
            for field in required_fields:
                if field in existing_columns:
                    existing_fields.append(field)
                else:
                    missing_fields.append(field)
            
            if existing_fields:
                print(f"✅ 已存在字段: {', '.join(existing_fields)}")
            if missing_fields:
                print(f"⚠️ 缺少字段: {', '.join(missing_fields)}")
            
            return True
            
        except mysql.connector.Error as e:
            print(f"❌ 检查表状态失败: {e}")
            return False
    
    def add_missing_columns(self):
        """添加缺失的列"""
        try:
            print(f"\n🔧 为{self.table_name}添加缺失的列...")
            
            # 需要添加的列定义
            columns_to_add = [
                ('y_probability', 'DECIMAL(10,6) DEFAULT NULL', 'y_probability列已添加'),
                ('ma_20', 'DECIMAL(20,6) DEFAULT NULL', 'ma_20列已添加'),
                ('ma_60', 'DECIMAL(20,6) DEFAULT NULL', 'ma_60列已添加'),
                ('controller', 'INT DEFAULT NULL', 'controller列已添加'),
                ('Full_Y', 'DECIMAL(20,10) DEFAULT NULL', 'Full_Y列已添加'),
                ('midprice', 'DECIMAL(20,6) DEFAULT NULL', 'midprice列已添加')
            ]
            
            added_count = 0
            for col_name, col_definition, success_msg in columns_to_add:
                # 检查列是否已存在
                self.cursor.execute(f"""
                    SELECT COUNT(*) FROM information_schema.COLUMNS 
                    WHERE TABLE_SCHEMA = 'finance' 
                    AND TABLE_NAME = '{self.table_name}' 
                    AND COLUMN_NAME = '{col_name}'
                """)
                
                if self.cursor.fetchone()[0] == 0:
                    # 添加列
                    alter_sql = f"ALTER TABLE {self.table_name} ADD COLUMN {col_name} {col_definition}"
                    self.cursor.execute(alter_sql)
                    print(f"   ✅ {success_msg}")
                    added_count += 1
                else:
                    print(f"   ⚠️ {col_name}列已存在，跳过")
            
            print(f"📊 总共添加了 {added_count} 个新列")
            return True
            
        except mysql.connector.Error as e:
            print(f"❌ 添加列失败: {e}")
            return False
    
    def update_midprice(self):
        """更新midprice字段"""
        try:
            print(f"\n🔄 更新{self.table_name}的midprice...")
            
            # 调用sp_averagelineV3更新midprice
            self.cursor.callproc('sp_averagelineV3', [self.table_name])
            
            # 消费结果集
            for result_set in self.cursor.stored_results():
                rows = result_set.fetchall()
                for row in rows:
                    print(f"📊 {row}")
            
            print("✅ midprice更新完成")
            return True
            
        except mysql.connector.Error as e:
            print(f"❌ 更新midprice失败: {e}")
            return False
    
    def calculate_moving_averages(self):
        """计算移动平均线"""
        try:
            print(f"\n📈 计算{self.table_name}的移动平均线...")
            
            # 计算MA20
            print("   📊 计算MA20...")
            self.cursor.execute(f"""
                UPDATE {self.table_name} t1 
                JOIN (
                    SELECT date, 
                        AVG(close) OVER (ORDER BY date ASC ROWS 19 PRECEDING) as ma20_value 
                    FROM {self.table_name} 
                    ORDER BY date ASC
                ) t2 ON t1.date = t2.date 
                SET t1.ma_20 = t2.ma20_value
            """)
            print("   ✅ MA20计算完成")
            
            # 计算MA60
            print("   📊 计算MA60...")
            self.cursor.execute(f"""
                UPDATE {self.table_name} t1 
                JOIN (
                    SELECT date, 
                        AVG(close) OVER (ORDER BY date ASC ROWS 59 PRECEDING) as ma60_value 
                    FROM {self.table_name} 
                    ORDER BY date ASC
                ) t2 ON t1.date = t2.date 
                SET t1.ma_60 = t2.ma60_value
            """)
            print("   ✅ MA60计算完成")
            
            return True
            
        except mysql.connector.Error as e:
            print(f"❌ 计算移动平均线失败: {e}")
            return False
    
    def calculate_y_probability(self):
        """计算y_probability"""
        try:
            print(f"\n🧮 计算{self.table_name}的y_probability...")
            
            # 基础y_probability计算
            print("   📊 计算基础y_probability...")
            self.cursor.execute(f"""
                UPDATE {self.table_name} 
                SET y_probability = CASE 
                  WHEN ma_20 > 0 THEN 
                    GREATEST(0.1, LEAST(0.9, 
                      CASE WHEN (close / ma_20) >= 1 THEN 
                        0.5 + 0.4 * ((close / ma_20 - 1) / (1 + ABS(close / ma_20 - 1))) 
                      ELSE 
                        0.5 - 0.4 * ((1 - close / ma_20) / (1 + ABS(1 - close / ma_20))) 
                      END 
                    )) 
                  ELSE 0.5 
                END 
                WHERE ma_20 IS NOT NULL AND close IS NOT NULL
            """)
            print("   ✅ 基础y_probability计算完成")
            
            # 添加趋势调整
            print("   📊 添加趋势调整...")
            self.cursor.execute(f"""
                UPDATE {self.table_name} 
                SET y_probability = GREATEST(0.1, LEAST(0.9, 
                  y_probability + CASE 
                    WHEN ma_20 > 0 AND ma_60 > 0 THEN 
                      0.1 * ((ma_20 / ma_60 - 1) / (1 + ABS(ma_20 / ma_60 - 1))) 
                    ELSE 0 
                  END 
                )) 
                WHERE ma_20 IS NOT NULL AND ma_60 IS NOT NULL AND y_probability IS NOT NULL
            """)
            print("   ✅ 趋势调整完成")
            
            return True
            
        except mysql.connector.Error as e:
            print(f"❌ 计算y_probability失败: {e}")
            return False
    
    def calculate_controller_and_full_y(self):
        """计算controller和Full_Y"""
        try:
            print(f"\n📊 计算{self.table_name}的controller和Full_Y...")
            
            # 计算controller (二元逻辑)
            print("   📊 计算controller...")
            self.cursor.execute(f"""
                UPDATE {self.table_name} 
                SET controller = CASE WHEN (close - midprice) > 0 THEN 1 ELSE 0 END 
                WHERE midprice IS NOT NULL
            """)
            print("   ✅ controller计算完成")
            
            # 计算Full_Y (累积比例)
            print("   📊 计算Full_Y...")
            self.cursor.execute(f"""
                UPDATE {self.table_name} t1 
                JOIN (
                    SELECT date, 
                        SUM(controller) OVER (ORDER BY date ASC ROWS UNBOUNDED PRECEDING) as cumulative_count, 
                        ROW_NUMBER() OVER (ORDER BY date ASC) as row_num 
                    FROM {self.table_name} 
                    WHERE controller IS NOT NULL 
                    ORDER BY date ASC
                ) t2 ON t1.date = t2.date 
                SET t1.Full_Y = t2.cumulative_count / t2.row_num
            """)
            print("   ✅ Full_Y计算完成")
            
            return True
            
        except mysql.connector.Error as e:
            print(f"❌ 计算controller和Full_Y失败: {e}")
            return False
    
    def verify_results(self):
        """验证计算结果"""
        try:
            print(f"\n🔍 验证{self.table_name}的计算结果...")
            
            # 检查数据完整性
            self.cursor.execute(f"""
                SELECT 
                    COUNT(*) as total_rows,
                    COUNT(y_probability) as y_prob_count,
                    COUNT(Full_Y) as full_y_count,
                    COUNT(controller) as controller_count,
                    COUNT(ma_20) as ma20_count,
                    COUNT(ma_60) as ma60_count,
                    COUNT(midprice) as midprice_count,
                    MIN(y_probability) as min_y_prob,
                    MAX(y_probability) as max_y_prob,
                    AVG(y_probability) as avg_y_prob,
                    SUM(controller) as controller_1_count
                FROM {self.table_name}
            """)
            
            stats = self.cursor.fetchone()
            total_rows = stats[0]
            y_prob_count = stats[1]
            full_y_count = stats[2]
            controller_count = stats[3]
            ma20_count = stats[4]
            ma60_count = stats[5]
            midprice_count = stats[6]
            min_y_prob = float(stats[7]) if stats[7] is not None else 0.0
            max_y_prob = float(stats[8]) if stats[8] is not None else 0.0
            avg_y_prob = float(stats[9]) if stats[9] is not None else 0.0
            controller_1_count = stats[10] if stats[10] is not None else 0
            
            print(f"📊 验证统计:")
            print(f"   • 总记录数: {total_rows}")
            print(f"   • y_probability记录数: {y_prob_count}")
            print(f"   • Full_Y记录数: {full_y_count}")
            print(f"   • controller记录数: {controller_count}")
            print(f"   • ma_20记录数: {ma20_count}")
            print(f"   • ma_60记录数: {ma60_count}")
            print(f"   • midprice记录数: {midprice_count}")
            print(f"   • y_probability范围: {min_y_prob:.6f} ~ {max_y_prob:.6f}")
            print(f"   • y_probability平均值: {avg_y_prob:.6f}")
            print(f"   • controller=1的数量: {controller_1_count}")
            
            # 计算覆盖率
            if total_rows > 0:
                y_prob_coverage = (y_prob_count / total_rows * 100)
                controller_coverage = (controller_count / total_rows * 100)
                k_value = controller_1_count / controller_count if controller_count > 0 else 0
                
                print(f"   • y_probability覆盖率: {y_prob_coverage:.1f}%")
                print(f"   • controller覆盖率: {controller_coverage:.1f}%")
                print(f"   • k值 (强势比例): {k_value:.6f}")
            
            # 显示最新10条数据
            self.cursor.execute(f"""
                SELECT 
                    date, 
                    close,
                    ma_20,
                    ma_60,
                    midprice,
                    y_probability,
                    controller,
                    Full_Y
                FROM {self.table_name} 
                WHERE y_probability IS NOT NULL
                ORDER BY date DESC 
                LIMIT 10
            """)
            
            latest_results = self.cursor.fetchall()
            print(f"\n📊 最新10条数据:")
            print("日期          | 收盘价    | MA20      | MA60      | y_prob  | controller | Full_Y")
            print("-" * 90)
            for row in latest_results:
                date_str = str(row[0])
                close_val = float(row[1]) if row[1] is not None else 0.0
                ma20_val = float(row[2]) if row[2] is not None else 0.0
                ma60_val = float(row[3]) if row[3] is not None else 0.0
                y_prob_val = float(row[5]) if row[5] is not None else 0.0
                controller_val = row[6] if row[6] is not None else 0
                full_y_val = float(row[7]) if row[7] is not None else 0.0
                print(f"{date_str} | {close_val:9.2f} | {ma20_val:9.2f} | {ma60_val:9.2f} | {y_prob_val:7.4f} | {controller_val:10d} | {full_y_val:10.6f}")
            
            return True
            
        except mysql.connector.Error as e:
            print(f"❌ 验证结果失败: {e}")
            return False
    
    def test_sp_updatecontroller(self):
        """测试sp_updatecontroller是否支持hkhsi50"""
        try:
            print(f"\n🧪 测试sp_updatecontroller对{self.table_name}的支持...")
            
            # 调用sp_updatecontroller
            args = [self.table_name, 0]
            result = self.cursor.callproc('sp_updatecontroller', args)
            
            # 获取结果集
            print("📊 sp_updatecontroller执行过程:")
            for result_set in self.cursor.stored_results():
                rows = result_set.fetchall()
                for row in rows:
                    print(f"   {row}")
            
            # 显示OUT参数结果
            k_value = result[1]
            print(f"📊 返回的k值: {k_value}")
            
            return True
            
        except mysql.connector.Error as e:
            print(f"❌ 测试sp_updatecontroller失败: {e}")
            return False
    
    def close_connection(self):
        """关闭数据库连接"""
        if self.cursor:
            self.cursor.close()
        if self.connection:
            self.connection.close()
        print("🔒 数据库连接已关闭")
    
    def run(self):
        """执行主流程"""
        print("🎯 为hkhsi50表添加y_probability字段")
        print("=" * 60)
        print(f"📅 执行时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"🏠 数据库: {self.db_config['host']}/{self.db_config['database']}")
        print(f"📊 目标表: {self.table_name}")
        
        try:
            # 1. 连接数据库
            if not self.connect_database():
                return False
            
            # 2. 检查表状态
            if not self.check_table_status():
                return False
            
            # 3. 添加缺失的列
            if not self.add_missing_columns():
                return False
            
            # 4. 更新midprice
            if not self.update_midprice():
                return False
            
            # 5. 计算移动平均线
            if not self.calculate_moving_averages():
                return False
            
            # 6. 计算y_probability
            if not self.calculate_y_probability():
                return False
            
            # 7. 计算controller和Full_Y
            if not self.calculate_controller_and_full_y():
                return False
            
            # 8. 验证结果
            if not self.verify_results():
                return False
            
            # 9. 测试sp_updatecontroller
            self.test_sp_updatecontroller()
            
            print("\n🎉 hkhsi50表的y_probability字段添加完成!")
            print("💡 现在hkhsi50表包含完整的博弈论分析指标:")
            print("   • midprice: 中值价格")
            print("   • controller: 二元强弱判断")
            print("   • Full_Y: 累积强势比例")
            print("   • y_probability: 控股商托价概率")
            print("   • ma_20, ma_60: 移动平均线")
            print("📝 使用方法: CALL sp_updatecontroller('hkhsi50', @k_value);")
            
            return True
            
        except Exception as e:
            print(f"❌ 执行失败: {e}")
            return False
        
        finally:
            self.close_connection()

def main():
    """主函数"""
    adder = HKHsi50YProbabilityAdder()
    success = adder.run()
    
    if success:
        print("\n✅ 任务完成!")
        print("📝 hkhsi50表现在支持完整的博弈论分析")
    else:
        print("\n❌ 任务失败!")

if __name__ == "__main__":
    main()
