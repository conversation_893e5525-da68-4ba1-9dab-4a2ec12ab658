#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
解释平仓价的概念
==============

说明开仓价和平仓价的区别，以及在交易中的作用

作者: Cosmoon NG
日期: 2025年7月
"""

import pymysql

def explain_close_price():
    """解释平仓价概念"""
    db_config = {
        'host': '************',
        'port': 3306,
        'database': 'finance',
        'user': 'root',
        'password': '',
        'charset': 'utf8mb4'
    }
    
    try:
        connection = pymysql.connect(**db_config)
        cursor = connection.cursor()
        
        print("📊 平仓价概念解释")
        print("="*80)
        print("🔍 基本概念:")
        print("   • 开仓价 (close列): 买入股票时的价格")
        print("   • 平仓价 (平仓价格列): 卖出股票时的价格")
        print("   • 盈亏 = (平仓价 - 开仓价) × 股数")
        print("="*80)
        
        # 获取前10条记录作为示例
        cursor.execute("""
            SELECT 交易序号, 开仓日期, 平仓日期, close, 平仓价格, 
                   交易股数, 净利润, `收益率%`
            FROM test 
            ORDER BY 交易序号
            LIMIT 10
        """)
        
        records = cursor.fetchall()
        
        print(f"{'序号':<4} {'开仓日期':<12} {'平仓日期':<12} {'开仓价':<8} {'平仓价':<8} "
              f"{'价格变动':<10} {'股数':<6} {'净利润':<8} {'收益率%':<8}")
        print("-" * 80)
        
        for record in records:
            trade_id, open_date, close_date, open_price, close_price, shares, profit, return_pct = record
            
            price_change = close_price - open_price
            price_change_pct = (close_price - open_price) / open_price * 100
            
            print(f"{trade_id:<4} {open_date:<12} {close_date:<12} {open_price:<8.2f} {close_price:<8.2f} "
                  f"{price_change:+8.2f} {shares:<6} {int(profit):<8} {float(return_pct):<8.2f}")
        
        print("\n🔍 交易流程说明:")
        print("1. 开仓: 在'开仓日期'以'开仓价'买入股票")
        print("2. 持仓: 持有股票一段时间")
        print("3. 平仓: 在'平仓日期'以'平仓价'卖出股票")
        print("4. 盈亏: 根据价格差异计算盈亏")
        
        print("\n📈 买涨策略 (做多):")
        print("   • 开仓: 买入股票")
        print("   • 平仓: 卖出股票")
        print("   • 盈利: 平仓价 > 开仓价")
        print("   • 亏损: 平仓价 < 开仓价")
        
        print("\n📉 买跌策略 (做空):")
        print("   • 开仓: 借入并卖出股票")
        print("   • 平仓: 买回并归还股票")
        print("   • 盈利: 平仓价 < 开仓价 (价格下跌)")
        print("   • 亏损: 平仓价 > 开仓价 (价格上涨)")
        
        print("\n🎯 止盈止损机制:")
        print("   • 止盈: 达到目标盈利时主动平仓")
        print("   • 止损: 达到最大亏损时主动平仓")
        print("   • 到期平仓: 持仓期满时按市价平仓")
        
        # 举例说明
        print("\n💡 具体例子 (交易1):")
        first_record = records[0]
        trade_id, open_date, close_date, open_price, close_price, shares, profit, return_pct = first_record
        
        print(f"   • 开仓: {open_date} 以 {open_price:.2f}港币 买入 {shares}股")
        print(f"   • 平仓: {close_date} 以 {close_price:.2f}港币 卖出 {shares}股")
        print(f"   • 价格变动: {close_price:.2f} - {open_price:.2f} = {close_price-open_price:+.2f}港币")
        print(f"   • 总盈亏: {close_price-open_price:+.2f} × {shares} = {int(profit)}港币")
        print(f"   • 收益率: {float(return_pct):.2f}%")
        
        connection.close()
        
    except Exception as e:
        print(f"❌ 查询失败: {e}")

if __name__ == "__main__":
    explain_close_price()
