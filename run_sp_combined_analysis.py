#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
独立运行存储过程修正数据
=====================

专门用于调用 sp_combined_stock_analysis 存储过程
修正 hkhsi50 表中的 Full_Y 和 E 值

使用方法:
python run_sp_combined_analysis.py

作者: Cosmoon NG
日期: 2025年7月16日
"""

import pymysql
import sys
from datetime import datetime

# 数据库配置
db_config = {
    'host': 'localhost',
    'user': 'root',
    'password': '12345678',
    'database': 'finance',
    'charset': 'utf8mb4'
}

def check_database_connection():
    """检查数据库连接"""
    try:
        conn = pymysql.connect(**db_config)
        conn.close()
        return True
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        return False

def check_table_exists():
    """检查 hkhsi50 表是否存在"""
    try:
        conn = pymysql.connect(**db_config)
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT COUNT(*) FROM information_schema.tables 
            WHERE table_schema = 'finance' AND table_name = 'hkhsi50'
        """)
        
        exists = cursor.fetchone()[0] > 0
        
        if exists:
            cursor.execute("SELECT COUNT(*) FROM hkhsi50")
            record_count = cursor.fetchone()[0]
            print(f"✅ 找到 hkhsi50 表，包含 {record_count:,} 条记录")
        else:
            print("❌ hkhsi50 表不存在")
        
        conn.close()
        return exists
        
    except Exception as e:
        print(f"❌ 检查表失败: {e}")
        return False

def check_stored_procedure():
    """检查存储过程是否存在"""
    try:
        conn = pymysql.connect(**db_config)
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT ROUTINE_NAME FROM information_schema.ROUTINES
            WHERE ROUTINE_SCHEMA = 'finance'
            AND ROUTINE_NAME = 'sp_combined_stock_analysis'
        """)
        
        exists = cursor.fetchone() is not None
        
        if not exists:
            print("❌ 存储过程 sp_combined_stock_analysis 不存在")
            
            # 查找其他可能的存储过程
            cursor.execute("""
                SELECT ROUTINE_NAME FROM information_schema.ROUTINES
                WHERE ROUTINE_SCHEMA = 'finance'
                AND ROUTINE_NAME LIKE '%stock%'
            """)
            
            available_procedures = cursor.fetchall()
            if available_procedures:
                print("📋 可用的股票相关存储过程:")
                for proc in available_procedures:
                    print(f"   • {proc[0]}")
        else:
            print("✅ 找到存储过程 sp_combined_stock_analysis")
        
        conn.close()
        return exists
        
    except Exception as e:
        print(f"❌ 检查存储过程失败: {e}")
        return False

def get_data_status():
    """获取数据状态"""
    try:
        conn = pymysql.connect(**db_config)
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT 
                COUNT(*) as 总记录数,
                MIN(Date) as 最早日期,
                MAX(Date) as 最新日期,
                AVG(Full_Y) as FullY平均值,
                AVG(E) as E平均值,
                COUNT(CASE WHEN Full_Y IS NULL THEN 1 END) as FullY空值,
                COUNT(CASE WHEN E IS NULL THEN 1 END) as E空值,
                MIN(Full_Y) as FullY最小值,
                MAX(Full_Y) as FullY最大值,
                MIN(E) as E最小值,
                MAX(E) as E最大值
            FROM hkhsi50
        """)
        
        stats = cursor.fetchone()
        conn.close()
        
        return {
            'total_records': stats[0],
            'min_date': stats[1],
            'max_date': stats[2],
            'avg_full_y': stats[3],
            'avg_e': stats[4],
            'null_full_y': stats[5],
            'null_e': stats[6],
            'min_full_y': stats[7],
            'max_full_y': stats[8],
            'min_e': stats[9],
            'max_e': stats[10]
        }
        
    except Exception as e:
        print(f"❌ 获取数据状态失败: {e}")
        return None

def run_stored_procedure():
    """运行存储过程"""
    try:
        conn = pymysql.connect(**db_config)
        cursor = conn.cursor()
        
        print("🔄 正在执行存储过程...")
        start_time = datetime.now()
        
        # 调用存储过程
        cursor.execute("CALL sp_combined_stock_analysis('hkhsi50')")
        
        # 提交事务
        conn.commit()
        
        end_time = datetime.now()
        execution_time = (end_time - start_time).total_seconds()
        
        print(f"✅ 存储过程执行完成，耗时 {execution_time:.2f} 秒")
        
        # 获取结果
        results = []
        try:
            while True:
                rows = cursor.fetchall()
                if rows:
                    results.extend(rows)
                if not cursor.nextset():
                    break
        except Exception:
            pass
        
        if results:
            print(f"📊 存储过程返回 {len(results)} 条结果")
            if len(results) <= 5:
                for i, row in enumerate(results, 1):
                    print(f"   {i}. {row}")
            else:
                for i, row in enumerate(results[:3], 1):
                    print(f"   {i}. {row}")
                print(f"   ... 还有 {len(results)-3} 条结果")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 执行存储过程失败: {e}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")
        return False

def show_strategy_analysis():
    """显示策略分析结果"""
    try:
        conn = pymysql.connect(**db_config)
        cursor = conn.cursor()
        
        # 最新记录分析
        cursor.execute("""
            SELECT Date, Close, MoneyFlowRatio, Full_Y, E,
                   CASE
                       WHEN Full_Y > 0.5 AND MoneyFlowRatio > 0.5 THEN '高值盈利区'
                       WHEN Full_Y > 0.333 AND Full_Y < 0.4 THEN '控股商控制区'
                       WHEN Full_Y < 0.25 OR MoneyFlowRatio < 0.25 THEN '强亏损区'
                       ELSE '其他区域'
                   END AS 策略区域
            FROM hkhsi50
            ORDER BY Date DESC
            LIMIT 5
        """)
        
        recent_data = cursor.fetchall()
        print(f"\n📊 最新5条记录的策略分析:")
        print(f"{'日期':<12} {'收盘价':<10} {'资金流':<8} {'FullY':<8} {'E值':<8} {'策略区域':<12}")
        print("-" * 70)
        
        for row in recent_data:
            date = str(row[0])
            close = row[1]
            money_flow = row[2] if row[2] is not None else 0
            full_y = row[3] if row[3] is not None else 0
            e_value = row[4] if row[4] is not None else 0
            strategy_zone = row[5]
            
            print(f"{date:<12} {close:<10.2f} {money_flow:<8.3f} {full_y:<8.3f} {e_value:<8.3f} {strategy_zone:<12}")
        
        # 策略区域分布统计
        cursor.execute("""
            SELECT 
                CASE
                    WHEN Full_Y > 0.5 AND MoneyFlowRatio > 0.5 THEN '高值盈利区'
                    WHEN Full_Y > 0.333 AND Full_Y < 0.4 THEN '控股商控制区'
                    WHEN Full_Y < 0.25 OR MoneyFlowRatio < 0.25 THEN '强亏损区'
                    ELSE '其他区域'
                END AS 策略区域,
                COUNT(*) as 天数,
                ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM hkhsi50), 1) as 占比
            FROM hkhsi50
            WHERE Full_Y IS NOT NULL AND MoneyFlowRatio IS NOT NULL
            GROUP BY 策略区域
            ORDER BY 天数 DESC
        """)
        
        zone_stats = cursor.fetchall()
        print(f"\n📈 策略区域分布统计:")
        print(f"{'策略区域':<12} {'天数':<8} {'占比':<8}")
        print("-" * 30)
        for zone, days, percentage in zone_stats:
            print(f"{zone:<12} {days:<8} {percentage}%")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 显示策略分析失败: {e}")

def main():
    """主函数"""
    print("🔧 独立运行存储过程修正数据")
    print("=" * 60)
    print(f"📅 执行时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"🎯 目标: 调用 sp_combined_stock_analysis('hkhsi50')")
    print("=" * 60)
    
    # 1. 检查数据库连接
    print("1️⃣ 检查数据库连接...")
    if not check_database_connection():
        return False
    
    # 2. 检查表是否存在
    print("\n2️⃣ 检查数据表...")
    if not check_table_exists():
        print("💡 请先运行 fetch_hsi_data_MariaDB10_fixed2.py 获取数据")
        return False
    
    # 3. 检查存储过程
    print("\n3️⃣ 检查存储过程...")
    if not check_stored_procedure():
        print("💡 请确保存储过程 sp_combined_stock_analysis 已创建")
        return False
    
    # 4. 获取修正前状态
    print("\n4️⃣ 获取修正前数据状态...")
    before_stats = get_data_status()
    if before_stats:
        print(f"📊 修正前状态:")
        print(f"   • 总记录数: {before_stats['total_records']:,}")
        print(f"   • 日期范围: {before_stats['min_date']} 至 {before_stats['max_date']}")
        print(f"   • Full_Y: 平均={before_stats['avg_full_y']:.4f}, 空值={before_stats['null_full_y']}")
        print(f"   • E值: 平均={before_stats['avg_e']:.4f}, 空值={before_stats['null_e']}")
    
    # 5. 运行存储过程
    print("\n5️⃣ 运行存储过程...")
    if not run_stored_procedure():
        return False
    
    # 6. 获取修正后状态
    print("\n6️⃣ 验证修正结果...")
    after_stats = get_data_status()
    if after_stats and before_stats:
        print(f"📊 修正后状态:")
        print(f"   • 总记录数: {after_stats['total_records']:,}")
        print(f"   • Full_Y: 平均={after_stats['avg_full_y']:.4f}, 范围=[{after_stats['min_full_y']:.4f}, {after_stats['max_full_y']:.4f}]")
        print(f"   • E值: 平均={after_stats['avg_e']:.4f}, 范围=[{after_stats['min_e']:.4f}, {after_stats['max_e']:.4f}]")
        
        # 显示变化
        if abs(before_stats['avg_full_y'] - after_stats['avg_full_y']) > 0.0001:
            change_full_y = after_stats['avg_full_y'] - before_stats['avg_full_y']
            change_e = after_stats['avg_e'] - before_stats['avg_e']
            print(f"\n🔄 数据修正效果:")
            print(f"   • Full_Y变化: {change_full_y:+.4f}")
            print(f"   • E值变化: {change_e:+.4f}")
        else:
            print(f"\n📊 数据未发生显著变化（可能已经是最新状态）")
    
    # 7. 显示策略分析
    print("\n7️⃣ 策略分析结果...")
    show_strategy_analysis()
    
    print(f"\n🎉 存储过程执行完成！")
    print(f"💡 数据已按照策略逻辑重新计算，可用于回测分析")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
