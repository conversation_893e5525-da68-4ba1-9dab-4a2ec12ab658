#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
添加MyE字段到Excel表格
=====================

MyE = 8 × MoneyFlowRatio × Full_Y - 3 × MoneyFlowRatio - 3 × Full_Y + 1

这是Cosmoon XYE系统的核心公式，其中：
- MoneyFlowRatio = X值 (资金流强度)
- Full_Y = Y值 (价格控制强度)
- MyE = 综合Cosmoon指标

作者: Cosmoon NG
"""

import pandas as pd
import mysql.connector
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

def calculate_mye(money_flow_ratio, full_y):
    """
    计算MyE值 (Cosmoon公式)
    
    公式: MyE = 8 × X × Y - 3 × X - 3 × Y + 1
    其中:
    - X = MoneyFlowRatio (资金流强度)
    - Y = Full_Y (价格控制强度)
    """
    x = money_flow_ratio
    y = full_y
    
    mye = 8 * x * y - 3 * x - 3 * y + 1
    
    return mye

def get_database_data():
    """从数据库获取技术指标数据"""
    try:
        config = {
            'host': 'localhost',
            'port': 3306,
            'user': 'root',
            'password': '12345678',
            'database': 'finance'
        }
        
        connection = mysql.connector.connect(**config)
        cursor = connection.cursor()
        
        query = """
        SELECT Y_Value, X_Value, E_Value, Full_Y, MoneyFlowRatio
        FROM eab_0023hk 
        ORDER BY Date DESC 
        LIMIT 1
        """
        
        cursor.execute(query)
        result = cursor.fetchone()
        
        cursor.close()
        connection.close()
        
        if result:
            return {
                'y_value': result[0],
                'x_value': result[1], 
                'e_value': result[2],
                'full_y': result[3],
                'money_flow_ratio': result[4]
            }
    except Exception as e:
        print(f"⚠️ 数据库查询失败: {e}")
    
    # 默认值
    return {
        'y_value': 0.2069,
        'x_value': 0.3211,
        'e_value': -0.0525,
        'full_y': 0.2069,
        'money_flow_ratio': 0.3211
    }

def add_mye_field():
    """添加MyE字段到Excel表格"""
    
    excel_file = "交易记录追踪0023HK.xlsx"
    
    print("🧮 添加MyE字段到Excel表格")
    print("=" * 50)
    
    try:
        # 读取Excel文件
        df = pd.read_excel(excel_file)
        print(f"📋 当前记录数: {len(df)}")
        print(f"📊 当前列数: {len(df.columns)}")
        
        # 获取数据库技术指标
        tech_data = get_database_data()
        print(f"📈 获取技术指标数据: {'成功' if tech_data else '失败'}")
        
        # 确保必要的字段存在
        required_fields = ['Full_Y', 'MoneyFlowRatio']
        missing_fields = [field for field in required_fields if field not in df.columns]
        
        if missing_fields:
            print(f"⚠️ 缺少必要字段: {missing_fields}")
            
            # 添加缺少的字段
            for field in missing_fields:
                if field == 'Full_Y':
                    y_col_idx = df.columns.get_loc('Y值') if 'Y值' in df.columns else len(df.columns)
                    df.insert(y_col_idx + 1, 'Full_Y', 0.0000)
                    print("➕ 添加 Full_Y 列")
                elif field == 'MoneyFlowRatio':
                    full_y_idx = df.columns.get_loc('Full_Y') if 'Full_Y' in df.columns else len(df.columns)
                    df.insert(full_y_idx + 1, 'MoneyFlowRatio', 0.0000)
                    print("➕ 添加 MoneyFlowRatio 列")
        
        # 添加MyE字段
        if 'MyE' not in df.columns:
            # 在MoneyFlowRatio后面插入MyE
            mfr_col_idx = df.columns.get_loc('MoneyFlowRatio') if 'MoneyFlowRatio' in df.columns else len(df.columns)
            df.insert(mfr_col_idx + 1, 'MyE', 0.0000)
            print("➕ 添加 MyE 列")
        
        # 计算MyE值
        money_flow_ratio = tech_data['money_flow_ratio']
        full_y = tech_data['full_y']
        mye_value = calculate_mye(money_flow_ratio, full_y)
        
        print(f"\n🧮 MyE计算过程:")
        print(f"   MoneyFlowRatio (X): {money_flow_ratio:.4f}")
        print(f"   Full_Y (Y): {full_y:.4f}")
        print(f"   公式: MyE = 8×X×Y - 3×X - 3×Y + 1")
        print(f"   计算: MyE = 8×{money_flow_ratio:.4f}×{full_y:.4f} - 3×{money_flow_ratio:.4f} - 3×{full_y:.4f} + 1")
        print(f"   结果: MyE = {mye_value:.4f}")
        
        # 更新最新记录的所有技术指标
        latest_idx = len(df) - 1
        
        # 更新技术指标字段
        if 'Y值' in df.columns:
            df.iloc[latest_idx, df.columns.get_loc('Y值')] = tech_data['y_value']
        if 'Full_Y' in df.columns:
            df.iloc[latest_idx, df.columns.get_loc('Full_Y')] = full_y
        if 'MoneyFlowRatio' in df.columns:
            df.iloc[latest_idx, df.columns.get_loc('MoneyFlowRatio')] = money_flow_ratio
        if 'MyE' in df.columns:
            df.iloc[latest_idx, df.columns.get_loc('MyE')] = mye_value
        if 'X值' in df.columns:
            df.iloc[latest_idx, df.columns.get_loc('X值')] = tech_data['x_value']
        if 'E值' in df.columns:
            df.iloc[latest_idx, df.columns.get_loc('E值')] = tech_data['e_value']
        
        # 更新备注
        if '备注' in df.columns:
            df.iloc[latest_idx, df.columns.get_loc('备注')] = f'完整XYE系统 含MyE计算值 MyE={mye_value:.4f}'
        
        # 保存Excel文件
        df.to_excel(excel_file, index=False)
        
        print(f"✅ Excel文件已更新: {excel_file}")
        print(f"📊 总列数: {len(df.columns)}")
        
        # 显示完整的XYE指标
        print(f"\n📈 完整XYE技术指标:")
        print(f"   Y值: {tech_data['y_value']:.4f}")
        print(f"   Full_Y: {full_y:.4f}")
        print(f"   X值: {tech_data['x_value']:.4f}")
        print(f"   MoneyFlowRatio: {money_flow_ratio:.4f}")
        print(f"   E值: {tech_data['e_value']:.4f}")
        print(f"   MyE: {mye_value:.4f} 🆕")
        
        # 交易信号分析
        print(f"\n🎯 交易信号分析:")
        if mye_value > 0:
            signal = "买入信号"
            signal_strength = "强烈买入" if mye_value > 0.1 else "买入"
        elif mye_value < -0.1:
            signal = "强烈卖出信号"
            signal_strength = "强烈卖出"
        else:
            signal = "观望信号"
            signal_strength = "观望"
        
        print(f"   MyE值: {mye_value:.4f}")
        print(f"   信号: {signal}")
        print(f"   强度: {signal_strength}")
        
        # 显示字段列表 (重点显示XYE相关字段)
        print(f"\n📋 XYE相关字段:")
        xye_fields = ['Y值', 'Full_Y', 'X值', 'MoneyFlowRatio', 'E值', 'MyE']
        for field in xye_fields:
            if field in df.columns:
                idx = df.columns.get_loc(field) + 1
                marker = "🆕" if field == 'MyE' else "📊"
                print(f"   {idx:2d}. {marker} {field}")
        
        return True
        
    except Exception as e:
        print(f"❌ 操作失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    try:
        success = add_mye_field()
        
        if success:
            print(f"\n🎯 MyE字段添加完成！")
            print(f"✅ MyE = 8×MoneyFlowRatio×Full_Y - 3×MoneyFlowRatio - 3×Full_Y + 1")
            print(f"📊 XYE系统现在包含完整的技术指标")
            print(f"🧮 MyE值可用于精确的交易信号判断")
        else:
            print(f"\n❌ MyE字段添加失败")
            
    except Exception as e:
        print(f"❌ 系统错误: {e}")

if __name__ == "__main__":
    main()
