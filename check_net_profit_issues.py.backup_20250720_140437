#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查test表净利润是否有问题
========================

检查净利润的计算逻辑和数据一致性

作者: Cosmoon NG
日期: 2025年7月
"""

import pymysql

def check_net_profit_issues():
    """检查净利润问题"""
    db_config = {
        'host': '************',
        'port': 3306,
        'database': 'finance',
        'user': 'root',
        'password': '',
        'charset': 'utf8mb4'
    }
    
    try:
        connection = pymysql.connect(**db_config)
        cursor = connection.cursor()
        
        print("🔍 检查test表净利润问题")
        print("="*80)
        
        # 1. 检查净利润的基本统计
        cursor.execute("""
            SELECT 
                COUNT(*) as 总记录数,
                SUM(净利润) as 净利润总和,
                AVG(净利润) as 平均净利润,
                MIN(净利润) as 最小净利润,
                MAX(净利润) as 最大净利润,
                <PERSON><PERSON>(毛利润) as 毛利润总和,
                AVG(毛利润) as 平均毛利润
            FROM test
        """)
        
        stats = cursor.fetchone()
        total_count, net_sum, net_avg, net_min, net_max, gross_sum, gross_avg = stats
        
        print(f"📊 净利润基本统计:")
        print(f"   • 总记录数: {total_count}")
        print(f"   • 净利润总和: {net_sum:+,.0f}港币")
        print(f"   • 平均净利润: {net_avg:+.0f}港币")
        print(f"   • 最小净利润: {net_min:+.0f}港币")
        print(f"   • 最大净利润: {net_max:+.0f}港币")
        print(f"   • 毛利润总和: {gross_sum:+,.0f}港币")
        print(f"   • 平均毛利润: {gross_avg:+.0f}港币")
        
        # 2. 检查净利润与毛利润的关系
        print(f"\n🔍 净利润 vs 毛利润分析:")
        cursor.execute("""
            SELECT 
                交易序号, 交易方向, close, 平仓价格, 交易股数,
                毛利润, 交易成本, 净利润,
                (毛利润 - 交易成本) as 计算净利润,
                (净利润 - (毛利润 - 交易成本)) as 差异
            FROM test 
            ORDER BY 交易序号
            LIMIT 10
        """)
        
        records = cursor.fetchall()
        
        print(f"前10条记录的净利润计算验证:")
        print("-" * 120)
        print(f"{'序号':<4} {'方向':<6} {'开仓价':<8} {'平仓价':<8} {'股数':<6} "
              f"{'毛利润':<8} {'成本':<6} {'净利润':<8} {'计算值':<8} {'差异':<6}")
        print("-" * 120)
        
        for record in records:
            trade_id, direction, open_price, close_price, shares, gross_profit, cost, net_profit, calc_net, diff = record
            print(f"{trade_id:<4} {direction:<6} {float(open_price):<8.2f} {float(close_price):<8.2f} {shares:<6} "
                  f"{gross_profit:<8} {cost:<6} {net_profit:<8} {calc_net:<8} {diff:<6}")
        
        # 3. 检查是否有异常的净利润
        print(f"\n⚠️ 异常净利润检查:")
        cursor.execute("""
            SELECT COUNT(*) FROM test 
            WHERE ABS(净利润 - (毛利润 - 交易成本)) > 1
        """)
        
        anomaly_count = cursor.fetchone()[0]
        print(f"   • 净利润计算异常记录: {anomaly_count}条")
        
        if anomaly_count > 0:
            cursor.execute("""
                SELECT 交易序号, 毛利润, 交易成本, 净利润, (毛利润 - 交易成本) as 应该净利润
                FROM test 
                WHERE ABS(净利润 - (毛利润 - 交易成本)) > 1
                LIMIT 5
            """)
            
            anomalies = cursor.fetchall()
            print(f"   异常记录示例:")
            for anomaly in anomalies:
                trade_id, gross, cost, net, should_net = anomaly
                print(f"     交易{trade_id}: 毛利润{gross}, 成本{cost}, 净利润{net}, 应该{should_net}")
        
        # 4. 检查观望记录的净利润
        print(f"\n👁️ 观望记录净利润检查:")
        cursor.execute("""
            SELECT COUNT(*), SUM(净利润), AVG(净利润)
            FROM test 
            WHERE 交易方向 = '观望'
        """)
        
        observe_stats = cursor.fetchone()
        observe_count, observe_sum, observe_avg = observe_stats
        
        print(f"   • 观望记录数: {observe_count}")
        print(f"   • 观望净利润总和: {observe_sum if observe_sum else 0}港币")
        print(f"   • 观望平均净利润: {observe_avg if observe_avg else 0}港币")
        
        if observe_sum and observe_sum != 0:
            print(f"   ⚠️ 警告: 观望记录净利润应该为0")
        
        # 5. 按交易方向分析净利润
        print(f"\n📈 按交易方向分析净利润:")
        cursor.execute("""
            SELECT 
                交易方向,
                COUNT(*) as 交易次数,
                SUM(净利润) as 净利润总和,
                AVG(净利润) as 平均净利润,
                MIN(净利润) as 最小净利润,
                MAX(净利润) as 最大净利润
            FROM test 
            GROUP BY 交易方向
            ORDER BY 净利润总和 DESC
        """)
        
        direction_stats = cursor.fetchall()
        
        print("-" * 80)
        print(f"{'交易方向':<8} {'次数':<6} {'净利润总和':<10} {'平均':<8} {'最小':<8} {'最大':<8}")
        print("-" * 80)
        
        for direction, count, total, avg, min_val, max_val in direction_stats:
            print(f"{direction:<8} {count:<6} {total:<10} {avg:<8.0f} {min_val:<8} {max_val:<8}")
        
        # 6. 检查净利润与profit/loss价格的关系
        print(f"\n🎯 净利润与profit/loss价格关系检查:")
        cursor.execute("""
            SELECT 
                交易序号, 交易方向, close, 平仓价格, `profit价格`, `loss价格`, 净利润,
                CASE 
                    WHEN 交易方向 = '观望' THEN '观望'
                    WHEN 交易方向 = '买涨' AND 平仓价格 >= `profit价格` THEN '应该止盈'
                    WHEN 交易方向 = '买涨' AND 平仓价格 <= `loss价格` THEN '应该止损'
                    WHEN 交易方向 = '买跌' AND 平仓价格 <= `profit价格` THEN '应该止盈'
                    WHEN 交易方向 = '买跌' AND 平仓价格 >= `loss价格` THEN '应该止损'
                    ELSE '应该到期平仓'
                END AS 应该结果
            FROM test 
            ORDER BY 交易序号
            LIMIT 10
        """)
        
        profit_loss_records = cursor.fetchall()
        
        print(f"前10条记录的profit/loss价格验证:")
        print("-" * 120)
        print(f"{'序号':<4} {'方向':<6} {'开仓价':<8} {'平仓价':<8} {'profit价格':<10} {'loss价格':<9} {'净利润':<8} {'应该结果':<12}")
        print("-" * 120)
        
        for record in profit_loss_records:
            trade_id, direction, open_price, close_price, profit_price, loss_price, net_profit, should_result = record
            print(f"{trade_id:<4} {direction:<6} {float(open_price):<8.2f} {float(close_price):<8.2f} "
                  f"{float(profit_price):<10.2f} {float(loss_price):<9.2f} {net_profit:<8} {should_result:<12}")
        
        # 7. 检查净利润的合理性
        print(f"\n🔍 净利润合理性检查:")
        cursor.execute("""
            SELECT 
                COUNT(CASE WHEN 净利润 > 0 THEN 1 END) as 盈利次数,
                COUNT(CASE WHEN 净利润 < 0 THEN 1 END) as 亏损次数,
                COUNT(CASE WHEN 净利润 = 0 THEN 1 END) as 平手次数,
                COUNT(CASE WHEN 净利润 > 100 THEN 1 END) as 大盈利次数,
                COUNT(CASE WHEN 净利润 < -100 THEN 1 END) as 大亏损次数
            FROM test
        """)
        
        profit_analysis = cursor.fetchone()
        profit_count, loss_count, even_count, big_profit, big_loss = profit_analysis
        
        print(f"   • 盈利次数: {profit_count} ({profit_count/total_count*100:.1f}%)")
        print(f"   • 亏损次数: {loss_count} ({loss_count/total_count*100:.1f}%)")
        print(f"   • 平手次数: {even_count} ({even_count/total_count*100:.1f}%)")
        print(f"   • 大盈利次数(>100): {big_profit}")
        print(f"   • 大亏损次数(<-100): {big_loss}")
        
        connection.close()
        print(f"\n✅ 净利润检查完成!")
        
    except Exception as e:
        print(f"❌ 检查净利润失败: {e}")

if __name__ == "__main__":
    check_net_profit_issues()
