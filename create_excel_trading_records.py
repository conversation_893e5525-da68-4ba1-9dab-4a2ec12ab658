#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建详细的HSI50交易记录Excel
==========================
包含您要求的所有字段：
i, 日期, 时间, 交易品种, 价格, 数量, Y值, X值, E值, 方向, 止盈价, 止损价, 收益, 总资本, 持仓时间, 备注
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import yfinance as yf
import warnings
warnings.filterwarnings('ignore')

class HSI50TradingRecordCreator:
    def __init__(self):
        """初始化交易记录创建器"""
        self.initial_capital = 30000
        self.monthly_addition = 1000
        self.max_position_ratio = 0.35  # 35%仓位

        # 策略参数
        self.take_profit_long = 0.016   # 多头止盈1.6%
        self.stop_loss_long = 0.008     # 多头止损0.8%
        self.take_profit_short = 0.008  # 空头止盈0.8%
        self.stop_loss_short = 0.016    # 空头止损1.6%

        self.trades = []
        self.daily_records = []

    def load_hsi_data(self):
        """加载HSI数据"""
        print("📊 从YFinance加载HSI数据...")
        try:
            # 获取恒生指数数据 (包含去年记录)
            ticker = yf.Ticker("^HSI")
            hist = ticker.history(start="2023-01-01", end=None, interval="1d")

            if hist.empty:
                print("❌ 无法获取HSI数据")
                return False

            # 数据预处理
            self.df = hist.reset_index()
            self.df.columns = [col.lower() for col in self.df.columns]

            # 确保列名正确
            self.df['Date'] = self.df['date']
            self.df['Open'] = self.df['open']
            self.df['High'] = self.df['high']
            self.df['Low'] = self.df['low']
            self.df['Close'] = self.df['close']
            self.df['Volume'] = self.df['volume']

            # 按日期排序
            self.df = self.df.sort_values('Date').reset_index(drop=True)

            print(f"✅ 成功加载 {len(self.df)} 条数据")
            print(f"📅 数据范围: {self.df['Date'].min().date()} 至 {self.df['Date'].max().date()}")

            # 计算技术指标
            self.calculate_indicators()

            return True

        except Exception as e:
            print(f"❌ 数据加载失败: {e}")
            return False

    def calculate_indicators(self):
        """计算技术指标"""
        print("   计算XYE指标...")

        # 计算移动平均线
        self.df['ma_20'] = self.df['Close'].rolling(window=20).mean()
        self.df['ma_60'] = self.df['Close'].rolling(window=60).mean()

        # 计算midprice
        self.df['midprice'] = (self.df['High'] + self.df['Low']) / 2

        # 计算controller (收盘价 > midprice 为1，否则为0)
        self.df['controller'] = (self.df['Close'] > self.df['midprice']).astype(int)

        # 计算Full_Y (累积强势比例)
        self.df['cumulative_controller'] = self.df['controller'].cumsum()
        self.df['row_number'] = range(1, len(self.df) + 1)
        self.df['Full_Y'] = self.df['cumulative_controller'] / self.df['row_number']

        # 计算XYE值
        self.df['Y'] = self.df['Full_Y']  # Y值 = Full_Y
        self.df['X'] = 1 - self.df['Full_Y']  # X值 = 1 - Full_Y

        # 计算E值 (期望值)
        self.df['price_vs_ma20'] = self.df['Close'] / self.df['ma_20']
        self.df['y_probability'] = np.where(
            self.df['price_vs_ma20'] >= 1,
            0.5 + 0.3 * np.tanh((self.df['price_vs_ma20'] - 1) * 2),
            0.5 - 0.3 * np.tanh((1 - self.df['price_vs_ma20']) * 2)
        )
        self.df['y_probability'] = np.clip(self.df['y_probability'], 0.1, 0.9)

        # E值计算 (综合期望)
        self.df['price_deviation'] = (self.df['Close'] - self.df['midprice']) / self.df['midprice']
        self.df['E'] = (self.df['y_probability'] * 0.4 +
                       self.df['price_deviation'] * 0.3 +
                       (self.df['Y'] - 0.5) * 0.3)

        # 计算回归线 (简化版本)
        self.df['regression_line'] = self.df['Close'].rolling(window=60).mean()

        print("✅ 技术指标计算完成")

    def check_entry_conditions(self, row):
        """检查入场条件"""
        y_value = row['Y']
        x_value = row['X']
        e_value = row['E']
        price = row['Close']
        regression_line = row['regression_line']

        # 基本数据检查
        if pd.isna(y_value) or pd.isna(x_value) or pd.isna(e_value) or pd.isna(regression_line):
            return 0

        # XY分区策略 (高质量版本)
        # 高值盈利区 (Y>0.45, X>0.45): 买涨 (双重高门槛)
        if y_value > 0.45 and x_value > 0.45:
            return 1  # 多头

        # 强亏损区 (Y<0.25 OR X<0.25): 买跌
        elif y_value < 0.25 or x_value < 0.25:
            return -1  # 空头

        # 其他区域: 买跌
        elif not (0.333 < y_value < 0.45):  # 非控股商控制区
            return -1  # 空头

        return 0  # 观望

    def add_monthly_capital(self, date, capital):
        """每月增加资金"""
        last_month = getattr(self, 'last_month', None)
        current_month = date.replace(day=1)

        if last_month is None or current_month > last_month:
            self.last_month = current_month
            return capital + self.monthly_addition

        return capital

    def run_detailed_backtest(self):
        """运行详细回测"""
        print("\n🎯 开始详细回测...")

        capital = self.initial_capital
        position = 0  # 0=空仓, 1=多头, -1=空头
        entry_price = 0
        entry_date = None
        entry_y = 0
        entry_x = 0
        entry_e = 0
        take_profit_price = 0
        stop_loss_price = 0
        trade_id = 1

        for i in range(60, len(self.df)):  # 从第60天开始
            row = self.df.iloc[i]
            date = row['Date']

            # 每月增加资金
            capital = self.add_monthly_capital(date, capital)

            # 检查平仓条件
            if position != 0:
                current_price = row['Close']
                high_price = row['High']
                low_price = row['Low']

                should_exit = False
                exit_reason = ""
                exit_price = current_price

                if position == 1:  # 多头持仓
                    # 检查止盈
                    if high_price >= take_profit_price:
                        should_exit = True
                        exit_reason = "止盈"
                        exit_price = take_profit_price
                    # 检查止损
                    elif low_price <= stop_loss_price:
                        should_exit = True
                        exit_reason = "止损"
                        exit_price = stop_loss_price

                elif position == -1:  # 空头持仓
                    # 检查止盈
                    if low_price <= take_profit_price:
                        should_exit = True
                        exit_reason = "止盈"
                        exit_price = take_profit_price
                    # 检查止损
                    elif high_price >= stop_loss_price:
                        should_exit = True
                        exit_reason = "止损"
                        exit_price = stop_loss_price

                if should_exit:
                    # 计算收益
                    position_size = capital * self.max_position_ratio

                    if position == 1:  # 多头
                        profit_ratio = (exit_price - entry_price) / entry_price
                    else:  # 空头
                        profit_ratio = (entry_price - exit_price) / entry_price

                    profit = position_size * profit_ratio
                    capital += profit

                    # 计算持仓时间
                    holding_days = (date - entry_date).days

                    # 记录平仓交易
                    trade_record = {
                        'i': trade_id,
                        '日期': date.strftime('%Y-%m-%d'),
                        '时间': '15:00:00',
                        '交易品种': 'HSI50',
                        '价格': round(exit_price, 2),
                        '数量': 1,
                        'Y值': round(entry_y, 6),
                        'X值': round(entry_x, 6),
                        'E值': round(entry_e, 6),
                        '方向': '看涨' if position == 1 else '看跌',
                        '止盈价': round(take_profit_price, 2),
                        '止损价': round(stop_loss_price, 2),
                        '收益': round(profit, 2),
                        '总资本': round(capital, 2),
                        '持仓时间': f"{holding_days}天",
                        '备注': f"{exit_reason}, 入场价{entry_price:.2f}, 收益率{profit_ratio*100:+.2f}%"
                    }

                    self.trades.append(trade_record)
                    trade_id += 1

                    # 重置持仓
                    position = 0
                    entry_price = 0
                    entry_date = None

            # 检查开仓条件
            if position == 0:
                signal = self.check_entry_conditions(row)

                if signal != 0:
                    position = signal
                    entry_price = row['Close']
                    entry_date = date
                    entry_y = row['Y']
                    entry_x = row['X']
                    entry_e = row['E']

                    # 计算止盈止损价格
                    if position == 1:  # 多头
                        take_profit_price = entry_price * (1 + self.take_profit_long)
                        stop_loss_price = entry_price * (1 - self.stop_loss_long)
                        direction = '看涨'
                    else:  # 空头
                        take_profit_price = entry_price * (1 - self.take_profit_short)
                        stop_loss_price = entry_price * (1 + self.stop_loss_short)
                        direction = '看跌'

                    # 记录开仓交易
                    trade_record = {
                        'i': trade_id,
                        '日期': date.strftime('%Y-%m-%d'),
                        '时间': '09:30:00',
                        '交易品种': 'HSI50',
                        '价格': round(entry_price, 2),
                        '数量': 1,
                        'Y值': round(entry_y, 6),
                        'X值': round(entry_x, 6),
                        'E值': round(entry_e, 6),
                        '方向': direction,
                        '止盈价': round(take_profit_price, 2),
                        '止损价': round(stop_loss_price, 2),
                        '收益': 0,
                        '总资本': round(capital, 2),
                        '持仓时间': '0天',
                        '备注': f"XY分区信号: Y={entry_y:.4f}, X={entry_x:.4f}, E={entry_e:.4f}"
                    }

                    self.trades.append(trade_record)
                    trade_id += 1

            # 记录每日状态 (每10天记录一次，减少数据量)
            if i % 10 == 0:
                daily_record = {
                    'i': i - 59,
                    '日期': date.strftime('%Y-%m-%d'),
                    '时间': '15:00:00',
                    '交易品种': 'HSI50',
                    '价格': round(row['Close'], 2),
                    '数量': 0,
                    'Y值': round(row['Y'], 6),
                    'X值': round(row['X'], 6),
                    'E值': round(row['E'], 6),
                    '方向': '看涨' if position == 1 else '看跌' if position == -1 else '空仓',
                    '止盈价': round(take_profit_price, 2) if position != 0 else 0,
                    '止损价': round(stop_loss_price, 2) if position != 0 else 0,
                    '收益': 0,
                    '总资本': round(capital, 2),
                    '持仓时间': f"{(date - entry_date).days}天" if position != 0 else '0天',
                    '备注': f"日终记录, 当前{'持仓' if position != 0 else '空仓'}, Y={row['Y']:.4f}, X={row['X']:.4f}"
                }

                self.daily_records.append(daily_record)

        print(f"✅ 回测完成！")
        print(f"📊 交易记录: {len(self.trades)} 条")
        print(f"📊 日终记录: {len(self.daily_records)} 条")
        print(f"💰 最终资金: {capital:,.2f}港元")

    def export_to_excel(self):
        """导出到Excel"""
        print("\n📄 导出Excel文件...")

        try:
            filename = f'HSI50详细交易记录_{datetime.now().strftime("%Y%m%d_%H%M%S")}.xlsx'

            with pd.ExcelWriter(filename, engine='openpyxl') as writer:

                # 1. 交易记录表
                if self.trades:
                    trades_df = pd.DataFrame(self.trades)
                    trades_df.to_excel(writer, sheet_name='交易记录', index=False)
                    print(f"✅ 交易记录表: {len(trades_df)} 条记录")

                # 2. 日终记录表
                if self.daily_records:
                    daily_df = pd.DataFrame(self.daily_records)
                    daily_df.to_excel(writer, sheet_name='日终记录', index=False)
                    print(f"✅ 日终记录表: {len(daily_df)} 条记录")

                # 3. 2024年记录表
                self.create_2024_records(writer)

                # 4. 统计汇总表
                self.create_summary_table(writer)

                # 5. 字段说明表
                self.create_field_description(writer)

            print(f"✅ Excel文件已保存: {filename}")
            return filename

        except Exception as e:
            print(f"❌ 导出Excel失败: {e}")
            return None

    def create_2024_records(self, writer):
        """创建2024年记录表"""
        if not self.trades:
            return

        trades_df = pd.DataFrame(self.trades)
        trades_df['日期_dt'] = pd.to_datetime(trades_df['日期'])

        # 筛选2024年记录
        records_2024 = trades_df[
            (trades_df['日期_dt'] >= '2024-01-01') &
            (trades_df['日期_dt'] < '2025-01-01')
        ].copy()

        if len(records_2024) > 0:
            records_2024 = records_2024.drop('日期_dt', axis=1)
            records_2024.to_excel(writer, sheet_name='2024年记录', index=False)
            print(f"✅ 2024年记录表: {len(records_2024)} 条记录")
        else:
            # 创建空表
            empty_df = pd.DataFrame(columns=[
                'i', '日期', '时间', '交易品种', '价格', '数量', 'Y值', 'X值', 'E值',
                '方向', '止盈价', '止损价', '收益', '总资本', '持仓时间', '备注'
            ])
            empty_df.to_excel(writer, sheet_name='2024年记录', index=False)
            print(f"⚠️ 2024年记录表: 无数据")

    def create_summary_table(self, writer):
        """创建统计汇总表"""
        if not self.trades:
            return

        trades_df = pd.DataFrame(self.trades)
        exit_trades = trades_df[trades_df['方向'].isin(['看涨', '看跌'])]

        if len(exit_trades) == 0:
            return

        # 基本统计
        total_trades = len(exit_trades)
        winning_trades = len(exit_trades[exit_trades['收益'] > 0])
        win_rate = winning_trades / total_trades if total_trades > 0 else 0

        total_profit = exit_trades['收益'].sum()
        avg_profit = exit_trades['收益'].mean()
        max_profit = exit_trades['收益'].max()
        min_profit = exit_trades['收益'].min()

        # 创建汇总数据
        summary_data = {
            '统计项目': [
                '总交易次数', '盈利交易', '亏损交易', '胜率',
                '总收益', '平均收益', '最大盈利', '最大亏损',
                '看涨交易', '看跌交易', '策略类型', '仓位比例'
            ],
            '数值': [
                total_trades,
                winning_trades,
                total_trades - winning_trades,
                f"{win_rate*100:.2f}%",
                f"{total_profit:,.2f}",
                f"{avg_profit:,.2f}",
                f"{max_profit:,.2f}",
                f"{min_profit:,.2f}",
                len(exit_trades[exit_trades['方向'] == '看涨']),
                len(exit_trades[exit_trades['方向'] == '看跌']),
                'XY分区策略',
                f"{self.max_position_ratio*100:.0f}%"
            ]
        }

        summary_df = pd.DataFrame(summary_data)
        summary_df.to_excel(writer, sheet_name='统计汇总', index=False)
        print(f"✅ 统计汇总表: {len(summary_df)} 项统计")

    def create_field_description(self, writer):
        """创建字段说明表"""
        field_data = {
            '字段名': [
                'i', '日期', '时间', '交易品种', '价格', '数量',
                'Y值', 'X值', 'E值', '方向', '止盈价', '止损价',
                '收益', '总资本', '持仓时间', '备注'
            ],
            '说明': [
                '交易序号',
                '交易日期 (YYYY-MM-DD)',
                '交易时间 (HH:MM:SS)',
                '交易标的 (HSI50)',
                '成交价格',
                '交易数量 (手)',
                'Y值 = Full_Y (累积强势比例)',
                'X值 = 1 - Full_Y (弱势比例)',
                'E值 = 期望值 (综合技术指标)',
                '交易方向 (看涨/看跌)',
                '止盈价格',
                '止损价格',
                '交易收益 (港元)',
                '账户总资金 (港元)',
                '持仓天数',
                '交易备注信息'
            ],
            '计算方式': [
                '自动递增',
                '交易发生日期',
                '09:30开盘, 15:00收盘',
                '恒生指数50',
                '实际成交价格',
                '固定1手',
                'controller累计数/总天数',
                '1 - Y值',
                'y_probability*0.4 + price_deviation*0.3 + (Y-0.5)*0.3',
                '基于XY分区策略',
                '开仓价 × (1±止盈比例)',
                '开仓价 × (1±止损比例)',
                '仓位金额 × 收益率',
                '累计资金变化',
                '平仓日期 - 开仓日期',
                '包含信号原因和结果'
            ]
        }

        field_df = pd.DataFrame(field_data)
        field_df.to_excel(writer, sheet_name='字段说明', index=False)
        print(f"✅ 字段说明表: {len(field_df)} 个字段")

    def run(self):
        """运行完整流程"""
        print("🎯 HSI50详细交易记录创建器")
        print("=" * 60)
        print(f"📅 创建时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"💰 初始资金: {self.initial_capital:,}港元")
        print(f"📈 每月追加: {self.monthly_addition:,}港元")
        print(f"📊 仓位比例: {self.max_position_ratio*100:.0f}%")

        if not self.load_hsi_data():
            print("❌ 数据加载失败")
            return None

        self.run_detailed_backtest()
        filename = self.export_to_excel()

        if filename:
            print("\n🎉 详细交易记录创建完成！")
            print(f"📄 文件名: {filename}")
            print("📊 包含表格:")
            print("   • 交易记录 (所有开仓平仓记录)")
            print("   • 日终记录 (定期状态记录)")
            print("   • 2024年记录 (去年的交易记录)")
            print("   • 统计汇总 (策略表现统计)")
            print("   • 字段说明 (各字段含义)")

            return filename
        else:
            print("❌ 文件创建失败")
            return None

def main():
    """主函数"""
    creator = HSI50TradingRecordCreator()
    creator.run()

if __name__ == "__main__":
    main()
