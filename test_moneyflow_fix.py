#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试MoneyFlowRatio错误修复
========================

验证存储过程修复是否成功

作者: Cosmoon NG
日期: 2025年7月24日
"""

import mysql.connector

def test_moneyflow_fix():
    """测试MoneyFlowRatio错误修复"""
    
    try:
        # 连接数据库
        conn = mysql.connector.connect(
            host="localhost",
            user="root",
            password="12345678",
            database="finance"
        )
        cursor = conn.cursor()
        
        print("🧪 测试MoneyFlowRatio错误修复")
        print("=" * 40)
        
        # 测试1: 调用修复后的存储过程
        print("\n1. 测试sp_combined_stock_analysis()...")
        try:
            cursor.execute("CALL sp_combined_stock_analysis()")
            results = cursor.fetchall()
            print(f"   ✅ 成功！返回 {len(results)} 条记录")
            
            if results:
                print(f"   📊 最新数据: {results[0][:3]}...")
                
        except mysql.connector.Error as e:
            print(f"   ❌ 失败: {e}")
        
        # 清理结果集
        cursor.nextset()
        
        # 测试2: 直接查询MoneyFlowRatio列
        print("\n2. 测试直接查询MoneyFlowRatio...")
        try:
            cursor.execute("""
                SELECT Date, Close, MoneyFlowRatio, MFI, TradingSignal
                FROM eab_0023hk 
                WHERE MoneyFlowRatio > 1.0
                ORDER BY Date DESC 
                LIMIT 5
            """)
            results = cursor.fetchall()
            print(f"   ✅ 成功！找到 {len(results)} 条MoneyFlowRatio > 1.0的记录")
            
            for row in results:
                signal_text = "做多" if row[4] == 1 else "做空" if row[4] == -1 else "观望"
                print(f"   📊 {row[0]} | 价格:{row[1]:.2f} | MFR:{row[2]:.4f} | {signal_text}")
                
        except mysql.connector.Error as e:
            print(f"   ❌ 失败: {e}")
        
        # 测试3: 检查存储过程列表
        print("\n3. 检查存储过程列表...")
        try:
            cursor.execute("SHOW PROCEDURE STATUS WHERE Db = 'finance'")
            procedures = cursor.fetchall()
            print(f"   ✅ 找到 {len(procedures)} 个存储过程:")
            
            for proc in procedures:
                proc_name = proc[1]
                print(f"     - {proc_name}")
                
        except mysql.connector.Error as e:
            print(f"   ❌ 失败: {e}")
        
        # 测试4: 获取最新市场数据
        print("\n4. 测试获取最新市场数据...")
        try:
            cursor.execute("""
                SELECT 
                    Date,
                    Close,
                    MoneyFlowRatio,
                    MFI,
                    Y_Value,
                    X_Value,
                    E_Value,
                    TradingSignal
                FROM eab_0023hk 
                ORDER BY Date DESC 
                LIMIT 1
            """)
            result = cursor.fetchone()
            
            if result:
                print(f"   ✅ 最新EAB数据 ({result[0]}):")
                print(f"     💰 收盘价: {result[1]:.2f}")
                print(f"     📊 MoneyFlowRatio: {result[2]:.4f}")
                print(f"     📊 MFI: {result[3]:.1f}")
                print(f"     📊 Y值: {result[4]:.4f}")
                print(f"     📊 X值: {result[5]:.4f}")
                print(f"     📊 E值: {result[6]:.4f}")
                
                signal_text = "做多" if result[7] == 1 else "做空" if result[7] == -1 else "观望"
                print(f"     🎯 交易信号: {signal_text}")
            else:
                print(f"   ⚠️ 没有找到数据")
                
        except mysql.connector.Error as e:
            print(f"   ❌ 失败: {e}")
        
        cursor.close()
        conn.close()
        
        print(f"\n🎉 MoneyFlowRatio错误修复验证完成!")
        print(f"💡 现在可以正常使用所有MoneyFlow相关功能")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

if __name__ == "__main__":
    test_moneyflow_fix()
