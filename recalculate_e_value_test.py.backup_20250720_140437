#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
重新计算test表的E值
==================

使用正确的定义重新计算E值：
- X = 资金流比例 (资金流入比例)
- Y = 控制系数 (控股商托价概率)
- E = 8xy - 3x - 3y + 1

作者: Cosmoon NG
日期: 2025年7月
"""

import pymysql
import pandas as pd

class TestTableEValueRecalculator:
    def __init__(self):
        """初始化E值重新计算器"""
        self.db_config = {
            'host': '************',
            'port': 3306,
            'database': 'finance',
            'user': 'root',
            'password': '',
            'charset': 'utf8mb4'
        }
        self.connection = None
        
    def connect_database(self):
        """连接数据库"""
        try:
            self.connection = pymysql.connect(**self.db_config)
            print(f"✅ 成功连接MySQL数据库: {self.db_config['host']}")
            return True
        except Exception as e:
            print(f"❌ 连接数据库失败: {e}")
            return False
    
    def check_test_table_columns(self):
        """检查test表的相关列"""
        try:
            cursor = self.connection.cursor()
            
            # 检查表结构
            cursor.execute("DESCRIBE test")
            columns = cursor.fetchall()
            
            column_names = [col[0] for col in columns]
            
            print("📊 test表相关列检查:")
            required_columns = ['资金流比例', '控制系数', 'E值']
            
            for col in required_columns:
                if col in column_names:
                    print(f"   ✅ {col}: 存在")
                else:
                    print(f"   ❌ {col}: 不存在")
            
            # 显示前3条数据
            cursor.execute("""
                SELECT 交易序号, `资金流比例`, `控制系数`, E值
                FROM test 
                ORDER BY 交易序号
                LIMIT 3
            """)
            
            sample_data = cursor.fetchall()
            
            print(f"\n📋 前3条数据示例:")
            print("-" * 60)
            print(f"{'序号':<4} {'资金流比例(X)':<12} {'控制系数(Y)':<12} {'当前E值':<10}")
            print("-" * 60)
            
            for trade_id, x_val, y_val, e_val in sample_data:
                print(f"{trade_id:<4} {float(x_val):<12.6f} {float(y_val):<12.6f} {float(e_val):<10.6f}")
            
            return True
            
        except Exception as e:
            print(f"❌ 检查test表列失败: {e}")
            return False
    
    def recalculate_e_values(self):
        """重新计算E值"""
        try:
            print("\n🧮 重新计算E值...")
            print("📊 公式: E = 8xy - 3x - 3y + 1")
            print("   其中: x = 资金流比例, y = 控制系数")
            
            cursor = self.connection.cursor()
            
            # 获取所有记录
            cursor.execute("""
                SELECT 交易序号, `资金流比例`, `控制系数`, E值
                FROM test 
                ORDER BY 交易序号
            """)
            
            records = cursor.fetchall()
            
            if not records:
                print("❌ test表中没有数据")
                return False
            
            print(f"📊 处理 {len(records)} 条记录...")
            
            # 计算新的E值
            updates = []
            
            for trade_id, x_val, y_val, old_e_val in records:
                x = float(x_val)  # 资金流比例
                y = float(y_val)  # 控制系数
                
                # 计算新的E值: E = 8xy - 3x - 3y + 1
                new_e_val = 8 * x * y - 3 * x - 3 * y + 1
                
                updates.append((new_e_val, trade_id))
                
                # 显示前15条计算过程
                if trade_id <= 15:
                    print(f"   交易{trade_id}: X={x:.6f}, Y={y:.6f}, "
                          f"旧E={float(old_e_val):.6f}, 新E={new_e_val:.6f}")
            
            # 批量更新E值
            update_sql = "UPDATE test SET E值 = %s WHERE 交易序号 = %s"
            cursor.executemany(update_sql, updates)
            self.connection.commit()
            
            print(f"✅ 成功重新计算并更新 {len(updates)} 条记录的E值")
            return True
            
        except Exception as e:
            print(f"❌ 重新计算E值失败: {e}")
            return False
    
    def verify_e_calculation(self):
        """验证E值计算结果"""
        try:
            cursor = self.connection.cursor()
            
            # 获取前20条记录验证
            cursor.execute("""
                SELECT 交易序号, `资金流比例`, `控制系数`, E值
                FROM test 
                ORDER BY 交易序号 
                LIMIT 20
            """)
            
            verification_data = cursor.fetchall()
            
            print("\n📊 E值计算验证 (前20条记录):")
            print("="*80)
            print(f"{'序号':<4} {'X(资金流)':<10} {'Y(控制)':<10} {'计算E值':<10} {'数据库E值':<10} {'差异':<8}")
            print("-" * 80)
            
            for trade_id, x_val, y_val, db_e_val in verification_data:
                x = float(x_val)
                y = float(y_val)
                calculated_e = 8 * x * y - 3 * x - 3 * y + 1
                db_e = float(db_e_val)
                difference = abs(calculated_e - db_e)
                
                print(f"{trade_id:<4} {x:<10.6f} {y:<10.6f} {calculated_e:<10.6f} {db_e:<10.6f} {difference:<8.6f}")
            
            # 统计信息
            cursor.execute("""
                SELECT 
                    COUNT(*) as total_records,
                    MIN(E值) as min_e,
                    MAX(E值) as max_e,
                    AVG(E值) as avg_e,
                    AVG(`资金流比例`) as avg_x,
                    AVG(`控制系数`) as avg_y
                FROM test
            """)
            
            stats = cursor.fetchone()
            total, min_e, max_e, avg_e, avg_x, avg_y = stats
            
            print(f"\n📈 重新计算后的E值统计:")
            print(f"   • 总记录数: {total}")
            print(f"   • E值范围: {float(min_e):.6f} 至 {float(max_e):.6f}")
            print(f"   • E值平均值: {float(avg_e):.6f}")
            print(f"   • X值平均值: {float(avg_x):.6f}")
            print(f"   • Y值平均值: {float(avg_y):.6f}")
            
            return True
            
        except Exception as e:
            print(f"❌ 验证E值计算失败: {e}")
            return False
    
    def analyze_e_value_distribution(self):
        """分析E值分布"""
        try:
            cursor = self.connection.cursor()
            
            # E值分布分析
            cursor.execute("""
                SELECT 
                    CASE 
                        WHEN E值 > 0 THEN 'E>0 (有利)'
                        WHEN E值 = 0 THEN 'E=0 (中性)'
                        ELSE 'E<0 (不利)'
                    END as e_category,
                    COUNT(*) as count,
                    AVG(E值) as avg_e,
                    AVG(`资金流比例`) as avg_x,
                    AVG(`控制系数`) as avg_y,
                    AVG(净利润) as avg_profit
                FROM test 
                GROUP BY e_category
                ORDER BY avg_e DESC
            """)
            
            e_distribution = cursor.fetchall()
            
            print(f"\n📊 E值分布分析:")
            print("-" * 80)
            print(f"{'E值区间':<12} {'次数':<6} {'平均E值':<10} {'平均X值':<10} {'平均Y值':<10} {'平均盈亏':<10}")
            print("-" * 80)
            
            for category, count, avg_e, avg_x, avg_y, avg_profit in e_distribution:
                print(f"{category:<12} {count:<6} {float(avg_e):<10.6f} {float(avg_x):<10.6f} "
                      f"{float(avg_y):<10.6f} {int(avg_profit):<10}")
            
            # 按策略区域分析E值
            cursor.execute("""
                SELECT 策略区域,
                       COUNT(*) as count,
                       AVG(E值) as avg_e,
                       MIN(E值) as min_e,
                       MAX(E值) as max_e,
                       AVG(净利润) as avg_profit
                FROM test 
                GROUP BY 策略区域
                ORDER BY avg_e DESC
            """)
            
            zone_e_stats = cursor.fetchall()
            
            print(f"\n📊 按策略区域的E值分析:")
            print("-" * 80)
            print(f"{'策略区域':<15} {'次数':<6} {'平均E值':<10} {'E值范围':<20} {'平均盈亏':<10}")
            print("-" * 80)
            
            for zone, count, avg_e, min_e, max_e, avg_profit in zone_e_stats:
                e_range = f"{float(min_e):.3f}~{float(max_e):.3f}"
                print(f"{zone:<15} {count:<6} {float(avg_e):<10.6f} {e_range:<20} {int(avg_profit):<10}")
            
            # 分析E值与盈亏的相关性
            cursor.execute("""
                SELECT 
                    CASE 
                        WHEN E值 >= 0.5 THEN 'E高(≥0.5)'
                        WHEN E值 >= 0 THEN 'E中(0~0.5)'
                        WHEN E值 >= -0.5 THEN 'E低(-0.5~0)'
                        ELSE 'E很低(<-0.5)'
                    END as e_level,
                    COUNT(*) as count,
                    AVG(净利润) as avg_profit,
                    SUM(净利润) as total_profit,
                    AVG(E值) as avg_e
                FROM test 
                GROUP BY e_level
                ORDER BY avg_e DESC
            """)
            
            e_profit_stats = cursor.fetchall()
            
            print(f"\n📈 E值与盈亏相关性分析:")
            print("-" * 70)
            print(f"{'E值水平':<12} {'次数':<6} {'平均盈亏':<10} {'总盈亏':<10} {'平均E值':<10}")
            print("-" * 70)
            
            for level, count, avg_profit, total_profit, avg_e in e_profit_stats:
                print(f"{level:<12} {count:<6} {int(avg_profit):<10} {int(total_profit):<10} {float(avg_e):<10.6f}")
            
            return True
            
        except Exception as e:
            print(f"❌ 分析E值分布失败: {e}")
            return False
    
    def show_extreme_e_values(self):
        """显示极值E值的交易"""
        try:
            cursor = self.connection.cursor()
            
            # 最高E值的交易
            cursor.execute("""
                SELECT 交易序号, 开仓日期, `资金流比例`, `控制系数`, E值, 净利润, 策略区域
                FROM test 
                ORDER BY E值 DESC
                LIMIT 5
            """)
            
            highest_e = cursor.fetchall()
            
            print(f"\n🔝 最高E值的5笔交易:")
            print("-" * 90)
            print(f"{'序号':<4} {'日期':<12} {'X值':<8} {'Y值':<8} {'E值':<10} {'盈亏':<8} {'策略区域':<12}")
            print("-" * 90)
            
            for trade_id, date, x_val, y_val, e_val, profit, zone in highest_e:
                print(f"{trade_id:<4} {date:<12} {float(x_val):<8.3f} {float(y_val):<8.3f} "
                      f"{float(e_val):<10.6f} {int(profit):<8} {zone:<12}")
            
            # 最低E值的交易
            cursor.execute("""
                SELECT 交易序号, 开仓日期, `资金流比例`, `控制系数`, E值, 净利润, 策略区域
                FROM test 
                ORDER BY E值 ASC
                LIMIT 5
            """)
            
            lowest_e = cursor.fetchall()
            
            print(f"\n🔻 最低E值的5笔交易:")
            print("-" * 90)
            print(f"{'序号':<4} {'日期':<12} {'X值':<8} {'Y值':<8} {'E值':<10} {'盈亏':<8} {'策略区域':<12}")
            print("-" * 90)
            
            for trade_id, date, x_val, y_val, e_val, profit, zone in lowest_e:
                print(f"{trade_id:<4} {date:<12} {float(x_val):<8.3f} {float(y_val):<8.3f} "
                      f"{float(e_val):<10.6f} {int(profit):<8} {zone:<12}")
            
            return True
            
        except Exception as e:
            print(f"❌ 显示极值E值失败: {e}")
            return False
    
    def close_connection(self):
        """关闭数据库连接"""
        if self.connection:
            self.connection.close()
            print("\n🔒 数据库连接已关闭")

def main():
    """主函数"""
    print("🏦 重新计算test表的E值")
    print("="*50)
    print("📊 使用正确定义:")
    print("   • X = 资金流比例 (资金流入比例)")
    print("   • Y = 控制系数 (控股商托价概率)")
    print("   • E = 8xy - 3x - 3y + 1")
    
    # 创建重新计算器
    recalculator = TestTableEValueRecalculator()
    
    # 连接数据库
    if not recalculator.connect_database():
        return
    
    # 检查表列
    if not recalculator.check_test_table_columns():
        recalculator.close_connection()
        return
    
    # 重新计算E值
    if not recalculator.recalculate_e_values():
        recalculator.close_connection()
        return
    
    # 验证计算结果
    recalculator.verify_e_calculation()
    
    # 分析E值分布
    recalculator.analyze_e_value_distribution()
    
    # 显示极值E值
    recalculator.show_extreme_e_values()
    
    # 关闭连接
    recalculator.close_connection()
    
    print("\n🎉 test表E值重新计算完成!")
    print("📊 E = 8xy - 3x - 3y + 1")
    print("💡 现在E值基于正确的资金流比例和控制系数计算")

if __name__ == "__main__":
    main()
