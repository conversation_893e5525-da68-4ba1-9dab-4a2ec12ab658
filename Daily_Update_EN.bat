@echo off
title Daily Update System - Position Management

echo.
echo ========================================
echo Daily Update System Starting
echo ========================================
echo Date: %date%
echo Time: %time%
echo.

REM Switch to script directory
echo Switching to working directory...
cd /d "D:\Users\Cosmoon NG\Documents\VSCode\Python\Finance\EAs\Investment02"

REM Verify directory switch was successful
if not exist "complete_daily_update_with_position.py" (
    echo ERROR: Cannot find working directory or files
    echo Current directory: %cd%
    echo Please check if the path is correct
    pause
    exit /b 1
)

echo Current working directory: %cd%
echo.

REM Check if Python is available
echo Checking Python environment...
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python not installed or not in PATH
    echo Please ensure Python is properly installed and added to system PATH
    pause
    exit /b 1
)

python --version
echo SUCCESS: Python environment check passed
echo.

REM Check if required files exist
echo Checking required files...
if not exist "complete_daily_update_with_position.py" (
    echo ERROR: Missing file: complete_daily_update_with_position.py
    pause
    exit /b 1
)

if not exist "position_manager_with_excel.py" (
    echo ERROR: Missing file: position_manager_with_excel.py
    pause
    exit /b 1
)

if not exist "fixed_daily_update_eab_table.py" (
    echo WARNING: Missing file: fixed_daily_update_eab_table.py (optional)
)

if not exist "fixed_update_full_y_controller.py" (
    echo WARNING: Missing file: fixed_update_full_y_controller.py (optional)
)

echo SUCCESS: Required files check passed
echo.

echo ========================================
echo Starting Complete Daily Update Tasks
echo ========================================
echo Including the following functions:
echo 1. Database Update (Get latest market data)
echo 2. Full_Y Field Update (Cumulative Controller=1 ratio)
echo 3. Position Decision and Excel Update (Smart trading decisions)
echo 4. Daily Report Generation (Comprehensive analysis report)
echo.
echo Executing, please wait...
echo.

REM Record start time
set start_time=%time%

REM Execute Python update script
python complete_daily_update_with_position.py

REM Record end time
set end_time=%time%

REM Check execution result
if errorlevel 1 (
    echo.
    echo ========================================
    echo ERROR: Update tasks partially failed
    echo ========================================
    echo Please check the error information above
    echo.
    echo TIPS:
    echo   - If position management succeeded, Excel file has been updated
    echo   - Database connection issues may cause partial function failures
    echo   - Check network connection and database service status
    echo.
) else (
    echo.
    echo ========================================
    echo SUCCESS: Update tasks executed successfully
    echo ========================================
    echo All tasks completed!
    echo.
    echo Execution content:
    echo   SUCCESS: Database update completed
    echo   SUCCESS: Full_Y field update completed
    echo   SUCCESS: Position decision completed
    echo   SUCCESS: Excel file update completed
    echo   SUCCESS: Daily report generation completed
    echo.
)

echo ========================================
echo Execution Summary
echo ========================================
echo Start time: %start_time%
echo End time: %end_time%
echo Execution date: %date%
echo.

REM Display Excel file status
echo Excel file status check:
if exist "交易记录追踪0023HK.xlsx" (
    echo SUCCESS: Main file exists: 交易记录追踪0023HK.xlsx
    
    REM Check for latest backup file
    for /f "delims=" %%i in ('dir /b /od "交易记录追踪0023HK_backup_*.xlsx" 2^>nul') do set latest_backup=%%i
    if defined latest_backup (
        echo SUCCESS: Latest backup: %latest_backup%
    ) else (
        echo WARNING: No backup files found
    )
) else (
    echo ERROR: Excel file does not exist or is being used
    echo Possible reasons:
    echo   - Excel file is currently open in another program
    echo   - File permission issues
    echo   - Program execution failed
)

echo.
echo Next update recommendations:
echo   - Execute daily after 5:00 PM
echo   - Ensure Excel file is not open
echo   - Check network connection status
echo.

REM Ask if user wants to view logs
set /p view_log="Do you want to view detailed execution logs? (y/n): "
if /i "%view_log%"=="y" (
    echo.
    echo Detailed logs please check the output content above
    echo To save logs, use: Daily_Update_EN.bat ^> daily_log.txt
    echo.
)

echo ========================================
echo Program Execution Completed
echo ========================================
echo Thank you for using Cosmoon Trading Signal System!
echo.
echo Press any key to exit...
pause >nul
