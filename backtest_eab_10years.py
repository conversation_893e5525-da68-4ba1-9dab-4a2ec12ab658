#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
东亚银行10年回测系统
==================
使用2500港元初始资本回测东亚银行10年数据
"""

import yfinance as yf
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

class EABBacktester:
    """东亚银行回测器"""
    
    def __init__(self):
        """初始化回测参数"""
        self.symbol = "0023.HK"
        self.initial_capital = 2500.0  # 初始资本2500港元
        self.position_size = 0.95      # 95%仓位
        
        # 止盈止损参数
        self.take_profit_long = 0.016   # 多头止盈1.6%
        self.stop_loss_long = 0.008     # 多头止损0.8%
        self.take_profit_short = 0.008  # 空头止盈0.8%
        self.stop_loss_short = 0.016    # 空头止损1.6%
        
        # 交易成本
        self.commission_rate = 0.0025   # 0.25%手续费
        
    def get_10year_data(self):
        """获取10年历史数据"""
        print("📊 获取东亚银行10年历史数据...")
        
        try:
            # 获取10年数据
            end_date = datetime.now()
            start_date = end_date - timedelta(days=10*365)
            
            eab = yf.Ticker(self.symbol)
            hist_data = eab.history(start=start_date, end=end_date)
            
            if hist_data.empty:
                print("❌ 未能获取到历史数据")
                return None
            
            # 数据预处理
            hist_data.reset_index(inplace=True)
            df = pd.DataFrame({
                'Date': hist_data['Date'].dt.date,
                'Open': hist_data['Open'],
                'High': hist_data['High'],
                'Low': hist_data['Low'],
                'Close': hist_data['Close'],
                'Volume': hist_data['Volume']
            })
            
            # 数据清理
            df = df.dropna()
            df = df.sort_values('Date').reset_index(drop=True)
            
            print(f"✅ 成功获取 {len(df):,} 条历史记录")
            print(f"📊 数据期间: {df['Date'].min()} 至 {df['Date'].max()}")
            print(f"💰 起始价格: {df['Close'].iloc[0]:.2f} 港元")
            print(f"💰 最新价格: {df['Close'].iloc[-1]:.2f} 港元")
            
            return df
            
        except Exception as e:
            print(f"❌ 获取数据失败: {e}")
            return None
    
    def calculate_indicators(self, df):
        """计算技术指标"""
        print("📊 计算技术指标...")
        
        df = df.copy()
        
        # 1. 计算Y指标 (价格在20日区间的位置)
        window = 20
        df['High_20'] = df['High'].rolling(window).max()
        df['Low_20'] = df['Low'].rolling(window).min()
        df['Y'] = (df['Close'] - df['Low_20']) / (df['High_20'] - df['Low_20'])
        df['Y'] = df['Y'].fillna(0.5).clip(0, 1)
        
        # 2. 计算X指标 (MoneyFlowRatio相关)
        # 典型价格和资金流
        df['TypicalPrice'] = (df['High'] + df['Low'] + df['Close']) / 3
        df['MoneyFlow'] = df['TypicalPrice'] * df['Volume']
        
        # 价格变化方向
        df['PriceChange'] = df['TypicalPrice'].diff()
        df['PositiveMoneyFlow'] = np.where(df['PriceChange'] > 0, df['MoneyFlow'], 0)
        df['NegativeMoneyFlow'] = np.where(df['PriceChange'] < 0, df['MoneyFlow'], 0)
        
        # 14日资金流比率
        period = 14
        df['PositiveMF_14'] = df['PositiveMoneyFlow'].rolling(period).sum()
        df['NegativeMF_14'] = df['NegativeMoneyFlow'].rolling(period).sum()
        df['MoneyFlowRatio'] = df['PositiveMF_14'] / (df['NegativeMF_14'] + 1e-10)
        
        # MFI
        df['MFI'] = 100 - (100 / (1 + df['MoneyFlowRatio']))
        
        # 简化的X值计算
        df['X'] = np.clip(df['MoneyFlowRatio'] / 10, 0, 1)  # 归一化到0-1
        
        # 3. 计算E指标
        df['E'] = (8 * df['X'] - 3) * df['Y'] - 3 * df['X'] + 1
        
        # 4. 计算移动平均线
        df['MA20'] = df['Close'].rolling(20).mean()
        df['MA60'] = df['Close'].rolling(60).mean()
        
        # 5. 计算RSI
        delta = df['Close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
        rs = gain / (loss + 1e-10)
        df['RSI'] = 100 - (100 / (1 + rs))
        
        print("✅ 技术指标计算完成")
        return df
    
    def generate_signals(self, df):
        """生成交易信号"""
        print("🎯 生成交易信号...")
        
        df = df.copy()
        signals = []
        
        for i, row in df.iterrows():
            if (pd.isna(row['Y']) or pd.isna(row['X']) or pd.isna(row['E']) or 
                pd.isna(row['RSI']) or pd.isna(row['MA20'])):
                signals.append(0)
                continue
            
            y_val = row['Y']
            x_val = row['X']
            e_val = row['E']
            rsi = row['RSI']
            price = row['Close']
            ma20 = row['MA20']
            
            # 多头信号条件
            long_condition = (
                y_val > 0.45 and 
                x_val > 0.45 and 
                e_val > 0 and 
                rsi < 70 and
                price > ma20
            )
            
            # 空头信号条件
            short_condition = (
                (y_val < 0.3 or x_val < 0.3 or e_val < -0.5) and
                (rsi > 30) and
                price < ma20
            )
            
            if long_condition:
                signals.append(1)
            elif short_condition:
                signals.append(-1)
            else:
                signals.append(0)
        
        df['Signal'] = signals
        
        # 统计信号
        signal_counts = df['Signal'].value_counts()
        print(f"📊 信号统计:")
        for signal, count in signal_counts.items():
            signal_name = "🟢 做多" if signal == 1 else "🔴 做空" if signal == -1 else "⚪ 观望"
            print(f"   {signal_name}: {count}次 ({count/len(df)*100:.1f}%)")
        
        return df
    
    def run_backtest(self, df):
        """运行回测"""
        print(f"\n💰 开始回测 (初始资本: {self.initial_capital:,.0f}港元)")
        print("=" * 80)
        
        # 初始化变量
        capital = self.initial_capital
        position = 0  # 0=空仓, 1=多头, -1=空头
        shares = 0
        entry_price = 0
        entry_date = None
        take_profit_price = 0
        stop_loss_price = 0
        
        # 记录交易
        trades = []
        equity_curve = []
        max_capital = capital
        max_drawdown = 0
        
        for i, row in df.iterrows():
            current_date = row['Date']
            current_price = row['Close']
            signal = row['Signal']
            
            # 记录资金曲线
            if position == 0:
                current_equity = capital
            else:
                if position == 1:  # 多头
                    current_equity = capital + shares * (current_price - entry_price)
                else:  # 空头
                    current_equity = capital + shares * (entry_price - current_price)
            
            equity_curve.append({
                'Date': current_date,
                'Equity': current_equity,
                'Price': current_price,
                'Position': position
            })
            
            # 更新最大回撤
            if current_equity > max_capital:
                max_capital = current_equity
            
            drawdown = (max_capital - current_equity) / max_capital
            if drawdown > max_drawdown:
                max_drawdown = drawdown
            
            # 检查平仓条件
            if position != 0:
                should_exit = False
                exit_reason = ""
                exit_price = current_price
                
                if position == 1:  # 多头
                    if row['High'] >= take_profit_price:
                        should_exit = True
                        exit_reason = "止盈"
                        exit_price = take_profit_price
                    elif row['Low'] <= stop_loss_price:
                        should_exit = True
                        exit_reason = "止损"
                        exit_price = stop_loss_price
                    elif signal == -1:  # 反向信号
                        should_exit = True
                        exit_reason = "反向信号"
                        exit_price = current_price
                
                elif position == -1:  # 空头
                    if row['Low'] <= take_profit_price:
                        should_exit = True
                        exit_reason = "止盈"
                        exit_price = take_profit_price
                    elif row['High'] >= stop_loss_price:
                        should_exit = True
                        exit_reason = "止损"
                        exit_price = stop_loss_price
                    elif signal == 1:  # 反向信号
                        should_exit = True
                        exit_reason = "反向信号"
                        exit_price = current_price
                
                if should_exit:
                    # 计算收益
                    if position == 1:  # 多头
                        gross_profit = shares * (exit_price - entry_price)
                    else:  # 空头
                        gross_profit = shares * (entry_price - exit_price)
                    
                    # 扣除手续费
                    commission = shares * exit_price * self.commission_rate
                    net_profit = gross_profit - commission
                    
                    capital += net_profit
                    holding_days = (current_date - entry_date).days
                    
                    # 记录交易
                    trades.append({
                        'entry_date': entry_date,
                        'exit_date': current_date,
                        'direction': '多头' if position == 1 else '空头',
                        'entry_price': entry_price,
                        'exit_price': exit_price,
                        'shares': shares,
                        'gross_profit': gross_profit,
                        'commission': commission,
                        'net_profit': net_profit,
                        'holding_days': holding_days,
                        'exit_reason': exit_reason,
                        'capital_after': capital
                    })
                    
                    position = 0
                    shares = 0
            
            # 检查开仓条件
            if position == 0 and signal != 0:
                position = signal
                entry_price = current_price
                entry_date = current_date
                
                # 计算仓位
                position_value = capital * self.position_size
                shares = int(position_value / entry_price)
                
                # 扣除开仓手续费
                open_commission = shares * entry_price * self.commission_rate
                capital -= open_commission
                
                # 计算止盈止损位
                if position == 1:  # 多头
                    take_profit_price = entry_price * (1 + self.take_profit_long)
                    stop_loss_price = entry_price * (1 - self.stop_loss_long)
                else:  # 空头
                    take_profit_price = entry_price * (1 - self.take_profit_short)
                    stop_loss_price = entry_price * (1 + self.stop_loss_short)
        
        # 如果最后还有持仓，强制平仓
        if position != 0:
            final_price = df['Close'].iloc[-1]
            if position == 1:
                gross_profit = shares * (final_price - entry_price)
            else:
                gross_profit = shares * (entry_price - final_price)
            
            commission = shares * final_price * self.commission_rate
            net_profit = gross_profit - commission
            capital += net_profit
            
            trades.append({
                'entry_date': entry_date,
                'exit_date': df['Date'].iloc[-1],
                'direction': '多头' if position == 1 else '空头',
                'entry_price': entry_price,
                'exit_price': final_price,
                'shares': shares,
                'gross_profit': gross_profit,
                'commission': commission,
                'net_profit': net_profit,
                'holding_days': (df['Date'].iloc[-1] - entry_date).days,
                'exit_reason': '强制平仓',
                'capital_after': capital
            })
        
        return trades, equity_curve, max_drawdown
    
    def analyze_results(self, trades, equity_curve, max_drawdown, df):
        """分析回测结果"""
        print(f"\n📊 回测结果分析")
        print("=" * 80)
        
        if not trades:
            print("❌ 没有交易记录")
            return
        
        # 基本统计
        final_capital = trades[-1]['capital_after'] if trades else self.initial_capital
        total_return = (final_capital - self.initial_capital) / self.initial_capital * 100
        
        print(f"💰 资金表现:")
        print(f"   初始资金: {self.initial_capital:,.2f} 港元")
        print(f"   最终资金: {final_capital:,.2f} 港元")
        print(f"   总收益: {final_capital - self.initial_capital:+,.2f} 港元")
        print(f"   总收益率: {total_return:+.2f}%")
        print(f"   最大回撤: {max_drawdown*100:.2f}%")
        
        # 交易统计
        total_trades = len(trades)
        winning_trades = len([t for t in trades if t['net_profit'] > 0])
        losing_trades = len([t for t in trades if t['net_profit'] < 0])
        win_rate = winning_trades / total_trades * 100 if total_trades > 0 else 0
        
        print(f"\n📊 交易统计:")
        print(f"   总交易次数: {total_trades}")
        print(f"   盈利交易: {winning_trades}次")
        print(f"   亏损交易: {losing_trades}次")
        print(f"   胜率: {win_rate:.1f}%")
        
        if trades:
            profits = [t['net_profit'] for t in trades]
            avg_profit = np.mean(profits)
            max_profit = max(profits)
            max_loss = min(profits)
            
            print(f"   平均每笔收益: {avg_profit:+.2f} 港元")
            print(f"   最大盈利: {max_profit:+.2f} 港元")
            print(f"   最大亏损: {max_loss:+.2f} 港元")
            
            # 持仓时间统计
            holding_days = [t['holding_days'] for t in trades]
            avg_holding = np.mean(holding_days)
            print(f"   平均持仓天数: {avg_holding:.1f}天")
        
        # 年化收益率
        start_date = df['Date'].iloc[0]
        end_date = df['Date'].iloc[-1]
        years = (end_date - start_date).days / 365.25
        annual_return = (final_capital / self.initial_capital) ** (1/years) - 1
        
        print(f"\n📈 年化表现:")
        print(f"   回测期间: {years:.1f}年")
        print(f"   年化收益率: {annual_return*100:+.2f}%")
        
        # 买入持有策略对比
        start_price = df['Close'].iloc[0]
        end_price = df['Close'].iloc[-1]
        buy_hold_return = (end_price - start_price) / start_price * 100
        
        print(f"\n🆚 买入持有对比:")
        print(f"   买入持有收益率: {buy_hold_return:+.2f}%")
        print(f"   策略超额收益: {total_return - buy_hold_return:+.2f}%")
        
        # 显示最近10笔交易
        print(f"\n📅 最近10笔交易:")
        print("-" * 120)
        recent_trades = trades[-10:] if len(trades) >= 10 else trades
        
        for trade in recent_trades:
            print(f"   {trade['entry_date']} → {trade['exit_date']} | "
                  f"{trade['direction']} | 价格:{trade['entry_price']:.2f}→{trade['exit_price']:.2f} | "
                  f"收益:{trade['net_profit']:+.2f} | {trade['exit_reason']} | "
                  f"资金:{trade['capital_after']:,.0f}")
        
        return {
            'final_capital': final_capital,
            'total_return': total_return,
            'annual_return': annual_return,
            'max_drawdown': max_drawdown,
            'total_trades': total_trades,
            'win_rate': win_rate,
            'buy_hold_return': buy_hold_return
        }
    
    def run_complete_backtest(self):
        """运行完整回测"""
        print("🎯 东亚银行10年回测系统")
        print("=" * 80)
        
        try:
            # 获取数据
            df = self.get_10year_data()
            if df is None:
                return None
            
            # 计算指标
            df = self.calculate_indicators(df)
            
            # 生成信号
            df = self.generate_signals(df)
            
            # 运行回测
            trades, equity_curve, max_drawdown = self.run_backtest(df)
            
            # 分析结果
            results = self.analyze_results(trades, equity_curve, max_drawdown, df)
            
            print(f"\n🎉 回测完成！")
            
            return {
                'results': results,
                'trades': trades,
                'equity_curve': equity_curve,
                'data': df
            }
            
        except Exception as e:
            print(f"❌ 回测失败: {e}")
            return None

def main():
    """主函数"""
    backtester = EABBacktester()
    backtest_results = backtester.run_complete_backtest()
    
    if backtest_results:
        print(f"\n💡 回测总结:")
        results = backtest_results['results']
        print(f"   📊 10年投资2500港元的结果:")
        print(f"   💰 最终资金: {results['final_capital']:,.2f}港元")
        print(f"   📈 总收益率: {results['total_return']:+.2f}%")
        print(f"   📊 年化收益率: {results['annual_return']*100:+.2f}%")
        print(f"   🎯 胜率: {results['win_rate']:.1f}%")
        print(f"   📉 最大回撤: {results['max_drawdown']*100:.2f}%")

if __name__ == "__main__":
    main()
