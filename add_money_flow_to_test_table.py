#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
为test表补全真实资金流数据
========================

功能：
1. 连接到MySQL数据库的test表
2. 添加真实资金流相关列
3. 从HK00023数据计算并更新资金流数据
4. 验证更新结果

作者: Cosmoon NG
日期: 2025年7月
"""

import pymysql
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

class TestTableMoneyFlowUpdater:
    def __init__(self):
        """初始化test表资金流更新器"""
        self.db_config = {
            'host': 'localhost',
            'port': 3306,
            'database': 'finance',
            'user': 'root',
            'password': '12345678',
            'charset': 'utf8mb4'
        }
        self.connection = None
        
    def connect_database(self):
        """连接数据库"""
        try:
            self.connection = pymysql.connect(**self.db_config)
            print(f"✅ 成功连接MySQL数据库: {self.db_config['host']}")
            return True
        except Exception as e:
            print(f"❌ 连接数据库失败: {e}")
            return False
    
    def check_test_table(self):
        """检查test表结构"""
        try:
            cursor = self.connection.cursor()
            
            # 检查test表是否存在
            cursor.execute("SHOW TABLES LIKE 'test'")
            if not cursor.fetchone():
                print("❌ test表不存在")
                return False
            
            # 检查表结构
            cursor.execute("DESCRIBE test")
            columns = cursor.fetchall()
            
            print("📊 test表当前结构:")
            for col in columns:
                print(f"   • {col[0]} ({col[1]})")
            
            # 检查数据量
            cursor.execute("SELECT COUNT(*) FROM test")
            count = cursor.fetchone()[0]
            print(f"\n📈 test表记录数: {count:,}")
            
            # 检查日期范围
            cursor.execute("SELECT MIN(date), MAX(date) FROM test")
            date_range = cursor.fetchone()
            print(f"📅 日期范围: {date_range[0]} 至 {date_range[1]}")
            
            return True
            
        except Exception as e:
            print(f"❌ 检查test表失败: {e}")
            return False
    
    def add_money_flow_columns(self):
        """为test表添加资金流列"""
        try:
            cursor = self.connection.cursor()
            
            # 检查是否已有资金流列
            cursor.execute("SHOW COLUMNS FROM test LIKE 'money_flow_%'")
            existing_flow_columns = cursor.fetchall()
            
            if existing_flow_columns:
                print("⚠️ test表已存在资金流列:")
                for col in existing_flow_columns:
                    print(f"   • {col[0]}")
                
                response = input("是否要重新创建资金流列? (y/n): ")
                if response.lower() != 'y':
                    return True
                
                # 删除现有资金流列
                flow_columns = ['money_flow_in', 'money_flow_out', 'net_money_flow', 
                               'money_flow_ratio', 'money_flow_intensity', 'cumulative_money_flow']
                for col in flow_columns:
                    try:
                        cursor.execute(f"ALTER TABLE test DROP COLUMN {col}")
                    except:
                        pass
            
            # 添加资金流列
            alter_statements = [
                "ALTER TABLE test ADD COLUMN money_flow_in DECIMAL(15,2) DEFAULT 0 COMMENT '资金流入'",
                "ALTER TABLE test ADD COLUMN money_flow_out DECIMAL(15,2) DEFAULT 0 COMMENT '资金流出'",
                "ALTER TABLE test ADD COLUMN net_money_flow DECIMAL(15,2) DEFAULT 0 COMMENT '净资金流'",
                "ALTER TABLE test ADD COLUMN money_flow_ratio DECIMAL(8,6) DEFAULT 0.5 COMMENT '资金流入比例'",
                "ALTER TABLE test ADD COLUMN money_flow_intensity DECIMAL(8,4) DEFAULT 1.0 COMMENT '资金流强度'",
                "ALTER TABLE test ADD COLUMN cumulative_money_flow DECIMAL(20,2) DEFAULT 0 COMMENT '累积资金流'"
            ]
            
            for statement in alter_statements:
                cursor.execute(statement)
            
            self.connection.commit()
            print("✅ 成功添加资金流列到test表")
            return True
            
        except Exception as e:
            print(f"❌ 添加资金流列失败: {e}")
            return False
    
    def calculate_money_flow_for_test(self):
        """为test表计算资金流数据"""
        try:
            print("🧮 开始计算test表的资金流数据...")
            
            # 读取test表数据
            cursor = self.connection.cursor()
            cursor.execute("""
                SELECT date, open, high, low, close, volume 
                FROM test 
                ORDER BY date
            """)
            
            data = cursor.fetchall()
            if not data:
                print("❌ test表中没有数据")
                return False
            
            # 转换为DataFrame
            df = pd.DataFrame(data, columns=['date', 'open', 'high', 'low', 'close', 'volume'])
            df['date'] = pd.to_datetime(df['date'])
            
            print(f"📊 处理 {len(df)} 条记录...")
            
            # 计算资金流 - 使用增强算法
            self.calculate_enhanced_money_flow(df)
            
            # 更新数据库
            self.update_test_table_money_flow(df)
            
            return True
            
        except Exception as e:
            print(f"❌ 计算资金流失败: {e}")
            return False
    
    def calculate_enhanced_money_flow(self, df):
        """计算增强的资金流数据"""
        print("🎯 使用增强算法计算资金流...")
        
        # 方法1: 基于价量关系的资金流
        price_change = (df['close'] - df['open']) / df['open']
        price_change = price_change.fillna(0)
        
        flow_in_1 = np.where(price_change > 0, df['volume'] * price_change, 0)
        flow_out_1 = np.where(price_change < 0, df['volume'] * abs(price_change), 0)
        
        # 方法2: 基于典型价格的资金流
        typical_price = (df['high'] + df['low'] + df['close']) / 3
        raw_money_flow = typical_price * df['volume']
        
        price_up = typical_price > typical_price.shift(1)
        price_up = price_up.fillna(False)
        
        flow_in_2 = np.where(price_up, raw_money_flow, 0)
        flow_out_2 = np.where(~price_up, raw_money_flow, 0)
        
        # 方法3: 基于收盘价位置的资金流
        high_low_diff = df['high'] - df['low']
        close_location_value = np.where(
            high_low_diff > 0,
            ((df['close'] - df['low']) - (df['high'] - df['close'])) / high_low_diff,
            0
        )
        
        money_flow_multiplier = close_location_value * df['volume']
        flow_in_3 = np.where(money_flow_multiplier > 0, money_flow_multiplier, 0)
        flow_out_3 = np.where(money_flow_multiplier < 0, abs(money_flow_multiplier), 0)
        
        # 方法4: 基于RSI调整的资金流
        delta = df['close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=14, min_periods=1).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=14, min_periods=1).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        rsi = rsi.fillna(50)
        
        rsi_factor = (rsi - 50) / 50
        flow_in_4 = np.where(rsi_factor > 0, df['volume'] * rsi_factor, 0)
        flow_out_4 = np.where(rsi_factor < 0, df['volume'] * abs(rsi_factor), 0)
        
        # 综合计算 (加权平均)
        flow_in = (flow_in_1 * 0.3 + flow_in_2 * 0.3 + flow_in_3 * 0.2 + flow_in_4 * 0.2)
        flow_out = (flow_out_1 * 0.3 + flow_out_2 * 0.3 + flow_out_3 * 0.2 + flow_out_4 * 0.2)
        
        # 计算其他指标
        net_flow = flow_in - flow_out
        total_flow = flow_in + flow_out
        flow_ratio = np.where(total_flow > 0, flow_in / total_flow, 0.5)
        
        # 计算资金流强度
        volume_ma5 = df['volume'].rolling(window=5, min_periods=1).mean()
        volume_ma20 = df['volume'].rolling(window=20, min_periods=1).mean()
        volume_ma60 = df['volume'].rolling(window=60, min_periods=1).mean()
        
        volume_baseline = (volume_ma5 * 0.5 + volume_ma20 * 0.3 + volume_ma60 * 0.2)
        flow_intensity = total_flow / volume_baseline
        flow_intensity = flow_intensity.fillna(1.0)
        
        # 计算累积资金流
        cumulative_flow = net_flow.cumsum()
        
        # 添加到DataFrame
        df['money_flow_in'] = flow_in
        df['money_flow_out'] = flow_out
        df['net_money_flow'] = net_flow
        df['money_flow_ratio'] = flow_ratio
        df['money_flow_intensity'] = flow_intensity
        df['cumulative_money_flow'] = cumulative_flow
        
        # 处理无穷大和NaN值
        money_flow_columns = ['money_flow_in', 'money_flow_out', 'net_money_flow', 
                             'money_flow_ratio', 'money_flow_intensity', 'cumulative_money_flow']
        
        for col in money_flow_columns:
            df[col] = df[col].replace([np.inf, -np.inf], 0)
            df[col] = df[col].fillna(0)
        
        print("✅ 资金流计算完成")
        
        # 显示统计信息
        print(f"📊 资金流统计:")
        print(f"   • 平均日流入: {df['money_flow_in'].mean():,.0f}")
        print(f"   • 平均日流出: {df['money_flow_out'].mean():,.0f}")
        print(f"   • 平均流入比例: {df['money_flow_ratio'].mean():.3f}")
        print(f"   • 累积资金流范围: {df['cumulative_money_flow'].min():,.0f} 至 {df['cumulative_money_flow'].max():,.0f}")
    
    def update_test_table_money_flow(self, df):
        """更新test表的资金流数据"""
        try:
            print("💾 更新test表资金流数据...")
            
            cursor = self.connection.cursor()
            
            # 准备批量更新语句
            update_sql = """
                UPDATE test 
                SET money_flow_in = %s,
                    money_flow_out = %s,
                    net_money_flow = %s,
                    money_flow_ratio = %s,
                    money_flow_intensity = %s,
                    cumulative_money_flow = %s
                WHERE date = %s
            """
            
            # 准备数据
            update_data = []
            for _, row in df.iterrows():
                update_data.append((
                    float(row['money_flow_in']),
                    float(row['money_flow_out']),
                    float(row['net_money_flow']),
                    float(row['money_flow_ratio']),
                    float(row['money_flow_intensity']),
                    float(row['cumulative_money_flow']),
                    row['date'].strftime('%Y-%m-%d')
                ))
            
            # 批量更新
            cursor.executemany(update_sql, update_data)
            self.connection.commit()
            
            print(f"✅ 成功更新 {len(update_data)} 条记录的资金流数据")
            return True
            
        except Exception as e:
            print(f"❌ 更新test表失败: {e}")
            return False
    
    def verify_test_table_update(self):
        """验证test表更新结果"""
        try:
            cursor = self.connection.cursor()
            
            # 检查更新后的数据
            cursor.execute("""
                SELECT date, close, volume, money_flow_in, money_flow_out, 
                       money_flow_ratio, money_flow_intensity, cumulative_money_flow
                FROM test 
                ORDER BY date DESC 
                LIMIT 10
            """)
            
            latest_data = cursor.fetchall()
            
            print("\n📊 test表最新10条记录的资金流数据:")
            print("="*140)
            print(f"{'日期':<12} {'收盘价':<8} {'成交量':<10} {'流入':<12} {'流出':<12} {'比例':<8} {'强度':<8} {'累积':<12}")
            print("-" * 140)
            
            for row in latest_data:
                date, close, volume, flow_in, flow_out, ratio, intensity, cumulative = row
                print(f"{date} {float(close):>7.2f} {int(volume):>9,} {float(flow_in):>11,.0f} {float(flow_out):>11,.0f} "
                      f"{float(ratio):>7.3f} {float(intensity):>7.2f} {float(cumulative):>11,.0f}")
            
            # 统计信息
            cursor.execute("""
                SELECT 
                    COUNT(*) as total_records,
                    COUNT(CASE WHEN money_flow_in > 0 OR money_flow_out > 0 THEN 1 END) as flow_records,
                    AVG(money_flow_in) as avg_in,
                    AVG(money_flow_out) as avg_out,
                    AVG(money_flow_ratio) as avg_ratio
                FROM test
            """)
            
            stats = cursor.fetchone()
            total, flow_records, avg_in, avg_out, avg_ratio = stats
            
            print(f"\n📈 test表资金流数据统计:")
            print(f"   • 总记录数: {total:,}")
            print(f"   • 有资金流数据的记录: {flow_records:,}")
            print(f"   • 数据完整率: {flow_records/total*100:.1f}%")
            print(f"   • 平均日流入: {float(avg_in):,.0f}")
            print(f"   • 平均日流出: {float(avg_out):,.0f}")
            print(f"   • 平均流入比例: {float(avg_ratio):.3f}")
            
            return True
            
        except Exception as e:
            print(f"❌ 验证test表更新失败: {e}")
            return False
    
    def close_connection(self):
        """关闭数据库连接"""
        if self.connection:
            self.connection.close()
            print("\n🔒 数据库连接已关闭")

def main():
    """主函数"""
    print("🏦 为test表补全真实资金流数据")
    print("="*60)
    
    # 创建更新器
    updater = TestTableMoneyFlowUpdater()
    
    # 连接数据库
    if not updater.connect_database():
        return
    
    # 检查test表
    if not updater.check_test_table():
        updater.close_connection()
        return
    
    # 添加资金流列
    if not updater.add_money_flow_columns():
        updater.close_connection()
        return
    
    # 计算并更新资金流数据
    if not updater.calculate_money_flow_for_test():
        updater.close_connection()
        return
    
    # 验证更新结果
    updater.verify_test_table_update()
    
    # 关闭连接
    updater.close_connection()
    
    print("\n🎉 test表真实资金流数据补全完成!")
    print("📊 新增列:")
    print("   • money_flow_in: 资金流入")
    print("   • money_flow_out: 资金流出")
    print("   • net_money_flow: 净资金流")
    print("   • money_flow_ratio: 资金流入比例")
    print("   • money_flow_intensity: 资金流强度")
    print("   • cumulative_money_flow: 累积资金流")

if __name__ == "__main__":
    main()
