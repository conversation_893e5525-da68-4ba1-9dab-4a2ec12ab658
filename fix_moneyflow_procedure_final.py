#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
最终修复MoneyFlowRatio存储过程错误
================================

根据实际表结构修复存储过程错误:
- eab_0023hk表包含MoneyFlowRatio列
- eab_0023hk_moneyflow表不存在
- 修复存储过程参数问题

作者: Cosmoon NG
日期: 2025年7月24日
"""

import mysql.connector
from datetime import datetime

class FinalMoneyFlowProcedureFixer:
    def __init__(self):
        """初始化修复器"""
        self.db_config = {
            'host': 'localhost',
            'user': 'root',
            'password': '12345678',
            'database': 'finance',
            'charset': 'utf8mb4',
            'autocommit': True
        }
        self.conn = None
        self.cursor = None
    
    def connect_database(self):
        """连接数据库"""
        try:
            print("🔗 连接MySQL数据库...")
            self.conn = mysql.connector.connect(**self.db_config)
            self.cursor = self.conn.cursor()
            print("✅ 数据库连接成功")
            return True
        except mysql.connector.Error as e:
            print(f"❌ 数据库连接失败: {e}")
            return False
    
    def check_current_procedures(self):
        """检查当前存储过程"""
        print("\n📋 检查当前存储过程...")
        
        try:
            # 查看存储过程状态
            self.cursor.execute("SHOW PROCEDURE STATUS WHERE Db = 'finance'")
            procedures = self.cursor.fetchall()
            
            print(f"   找到 {len(procedures)} 个存储过程:")
            for proc in procedures:
                proc_name = proc[1]  # 存储过程名称
                print(f"     - {proc_name}")
                
                # 检查存储过程定义
                try:
                    self.cursor.execute(f"SHOW CREATE PROCEDURE {proc_name}")
                    result = self.cursor.fetchone()
                    if result and len(result) > 2:
                        proc_definition = result[2]
                        if 'MoneyFlowRatio' in proc_definition:
                            print(f"       ⚠️ 包含MoneyFlowRatio引用")
                            
                            # 检查是否引用了不存在的表
                            if 'eab_0023hk_moneyflow' in proc_definition:
                                print(f"       ❌ 引用了不存在的表: eab_0023hk_moneyflow")
                                return proc_name, proc_definition
                            
                except mysql.connector.Error as e:
                    print(f"       ❌ 无法查看存储过程定义: {e}")
            
            return None, None
            
        except mysql.connector.Error as e:
            print(f"❌ 检查存储过程失败: {e}")
            return None, None
    
    def fix_procedure(self, proc_name, proc_definition):
        """修复存储过程"""
        print(f"\n🔧 修复存储过程: {proc_name}")
        
        try:
            # 备份原存储过程
            backup_name = f"{proc_name}_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            print(f"   📦 创建备份: {backup_name}")
            
            # 创建备份（重命名原存储过程的定义）
            backup_sql = proc_definition.replace(f"PROCEDURE `{proc_name}`", f"PROCEDURE `{backup_name}`")
            
            # 删除原存储过程
            print(f"   🗑️ 删除原存储过程...")
            self.cursor.execute(f"DROP PROCEDURE IF EXISTS {proc_name}")
            
            # 创建修复后的存储过程
            print(f"   🔨 创建修复后的存储过程...")
            
            # 根据实际情况修复存储过程
            if proc_name == 'sp_combined_stock_analysis':
                # 创建一个不需要参数的版本
                fixed_sql = """
                CREATE PROCEDURE sp_combined_stock_analysis()
                BEGIN
                    -- 修复后的存储过程：使用实际存在的表和列
                    SELECT 
                        h.Date,
                        h.Close as HSI_Close,
                        h.MoneyFlowRatio as HSI_MoneyFlowRatio,
                        h.Full_Y as HSI_Full_Y,
                        h.E as HSI_E_Value,
                        e.Close as EAB_Close,
                        e.MoneyFlowRatio as EAB_MoneyFlowRatio,
                        e.MFI as EAB_MFI,
                        e.Y_Value as EAB_Y_Value,
                        e.X_Value as EAB_X_Value,
                        e.E_Value as EAB_E_Value,
                        e.TradingSignal as EAB_Signal
                    FROM hkhsi50 h
                    LEFT JOIN eab_0023hk e ON h.Date = e.Date
                    WHERE h.Date >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
                    ORDER BY h.Date DESC
                    LIMIT 100;
                END
                """
            else:
                # 对于其他存储过程，简单替换表名
                fixed_sql = proc_definition.replace('eab_0023hk_moneyflow', 'eab_0023hk')
            
            self.cursor.execute(fixed_sql)
            print(f"   ✅ 存储过程修复成功")
            
            return True
            
        except mysql.connector.Error as e:
            print(f"   ❌ 修复存储过程失败: {e}")
            self.conn.rollback()
            return False
    
    def create_safe_procedures(self):
        """创建安全的MoneyFlow查询存储过程"""
        print("\n🛡️ 创建安全的MoneyFlow查询存储过程...")
        
        safe_procedures = [
            {
                'name': 'sp_get_eab_data',
                'sql': """
                CREATE PROCEDURE sp_get_eab_data(
                    IN start_date DATE,
                    IN end_date DATE
                )
                BEGIN
                    SELECT 
                        Date,
                        Close,
                        Volume,
                        MoneyFlowRatio,
                        MFI,
                        Y_Value,
                        X_Value,
                        E_Value,
                        TradingSignal
                    FROM eab_0023hk
                    WHERE Date BETWEEN start_date AND end_date
                    ORDER BY Date DESC;
                END
                """
            },
            {
                'name': 'sp_analyze_eab_signals',
                'sql': """
                CREATE PROCEDURE sp_analyze_eab_signals(
                    IN days_back INT
                )
                BEGIN
                    SELECT 
                        TradingSignal,
                        COUNT(*) as signal_count,
                        AVG(MoneyFlowRatio) as avg_money_flow_ratio,
                        AVG(MFI) as avg_mfi,
                        AVG(Close) as avg_price
                    FROM eab_0023hk
                    WHERE Date >= DATE_SUB(CURDATE(), INTERVAL days_back DAY)
                    GROUP BY TradingSignal
                    ORDER BY TradingSignal;
                END
                """
            },
            {
                'name': 'sp_get_latest_market_data',
                'sql': """
                CREATE PROCEDURE sp_get_latest_market_data()
                BEGIN
                    SELECT 
                        'EAB' as symbol,
                        Date,
                        Close,
                        MoneyFlowRatio,
                        MFI,
                        Y_Value,
                        X_Value,
                        E_Value,
                        TradingSignal,
                        CASE 
                            WHEN TradingSignal = 1 THEN '做多'
                            WHEN TradingSignal = -1 THEN '做空'
                            ELSE '观望'
                        END as signal_text
                    FROM eab_0023hk
                    ORDER BY Date DESC
                    LIMIT 1
                    
                    UNION ALL
                    
                    SELECT 
                        'HSI' as symbol,
                        Date,
                        Close,
                        MoneyFlowRatio,
                        NULL as MFI,
                        Full_Y as Y_Value,
                        NULL as X_Value,
                        E as E_Value,
                        NULL as TradingSignal,
                        '无信号' as signal_text
                    FROM hkhsi50
                    ORDER BY Date DESC
                    LIMIT 1;
                END
                """
            }
        ]
        
        for proc in safe_procedures:
            try:
                # 删除已存在的存储过程
                self.cursor.execute(f"DROP PROCEDURE IF EXISTS {proc['name']}")
                
                # 创建新存储过程
                self.cursor.execute(proc['sql'])
                print(f"   ✅ 创建存储过程: {proc['name']}")
                
            except mysql.connector.Error as e:
                print(f"   ❌ 创建存储过程 {proc['name']} 失败: {e}")
    
    def test_procedures(self):
        """测试存储过程"""
        print("\n🧪 测试存储过程...")
        
        test_cases = [
            {
                'name': 'sp_combined_stock_analysis',
                'call': 'CALL sp_combined_stock_analysis()',
                'description': '综合股票分析'
            },
            {
                'name': 'sp_get_latest_market_data',
                'call': 'CALL sp_get_latest_market_data()',
                'description': '获取最新市场数据'
            },
            {
                'name': 'sp_analyze_eab_signals',
                'call': 'CALL sp_analyze_eab_signals(30)',
                'description': '分析EAB信号(30天)'
            }
        ]
        
        for test in test_cases:
            try:
                print(f"   🔍 测试: {test['description']}")
                self.cursor.execute(test['call'])
                
                # 获取结果
                results = self.cursor.fetchall()
                print(f"     ✅ 成功，返回 {len(results)} 条记录")
                
                # 显示前几条结果
                if results and len(results) > 0:
                    print(f"     📊 示例数据: {results[0][:3]}...")
                
            except mysql.connector.Error as e:
                print(f"     ❌ 测试失败: {e}")
    
    def close_connection(self):
        """关闭数据库连接"""
        if self.cursor:
            self.cursor.close()
        if self.conn:
            self.conn.close()
        print("🔗 数据库连接已关闭")

def main():
    """主函数"""
    print("🔧 最终修复MoneyFlowRatio存储过程错误")
    print("=" * 60)
    
    fixer = FinalMoneyFlowProcedureFixer()
    
    try:
        # 连接数据库
        if not fixer.connect_database():
            return
        
        # 检查当前存储过程
        proc_name, proc_definition = fixer.check_current_procedures()
        
        # 修复问题存储过程
        if proc_name and proc_definition:
            if fixer.fix_procedure(proc_name, proc_definition):
                print(f"✅ 存储过程 {proc_name} 修复成功")
            else:
                print(f"❌ 存储过程 {proc_name} 修复失败")
        else:
            print("✅ 没有发现需要修复的存储过程")
        
        # 创建安全的存储过程
        fixer.create_safe_procedures()
        
        # 测试存储过程
        fixer.test_procedures()
        
        print(f"\n🎉 MoneyFlowRatio错误修复完成!")
        print(f"💡 现在可以安全使用所有存储过程")
        
    finally:
        fixer.close_connection()

if __name__ == "__main__":
    main()
