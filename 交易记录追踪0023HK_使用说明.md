# 交易记录追踪0023HK - 使用说明

## 📋 系统概述

**交易记录追踪0023HK** 是专门为东亚银行(0023.HK)设计的智能交易记录追踪系统，基于Cosmoon XYE技术指标，提供完整的交易记录、分析和报告功能。

---

## 🚀 快速开始

### 1. 运行系统
```bash
python 交易记录追踪0023HK.py
```

### 2. 系统自动执行
- ✅ 加载0023HK历史数据（2年）
- ✅ 计算XYE技术指标
- ✅ 执行交易策略
- ✅ 生成Excel交易记录
- ✅ 创建分析图表
- ✅ 输出完整报告

---

## 📊 系统功能

### 🎯 核心功能
1. **实时数据获取**: 从Yahoo Finance获取0023HK最新数据
2. **XYE技术分析**: 使用专有XYE指标系统
3. **智能交易策略**: 自动执行买卖决策
4. **风险控制**: 严格的止盈止损机制
5. **完整记录**: 25个字段的详细交易记录
6. **可视化分析**: 自动生成分析图表

### 📈 交易策略参数
- **多头止盈**: 2.5%
- **多头止损**: 1.5%
- **空头止盈**: 1.5%
- **空头止损**: 2.5%
- **手续费**: 0.1%
- **开仓资金**: 70%可用资金

### 💰 资金管理
- **初始资金**: 10,000港元
- **每月追加**: 3,000港元
- **追加时间**: 每月1日自动注入

---

## 📁 输出文件

### 1. Excel交易记录
**文件名**: `交易记录追踪0023HK_YYYYMMDD_HHMMSS.xlsx`

**包含字段**:
- 交易序号、交易日期、交易类型
- 交易方向、交易价格、持仓数量
- 交易金额、手续费、净交易额
- 持仓成本、当前市值、浮动盈亏
- 实现盈亏、累计盈亏、账户余额
- 总资产、收益率、累计收益率
- Y值、X值、E值、信号强度
- 风险等级、平仓原因、备注

### 2. 分析图表
**文件名**: `0023HK交易分析图表_YYYYMMDD_HHMMSS.png`

**包含图表**:
- 资产增长曲线
- 累计收益率变化
- 交易盈亏分布
- XYE信号分布

---

## 🔧 系统配置

### 修改交易参数
```python
# 在TradingTracker0023HK类的__init__方法中修改
self.take_profit_long = 0.025   # 多头止盈
self.stop_loss_long = 0.015     # 多头止损
self.take_profit_short = 0.015  # 空头止盈
self.stop_loss_short = 0.025    # 空头止损
```

### 修改资金设置
```python
self.initial_capital = 10000    # 初始资金
self.monthly_addition = 3000    # 每月追加
```

### 修改数据周期
```python
# 在main函数中修改
tracker.load_market_data(period="2y")  # 2年数据
# 可选: "1y", "6m", "3m"
```

---

## 📊 XYE技术指标说明

### Y值 (资金流向指标)
- **计算方式**: 基于14日资金流向比率
- **取值范围**: 0-100
- **信号含义**: 
  - Y > 60: 强烈买入信号
  - Y < 40: 强烈卖出信号

### X值 (市场情绪指标)
- **计算方式**: RSI结合波动率
- **取值范围**: 0-1
- **信号含义**:
  - X > 0.5: 市场情绪积极
  - X < 0.5: 市场情绪消极

### E值 (控股商控制指标)
- **计算方式**: 价格-成交量趋势
- **信号含义**:
  - E > 0: 控股商买入
  - E < 0: 控股商卖出

---

## 📈 交易信号逻辑

### 强烈买入
- Y > 0.6 AND X > 0.5 AND E > 0

### 强烈卖出
- Y < 0.4 AND X < 0.5 AND E < 0

### 买入
- Y > 0.5 AND X > 0.4

### 卖出
- Y < 0.5 AND X < 0.6

### 观望
- 其他情况

---

## 🎯 最新运行结果

### 📊 交易统计 (2025-07-28)
- **总交易次数**: 86次
- **胜率**: 44.71%
- **盈亏比**: 1.28
- **累计收益率**: 9.71%

### 💰 资产表现
- **初始资金**: 10,000港元
- **总投入**: 34,000港元
- **最终资产**: 37,301.32港元
- **最高收益率**: 89.01%

### 🎯 信号分布
- **强烈买入**: 84次
- **买入**: 74次
- **观望**: 21次

---

## ⚠️ 注意事项

### 1. 数据依赖
- 需要稳定的网络连接获取Yahoo Finance数据
- 建议在交易时间外运行以获得完整数据

### 2. 风险提示
- 本系统仅供学习和研究使用
- 实际投资请谨慎考虑风险
- 历史表现不代表未来收益

### 3. 系统要求
- Python 3.7+
- 必需库: yfinance, pandas, numpy, matplotlib, scipy

---

## 🔄 系统更新

### 版本历史
- **v2.0** (2025-07-28): 完整的交易记录追踪系统
- **v1.0** (2025-07-22): 基础版本

### 计划功能
- [ ] 实时监控模式
- [ ] 多标的支持
- [ ] 策略回测优化
- [ ] 风险预警系统

---

## 📞 技术支持

**开发者**: Cosmoon NG  
**技术架构**: Python + yfinance + XYE指标  
**更新频率**: 根据市场需求持续优化  

---

*本系统严格按照Excel表格逻辑执行所有交易记录，确保数据的准确性和完整性。*
