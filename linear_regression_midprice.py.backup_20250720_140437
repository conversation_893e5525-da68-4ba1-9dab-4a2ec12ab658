#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
为stock_600887_ss表生成线性回归中值公式 y = mx + b
=====================================================
使用多种线性回归方法计算中值价格
"""

import mysql.connector
import pandas as pd
import numpy as np
from datetime import datetime
from sklearn.linear_model import LinearRegression
from scipy import stats
import warnings
warnings.filterwarnings('ignore')

class LinearRegressionMidPrice:
    def __init__(self):
        """初始化数据库连接"""
        self.db_config = {
            'host': '************',
            'database': 'finance',
            'user': 'root',
            'password': '',
            'charset': 'utf8mb4',
            'autocommit': True
        }
        self.connection = None
        self.cursor = None
        self.table_name = 'stock_600887_ss'
        
    def connect_database(self):
        """连接数据库"""
        try:
            print("🔗 连接MySQL数据库...")
            self.connection = mysql.connector.connect(**self.db_config)
            self.cursor = self.connection.cursor()
            print(f"✅ 成功连接到数据库: {self.db_config['host']}/{self.db_config['database']}")
            return True
        except mysql.connector.Error as e:
            print(f"❌ 数据库连接失败: {e}")
            return False
    
    def load_stock_data(self):
        """加载股票数据"""
        try:
            print(f"📊 加载{self.table_name}数据...")
            
            # 获取股票数据
            query = f"""
                SELECT date, open, high, low, close, volume
                FROM {self.table_name}
                ORDER BY date ASC
            """
            
            self.cursor.execute(query)
            data = self.cursor.fetchall()
            
            if not data:
                print("❌ 没有数据")
                return None
            
            # 转换为DataFrame
            df = pd.DataFrame(data, columns=['date', 'open', 'high', 'low', 'close', 'volume'])
            
            # 转换数据类型
            df['date'] = pd.to_datetime(df['date'])
            price_columns = ['open', 'high', 'low', 'close']
            for col in price_columns:
                df[col] = pd.to_numeric(df[col], errors='coerce')
            df['volume'] = pd.to_numeric(df['volume'], errors='coerce')
            
            # 添加序号作为x轴
            df['x'] = range(len(df))
            
            # 计算传统中值价格
            df['traditional_midprice'] = (df['high'] + df['low']) / 2
            
            print(f"✅ 成功加载{len(df)}条数据")
            print(f"📅 数据时间范围: {df['date'].min()} 至 {df['date'].max()}")
            
            return df
            
        except Exception as e:
            print(f"❌ 加载数据失败: {e}")
            return None
    
    def method1_simple_linear_regression(self, df):
        """方法1: 简单线性回归 - 基于收盘价"""
        print("\n📈 方法1: 简单线性回归 (基于收盘价)")
        
        # 使用收盘价进行线性回归
        x = df['x'].values.reshape(-1, 1)
        y = df['close'].values
        
        # 使用sklearn进行线性回归
        model = LinearRegression()
        model.fit(x, y)
        
        m = model.coef_[0]
        b = model.intercept_
        r_squared = model.score(x, y)
        
        # 计算预测值
        df['lr_midprice_method1'] = model.predict(x)
        
        print(f"   📊 回归方程: y = {m:.6f}x + {b:.6f}")
        print(f"   📊 R² = {r_squared:.6f}")
        print(f"   📊 斜率解释: 每天价格变化 {m:.6f} 元")
        
        return m, b, r_squared, 'lr_midprice_method1'
    
    def method2_weighted_price_regression(self, df):
        """方法2: 加权价格回归 - 基于成交量加权平均价格"""
        print("\n📈 方法2: 成交量加权价格回归")
        
        # 计算成交量加权平均价格 VWAP
        df['vwap'] = (df['high'] + df['low'] + df['close']) / 3
        
        x = df['x'].values.reshape(-1, 1)
        y = df['vwap'].values
        
        # 使用成交量作为权重
        weights = df['volume'].values
        weights = weights / weights.max()  # 标准化权重
        
        # 加权线性回归
        model = LinearRegression()
        model.fit(x, y, sample_weight=weights)
        
        m = model.coef_[0]
        b = model.intercept_
        r_squared = model.score(x, y, sample_weight=weights)
        
        # 计算预测值
        df['lr_midprice_method2'] = model.predict(x)
        
        print(f"   📊 回归方程: y = {m:.6f}x + {b:.6f}")
        print(f"   📊 加权R² = {r_squared:.6f}")
        print(f"   📊 斜率解释: 每天VWAP变化 {m:.6f} 元")
        
        return m, b, r_squared, 'lr_midprice_method2'
    
    def method3_robust_regression(self, df):
        """方法3: 稳健回归 - 基于中位数回归"""
        print("\n📈 方法3: 稳健回归 (基于中位数)")
        
        # 使用传统中值价格
        x = df['x'].values
        y = df['traditional_midprice'].values
        
        # 使用scipy进行线性回归
        slope, intercept, r_value, p_value, std_err = stats.linregress(x, y)
        
        m = slope
        b = intercept
        r_squared = r_value ** 2
        
        # 计算预测值
        df['lr_midprice_method3'] = m * df['x'] + b
        
        print(f"   📊 回归方程: y = {m:.6f}x + {b:.6f}")
        print(f"   📊 R² = {r_squared:.6f}")
        print(f"   📊 p值 = {p_value:.6e}")
        print(f"   📊 标准误差 = {std_err:.6e}")
        print(f"   📊 斜率解释: 每天中值价格变化 {m:.6f} 元")
        
        return m, b, r_squared, 'lr_midprice_method3'
    
    def method4_moving_regression(self, df, window=60):
        """方法4: 滚动回归 - 基于滚动窗口"""
        print(f"\n📈 方法4: 滚动回归 (窗口={window}天)")
        
        # 初始化结果列
        df['lr_midprice_method4'] = np.nan
        slopes = []
        intercepts = []
        
        for i in range(window, len(df)):
            # 获取窗口数据
            window_data = df.iloc[i-window:i]
            x_window = window_data['x'].values
            y_window = window_data['close'].values
            
            # 线性回归
            slope, intercept, r_value, p_value, std_err = stats.linregress(x_window, y_window)
            
            # 预测当前点
            predicted_value = slope * df.iloc[i]['x'] + intercept
            df.loc[df.index[i], 'lr_midprice_method4'] = predicted_value
            
            slopes.append(slope)
            intercepts.append(intercept)
        
        # 计算平均参数
        avg_slope = np.mean(slopes)
        avg_intercept = np.mean(intercepts)
        
        # 计算整体R²
        valid_data = df.dropna(subset=['lr_midprice_method4'])
        if len(valid_data) > 0:
            correlation = np.corrcoef(valid_data['close'], valid_data['lr_midprice_method4'])[0, 1]
            r_squared = correlation ** 2
        else:
            r_squared = 0
        
        print(f"   📊 平均回归方程: y = {avg_slope:.6f}x + {avg_intercept:.6f}")
        print(f"   📊 整体R² = {r_squared:.6f}")
        print(f"   📊 斜率范围: {min(slopes):.6f} ~ {max(slopes):.6f}")
        print(f"   📊 平均斜率解释: 每天价格变化 {avg_slope:.6f} 元")
        
        return avg_slope, avg_intercept, r_squared, 'lr_midprice_method4'
    
    def method5_polynomial_regression(self, df, degree=2):
        """方法5: 多项式回归 - 捕捉非线性趋势"""
        print(f"\n📈 方法5: {degree}次多项式回归")
        
        x = df['x'].values
        y = df['close'].values
        
        # 多项式拟合
        coefficients = np.polyfit(x, y, degree)
        polynomial = np.poly1d(coefficients)
        
        # 计算预测值
        df['lr_midprice_method5'] = polynomial(x)
        
        # 计算R²
        y_pred = polynomial(x)
        ss_res = np.sum((y - y_pred) ** 2)
        ss_tot = np.sum((y - np.mean(y)) ** 2)
        r_squared = 1 - (ss_res / ss_tot)
        
        # 对于线性部分，提取一次项系数作为斜率
        if degree >= 1:
            m = coefficients[-2]  # 一次项系数
            b = coefficients[-1]  # 常数项
        else:
            m = 0
            b = coefficients[-1]
        
        print(f"   📊 多项式方程: {polynomial}")
        print(f"   📊 线性部分: y = {m:.6f}x + {b:.6f}")
        print(f"   📊 R² = {r_squared:.6f}")
        print(f"   📊 斜率解释: 线性趋势每天变化 {m:.6f} 元")
        
        return m, b, r_squared, 'lr_midprice_method5'
    
    def compare_methods(self, df, results):
        """比较不同方法的效果"""
        print("\n📊 方法比较分析:")
        print("=" * 80)
        
        comparison_data = []
        
        for i, (m, b, r_squared, column_name) in enumerate(results, 1):
            # 计算与实际收盘价的相关性
            valid_data = df.dropna(subset=[column_name])
            if len(valid_data) > 0:
                correlation_with_close = np.corrcoef(valid_data['close'], valid_data[column_name])[0, 1]
                
                # 计算平均绝对误差
                mae = np.mean(np.abs(valid_data['close'] - valid_data[column_name]))
                
                # 计算均方根误差
                rmse = np.sqrt(np.mean((valid_data['close'] - valid_data[column_name]) ** 2))
                
                comparison_data.append({
                    '方法': f'方法{i}',
                    '斜率(m)': f'{m:.6f}',
                    '截距(b)': f'{b:.2f}',
                    'R²': f'{r_squared:.6f}',
                    '与收盘价相关性': f'{correlation_with_close:.6f}',
                    '平均绝对误差': f'{mae:.4f}',
                    '均方根误差': f'{rmse:.4f}'
                })
        
        # 显示比较表格
        comparison_df = pd.DataFrame(comparison_data)
        print(comparison_df.to_string(index=False))
        
        # 推荐最佳方法
        best_r2_idx = max(range(len(results)), key=lambda i: results[i][2])
        best_method = results[best_r2_idx]
        
        print(f"\n🏆 推荐方法: 方法{best_r2_idx + 1}")
        print(f"   📊 最佳回归方程: y = {best_method[0]:.6f}x + {best_method[1]:.6f}")
        print(f"   📊 R² = {best_method[2]:.6f}")
        
        return best_method
    
    def add_best_method_to_database(self, df, best_method):
        """将最佳方法的结果添加到数据库"""
        try:
            print(f"\n💾 将最佳方法结果添加到数据库...")
            
            m, b, r_squared, column_name = best_method
            
            # 检查是否已有lr_midprice列
            self.cursor.execute(f"SHOW COLUMNS FROM {self.table_name} LIKE 'lr_midprice'")
            if not self.cursor.fetchone():
                # 添加lr_midprice列
                alter_sql = f"""
                    ALTER TABLE {self.table_name} 
                    ADD COLUMN lr_midprice DECIMAL(20,6) DEFAULT NULL 
                    COMMENT '线性回归中值价格 y=mx+b'
                """
                self.cursor.execute(alter_sql)
                print("✅ 已添加lr_midprice列")
            
            # 更新数据
            update_count = 0
            for index, row in df.iterrows():
                if pd.notna(row[column_name]):
                    update_sql = f"""
                        UPDATE {self.table_name} 
                        SET lr_midprice = %s 
                        WHERE date = %s
                    """
                    self.cursor.execute(update_sql, (float(row[column_name]), row['date']))
                    update_count += 1
            
            print(f"✅ 成功更新{update_count}条记录")
            
            # 保存回归参数到数据库（创建参数表）
            self.save_regression_parameters(m, b, r_squared)
            
            return True
            
        except Exception as e:
            print(f"❌ 添加到数据库失败: {e}")
            return False
    
    def save_regression_parameters(self, m, b, r_squared):
        """保存回归参数到数据库"""
        try:
            # 创建参数表（如果不存在）
            create_table_sql = """
                CREATE TABLE IF NOT EXISTS regression_parameters (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    table_name VARCHAR(100) NOT NULL,
                    method_name VARCHAR(100) NOT NULL,
                    slope_m DECIMAL(20,10) NOT NULL,
                    intercept_b DECIMAL(20,6) NOT NULL,
                    r_squared DECIMAL(10,8) NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    formula VARCHAR(200) NOT NULL,
                    UNIQUE KEY unique_table_method (table_name, method_name)
                )
            """
            self.cursor.execute(create_table_sql)
            
            # 插入或更新参数
            formula = f"y = {m:.6f}x + {b:.6f}"
            insert_sql = """
                INSERT INTO regression_parameters 
                (table_name, method_name, slope_m, intercept_b, r_squared, formula)
                VALUES (%s, %s, %s, %s, %s, %s)
                ON DUPLICATE KEY UPDATE
                slope_m = VALUES(slope_m),
                intercept_b = VALUES(intercept_b),
                r_squared = VALUES(r_squared),
                formula = VALUES(formula),
                created_at = CURRENT_TIMESTAMP
            """
            
            self.cursor.execute(insert_sql, (
                self.table_name, 
                'linear_regression_best', 
                m, b, r_squared, formula
            ))
            
            print(f"✅ 回归参数已保存: {formula}")
            
        except Exception as e:
            print(f"❌ 保存参数失败: {e}")
    
    def verify_results(self):
        """验证结果"""
        try:
            print("\n🔍 验证结果...")
            
            # 统计lr_midprice数据
            self.cursor.execute(f"""
                SELECT 
                    COUNT(*) as total_rows,
                    COUNT(lr_midprice) as lr_midprice_rows,
                    MIN(lr_midprice) as min_lr_midprice,
                    MAX(lr_midprice) as max_lr_midprice,
                    AVG(lr_midprice) as avg_lr_midprice
                FROM {self.table_name}
            """)
            
            stats = self.cursor.fetchone()
            print(f"📊 统计结果:")
            print(f"   • 总记录数: {stats[0]}")
            print(f"   • lr_midprice记录数: {stats[1]}")
            print(f"   • 覆盖率: {stats[1]/stats[0]*100:.1f}%")
            if stats[2] is not None:
                print(f"   • lr_midprice范围: {float(stats[2]):.4f} ~ {float(stats[3]):.4f}")
                print(f"   • lr_midprice平均值: {float(stats[4]):.4f}")
            
            # 显示最新数据对比
            self.cursor.execute(f"""
                SELECT date, close, midprice, lr_midprice,
                       ROUND((close - lr_midprice) / lr_midprice * 100, 2) as lr_deviation_pct,
                       ROUND((close - midprice) / midprice * 100, 2) as sp_deviation_pct
                FROM {self.table_name} 
                WHERE lr_midprice IS NOT NULL AND midprice IS NOT NULL
                ORDER BY date DESC 
                LIMIT 10
            """)
            
            results = self.cursor.fetchall()
            print(f"\n📊 最新10条数据对比:")
            print("日期          | 收盘价  | sp_midprice | lr_midprice | LR偏差% | SP偏差%")
            print("-" * 75)
            for row in results:
                date_str = str(row[0])
                close_val = float(row[1]) if row[1] is not None else 0.0
                sp_mid = float(row[2]) if row[2] is not None else 0.0
                lr_mid = float(row[3]) if row[3] is not None else 0.0
                lr_dev = float(row[4]) if row[4] is not None else 0.0
                sp_dev = float(row[5]) if row[5] is not None else 0.0
                print(f"{date_str} | {close_val:7.2f} | {sp_mid:11.4f} | {lr_mid:11.4f} | {lr_dev:7.2f}% | {sp_dev:7.2f}%")
            
            return True
            
        except Exception as e:
            print(f"❌ 验证失败: {e}")
            return False
    
    def close_connection(self):
        """关闭数据库连接"""
        if self.cursor:
            self.cursor.close()
        if self.connection:
            self.connection.close()
        print("🔒 数据库连接已关闭")
    
    def run(self):
        """执行主流程"""
        print("🎯 为stock_600887_ss生成线性回归中值公式 y = mx + b")
        print("=" * 80)
        print(f"📅 执行时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"🏠 数据库: {self.db_config['host']}/{self.db_config['database']}")
        print(f"📊 目标表: {self.table_name}")
        
        try:
            # 1. 连接数据库
            if not self.connect_database():
                return False
            
            # 2. 加载数据
            df = self.load_stock_data()
            if df is None:
                return False
            
            # 3. 应用不同的回归方法
            results = []
            
            # 方法1: 简单线性回归
            result1 = self.method1_simple_linear_regression(df)
            results.append(result1)
            
            # 方法2: 加权价格回归
            result2 = self.method2_weighted_price_regression(df)
            results.append(result2)
            
            # 方法3: 稳健回归
            result3 = self.method3_robust_regression(df)
            results.append(result3)
            
            # 方法4: 滚动回归
            result4 = self.method4_moving_regression(df)
            results.append(result4)
            
            # 方法5: 多项式回归
            result5 = self.method5_polynomial_regression(df)
            results.append(result5)
            
            # 4. 比较方法
            best_method = self.compare_methods(df, results)
            
            # 5. 添加最佳方法到数据库
            if not self.add_best_method_to_database(df, best_method):
                return False
            
            # 6. 验证结果
            if not self.verify_results():
                return False
            
            print("\n🎉 线性回归中值公式生成完成!")
            print("💡 现在您有两种中值价格:")
            print("   • midprice: sp_averagelineV3生成的复杂算法")
            print("   • lr_midprice: 线性回归生成的 y = mx + b 公式")
            
            return True
            
        except Exception as e:
            print(f"❌ 执行失败: {e}")
            return False
        
        finally:
            self.close_connection()

def main():
    """主函数"""
    generator = LinearRegressionMidPrice()
    success = generator.run()
    
    if success:
        print("\n✅ 任务完成!")
        print("📝 使用方法:")
        print("   SELECT date, close, midprice, lr_midprice FROM stock_600887_ss ORDER BY date DESC LIMIT 10;")
    else:
        print("\n❌ 任务失败!")

if __name__ == "__main__":
    main()
