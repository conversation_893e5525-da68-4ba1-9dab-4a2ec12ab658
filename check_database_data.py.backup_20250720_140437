#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查数据库数据完整性
==================

检查数据库中的Y和X数据是否齐全，为Cosmoon的博弈论策略做准备

作者: 博弈论投资策略团队
日期: 2025年7月
"""

import pymysql
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

def check_database_data():
    """检查数据库数据完整性"""
    
    db_config = {
        'host': '************',
        'port': 3306,
        'database': 'finance',
        'user': 'root',
        'password': '',
        'charset': 'utf8mb4'
    }
    
    try:
        connection = pymysql.connect(**db_config)
        cursor = connection.cursor()
        
        print("🔍 检查数据库数据完整性")
        print("="*60)
        
        # 1. 检查可用的表
        print("📊 检查可用表:")
        cursor.execute("SHOW TABLES")
        tables = [table[0] for table in cursor.fetchall()]
        print(f"   可用表: {', '.join(tables)}")
        
        # 2. 检查每个重要表的数据
        important_tables = ['hk00023', 'hk2800', 'sp_stock_anilisys']
        
        for table in important_tables:
            if table in tables:
                print(f"\n📈 检查表 {table}:")
                print("-" * 40)
                
                # 检查表结构
                cursor.execute(f"DESCRIBE {table}")
                columns = cursor.fetchall()
                column_names = [col[0] for col in columns]
                print(f"   列名: {', '.join(column_names)}")
                
                # 检查数据量
                cursor.execute(f"SELECT COUNT(*) FROM {table}")
                total_count = cursor.fetchone()[0]
                print(f"   总记录数: {total_count:,}")
                
                # 检查日期范围
                if 'date' in column_names:
                    cursor.execute(f"SELECT MIN(date), MAX(date) FROM {table}")
                    date_range = cursor.fetchone()
                    if date_range[0] and date_range[1]:
                        print(f"   日期范围: {date_range[0]} 至 {date_range[1]}")
                        
                        # 计算年数
                        start_date = pd.to_datetime(date_range[0])
                        end_date = pd.to_datetime(date_range[1])
                        years = (end_date - start_date).days / 365.25
                        print(f"   数据年数: {years:.1f} 年")
                
                # 检查Y和X相关字段
                y_fields = [col for col in column_names if 'y_' in col.lower() or 'probability' in col.lower()]
                x_fields = [col for col in column_names if 'x_' in col.lower() or 'inflow' in col.lower() or 'ratio' in col.lower()]
                
                if y_fields:
                    print(f"   Y相关字段: {', '.join(y_fields)}")
                    for field in y_fields:
                        cursor.execute(f"SELECT COUNT(*) FROM {table} WHERE {field} IS NOT NULL")
                        count = cursor.fetchone()[0]
                        percentage = (count / total_count * 100) if total_count > 0 else 0
                        print(f"     {field}: {count:,} 条 ({percentage:.1f}%)")
                        
                        # 检查数值范围
                        cursor.execute(f"SELECT MIN({field}), MAX({field}), AVG({field}) FROM {table} WHERE {field} IS NOT NULL")
                        stats = cursor.fetchone()
                        if stats[0] is not None:
                            print(f"       范围: {stats[0]:.4f} - {stats[1]:.4f}, 平均: {stats[2]:.4f}")
                
                if x_fields:
                    print(f"   X相关字段: {', '.join(x_fields)}")
                    for field in x_fields:
                        cursor.execute(f"SELECT COUNT(*) FROM {table} WHERE {field} IS NOT NULL")
                        count = cursor.fetchone()[0]
                        percentage = (count / total_count * 100) if total_count > 0 else 0
                        print(f"     {field}: {count:,} 条 ({percentage:.1f}%)")
                        
                        # 检查数值范围
                        cursor.execute(f"SELECT MIN({field}), MAX({field}), AVG({field}) FROM {table} WHERE {field} IS NOT NULL")
                        stats = cursor.fetchone()
                        if stats[0] is not None:
                            print(f"       范围: {stats[0]:.4f} - {stats[1]:.4f}, 平均: {stats[2]:.4f}")
                
                # 检查价格相关字段
                price_fields = [col for col in column_names if col.lower() in ['close', 'price', 'open', 'high', 'low']]
                if price_fields:
                    print(f"   价格字段: {', '.join(price_fields)}")
                    for field in price_fields:
                        cursor.execute(f"SELECT COUNT(*) FROM {table} WHERE {field} IS NOT NULL AND {field} > 0")
                        count = cursor.fetchone()[0]
                        percentage = (count / total_count * 100) if total_count > 0 else 0
                        print(f"     {field}: {count:,} 条 ({percentage:.1f}%)")
                        
                        if count > 0:
                            cursor.execute(f"SELECT MIN({field}), MAX({field}), AVG({field}) FROM {table} WHERE {field} IS NOT NULL AND {field} > 0")
                            stats = cursor.fetchone()
                            print(f"       范围: {stats[0]:.2f} - {stats[1]:.2f}, 平均: {stats[2]:.2f}")
                
                # 检查最近的数据
                if 'date' in column_names:
                    print(f"   最近10条记录:")
                    cursor.execute(f"SELECT * FROM {table} ORDER BY date DESC LIMIT 10")
                    recent_data = cursor.fetchall()
                    
                    if recent_data:
                        # 显示列标题
                        print(f"     {'日期':<12} {'价格':<8} {'Y值':<8} {'X值':<8}")
                        print(f"     {'-'*12} {'-'*8} {'-'*8} {'-'*8}")
                        
                        for row in recent_data:
                            date_val = str(row[0]) if row[0] else "N/A"
                            
                            # 找价格字段
                            price_val = "N/A"
                            for i, col_name in enumerate(column_names):
                                if col_name.lower() in ['close', 'price'] and i < len(row):
                                    if row[i] is not None:
                                        price_val = f"{row[i]:.2f}"
                                    break
                            
                            # 找Y字段
                            y_val = "N/A"
                            for i, col_name in enumerate(column_names):
                                if 'y_' in col_name.lower() or 'probability' in col_name.lower():
                                    if i < len(row) and row[i] is not None:
                                        y_val = f"{row[i]:.4f}"
                                    break
                            
                            # 找X字段
                            x_val = "N/A"
                            for i, col_name in enumerate(column_names):
                                if 'x_' in col_name.lower() or 'inflow' in col_name.lower() or 'ratio' in col_name.lower():
                                    if i < len(row) and row[i] is not None:
                                        x_val = f"{row[i]:.4f}"
                                    break
                            
                            print(f"     {date_val:<12} {price_val:<8} {y_val:<8} {x_val:<8}")
        
        # 3. 检查数据完整性（同时有Y和X值的记录）
        print(f"\n🎯 数据完整性分析:")
        print("-" * 40)
        
        for table in important_tables:
            if table in tables:
                # 找到Y和X字段
                cursor.execute(f"DESCRIBE {table}")
                columns = cursor.fetchall()
                column_names = [col[0] for col in columns]
                
                y_field = None
                x_field = None
                
                for col in column_names:
                    if 'y_' in col.lower() or 'probability' in col.lower():
                        y_field = col
                    if 'x_' in col.lower() or 'inflow' in col.lower() or 'ratio' in col.lower():
                        x_field = col
                
                if y_field and x_field:
                    # 检查同时有Y和X值的记录
                    cursor.execute(f"""
                        SELECT COUNT(*) FROM {table} 
                        WHERE {y_field} IS NOT NULL 
                        AND {x_field} IS NOT NULL
                        AND date >= DATE_SUB(CURDATE(), INTERVAL 20 YEAR)
                    """)
                    complete_count = cursor.fetchone()[0]
                    
                    cursor.execute(f"""
                        SELECT COUNT(*) FROM {table} 
                        WHERE date >= DATE_SUB(CURDATE(), INTERVAL 20 YEAR)
                    """)
                    total_20year = cursor.fetchone()[0]
                    
                    if total_20year > 0:
                        completeness = (complete_count / total_20year) * 100
                        print(f"   {table}: {complete_count:,}/{total_20year:,} ({completeness:.1f}%) 完整记录 (20年)")
                        
                        if completeness >= 80:
                            print(f"     ✅ 数据完整性良好，可以进行回测")
                        elif completeness >= 50:
                            print(f"     ⚠️ 数据完整性一般，建议检查缺失数据")
                        else:
                            print(f"     ❌ 数据完整性较差，需要补充数据")
        
        # 4. 推荐使用的表
        print(f"\n💡 推荐使用的表:")
        print("-" * 40)
        
        best_table = None
        best_completeness = 0
        
        for table in important_tables:
            if table in tables:
                cursor.execute(f"DESCRIBE {table}")
                columns = cursor.fetchall()
                column_names = [col[0] for col in columns]
                
                y_field = None
                x_field = None
                
                for col in column_names:
                    if 'y_' in col.lower() or 'probability' in col.lower():
                        y_field = col
                    if 'x_' in col.lower() or 'inflow' in col.lower() or 'ratio' in col.lower():
                        x_field = col
                
                if y_field and x_field:
                    cursor.execute(f"""
                        SELECT COUNT(*) FROM {table} 
                        WHERE {y_field} IS NOT NULL 
                        AND {x_field} IS NOT NULL
                        AND date >= DATE_SUB(CURDATE(), INTERVAL 20 YEAR)
                    """)
                    complete_count = cursor.fetchone()[0]
                    
                    cursor.execute(f"""
                        SELECT COUNT(*) FROM {table} 
                        WHERE date >= DATE_SUB(CURDATE(), INTERVAL 20 YEAR)
                    """)
                    total_count = cursor.fetchone()[0]
                    
                    if total_count > 0:
                        completeness = (complete_count / total_count) * 100
                        if completeness > best_completeness:
                            best_completeness = completeness
                            best_table = table
        
        if best_table:
            print(f"   推荐使用: {best_table} (完整性: {best_completeness:.1f}%)")
            print(f"   可以开始进行Cosmoon的博弈论策略回测！")
        else:
            print(f"   ❌ 未找到合适的数据表进行回测")
        
        connection.close()
        return True
        
    except Exception as e:
        print(f"❌ 数据库检查失败: {e}")
        return False

def main():
    """主函数"""
    print("🎯 Cosmoon博弈论策略 - 数据库数据检查")
    print("="*60)
    print("📅 检查时间:", datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
    
    if check_database_data():
        print(f"\n🎉 数据库检查完成!")
        print(f"💡 现在可以运行Cosmoon的博弈论回测了")
    else:
        print(f"\n❌ 数据库检查失败")

if __name__ == "__main__":
    main()
