@echo off
title Daily Trading System

echo ========================================
echo Daily Trading System
echo ========================================
echo Date: %date%
echo Time: %time%
echo.

echo Current directory: %cd%
echo.

echo Checking Python...
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python not found
    pause
    exit /b 1
)

echo Python: OK
echo.

echo Checking main script...
if not exist "complete_daily_update_with_position.py" (
    echo ERROR: Main script not found
    pause
    exit /b 1
)

echo Script: OK
echo.

echo ========================================
echo Starting Daily Update
echo ========================================
echo.

python complete_daily_update_with_position.py

echo.
echo ========================================
echo Update Completed
echo ========================================
echo.

if exist "交易记录追踪0023HK.xlsx" (
    echo Excel file: OK
) else (
    echo Excel file: NOT FOUND
)

echo.
echo Press any key to exit...
pause >nul
