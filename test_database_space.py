#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试数据库空间和功能
==================
验证数据库是否可以正常工作
"""

import mysql.connector
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

def test_database_functionality():
    """测试数据库功能"""
    
    # 数据库配置
    db_config = {
        'host': 'localhost',
        'database': 'finance',
        'user': 'root',
        'password': '12345678',
        'charset': 'utf8mb4',
        'autocommit': True
    }
    
    try:
        print("🧪 测试数据库空间和功能")
        print("=" * 50)
        print(f"📅 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        # 连接数据库
        connection = mysql.connector.connect(**db_config)
        cursor = connection.cursor()
        print(f"✅ 成功连接到数据库: {db_config['host']}/{db_config['database']}")
        
        # 1. 检查数据库空间
        print("\n📊 检查数据库空间...")
        cursor.execute("""
            SELECT 
                ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) as 'Size_MB',
                COUNT(*) as table_count
            FROM information_schema.tables 
            WHERE table_schema = 'finance'
        """)
        
        size_info = cursor.fetchone()
        db_size = size_info[0]
        table_count = size_info[1]
        
        print(f"📊 数据库大小: {db_size} MB")
        print(f"📊 表数量: {table_count}")
        
        # 2. 测试基本操作
        print("\n🧪 测试基本数据库操作...")
        
        # 创建测试表
        try:
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS space_test (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    test_data VARCHAR(100),
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            print("✅ 创建测试表成功")
        except mysql.connector.Error as e:
            print(f"❌ 创建测试表失败: {e}")
            return False
        
        # 插入测试数据
        try:
            cursor.execute("""
                INSERT INTO space_test (test_data) 
                VALUES ('Database space test'), ('Function test'), ('Connection test')
            """)
            print("✅ 插入测试数据成功")
        except mysql.connector.Error as e:
            print(f"❌ 插入测试数据失败: {e}")
            return False
        
        # 查询测试数据
        try:
            cursor.execute("SELECT COUNT(*) FROM space_test")
            test_count = cursor.fetchone()[0]
            print(f"✅ 查询测试数据成功: {test_count} 条记录")
        except mysql.connector.Error as e:
            print(f"❌ 查询测试数据失败: {e}")
            return False
        
        # 3. 测试sp_updatecontroller函数
        print("\n🧪 测试sp_updatecontroller函数...")
        try:
            # 检查函数是否存在
            cursor.execute("""
                SELECT COUNT(*) FROM information_schema.ROUTINES 
                WHERE ROUTINE_SCHEMA = 'finance' 
                AND ROUTINE_NAME = 'sp_updatecontroller'
            """)
            
            if cursor.fetchone()[0] > 0:
                print("✅ sp_updatecontroller函数存在")
                
                # 测试调用
                args = ['stock_600887_ss', 0]
                result = cursor.callproc('sp_updatecontroller', args)
                
                # 消费结果集
                for result_set in cursor.stored_results():
                    rows = result_set.fetchall()
                    for row in rows:
                        print(f"📊 {row}")
                
                k_value = result[1]
                print(f"✅ sp_updatecontroller调用成功, k值: {k_value}")
            else:
                print("⚠️ sp_updatecontroller函数不存在")
        except mysql.connector.Error as e:
            print(f"❌ 测试sp_updatecontroller失败: {e}")
        
        # 4. 检查关键表的状态
        print("\n📊 检查关键表状态...")
        key_tables = ['stock_600887_ss', 'stock_600036_ss', 'stock_600000_ss']
        
        for table_name in key_tables:
            try:
                cursor.execute(f"""
                    SELECT 
                        COUNT(*) as row_count,
                        MAX(date) as latest_date,
                        COUNT(DISTINCT date) as unique_dates
                    FROM {table_name}
                """)
                
                table_info = cursor.fetchone()
                if table_info:
                    row_count = table_info[0]
                    latest_date = table_info[1]
                    unique_dates = table_info[2]
                    print(f"✅ {table_name}: {row_count} 行, 最新日期: {latest_date}, 唯一日期: {unique_dates}")
                else:
                    print(f"⚠️ {table_name}: 无数据")
            except mysql.connector.Error as e:
                print(f"❌ 检查 {table_name} 失败: {e}")
        
        # 5. 检查磁盘空间使用情况
        print("\n💾 检查存储引擎状态...")
        try:
            cursor.execute("SHOW ENGINE INNODB STATUS")
            # 这个查询返回大量信息，我们只检查是否能执行
            cursor.fetchall()
            print("✅ InnoDB引擎状态正常")
        except mysql.connector.Error as e:
            print(f"⚠️ 检查InnoDB状态失败: {e}")
        
        # 6. 清理测试数据
        print("\n🗑️ 清理测试数据...")
        try:
            cursor.execute("DROP TABLE IF EXISTS space_test")
            print("✅ 清理测试表成功")
        except mysql.connector.Error as e:
            print(f"⚠️ 清理测试表失败: {e}")
        
        # 7. 最终状态检查
        print("\n📊 最终状态检查...")
        cursor.execute("""
            SELECT 
                ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) as 'Size_MB'
            FROM information_schema.tables 
            WHERE table_schema = 'finance'
        """)
        
        final_size = cursor.fetchone()[0]
        print(f"📊 最终数据库大小: {final_size} MB")
        
        # 关闭连接
        cursor.close()
        connection.close()
        
        print("\n🎉 数据库功能测试完成!")
        
        # 生成状态报告
        print("\n📋 状态报告:")
        print("=" * 50)
        print(f"✅ 数据库连接: 正常")
        print(f"✅ 基本操作: 正常")
        print(f"✅ 数据大小: {final_size} MB")
        print(f"✅ 表数量: {table_count}")
        
        if final_size < 100:
            print("💡 数据库大小正常，无需担心空间问题")
        elif final_size < 500:
            print("⚠️ 数据库大小适中，建议定期清理")
        else:
            print("🚨 数据库较大，建议执行清理操作")
        
        return True
        
    except mysql.connector.Error as e:
        print(f"❌ 数据库连接或操作失败: {e}")
        
        # 提供解决建议
        print("\n💡 可能的解决方案:")
        print("1. 检查数据库服务是否正常运行")
        print("2. 检查磁盘空间是否充足")
        print("3. 检查MySQL配置文件")
        print("4. 重启MySQL服务")
        print("5. 检查网络连接")
        
        return False
    except Exception as e:
        print(f"❌ 执行失败: {e}")
        return False

def show_space_management_tips():
    """显示空间管理建议"""
    print("\n💡 数据库空间管理建议:")
    print("=" * 50)
    
    print("🔧 定期维护:")
    print("   1. 每月运行 OPTIMIZE TABLE")
    print("   2. 清理二进制日志")
    print("   3. 删除不需要的临时表")
    print("   4. 监控数据库大小增长")
    
    print("\n📊 数据管理:")
    print("   1. 归档超过2年的历史数据")
    print("   2. 删除重复或无效数据")
    print("   3. 压缩大表")
    print("   4. 优化索引使用")
    
    print("\n⚠️ 紧急情况:")
    print("   1. 删除最老的数据")
    print("   2. 临时移动数据到其他位置")
    print("   3. 增加磁盘空间")
    print("   4. 联系系统管理员")
    
    print("\n📝 SQL脚本:")
    print("   • database_space_solution.sql - 完整清理方案")
    print("   • quick_cleanup_script.sql - 快速清理")

def main():
    """主函数"""
    success = test_database_functionality()
    
    if success:
        print("\n✅ 测试完成!")
        print("📝 数据库功能正常，可以继续使用")
    else:
        print("\n❌ 测试失败!")
        print("📝 请检查数据库状态或联系管理员")
    
    show_space_management_tips()

if __name__ == "__main__":
    main()
