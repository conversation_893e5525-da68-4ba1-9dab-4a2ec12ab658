#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试更新后的算法
==============
测试基于HSI50算法改写的XY指标和新增的做空条件
"""

import mysql.connector
import pandas as pd
import numpy as np
from datetime import datetime
from my_trading_algorithm_core import MyTradingAlgorithmCore
import warnings
warnings.filterwarnings('ignore')

class AlgorithmTester:
    """算法测试类"""
    
    def __init__(self):
        """初始化"""
        self.host = "localhost"
        self.user = "root"
        self.password = "12345678"
        self.database = "finance"
        
    def connect_database(self):
        """连接数据库"""
        try:
            self.conn = mysql.connector.connect(
                host=self.host,
                user=self.user,
                password=self.password,
                database=self.database
            )
            print("✅ 数据库连接成功")
            return True
        except Exception as e:
            print(f"❌ 数据库连接失败: {e}")
            return False
    
    def load_test_data(self):
        """加载测试数据"""
        print("📊 加载hkhsi50测试数据...")
        
        query = """
            SELECT Date, Open, High, Low, Close, Volume 
            FROM hkhsi50 
            ORDER BY Date DESC
            LIMIT 1000
        """
        
        df = pd.read_sql_query(query, self.conn)
        df = df.sort_values('Date').reset_index(drop=True)
        
        # 数据类型转换
        for col in ['Open', 'High', 'Low', 'Close', 'Volume']:
            df[col] = pd.to_numeric(df[col], errors='coerce')
        
        print(f"✅ 成功加载 {len(df):,} 条记录")
        return df
    
    def test_xy_indicators(self):
        """测试XY指标计算"""
        print("\n🎯 测试XY指标计算")
        print("=" * 60)
        
        if not self.connect_database():
            return
        
        df = self.load_test_data()
        algo = MyTradingAlgorithmCore()
        
        # 计算Y指标
        df = algo.calculate_y_indicator(df)
        
        # 计算X指标
        df = algo.calculate_x_indicator(df)
        
        # 显示最新的指标值
        latest = df.iloc[-1]
        
        print(f"📅 最新数据日期: {latest['Date']}")
        print(f"💰 收盘价: {latest['Close']:.2f}")
        print(f"📊 Y值 (价格位置): {latest['Y']:.4f}")
        print(f"📊 X值 (综合指标): {latest['X']:.4f}")
        
        # 统计指标分布
        print(f"\n📊 Y值统计:")
        print(f"   • 最小值: {df['Y'].min():.4f}")
        print(f"   • 最大值: {df['Y'].max():.4f}")
        print(f"   • 平均值: {df['Y'].mean():.4f}")
        print(f"   • 中位数: {df['Y'].median():.4f}")
        
        print(f"\n📊 X值统计:")
        print(f"   • 最小值: {df['X'].min():.4f}")
        print(f"   • 最大值: {df['X'].max():.4f}")
        print(f"   • 平均值: {df['X'].mean():.4f}")
        print(f"   • 中位数: {df['X'].median():.4f}")
        
        self.conn.close()
        return df
    
    def test_trading_signals(self):
        """测试交易信号生成"""
        print("\n🎯 测试交易信号生成")
        print("=" * 60)
        
        if not self.connect_database():
            return
        
        df = self.load_test_data()
        algo = MyTradingAlgorithmCore()
        
        # 处理完整数据
        df = algo.process_market_data(df)
        
        # 统计信号分布
        signal_counts = df['signal'].value_counts().sort_index()
        total_signals = len(df)
        
        print(f"📊 信号统计 (最近1000天):")
        for signal, count in signal_counts.items():
            if signal == 1:
                signal_name = "🟢 做多"
            elif signal == -1:
                signal_name = "🔴 做空"
            else:
                signal_name = "⚪ 观望"
            
            percentage = count / total_signals * 100
            print(f"   {signal_name}: {count}次 ({percentage:.1f}%)")
        
        # 显示最近10天的信号
        print(f"\n📅 最近10天信号:")
        recent_signals = df.tail(10)[['Date', 'Close', 'Y', 'X', 'signal', 'price_position']]
        
        for _, row in recent_signals.iterrows():
            signal_text = "🟢 做多" if row['signal'] == 1 else "🔴 做空" if row['signal'] == -1 else "⚪ 观望"
            print(f"   {row['Date'].strftime('%Y-%m-%d')}: {signal_text} | "
                  f"价格:{row['Close']:.1f} | Y:{row['Y']:.3f} | X:{row['X']:.3f} | "
                  f"价格位置:{row['price_position']:.3f}")
        
        # 分析做空条件触发情况
        self.analyze_short_conditions(df)
        
        self.conn.close()
        return df
    
    def analyze_short_conditions(self, df):
        """分析做空条件触发情况"""
        print(f"\n🔍 做空条件分析:")
        
        # 条件1: Y<0.3 或 X<0.3
        condition1 = (df['Y'] < 0.3) | (df['X'] < 0.3)
        
        # 条件2: X>0.45 且 Y<0.35
        condition2 = (df['X'] > 0.45) & (df['Y'] < 0.35)
        
        # 条件3: X<0.45 且 Y>0.35
        condition3 = (df['X'] < 0.45) & (df['Y'] > 0.35)
        
        # 价格高于回归线
        price_above_regression = df['price_position'] > 0
        
        # 实际做空信号
        actual_short = df['signal'] == -1
        
        print(f"   条件1 (Y<0.3或X<0.3): {condition1.sum()}次 ({condition1.sum()/len(df)*100:.1f}%)")
        print(f"   条件2 (X>0.45且Y<0.35): {condition2.sum()}次 ({condition2.sum()/len(df)*100:.1f}%)")
        print(f"   条件3 (X<0.45且Y>0.35): {condition3.sum()}次 ({condition3.sum()/len(df)*100:.1f}%)")
        print(f"   价格高于回归线: {price_above_regression.sum()}次 ({price_above_regression.sum()/len(df)*100:.1f}%)")
        print(f"   实际做空信号: {actual_short.sum()}次 ({actual_short.sum()/len(df)*100:.1f}%)")
        
        # 分析各条件对做空信号的贡献
        short_by_condition1 = (condition1 & price_above_regression).sum()
        short_by_condition2 = (condition2 & price_above_regression).sum()
        short_by_condition3 = (condition3 & price_above_regression).sum()
        
        print(f"\n   各条件贡献的做空信号:")
        print(f"   • 条件1贡献: {short_by_condition1}次")
        print(f"   • 条件2贡献: {short_by_condition2}次")
        print(f"   • 条件3贡献: {short_by_condition3}次")
    
    def test_real_time_signal(self):
        """测试实时信号"""
        print("\n⚡ 测试实时信号")
        print("=" * 60)
        
        if not self.connect_database():
            return
        
        df = self.load_test_data()
        algo = MyTradingAlgorithmCore()
        
        # 处理数据
        df = algo.process_market_data(df)
        
        # 获取最新信号
        signal_info = algo.get_latest_signal(df)
        
        print(f"📅 最新信号时间: {signal_info['date']}")
        print(f"💰 当前价格: {signal_info['close_price']:.2f}")
        print(f"📊 Y值: {signal_info['y_value']:.4f}")
        print(f"📊 X值: {signal_info['x_value']:.4f}")
        print(f"📊 MFI: {signal_info['mfi']:.1f}")
        print(f"📊 RSI: {signal_info['rsi']:.1f}")
        
        # 解释信号
        signal = signal_info['signal']
        if signal == 1:
            print(f"🟢 交易信号: 做多")
            print(f"   原因: Y>0.45且X>0.45且价格低于回归线")
        elif signal == -1:
            print(f"🔴 交易信号: 做空")
            print(f"   原因: 满足做空条件且价格高于回归线")
        else:
            print(f"⚪ 交易信号: 观望")
            print(f"   原因: 不满足做多或做空条件")
        
        # 计算止盈止损位
        if signal != 0:
            take_profit, stop_loss = algo.calculate_stop_levels(signal_info['close_price'], signal)
            print(f"\n🎯 建议交易参数:")
            print(f"   入场价格: {signal_info['close_price']:.2f}")
            print(f"   止盈位: {take_profit:.2f}")
            print(f"   止损位: {stop_loss:.2f}")
            
            if signal == 1:
                profit_pct = (take_profit - signal_info['close_price']) / signal_info['close_price'] * 100
                loss_pct = (signal_info['close_price'] - stop_loss) / signal_info['close_price'] * 100
            else:
                profit_pct = (signal_info['close_price'] - take_profit) / signal_info['close_price'] * 100
                loss_pct = (stop_loss - signal_info['close_price']) / signal_info['close_price'] * 100
            
            print(f"   预期盈利: {profit_pct:.1f}%")
            print(f"   最大亏损: {loss_pct:.1f}%")
        
        self.conn.close()
    
    def run_all_tests(self):
        """运行所有测试"""
        print("🎯 更新后算法完整测试")
        print("=" * 80)
        
        self.test_xy_indicators()
        self.test_trading_signals()
        self.test_real_time_signal()
        
        print(f"\n🎉 所有测试完成！")
        print(f"💡 算法已成功更新为HSI50增强版")

def main():
    """主函数"""
    tester = AlgorithmTester()
    tester.run_all_tests()

if __name__ == "__main__":
    main()
