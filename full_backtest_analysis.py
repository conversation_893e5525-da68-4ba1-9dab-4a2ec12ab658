#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
全回测分析
=========
使用本地数据库进行完整的回测分析
"""

import sqlite3
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

class FullBacktestAnalysis:
    def __init__(self):
        """初始化全回测分析器"""
        self.initial_capital = 30000
        self.monthly_addition = 1000
        self.max_position_ratio = 0.35  # 35%仓位

        # 策略参数
        self.take_profit_long = 0.016   # 多头止盈1.6%
        self.stop_loss_long = 0.008     # 多头止损0.8%
        self.take_profit_short = 0.008  # 空头止盈0.8%
        self.stop_loss_short = 0.016    # 空头止损1.6%

        # Y>0.43, X>0.43策略
        self.y_threshold = 0.43
        self.x_threshold = 0.43

        self.trades = []
        self.daily_equity = []

    def check_available_databases(self):
        """检查可用的数据库"""
        print("🔍 检查可用的数据库文件...")

        import os
        db_files = [f for f in os.listdir('.') if f.endswith('.db')]

        print(f"   发现数据库文件: {db_files}")

        for db_file in db_files:
            try:
                conn = sqlite3.connect(db_file)
                cursor = conn.cursor()

                # 获取表名
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
                tables = cursor.fetchall()

                print(f"\n📊 数据库: {db_file}")
                print(f"   包含表: {[table[0] for table in tables]}")

                # 检查每个表的记录数
                for table in tables:
                    table_name = table[0]
                    try:
                        cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
                        count = cursor.fetchone()[0]
                        print(f"   • {table_name}: {count:,}条记录")

                        # 检查列名
                        cursor.execute(f"PRAGMA table_info({table_name})")
                        columns = cursor.fetchall()
                        column_names = [col[1] for col in columns]
                        print(f"     列: {column_names[:10]}...")  # 显示前10列

                    except Exception as e:
                        print(f"   • {table_name}: 无法读取 ({e})")

                conn.close()

            except Exception as e:
                print(f"   ❌ 无法打开 {db_file}: {e}")

    def load_full_data(self):
        """加载完整数据"""
        print("\n📊 尝试加载完整数据...")

        # 尝试不同的数据库和表名组合，优先使用记录最多的
        db_table_combinations = [
            ("hsi_25years.db", "hsi_data"),  # 6,158条记录，优先使用
            ("hk00023_20year.db", "hk00023"),  # 4,932条记录
            ("finance.db", "hkhsi50"),
            ("hsi_25years.db", "hsi50_data"),
            ("hsi_data.db", "hsi_data"),
            ("finance.db", "hsi_data"),
            ("hsi_25years.db", "hkhsi50")
        ]

        for db_name, table_name in db_table_combinations:
            try:
                print(f"   尝试: {db_name} -> {table_name}")
                conn = sqlite3.connect(db_name)

                # 先检查表是否存在
                cursor = conn.cursor()
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
                tables = [table[0] for table in cursor.fetchall()]

                if table_name not in tables:
                    print(f"   ❌ 表 {table_name} 不存在")
                    conn.close()
                    continue

                # 检查记录数
                cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
                count = cursor.fetchone()[0]
                print(f"   📊 找到 {count:,} 条记录")

                if count < 1000:
                    print(f"   ⚠️ 记录数太少，跳过")
                    conn.close()
                    continue

                # 检查列名
                cursor.execute(f"PRAGMA table_info({table_name})")
                columns = cursor.fetchall()
                column_names = [col[1] for col in columns]
                print(f"   📋 列名: {column_names}")

                # 尝试加载数据
                query = f"""
                    SELECT * FROM {table_name}
                    ORDER BY date ASC
                    LIMIT 10
                """

                sample_df = pd.read_sql_query(query, conn)
                print(f"   📊 样本数据:")
                print(sample_df.head())

                # 如果样本数据看起来正确，加载全部数据
                if len(sample_df) > 0:
                    print(f"   ✅ 开始加载全部数据...")

                    # 检查必要的列是否存在
                    required_columns = ['date', 'open', 'high', 'low', 'close']
                    missing_columns = [col for col in required_columns if col not in column_names]

                    if missing_columns:
                        print(f"   ❌ 缺少必要列: {missing_columns}")
                        conn.close()
                        continue

                    # 加载全部数据
                    full_query = f"""
                        SELECT * FROM {table_name}
                        ORDER BY date ASC
                    """

                    self.df = pd.read_sql_query(full_query, conn)
                    conn.close()

                    print(f"   ✅ 成功加载 {len(self.df):,} 条记录")
                    print(f"   📅 数据范围: {self.df['date'].min()} 至 {self.df['date'].max()}")

                    # 数据预处理
                    self.preprocess_data()
                    return True

                conn.close()

            except Exception as e:
                print(f"   ❌ 加载失败: {e}")
                continue

        print("❌ 无法找到合适的数据源")
        return False

    def preprocess_data(self):
        """数据预处理"""
        print("   🔧 数据预处理...")

        # 确保日期格式正确
        self.df['Date'] = pd.to_datetime(self.df['date'])
        self.df = self.df.sort_values('Date').reset_index(drop=True)

        # 重命名列以保持一致性
        column_mapping = {
            'open': 'Open',
            'high': 'High',
            'low': 'Low',
            'close': 'Close',
            'volume': 'Volume'
        }

        for old_col, new_col in column_mapping.items():
            if old_col in self.df.columns:
                self.df[new_col] = self.df[old_col]

        # 计算技术指标
        self.calculate_indicators()

        print(f"   ✅ 预处理完成，数据范围: {self.df['Date'].min().date()} 至 {self.df['Date'].max().date()}")

    def calculate_indicators(self):
        """计算技术指标"""
        print("   📊 计算技术指标...")

        # 移动平均线
        self.df['ma_20'] = self.df['Close'].rolling(window=20).mean()
        self.df['ma_60'] = self.df['Close'].rolling(window=60).mean()

        # 计算XY指标
        self.df['midprice'] = (self.df['High'] + self.df['Low']) / 2
        self.df['controller'] = (self.df['Close'] > self.df['midprice']).astype(int)
        self.df['cumulative_controller'] = self.df['controller'].cumsum()
        self.df['row_number'] = range(1, len(self.df) + 1)
        self.df['Full_Y'] = self.df['cumulative_controller'] / self.df['row_number']

        self.df['Y'] = self.df['Full_Y']
        self.df['X'] = 1 - self.df['Full_Y']

        # 计算E值
        self.df['price_vs_ma20'] = self.df['Close'] / self.df['ma_20']
        self.df['y_probability'] = np.where(
            self.df['price_vs_ma20'] >= 1,
            0.5 + 0.3 * np.tanh((self.df['price_vs_ma20'] - 1) * 2),
            0.5 - 0.3 * np.tanh((1 - self.df['price_vs_ma20']) * 2)
        )
        self.df['y_probability'] = np.clip(self.df['y_probability'], 0.1, 0.9)

        self.df['price_deviation'] = (self.df['Close'] - self.df['midprice']) / self.df['midprice']
        self.df['E'] = (self.df['y_probability'] * 0.4 +
                       self.df['price_deviation'] * 0.3 +
                       (self.df['Y'] - 0.5) * 0.3)

        print("   ✅ 技术指标计算完成")

    def check_entry_conditions(self, row):
        """检查入场条件"""
        y_value = row['Y']
        x_value = row['X']

        if pd.isna(y_value) or pd.isna(x_value):
            return 0

        # Y>0.43且X>0.43: 看涨
        if y_value > self.y_threshold and x_value > self.x_threshold:
            return 1  # 多头

        # 强亏损区: 看跌
        elif y_value < 0.25 or x_value < 0.25:
            return -1  # 空头

        # 其他区域: 看跌
        elif not (0.333 < y_value < self.y_threshold):
            return -1  # 空头

        return 0  # 观望

    def add_monthly_capital(self, date, capital):
        """每月增加资金"""
        last_month = getattr(self, 'last_month', None)
        current_month = date.replace(day=1)

        if last_month is None or current_month > last_month:
            self.last_month = current_month
            return capital + self.monthly_addition

        return capital

    def run_full_backtest(self):
        """运行完整回测"""
        print(f"\n🎯 开始完整回测 (Y>{self.y_threshold}, X>{self.x_threshold}策略)...")

        capital = self.initial_capital
        position = 0
        entry_price = 0
        entry_date = None
        entry_y = 0
        entry_x = 0
        entry_e = 0
        take_profit_price = 0
        stop_loss_price = 0

        max_capital = capital
        max_drawdown = 0

        start_index = max(60, 0)  # 从第60天开始或从头开始

        for i in range(start_index, len(self.df)):
            row = self.df.iloc[i]
            date = row['Date']

            # 每月增加资金
            capital = self.add_monthly_capital(date, capital)

            # 记录每日权益
            current_equity = capital
            if position != 0:
                position_size = capital * self.max_position_ratio
                if position == 1:  # 多头
                    unrealized_pnl = (row['Close'] - entry_price) / entry_price * position_size
                else:  # 空头
                    unrealized_pnl = (entry_price - row['Close']) / entry_price * position_size
                current_equity += unrealized_pnl

            # 更新最大资金和回撤
            if current_equity > max_capital:
                max_capital = current_equity

            current_drawdown = (max_capital - current_equity) / max_capital
            if current_drawdown > max_drawdown:
                max_drawdown = current_drawdown

            # 检查平仓条件
            if position != 0:
                should_exit = False
                exit_reason = ""
                exit_price = row['Close']

                if position == 1:  # 多头
                    if row['High'] >= take_profit_price:
                        should_exit = True
                        exit_reason = "止盈"
                        exit_price = take_profit_price
                    elif row['Low'] <= stop_loss_price:
                        should_exit = True
                        exit_reason = "止损"
                        exit_price = stop_loss_price

                elif position == -1:  # 空头
                    if row['Low'] <= take_profit_price:
                        should_exit = True
                        exit_reason = "止盈"
                        exit_price = take_profit_price
                    elif row['High'] >= stop_loss_price:
                        should_exit = True
                        exit_reason = "止损"
                        exit_price = stop_loss_price

                if should_exit:
                    position_size = capital * self.max_position_ratio

                    if position == 1:
                        profit_ratio = (exit_price - entry_price) / entry_price
                    else:
                        profit_ratio = (entry_price - exit_price) / entry_price

                    profit = position_size * profit_ratio
                    capital += profit

                    holding_days = (date - entry_date).days

                    self.trades.append({
                        'entry_date': entry_date,
                        'exit_date': date,
                        'direction': '看涨' if position == 1 else '看跌',
                        'entry_price': entry_price,
                        'exit_price': exit_price,
                        'profit': profit,
                        'profit_ratio': profit_ratio,
                        'holding_days': holding_days,
                        'exit_reason': exit_reason,
                        'entry_y': entry_y,
                        'entry_x': entry_x,
                        'entry_e': entry_e,
                        'capital_after': capital
                    })

                    position = 0

            # 检查开仓条件
            if position == 0:
                signal = self.check_entry_conditions(row)

                if signal != 0:
                    position = signal
                    entry_price = row['Close']
                    entry_date = date
                    entry_y = row['Y']
                    entry_x = row['X']
                    entry_e = row['E']

                    if position == 1:
                        take_profit_price = entry_price * (1 + self.take_profit_long)
                        stop_loss_price = entry_price * (1 - self.stop_loss_long)
                    else:
                        take_profit_price = entry_price * (1 - self.take_profit_short)
                        stop_loss_price = entry_price * (1 + self.stop_loss_short)

        self.final_capital = capital
        self.max_drawdown = max_drawdown

        print(f"✅ 完整回测完成！")
        print(f"💰 最终资金: {self.final_capital:,.2f}港元")
        print(f"📊 总交易次数: {len(self.trades)}笔")
        print(f"📉 最大回撤: {self.max_drawdown*100:.2f}%")

    def generate_full_report(self):
        """生成完整报告"""
        print("\n" + "="*80)
        print("📊 HSI50 XY策略完整回测报告 (全数据)")
        print("="*80)
        print(f"📅 报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"🎯 策略参数: Y>{self.y_threshold}, X>{self.x_threshold}")
        print(f"📊 数据记录数: {len(self.df):,}条")

        if not self.trades:
            print("❌ 没有产生任何交易")
            return

        # 基本统计
        trades_df = pd.DataFrame(self.trades)

        total_trades = len(trades_df)
        winning_trades = len(trades_df[trades_df['profit'] > 0])
        losing_trades = total_trades - winning_trades
        win_rate = winning_trades / total_trades if total_trades > 0 else 0

        total_profit = trades_df['profit'].sum()
        avg_profit = trades_df['profit'].mean()
        max_profit = trades_df['profit'].max()
        min_profit = trades_df['profit'].min()

        # 时间统计
        start_date = self.df['Date'].min()
        end_date = self.df['Date'].max()
        total_days = (end_date - start_date).days
        total_years = total_days / 365

        # 投入资金计算
        months = total_days / 30
        total_invested = self.initial_capital + months * self.monthly_addition
        net_profit = self.final_capital - total_invested
        total_return = net_profit / total_invested if total_invested > 0 else 0
        annual_return = (self.final_capital / total_invested) ** (1/total_years) - 1 if total_years > 0 else 0

        print(f"\n📊 基本统计:")
        print(f"   回测期间: {start_date.date()} 至 {end_date.date()} ({total_years:.1f}年)")
        print(f"   数据记录: {len(self.df):,}条")
        print(f"   总投入: {total_invested:,.2f}港元")
        print(f"   最终资金: {self.final_capital:,.2f}港元")
        print(f"   净收益: {net_profit:,.2f}港元")
        print(f"   总收益率: {total_return*100:.2f}%")
        print(f"   年化收益率: {annual_return*100:.2f}%")

        print(f"\n📈 交易统计:")
        print(f"   总交易次数: {total_trades}笔")
        print(f"   盈利交易: {winning_trades}笔 ({win_rate*100:.1f}%)")
        print(f"   亏损交易: {losing_trades}笔 ({(1-win_rate)*100:.1f}%)")
        print(f"   平均每笔收益: {avg_profit:.2f}港元")
        print(f"   最大盈利: {max_profit:.2f}港元")
        print(f"   最大亏损: {min_profit:.2f}港元")

        # 止盈止损统计
        tp_trades = trades_df[trades_df['exit_reason'] == '止盈']
        sl_trades = trades_df[trades_df['exit_reason'] == '止损']

        print(f"\n🎯 止盈止损统计:")
        print(f"   止盈次数: {len(tp_trades)}笔 ({len(tp_trades)/total_trades*100:.1f}%)")
        print(f"   止损次数: {len(sl_trades)}笔 ({len(sl_trades)/total_trades*100:.1f}%)")

        if len(tp_trades) > 0:
            print(f"   止盈平均收益: {tp_trades['profit'].mean():.2f}港元")
        if len(sl_trades) > 0:
            print(f"   止损平均亏损: {sl_trades['profit'].mean():.2f}港元")

        # 方向统计
        long_trades = trades_df[trades_df['direction'] == '看涨']
        short_trades = trades_df[trades_df['direction'] == '看跌']

        print(f"\n📊 方向统计:")
        print(f"   看涨交易: {len(long_trades)}笔")
        print(f"   看跌交易: {len(short_trades)}笔")

        if len(long_trades) > 0:
            long_win_rate = len(long_trades[long_trades['profit'] > 0]) / len(long_trades)
            print(f"   看涨胜率: {long_win_rate*100:.1f}%")
            print(f"   看涨平均收益: {long_trades['profit'].mean():.2f}港元")

        if len(short_trades) > 0:
            short_win_rate = len(short_trades[short_trades['profit'] > 0]) / len(short_trades)
            print(f"   看跌胜率: {short_win_rate*100:.1f}%")
            print(f"   看跌平均收益: {short_trades['profit'].mean():.2f}港元")

        # 风险指标
        print(f"\n📉 风险指标:")
        print(f"   最大回撤: {self.max_drawdown*100:.2f}%")

        if annual_return > 0 and self.max_drawdown > 0:
            calmar_ratio = annual_return / self.max_drawdown
            print(f"   卡尔玛比率: {calmar_ratio:.2f}")

        # XY指标分析
        print(f"\n🎯 XY指标分析:")
        signal_data = self.df[(self.df['Y'] > self.y_threshold) & (self.df['X'] > self.x_threshold)]

        if len(signal_data) > 0:
            print(f"   满足Y>{self.y_threshold}, X>{self.x_threshold}的天数: {len(signal_data)}天")
            print(f"   占总天数比例: {len(signal_data)/len(self.df)*100:.1f}%")
            print(f"   信号期间Y值范围: {signal_data['Y'].min():.4f} ~ {signal_data['Y'].max():.4f}")
            print(f"   信号期间X值范围: {signal_data['X'].min():.4f} ~ {signal_data['X'].max():.4f}")

        print(f"\n🎉 全数据回测完成！")
        print(f"📊 这是基于 {len(self.df):,} 条记录的完整分析")

        return {
            'total_records': len(self.df),
            'total_trades': total_trades,
            'win_rate': win_rate,
            'annual_return': annual_return,
            'max_drawdown': self.max_drawdown,
            'final_capital': self.final_capital
        }

    def run(self):
        """运行完整分析"""
        print("🎯 HSI50 XY策略全数据回测分析")
        print("=" * 60)

        # 检查数据库
        self.check_available_databases()

        # 加载数据
        if not self.load_full_data():
            print("❌ 无法加载数据，分析终止")
            return None

        # 运行回测
        self.run_full_backtest()

        # 生成报告
        results = self.generate_full_report()

        return results

def main():
    """主函数"""
    analyzer = FullBacktestAnalysis()
    results = analyzer.run()

    if results:
        print(f"\n🎉 全数据分析完成！")
        print(f"📊 基于 {results['total_records']:,} 条记录的分析")
        print(f"🎯 总交易: {results['total_trades']}笔")
        print(f"✅ 胜率: {results['win_rate']*100:.1f}%")
        print(f"📈 年化收益: {results['annual_return']*100:.2f}%")

if __name__ == "__main__":
    main()
