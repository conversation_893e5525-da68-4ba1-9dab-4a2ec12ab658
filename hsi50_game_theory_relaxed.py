#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
HSI50博弈论回测系统 - 宽松版本
============================

为了展示博弈论策略的实际运作，适当放宽交易条件：
- 期望值阈值降低到5%
- 胜率阈值降低到60%
- 偏离阈值降低到5%
- 其他保持严格

作者: Cosmoon NG
日期: 2025年7月
"""

import pandas as pd
import yfinance as yf
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

class HSI50GameTheoryRelaxed:
    def __init__(self):
        """初始化宽松版博弈论回测系统"""
        self.symbol = "^HSI"
        self.initial_capital = 30000
        self.monthly_investment = 3000
        self.data = None
        
        # 宽松的博弈论策略参数
        self.strategy_params = {
            # 博弈论参数 - 宽松版
            'min_expected_value': 0.05,     # 最小期望值5%（降低）
            'win_probability_threshold': 0.6, # 胜率阈值60%（降低）
            'risk_reward_ratio': 2.5,       # 风险回报比1:2.5
            'market_sentiment_weight': 0.3,
            'opponent_strategy_weight': 0.2,
            
            # 回归中线参数 - 宽松版
            'median_period': 60,            # 缩短到60天
            'deviation_threshold': 0.05,    # 偏离阈值5%（降低）
            'extreme_deviation': 0.08,     # 极端偏离阈值8%（降低）
            
            # 凯利公式参数
            'kelly_multiplier': 0.6,
            'max_position_ratio': 0.1,     # 提高到10%
            'max_positions': 1,
            
            # 持仓参数
            'min_holding_days': 30,         # 缩短到30天
            'max_holding_days': 180,        # 缩短到180天
            'position_cooldown': 30,        # 缩短到30天
            
            # 止盈止损参数
            'take_profit': 0.15,            # 降低到15%
            'stop_loss': 0.06,              # 降低到6%
            
            'transaction_cost': 30,
            'compound_interest': True,
        }
        
        self.trades = []
        self.monthly_investments = []
        self.daily_portfolio = []
        self.current_positions = []
        self.current_capital = self.initial_capital
        self.last_trade_date = None
        self.total_invested = self.initial_capital
        self.game_theory_signals = []
    
    def fetch_hsi_data(self):
        """获取恒生指数数据"""
        print("📈 获取恒生指数20年历史数据...")
        
        try:
            end_date = datetime.now()
            start_date = end_date - timedelta(days=20*365)
            
            ticker = yf.Ticker(self.symbol)
            self.data = ticker.history(start=start_date, end=end_date)
            
            if self.data.empty:
                return False
            
            self.data.reset_index(inplace=True)
            self.data.columns = [col.lower() for col in self.data.columns]
            
            print(f"✅ 成功获取数据: {len(self.data):,} 天")
            return True
            
        except Exception as e:
            print(f"❌ 获取数据失败: {e}")
            return False
    
    def calculate_indicators(self):
        """计算指标"""
        print("📊 计算博弈论指标...")
        
        # 基础指标
        self.data['ma_20'] = self.data['close'].rolling(window=20).mean()
        self.data['ma_60'] = self.data['close'].rolling(window=60).mean()
        
        # 回归中线
        self.data['median_price'] = self.data['close'].rolling(window=self.strategy_params['median_period']).median()
        self.data['price_deviation'] = (self.data['close'] - self.data['median_price']) / self.data['median_price']
        
        # RSI
        delta = self.data['close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
        rs = gain / loss
        self.data['rsi'] = 100 - (100 / (1 + rs))
        
        # 市场情绪
        self.data['price_momentum'] = self.data['close'].pct_change(5)
        self.data['market_sentiment'] = np.tanh(self.data['price_momentum'] * 10)
        
        # 趋势
        self.data['uptrend'] = (self.data['ma_20'] > self.data['ma_60']) & (self.data['close'] > self.data['ma_20'])
        self.data['downtrend'] = (self.data['ma_20'] < self.data['ma_60']) & (self.data['close'] < self.data['ma_20'])
        
        print("✅ 指标计算完成")
    
    def calculate_game_theory_expected_value(self, row):
        """计算博弈论期望值 - 宽松版"""
        deviation = row['price_deviation']
        market_sentiment = row['market_sentiment']
        
        # 基础胜率（更宽松）
        if abs(deviation) < self.strategy_params['deviation_threshold']:
            base_win_prob = 0.5
        elif deviation > self.strategy_params['extreme_deviation']:
            base_win_prob = 0.65 + min(0.15, abs(deviation) - self.strategy_params['extreme_deviation']) * 5
        elif deviation < -self.strategy_params['extreme_deviation']:
            base_win_prob = 0.65 + min(0.15, abs(deviation) - self.strategy_params['extreme_deviation']) * 5
        else:
            base_win_prob = 0.5 + abs(deviation) * 3
        
        # 市场情绪调整
        sentiment_adjustment = market_sentiment * self.strategy_params['market_sentiment_weight']
        
        # 最终胜率
        win_probability = base_win_prob + sentiment_adjustment
        win_probability = np.clip(win_probability, 0.3, 0.85)
        
        # 期望值计算
        expected_win = self.strategy_params['take_profit']
        expected_loss = self.strategy_params['stop_loss']
        expected_value = win_probability * expected_win - (1 - win_probability) * expected_loss
        
        return expected_value, win_probability
    
    def get_game_theory_signal(self, row):
        """获取博弈论信号 - 宽松版"""
        deviation = row['price_deviation']
        uptrend = row['uptrend']
        downtrend = row['downtrend']
        
        # 检查冷却期
        if self.last_trade_date:
            days_since_last_trade = (row['date'] - self.last_trade_date).days
            if days_since_last_trade < self.strategy_params['position_cooldown']:
                return 'COOLDOWN', 0, 0
        
        # 计算期望值
        expected_value, win_probability = self.calculate_game_theory_expected_value(row)
        
        # 记录信号
        self.game_theory_signals.append({
            'date': row['date'].strftime('%Y-%m-%d'),
            'expected_value': expected_value,
            'win_probability': win_probability,
            'deviation': deviation,
            'signal': 'HOLD'
        })
        
        # 宽松的交易条件
        if (expected_value > self.strategy_params['min_expected_value'] and 
            win_probability > self.strategy_params['win_probability_threshold']):
            
            # 做多信号
            if (deviation < -self.strategy_params['extreme_deviation'] and uptrend):
                self.game_theory_signals[-1]['signal'] = 'GAME_BUY'
                return 'GAME_BUY', expected_value, win_probability
            
            # 做空信号
            elif (deviation > self.strategy_params['extreme_deviation'] and downtrend):
                self.game_theory_signals[-1]['signal'] = 'GAME_SELL'
                return 'GAME_SELL', expected_value, win_probability
        
        return 'MINIMAL_HOLD', expected_value, win_probability
    
    def backtest_relaxed_strategy(self):
        """执行宽松博弈论策略回测"""
        print("\n🚀 开始宽松博弈论策略回测...")
        print("="*50)
        print(f"💰 初始资金: {self.initial_capital:,} 港币")
        print(f"📅 每月定投: {self.monthly_investment:,} 港币")
        print(f"🎯 期望值阈值: {self.strategy_params['min_expected_value']*100}%")
        print(f"🎲 胜率阈值: {self.strategy_params['win_probability_threshold']*100}%")
        print(f"📊 偏离阈值: {self.strategy_params['extreme_deviation']*100}%")
        print("="*50)
        
        total_trades = 0
        winning_trades = 0
        minimal_hold_days = 0
        last_investment_month = None
        
        for i in range(60, len(self.data)):
            row = self.data.iloc[i]
            date = row['date']
            price = row['close']
            
            # 每月定投
            current_month = date.strftime('%Y-%m')
            if last_investment_month != current_month:
                self.current_capital += self.monthly_investment
                self.total_invested += self.monthly_investment
                self.monthly_investments.append({
                    'date': date.strftime('%Y-%m-%d'),
                    'amount': self.monthly_investment,
                    'total_invested': self.total_invested
                })
                last_investment_month = current_month
            
            # 获取信号
            signal, expected_value, win_probability = self.get_game_theory_signal(row)
            
            # 检查退出条件
            positions_to_close = []
            for j, position in enumerate(self.current_positions):
                holding_days = (date - position['entry_date']).days
                
                if position['direction'] == 'LONG':
                    profit_pct = (price - position['entry_price']) / position['entry_price']
                else:
                    profit_pct = (position['entry_price'] - price) / position['entry_price']
                
                should_exit = False
                exit_reason = ''
                
                if holding_days >= self.strategy_params['max_holding_days']:
                    should_exit, exit_reason = True, '时间止损'
                elif profit_pct >= self.strategy_params['take_profit']:
                    should_exit, exit_reason = True, '止盈'
                elif profit_pct <= -self.strategy_params['stop_loss']:
                    should_exit, exit_reason = True, '止损'
                
                if should_exit:
                    # 计算盈亏
                    if position['direction'] == 'LONG':
                        price_diff = price - position['entry_price']
                    else:
                        price_diff = position['entry_price'] - price
                    
                    gross_profit = price_diff * position['shares']
                    net_profit = gross_profit - self.strategy_params['transaction_cost']
                    
                    if self.strategy_params['compound_interest']:
                        self.current_capital += net_profit
                    
                    # 记录交易
                    self.trades.append({
                        'entry_date': position['entry_date'].strftime('%Y-%m-%d'),
                        'exit_date': date.strftime('%Y-%m-%d'),
                        'direction': position['direction'],
                        'entry_price': round(position['entry_price'], 2),
                        'exit_price': round(price, 2),
                        'shares': round(position['shares'], 2),
                        'net_profit': round(net_profit, 2),
                        'profit_pct': round(profit_pct * 100, 2),
                        'holding_days': holding_days,
                        'expected_value': round(position.get('expected_value', 0), 3),
                        'win_probability': round(position.get('win_probability', 0), 3),
                        'exit_reason': exit_reason
                    })
                    
                    if net_profit > 0:
                        winning_trades += 1
                    
                    total_trades += 1
                    self.last_trade_date = date
                    positions_to_close.append(j)
            
            # 移除已平仓的持仓
            for j in sorted(positions_to_close, reverse=True):
                del self.current_positions[j]
            
            # 开仓决策
            if signal in ['MINIMAL_HOLD', 'COOLDOWN']:
                minimal_hold_days += 1
                action = '很少持仓' if signal == 'MINIMAL_HOLD' else '冷却期'
            elif len(self.current_positions) < self.strategy_params['max_positions']:
                # 计算仓位
                kelly_position = min(0.1, max(0, (win_probability * 2 - 1) * 0.5))
                
                if kelly_position > 0:
                    investment_amount = self.current_capital * kelly_position
                    
                    if investment_amount >= self.strategy_params['transaction_cost'] * 2:
                        shares = (investment_amount - self.strategy_params['transaction_cost']) / price
                        
                        if signal == 'GAME_BUY':
                            direction = 'LONG'
                            action = '博弈买涨'
                        else:
                            direction = 'SHORT'
                            action = '博弈买跌'
                        
                        position = {
                            'entry_date': date,
                            'entry_price': price,
                            'shares': shares,
                            'direction': direction,
                            'investment': investment_amount,
                            'expected_value': expected_value,
                            'win_probability': win_probability
                        }
                        
                        self.current_positions.append(position)
                        self.last_trade_date = date
                    else:
                        action = '资金不足'
                        minimal_hold_days += 1
                else:
                    action = '凯利系数不足'
                    minimal_hold_days += 1
            else:
                action = '仓位已满'
                minimal_hold_days += 1
            
            # 记录每日数据
            position_value = 0
            for position in self.current_positions:
                if position['direction'] == 'LONG':
                    unrealized_profit = (price - position['entry_price']) * position['shares']
                else:
                    unrealized_profit = (position['entry_price'] - price) * position['shares']
                position_value += position['investment'] + unrealized_profit
            
            total_value = self.current_capital + position_value
            
            self.daily_portfolio.append({
                'date': date.strftime('%Y-%m-%d'),
                'price': price,
                'total_value': total_value,
                'action': action,
                'expected_value': expected_value,
                'win_probability': win_probability
            })
        
        print(f"\n✅ 宽松博弈论策略回测完成!")
        print(f"📊 总交易次数: {total_trades}")
        print(f"📈 盈利次数: {winning_trades}")
        print(f"⏸️ 很少持仓天数: {minimal_hold_days}")
        print(f"💰 总投入资金: {self.total_invested:,} 港币")
        if total_trades > 0:
            print(f"🎯 胜率: {winning_trades/total_trades*100:.1f}%")
        
        return (pd.DataFrame(self.trades), 
                pd.DataFrame(self.monthly_investments), 
                pd.DataFrame(self.daily_portfolio),
                pd.DataFrame(self.game_theory_signals))

def main():
    """主函数"""
    print("🏢 HSI50宽松博弈论策略回测系统")
    print("=" * 50)
    print("💰 初始资金: 30,000港元")
    print("📅 每月定投: 3,000港元")
    print("🎯 宽松策略参数:")
    print("   • 期望值阈值: 5%")
    print("   • 胜率阈值: 60%")
    print("   • 偏离阈值: 8%")
    print("   • 止盈: 15%, 止损: 6%")
    
    backtester = HSI50GameTheoryRelaxed()
    
    if not backtester.fetch_hsi_data():
        return
    
    backtester.calculate_indicators()
    trades_df, monthly_df, daily_df, signals_df = backtester.backtest_relaxed_strategy()
    
    # 简单分析
    if len(trades_df) > 0:
        final_value = daily_df['total_value'].iloc[-1]
        total_invested = backtester.total_invested
        net_return = final_value - total_invested
        net_return_rate = (net_return / total_invested) * 100
        
        print(f"\n📊 最终结果:")
        print(f"• 总投入: {total_invested:,} 港元")
        print(f"• 最终价值: {final_value:,.0f} 港元")
        print(f"• 净收益: {net_return:,.0f} 港元")
        print(f"• 净收益率: {net_return_rate:.2f}%")
        
        if len(trades_df) > 0:
            avg_expected_value = trades_df['expected_value'].mean()
            avg_win_prob = trades_df['win_probability'].mean()
            print(f"• 平均期望值: {avg_expected_value:.3f}")
            print(f"• 平均预测胜率: {avg_win_prob:.3f}")

if __name__ == "__main__":
    main()
