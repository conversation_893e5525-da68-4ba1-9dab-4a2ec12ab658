#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
检查stock_0002_hk表的完整结构
============================

检查MySQL数据库中stock_0002_hk表的所有字段
确保包含MoneyFlowRatio等必要字段

作者: Cosmoon NG
日期: 2025年7月24日
"""

import mysql.connector

def check_table_structure():
    """检查表结构"""
    print("🔍 检查stock_0002_hk表的完整结构")
    print("=" * 50)
    
    # 数据库配置
    db_config = {
        'host': 'localhost',
        'user': 'root',
        'password': '12345678',
        'database': 'finance',
        'charset': 'utf8mb4'
    }
    
    try:
        # 连接数据库
        connection = mysql.connector.connect(**db_config)
        cursor = connection.cursor()
        print("✅ 数据库连接成功")
        
        # 1. 检查表是否存在
        cursor.execute("SHOW TABLES LIKE 'stock_0002_hk'")
        result = cursor.fetchone()
        
        if not result:
            print("❌ 表 stock_0002_hk 不存在")
            return
        
        print("✅ 找到表: stock_0002_hk")
        
        # 2. 获取完整表结构
        cursor.execute("DESCRIBE stock_0002_hk")
        columns = cursor.fetchall()
        
        print(f"\n📋 表结构 (共{len(columns)}个字段):")
        print("字段名                | 类型                    | 空值 | 键   | 默认值")
        print("-" * 80)
        
        field_names = []
        for col in columns:
            field_name = col[0]
            field_type = col[1]
            null_allowed = col[2]
            key_info = col[3]
            default_val = col[4] if col[4] is not None else "NULL"
            
            field_names.append(field_name)
            print(f"{field_name:<20} | {field_type:<22} | {null_allowed:<4} | {key_info:<4} | {default_val}")
        
        # 3. 检查关键字段是否存在
        required_fields = [
            'MoneyFlowRatio', 'MoneyFlowRaito',  # 可能的拼写
            'y_probability', 'inflow_ratio', 'Full_Y',
            'midprice', 'Controller', 'E'
        ]
        
        print(f"\n🔍 关键字段检查:")
        missing_fields = []
        existing_fields = []
        
        for field in required_fields:
            if field in field_names:
                print(f"   ✅ {field} - 存在")
                existing_fields.append(field)
            else:
                print(f"   ❌ {field} - 缺失")
                missing_fields.append(field)
        
        # 4. 查看样本数据
        print(f"\n📊 样本数据 (最新5条记录):")
        
        # 构建查询语句，只选择存在的字段
        sample_fields = ['date', 'close']
        for field in ['y_probability', 'inflow_ratio', 'Full_Y', 'MoneyFlowRatio', 'MoneyFlowRaito', 'E']:
            if field in field_names:
                sample_fields.append(field)
        
        query = f"SELECT {', '.join(sample_fields)} FROM stock_0002_hk ORDER BY date DESC LIMIT 5"
        cursor.execute(query)
        
        sample_data = cursor.fetchall()
        
        # 打印表头
        header = " | ".join([f"{field:<12}" for field in sample_fields])
        print(header)
        print("-" * len(header))
        
        # 打印数据
        for row in sample_data:
            row_str = " | ".join([f"{str(val):<12}" for val in row])
            print(row_str)
        
        # 5. 检查记录数和日期范围
        cursor.execute("SELECT COUNT(*), MIN(date), MAX(date) FROM stock_0002_hk")
        stats = cursor.fetchone()
        
        print(f"\n📈 数据统计:")
        print(f"   总记录数: {stats[0]}")
        print(f"   日期范围: {stats[1]} 至 {stats[2]}")
        
        # 6. 建议修复方案
        if missing_fields:
            print(f"\n💡 修复建议:")
            if 'MoneyFlowRatio' in missing_fields and 'MoneyFlowRaito' in missing_fields:
                print("   需要添加 MoneyFlowRatio 字段")
            
            print("   可以通过以下方式修复:")
            print("   1. 添加缺失字段到表结构")
            print("   2. 重新计算并更新所有技术指标")
            print("   3. 确保字段名称拼写正确")
        
        cursor.close()
        connection.close()
        
        return field_names, missing_fields, existing_fields
        
    except mysql.connector.Error as e:
        print(f"❌ 数据库操作失败: {e}")
        return None, None, None
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return None, None, None

def suggest_alter_table(missing_fields):
    """建议ALTER TABLE语句"""
    if not missing_fields:
        return
    
    print(f"\n🔧 建议的ALTER TABLE语句:")
    print("=" * 40)
    
    alter_statements = []
    
    if 'MoneyFlowRatio' in missing_fields:
        alter_statements.append("ADD COLUMN MoneyFlowRatio DECIMAL(8,6) DEFAULT NULL COMMENT '资金流比率'")
    
    if 'MoneyFlowRaito' in missing_fields:  # 如果是拼写错误的版本
        alter_statements.append("ADD COLUMN MoneyFlowRaito DECIMAL(8,6) DEFAULT NULL COMMENT '资金流比率(拼写错误版本)'")
    
    for statement in alter_statements:
        print(f"ALTER TABLE stock_0002_hk {statement};")

if __name__ == "__main__":
    field_names, missing_fields, existing_fields = check_table_structure()
    
    if missing_fields:
        suggest_alter_table(missing_fields)
        
    print(f"\n✅ 表结构检查完成")
