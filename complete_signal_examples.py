#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整信号示例 - 看涨和看跌的完整分析
展示东亚银行(0023.HK)看涨和看跌的具体入场价位和止盈止损
"""

import yfinance as yf
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

class CompleteSignalAnalyzer:
    def __init__(self):
        """初始化完整信号分析器"""
        self.ticker = '0023.HK'
        
        # 策略参数
        self.x_threshold_long = 0.45    # 买涨X阈值
        self.y_threshold_long = 0.45    # 买涨Y阈值
        self.x_threshold_short = 0.25   # 买跌X阈值
        self.y_threshold_short = 0.25   # 买跌Y阈值
        
        # 止盈止损参数（不对称设计）
        self.take_profit_long = 0.012   # 多头止盈 1.2%
        self.stop_loss_long = 0.008     # 多头止损 0.8%
        self.take_profit_short = 0.008  # 空头止盈 0.8%
        self.stop_loss_short = 0.012    # 空头止损 1.2%

    def show_complete_examples(self):
        """显示完整的看涨和看跌示例"""
        print("🏦 东亚银行(0023.HK) 完整信号分析")
        print("=" * 70)
        
        # 获取当前真实数据
        current_data = self.get_current_data()
        if current_data is not None:
            print("📊 当前真实市场状态:")
            self.analyze_current_signal(current_data)
        
        print("\n" + "=" * 70)
        print("📚 完整信号示例说明")
        print("=" * 70)
        
        # 示例1：看涨信号
        self.show_bullish_example()
        
        print("\n" + "-" * 70)
        
        # 示例2：看跌信号
        self.show_bearish_example()
        
        print("\n" + "-" * 70)
        
        # 示例3：观望信号
        self.show_wait_example()

    def get_current_data(self):
        """获取当前真实数据"""
        try:
            end_date = datetime.now()
            start_date = end_date - timedelta(days=100)
            data = yf.download(self.ticker, start=start_date, end=end_date, progress=False)
            
            if data.empty:
                return None
            
            # 处理数据
            if isinstance(data.columns, pd.MultiIndex):
                data.columns = [col[0] for col in data.columns]
            
            data = data.rename(columns={
                'Open': 'open', 'High': 'high', 'Low': 'low', 
                'Close': 'close', 'Volume': 'volume'
            })
            
            # 计算指标
            data = self.calculate_indicators(data)
            return data.iloc[-1]
            
        except Exception as e:
            print(f"❌ 数据获取失败: {e}")
            return None

    def calculate_indicators(self, df):
        """计算技术指标"""
        # 回归线
        window = 60
        df['regression_line'] = df['close'].rolling(window=window).apply(
            lambda x: np.polyval(np.polyfit(range(len(x)), x, 1), len(x)-1), raw=False
        )
        
        # RSI
        delta = df['close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
        rs = gain / loss
        df['rsi'] = 100 - (100 / (1 + rs))
        
        # 资金流比例 (X值)
        price_change = (df['close'] - df['open']) / df['open']
        money_flow = df['volume'] * price_change
        
        def calc_inflow_ratio(flows):
            if len(flows) == 0 or flows.isna().all():
                return 0.5
            flows = flows.dropna()
            if len(flows) == 0:
                return 0.5
            
            inflows = flows[flows > 0].sum()
            outflows = abs(flows[flows < 0].sum())
            total_flow = inflows + outflows
            
            return inflows / total_flow if total_flow > 0 else 0.5
        
        df['money_flow_ratio'] = money_flow.rolling(window=20).apply(calc_inflow_ratio, raw=False)
        df['money_flow_ratio'] = np.clip(df['money_flow_ratio'], 0.1, 0.9).fillna(0.5)
        
        # Full_Y值 (控制系数)
        price_momentum = df['close'].pct_change(10)
        df['full_y'] = (df['rsi'] / 100 + np.tanh(price_momentum * 5) + 1) / 2
        df['full_y'] = np.clip(df['full_y'], 0.1, 0.9)
        
        # E值
        df['e_value'] = 8 * df['money_flow_ratio'] * df['full_y'] - 3 * df['money_flow_ratio'] - 3 * df['full_y'] + 1
        
        # 价格位置
        df['price_position'] = (df['close'] - df['regression_line']) / df['regression_line']
        
        return df

    def analyze_current_signal(self, data):
        """分析当前真实信号"""
        current_price = data['close']
        x_val = data['money_flow_ratio']
        y_val = data['full_y']
        e_val = data['e_value']
        price_pos = data['price_position']
        regression_price = data['regression_line']
        
        print(f"💰 当前价格: {current_price:.2f} 港币")
        print(f"📊 回归线价格: {regression_price:.2f} 港币")
        print(f"🔢 X值: {x_val:.3f} | Y值: {y_val:.3f} | E值: {e_val:.3f}")
        print(f"📍 价格位置: {price_pos*100:.1f}% ({'高于' if price_pos > 0 else '低于'}回归线)")
        
        # 判断信号
        long_condition = (e_val > 0 and x_val > self.x_threshold_long and 
                         y_val > self.y_threshold_long and price_pos < 0)
        short_condition = ((y_val < self.y_threshold_short or x_val < self.x_threshold_short) and 
                          price_pos > 0)
        
        if long_condition:
            print("🟢 当前信号: 看涨 - 买入做多")
            self.show_long_prices(current_price)
        elif short_condition:
            print("🔴 当前信号: 看跌 - 买入做空")
            self.show_short_prices(current_price)
        else:
            print("⚪ 当前信号: 观望 - 等待机会")

    def show_bullish_example(self):
        """显示看涨信号示例"""
        print("🟢 看涨信号示例 - 买入做多")
        print("=" * 50)
        
        # 模拟看涨条件的数据
        example_price = 12.50
        example_regression = 12.65
        
        print("📋 看涨条件:")
        print("  ✅ E值 > 0 (例如: 0.8)")
        print("  ✅ X值 > 0.45 (例如: 0.65)")
        print("  ✅ Y值 > 0.45 (例如: 0.70)")
        print("  ✅ 价格低于回归线 (例如: -1.2%)")
        
        print(f"\n💰 价格设置示例:")
        print(f"  📊 回归线价格: {example_regression:.2f} 港币")
        print(f"  📍 当前价格: {example_price:.2f} 港币 (低于回归线)")
        
        self.show_long_prices(example_price)
        
        print(f"\n🎯 操作逻辑:")
        print(f"  • 价格被低估，资金流入强劲")
        print(f"  • 控制系数高，市场情绪积极")
        print(f"  • 博弈论期望值为正，胜率较高")

    def show_bearish_example(self):
        """显示看跌信号示例"""
        print("🔴 看跌信号示例 - 买入做空")
        print("=" * 50)
        
        # 模拟看跌条件的数据
        example_price = 12.80
        example_regression = 12.60
        
        print("📋 看跌条件:")
        print("  ✅ Y值 < 0.25 或 X值 < 0.25 (例如: Y=0.20, X=0.30)")
        print("  ✅ 价格高于回归线 (例如: +1.6%)")
        
        print(f"\n💰 价格设置示例:")
        print(f"  📊 回归线价格: {example_regression:.2f} 港币")
        print(f"  📍 当前价格: {example_price:.2f} 港币 (高于回归线)")
        
        self.show_short_prices(example_price)
        
        print(f"\n🎯 操作逻辑:")
        print(f"  • 价格被高估，资金流出或控制力弱")
        print(f"  • 市场情绪转弱，回调概率高")
        print(f"  • 适合做空获利")

    def show_wait_example(self):
        """显示观望信号示例"""
        print("⚪ 观望信号示例 - 等待机会")
        print("=" * 50)
        
        print("📋 观望条件:")
        print("  ❌ 不满足看涨条件 (E值≤0 或 X值≤0.45 或 Y值≤0.45 或 价格高于回归线)")
        print("  ❌ 不满足看跌条件 (Y值≥0.25 且 X值≥0.25 或 价格低于回归线)")
        
        print(f"\n💰 价格设置示例:")
        print(f"  📊 回归线价格: 12.60 港币")
        print(f"  📍 当前价格: 12.58 港币 (接近回归线)")
        print(f"  🔢 指标: X=0.40, Y=0.35, E=-0.1")
        
        print(f"\n🎯 等待策略:")
        print(f"  • 设置价格提醒，关注突破")
        print(f"  • 等待指标改善或价格偏离")
        print(f"  • 保持耐心，不强行交易")

    def show_long_prices(self, entry_price):
        """显示看涨价格设置"""
        stop_loss_price = entry_price * (1 - self.stop_loss_long)
        take_profit_price = entry_price * (1 + self.take_profit_long)
        
        print(f"  📍 建议入场价: {entry_price:.2f} 港币")
        print(f"  🛑 止损价位: {stop_loss_price:.2f} 港币 (下跌{self.stop_loss_long*100:.1f}%)")
        print(f"  🎯 止盈价位: {take_profit_price:.2f} 港币 (上涨{self.take_profit_long*100:.1f}%)")
        
        risk = entry_price - stop_loss_price
        reward = take_profit_price - entry_price
        
        print(f"  📊 风险: {risk:.2f} 港币 | 收益: {reward:.2f} 港币")
        print(f"  📈 风险收益比: 1:{(reward/risk):.1f}")

    def show_short_prices(self, entry_price):
        """显示看跌价格设置"""
        stop_loss_price = entry_price * (1 + self.stop_loss_short)
        take_profit_price = entry_price * (1 - self.take_profit_short)
        
        print(f"  📍 建议入场价: {entry_price:.2f} 港币")
        print(f"  🛑 止损价位: {stop_loss_price:.2f} 港币 (上涨{self.stop_loss_short*100:.1f}%)")
        print(f"  🎯 止盈价位: {take_profit_price:.2f} 港币 (下跌{self.take_profit_short*100:.1f}%)")
        
        risk = stop_loss_price - entry_price
        reward = entry_price - take_profit_price
        
        print(f"  📊 风险: {risk:.2f} 港币 | 收益: {reward:.2f} 港币")
        print(f"  📈 风险收益比: 1:{(reward/risk):.1f}")

def main():
    """主函数"""
    analyzer = CompleteSignalAnalyzer()
    analyzer.show_complete_examples()
    
    print(f"\n" + "=" * 70)
    print("💡 总结:")
    print("  🟢 看涨: E>0 + X>0.45 + Y>0.45 + 价格低于回归线")
    print("  🔴 看跌: (Y<0.25 或 X<0.25) + 价格高于回归线")
    print("  ⚪ 观望: 不满足以上条件时")
    print("\n⚠️ 风险提醒: 严格执行止盈止损，控制风险！")

if __name__ == "__main__":
    main()
