#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
将凯利公式策略结果保存到数据库
============================

功能：
1. 重新运行凯利公式策略
2. 将每条记录详细保存到数据库
3. 创建kelly_results表存储结果
4. 显示所有记录的详细信息

作者: Cosmoon NG
日期: 2025年7月
"""

import pymysql
import pandas as pd
import numpy as np
from datetime import datetime

class KellyResultsDatabaseSaver:
    def __init__(self):
        """初始化凯利结果数据库保存器"""
        self.db_config = {
            'host': '************',
            'port': 3306,
            'database': 'finance',
            'user': 'root',
            'password': '',
            'charset': 'utf8mb4'
        }
        self.connection = None
        self.initial_capital = 30000
        
        # 凯利公式策略参数
        self.strategy_params = {
            'take_profit': 0.02,    # 2%止盈
            'stop_loss': 0.01,      # 1%止损
            'odds_ratio': 2.0,      # 赔率1:2
            'high_profit_y': 0.43,
            'high_profit_x': 0.43,
            'control_zone_min': 0.333,
            'control_zone_max': 0.4,
            'strong_loss_y': 0.25,
            'strong_loss_x': 0.25,
            'transaction_cost': 0.0025,
            'max_position_ratio': 0.25,
        }
        
    def connect_database(self):
        """连接数据库"""
        try:
            self.connection = pymysql.connect(**self.db_config)
            print(f"✅ 成功连接MySQL数据库: {self.db_config['host']}")
            return True
        except Exception as e:
            print(f"❌ 连接数据库失败: {e}")
            return False
    
    def create_kelly_results_table(self):
        """创建凯利结果表"""
        try:
            cursor = self.connection.cursor()
            
            # 删除现有表
            cursor.execute("DROP TABLE IF EXISTS kelly_results")
            
            # 创建新表
            create_table_sql = """
                CREATE TABLE kelly_results (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    交易序号 INT,
                    开仓日期 DATE,
                    开仓价格 DECIMAL(10,2),
                    平仓价格 DECIMAL(10,2),
                    Y值_控制系数 DECIMAL(8,6),
                    X值_资金流比例 DECIMAL(8,6),
                    E值 DECIMAL(10,6),
                    凯利策略区域 VARCHAR(20),
                    凯利交易方向 VARCHAR(10),
                    凯利仓位比例 DECIMAL(8,4),
                    交易股数 INT,
                    交易金额 DECIMAL(15,2),
                    收益率_百分比 DECIMAL(8,4),
                    净利润 DECIMAL(15,2),
                    平仓原因 VARCHAR(20),
                    账户余额 DECIMAL(15,2),
                    真实流入 DECIMAL(15,2),
                    真实流出 DECIMAL(15,2),
                    凯利交易理由 TEXT,
                    止盈参数 VARCHAR(10),
                    止损参数 VARCHAR(10),
                    赔率 VARCHAR(10),
                    创建时间 TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    INDEX idx_trade_no (交易序号),
                    INDEX idx_date (开仓日期),
                    INDEX idx_zone (凯利策略区域),
                    INDEX idx_direction (凯利交易方向)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='凯利公式策略结果表'
            """
            
            cursor.execute(create_table_sql)
            self.connection.commit()
            
            print("✅ 成功创建kelly_results表")
            return True
            
        except Exception as e:
            print(f"❌ 创建kelly_results表失败: {e}")
            return False
    
    def load_test_data_and_calculate_kelly(self):
        """加载test数据并计算凯利参数"""
        try:
            cursor = self.connection.cursor()
            cursor.execute("""
                SELECT 交易序号, 开仓日期, 平仓日期, 持仓天数, 交易方向, 
                       close, 平仓价格, 交易股数, 交易金额, 净利润, `收益率%`,
                       `控制系数`, `资金流比例`, E值, 策略区域, 交易理由, 平仓原因,
                       `真实流入`, `真实流出`
                FROM test 
                ORDER BY 交易序号
                LIMIT 100
            """)
            
            data = cursor.fetchall()
            columns = ['交易序号', '开仓日期', '平仓日期', '持仓天数', '交易方向', 
                      '开仓价格', '平仓价格', '交易股数', '交易金额', '净利润', '收益率',
                      'Y值', 'X值', 'E值', '策略区域', '交易理由', '平仓原因',
                      '真实流入', '真实流出']
            
            df = pd.DataFrame(data, columns=columns)
            
            # 计算凯利参数
            total_trades = len(df[df['净利润'] != 0])
            winning_trades = len(df[df['净利润'] > 0])
            
            if total_trades > 0:
                p = winning_trades / total_trades
                q = 1 - p
                b = self.strategy_params['odds_ratio']
                kelly_f = (b * p - q) / b
                optimal_position = min(kelly_f, self.strategy_params['max_position_ratio']) if kelly_f > 0 else 0
                
                kelly_params = {
                    'win_rate': p,
                    'lose_rate': q,
                    'kelly_f': kelly_f,
                    'optimal_position': optimal_position
                }
                
                print(f"📊 凯利公式参数:")
                print(f"   • 胜率: {p:.3f}")
                print(f"   • 凯利系数: {kelly_f:.3f}")
                print(f"   • 最优仓位: {optimal_position*100:.1f}%")
                
                return df, kelly_params
            else:
                return None, None
                
        except Exception as e:
            print(f"❌ 加载数据失败: {e}")
            return None, None
    
    def run_kelly_strategy_and_save(self, df, kelly_params):
        """运行凯利策略并保存到数据库"""
        try:
            print("🎯 运行凯利策略并保存每条记录到数据库...")
            
            if kelly_params['optimal_position'] <= 0:
                print("❌ 凯利系数为负，不进行交易")
                return False
            
            # 分类策略区域
            conditions = [
                (df['Y值'] > self.strategy_params['high_profit_y']) & 
                (df['X值'] > self.strategy_params['high_profit_x']),
                (df['Y值'] > self.strategy_params['control_zone_min']) & 
                (df['Y值'] < self.strategy_params['control_zone_max']),
                (df['Y值'] < self.strategy_params['strong_loss_y']) | 
                (df['X值'] < self.strategy_params['strong_loss_x']),
            ]
            choices = ['高值盈利区', '控股商控制区', '强亏损区']
            df['凯利策略区域'] = np.select(conditions, choices, default='其他区域')
            
            current_cash = self.initial_capital
            cursor = self.connection.cursor()
            
            for _, row in df.iterrows():
                trade_id = row['交易序号']
                open_date = row['开仓日期']
                open_price = row['开仓价格']
                close_price = row['平仓价格']
                y_val = row['Y值']
                x_val = row['X值']
                e_val = row['E值']
                zone = row['凯利策略区域']
                real_flow_in = row['真实流入']
                real_flow_out = row['真实流出']
                
                # 确定策略
                if zone == '控股商控制区':
                    action = '观望'
                    direction = '观望'
                    reason = f'控股商控制区：控制系数Y={y_val:.3f}在0.333-0.4之间，凯利公式建议观望'
                elif zone == '高值盈利区':
                    action = '买入'
                    direction = '做多'
                    reason = f'高值盈利区：控制系数Y={y_val:.3f}>0.43且资金流比例X={x_val:.3f}>0.43，凯利公式做多'
                elif zone == '强亏损区':
                    action = '卖出'
                    direction = '做空'
                    reason = f'强亏损区：控制系数Y={y_val:.3f}<0.25或资金流比例X={x_val:.3f}<0.25，凯利公式做空'
                else:
                    action = '卖出'
                    direction = '做空'
                    reason = f'其他区域：控制系数Y={y_val:.3f}，资金流比例X={x_val:.3f}，凯利公式做空'
                
                # 计算交易结果
                if action == '观望':
                    net_profit = 0
                    profit_pct = 0
                    exit_reason = '观望'
                    actual_value = 0
                    shares = 0
                    kelly_position = 0
                else:
                    kelly_position = kelly_params['optimal_position']
                    position_value = current_cash * kelly_position
                    shares = int(position_value / open_price / 100) * 100
                    actual_value = shares * open_price
                    
                    if shares >= 100 and current_cash > actual_value:
                        # 严格按照止盈止损计算
                        if direction == '做多':
                            profit_pct = (close_price - open_price) / open_price
                            if profit_pct >= self.strategy_params['take_profit']:
                                exit_reason = '止盈'
                                profit_pct = self.strategy_params['take_profit']
                            elif profit_pct <= -self.strategy_params['stop_loss']:
                                exit_reason = '止损'
                                profit_pct = -self.strategy_params['stop_loss']
                            else:
                                exit_reason = '到期平仓'
                        else:  # 做空
                            profit_pct = (open_price - close_price) / open_price
                            if profit_pct >= self.strategy_params['take_profit']:
                                exit_reason = '止盈'
                                profit_pct = self.strategy_params['take_profit']
                            elif profit_pct <= -self.strategy_params['stop_loss']:
                                exit_reason = '止损'
                                profit_pct = -self.strategy_params['stop_loss']
                            else:
                                exit_reason = '到期平仓'
                        
                        gross_profit = profit_pct * actual_value
                        transaction_cost = actual_value * self.strategy_params['transaction_cost'] * 2
                        net_profit = gross_profit - transaction_cost
                        current_cash += net_profit
                    else:
                        net_profit = 0
                        profit_pct = 0
                        exit_reason = '资金不足'
                        actual_value = 0
                        shares = 0
                        kelly_position = 0
                
                # 插入数据库
                insert_sql = """
                    INSERT INTO kelly_results (
                        交易序号, 开仓日期, 开仓价格, 平仓价格, Y值_控制系数, X值_资金流比例, E值,
                        凯利策略区域, 凯利交易方向, 凯利仓位比例, 交易股数, 交易金额,
                        收益率_百分比, 净利润, 平仓原因, 账户余额, 真实流入, 真实流出,
                        凯利交易理由, 止盈参数, 止损参数, 赔率
                    ) VALUES (
                        %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s
                    )
                """
                
                cursor.execute(insert_sql, (
                    trade_id, open_date, round(open_price, 2), round(close_price, 2),
                    round(y_val, 6), round(x_val, 6), round(e_val, 6),
                    zone, direction, round(kelly_position, 4), shares, round(actual_value, 0),
                    round(profit_pct * 100, 4), round(net_profit, 0), exit_reason, round(current_cash, 0),
                    round(real_flow_in, 0), round(real_flow_out, 0), reason,
                    f"+{self.strategy_params['take_profit']*100}%",
                    f"-{self.strategy_params['stop_loss']*100}%",
                    f"1:{self.strategy_params['odds_ratio']}"
                ))
            
            self.connection.commit()
            print(f"✅ 成功保存100条凯利策略记录到数据库")
            
            # 显示汇总信息
            print(f"💰 最终资金: {current_cash:,.0f}港币")
            print(f"📈 总收益: {current_cash - self.initial_capital:+,.0f}港币")
            print(f"📊 总收益率: {(current_cash / self.initial_capital - 1) * 100:+.2f}%")
            
            return True
            
        except Exception as e:
            print(f"❌ 运行凯利策略并保存失败: {e}")
            return False
    
    def display_all_records(self):
        """显示所有记录"""
        try:
            cursor = self.connection.cursor()
            
            # 查询所有记录
            cursor.execute("""
                SELECT 交易序号, 开仓日期, 凯利策略区域, 凯利交易方向, 
                       Y值_控制系数, X值_资金流比例, E值, 凯利仓位比例,
                       交易金额, 收益率_百分比, 净利润, 平仓原因, 账户余额
                FROM kelly_results 
                ORDER BY 交易序号
            """)
            
            records = cursor.fetchall()
            
            print(f"\n📊 凯利公式策略 - 所有100条记录详细列表:")
            print("="*150)
            print(f"{'序号':<4} {'日期':<12} {'策略区域':<12} {'方向':<6} {'Y值':<8} {'X值':<8} {'E值':<10} "
                  f"{'仓位%':<8} {'金额':<10} {'收益%':<8} {'净利润':<8} {'平仓原因':<10} {'余额':<10}")
            print("-" * 150)
            
            for record in records:
                (trade_no, date, zone, direction, y_val, x_val, e_val, position, 
                 amount, return_pct, profit, exit_reason, balance) = record
                
                print(f"{trade_no:<4} {date:<12} {zone:<12} {direction:<6} {float(y_val):<8.3f} "
                      f"{float(x_val):<8.3f} {float(e_val):<10.3f} {float(position)*100:<7.1f}% "
                      f"{int(amount):<10,} {float(return_pct):<7.2f}% {int(profit):<8,} "
                      f"{exit_reason:<10} {int(balance):<10,}")
            
            # 统计分析
            cursor.execute("""
                SELECT 
                    COUNT(*) as total,
                    SUM(CASE WHEN 凯利交易方向 != '观望' THEN 1 ELSE 0 END) as actual_trades,
                    SUM(CASE WHEN 净利润 > 0 THEN 1 ELSE 0 END) as winning,
                    SUM(CASE WHEN 净利润 < 0 THEN 1 ELSE 0 END) as losing,
                    SUM(净利润) as total_profit,
                    AVG(CASE WHEN 凯利交易方向 != '观望' THEN 收益率_百分比 END) as avg_return,
                    MAX(账户余额) as final_balance
                FROM kelly_results
            """)
            
            stats = cursor.fetchone()
            total, actual_trades, winning, losing, total_profit, avg_return, final_balance = stats
            
            print(f"\n📈 凯利策略统计汇总:")
            print("-" * 60)
            print(f"• 总记录数: {total}")
            print(f"• 实际交易: {actual_trades}")
            print(f"• 观望次数: {total - actual_trades}")
            print(f"• 盈利次数: {winning}")
            print(f"• 亏损次数: {losing}")
            print(f"• 胜率: {winning/actual_trades*100:.1f}%" if actual_trades > 0 else "• 胜率: N/A")
            print(f"• 总盈亏: {int(total_profit):+,}港币")
            print(f"• 平均收益率: {float(avg_return):.2f}%" if avg_return else "• 平均收益率: N/A")
            print(f"• 最终资金: {int(final_balance):,}港币")
            print(f"• 总收益率: {(final_balance/self.initial_capital-1)*100:+.2f}%")
            
            # 按策略区域统计
            cursor.execute("""
                SELECT 凯利策略区域, 
                       COUNT(*) as count,
                       SUM(净利润) as total_profit,
                       AVG(净利润) as avg_profit,
                       SUM(CASE WHEN 净利润 > 0 THEN 1 ELSE 0 END) as wins
                FROM kelly_results 
                GROUP BY 凯利策略区域
                ORDER BY total_profit DESC
            """)
            
            zone_stats = cursor.fetchall()
            
            print(f"\n📊 按策略区域统计:")
            print("-" * 70)
            for zone, count, total_profit, avg_profit, wins in zone_stats:
                win_rate = wins/count*100 if count > 0 else 0
                print(f"• {zone}: {count}次, 总盈亏{int(total_profit):+,}港币, "
                      f"平均{int(avg_profit):+}港币, 胜率{win_rate:.1f}%")
            
            return True
            
        except Exception as e:
            print(f"❌ 显示记录失败: {e}")
            return False
    
    def close_connection(self):
        """关闭数据库连接"""
        if self.connection:
            self.connection.close()
            print("\n🔒 数据库连接已关闭")

def main():
    """主函数"""
    print("🏦 将凯利公式策略结果保存到数据库")
    print("="*60)
    
    # 创建保存器
    saver = KellyResultsDatabaseSaver()
    
    # 连接数据库
    if not saver.connect_database():
        return
    
    # 创建结果表
    if not saver.create_kelly_results_table():
        saver.close_connection()
        return
    
    # 加载数据并计算凯利参数
    df, kelly_params = saver.load_test_data_and_calculate_kelly()
    if df is None or kelly_params is None:
        saver.close_connection()
        return
    
    # 运行策略并保存
    if not saver.run_kelly_strategy_and_save(df, kelly_params):
        saver.close_connection()
        return
    
    # 显示所有记录
    saver.display_all_records()
    
    # 关闭连接
    saver.close_connection()
    
    print(f"\n🎉 凯利公式策略结果已保存到kelly_results表!")
    print("📊 可以通过SQL查询详细分析每条记录")

if __name__ == "__main__":
    main()
