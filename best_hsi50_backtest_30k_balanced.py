#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
HSI50平衡策略回测系统 - 30K资金20年历史 (平衡版)
===============================================

平衡风险控制与交易机会：
- 适中的信号过滤条件
- 合理的止盈止损比例
- 优化的仓位管理
- 持仓不动策略优先

作者: Cosmoon NG
日期: 2025年7月
"""

import pandas as pd
import yfinance as yf
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

class BalancedHSI50Backtest:
    def __init__(self):
        """初始化平衡HSI50回测系统"""
        self.symbol = "^HSI"  # 恒生指数
        self.initial_capital = 30000  # 30K港币
        self.data = None
        
        # 平衡策略参数
        self.strategy_params = {
            # 区域定义 - 平衡版本
            'high_profit_y': 0.5,            # 适中的高值盈利区阈值
            'high_profit_x': 0.5,            # 适中的高值盈利区阈值
            'control_zone_min': 0.333,       # 控股商控制区
            'control_zone_max': 0.4,         # 控股商控制区
            'strong_loss_y': 0.25,           # 强亏损区阈值
            'strong_loss_x': 0.25,           # 强亏损区阈值
            
            # 凯利公式参数
            'kelly_win_rate': 0.5,           # 平衡的胜率预期
            'kelly_win_ratio': 2,            # 1:2赔率
            'kelly_loss_ratio': 1,           # 亏损倍数
            'max_position_ratio': 0.2,       # 最大单次仓位20%
            'max_total_positions': 2,        # 最多同时持有2个仓位
            
            # 止盈止损参数
            'take_profit': 0.02,             # 止盈2%
            'stop_loss': 0.01,               # 止损1%
            
            'transaction_cost': 30,          # 每笔交易成本30港币
            'point_value': 50,               # 每个恒指点子价值50港元
            'min_holding_days': 2,           # 最少持有2天
            'max_holding_days': 20,          # 最多持有20天
            
            # 持仓不动策略优先
            'hold_preference': True,         # 优先持仓不动
            'hold_threshold': 0.005,         # 小幅波动持仓阈值
        }
        
        self.trades = []
        self.daily_portfolio = []
        self.current_positions = []
        self.hold_periods = []  # 记录持仓不动期间
    
    def fetch_hsi_data(self):
        """获取恒生指数20年历史数据"""
        print("📈 获取恒生指数20年历史数据...")
        
        try:
            # 获取20年数据
            end_date = datetime.now()
            start_date = end_date - timedelta(days=20*365)
            
            ticker = yf.Ticker(self.symbol)
            self.data = ticker.history(start=start_date, end=end_date)
            
            if self.data.empty:
                print("❌ 数据获取失败：返回空数据")
                return False
            
            self.data.reset_index(inplace=True)
            self.data.columns = [col.lower() for col in self.data.columns]
            
            print(f"✅ 成功获取恒生指数数据:")
            print(f"   • 数据期间: {self.data['date'].min().strftime('%Y-%m-%d')} 至 {self.data['date'].max().strftime('%Y-%m-%d')}")
            print(f"   • 总记录数: {len(self.data):,} 天")
            
            return True
            
        except Exception as e:
            print(f"❌ 获取数据失败: {e}")
            return False
    
    def calculate_indicators(self):
        """计算技术指标和X、Y值"""
        print("📊 计算技术指标和平衡策略参数...")
        
        # 移动平均线
        self.data['ma_20'] = self.data['close'].rolling(window=20).mean()
        self.data['ma_60'] = self.data['close'].rolling(window=60).mean()
        
        # RSI
        delta = self.data['close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
        rs = gain / loss
        self.data['rsi'] = 100 - (100 / (1 + rs))
        
        # 成交量指标
        self.data['volume_ma'] = self.data['volume'].rolling(window=20).mean()
        self.data['volume_ratio'] = self.data['volume'] / self.data['volume_ma']
        
        # Y值计算 (控制系数)
        price_vs_ma20 = self.data['close'] / self.data['ma_20']
        base_y = np.where(price_vs_ma20 >= 1, 
                         0.5 + 0.4 * np.tanh((price_vs_ma20 - 1) * 3),
                         0.5 - 0.4 * np.tanh((1 - price_vs_ma20) * 3))
        
        # 趋势调整
        ma_trend = (self.data['ma_20'] / self.data['ma_60']).fillna(1)
        trend_adjustment = 0.1 * np.tanh((ma_trend - 1) * 2)
        
        # 成交量调整
        volume_adjustment = 0.05 * np.tanh((self.data['volume_ratio'] - 1))
        
        self.data['y_probability'] = base_y + trend_adjustment + volume_adjustment
        self.data['y_probability'] = np.clip(self.data['y_probability'], 0.1, 0.9)
        self.data['y_probability'].fillna(0.5, inplace=True)
        
        # X值计算 (资金流比例)
        price_change = (self.data['close'] - self.data['open']) / self.data['open']
        money_flow = self.data['volume'] * price_change
        
        def calc_inflow_ratio(flows):
            if len(flows) == 0 or flows.isna().all():
                return 0.5
            flows = flows.dropna()
            if len(flows) == 0:
                return 0.5
            
            inflows = flows[flows > 0].sum()
            outflows = abs(flows[flows < 0].sum())
            total_flow = inflows + outflows
            
            return inflows / total_flow if total_flow > 0 else 0.5
        
        base_x = money_flow.rolling(window=20).apply(calc_inflow_ratio, raw=False)
        rsi_adjustment = 0.3 * (self.data['rsi'] / 100 - 0.5)
        
        self.data['inflow_ratio'] = base_x + rsi_adjustment
        self.data['inflow_ratio'] = np.clip(self.data['inflow_ratio'], 0.1, 0.9)
        self.data['inflow_ratio'].fillna(0.5, inplace=True)
        
        # E值计算
        self.data['e_value'] = (8 * self.data['inflow_ratio'] * self.data['y_probability'] - 
                               3 * self.data['inflow_ratio'] - 3 * self.data['y_probability'] + 1)
        
        print("✅ 平衡指标计算完成")
    
    def calculate_kelly_position(self, win_rate_modifier=1.0):
        """计算凯利公式仓位"""
        win_rate = self.strategy_params['kelly_win_rate'] * win_rate_modifier
        win_ratio = self.strategy_params['kelly_win_ratio']
        loss_ratio = self.strategy_params['kelly_loss_ratio']
        
        b = win_ratio / loss_ratio
        p = win_rate
        q = 1 - p
        
        kelly_fraction = (b * p - q) / b
        return max(0, min(kelly_fraction, self.strategy_params['max_position_ratio']))
    
    def classify_strategy_zone(self, y_val, x_val, e_val):
        """分类策略区域"""
        if (y_val > self.strategy_params['high_profit_y'] and 
            x_val > self.strategy_params['high_profit_x'] and
            e_val > 0):
            return 'HIGH_PROFIT'
        elif (self.strategy_params['control_zone_min'] < y_val < self.strategy_params['control_zone_max']):
            return 'CONTROL'
        elif (y_val < self.strategy_params['strong_loss_y'] or 
              x_val < self.strategy_params['strong_loss_x']) and e_val < 0:
            return 'STRONG_LOSS'
        else:
            return 'OTHER'
    
    def should_hold_position(self, position, current_price, profit_pct):
        """判断是否应该持仓不动"""
        if not self.strategy_params['hold_preference']:
            return False
        
        # 小幅盈利或亏损时优先持仓
        if abs(profit_pct) < self.strategy_params['hold_threshold']:
            return True
        
        # 盈利但未达到止盈点时持仓
        if 0 < profit_pct < self.strategy_params['take_profit'] * 0.8:
            return True
        
        return False
