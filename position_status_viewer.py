#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
持仓状态查看器
专门用于查看和分析当前持仓情况
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import os
import warnings
warnings.filterwarnings('ignore')

class PositionStatusViewer:
    """持仓状态查看器"""
    
    def __init__(self, excel_file="交易记录追踪0023HK.xlsx"):
        self.excel_file = excel_file
        self.symbol = "0023.HK"
        self.stock_name = "东亚银行"
        
    def load_trading_records(self):
        """加载交易记录"""
        try:
            if os.path.exists(self.excel_file):
                df = pd.read_excel(self.excel_file)
                return df
            else:
                print(f"❌ 文件不存在: {self.excel_file}")
                return None
        except Exception as e:
            print(f"❌ 加载文件失败: {e}")
            return None
    
    def analyze_current_position(self, df):
        """分析当前持仓状态"""
        if df is None or len(df) == 0:
            return None
        
        # 获取最新记录
        latest = df.iloc[-1]
        
        # 分析持仓状态
        position_info = {
            'date': latest.get('交易日期', ''),
            'trade_type': latest.get('交易类型', ''),
            'trade_direction': latest.get('交易方向', ''),
            'current_price': latest.get('交易价格', 0),
            'position_quantity': latest.get('持仓数量', 0),
            'position_cost': latest.get('持仓成本', 0),
            'current_market_value': latest.get('当前市值', 0),
            'unrealized_pnl': latest.get('浮动盈亏', 0),
            'realized_pnl': latest.get('实现盈亏', 0),
            'total_pnl': latest.get('累计盈亏', 0),
            'account_balance': latest.get('账户余额', 0),
            'total_assets': latest.get('总资产', 0),
            'cumulative_return': latest.get('累计收益率', 0),
            'y_value': latest.get('Y值', 0),
            'x_value': latest.get('X值', 0),
            'e_value': latest.get('E值', 0),
            'signal_strength': latest.get('信号强度', ''),
            'risk_level': latest.get('风险等级', ''),
            'notes': latest.get('备注', '')
        }
        
        # 计算额外的持仓指标
        if position_info['position_quantity'] > 0 and position_info['position_cost'] > 0:
            # 持仓状态
            position_info['is_holding'] = True
            position_info['position_direction'] = '多头' if '多头' in str(position_info['trade_direction']) else '空头' if '空头' in str(position_info['trade_direction']) else '未知'
            
            # 盈亏比例
            if position_info['position_cost'] > 0:
                if position_info['position_direction'] == '多头':
                    position_info['pnl_percentage'] = (position_info['current_price'] - position_info['position_cost']) / position_info['position_cost'] * 100
                else:
                    position_info['pnl_percentage'] = (position_info['position_cost'] - position_info['current_price']) / position_info['position_cost'] * 100
            else:
                position_info['pnl_percentage'] = 0
            
            # 持仓比例
            if position_info['total_assets'] > 0:
                position_info['position_ratio'] = position_info['current_market_value'] / position_info['total_assets'] * 100
            else:
                position_info['position_ratio'] = 0
                
            # 计算持仓天数（从最近的开仓记录开始）
            position_info['holding_days'] = self.calculate_holding_days(df)
            
        else:
            position_info['is_holding'] = False
            position_info['position_direction'] = '空仓'
            position_info['pnl_percentage'] = 0
            position_info['position_ratio'] = 0
            position_info['holding_days'] = 0
        
        return position_info
    
    def calculate_holding_days(self, df):
        """计算持仓天数"""
        try:
            # 从最新记录向前查找最近的开仓记录
            for i in range(len(df)-1, -1, -1):
                record = df.iloc[i]
                if record.get('交易类型') == '开仓':
                    entry_date = pd.to_datetime(record.get('交易日期'))
                    current_date = pd.to_datetime(df.iloc[-1].get('交易日期'))
                    return (current_date - entry_date).days + 1
            return 0
        except:
            return 0
    
    def get_recent_trades(self, df, days=7):
        """获取最近几天的交易记录"""
        if df is None or len(df) == 0:
            return None
        
        try:
            # 转换日期列
            df['交易日期'] = pd.to_datetime(df['交易日期'])
            
            # 获取最近N天的记录
            latest_date = df['交易日期'].max()
            start_date = latest_date - timedelta(days=days)
            
            recent_df = df[df['交易日期'] >= start_date].copy()
            recent_df = recent_df.sort_values('交易日期', ascending=False)
            
            return recent_df
        except:
            return df.tail(10)  # 如果日期处理失败，返回最后10条记录
    
    def display_position_status(self):
        """显示持仓状态"""
        print("📊 持仓状态查看器")
        print("=" * 60)
        print(f"📅 查看时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"🏦 股票代码: {self.symbol} {self.stock_name}")
        print(f"📄 数据文件: {self.excel_file}")
        
        # 加载数据
        df = self.load_trading_records()
        if df is None:
            return
        
        # 分析持仓状态
        position = self.analyze_current_position(df)
        if position is None:
            print("❌ 无法分析持仓状态")
            return
        
        print(f"\n📈 当前市场状态:")
        print(f"   最新价格: {position['current_price']:.2f} 港元")
        print(f"   Y值: {position['y_value']:.4f}")
        print(f"   X值: {position['x_value']:.4f}")
        print(f"   E值: {position['e_value']:.4f}")
        print(f"   信号强度: {position['signal_strength']}")
        print(f"   风险等级: {position['risk_level']}")
        
        print(f"\n💼 持仓状态:")
        if position['is_holding']:
            print(f"   持仓状态: ✅ 持仓中")
            print(f"   持仓方向: {position['position_direction']}")
            print(f"   持仓数量: {position['position_quantity']:,.0f} 股")
            print(f"   持仓成本: {position['position_cost']:.2f} 港元")
            print(f"   当前市值: {position['current_market_value']:,.2f} 港元")
            print(f"   持仓天数: {position['holding_days']} 天")
            print(f"   持仓比例: {position['position_ratio']:.2f}%")
            
            # 盈亏状态
            pnl_icon = "📈" if position['pnl_percentage'] > 0 else "📉" if position['pnl_percentage'] < 0 else "➖"
            print(f"   盈亏状态: {pnl_icon} {position['pnl_percentage']:+.2f}%")
            print(f"   浮动盈亏: {position['unrealized_pnl']:+,.2f} 港元")
        else:
            print(f"   持仓状态: ⭕ 空仓")
            print(f"   持仓方向: 无")
            print(f"   持仓数量: 0 股")
        
        print(f"\n💰 资产状况:")
        print(f"   账户余额: {position['account_balance']:,.2f} 港元")
        print(f"   总资产: {position['total_assets']:,.2f} 港元")
        print(f"   累计收益率: {position['cumulative_return']:+.2f}%")
        print(f"   实现盈亏: {position['realized_pnl']:+,.2f} 港元")
        print(f"   累计盈亏: {position['total_pnl']:+,.2f} 港元")
        
        # 显示最近交易记录
        print(f"\n📋 最近7天交易记录:")
        recent_trades = self.get_recent_trades(df, 7)
        if recent_trades is not None and len(recent_trades) > 0:
            print(f"{'日期':<12} {'类型':<6} {'方向':<8} {'价格':<8} {'数量':<8} {'盈亏':<10} {'备注':<20}")
            print("-" * 80)
            
            for _, trade in recent_trades.head(10).iterrows():
                date_str = trade['交易日期'].strftime('%m-%d') if hasattr(trade['交易日期'], 'strftime') else str(trade['交易日期'])[:10]
                trade_type = str(trade.get('交易类型', ''))[:6]
                direction = str(trade.get('交易方向', ''))[:8]
                price = trade.get('交易价格', 0)
                quantity = trade.get('持仓数量', 0)
                pnl = trade.get('浮动盈亏', 0)
                notes = str(trade.get('备注', ''))[:20]
                
                print(f"{date_str:<12} {trade_type:<6} {direction:<8} {price:<8.2f} {quantity:<8.0f} {pnl:<+10.2f} {notes:<20}")
        
        print(f"\n📝 备注: {position['notes']}")
        print("\n✅ 持仓状态查看完成")

def main():
    """主函数"""
    viewer = PositionStatusViewer()
    viewer.display_position_status()

if __name__ == "__main__":
    main()
