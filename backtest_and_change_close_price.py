#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
根据回测逻辑修改平仓价格并重新计算
===============================

逻辑：
1. 如果回测结果是"止盈" → 平仓价格改为profit价格
2. 如果回测结果是"止损" → 平仓价格改为loss价格  
3. 如果是"到期平仓" → 平仓价格保持不变
4. 如果是"观望" → 不修改
5. 重新计算毛利润和净利润

作者: Cosmoon NG
日期: 2025年7月
"""

import pymysql

def backtest_and_change_close_price():
    """根据回测逻辑修改平仓价格"""
    db_config = {
        'host': 'localhost',
        'port': 3306,
        'database': 'finance',
        'user': 'root',
        'password': '12345678',
        'charset': 'utf8mb4'
    }
    
    try:
        connection = pymysql.connect(**db_config)
        cursor = connection.cursor()
        
        print("🎯 根据回测逻辑修改平仓价格")
        print("="*80)
        print("📊 修改逻辑:")
        print("   • 止盈 → 平仓价格 = profit价格")
        print("   • 止损 → 平仓价格 = loss价格")
        print("   • 到期平仓 → 平仓价格不变")
        print("   • 观望 → 不修改")
        print("="*80)
        
        # 1. 获取所有记录进行回测判断
        cursor.execute("""
            SELECT 交易序号, 交易方向, close, 平仓价格, `profit价格`, `loss价格`, 交易股数
            FROM test 
            ORDER BY 交易序号
        """)
        
        records = cursor.fetchall()
        
        take_profit_changes = 0
        stop_loss_changes = 0
        no_changes = 0
        observe_records = 0
        
        print(f"\n📊 处理 {len(records)} 条记录...")
        
        # 2. 逐条处理每个记录
        for record in records:
            trade_id, direction, open_price, close_price, profit_price, loss_price, shares = record
            
            if direction == '观望':
                observe_records += 1
                continue
            
            # 判断回测结果并决定新的平仓价格
            new_close_price = close_price  # 默认不变
            change_reason = "到期平仓"
            
            if direction == '买涨':
                if float(close_price) >= float(profit_price):
                    new_close_price = profit_price
                    change_reason = "止盈"
                    take_profit_changes += 1
                elif float(close_price) <= float(loss_price):
                    new_close_price = loss_price
                    change_reason = "止损"
                    stop_loss_changes += 1
                else:
                    no_changes += 1
            
            elif direction == '买跌':
                if float(close_price) <= float(profit_price):
                    new_close_price = profit_price
                    change_reason = "止盈"
                    take_profit_changes += 1
                elif float(close_price) >= float(loss_price):
                    new_close_price = loss_price
                    change_reason = "止损"
                    stop_loss_changes += 1
                else:
                    no_changes += 1
            
            # 3. 更新平仓价格（如果有变化）
            if new_close_price != close_price:
                cursor.execute("""
                    UPDATE test 
                    SET 平仓价格 = %s
                    WHERE 交易序号 = %s
                """, (float(new_close_price), trade_id))
        
        # 提交平仓价格修改
        connection.commit()
        
        print(f"✅ 平仓价格修改完成:")
        print(f"   • 止盈修改: {take_profit_changes}条")
        print(f"   • 止损修改: {stop_loss_changes}条")
        print(f"   • 到期平仓: {no_changes}条")
        print(f"   • 观望记录: {observe_records}条")
        
        # 4. 重新计算毛利润
        print(f"\n🧮 重新计算毛利润...")
        
        # 买涨: 毛利润 = (平仓价格 - 开仓价格) × 股数
        cursor.execute("""
            UPDATE test 
            SET 毛利润 = (平仓价格 - close) * 交易股数
            WHERE 交易方向 = '买涨'
        """)
        
        buy_long_updates = cursor.rowcount
        
        # 买跌: 毛利润 = (开仓价格 - 平仓价格) × 股数
        cursor.execute("""
            UPDATE test 
            SET 毛利润 = (close - 平仓价格) * 交易股数
            WHERE 交易方向 = '买跌'
        """)
        
        buy_short_updates = cursor.rowcount
        
        # 观望: 毛利润 = 0
        cursor.execute("""
            UPDATE test 
            SET 毛利润 = 0
            WHERE 交易方向 = '观望'
        """)
        
        observe_updates = cursor.rowcount
        
        print(f"✅ 毛利润重新计算完成:")
        print(f"   • 买涨记录: {buy_long_updates}条")
        print(f"   • 买跌记录: {buy_short_updates}条")
        print(f"   • 观望记录: {observe_updates}条")
        
        # 5. 重新计算净利润
        print(f"\n💰 重新计算净利润...")
        
        cursor.execute("""
            UPDATE test 
            SET 净利润 = 毛利润 - 交易成本
        """)
        
        net_profit_updates = cursor.rowcount
        print(f"✅ 净利润重新计算完成: {net_profit_updates}条")
        
        # 提交所有修改
        connection.commit()
        
        # 6. 验证修改结果
        print(f"\n📊 验证修改结果...")
        
        cursor.execute("""
            SELECT 
                COUNT(*) as 总记录数,
                SUM(毛利润) as 毛利润总和,
                SUM(净利润) as 净利润总和,
                AVG(净利润) as 平均净利润
            FROM test
        """)
        
        summary = cursor.fetchone()
        total_count, gross_sum, net_sum, net_avg = summary
        
        print(f"   • 总记录数: {total_count}")
        print(f"   • 毛利润总和: {gross_sum:+,.0f}港币")
        print(f"   • 净利润总和: {net_sum:+,.0f}港币")
        print(f"   • 平均净利润: {net_avg:+.1f}港币")
        
        # 7. 显示修改后的前20条记录
        print(f"\n📋 修改后前20条记录:")
        cursor.execute("""
            SELECT 交易序号, 交易方向, close, 平仓价格, `profit价格`, `loss价格`, 
                   毛利润, 净利润,
                   CASE 
                       WHEN 交易方向 = '观望' THEN '观望'
                       WHEN 交易方向 = '买涨' AND 平仓价格 = `profit价格` THEN '止盈'
                       WHEN 交易方向 = '买涨' AND 平仓价格 = `loss价格` THEN '止损'
                       WHEN 交易方向 = '买跌' AND 平仓价格 = `profit价格` THEN '止盈'
                       WHEN 交易方向 = '买跌' AND 平仓价格 = `loss价格` THEN '止损'
                       ELSE '到期平仓'
                   END AS 实际结果
            FROM test 
            ORDER BY 交易序号 
            LIMIT 20
        """)
        
        modified_records = cursor.fetchall()
        
        print("-" * 140)
        print(f"{'序号':<4} {'方向':<6} {'开仓价':<8} {'平仓价':<8} {'profit价格':<10} {'loss价格':<9} "
              f"{'毛利润':<8} {'净利润':<8} {'结果':<10}")
        print("-" * 140)
        
        for record in modified_records:
            trade_id, direction, open_price, close_price, profit_price, loss_price, gross_profit, net_profit, result = record
            print(f"{trade_id:<4} {direction:<6} {float(open_price):<8.2f} {float(close_price):<8.2f} "
                  f"{float(profit_price):<10.2f} {float(loss_price):<9.2f} "
                  f"{gross_profit:<8} {net_profit:<8} {result:<10}")
        
        # 8. 按结果类型统计
        print(f"\n📈 按结果类型统计:")
        cursor.execute("""
            SELECT 
                CASE 
                    WHEN 交易方向 = '观望' THEN '观望'
                    WHEN 交易方向 = '买涨' AND 平仓价格 = `profit价格` THEN '止盈'
                    WHEN 交易方向 = '买涨' AND 平仓价格 = `loss价格` THEN '止损'
                    WHEN 交易方向 = '买跌' AND 平仓价格 = `profit价格` THEN '止盈'
                    WHEN 交易方向 = '买跌' AND 平仓价格 = `loss价格` THEN '止损'
                    ELSE '到期平仓'
                END AS 结果类型,
                COUNT(*) as 数量,
                SUM(净利润) as 净利润总和,
                AVG(净利润) as 平均净利润
            FROM test 
            GROUP BY 结果类型
            ORDER BY 数量 DESC
        """)
        
        result_stats = cursor.fetchall()
        
        print("-" * 60)
        print(f"{'结果类型':<10} {'数量':<6} {'净利润总和':<12} {'平均净利润':<10}")
        print("-" * 60)
        
        for result_type, count, total_profit, avg_profit in result_stats:
            print(f"{result_type:<10} {count:<6} {total_profit:<12.0f} {avg_profit:<10.1f}")
        
        # 9. 按策略区域统计
        print(f"\n📊 按策略区域统计修改后结果:")
        cursor.execute("""
            SELECT 
                CASE 
                    WHEN `控制系数` > 0.43 AND `资金流比例` > 0.43 THEN '高值盈利区'
                    WHEN `控制系数` > 0.333 AND `控制系数` < 0.4 THEN '控股商控制区'
                    WHEN (`控制系数` < 0.25 OR `资金流比例` < 0.25) AND NOT (`控制系数` > 0.333 AND `控制系数` < 0.4) THEN '强亏损区'
                    ELSE '其他区域'
                END AS 策略区域,
                COUNT(*) as 总次数,
                SUM(净利润) as 净利润总和,
                AVG(净利润) as 平均净利润,
                SUM(CASE WHEN 交易方向 = '买涨' AND 平仓价格 = `profit价格` THEN 1 
                         WHEN 交易方向 = '买跌' AND 平仓价格 = `profit价格` THEN 1 ELSE 0 END) as 止盈次数,
                SUM(CASE WHEN 交易方向 = '买涨' AND 平仓价格 = `loss价格` THEN 1 
                         WHEN 交易方向 = '买跌' AND 平仓价格 = `loss价格` THEN 1 ELSE 0 END) as 止损次数
            FROM test 
            GROUP BY 策略区域
            ORDER BY 净利润总和 DESC
        """)
        
        zone_stats = cursor.fetchall()
        
        print("-" * 100)
        print(f"{'策略区域':<12} {'总次数':<8} {'净利润总和':<12} {'平均净利润':<12} {'止盈':<6} {'止损':<6}")
        print("-" * 100)
        
        for zone, count, total_profit, avg_profit, take_profit, stop_loss in zone_stats:
            print(f"{zone:<12} {count:<8} {total_profit:<12.0f} {avg_profit:<12.1f} {take_profit:<6} {stop_loss:<6}")
        
        connection.close()
        print(f"\n🎉 平仓价格修改和重新计算完成!")
        
    except Exception as e:
        print(f"❌ 修改平仓价格失败: {e}")

if __name__ == "__main__":
    backtest_and_change_close_price()
