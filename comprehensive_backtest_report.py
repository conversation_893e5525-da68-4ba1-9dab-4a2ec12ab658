#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
HSI50 XY策略完整回测报告
======================
基于Y>0.45且X>0.45的策略进行全面回测分析
"""

import yfinance as yf
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import matplotlib.pyplot as plt
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

class ComprehensiveBacktestReport:
    def __init__(self):
        """初始化回测报告器"""
        self.initial_capital = 30000
        self.monthly_addition = 1000
        self.max_position_ratio = 0.35  # 35%仓位

        # 策略参数
        self.take_profit_long = 0.016   # 多头止盈1.6%
        self.stop_loss_long = 0.008     # 多头止损0.8%
        self.take_profit_short = 0.008  # 空头止盈0.8%
        self.stop_loss_short = 0.016    # 空头止损1.6%

        # Y>0.43, X>0.43策略
        self.y_threshold = 0.43
        self.x_threshold = 0.43

        self.trades = []
        self.daily_equity = []

    def load_and_prepare_data(self):
        """加载和准备数据"""
        print("📊 加载HSI数据进行回测...")
        try:
            ticker = yf.Ticker("^HSI")
            hist = ticker.history(start="2000-01-01", end=None, interval="1d")

            if hist.empty:
                print("❌ 无法获取HSI数据")
                return False

            self.df = hist.reset_index()
            self.df.columns = [col.lower() for col in self.df.columns]
            self.df['Date'] = self.df['date']
            self.df['Close'] = self.df['close']
            self.df['High'] = self.df['high']
            self.df['Low'] = self.df['low']
            self.df['Volume'] = self.df['volume']

            self.df = self.df.sort_values('Date').reset_index(drop=True)

            print(f"✅ 成功加载 {len(self.df)} 条数据")
            print(f"📅 数据范围: {self.df['Date'].min().date()} 至 {self.df['Date'].max().date()}")

            self.calculate_indicators()
            return True

        except Exception as e:
            print(f"❌ 数据加载失败: {e}")
            return False

    def calculate_indicators(self):
        """计算技术指标"""
        print("   计算技术指标...")

        # 移动平均线
        self.df['ma_20'] = self.df['Close'].rolling(window=20).mean()
        self.df['ma_60'] = self.df['Close'].rolling(window=60).mean()

        # 计算XY指标
        self.df['midprice'] = (self.df['High'] + self.df['Low']) / 2
        self.df['controller'] = (self.df['Close'] > self.df['midprice']).astype(int)
        self.df['cumulative_controller'] = self.df['controller'].cumsum()
        self.df['row_number'] = range(1, len(self.df) + 1)
        self.df['Full_Y'] = self.df['cumulative_controller'] / self.df['row_number']

        self.df['Y'] = self.df['Full_Y']
        self.df['X'] = 1 - self.df['Full_Y']

        # 计算E值
        self.df['price_vs_ma20'] = self.df['Close'] / self.df['ma_20']
        self.df['y_probability'] = np.where(
            self.df['price_vs_ma20'] >= 1,
            0.5 + 0.3 * np.tanh((self.df['price_vs_ma20'] - 1) * 2),
            0.5 - 0.3 * np.tanh((1 - self.df['price_vs_ma20']) * 2)
        )
        self.df['y_probability'] = np.clip(self.df['y_probability'], 0.1, 0.9)

        self.df['price_deviation'] = (self.df['Close'] - self.df['midprice']) / self.df['midprice']
        self.df['E'] = (self.df['y_probability'] * 0.4 +
                       self.df['price_deviation'] * 0.3 +
                       (self.df['Y'] - 0.5) * 0.3)

        # RSI
        delta = self.df['Close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
        rs = gain / loss
        self.df['rsi'] = 100 - (100 / (1 + rs))

        print("✅ 技术指标计算完成")

    def check_entry_conditions(self, row):
        """检查入场条件"""
        y_value = row['Y']
        x_value = row['X']

        if pd.isna(y_value) or pd.isna(x_value):
            return 0

        # Y>0.43且X>0.43: 看涨 (降低门槛增加机会)
        if y_value > self.y_threshold and x_value > self.x_threshold:
            return 1  # 多头

        # 强亏损区: 看跌
        elif y_value < 0.25 or x_value < 0.25:
            return -1  # 空头

        # 其他区域: 看跌
        elif not (0.333 < y_value < self.y_threshold):
            return -1  # 空头

        return 0  # 观望

    def add_monthly_capital(self, date, capital):
        """每月增加资金"""
        last_month = getattr(self, 'last_month', None)
        current_month = date.replace(day=1)

        if last_month is None or current_month > last_month:
            self.last_month = current_month
            return capital + self.monthly_addition

        return capital

    def run_backtest(self):
        """运行回测"""
        print("\n🎯 开始回测 (Y>0.45, X>0.45策略)...")

        capital = self.initial_capital
        position = 0
        entry_price = 0
        entry_date = None
        entry_y = 0
        entry_x = 0
        entry_e = 0
        take_profit_price = 0
        stop_loss_price = 0

        max_capital = capital
        max_drawdown = 0

        for i in range(60, len(self.df)):
            row = self.df.iloc[i]
            date = row['Date']

            # 每月增加资金
            capital = self.add_monthly_capital(date, capital)

            # 记录每日权益
            current_equity = capital
            if position != 0:
                position_size = capital * self.max_position_ratio
                if position == 1:  # 多头
                    unrealized_pnl = (row['Close'] - entry_price) / entry_price * position_size
                else:  # 空头
                    unrealized_pnl = (entry_price - row['Close']) / entry_price * position_size
                current_equity += unrealized_pnl

            # 更新最大资金和回撤
            if current_equity > max_capital:
                max_capital = current_equity

            current_drawdown = (max_capital - current_equity) / max_capital
            if current_drawdown > max_drawdown:
                max_drawdown = current_drawdown

            self.daily_equity.append({
                'date': date,
                'equity': current_equity,
                'capital': capital,
                'position': position,
                'drawdown': current_drawdown,
                'Y': row['Y'],
                'X': row['X'],
                'price': row['Close']
            })

            # 检查平仓条件
            if position != 0:
                should_exit = False
                exit_reason = ""
                exit_price = row['Close']

                if position == 1:  # 多头
                    if row['High'] >= take_profit_price:
                        should_exit = True
                        exit_reason = "止盈"
                        exit_price = take_profit_price
                    elif row['Low'] <= stop_loss_price:
                        should_exit = True
                        exit_reason = "止损"
                        exit_price = stop_loss_price

                elif position == -1:  # 空头
                    if row['Low'] <= take_profit_price:
                        should_exit = True
                        exit_reason = "止盈"
                        exit_price = take_profit_price
                    elif row['High'] >= stop_loss_price:
                        should_exit = True
                        exit_reason = "止损"
                        exit_price = stop_loss_price

                if should_exit:
                    position_size = capital * self.max_position_ratio

                    if position == 1:
                        profit_ratio = (exit_price - entry_price) / entry_price
                    else:
                        profit_ratio = (entry_price - exit_price) / entry_price

                    profit = position_size * profit_ratio
                    capital += profit

                    holding_days = (date - entry_date).days

                    self.trades.append({
                        'entry_date': entry_date,
                        'exit_date': date,
                        'direction': '看涨' if position == 1 else '看跌',
                        'entry_price': entry_price,
                        'exit_price': exit_price,
                        'profit': profit,
                        'profit_ratio': profit_ratio,
                        'holding_days': holding_days,
                        'exit_reason': exit_reason,
                        'entry_y': entry_y,
                        'entry_x': entry_x,
                        'entry_e': entry_e,
                        'capital_after': capital
                    })

                    position = 0

            # 检查开仓条件
            if position == 0:
                signal = self.check_entry_conditions(row)

                if signal != 0:
                    position = signal
                    entry_price = row['Close']
                    entry_date = date
                    entry_y = row['Y']
                    entry_x = row['X']
                    entry_e = row['E']

                    if position == 1:
                        take_profit_price = entry_price * (1 + self.take_profit_long)
                        stop_loss_price = entry_price * (1 - self.stop_loss_long)
                    else:
                        take_profit_price = entry_price * (1 - self.take_profit_short)
                        stop_loss_price = entry_price * (1 + self.stop_loss_short)

        self.final_capital = capital
        self.max_drawdown = max_drawdown

        print(f"✅ 回测完成！")
        print(f"💰 最终资金: {self.final_capital:,.2f}港元")
        print(f"📊 总交易次数: {len(self.trades)}笔")
        print(f"📉 最大回撤: {self.max_drawdown*100:.2f}%")

    def generate_comprehensive_report(self):
        """生成综合回测报告"""
        print("\n" + "="*80)
        print("📊 HSI50 XY策略完整回测报告")
        print("="*80)
        print(f"📅 报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"🎯 策略参数: Y>{self.y_threshold}, X>{self.x_threshold}")
        print(f"💰 初始资金: {self.initial_capital:,}港元")
        print(f"📈 每月追加: {self.monthly_addition:,}港元")
        print(f"📊 最大仓位: {self.max_position_ratio*100:.0f}%")

        if not self.trades:
            print("❌ 没有产生任何交易")
            return

        # 基本统计
        trades_df = pd.DataFrame(self.trades)
        equity_df = pd.DataFrame(self.daily_equity)

        total_trades = len(trades_df)
        winning_trades = len(trades_df[trades_df['profit'] > 0])
        losing_trades = total_trades - winning_trades
        win_rate = winning_trades / total_trades if total_trades > 0 else 0

        total_profit = trades_df['profit'].sum()
        avg_profit = trades_df['profit'].mean()
        max_profit = trades_df['profit'].max()
        min_profit = trades_df['profit'].min()

        # 时间统计
        start_date = self.df['Date'].min()
        end_date = self.df['Date'].max()
        total_days = (end_date - start_date).days
        total_years = total_days / 365

        # 投入资金计算
        months = total_days / 30
        total_invested = self.initial_capital + months * self.monthly_addition
        net_profit = self.final_capital - total_invested
        total_return = net_profit / total_invested if total_invested > 0 else 0
        annual_return = (self.final_capital / total_invested) ** (1/total_years) - 1 if total_years > 0 else 0

        print(f"\n📊 基本统计:")
        print(f"   回测期间: {start_date.date()} 至 {end_date.date()} ({total_years:.1f}年)")
        print(f"   总投入: {total_invested:,.2f}港元")
        print(f"   最终资金: {self.final_capital:,.2f}港元")
        print(f"   净收益: {net_profit:,.2f}港元")
        print(f"   总收益率: {total_return*100:.2f}%")
        print(f"   年化收益率: {annual_return*100:.2f}%")

        print(f"\n📈 交易统计:")
        print(f"   总交易次数: {total_trades}笔")
        print(f"   盈利交易: {winning_trades}笔 ({win_rate*100:.1f}%)")
        print(f"   亏损交易: {losing_trades}笔 ({(1-win_rate)*100:.1f}%)")
        print(f"   平均每笔收益: {avg_profit:.2f}港元")
        print(f"   最大盈利: {max_profit:.2f}港元")
        print(f"   最大亏损: {min_profit:.2f}港元")

        # 止盈止损统计
        tp_trades = trades_df[trades_df['exit_reason'] == '止盈']
        sl_trades = trades_df[trades_df['exit_reason'] == '止损']

        print(f"\n🎯 止盈止损统计:")
        print(f"   止盈次数: {len(tp_trades)}笔 ({len(tp_trades)/total_trades*100:.1f}%)")
        print(f"   止损次数: {len(sl_trades)}笔 ({len(sl_trades)/total_trades*100:.1f}%)")

        if len(tp_trades) > 0:
            print(f"   止盈平均收益: {tp_trades['profit'].mean():.2f}港元")
        if len(sl_trades) > 0:
            print(f"   止损平均亏损: {sl_trades['profit'].mean():.2f}港元")

        # 方向统计
        long_trades = trades_df[trades_df['direction'] == '看涨']
        short_trades = trades_df[trades_df['direction'] == '看跌']

        print(f"\n📊 方向统计:")
        print(f"   看涨交易: {len(long_trades)}笔")
        print(f"   看跌交易: {len(short_trades)}笔")

        if len(long_trades) > 0:
            long_win_rate = len(long_trades[long_trades['profit'] > 0]) / len(long_trades)
            print(f"   看涨胜率: {long_win_rate*100:.1f}%")
            print(f"   看涨平均收益: {long_trades['profit'].mean():.2f}港元")

        if len(short_trades) > 0:
            short_win_rate = len(short_trades[short_trades['profit'] > 0]) / len(short_trades)
            print(f"   看跌胜率: {short_win_rate*100:.1f}%")
            print(f"   看跌平均收益: {short_trades['profit'].mean():.2f}港元")

        # 持仓时间统计
        avg_holding = trades_df['holding_days'].mean()
        max_holding = trades_df['holding_days'].max()
        min_holding = trades_df['holding_days'].min()

        print(f"\n⏰ 持仓时间统计:")
        print(f"   平均持仓: {avg_holding:.1f}天")
        print(f"   最长持仓: {max_holding}天")
        print(f"   最短持仓: {min_holding}天")

        # 风险指标
        print(f"\n📉 风险指标:")
        print(f"   最大回撤: {self.max_drawdown*100:.2f}%")

        if annual_return > 0 and self.max_drawdown > 0:
            calmar_ratio = annual_return / self.max_drawdown
            print(f"   卡尔玛比率: {calmar_ratio:.2f}")

        # 月度收益分析
        equity_df['month'] = pd.to_datetime(equity_df['date']).dt.to_period('M')
        monthly_returns = equity_df.groupby('month')['equity'].last().pct_change().dropna()

        if len(monthly_returns) > 0:
            monthly_win_rate = (monthly_returns > 0).sum() / len(monthly_returns)
            print(f"   月度胜率: {monthly_win_rate*100:.1f}%")
            print(f"   月度收益标准差: {monthly_returns.std()*100:.2f}%")

        # 买入持有对比
        hsi_start = self.df['Close'].iloc[60]
        hsi_end = self.df['Close'].iloc[-1]
        buy_hold_return = (hsi_end - hsi_start) / hsi_start
        buy_hold_annual = (buy_hold_return + 1) ** (1/total_years) - 1

        print(f"\n📊 策略对比:")
        print(f"   XY策略年化收益: {annual_return*100:.2f}%")
        print(f"   买入持有年化收益: {buy_hold_annual*100:.2f}%")
        print(f"   超额收益: {(annual_return - buy_hold_annual)*100:+.2f}%")

        # XY指标分析
        print(f"\n🎯 XY指标分析:")
        signal_data = self.df[(self.df['Y'] > self.y_threshold) & (self.df['X'] > self.x_threshold)]

        if len(signal_data) > 0:
            print(f"   满足Y>{self.y_threshold}, X>{self.x_threshold}的天数: {len(signal_data)}天")
            print(f"   占总天数比例: {len(signal_data)/len(self.df)*100:.1f}%")
            print(f"   信号期间Y值范围: {signal_data['Y'].min():.4f} ~ {signal_data['Y'].max():.4f}")
            print(f"   信号期间X值范围: {signal_data['X'].min():.4f} ~ {signal_data['X'].max():.4f}")

        # 最近交易
        print(f"\n📋 最近5笔交易:")
        recent_trades = trades_df.tail(5)
        for _, trade in recent_trades.iterrows():
            direction_emoji = "📈" if trade['direction'] == '看涨' else "📉"
            profit_emoji = "💰" if trade['profit'] > 0 else "💔"
            print(f"   {direction_emoji} {trade['exit_date'].strftime('%Y-%m-%d')}: {trade['direction']}")
            print(f"      {profit_emoji} {trade['exit_reason']}, 收益: {trade['profit']:+.2f}港元 ({trade['profit_ratio']*100:+.2f}%)")
            print(f"      持仓{trade['holding_days']}天, Y={trade['entry_y']:.4f}, X={trade['entry_x']:.4f}")

        # 策略评估
        print(f"\n🎉 策略评估:")

        if win_rate >= 0.5:
            win_rating = "优秀"
        elif win_rate >= 0.4:
            win_rating = "良好"
        elif win_rate >= 0.3:
            win_rating = "一般"
        else:
            win_rating = "较差"

        if annual_return >= 0.1:
            return_rating = "优秀"
        elif annual_return >= 0.05:
            return_rating = "良好"
        elif annual_return >= 0:
            return_rating = "一般"
        else:
            return_rating = "较差"

        if self.max_drawdown <= 0.05:
            risk_rating = "低风险"
        elif self.max_drawdown <= 0.1:
            risk_rating = "中低风险"
        elif self.max_drawdown <= 0.2:
            risk_rating = "中等风险"
        else:
            risk_rating = "高风险"

        print(f"   胜率评级: {win_rating} ({win_rate*100:.1f}%)")
        print(f"   收益评级: {return_rating} (年化{annual_return*100:.2f}%)")
        print(f"   风险评级: {risk_rating} (最大回撤{self.max_drawdown*100:.2f}%)")

        # 改进建议
        print(f"\n💡 改进建议:")

        if win_rate < 0.4:
            print(f"   • 胜率偏低，考虑提高Y、X门槛或增加过滤条件")

        if self.max_drawdown > 0.1:
            print(f"   • 回撤较大，考虑降低仓位或优化止损")

        if annual_return < 0.05:
            print(f"   • 收益率偏低，考虑优化参数或增加仓位")

        if len(long_trades) == 0 or len(short_trades) == 0:
            print(f"   • 交易方向单一，考虑平衡多空策略")

        print(f"\n📄 详细交易记录已保存到Excel文件")

        return {
            'total_return': total_return,
            'annual_return': annual_return,
            'win_rate': win_rate,
            'max_drawdown': self.max_drawdown,
            'total_trades': total_trades,
            'final_capital': self.final_capital
        }

    def run_full_analysis(self):
        """运行完整分析"""
        if not self.load_and_prepare_data():
            return None

        self.run_backtest()
        results = self.generate_comprehensive_report()

        return results

def main():
    """主函数"""
    print("🎯 HSI50 XY策略完整回测分析")
    print("基于Y>0.45且X>0.45的高质量信号策略")
    print("="*60)

    backtest = ComprehensiveBacktestReport()
    results = backtest.run_full_analysis()

    if results:
        print(f"\n🎉 回测分析完成！")
        print(f"📊 策略表现: 年化收益{results['annual_return']*100:.2f}%, 胜率{results['win_rate']*100:.1f}%, 最大回撤{results['max_drawdown']*100:.2f}%")
    else:
        print(f"❌ 回测分析失败")

if __name__ == "__main__":
    main()
