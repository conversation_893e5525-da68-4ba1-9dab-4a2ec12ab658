#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import mysql.connector
from datetime import datetime

def test_finance_database():
    """测试finance数据库连接"""
    
    print("测试finance数据库连接")
    print("=" * 50)
    
    config = {
        'host': 'localhost',
        'port': 3306,
        'user': 'root',
        'password': '12345678',
        'database': 'finance'
    }
    
    try:
        print(f"连接到 {config['host']}:{config['port']}/finance ...")
        connection = mysql.connector.connect(**config)
        
        if connection.is_connected():
            print("✅ finance数据库连接成功")
            
            cursor = connection.cursor()
            
            # 检查eab_0023hk表是否存在
            cursor.execute("SHOW TABLES LIKE 'eab_0023hk'")
            result = cursor.fetchone()
            
            if result:
                print("✅ 找到表: eab_0023hk")
                
                # 查看表结构
                cursor.execute("DESCRIBE eab_0023hk")
                columns = cursor.fetchall()
                print("📋 表结构:")
                for col in columns:
                    print(f"   {col[0]} - {col[1]}")
                
                # 查看记录数
                cursor.execute("SELECT COUNT(*) FROM eab_0023hk")
                count = cursor.fetchone()[0]
                print(f"📊 记录数: {count}")
                
            else:
                print("❌ 未找到表: eab_0023hk")
            
            # 检查存储过程
            cursor.execute("""
                SELECT ROUTINE_NAME 
                FROM INFORMATION_SCHEMA.ROUTINES 
                WHERE ROUTINE_SCHEMA = 'finance' 
                AND ROUTINE_NAME = 'sp_combined_stock_analysis'
            """)
            
            sp_result = cursor.fetchone()
            if sp_result:
                print("✅ 找到存储过程: sp_combined_stock_analysis")
                
                # 尝试调用存储过程
                try:
                    print("🔄 调用存储过程...")
                    cursor.callproc('sp_combined_stock_analysis', ['finance.eab_0023hk'])
                    connection.commit()
                    print("✅ 存储过程执行成功")
                    
                    # 获取结果
                    for result in cursor.stored_results():
                        rows = result.fetchall()
                        if rows:
                            print(f"📊 返回 {len(rows)} 条记录")
                            
                except Exception as e:
                    print(f"❌ 存储过程执行失败: {e}")
                    
            else:
                print("❌ 未找到存储过程: sp_combined_stock_analysis")
            
            cursor.close()
            connection.close()
            print("🔌 数据库连接已关闭")
            
        return True
        
    except mysql.connector.Error as e:
        if e.errno == 2003:
            print("❌ 无法连接到MySQL服务器")
        elif e.errno == 1045:
            print("❌ 认证失败 (用户名或密码错误)")
        elif e.errno == 1049:
            print("❌ 数据库 'finance' 不存在")
        else:
            print(f"❌ MySQL错误: {e}")
        return False
        
    except Exception as e:
        print(f"❌ 连接失败: {e}")
        return False

if __name__ == "__main__":
    success = test_finance_database()
    
    if success:
        print("\n✅ 数据库测试通过，可以运行完整系统")
    else:
        print("\n❌ 数据库测试失败，请检查配置")
