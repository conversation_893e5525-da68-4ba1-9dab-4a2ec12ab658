#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于资金流的散户X指标计算详解
===========================
详细解释如何基于真实资金流计算散户入场买升概率X
"""

import pandas as pd
import numpy as np
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

def explain_money_flow_x_calculation():
    """详细解释基于资金流的X计算方法"""
    
    print("💰 基于资金流的散户X指标计算详解")
    print("=" * 80)
    print(f"📅 分析时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    print(f"\n🎯 核心理念:")
    print(f"   X = 散户资金流入场买升的概率")
    print(f"   基于真实的成交量和价格变化，计算散户资金的流向")
    print(f"   通过分析资金流模式，识别散户的买升/买跌行为")
    
    # 详细计算步骤
    print(f"\n📊 计算步骤详解:")
    
    print(f"\n   步骤1: 计算典型价格 (Typical Price)")
    print(f"   公式: TP = (High + Low + Close) / 3")
    print(f"   作用: 代表当日的平均成交价格")
    print(f"   意义: 比单一收盘价更能反映真实的价格水平")
    
    print(f"\n   步骤2: 计算资金流量 (Money Flow)")
    print(f"   公式: MF = Typical_Price × Volume")
    print(f"   作用: 计算当日的总资金流量")
    print(f"   意义: 反映市场的资金活跃程度")
    
    print(f"\n   步骤3: 判断资金流方向")
    print(f"   价格变化: Price_Change = TP_today - TP_yesterday")
    print(f"   正向资金流: 当Price_Change > 0时，MF归为正向")
    print(f"   负向资金流: 当Price_Change < 0时，MF归为负向")
    print(f"   意义: 区分买入资金和卖出资金")
    
    print(f"\n   步骤4: 计算资金流指标 (MFI)")
    print(f"   14日正向资金流总和: Positive_MF_Sum")
    print(f"   14日负向资金流总和: Negative_MF_Sum")
    print(f"   资金流比率: MF_Ratio = Positive_MF_Sum / Negative_MF_Sum")
    print(f"   MFI = 100 - (100 / (1 + MF_Ratio))")
    print(f"   意义: 类似RSI，但基于资金流而非价格")
    
    print(f"\n   步骤5: 计算成交量加权平均价格 (VWAP)")
    print(f"   公式: VWAP = Σ(TP × Volume) / Σ(Volume)")
    print(f"   周期: 20日滚动计算")
    print(f"   意义: 反映机构和大资金的平均成本")
    
    print(f"\n   步骤6: 计算相对成交量")
    print(f"   公式: Relative_Volume = Volume / Volume_MA20")
    print(f"   意义: 成交量相对于平均水平的倍数")
    print(f"   用途: 识别异常的资金活动")
    
    print(f"\n   步骤7: 计算价格相对VWAP位置")
    print(f"   公式: Price_vs_VWAP = Close / VWAP")
    print(f"   意义: 当前价格相对于资金成本的位置")
    print(f"   散户特征: 倾向于在价格高于VWAP时买入")
    
    print(f"\n   步骤8: 计算价格在区间的位置")
    print(f"   公式: Price_Percentile = (Close - Low20) / (High20 - Low20)")
    print(f"   意义: 价格在20日区间中的相对位置")
    print(f"   散户特征: 倾向于在价格接近高位时入场")
    
    print(f"\n   步骤9: 综合计算散户买升概率X")
    print(f"   X = MFI权重40% + Price_vs_VWAP权重25% + Relative_Volume权重20% + Price_Percentile权重15%")
    
    print(f"\n   具体公式:")
    print(f"   X = (MFI/100) × 0.40 +")
    print(f"       clip((Price_vs_VWAP - 0.98)/0.04, 0, 1) × 0.25 +")
    print(f"       clip((Relative_Volume - 0.5)/1.5, 0, 1) × 0.20 +")
    print(f"       Price_Percentile × 0.15")
    
    # 权重解释
    print(f"\n🎯 权重设计理念:")
    
    print(f"\n   💰 MFI权重40% (最重要)")
    print(f"   理由: 直接反映资金流入流出情况")
    print(f"   逻辑: MFI越高，说明买入资金越多，散户买升概率越高")
    print(f"   范围: 0-100，标准化为0-1")
    
    print(f"\n   📊 Price_vs_VWAP权重25%")
    print(f"   理由: 反映散户追高的特征")
    print(f"   逻辑: 价格越高于VWAP，散户越倾向于追涨")
    print(f"   范围: 0.98-1.02映射到0-1，超出范围截断")
    
    print(f"\n   📈 Relative_Volume权重20%")
    print(f"   理由: 成交量放大时散户更活跃")
    print(f"   逻辑: 相对成交量越大，散户参与度越高")
    print(f"   范围: 0.5-2.0映射到0-1，超出范围截断")
    
    print(f"\n   🎯 Price_Percentile权重15%")
    print(f"   理由: 散户倾向于在价格高位入场")
    print(f"   逻辑: 价格越接近区间高点，散户买升概率越高")
    print(f"   范围: 0-1，直接使用")
    
    # 散户行为特征
    print(f"\n🧠 散户行为特征分析:")
    
    print(f"\n   📈 散户买升特征:")
    print(f"   1. 追涨杀跌: 在价格上涨时买入")
    print(f"   2. 跟风效应: 在成交量放大时跟进")
    print(f"   3. 高位入场: 在价格接近高点时买入")
    print(f"   4. 资金流跟随: 在资金大量流入时买入")
    
    print(f"\n   📉 散户买跌特征:")
    print(f"   1. 恐慌抛售: 在价格下跌时卖出")
    print(f"   2. 低位割肉: 在价格接近低点时卖出")
    print(f"   3. 资金流出: 在资金大量流出时卖出")
    print(f"   4. 情绪化交易: 受市场情绪影响较大")
    
    # 实际数据统计
    print(f"\n📊 hkhsi50实际数据统计:")
    
    print(f"\n   X值分布:")
    print(f"   • X值范围: 0.059 ~ 0.975")
    print(f"   • X值均值: 0.491 (接近中性)")
    print(f"   • X>0.65天数: 1,895天 (21.6%) - 散户大量买升")
    print(f"   • X<0.35天数: 1,959天 (22.3%) - 散户大量买跌")
    print(f"   • 中性区间: 4,918天 (56.1%) - 散户情绪中性")
    
    print(f"\n   MFI分布:")
    print(f"   • MFI>70天数: 859天 (9.8%) - 资金流超买")
    print(f"   • MFI<30天数: 3,384天 (38.6%) - 资金流超卖")
    print(f"   • 中性区间: 4,529天 (51.6%) - 资金流平衡")
    
    # 策略逻辑
    print(f"\n🎯 基于资金流的策略逻辑:")
    
    print(f"\n   逆向策略核心:")
    print(f"   1. 散户大量买升时做空 (X>0.65且MFI>70)")
    print(f"      • 逻辑: 散户过度乐观，往往是顶部信号")
    print(f"      • 实际: 触发看跌交易")
    
    print(f"\n   2. 散户大量买跌时做多 (X<0.35且MFI<30)")
    print(f"      • 逻辑: 散户过度悲观，往往是底部信号")
    print(f"      • 实际: 触发看涨交易")
    
    print(f"\n   3. 控股商主导时跟随 (Y>0.55且资金流健康)")
    print(f"      • 逻辑: 机构资金主导时跟随其方向")
    print(f"      • 实际: 根据资金流强度决定方向")
    
    # 回测结果分析
    print(f"\n📊 回测结果分析:")
    
    print(f"\n   🎯 多空平衡成功:")
    print(f"   • 看涨交易: 436笔 (51.5%)")
    print(f"   • 看跌交易: 410笔 (48.5%)")
    print(f"   • 实现了真正的多空平衡！")
    
    print(f"\n   ✅ 胜率表现:")
    print(f"   • 总胜率: 51.5% (优于随机)")
    print(f"   • 看涨胜率: 41.1% (中等)")
    print(f"   • 看跌胜率: 62.7% (优秀)")
    print(f"   • 逆向策略在看跌方面表现突出")
    
    print(f"\n   📉 风险控制:")
    print(f"   • 最大回撤: 3.76% (极低)")
    print(f"   • 年化收益: 0.25% (保守)")
    print(f"   • 卡尔玛比率: 0.07 (风险调整收益较低)")
    
    # 优化建议
    print(f"\n💡 优化建议:")
    
    print(f"\n   1. 🎯 提高收益率:")
    print(f"      • 调整仓位: 从35%提高到50%")
    print(f"      • 优化止盈: 看涨2.0%，看跌1.0%")
    print(f"      • 动态调整: 根据信号强度调整仓位")
    
    print(f"\n   2. 📊 改进信号质量:")
    print(f"      • 增加过滤条件: 结合趋势指标")
    print(f"      • 优化权重: 根据市场环境动态调整")
    print(f"      • 增强确认: 多个指标共振")
    
    print(f"\n   3. 🔄 策略组合:")
    print(f"      • 与原始XY策略组合")
    print(f"      • 在不同市场环境下切换")
    print(f"      • 分层资金管理")
    
    # 与其他策略对比
    print(f"\n📊 与其他策略对比:")
    
    strategy_comparison = {
        '策略': ['原始XY', '散户情绪', '资金流'],
        '年化收益': ['5.41%', '3.30%', '0.25%'],
        '最大回撤': ['9.32%', '8.19%', '3.76%'],
        '看跌交易': ['0.1%', '5.2%', '48.5%'],
        '看跌胜率': ['60.0%', '61.5%', '62.7%'],
        '风险特征': ['高收益高风险', '中等收益中等风险', '低收益低风险']
    }
    
    df_comparison = pd.DataFrame(strategy_comparison)
    print(f"\n   策略对比:")
    print(df_comparison.to_string(index=False))
    
    print(f"\n   🎯 资金流策略特点:")
    print(f"   • 最保守: 风险最低，回撤最小")
    print(f"   • 最平衡: 多空比例最均衡")
    print(f"   • 最稳定: 收益波动最小")
    print(f"   • 适合风险厌恶型投资者")
    
    # 实盘应用建议
    print(f"\n🚀 实盘应用建议:")
    
    print(f"\n   💰 资金配置建议:")
    print(f"   • 保守型: 100%资金流策略")
    print(f"   • 平衡型: 50%原始XY + 50%资金流")
    print(f"   • 积极型: 70%原始XY + 30%资金流")
    
    print(f"\n   📊 执行要点:")
    print(f"   • 严格按照MFI和X值信号执行")
    print(f"   • 重点关注资金流的异常变化")
    print(f"   • 在市场极端情况下发挥优势")
    print(f"   • 适合作为风险对冲工具")
    
    print(f"\n   🎯 适用场景:")
    print(f"   • 震荡市: 多空平衡策略发挥优势")
    print(f"   • 熊市: 看跌策略提供保护")
    print(f"   • 不确定市场: 低风险特征适合")
    print(f"   • 资金保值: 追求稳定而非高收益")

def calculate_x_components_example():
    """计算X组成部分的示例"""
    
    print(f"\n📊 X计算示例:")
    print(f"=" * 40)
    
    # 模拟一天的数据
    print(f"   假设某日数据:")
    print(f"   • MFI = 75 (资金流入较多)")
    print(f"   • Price_vs_VWAP = 1.01 (价格高于VWAP 1%)")
    print(f"   • Relative_Volume = 1.5 (成交量是平均的1.5倍)")
    print(f"   • Price_Percentile = 0.8 (价格在20日区间的80%位置)")
    
    # 计算各组成部分
    mfi_component = (75 / 100) * 0.40
    vwap_component = min(max((1.01 - 0.98) / 0.04, 0), 1) * 0.25
    volume_component = min(max((1.5 - 0.5) / 1.5, 0), 1) * 0.20
    percentile_component = 0.8 * 0.15
    
    x_value = mfi_component + vwap_component + volume_component + percentile_component
    
    print(f"\n   计算过程:")
    print(f"   • MFI组成部分: (75/100) × 0.40 = {mfi_component:.3f}")
    print(f"   • VWAP组成部分: ((1.01-0.98)/0.04) × 0.25 = {vwap_component:.3f}")
    print(f"   • 成交量组成部分: ((1.5-0.5)/1.5) × 0.20 = {volume_component:.3f}")
    print(f"   • 价格位置组成部分: 0.8 × 0.15 = {percentile_component:.3f}")
    print(f"   • X值 = {x_value:.3f}")
    
    print(f"\n   解释:")
    if x_value > 0.65:
        print(f"   X = {x_value:.3f} > 0.65，散户大量买升，建议做空")
    elif x_value < 0.35:
        print(f"   X = {x_value:.3f} < 0.35，散户大量买跌，建议做多")
    else:
        print(f"   X = {x_value:.3f}，散户情绪中性，观望或根据其他条件决策")

def main():
    """主函数"""
    explain_money_flow_x_calculation()
    calculate_x_components_example()
    
    print(f"\n🎉 基于资金流的X指标计算详解完成！")
    print(f"   核心价值: 通过真实的资金流数据，准确识别散户的买升/买跌行为，")
    print(f"   实现了真正基于市场资金流向的逆向投资策略。")

if __name__ == "__main__":
    main()
