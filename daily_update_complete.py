#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
每日完整更新系统
执行顺序：
1. 更新数据库 (daily_update_eab_table.py)
2. 运行存储过程 sp_updatecontroller_enhanced 更新 Full_Y
3. 查看持仓状态 (position_status_viewer.py)
"""

import subprocess
import sys
from datetime import datetime
import os

def print_header(title):
    """打印标题"""
    print("\n" + "=" * 60)
    print(f"🎯 {title}")
    print("=" * 60)

def print_step(step_num, description):
    """打印步骤"""
    print(f"\n{step_num}️⃣ {description}")
    print("-" * 40)

def run_database_update():
    """步骤1: 更新数据库"""
    print_step(1, "更新数据库 (daily_update_eab_table.py)")

    try:
        # 运行数据库更新脚本
        result = subprocess.run([sys.executable, "daily_update_eab_table.py"],
                              capture_output=True, text=True, timeout=120)

        if result.returncode == 0:
            print("✅ 数据库更新成功")
            # 显示输出的关键信息
            output_lines = result.stdout.split('\n')
            for line in output_lines:
                if any(keyword in line for keyword in ['今日数据', '收盘价', 'Y值', 'X值', 'E值', '更新结果汇总']):
                    print(f"   {line}")
            return True
        else:
            print("❌ 数据库更新失败")
            print(f"错误信息: {result.stderr}")
            return False

    except subprocess.TimeoutExpired:
        print("❌ 数据库更新超时")
        return False
    except Exception as e:
        print(f"❌ 数据库更新异常: {e}")
        return False

def run_sp_updatecontroller_enhanced():
    """步骤2: 更新Full_Y和Controller字段"""
    print_step(2, "更新Full_Y和Controller字段")

    try:
        # 运行专用的Full_Y更新脚本
        result = subprocess.run([sys.executable, "update_full_y_controller.py"],
                              capture_output=True, text=True, timeout=60)

        if result.returncode == 0:
            print("✅ Full_Y和Controller更新成功")
            # 显示输出的关键信息
            output_lines = result.stdout.split('\n')
            for line in output_lines:
                if any(keyword in line for keyword in ['更新统计', '最新记录完整性检查', '完成时间']):
                    print(f"   {line}")
            return True
        else:
            print("❌ Full_Y和Controller更新失败")
            print(f"错误信息: {result.stderr}")
            return False

    except subprocess.TimeoutExpired:
        print("❌ Full_Y更新超时")
        return False
    except Exception as e:
        print(f"❌ Full_Y更新异常: {e}")
        return False

def run_position_status_viewer():
    """步骤3: 查看持仓状态"""
    print_step(3, "查看持仓状态 (position_status_viewer.py)")

    try:
        # 运行持仓状态查看器
        result = subprocess.run([sys.executable, "position_status_viewer.py"],
                              capture_output=True, text=True, timeout=60)

        if result.returncode == 0:
            print("✅ 持仓状态查看成功")
            # 显示输出
            print(result.stdout)
            return True
        else:
            print("❌ 持仓状态查看失败")
            print(f"错误信息: {result.stderr}")
            return False

    except subprocess.TimeoutExpired:
        print("❌ 持仓状态查看超时")
        return False
    except Exception as e:
        print(f"❌ 持仓状态查看异常: {e}")
        return False

def verify_files_exist():
    """验证必要文件是否存在"""
    required_files = [
        "daily_update_eab_table.py",
        "update_full_y_controller.py",
        "position_status_viewer.py"
    ]

    missing_files = []
    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)

    if missing_files:
        print("❌ 缺少必要文件:")
        for file in missing_files:
            print(f"   • {file}")
        return False

    return True

def main():
    """主函数"""
    print_header("每日完整更新系统")
    print(f"🕐 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

    # 验证文件
    if not verify_files_exist():
        print("\n❌ 系统检查失败，请确保所有必要文件存在")
        return False

    print("✅ 系统检查通过")

    # 执行步骤
    success_count = 0
    total_steps = 3

    # 步骤1: 更新数据库
    if run_database_update():
        success_count += 1

    # 步骤2: 运行存储过程
    if run_sp_updatecontroller_enhanced():
        success_count += 1

    # 步骤3: 查看持仓状态
    if run_position_status_viewer():
        success_count += 1

    # 总结
    print_header("更新完成总结")
    print(f"🕐 完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"📊 成功步骤: {success_count}/{total_steps}")
    print(f"📈 成功率: {success_count/total_steps*100:.1f}%")

    if success_count == total_steps:
        print("🎉 所有更新任务完成！")
        return True
    else:
        print("⚠️ 部分任务失败，请检查错误信息")
        return False

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n⚠️ 用户中断操作")
        sys.exit(1)
    except Exception as e:
        print(f"\n\n❌ 系统异常: {e}")
        sys.exit(1)
