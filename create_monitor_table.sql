-- 创建0023.HK实时监控数据表

USE finance;

-- 删除已存在的表（如果需要重新创建）
-- DROP TABLE IF EXISTS realtime_monitor_0023hk;

-- 创建实时监控表
CREATE TABLE IF NOT EXISTS realtime_monitor_0023hk (
    id INT AUTO_INCREMENT PRIMARY KEY,
    timestamp DATETIME NOT NULL,
    price DECIMAL(10,4) NOT NULL,
    open_price DECIMAL(10,4) DEFAULT 0,
    high_price DECIMAL(10,4) DEFAULT 0,
    low_price DECIMAL(10,4) DEFAULT 0,
    volume BIGINT DEFAULT 0,
    previous_close DECIMAL(10,4) DEFAULT 0,
    ma5 DECIMAL(10,4) DEFAULT 0,
    ma10 DECIMAL(10,4) DEFAULT 0,
    ma20 DECIMAL(10,4) DEFAULT 0,
    rsi DECIMAL(8,4) DEFAULT 50,
    bb_upper DECIMAL(10,4) DEFAULT 0,
    bb_middle DECIMAL(10,4) DEFAULT 0,
    bb_lower DECIMAL(10,4) DEFAULT 0,
    trading_signal VARCHAR(255) DEFAULT '',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_timestamp (timestamp),
    INDEX idx_price (price),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='0023.HK实时监控数据';

-- 显示表结构
DESCRIBE realtime_monitor_0023hk;
