#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试HK2800博弈论策略
"""

import pymysql
import pandas as pd
import numpy as np

def test_hk2800_strategy():
    """测试HK2800博弈论策略"""
    
    db_config = {
        'host': 'localhost',
        'port': 3306,
        'database': 'finance',
        'user': 'root',
        'password': '12345678',
        'charset': 'utf8mb4'
    }
    
    try:
        connection = pymysql.connect(**db_config)
        cursor = connection.cursor()
        
        print("🎯 HK2800博弈论策略测试")
        print("="*60)
        print("📊 目标：盈富基金 (追踪恒生指数)")
        print("🎯 策略：Y≥X≥0.4")
        
        # 1. 获取完整数据
        cursor.execute("""
            SELECT date, close, adj_close, y_probability, inflow_ratio, median_price
            FROM hk2800 
            WHERE y_probability IS NOT NULL 
            AND inflow_ratio IS NOT NULL
            ORDER BY date ASC
        """)
        
        data = cursor.fetchall()
        df = pd.DataFrame(data, columns=['date', 'close', 'adj_close', 'y_probability', 'inflow_ratio', 'median_price'])
        
        for col in ['close', 'adj_close', 'y_probability', 'inflow_ratio', 'median_price']:
            df[col] = pd.to_numeric(df[col])
        df['date'] = pd.to_datetime(df['date'])
        
        print(f"📊 数据范围: {len(df)} 条记录")
        print(f"📅 时间跨度: {df['date'].min().strftime('%Y-%m-%d')} 到 {df['date'].max().strftime('%Y-%m-%d')}")
        
        # 2. 计算策略条件
        df['condition_04'] = (df['y_probability'] >= 0.4) & (df['inflow_ratio'] >= 0.4) & (df['y_probability'] >= df['inflow_ratio'])
        df['edeta'] = (8 * df['inflow_ratio'] - 3) * df['y_probability'] - 3 * df['inflow_ratio'] + 1
        df['signal'] = df['condition_04'] & (df['edeta'] > 0)
        
        # 3. 策略统计
        total_days = len(df)
        signal_days = df['signal'].sum()
        
        print(f"\n📊 Y≥X≥0.4策略统计:")
        print(f"   总交易日: {total_days}")
        print(f"   信号天数: {signal_days}")
        print(f"   信号频率: {signal_days/total_days*100:.2f}%")
        
        years = (df['date'].max() - df['date'].min()).days / 365.25
        print(f"   年均交易: {signal_days/years:.1f}次")
        
        # 4. 当前市场状态
        latest = df.iloc[-1]
        current_signal = latest['signal']
        
        print(f"\n📊 当前市场状态 ({latest['date'].strftime('%Y-%m-%d')}):")
        print(f"   当前价格: {latest['close']:.2f} 港币")
        print(f"   Y值: {latest['y_probability']:.3f}")
        print(f"   X值: {latest['inflow_ratio']:.3f}")
        print(f"   Edeta: {latest['edeta']:+.3f}")
        print(f"   策略信号: {'✅ 买入' if current_signal else '❌ 观望'}")
        
        # 5. 历史表现分析
        if signal_days > 0:
            print(f"\n💰 历史表现分析:")
            
            signal_data = df[df['signal']].copy()
            
            # 计算不同持有期的收益
            holding_periods = [30, 60, 90, 120]
            
            for days in holding_periods:
                returns = []
                
                for i, row in signal_data.iterrows():
                    entry_date = row['date']
                    entry_price = row['adj_close']
                    
                    # 找到持有期后的价格
                    exit_date = entry_date + pd.Timedelta(days=days)
                    future_data = df[df['date'] >= exit_date]
                    
                    if not future_data.empty:
                        exit_price = future_data.iloc[0]['adj_close']
                        return_rate = (exit_price - entry_price) / entry_price
                        returns.append(return_rate)
                
                if returns:
                    avg_return = np.mean(returns)
                    win_rate = sum(1 for r in returns if r > 0) / len(returns)
                    annual_return = avg_return * (signal_days / years)
                    
                    print(f"   {days}天持有: 平均收益{avg_return*100:+.2f}%, 胜率{win_rate*100:.1f}%, 年化{annual_return*100:+.2f}%")
        
        # 6. 与买入持有对比
        start_price = df.iloc[0]['adj_close']
        end_price = df.iloc[-1]['adj_close']
        buy_hold_return = (end_price - start_price) / start_price
        buy_hold_annual = ((1 + buy_hold_return) ** (1/years)) - 1
        
        print(f"\n📊 基准对比:")
        print(f"   买入持有总收益: {buy_hold_return*100:+.2f}%")
        print(f"   买入持有年化: {buy_hold_annual*100:+.2f}%")
        print(f"   数据年限: {years:.1f}年")
        
        # 7. Y值和X值分布分析
        print(f"\n📊 Y值和X值分析:")
        print(f"   Y值范围: {df['y_probability'].min():.3f} - {df['y_probability'].max():.3f}")
        print(f"   Y值平均: {df['y_probability'].mean():.3f}")
        print(f"   X值范围: {df['inflow_ratio'].min():.3f} - {df['inflow_ratio'].max():.3f}")
        print(f"   X值平均: {df['inflow_ratio'].mean():.3f}")
        
        # Y≥0.5和X≥0.5的比例
        y_ge_05 = (df['y_probability'] >= 0.5).sum()
        x_ge_05 = (df['inflow_ratio'] >= 0.5).sum()
        
        print(f"   Y≥0.5天数: {y_ge_05} ({y_ge_05/total_days*100:.1f}%)")
        print(f"   X≥0.5天数: {x_ge_05} ({x_ge_05/total_days*100:.1f}%)")
        
        # 8. 分年度信号分析
        df['year'] = df['date'].dt.year
        yearly_signals = df.groupby('year').agg({
            'signal': 'sum',
            'date': 'count',
            'y_probability': 'mean',
            'inflow_ratio': 'mean'
        }).rename(columns={'date': 'total_days'})
        
        yearly_signals['signal_rate'] = yearly_signals['signal'] / yearly_signals['total_days'] * 100
        
        print(f"\n📅 分年度信号分析:")
        print("年份   信号数  信号率%  平均Y值  平均X值")
        print("-" * 45)
        
        for year, stats in yearly_signals.iterrows():
            if stats['total_days'] > 50:  # 只显示有足够数据的年份
                print(f"{year}     {stats['signal']:>3}    {stats['signal_rate']:>5.1f}   {stats['y_probability']:>6.3f}  {stats['inflow_ratio']:>6.3f}")
        
        # 9. 策略评估
        print(f"\n💡 HK2800策略评估:")
        print("="*40)
        
        if signal_days / years >= 20:
            print("✅ 交易频率适中，有足够机会")
        elif signal_days / years >= 10:
            print("⚠️ 交易频率偏低，机会有限")
        else:
            print("❌ 交易频率过低，策略可能不适用")
        
        if latest['y_probability'] > 0.5:
            print("✅ 当前Y值健康，市场状态良好")
        else:
            print("⚠️ 当前Y值偏低，需谨慎操作")
        
        # 10. ETF特点分析
        print(f"\n📊 ETF特点分析:")
        print("="*30)
        
        # 价格波动性
        daily_returns = df['adj_close'].pct_change().dropna()
        volatility = daily_returns.std() * np.sqrt(252)  # 年化波动率
        
        print(f"   年化波动率: {volatility*100:.1f}%")
        print(f"   最大单日涨幅: {daily_returns.max()*100:+.2f}%")
        print(f"   最大单日跌幅: {daily_returns.min()*100:+.2f}%")
        
        # ETF vs 个股对比
        print(f"\n🔍 ETF vs 个股特点:")
        print("   ✅ ETF波动性相对较低")
        print("   ✅ 分散风险，追踪大盘")
        print("   ✅ 流动性好，交易成本低")
        print("   ⚠️ 个股选择效应有限")
        print("   ⚠️ 无法获得超额Alpha")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        if 'connection' in locals():
            connection.close()

if __name__ == "__main__":
    test_hk2800_strategy()
