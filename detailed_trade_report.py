#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
详细交易记录报告
==============

生成最详细的交易记录，包括每笔交易的完整信息
适合打印和详细分析

作者: Cosmoon NG
日期: 2025年7月16日
"""

import pymysql
import pandas as pd
import numpy as np
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# 数据库配置
db_config = {
    'host': 'localhost',
    'user': 'root',
    'password': '12345678',
    'database': 'finance',
    'charset': 'utf8mb4'
}

def generate_detailed_trade_report():
    """生成详细交易记录报告"""
    print("📊 生成详细交易记录报告")
    print("=" * 100)
    
    try:
        # 连接数据库
        conn = pymysql.connect(**db_config)
        
        # 获取最近2年的数据进行分析
        query = """
        SELECT Date, Open, High, Low, Close, Volume,
               MoneyFlowRatio, Full_Y, E, Controller
        FROM hkhsi50
        WHERE Date >= '2023-01-01'
        ORDER BY Date ASC
        """
        
        df = pd.read_sql(query, conn)
        conn.close()
        
        if df.empty:
            print("❌ 无数据")
            return
        
        print(f"📅 分析期间: {df['Date'].min()} 至 {df['Date'].max()}")
        print(f"📊 数据量: {len(df):,} 天")
        
        # 执行回测
        trades = execute_detailed_backtest(df)
        
        if not trades:
            print("❌ 无交易记录")
            return
        
        # 生成详细报告
        generate_trade_details(trades)
        save_detailed_report(trades, df)
        
    except Exception as e:
        print(f"❌ 生成报告失败: {e}")
        import traceback
        print(traceback.format_exc())

def execute_detailed_backtest(df):
    """执行详细回测"""
    print("🚀 执行详细回测...")
    
    # 交易参数
    initial_capital = 30000
    monthly_addition = 2000
    take_profit_long = 0.016
    stop_loss_long = 0.008
    take_profit_short = 0.008
    stop_loss_short = 0.016
    
    # 状态变量
    position = 0
    entry_price = 0
    entry_date = None
    entry_index = 0
    shares = 0
    capital = initial_capital
    last_month = None
    trade_id = 1
    
    trades = []
    
    for i in range(20, len(df)):
        row = df.iloc[i]
        date = pd.to_datetime(row['Date']).date()
        price = float(row['Close'])
        high = float(row['High'])
        low = float(row['Low'])
        open_price = float(row['Open'])
        
        # 每月增加资金
        current_month = date.replace(day=1)
        monthly_added = 0
        if last_month is None or current_month > last_month:
            last_month = current_month
            capital += monthly_addition
            monthly_added = monthly_addition
        
        # 检查退出条件
        if position != 0:
            should_exit = False
            exit_price = price
            exit_type = ''
            exit_reason = ''
            
            if position == 1:  # 多头
                profit_ratio = (high - entry_price) / entry_price
                loss_ratio = (entry_price - low) / entry_price
                
                if profit_ratio >= take_profit_long:
                    should_exit = True
                    exit_price = entry_price * (1 + take_profit_long)
                    exit_type = 'long_tp'
                    exit_reason = f'多头止盈 (涨幅{profit_ratio*100:.1f}%达到{take_profit_long*100}%)'
                elif loss_ratio >= stop_loss_long:
                    should_exit = True
                    exit_price = entry_price * (1 - stop_loss_long)
                    exit_type = 'long_sl'
                    exit_reason = f'多头止损 (跌幅{loss_ratio*100:.1f}%达到{stop_loss_long*100}%)'
            
            elif position == -1:  # 空头
                profit_ratio = (entry_price - low) / entry_price
                loss_ratio = (high - entry_price) / entry_price
                
                if profit_ratio >= take_profit_short:
                    should_exit = True
                    exit_price = entry_price * (1 - take_profit_short)
                    exit_type = 'short_tp'
                    exit_reason = f'空头止盈 (跌幅{profit_ratio*100:.1f}%达到{take_profit_short*100}%)'
                elif loss_ratio >= stop_loss_short:
                    should_exit = True
                    exit_price = entry_price * (1 + stop_loss_short)
                    exit_type = 'short_sl'
                    exit_reason = f'空头止损 (涨幅{loss_ratio*100:.1f}%达到{stop_loss_short*100}%)'
            
            if should_exit:
                # 计算详细信息
                holding_days = (date - entry_date).days
                entry_row = df.iloc[entry_index]
                
                if position == 1:
                    profit_amount = (exit_price - entry_price) * shares
                    capital += shares * exit_price
                else:
                    profit_amount = (entry_price - exit_price) * shares
                    capital -= shares * exit_price
                
                profit_pct = profit_amount / (entry_price * shares) * 100
                
                # 详细交易记录
                trade_detail = {
                    'trade_id': trade_id,
                    'direction': '多头' if position == 1 else '空头',
                    
                    # 开仓信息
                    'entry_date': entry_date,
                    'entry_price': round(entry_price, 2),
                    'entry_open': round(float(entry_row['Open']), 2),
                    'entry_high': round(float(entry_row['High']), 2),
                    'entry_low': round(float(entry_row['Low']), 2),
                    'entry_volume': int(entry_row['Volume']),
                    'entry_money_flow': round(float(entry_row['MoneyFlowRatio']), 4),
                    'entry_full_y': round(float(entry_row['Full_Y']), 4),
                    'entry_e_value': round(float(entry_row['E']), 4),
                    'entry_signal_strength': get_signal_strength(entry_row),
                    'entry_market_condition': get_market_condition(entry_row),
                    'entry_reason': get_entry_reason(entry_row, position),
                    
                    # 平仓信息
                    'exit_date': date,
                    'exit_price': round(exit_price, 2),
                    'exit_open': round(open_price, 2),
                    'exit_high': round(high, 2),
                    'exit_low': round(low, 2),
                    'exit_volume': int(row['Volume']),
                    'exit_money_flow': round(float(row['MoneyFlowRatio']), 4),
                    'exit_full_y': round(float(row['Full_Y']), 4),
                    'exit_e_value': round(float(row['E']), 4),
                    'exit_type': exit_type,
                    'exit_reason': exit_reason,
                    'exit_market_condition': get_market_condition(row),
                    
                    # 交易结果
                    'shares': round(shares, 2),
                    'investment_amount': round(entry_price * shares, 2),
                    'holding_days': holding_days,
                    'profit_amount': round(profit_amount, 2),
                    'profit_pct': round(profit_pct, 2),
                    'capital_before': round(capital - profit_amount, 2),
                    'capital_after': round(capital, 2),
                    'monthly_added': monthly_added,
                    
                    # 市场变化
                    'price_change': round(exit_price - entry_price, 2),
                    'price_change_pct': round((exit_price - entry_price) / entry_price * 100, 2),
                    'max_favorable': round(high - entry_price if position == 1 else entry_price - low, 2),
                    'max_adverse': round(entry_price - low if position == 1 else high - entry_price, 2),
                    
                    # 技术指标变化
                    'money_flow_change': round(float(row['MoneyFlowRatio']) - float(entry_row['MoneyFlowRatio']), 4),
                    'full_y_change': round(float(row['Full_Y']) - float(entry_row['Full_Y']), 4),
                    'e_value_change': round(float(row['E']) - float(entry_row['E']), 4)
                }
                
                trades.append(trade_detail)
                
                trade_id += 1
                position = 0
                entry_price = 0
                entry_date = None
                entry_index = 0
                shares = 0
        
        # 检查开仓信号
        if position == 0:
            signal = get_trading_signal(row)
            
            if signal in ['LONG', 'SHORT']:
                shares = (capital * 0.95) / price
                
                if shares > 0:
                    if signal == 'LONG':
                        capital -= shares * price
                        position = 1
                    else:
                        capital += shares * price
                        position = -1
                    
                    entry_price = price
                    entry_date = date
                    entry_index = i
    
    print(f"✅ 详细回测完成，生成 {len(trades)} 笔交易")
    return trades

def get_trading_signal(row):
    """获取交易信号"""
    e_val = float(row['E'])
    money_flow = float(row['MoneyFlowRatio'])
    full_y = float(row['Full_Y'])
    
    if e_val > 0 and money_flow > 0.45 and full_y > 0.45:
        return 'LONG'
    elif full_y < 0.25 or money_flow < 0.25:
        return 'SHORT'
    return 'HOLD'

def get_signal_strength(row):
    """获取信号强度"""
    e_val = float(row['E'])
    money_flow = float(row['MoneyFlowRatio'])
    full_y = float(row['Full_Y'])
    
    if e_val > 0.3 and money_flow > 0.6 and full_y > 0.6:
        return '强信号'
    elif e_val > 0 and money_flow > 0.45 and full_y > 0.45:
        return '中等信号'
    elif money_flow < 0.25 or full_y < 0.25:
        return '弱信号'
    else:
        return '无信号'

def get_market_condition(row):
    """获取市场状态"""
    money_flow = float(row['MoneyFlowRatio'])
    full_y = float(row['Full_Y'])
    
    if full_y > 0.5 and money_flow > 0.5:
        return '高值盈利区'
    elif full_y > 0.333 and full_y < 0.4:
        return '控股商控制区'
    elif full_y < 0.25 or money_flow < 0.25:
        return '强亏损区'
    else:
        return '其他区域'

def get_entry_reason(row, position):
    """获取开仓原因"""
    e_val = float(row['E'])
    money_flow = float(row['MoneyFlowRatio'])
    full_y = float(row['Full_Y'])
    
    if position == 1:  # 多头
        reasons = []
        if e_val > 0:
            reasons.append(f'E值{e_val:.3f}>0')
        if money_flow > 0.45:
            reasons.append(f'资金流{money_flow:.3f}>0.45')
        if full_y > 0.45:
            reasons.append(f'FullY{full_y:.3f}>0.45')
        return '买涨: ' + ', '.join(reasons)
    
    else:  # 空头
        reasons = []
        if full_y < 0.25:
            reasons.append(f'FullY{full_y:.3f}<0.25')
        if money_flow < 0.25:
            reasons.append(f'资金流{money_flow:.3f}<0.25')
        return '买跌: ' + ', '.join(reasons)

def generate_trade_details(trades):
    """生成交易详情"""
    print(f"\n📊 详细交易记录 (最近20笔)")
    print("=" * 150)
    
    # 显示最近20笔交易
    recent_trades = trades[-20:] if len(trades) > 20 else trades
    
    for i, trade in enumerate(recent_trades, 1):
        print(f"\n🔸 交易 #{trade['trade_id']} - {trade['direction']}")
        print("-" * 100)
        
        # 开仓信息
        print(f"📈 开仓: {trade['entry_date']} @{trade['entry_price']}")
        print(f"   • 当日行情: 开{trade['entry_open']} 高{trade['entry_high']} 低{trade['entry_low']} 收{trade['entry_price']}")
        print(f"   • 技术指标: 资金流{trade['entry_money_flow']} FullY{trade['entry_full_y']} E值{trade['entry_e_value']}")
        print(f"   • 市场状态: {trade['entry_market_condition']} ({trade['entry_signal_strength']})")
        print(f"   • 开仓原因: {trade['entry_reason']}")
        print(f"   • 投资金额: {trade['investment_amount']:,.0f}港币 ({trade['shares']:.2f}股)")
        
        # 平仓信息
        print(f"📉 平仓: {trade['exit_date']} @{trade['exit_price']} (持仓{trade['holding_days']}天)")
        print(f"   • 当日行情: 开{trade['exit_open']} 高{trade['exit_high']} 低{trade['exit_low']} 收{trade['exit_price']}")
        print(f"   • 技术指标: 资金流{trade['exit_money_flow']} FullY{trade['exit_full_y']} E值{trade['exit_e_value']}")
        print(f"   • 市场状态: {trade['exit_market_condition']}")
        print(f"   • 平仓原因: {trade['exit_reason']}")
        
        # 交易结果
        profit_color = "💰" if trade['profit_amount'] > 0 else "💸"
        print(f"{profit_color} 交易结果:")
        print(f"   • 价格变化: {trade['entry_price']} → {trade['exit_price']} ({trade['price_change_pct']:+.2f}%)")
        print(f"   • 盈亏金额: {trade['profit_amount']:+,.0f}港币 ({trade['profit_pct']:+.2f}%)")
        print(f"   • 资金变化: {trade['capital_before']:,.0f} → {trade['capital_after']:,.0f}")
        print(f"   • 最大有利: {trade['max_favorable']:+.2f} 最大不利: {trade['max_adverse']:+.2f}")
        
        # 指标变化
        print(f"📊 指标变化:")
        print(f"   • 资金流: {trade['entry_money_flow']} → {trade['exit_money_flow']} ({trade['money_flow_change']:+.4f})")
        print(f"   • FullY: {trade['entry_full_y']} → {trade['exit_full_y']} ({trade['full_y_change']:+.4f})")
        print(f"   • E值: {trade['entry_e_value']} → {trade['exit_e_value']} ({trade['e_value_change']:+.4f})")

def save_detailed_report(trades, df):
    """保存详细报告"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"超详细交易记录_{timestamp}.xlsx"
    
    # 转换为DataFrame
    trades_df = pd.DataFrame(trades)
    
    with pd.ExcelWriter(filename, engine='openpyxl') as writer:
        # 详细交易记录
        trades_df.to_excel(writer, sheet_name='详细交易记录', index=False)
        
        # 汇总统计
        if len(trades_df) > 0:
            total_trades = len(trades_df)
            winning_trades = len(trades_df[trades_df['profit_amount'] > 0])
            
            summary_data = [
                ['交易统计', ''],
                ['总交易次数', total_trades],
                ['盈利次数', winning_trades],
                ['亏损次数', total_trades - winning_trades],
                ['胜率(%)', round(winning_trades/total_trades*100, 2)],
                ['', ''],
                ['盈亏统计', ''],
                ['总盈亏', trades_df['profit_amount'].sum()],
                ['平均盈亏', round(trades_df['profit_amount'].mean(), 2)],
                ['最大盈利', trades_df['profit_amount'].max()],
                ['最大亏损', trades_df['profit_amount'].min()],
                ['', ''],
                ['持仓统计', ''],
                ['平均持仓天数', round(trades_df['holding_days'].mean(), 1)],
                ['最长持仓', trades_df['holding_days'].max()],
                ['最短持仓', trades_df['holding_days'].min()],
                ['', ''],
                ['资金统计', ''],
                ['初始资金', 30000],
                ['最终资金', trades_df['capital_after'].iloc[-1]],
                ['总收益', trades_df['capital_after'].iloc[-1] - 30000],
                ['收益率(%)', round((trades_df['capital_after'].iloc[-1] / 30000 - 1) * 100, 2)]
            ]
            
            summary_df = pd.DataFrame(summary_data, columns=['指标', '数值'])
            summary_df.to_excel(writer, sheet_name='汇总统计', index=False)
    
    print(f"\n✅ 超详细交易记录已保存至: {filename}")
    print(f"📊 包含 {len(trades)} 笔交易的完整信息")

def main():
    """主函数"""
    generate_detailed_trade_report()

if __name__ == "__main__":
    main()
