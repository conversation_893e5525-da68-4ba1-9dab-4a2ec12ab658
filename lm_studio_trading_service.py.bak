#!/usr/bin/env python3
"""
LM Studio 交易计算服务接轨程序
与 MariaDB 数据库集成，提供交易计算功能
"""

import json
import logging
import mysql.connector
from flask import Flask, request, jsonify
from flask_cors import CORS
import requests
from datetime import datetime
import traceback

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = Flask(__name__)
CORS(app)

# 数据库配置
DB_CONFIG = {
    'host': 'localhost',
    'port': 3306,
    'user': 'root',
    'password': 'your_password',  # 请修改为您的密码
    'database': 'your_database',  # 请修改为您的数据库名
    'charset': 'utf8mb4'
}

# LM Studio 配置
LM_STUDIO_CONFIG = {
    'base_url': 'http://localhost:1234/v1',  # LM Studio 默认地址
    'model': 'your-model-name'  # 请修改为您使用的模型名
}

class TradingCalculatorService:
    """交易计算服务类"""
    
    def __init__(self):
        self.db_config = DB_CONFIG
        
    def get_db_connection(self):
        """获取数据库连接"""
        try:
            connection = mysql.connector.connect(**self.db_config)
            return connection
        except Exception as e:
            logger.error(f"数据库连接失败: {e}")
            raise
    
    def execute_stored_procedure(self, table_name):
        """执行存储过程更新交易数据"""
        try:
            connection = self.get_db_connection()
            cursor = connection.cursor()
            
            # 调用存储过程
            cursor.callproc('sp_updatecontroller_enhanced', [table_name])
            
            # 获取结果
            results = []
            for result in cursor.stored_results():
                results.extend(result.fetchall())
            
            # 获取 k 值
            cursor.execute("SELECT @_sp_updatecontroller_enhanced_1 as k_value")
            k_value = cursor.fetchone()[0]
            
            connection.commit()
            cursor.close()
            connection.close()
            
            return {
                'success': True,
                'k_value': float(k_value) if k_value else 0,
                'messages': results,
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"存储过程执行失败: {e}")
            return {
                'success': False,
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }
    
    def get_trading_data(self, table_name, limit=10):
        """获取交易数据"""
        try:
            connection = self.get_db_connection()
            cursor = connection.cursor(dictionary=True)
            
            query = f"""
            SELECT 
                id, date, open, high, low, close, 
                midprice, controller, Full_Y, E, MFI
            FROM `{table_name}` 
            ORDER BY date DESC, id DESC 
            LIMIT %s
            """
            
            cursor.execute(query, (limit,))
            data = cursor.fetchall()
            
            cursor.close()
            connection.close()
            
            return {
                'success': True,
                'data': data,
                'count': len(data),
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"获取交易数据失败: {e}")
            return {
                'success': False,
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }
    
    def get_statistics(self, table_name):
        """获取统计信息"""
        try:
            connection = self.get_db_connection()
            cursor = connection.cursor(dictionary=True)
            
            query = f"""
            SELECT 
                COUNT(*) as total_records,
                SUM(CASE WHEN controller = 0 THEN 1 ELSE 0 END) as controller_0,
                SUM(CASE WHEN controller = 1 THEN 1 ELSE 0 END) as controller_1,
                SUM(CASE WHEN controller = 3 THEN 1 ELSE 0 END) as controller_3,
                MIN(Full_Y) as min_full_y,
                MAX(Full_Y) as max_full_y,
                AVG(Full_Y) as avg_full_y,
                MIN(E) as min_e,
                MAX(E) as max_e,
                AVG(E) as avg_e,
                COUNT(CASE WHEN E IS NOT NULL THEN 1 END) as e_calculated,
                COUNT(CASE WHEN MFI IS NOT NULL THEN 1 END) as mfi_available
            FROM `{table_name}`
            """
            
            cursor.execute(query)
            stats = cursor.fetchone()
            
            cursor.close()
            connection.close()
            
            return {
                'success': True,
                'statistics': stats,
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"获取统计信息失败: {e}")
            return {
                'success': False,
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }

# 创建服务实例
trading_service = TradingCalculatorService()

# API 路由
@app.route('/api/health', methods=['GET'])
def health_check():
    """健康检查"""
    return jsonify({
        'status': 'healthy',
        'service': 'LM Studio Trading Calculator Service',
        'timestamp': datetime.now().isoformat()
    })

@app.route('/api/update_trading_data', methods=['POST'])
def update_trading_data():
    """更新交易数据"""
    try:
        data = request.get_json()
        table_name = data.get('table_name')
        
        if not table_name:
            return jsonify({
                'success': False,
                'error': 'table_name is required'
            }), 400
        
        result = trading_service.execute_stored_procedure(table_name)
        return jsonify(result)
        
    except Exception as e:
        logger.error(f"更新交易数据API错误: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/get_trading_data', methods=['GET'])
def get_trading_data():
    """获取交易数据"""
    try:
        table_name = request.args.get('table_name')
        limit = int(request.args.get('limit', 10))
        
        if not table_name:
            return jsonify({
                'success': False,
                'error': 'table_name is required'
            }), 400
        
        result = trading_service.get_trading_data(table_name, limit)
        return jsonify(result)
        
    except Exception as e:
        logger.error(f"获取交易数据API错误: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/get_statistics', methods=['GET'])
def get_statistics():
    """获取统计信息"""
    try:
        table_name = request.args.get('table_name')
        
        if not table_name:
            return jsonify({
                'success': False,
                'error': 'table_name is required'
            }), 400
        
        result = trading_service.get_statistics(table_name)
        return jsonify(result)
        
    except Exception as e:
        logger.error(f"获取统计信息API错误: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/lm_studio_chat', methods=['POST'])
def lm_studio_chat():
    """与 LM Studio 进行对话"""
    try:
        data = request.get_json()
        message = data.get('message', '')
        table_name = data.get('table_name', '')
        
        # 如果消息包含交易相关关键词，自动获取数据
        trading_keywords = ['交易', 'trading', '数据', 'data', '统计', 'statistics', 'k值', 'controller']
        
        context = ""
        if any(keyword in message.lower() for keyword in trading_keywords) and table_name:
            # 获取最新的统计信息
            stats_result = trading_service.get_statistics(table_name)
            if stats_result['success']:
                context = f"\n\n当前交易数据统计:\n{json.dumps(stats_result['statistics'], indent=2, ensure_ascii=False)}"
        
        # 发送到 LM Studio
        lm_response = requests.post(
            f"{LM_STUDIO_CONFIG['base_url']}/chat/completions",
            json={
                "model": LM_STUDIO_CONFIG['model'],
                "messages": [
                    {
                        "role": "system",
                        "content": "你是一个专业的交易数据分析助手。你可以帮助分析交易数据、计算指标和提供投资建议。"
                    },
                    {
                        "role": "user",
                        "content": message + context
                    }
                ],
                "temperature": 0.7,
                "max_tokens": 1000
            },
            headers={"Content-Type": "application/json"}
        )
        
        if lm_response.status_code == 200:
            lm_data = lm_response.json()
            return jsonify({
                'success': True,
                'response': lm_data['choices'][0]['message']['content'],
                'context_included': bool(context),
                'timestamp': datetime.now().isoformat()
            })
        else:
            return jsonify({
                'success': False,
                'error': f"LM Studio API 错误: {lm_response.status_code}"
            }), 500
            
    except Exception as e:
        logger.error(f"LM Studio 对话API错误: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

if __name__ == '__main__':
    print("🚀 启动 LM Studio 交易计算服务...")
    print("📊 API 端点:")
    print("   - GET  /api/health - 健康检查")
    print("   - POST /api/update_trading_data - 更新交易数据")
    print("   - GET  /api/get_trading_data - 获取交易数据")
    print("   - GET  /api/get_statistics - 获取统计信息")
    print("   - POST /api/lm_studio_chat - LM Studio 对话")
    print("🔗 服务地址: http://localhost:5000")
    
    app.run(host='0.0.0.0', port=5000, debug=True)
