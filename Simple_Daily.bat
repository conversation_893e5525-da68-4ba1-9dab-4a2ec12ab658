@echo off
title Daily Update System

echo ========================================
echo Daily Update System
echo ========================================
echo Date: %date%
echo Time: %time%
echo.

REM Change to working directory
cd /d "D:\Users\Cosmoon NG\Documents\VSCode\Python\Finance\EAs\Investment02"

REM Check if we're in the right directory
if not exist "complete_daily_update_with_position.py" (
    echo ERROR: Cannot find required files
    echo Current directory: %cd%
    pause
    exit /b 1
)

echo Working directory: %cd%
echo.

REM Check Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python not found
    pause
    exit /b 1
)

echo Python check: OK
echo.

REM Execute the main script
echo Starting daily update...
echo.

python complete_daily_update_with_position.py

echo.
echo ========================================
echo Update completed at %time%
echo ========================================

REM Check Excel file
if exist "交易记录追踪0023HK.xlsx" (
    echo Excel file: OK
) else (
    echo Excel file: NOT FOUND
)

echo.
echo Press any key to exit...
pause >nul
