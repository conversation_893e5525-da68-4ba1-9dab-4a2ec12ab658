#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试修复后的代码
================

验证所有修复是否成功

作者: Cosmoon NG
"""

import importlib.util

def test_import(module_name, file_path):
    """测试模块导入"""
    try:
        spec = importlib.util.spec_from_file_location(module_name, file_path)
        module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(module)
        print(f"✅ {module_name}: 导入成功")
        return True
    except Exception as e:
        print(f"❌ {module_name}: 导入失败 - {e}")
        return False

def main():
    """主函数"""
    print("🔧 测试修复后的代码")
    print("=" * 50)

    # 测试文件列表
    test_files = [
        ("700HK", "700HK.py"),
        ("backtest_0023hk_final", "backtest_0023hk_final.py"),
        ("visualize_700hk_results", "visualize_700hk_results.py"),
        ("visualize_700hk_english", "visualize_700hk_english.py"),
    ]

    success_count = 0
    total_count = len(test_files)

    for module_name, file_path in test_files:
        if test_import(module_name, file_path):
            success_count += 1

    print(f"\n📊 测试结果:")
    print(f"   成功: {success_count}/{total_count}")
    print(f"   成功率: {success_count/total_count*100:.1f}%")

    if success_count == total_count:
        print(f"\n🎉 所有修复成功！")
        print(f"✅ 代码质量问题已解决")
        print(f"✅ 未使用的导入已清理")
        print(f"✅ 未使用的变量已修复")
    else:
        print(f"\n⚠️ 还有 {total_count - success_count} 个问题需要解决")

if __name__ == "__main__":
    main()
