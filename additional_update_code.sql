-- 在现有存储过程中添加以下代码片段（在controller更新之后）

    -- 添加Full_Y列检查和更新
    SET @sql = CONCAT(
        'SELECT COUNT(*) INTO @col_exists FROM information_schema.COLUMNS ',
        'WHERE TABLE_SCHEMA=DATABASE() AND TABLE_NAME=''', tablename, ''' AND COLUMN_NAME=''Full_Y'''
    );
    PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;
    SET col_exists = @col_exists;

    IF col_exists = 0 THEN
        SET @sql = CONCAT('ALTER TABLE `', tablename, '` ADD COLUMN `Full_Y` DECIMAL(10,3)');
        PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;
        SELECT 'Full_Y列已添加' AS full_y_status;
    END IF;

    -- 更新Full_Y字段
    SET @sql = CONCAT(
        'UPDATE `', tablename, '` ',
        'SET Full_Y = ROUND((high + low + close) / 3, 3) ',
        'WHERE high IS NOT NULL AND low IS NOT NULL AND close IS NOT NULL'
    );
    PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;
    SELECT 'Full_Y字段更新完成' AS full_y_update_status;

    -- 添加E列检查和更新
    SET @sql = CONCAT(
        'SELECT COUNT(*) INTO @col_exists FROM information_schema.COLUMNS ',
        'WHERE TABLE_SCHEMA=DATABASE() AND TABLE_NAME=''', tablename, ''' AND COLUMN_NAME=''E'''
    );
    PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;
    SET col_exists = @col_exists;

    IF col_exists = 0 THEN
        SET @sql = CONCAT('ALTER TABLE `', tablename, '` ADD COLUMN `E` DECIMAL(10,6)');
        PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;
        SELECT 'E列已添加' AS e_status;
    END IF;

    -- 更新E字段
    SET @sql = CONCAT(
        'UPDATE `', tablename, '` ',
        'SET E = ROUND((close - open) / NULLIF(open, 0), 6) ',
        'WHERE open IS NOT NULL AND close IS NOT NULL AND open != 0'
    );
    PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;
    SELECT 'E字段更新完成' AS e_update_status;
