#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
HSI50指数回测系统
=================

策略：
- 使用20年历史数据回测
- 初始资金：30,000
- 每月定投：3,000
- 交易信号：回归中线
- 头寸管理：凯利公式
- 复利计算

作者: Cascade
日期: 2025年7月
"""

import pymysql
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from scipy import stats
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# 数据库配置
DB_CONFIG = {
    'host': 'localhost',
    'user': 'root',
    'password': '12345678',
    'database': 'finance',
    'charset': 'utf8mb4'
}

# 回测参数
INITIAL_CAPITAL = 30000  # 初始资金
MONTHLY_INVESTMENT = 3000  # 每月定投金额
RISK_FREE_RATE = 0.02  # 无风险利率
TRANSACTION_COST_RATE = 0.001  # 交易成本率

class KellyBacktester:
    def __init__(self):
        self.data = None
        self.signals = pd.DataFrame()
        self.portfolio = pd.DataFrame()
        
    def load_data(self):
        """从数据库加载HSI50历史数据"""
        print("正在加载数据...")
        conn = pymysql.connect(**DB_CONFIG)
        query = """
        SELECT Date, Open, High, Low, Close, Volume 
        FROM hkhsi50 
        WHERE Date >= DATE_SUB(CURDATE(), INTERVAL 20 YEAR)
        ORDER BY Date
        """
        self.data = pd.read_sql(query, conn, parse_dates=['Date'], index_col='Date')
        conn.close()
        print(f"加载完成，共 {len(self.data)} 条数据")
        
    def calculate_indicators(self, window=20):
        """计算技术指标"""
        print("正在计算技术指标...")
        df = self.data.copy()
        
        # 计算收益率
        df['Returns'] = df['Close'].pct_change()
        
        # 计算回归中线（使用滚动线性回归）
        df['Regression_Line'] = df['Close'].rolling(window=window).apply(
            lambda x: np.poly1d(np.polyfit(range(len(x)), x, 1))(len(x)-1))
        
        # 计算价格与回归线的偏离度
        df['Deviation'] = (df['Close'] - df['Regression_Line']) / df['Regression_Line']
        
        # 计算波动率（用于凯利公式）
        df['Volatility'] = df['Returns'].rolling(window=window).std() * np.sqrt(252)
        
        self.data = df.dropna()
        
    def generate_signals(self, entry_threshold=0.02, exit_threshold=0.005):
        """生成交易信号"""
        print("正在生成交易信号...")
        df = self.data.copy()
        
        # 初始化信号列
        df['Signal'] = 0
        df['Position'] = 0
        
        # 生成交易信号
        for i in range(1, len(df)):
            # 当价格低于回归线且偏离度超过阈值时买入
            if df['Deviation'][i] < -entry_threshold and df['Signal'][i-1] <= 0:
                df['Signal'][i] = 1
            # 当价格高于回归线且偏离度超过阈值时卖出
            elif df['Deviation'][i] > entry_threshold and df['Signal'][i-1] >= 0:
                df['Signal'][i] = -1
            else:
                df['Signal'][i] = df['Signal'][i-1]
                
        self.signals = df[['Signal']]
        
    def calculate_kelly_position(self, window=252):
        """使用凯利公式计算头寸规模"""
        print("正在计算凯利头寸...")
        df = self.data.join(self.signals)
        
        # 计算历史收益率统计
        returns = df['Returns'].dropna()
        win_rate = len(returns[returns > 0]) / len(returns)
        avg_win = returns[returns > 0].mean()
        avg_loss = abs(returns[returns < 0].mean())
        
        # 凯利公式：f* = (bp - q) / b
        # 其中 b = 平均盈利/平均亏损, p = 胜率, q = 1 - p
        if avg_loss != 0 and avg_win != 0:
            b = avg_win / avg_loss
            kelly_fraction = (b * win_rate - (1 - win_rate)) / b
            # 保守起见，使用半凯利
            kelly_fraction = max(0, min(kelly_fraction * 0.5, 0.95))
        else:
            kelly_fraction = 0.5  # 默认值
            
        print(f"凯利头寸比例: {kelly_fraction:.2%}")
        return kelly_fraction
    
    def backtest(self):
        """运行回测"""
        print("开始回测...")
        df = self.data.join(self.signals)
        kelly_fraction = self.calculate_kelly_position()
        
        # 初始化投资组合
        portfolio = pd.DataFrame(index=df.index)
        portfolio['Close'] = df['Close']
        portfolio['Signal'] = df['Signal']
        
        # 初始化账户
        portfolio['Cash'] = 0.0
        portfolio['Shares'] = 0.0
        portfolio['Total'] = 0.0
        portfolio['Investment'] = 0.0
        
        # 初始资金
        cash = INITIAL_CAPITAL
        shares = 0
        investment = INITIAL_CAPITAL
        
        # 记录上次定投日期
        last_investment_date = None
        
        for i, (date, row) in enumerate(portfolio.iterrows()):
            # 每月定投
            if last_investment_date is None or (date.month != last_investment_date.month or 
                                              date.year != last_investment_date.year):
                cash += MONTHLY_INVESTMENT
                investment += MONTHLY_INVESTMENT
                last_investment_date = date
            
            # 计算当前头寸价值
            position_value = shares * row['Close']
            total = cash + position_value
            
            # 凯利头寸管理
            target_position_value = total * kelly_fraction
            
            # 生成交易信号
            if row['Signal'] == 1 and position_value < target_position_value * 0.99:  # 买入信号
                # 计算需要买入的金额
                buy_amount = min(cash, (target_position_value - position_value) / row['Close'] * row['Close'])
                if buy_amount > 0:
                    shares_to_buy = buy_amount / row['Close']
                    shares += shares_to_buy
                    cash -= buy_amount * (1 + TRANSACTION_COST_RATE)
            
            elif row['Signal'] == -1 and position_value > 0:  # 卖出信号
                # 卖出所有头寸
                sell_amount = shares * row['Close']
                cash += sell_amount * (1 - TRANSACTION_COST_RATE)
                shares = 0
            
            # 更新投资组合
            portfolio.at[date, 'Cash'] = cash
            portfolio.at[date, 'Shares'] = shares
            portfolio.at[date, 'Total'] = cash + shares * row['Close']
            portfolio.at[date, 'Investment'] = investment
            
            # 计算收益率
            if i > 0:
                prev_total = portfolio.iloc[i-1]['Total']
                portfolio.at[date, 'Return'] = (portfolio.at[date, 'Total'] / prev_total) - 1
        
        self.portfolio = portfolio
        return portfolio
    
    def analyze_results(self):
        """分析回测结果"""
        if self.portfolio.empty:
            print("没有可分析的回测结果")
            return
            
        portfolio = self.portfolio
        
        # 计算累计收益率
        portfolio['Cumulative_Return'] = (1 + portfolio['Return'].fillna(0)).cumprod() - 1
        
        # 计算年化收益率
        total_years = (portfolio.index[-1] - portfolio.index[0]).days / 365.25
        cagr = (portfolio['Total'].iloc[-1] / portfolio['Investment'].iloc[-1]) ** (1/total_years) - 1
        
        # 计算最大回撤
        portfolio['Peak'] = portfolio['Total'].cummax()
        portfolio['Drawdown'] = (portfolio['Total'] - portfolio['Peak']) / portfolio['Peak']
        max_drawdown = portfolio['Drawdown'].min()
        
        # 计算夏普比率
        excess_returns = portfolio['Return'] - (RISK_FREE_RATE / 252)
        sharpe_ratio = np.sqrt(252) * excess_returns.mean() / portfolio['Return'].std()
        
        # 输出结果
        print("\n" + "="*50)
        print(f"回测结果 (20年HSI50指数)")
        print("="*50)
        print(f"回测期间: {portfolio.index[0].date()} 至 {portfolio.index[-1].date()}")
        print(f"初始资金: ${INITIAL_CAPITAL:,.2f}")
        print(f"最终资产: ${portfolio['Total'].iloc[-1]:,.2f}")
        print(f"总投资额: ${portfolio['Investment'].iloc[-1]:,.2f}")
        print(f"总收益率: {((portfolio['Total'].iloc[-1] / portfolio['Investment'].iloc[-1]) - 1)*100:.2f}%")
        print(f"年化收益率: {cagr*100:.2f}%")
        print(f"最大回撤: {max_drawdown*100:.2f}%")
        print(f"夏普比率: {sharpe_ratio:.2f}")
        print("="*50 + "\n")
        
        # 绘制结果
        self.plot_results()
    
    def plot_results(self):
        """绘制回测结果"""
        plt.figure(figsize=(15, 10))
        
        # 绘制资产曲线
        plt.subplot(2, 1, 1)
        plt.plot(self.portfolio.index, self.portfolio['Total'], label='投资组合价值', color='blue')
        plt.plot(self.portfolio.index, self.portfolio['Investment'], label='总投资额', color='green', linestyle='--')
        plt.title('投资组合表现')
        plt.legend()
        plt.grid(True)
        
        # 绘制回撤
        plt.subplot(2, 1, 2)
        plt.fill_between(self.portfolio.index, self.portfolio['Drawdown']*100, 0, 
                        color='red', alpha=0.3)
        plt.title('回撤 (%)')
        plt.grid(True)
        
        plt.tight_layout()
        plt.show()

def main():
    print("HSI50指数回测系统 - 回归中线 + 凯利公式策略")
    print("="*60)
    
    # 创建回测器实例
    backtester = KellyBacktester()
    
    try:
        # 加载数据
        backtester.load_data()
        
        # 计算技术指标
        backtester.calculate_indicators()
        
        # 生成交易信号
        backtester.generate_signals()
        
        # 运行回测
        backtester.backtest()
        
        # 分析结果
        backtester.analyze_results()
        
    except Exception as e:
        print(f"❌ 发生错误: {str(e)}")
        import traceback
        traceback.print_exc()
    
    print("\n回测完成！")

if __name__ == "__main__":
    main()
