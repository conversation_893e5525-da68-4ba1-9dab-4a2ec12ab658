#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
验证数据库中XYE计算的正确性
对比手工计算和数据库存储的值
"""

import yfinance as yf
import pandas as pd
import numpy as np
import mysql.connector
from datetime import datetime, timedelta

def get_database_latest_xye():
    """从数据库获取最新的XYE数据"""
    try:
        config = {
            'host': 'localhost',
            'port': 3306,
            'user': 'root',
            'password': '12345678',
            'database': 'finance',
            'charset': 'utf8mb4'
        }

        connection = mysql.connector.connect(**config)
        cursor = connection.cursor()

        query = """
        SELECT Date, Close, High, Low, Volume, Y_Value, X_Value, E_Value, MFI, RSI
        FROM eab_0023hk
        ORDER BY Date DESC
        LIMIT 1
        """

        cursor.execute(query)
        result = cursor.fetchone()

        if result:
            columns = ['Date', 'Close', 'High', 'Low', 'Volume', 'Y_Value', 'X_Value', 'E_Value', 'MFI', 'RSI']
            data = dict(zip(columns, result))

            # 转换数值类型
            for key in ['Close', 'High', 'Low', 'Volume', 'Y_Value', 'X_Value', 'E_Value', 'MFI', 'RSI']:
                if key in data and data[key] is not None:
                    data[key] = float(data[key])

            cursor.close()
            connection.close()
            return data
        else:
            cursor.close()
            connection.close()
            return None

    except Exception as e:
        print(f"❌ 数据库查询失败: {e}")
        return None

def calculate_xye_manually(target_date=None):
    """手工计算XYE值进行验证"""
    try:
        print("📊 手工计算XYE值...")

        # 获取历史数据
        ticker = yf.Ticker("0023.HK")
        hist = ticker.history(period="100d")  # 获取足够的历史数据

        if len(hist) == 0:
            print("❌ 无法获取历史数据")
            return None

        print(f"✅ 获取到 {len(hist)} 天的历史数据")

        # 重置索引
        hist.reset_index(inplace=True)
        hist['Date'] = hist['Date'].dt.date

        # 1. 计算Y值 (价格在20日区间的位置)
        window = 20
        hist['high_20'] = hist['High'].rolling(window).max()
        hist['low_20'] = hist['Low'].rolling(window).min()
        hist['y_value'] = (hist['Close'] - hist['low_20']) / (hist['high_20'] - hist['low_20'])
        hist['y_value'] = hist['y_value'].fillna(0.5).clip(0, 1)

        # 2. 计算X值 (基于MFI)
        # 典型价格
        hist['typical_price'] = (hist['High'] + hist['Low'] + hist['Close']) / 3
        hist['money_flow'] = hist['typical_price'] * hist['Volume']
        hist['price_change'] = hist['typical_price'].diff()

        # 正负资金流
        hist['positive_mf'] = np.where(hist['price_change'] > 0, hist['money_flow'], 0)
        hist['negative_mf'] = np.where(hist['price_change'] < 0, hist['money_flow'], 0)

        # 14日资金流比率
        period = 14
        hist['positive_mf_14'] = hist['positive_mf'].rolling(period).sum()
        hist['negative_mf_14'] = hist['negative_mf'].rolling(period).sum()
        hist['money_flow_ratio'] = hist['positive_mf_14'] / (hist['negative_mf_14'] + 1e-10)

        # MFI和X值
        hist['mfi'] = 100 - (100 / (1 + hist['money_flow_ratio']))
        hist['x_value'] = hist['mfi'] / 100

        # 3. 计算E值 (Cosmoon公式)
        hist['e_value'] = (8 * hist['x_value'] - 3) * hist['y_value'] - 3 * hist['x_value'] + 1

        # 4. 计算RSI
        delta = hist['Close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
        rs = gain / (loss + 1e-10)
        hist['rsi'] = 100 - (100 / (1 + rs))

        # 获取指定日期或最新数据
        if target_date:
            # 查找指定日期的数据
            target_rows = hist[hist['Date'] == target_date]
            if len(target_rows) > 0:
                latest = target_rows.iloc[-1]
                print(f"✅ 找到目标日期 {target_date} 的数据")
            else:
                print(f"⚠️ 未找到目标日期 {target_date} 的数据，使用最新数据")
                latest = hist.iloc[-1]
        else:
            latest = hist.iloc[-1]

        return {
            'Date': latest['Date'],
            'Close': latest['Close'],
            'High': latest['High'],
            'Low': latest['Low'],
            'Volume': latest['Volume'],
            'Y_Value': latest['y_value'],
            'X_Value': latest['x_value'],
            'E_Value': latest['e_value'],
            'MFI': latest['mfi'],
            'RSI': latest['rsi'],
            'high_20': latest['high_20'],
            'low_20': latest['low_20'],
            'money_flow_ratio': latest['money_flow_ratio']
        }

    except Exception as e:
        print(f"❌ 手工计算失败: {e}")
        return None

def compare_calculations():
    """对比数据库和手工计算的结果"""
    print("🔍 XYE计算验证工具")
    print("=" * 60)

    # 获取数据库数据
    print("1. 从数据库获取最新XYE数据...")
    db_data = get_database_latest_xye()

    if not db_data:
        print("❌ 无法获取数据库数据")
        return

    print(f"✅ 数据库数据获取成功: {db_data['Date']}")

    # 手工计算（使用数据库相同的日期）
    print("\n2. 手工计算XYE值...")
    db_date = db_data['Date']
    manual_data = calculate_xye_manually(target_date=db_date)

    if not manual_data:
        print("❌ 手工计算失败")
        return

    print(f"✅ 手工计算完成: {manual_data['Date']}")

    # 对比结果
    print(f"\n📊 计算结果对比:")
    print("=" * 60)

    # 基础数据对比
    print(f"📅 日期对比:")
    print(f"   数据库: {db_data['Date']}")
    print(f"   手工计算: {manual_data['Date']}")
    print(f"   是否一致: {'✅' if str(db_data['Date']) == str(manual_data['Date']) else '❌'}")

    print(f"\n💰 价格数据对比:")
    print(f"   收盘价 - 数据库: {db_data['Close']:.4f}, 手工: {manual_data['Close']:.4f}, 差异: {abs(db_data['Close'] - manual_data['Close']):.6f}")
    print(f"   最高价 - 数据库: {db_data['High']:.4f}, 手工: {manual_data['High']:.4f}, 差异: {abs(db_data['High'] - manual_data['High']):.6f}")
    print(f"   最低价 - 数据库: {db_data['Low']:.4f}, 手工: {manual_data['Low']:.4f}, 差异: {abs(db_data['Low'] - manual_data['Low']):.6f}")

    # XYE指标对比
    print(f"\n🎯 XYE指标对比:")

    # Y值对比
    y_diff = abs(db_data['Y_Value'] - manual_data['Y_Value'])
    y_match = y_diff < 0.001
    print(f"   Y值 - 数据库: {db_data['Y_Value']:.6f}, 手工: {manual_data['Y_Value']:.6f}")
    print(f"        差异: {y_diff:.6f} {'✅' if y_match else '❌'}")

    # X值对比
    x_diff = abs(db_data['X_Value'] - manual_data['X_Value'])
    x_match = x_diff < 0.001
    print(f"   X值 - 数据库: {db_data['X_Value']:.6f}, 手工: {manual_data['X_Value']:.6f}")
    print(f"        差异: {x_diff:.6f} {'✅' if x_match else '❌'}")

    # E值对比
    e_diff = abs(db_data['E_Value'] - manual_data['E_Value'])
    e_match = e_diff < 0.001
    print(f"   E值 - 数据库: {db_data['E_Value']:.6f}, 手工: {manual_data['E_Value']:.6f}")
    print(f"        差异: {e_diff:.6f} {'✅' if e_match else '❌'}")

    # MFI对比
    mfi_diff = abs(db_data['MFI'] - manual_data['MFI'])
    mfi_match = mfi_diff < 0.1
    print(f"   MFI - 数据库: {db_data['MFI']:.4f}, 手工: {manual_data['MFI']:.4f}")
    print(f"        差异: {mfi_diff:.4f} {'✅' if mfi_match else '❌'}")

    # RSI对比
    rsi_diff = abs(db_data['RSI'] - manual_data['RSI'])
    rsi_match = rsi_diff < 0.1
    print(f"   RSI - 数据库: {db_data['RSI']:.4f}, 手工: {manual_data['RSI']:.4f}")
    print(f"        差异: {rsi_diff:.4f} {'✅' if rsi_match else '❌'}")

    # 详细计算过程
    print(f"\n🔍 Y值计算详情:")
    print(f"   20日最高价: {manual_data['high_20']:.4f}")
    print(f"   20日最低价: {manual_data['low_20']:.4f}")
    print(f"   当前收盘价: {manual_data['Close']:.4f}")
    print(f"   Y值公式: ({manual_data['Close']:.4f} - {manual_data['low_20']:.4f}) / ({manual_data['high_20']:.4f} - {manual_data['low_20']:.4f})")
    print(f"   Y值结果: {manual_data['Y_Value']:.6f}")

    print(f"\n🔍 X值计算详情:")
    print(f"   资金流比率: {manual_data['money_flow_ratio']:.6f}")
    print(f"   MFI: 100 - (100 / (1 + {manual_data['money_flow_ratio']:.6f})) = {manual_data['MFI']:.4f}")
    print(f"   X值: {manual_data['MFI']:.4f} / 100 = {manual_data['X_Value']:.6f}")

    print(f"\n🔍 E值计算详情:")
    print(f"   E值公式: (8 * {manual_data['X_Value']:.6f} - 3) * {manual_data['Y_Value']:.6f} - 3 * {manual_data['X_Value']:.6f} + 1")
    print(f"   E值结果: {manual_data['E_Value']:.6f}")

    # 总结
    print(f"\n🏆 验证总结:")
    all_match = y_match and x_match and e_match and mfi_match and rsi_match
    if all_match:
        print("✅ 所有指标计算正确，数据库XYE值与手工计算一致！")
    else:
        print("⚠️ 发现计算差异，需要检查算法或数据源：")
        if not y_match:
            print(f"   • Y值差异过大: {y_diff:.6f}")
        if not x_match:
            print(f"   • X值差异过大: {x_diff:.6f}")
        if not e_match:
            print(f"   • E值差异过大: {e_diff:.6f}")
        if not mfi_match:
            print(f"   • MFI差异过大: {mfi_diff:.4f}")
        if not rsi_match:
            print(f"   • RSI差异过大: {rsi_diff:.4f}")

if __name__ == "__main__":
    compare_calculations()
