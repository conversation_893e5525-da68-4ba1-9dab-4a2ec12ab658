#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析详细交易记录
==============

对生成的交易记录进行深度分析
包括：盈亏分布、持仓时间、信号强度、市场条件等

作者: Cosmoon NG
日期: 2025年7月16日
"""

import pymysql
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

# 数据库配置
db_config = {
    'host': '************',
    'user': 'root',
    'password': '',
    'database': 'finance',
    'charset': 'utf8mb4'
}

def analyze_trading_performance():
    """分析交易表现"""
    print("📊 详细交易记录分析报告")
    print("=" * 80)
    
    try:
        # 连接数据库获取数据
        conn = pymysql.connect(**db_config)
        
        # 获取基础数据用于回测
        query = """
        SELECT Date, Open, High, Low, Close, Volume,
               MoneyFlowRatio, Full_Y, E, Controller
        FROM hkhsi50
        WHERE Date >= '2020-01-01'
        ORDER BY Date ASC
        """
        
        df = pd.read_sql(query, conn)
        conn.close()
        
        if df.empty:
            print("❌ 无数据")
            return
        
        print(f"✅ 分析期间: {df['Date'].min()} 至 {df['Date'].max()}")
        print(f"📊 数据量: {len(df):,} 天")
        
        # 执行简化回测获取交易记录
        trades, daily_equity = execute_backtest(df)
        
        if not trades:
            print("❌ 无交易记录")
            return
        
        # 转换为DataFrame
        trades_df = pd.DataFrame(trades)
        daily_df = pd.DataFrame(daily_equity)
        
        # 分析交易记录
        analyze_trades(trades_df)
        analyze_performance_by_conditions(trades_df)
        analyze_holding_periods(trades_df)
        analyze_monthly_performance(trades_df, daily_df)
        
        # 保存分析结果
        save_analysis_results(trades_df, daily_df)
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        import traceback
        print(traceback.format_exc())

def execute_backtest(df):
    """执行简化回测"""
    print("🚀 执行回测分析...")
    
    # 交易参数
    initial_capital = 30000
    monthly_addition = 2000
    take_profit_long = 0.016
    stop_loss_long = 0.008
    take_profit_short = 0.008
    stop_loss_short = 0.016
    
    # 状态变量
    position = 0
    entry_price = 0
    entry_date = None
    shares = 0
    capital = initial_capital
    last_month = None
    trade_id = 1
    
    trades = []
    daily_equity = []
    
    for i in range(20, len(df)):  # 从第20天开始
        row = df.iloc[i]
        date = pd.to_datetime(row['Date']).date()
        price = float(row['Close'])
        high = float(row['High'])
        low = float(row['Low'])
        
        # 每月增加资金
        current_month = date.replace(day=1)
        monthly_added = 0
        if last_month is None or current_month > last_month:
            last_month = current_month
            capital += monthly_addition
            monthly_added = monthly_addition
        
        # 检查退出条件
        if position != 0:
            should_exit = False
            exit_price = price
            exit_type = ''
            
            if position == 1:  # 多头
                if (high - entry_price) / entry_price >= take_profit_long:
                    should_exit, exit_price, exit_type = True, entry_price * (1 + take_profit_long), 'long_tp'
                elif (entry_price - low) / entry_price >= stop_loss_long:
                    should_exit, exit_price, exit_type = True, entry_price * (1 - stop_loss_long), 'long_sl'
            
            elif position == -1:  # 空头
                if (entry_price - low) / entry_price >= take_profit_short:
                    should_exit, exit_price, exit_type = True, entry_price * (1 - take_profit_short), 'short_tp'
                elif (high - entry_price) / entry_price >= stop_loss_short:
                    should_exit, exit_price, exit_type = True, entry_price * (1 + stop_loss_short), 'short_sl'
            
            if should_exit:
                # 计算盈亏
                holding_days = (date - entry_date).days
                
                if position == 1:
                    profit_amount = (exit_price - entry_price) * shares
                    capital += shares * exit_price
                else:
                    profit_amount = (entry_price - exit_price) * shares
                    capital -= shares * exit_price
                
                profit_pct = profit_amount / (entry_price * shares) * 100
                
                # 记录交易
                trades.append({
                    'trade_id': trade_id,
                    'entry_date': entry_date,
                    'exit_date': date,
                    'direction': '多头' if position == 1 else '空头',
                    'entry_price': entry_price,
                    'exit_price': exit_price,
                    'shares': shares,
                    'holding_days': holding_days,
                    'profit_amount': profit_amount,
                    'profit_pct': profit_pct,
                    'exit_type': exit_type,
                    'capital_after': capital,
                    'entry_signal_strength': get_signal_strength(df.iloc[i-holding_days] if i-holding_days >= 0 else row),
                    'entry_market_condition': get_market_condition(df.iloc[i-holding_days] if i-holding_days >= 0 else row),
                    'exit_market_condition': get_market_condition(row)
                })
                
                trade_id += 1
                position = 0
                entry_price = 0
                entry_date = None
                shares = 0
        
        # 检查开仓信号
        if position == 0:
            signal = get_trading_signal(row)
            
            if signal in ['LONG', 'SHORT']:
                shares = (capital * 0.95) / price
                
                if shares > 0:
                    if signal == 'LONG':
                        capital -= shares * price
                        position = 1
                    else:
                        capital += shares * price
                        position = -1
                    
                    entry_price = price
                    entry_date = date
        
        # 计算总价值
        if position == 1:
            total_value = capital + shares * price
        elif position == -1:
            total_value = capital + (entry_price - price) * shares
        else:
            total_value = capital
        
        # 记录每日权益
        daily_equity.append({
            'date': date,
            'close': price,
            'capital': capital,
            'total_value': total_value,
            'position': position,
            'monthly_added': monthly_added,
            'money_flow_ratio': float(row['MoneyFlowRatio']),
            'full_y': float(row['Full_Y']),
            'e_value': float(row['E']),
            'signal': signal if position == 0 else 'HOLD',
            'market_condition': get_market_condition(row)
        })
    
    print(f"✅ 回测完成，生成 {len(trades)} 笔交易")
    return trades, daily_equity

def get_trading_signal(row):
    """获取交易信号"""
    e_val = float(row['E'])
    money_flow = float(row['MoneyFlowRatio'])
    full_y = float(row['Full_Y'])
    
    if e_val > 0 and money_flow > 0.45 and full_y > 0.45:
        return 'LONG'
    elif full_y < 0.25 or money_flow < 0.25:
        return 'SHORT'
    return 'HOLD'

def get_signal_strength(row):
    """获取信号强度"""
    e_val = float(row['E'])
    money_flow = float(row['MoneyFlowRatio'])
    full_y = float(row['Full_Y'])
    
    if e_val > 0.3 and money_flow > 0.6 and full_y > 0.6:
        return '强信号'
    elif e_val > 0 and money_flow > 0.45 and full_y > 0.45:
        return '中等信号'
    elif money_flow < 0.25 or full_y < 0.25:
        return '弱信号'
    else:
        return '无信号'

def get_market_condition(row):
    """获取市场状态"""
    money_flow = float(row['MoneyFlowRatio'])
    full_y = float(row['Full_Y'])
    
    if full_y > 0.5 and money_flow > 0.5:
        return '高值盈利区'
    elif full_y > 0.333 and full_y < 0.4:
        return '控股商控制区'
    elif full_y < 0.25 or money_flow < 0.25:
        return '强亏损区'
    else:
        return '其他区域'

def analyze_trades(trades_df):
    """分析交易记录"""
    print(f"\n📊 一、交易基本统计")
    print("-" * 60)
    
    total_trades = len(trades_df)
    winning_trades = len(trades_df[trades_df['profit_amount'] > 0])
    losing_trades = total_trades - winning_trades
    
    if total_trades == 0:
        print("❌ 无交易记录")
        return
    
    win_rate = winning_trades / total_trades * 100
    avg_profit = trades_df['profit_amount'].mean()
    max_profit = trades_df['profit_amount'].max()
    max_loss = trades_df['profit_amount'].min()
    
    print(f"💰 交易概况:")
    print(f"   • 总交易次数: {total_trades}")
    print(f"   • 盈利次数: {winning_trades}")
    print(f"   • 亏损次数: {losing_trades}")
    print(f"   • 胜率: {win_rate:.1f}%")
    print(f"   • 平均盈亏: {avg_profit:,.0f} 港币")
    print(f"   • 最大盈利: {max_profit:,.0f} 港币")
    print(f"   • 最大亏损: {max_loss:,.0f} 港币")
    
    # 按方向分析
    long_trades = trades_df[trades_df['direction'] == '多头']
    short_trades = trades_df[trades_df['direction'] == '空头']
    
    if len(long_trades) > 0:
        long_wins = len(long_trades[long_trades['profit_amount'] > 0])
        long_win_rate = long_wins / len(long_trades) * 100
        print(f"\n📈 多头交易:")
        print(f"   • 交易次数: {len(long_trades)}")
        print(f"   • 胜率: {long_win_rate:.1f}%")
        print(f"   • 平均盈亏: {long_trades['profit_amount'].mean():,.0f} 港币")
    
    if len(short_trades) > 0:
        short_wins = len(short_trades[short_trades['profit_amount'] > 0])
        short_win_rate = short_wins / len(short_trades) * 100
        print(f"\n📉 空头交易:")
        print(f"   • 交易次数: {len(short_trades)}")
        print(f"   • 胜率: {short_win_rate:.1f}%")
        print(f"   • 平均盈亏: {short_trades['profit_amount'].mean():,.0f} 港币")

def analyze_performance_by_conditions(trades_df):
    """按市场条件分析表现"""
    print(f"\n📊 二、按市场条件分析")
    print("-" * 60)
    
    # 按入场市场条件分析
    condition_stats = trades_df.groupby('entry_market_condition').agg({
        'profit_amount': ['count', 'mean', lambda x: (x > 0).sum()],
        'holding_days': 'mean'
    }).round(2)
    
    condition_stats.columns = ['交易次数', '平均盈亏', '盈利次数', '平均持仓天数']
    condition_stats['胜率(%)'] = (condition_stats['盈利次数'] / condition_stats['交易次数'] * 100).round(1)
    
    print("📈 按入场市场条件统计:")
    print(condition_stats)
    
    # 按信号强度分析
    if 'entry_signal_strength' in trades_df.columns:
        signal_stats = trades_df.groupby('entry_signal_strength').agg({
            'profit_amount': ['count', 'mean', lambda x: (x > 0).sum()],
            'holding_days': 'mean'
        }).round(2)
        
        signal_stats.columns = ['交易次数', '平均盈亏', '盈利次数', '平均持仓天数']
        signal_stats['胜率(%)'] = (signal_stats['盈利次数'] / signal_stats['交易次数'] * 100).round(1)
        
        print(f"\n🎯 按入场信号强度统计:")
        print(signal_stats)

def analyze_holding_periods(trades_df):
    """分析持仓时间"""
    print(f"\n📊 三、持仓时间分析")
    print("-" * 60)
    
    holding_days = trades_df['holding_days']
    
    print(f"⏰ 持仓时间统计:")
    print(f"   • 平均持仓: {holding_days.mean():.1f} 天")
    print(f"   • 最长持仓: {holding_days.max()} 天")
    print(f"   • 最短持仓: {holding_days.min()} 天")
    print(f"   • 中位数: {holding_days.median():.1f} 天")
    
    # 按持仓时间分组分析
    trades_df['holding_group'] = pd.cut(trades_df['holding_days'], 
                                       bins=[0, 1, 3, 7, 14, 30, float('inf')],
                                       labels=['1天', '2-3天', '4-7天', '8-14天', '15-30天', '30天+'])
    
    holding_group_stats = trades_df.groupby('holding_group').agg({
        'profit_amount': ['count', 'mean', lambda x: (x > 0).sum()]
    }).round(2)
    
    holding_group_stats.columns = ['交易次数', '平均盈亏', '盈利次数']
    holding_group_stats['胜率(%)'] = (holding_group_stats['盈利次数'] / holding_group_stats['交易次数'] * 100).round(1)
    
    print(f"\n📊 按持仓时间分组:")
    print(holding_group_stats)

def analyze_monthly_performance(trades_df, daily_df):
    """分析月度表现"""
    print(f"\n📊 四、月度表现分析")
    print("-" * 60)
    
    # 按月统计交易
    trades_df['exit_month'] = pd.to_datetime(trades_df['exit_date']).dt.to_period('M')
    monthly_trades = trades_df.groupby('exit_month').agg({
        'profit_amount': ['count', 'sum', 'mean', lambda x: (x > 0).sum()]
    }).round(2)
    
    monthly_trades.columns = ['交易次数', '总盈亏', '平均盈亏', '盈利次数']
    monthly_trades['胜率(%)'] = (monthly_trades['盈利次数'] / monthly_trades['交易次数'] * 100).round(1)
    
    print(f"📅 最近12个月交易表现:")
    recent_months = monthly_trades.tail(12)
    print(recent_months)
    
    # 按年统计
    trades_df['exit_year'] = pd.to_datetime(trades_df['exit_date']).dt.year
    yearly_trades = trades_df.groupby('exit_year').agg({
        'profit_amount': ['count', 'sum', 'mean', lambda x: (x > 0).sum()]
    }).round(2)
    
    yearly_trades.columns = ['交易次数', '总盈亏', '平均盈亏', '盈利次数']
    yearly_trades['胜率(%)'] = (yearly_trades['盈利次数'] / yearly_trades['交易次数'] * 100).round(1)
    
    print(f"\n📊 按年度统计:")
    print(yearly_trades.tail(5))

def save_analysis_results(trades_df, daily_df):
    """保存分析结果"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"交易记录深度分析_{timestamp}.xlsx"
    
    with pd.ExcelWriter(filename, engine='openpyxl') as writer:
        # 保存交易记录
        trades_df.to_excel(writer, sheet_name='交易记录', index=False)
        
        # 保存每日记录
        daily_df.to_excel(writer, sheet_name='每日权益', index=False)
        
        # 详细统计
        stats_data = []
        
        # 基本统计
        total_trades = len(trades_df)
        winning_trades = len(trades_df[trades_df['profit_amount'] > 0])
        
        stats_data.extend([
            ['基本统计', ''],
            ['总交易次数', total_trades],
            ['盈利次数', winning_trades],
            ['胜率(%)', round(winning_trades/total_trades*100, 2) if total_trades > 0 else 0],
            ['平均盈亏', round(trades_df['profit_amount'].mean(), 2)],
            ['最大盈利', trades_df['profit_amount'].max()],
            ['最大亏损', trades_df['profit_amount'].min()],
            ['平均持仓天数', round(trades_df['holding_days'].mean(), 1)],
            ['', ''],
            ['资金统计', ''],
            ['初始资金', 30000],
            ['最终资金', daily_df['total_value'].iloc[-1] if len(daily_df) > 0 else 0],
            ['总收益', daily_df['total_value'].iloc[-1] - 30000 if len(daily_df) > 0 else 0],
            ['收益率(%)', round((daily_df['total_value'].iloc[-1] / 30000 - 1) * 100, 2) if len(daily_df) > 0 else 0]
        ])
        
        stats_df = pd.DataFrame(stats_data, columns=['指标', '数值'])
        stats_df.to_excel(writer, sheet_name='统计汇总', index=False)
    
    print(f"\n✅ 分析结果已保存至: {filename}")

def main():
    """主函数"""
    analyze_trading_performance()

if __name__ == "__main__":
    main()
