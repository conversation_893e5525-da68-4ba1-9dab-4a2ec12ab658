#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
正确的买跌策略分析
================

买跌策略正确计算：
- 止盈：价格下跌时盈利 → 止盈价格 = 开仓价 × (1 - 止盈%)
- 止损：价格上涨时亏损 → 止损价格 = 开仓价 × (1 + 止损%)

其他区域买跌策略：
- 止盈：1% → 止盈价格 = 开仓价 × (1 - 1%) = 开仓价 × 0.99
- 止损：2% → 止损价格 = 开仓价 × (1 + 2%) = 开仓价 × 1.02
- 赔率：1%:2% = 1:2 (有利赔率)

作者: Cosmoon NG
日期: 2025年7月
"""

import pymysql
import pandas as pd

class CorrectShortStrategyAnalyzer:
    def __init__(self):
        """初始化正确的买跌策略分析器"""
        self.db_config = {
            'host': '************',
            'port': 3306,
            'database': 'finance',
            'user': 'root',
            'password': '',
            'charset': 'utf8mb4'
        }
        self.connection = None
        self.position_size = 100  # 100股
        
    def connect_database(self):
        """连接数据库"""
        try:
            self.connection = pymysql.connect(**self.db_config)
            print(f"✅ 成功连接MySQL数据库: {self.db_config['host']}")
            return True
        except Exception as e:
            print(f"❌ 连接数据库失败: {e}")
            return False
    
    def analyze_correct_short_strategy(self):
        """正确分析买跌策略"""
        try:
            cursor = self.connection.cursor()
            
            # 获取其他区域的记录
            cursor.execute("""
                SELECT 交易序号, 开仓日期, close, 平仓价格, `控制系数`, `资金流比例`
                FROM test 
                WHERE NOT (`控制系数` > 0.43 AND `资金流比例` > 0.43) 
                  AND NOT (`控制系数` > 0.333 AND `控制系数` < 0.4) 
                  AND NOT (`控制系数` < 0.25 OR `资金流比例` < 0.25)
                ORDER BY 交易序号
            """)
            
            other_zone_records = cursor.fetchall()
            
            print("📊 其他区域买跌策略正确计算")
            print("="*120)
            print("🔍 买跌策略公式:")
            print("   • 止盈：价格下跌时盈利 → 止盈价格 = 开仓价 × (1 - 1%) = 开仓价 × 0.99")
            print("   • 止损：价格上涨时亏损 → 止损价格 = 开仓价 × (1 + 2%) = 开仓价 × 1.02")
            print("   • 赔率：1%:2% = 1:2 (有利赔率)")
            print("="*120)
            
            print(f"{'序号':<4} {'日期':<12} {'开仓价':<8} {'平仓价':<8} {'价格变动%':<10} "
                  f"{'止盈价':<8} {'止盈金额':<8} {'止损价':<8} {'止损金额':<8} {'实际结果':<10} {'实际金额':<8}")
            print("-" * 120)
            
            total_profit = 0
            take_profit_count = 0
            stop_loss_count = 0
            normal_close_count = 0
            
            for record in other_zone_records:
                trade_id, open_date, open_price, close_price, y_val, x_val = record
                
                # 买跌策略正确计算
                # 止盈：价格下跌1%时盈利
                take_profit_price = open_price * (1 - 0.01)  # 开仓价 × 0.99
                take_profit_amount = (open_price - take_profit_price) * self.position_size  # 盈利金额
                
                # 止损：价格上涨2%时亏损
                stop_loss_price = open_price * (1 + 0.02)  # 开仓价 × 1.02
                stop_loss_amount = -(stop_loss_price - open_price) * self.position_size  # 亏损金额
                
                # 计算价格变动
                price_change_pct = (close_price - open_price) / open_price * 100
                
                # 判断实际结果
                if close_price <= take_profit_price:
                    # 价格下跌到止盈价格，止盈
                    result = '止盈'
                    result_amount = take_profit_amount
                    take_profit_count += 1
                elif close_price >= stop_loss_price:
                    # 价格上涨到止损价格，止损
                    result = '止损'
                    result_amount = stop_loss_amount
                    stop_loss_count += 1
                else:
                    # 价格在止盈止损之间，到期平仓
                    result = '到期平仓'
                    # 买跌：价格下跌盈利，价格上涨亏损
                    result_amount = (open_price - close_price) * self.position_size
                    normal_close_count += 1
                
                total_profit += result_amount
                
                print(f"{trade_id:<4} {open_date:<12} {open_price:<8.2f} {close_price:<8.2f} {price_change_pct:<9.2f}% "
                      f"{take_profit_price:<8.2f} {take_profit_amount:<8.0f} {stop_loss_price:<8.2f} "
                      f"{stop_loss_amount:<8.0f} {result:<10} {result_amount:<8.0f}")
            
            total_trades = len(other_zone_records)
            
            print("\n" + "="*80)
            print(f"📊 其他区域买跌策略正确结果汇总:")
            print(f"   • 总交易: {total_trades}次")
            print(f"   • 止盈: {take_profit_count}次 ({take_profit_count/total_trades*100:.1f}%)")
            print(f"   • 止损: {stop_loss_count}次 ({stop_loss_count/total_trades*100:.1f}%)")
            print(f"   • 到期平仓: {normal_close_count}次 ({normal_close_count/total_trades*100:.1f}%)")
            print(f"   • 总盈亏: {total_profit:+,.0f}港币")
            print(f"   • 平均盈亏: {total_profit/total_trades:+.0f}港币/次")
            
            # 分析赔率效果
            if take_profit_count > 0 and stop_loss_count > 0:
                actual_win_rate = take_profit_count / (take_profit_count + stop_loss_count)
                theoretical_odds = 2.0  # 1%:2% = 1:2
                print(f"   • 止盈止损胜率: {actual_win_rate:.3f}")
                print(f"   • 理论赔率: 2:1 (止盈1% vs 止损2%)")
                
                # 凯利公式
                kelly_f = (theoretical_odds * actual_win_rate - (1 - actual_win_rate)) / theoretical_odds
                print(f"   • 凯利系数: {kelly_f:.6f}")
                
                if kelly_f > 0:
                    print(f"   ✅ 凯利系数为正，理论上有利可图")
                else:
                    print(f"   ❌ 凯利系数为负，需要提高胜率")
            
            return True
            
        except Exception as e:
            print(f"❌ 分析正确买跌策略失败: {e}")
            return False
    
    def compare_all_zones_correct(self):
        """正确比较所有策略区域"""
        try:
            cursor = self.connection.cursor()
            
            print(f"\n📊 所有策略区域正确计算对比:")
            print("="*120)
            
            zones = [
                ('高值盈利区', '买涨', 2.0, 1.0, "`控制系数` > 0.43 AND `资金流比例` > 0.43"),
                ('强亏损区', '买跌', 2.0, 1.0, "(`控制系数` < 0.25 OR `资金流比例` < 0.25) AND NOT (`控制系数` > 0.333 AND `控制系数` < 0.4)"),
                ('其他区域', '买跌', 1.0, 2.0, """NOT (`控制系数` > 0.43 AND `资金流比例` > 0.43) 
                                                   AND NOT (`控制系数` > 0.333 AND `控制系数` < 0.4) 
                                                   AND NOT (`控制系数` < 0.25 OR `资金流比例` < 0.25)""")
            ]
            
            for zone_name, direction, take_profit_pct, stop_loss_pct, where_clause in zones:
                print(f"\n🎯 {zone_name} - {direction}策略:")
                print(f"   止盈: {take_profit_pct}%, 止损: {stop_loss_pct}%, 赔率: {take_profit_pct}:{stop_loss_pct}")
                
                cursor.execute(f"""
                    SELECT 交易序号, close, 平仓价格
                    FROM test 
                    WHERE {where_clause}
                    ORDER BY 交易序号
                """)
                
                trades = cursor.fetchall()
                
                if not trades:
                    print("   无交易记录")
                    continue
                
                zone_total_profit = 0
                take_profit_count = 0
                stop_loss_count = 0
                normal_close_count = 0
                
                for trade_id, open_price, close_price in trades:
                    if direction == '买涨':
                        # 买涨策略
                        take_profit_price = open_price * (1 + take_profit_pct / 100)
                        stop_loss_price = open_price * (1 - stop_loss_pct / 100)
                        
                        if close_price >= take_profit_price:
                            result_amount = (take_profit_price - open_price) * self.position_size
                            take_profit_count += 1
                        elif close_price <= stop_loss_price:
                            result_amount = -(open_price - stop_loss_price) * self.position_size
                            stop_loss_count += 1
                        else:
                            result_amount = (close_price - open_price) * self.position_size
                            normal_close_count += 1
                    
                    else:  # 买跌策略
                        # 买跌策略：止盈价格下跌，止损价格上涨
                        take_profit_price = open_price * (1 - take_profit_pct / 100)
                        stop_loss_price = open_price * (1 + stop_loss_pct / 100)
                        
                        if close_price <= take_profit_price:
                            # 价格下跌到止盈价格
                            result_amount = (open_price - take_profit_price) * self.position_size
                            take_profit_count += 1
                        elif close_price >= stop_loss_price:
                            # 价格上涨到止损价格
                            result_amount = -(stop_loss_price - open_price) * self.position_size
                            stop_loss_count += 1
                        else:
                            # 价格在止盈止损之间
                            result_amount = (open_price - close_price) * self.position_size
                            normal_close_count += 1
                    
                    zone_total_profit += result_amount
                
                total_trades = len(trades)
                print(f"   • 总交易: {total_trades}次")
                print(f"   • 止盈: {take_profit_count}次 ({take_profit_count/total_trades*100:.1f}%)")
                print(f"   • 止损: {stop_loss_count}次 ({stop_loss_count/total_trades*100:.1f}%)")
                print(f"   • 到期平仓: {normal_close_count}次 ({normal_close_count/total_trades*100:.1f}%)")
                print(f"   • 总盈亏: {zone_total_profit:+,.0f}港币")
                print(f"   • 平均盈亏: {zone_total_profit/total_trades:+.0f}港币/次")
                
                # 计算凯利系数
                if take_profit_count > 0 and stop_loss_count > 0:
                    actual_win_rate = take_profit_count / (take_profit_count + stop_loss_count)
                    theoretical_odds = take_profit_pct / stop_loss_pct
                    kelly_f = (theoretical_odds * actual_win_rate - (1 - actual_win_rate)) / theoretical_odds
                    
                    print(f"   • 止盈止损胜率: {actual_win_rate:.3f}")
                    print(f"   • 理论赔率: {theoretical_odds:.1f}:1")
                    print(f"   • 凯利系数: {kelly_f:.6f}")
                    
                    if kelly_f > 0:
                        print(f"   ✅ 凯利系数为正，理论上有利")
                    else:
                        print(f"   ❌ 凯利系数为负，不利策略")
            
            return True
            
        except Exception as e:
            print(f"❌ 比较所有区域失败: {e}")
            return False
    
    def close_connection(self):
        """关闭数据库连接"""
        if self.connection:
            self.connection.close()
            print("\n🔒 数据库连接已关闭")

def main():
    """主函数"""
    print("📊 正确的买跌策略分析")
    print("="*80)
    print("🔍 买跌策略正确公式:")
    print("   • 止盈：价格下跌时盈利 → 止盈价格 = 开仓价 × (1 - 止盈%)")
    print("   • 止损：价格上涨时亏损 → 止损价格 = 开仓价 × (1 + 止损%)")
    print("   • 其他区域：止盈1%, 止损2%, 赔率1:2 (有利)")
    
    # 创建分析器
    analyzer = CorrectShortStrategyAnalyzer()
    
    # 连接数据库
    if not analyzer.connect_database():
        return
    
    # 正确分析其他区域买跌策略
    analyzer.analyze_correct_short_strategy()
    
    # 正确比较所有策略区域
    analyzer.compare_all_zones_correct()
    
    # 关闭连接
    analyzer.close_connection()
    
    print(f"\n🎯 正确的买跌策略分析完成！")
    print("💡 现在可以看到其他区域买跌策略的真实表现")

if __name__ == "__main__":
    main()
