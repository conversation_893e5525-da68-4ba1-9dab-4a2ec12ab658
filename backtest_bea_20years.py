#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
00023.HK (恒生银行) 20年回测系统
==========================

特点：
1. 使用回归中线作为趋势判断
2. 使用凯利公式优化仓位
3. 采用Cosmoon XYE方法
4. 每月复利加入3000

作者: Cosmoon NG
日期: 2025年7月
"""
import sys
import os
import io
import locale
import pandas as pd
import numpy as np
import yfinance as yf
import sqlite3
import traceback
import matplotlib.pyplot as plt
from datetime import datetime, timedelta
from scipy import stats

# 设置标准输出编码为UTF-8
if sys.stdout.encoding != 'utf-8':
    sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8', errors='replace')

# 设置控制台编码
if os.name == 'nt':  # Windows
    if sys.stdout.encoding != 'utf-8':
        import codecs
        sys.stdout = codecs.getwriter('utf-8')(sys.stdout.buffer, 'strict')
    # 设置控制台代码页为UTF-8
    os.system('chcp 65001 > nul')

import sqlite3
import pandas as pd
import numpy as np
import os
import yfinance as yf
from datetime import datetime, timedelta
import matplotlib.pyplot as plt
from scipy import stats
import matplotlib.pyplot as plt
plt.rcParams['font.sans-serif'] = ['SimHei']  # Use SimHei for Chinese characters
plt.rcParams['axes.unicode_minus'] = False  # This is to display minus signs correctly



class BEABankBacktest:
    def __init__(self):
        """初始化回测系统"""
        # 基础参数
        self.ticker = '0023.HK'  # 东亚银行 (BEA)
        self.initial_capital = 3000  # 初始资金3K
        self.monthly_addition = 3000  # 每月追加1000
        
        # 动态止盈止损参数
        self.base_take_profit = 0.012  # 基础止盈 1.2%
        self.base_stop_loss = 0.008    # 基础止损 0.6%
        self.max_profit_multiplier = 2.0  # 最大止盈倍数
        self.min_loss_multiplier = 0.8   # 最小止损倍数
        
        # 风险控制参数
        self.max_position_size = 0.5    # 最大仓位比例
        self.min_position_size = 0.1    # 最小仓位比例
        self.risk_per_trade = 0.02      # 单次交易风险比例
        self.consecutive_loss_limit = 3  # 连续亏损限制
        
        # 策略参数
        self.position = 0  # 当前持仓，1为多头，-1为空头，0为空仓
        self.current_price = 0  # 当前持仓价格
        self.entry_time = None  # 入场时间
        self.consecutive_losses = 0  # 连续亏损次数
        self.trailing_stop = None  # 追踪止损价格
        
        # 市场状态参数 (0:震荡, 1:上升趋势, -1:下降趋势)
        self.market_state = 0
        
        # 动态XY阈值
        self.x_threshold_long = 0.45  # 多头X阈值
        self.y_threshold_long = 0.45  # 多头Y阈值
        self.x_threshold_short = 0.25  # 空头X阈值
        self.y_threshold_short = 0.25  # 空头Y阈值
        
        # 交易统计
        self.win_count = 0
        self.loss_count = 0
        self.total_trades = 0
        self.win_rate = 0.0

        # 缺失的止盈止损参数（用于信号生成和回测）
        self.stop_loss_long = self.base_stop_loss
        self.stop_loss_short = self.base_stop_loss
        self.take_profit_long = self.base_take_profit
        self.take_profit_short = self.base_take_profit
    def calculate_kelly(self, win_rate=None, profit_ratio=None):
        """凯利公式建议仓位比例"""
        if win_rate is None:
            win_rate = self.win_rate if self.total_trades > 0 else 0.5
        if profit_ratio is None:
            profit_ratio = 2.0
        q = 1 - win_rate
        if win_rate <= 0 or profit_ratio <= 0:
            return self.min_position_size
        kelly = (win_rate * profit_ratio - q) / profit_ratio
        return max(self.min_position_size, min(kelly, self.max_position_size))
        
    def fetch_yf_data(self, ticker='0023.HK', start_date=None, end_date=None):
        """使用yfinance获取数据"""
        if end_date is None:
            end_date = datetime.now().strftime('%Y-%m-%d')
        if start_date is None:
            # 获取3年历史数据
            start_date = (datetime.now() - timedelta(days=365*3)).strftime('%Y-%m-%d')
            
        print(f"\n1. 从Yahoo Finance获取数据 ({start_date} 至 {end_date})...")
        try:
            # 获取数据
            data = yf.download(ticker, start=start_date, end=end_date)

            if data.empty:
                raise ValueError("未获取到数据，请检查股票代码和日期范围")

            # 处理多级列名
            if isinstance(data.columns, pd.MultiIndex):
                # 展平多级列名，只保留第一级
                data.columns = [col[0] for col in data.columns]

            # 重命名列以匹配现有代码
            data = data.rename(columns={
                'Open': 'open',
                'High': 'high',
                'Low': 'low',
                'Close': 'close',
                'Volume': 'volume'
            })

            # 重置索引并将日期转换为列
            data = data.reset_index()
            data = data.rename(columns={'Date': 'date'})
            
            # 计算成交量比率 (x_value)
            data['x_value'] = data['volume'] / data['volume'].rolling(20).mean()
            data['x_value'] = data['x_value'].fillna(0.5).clip(0.1, 2.0)  # 限制范围
            
            # 计算资金流比率 (y_value)
            # 简化计算：使用价格变化与成交量的关系
            price_change = data['close'].pct_change()
            # 使用简单的成交量比率作为y_value的代理
            volume_ma = data['volume'].rolling(20).mean()
            volume_ma = volume_ma.replace(0, 1)  # 避免除零
            data['y_value'] = data['volume'] / volume_ma
            data['y_value'] = data['y_value'].fillna(0.5).clip(0.1, 2.0)  # 限制范围
            
            # 能量指标 (e_value) - 使用RSI的变体
            delta = data['close'].diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
            rs = gain / loss
            data['e_value'] = 1 - (1 / (1 + rs))
            data['e_value'] = data['e_value'].fillna(0.5).clip(0.1, 0.9)  # 限制范围
            
            # 确保日期格式正确
            data['date'] = pd.to_datetime(data['date'])
            
            print(f"✓ 成功获取 {len(data)} 条数据")
            print(f"数据范围：{data['date'].min().date()} 至 {data['date'].max().date()}")
            
            return data
            
        except Exception as e:
            print(f"❌ 从Yahoo Finance获取数据失败: {str(e)}")
            raise
    
    def load_data(self, use_yf=True):
        """加载数据"""
        if use_yf:
            # 使用yfinance获取最新数据
            self.df = self.fetch_yf_data()
        else:
            # 使用原有数据库方式
            print("\n1. 从数据库加载数据...")
            try:
                conn = sqlite3.connect('hsi_25years.db')
                three_years_ago = (datetime.now() - timedelta(days=20*365)).strftime('%Y-%m-%d')
                self.df = pd.read_sql(f"""
                    SELECT date, open, high, low, close, volume, 
                           volume_ratio as x_value, 
                           inflow_ratio as y_value, 
                           0.5 as e_value 
                    FROM hsi_data 
                    WHERE date >= '{three_years_ago}'
                    ORDER BY date
                """, conn)
                conn.close()
                
                # 转换日期
                self.df['date'] = pd.to_datetime(self.df['date'])
                print(f"✓ 从数据库加载了 {len(self.df)} 条数据")
                print(f"数据范围：{self.df['date'].min().date()} 至 {self.df['date'].max().date()}")
                
            except Exception as e:
                print(f"❌ 数据库加载失败: {str(e)}")
                print("尝试从Yahoo Finance获取数据...")
                self.df = self.fetch_yf_data()
        
    def calculate_regression_line(self, window=60):
        """计算回归线
        
        使用numpy的polynomial.polyfit进行滚动窗口回归计算
        """
        print("\n2. 计算回归线...")
        try:
            # 确保数据按日期排序
            self.df = self.df.sort_values('date').reset_index(drop=True)
            
            # 初始化新列
            close_prices = self.df['close'].values
            n = len(close_prices)
            
            # 初始化结果数组
            regression_line = np.full(n, np.nan)
            price_position = np.zeros(n)
            upper_band = np.full(n, np.nan)
            lower_band = np.full(n, np.nan)
            
            # 使用numpy的polyfit进行滚动回归
            for i in range(window, n):
                # 获取窗口数据
                window_prices = close_prices[i-window:i+1]
                x = np.arange(len(window_prices))
                
                # 初始化变量
                current_regression = None
                std_dev = 0.0
                
                try:
                    # 确保输入是1D数组且没有NaN值
                    x_clean = x.astype(float)
                    y_clean = window_prices.astype(float)
                    
                    # 检查是否有NaN值
                    mask = ~(np.isnan(x_clean) | np.isnan(y_clean))
                    if np.sum(mask) < 2:  # 至少需要2个点
                        raise ValueError("Not enough data points")
                        
                    x_clean = x_clean[mask]
                    y_clean = y_clean[mask]
                    
                    # 使用numpy的polyfit进行线性拟合
                    coef = np.polyfit(x_clean, y_clean, 1)
                    poly = np.poly1d(coef)
                    
                    # 计算当前点的回归值（窗口内最后一个点）
                    current_x = x_clean[-1] if len(x_clean) > 0 else 0
                    current_regression = float(poly(current_x))  # 确保是标量
                    regression_line[i] = current_regression
                    
                    # 计算价格相对回归线的位置
                    current_price = close_prices[i]
                    if current_regression != 0:
                        price_position[i] = (current_price - current_regression) / current_regression
                    
                    # 计算残差和标准差
                    if len(window_prices) > 1:  # 需要至少2个点计算标准差
                        residuals = y_clean - poly(x_clean)  # 使用清洗后的x值
                        if len(residuals) > 1:  # 确保有足够的数据点计算标准差
                            std_dev = float(np.std(residuals, ddof=1))  # 转换为Python标量
                            upper_band[i] = current_regression + 2 * std_dev
                            lower_band[i] = current_regression - 2 * std_dev
                            
                except Exception as e:
                    # 如果拟合失败，使用前一个有效值或默认值
                    if i > 0:
                        regression_line[i] = regression_line[i-1]
                        price_position[i] = price_position[i-1]
                        upper_band[i] = upper_band[i-1]
                        lower_band[i] = lower_band[i-1]
                    else:
                        regression_line[i] = close_prices[i]  # 使用当前价格作为默认值
                        price_position[i] = 0.0
                        upper_band[i] = close_prices[i] * 1.1  # 默认上下轨
                        lower_band[i] = close_prices[i] * 0.9
            
            # 将结果存入DataFrame
            self.df['regression_line'] = regression_line
            self.df['price_position'] = price_position
            self.df['upper_band'] = upper_band
            self.df['lower_band'] = lower_band
            
            # 向前填充NaN值 (使用新的pandas语法)
            self.df['regression_line'] = self.df['regression_line'].bfill()
            self.df['upper_band'] = self.df['upper_band'].bfill()
            self.df['lower_band'] = self.df['lower_band'].bfill()
            
            # 计算R²
            valid_regression = self.df['regression_line'].notna()
            if valid_regression.sum() > 1:
                r_value = np.corrcoef(
                    self.df.loc[valid_regression, 'close'],
                    self.df.loc[valid_regression, 'regression_line']
                )[0, 1]
                r_squared = r_value ** 2
            else:
                r_squared = 0
            
            print(f"✓ 回归线计算完成 (R² = {r_squared:.4f}, 滚动窗口: {window}天)")

        except Exception as e:
            print(f"❌ 回归线计算失败: {str(e)}")
            import traceback
            traceback.print_exc()
            raise

        # 计算技术指标
        print("3. 计算技术指标...")

        try:
            # 检查数据结构
            print(f"  - 数据形状: {self.df.shape}")
            print(f"  - 列名: {list(self.df.columns)}")

            # 先计算基础技术指标
            print("  - 计算RSI...")
            close_series = self.df['close']
            if isinstance(close_series, pd.DataFrame):
                close_series = close_series.iloc[:, 0]  # 取第一列
            self.df['rsi'] = self.calculate_rsi(close_series, 14)

            print("  - 计算MACD...")
            macd_result = self.calculate_macd(close_series)
            self.df['macd'] = macd_result[0]
            self.df['macd_signal'] = macd_result[1]

            print("  - 计算布林带...")
            bb_result = self.calculate_bollinger_bands(close_series)
            self.df['bb_upper'] = bb_result[0]
            self.df['bb_lower'] = bb_result[1]

            # 计算自定义指标 (按照您的方法)
            print("  - 计算资金流比例...")
            self.df['money_flow_ratio'] = self.calculate_money_flow_ratio()

            print("  - 计算Full_Y...")
            self.df['full_y'] = self.calculate_full_y()

            print("  - 计算E值...")
            self.df['e_value'] = self.calculate_e_value()

            # 计算价格相对回归线位置
            print("  - 计算价格位置...")
            self.df['price_position'] = (self.df['close'] - self.df['regression_line']) / self.df['regression_line']

            # 计算移动平均线
            print("  - 计算移动平均线...")
            self.df['ma5'] = close_series.rolling(5).mean()
            self.df['ma10'] = close_series.rolling(10).mean()
            self.df['ma20'] = close_series.rolling(20).mean()
            self.df['ma60'] = close_series.rolling(60).mean()

            # 计算收益率
            print("  - 计算收益率...")
            self.df['returns'] = close_series.pct_change()

            print("✓ 技术指标计算完成")

            # 修复：补充returns列，供后续回测使用
            self.df['returns'] = self.df['close'].pct_change()

        except Exception as e:
            print(f"❌ 技术指标计算失败: {str(e)}")
            import traceback
            traceback.print_exc()
            raise
    
    def calculate_rsi(self, prices, window=14):
        """计算RSI指标"""
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=window).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=window).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        return rsi

    def calculate_macd(self, prices, fast=12, slow=26, signal=9):
        """计算MACD指标"""
        ema_fast = prices.ewm(span=fast).mean()
        ema_slow = prices.ewm(span=slow).mean()
        macd = ema_fast - ema_slow
        macd_signal = macd.ewm(span=signal).mean()
        return macd, macd_signal

    def calculate_bollinger_bands(self, prices, window=20, num_std=2):
        """计算布林带"""
        rolling_mean = prices.rolling(window=window).mean()
        rolling_std = prices.rolling(window=window).std()
        upper_band = rolling_mean + (rolling_std * num_std)
        lower_band = rolling_mean - (rolling_std * num_std)
        return upper_band, lower_band

    def calculate_money_flow_ratio(self):
        """计算资金流比例 (X值)"""
        # 基于成交量和价格变化计算资金流
        price_change = (self.df['close'] - self.df['open']) / self.df['open']
        money_flow = self.df['volume'] * price_change

        def calc_inflow_ratio(flows):
            if len(flows) == 0 or flows.isna().all():
                return 0.5
            flows = flows.dropna()
            if len(flows) == 0:
                return 0.5

            inflows = flows[flows > 0].sum()
            outflows = abs(flows[flows < 0].sum())
            total_flow = inflows + outflows

            return inflows / total_flow if total_flow > 0 else 0.5

        money_flow_ratio = money_flow.rolling(window=20).apply(calc_inflow_ratio, raw=False)
        return np.clip(money_flow_ratio, 0.1, 0.9).fillna(0.5)

    def calculate_full_y(self):
        """计算Full_Y值 (控制系数)"""
        # 基于RSI和价格动量计算
        price_momentum = self.df['close'].pct_change(10)

        # 确保rsi是Series而不是DataFrame
        rsi_series = self.df['rsi']
        if isinstance(rsi_series, pd.DataFrame):
            rsi_series = rsi_series.iloc[:, 0]  # 取第一列

        full_y = (rsi_series / 100 + np.tanh(price_momentum * 5) + 1) / 2
        return np.clip(full_y, 0.1, 0.9).fillna(0.5)

    def calculate_e_value(self):
        """计算E值 (博弈论期望值)"""
        x = self.df['money_flow_ratio']
        y = self.df['full_y']
        e_value = 8 * x * y - 3 * x - 3 * y + 1
        return e_value

    def calculate_position_size(self, current_price, capital, volatility):
        """
        计算建议仓位大小，综合考虑:
        1. 凯利公式
        2. 市场波动率
        3. 风险管理规则
        4. 连续亏损情况
        """
        # 基于历史数据计算胜率
        win_rate = self.win_rate if self.total_trades > 0 else 0.5
        
        # 计算动态盈亏比
        current_profit_ratio = self.calculate_dynamic_profit_ratio(volatility)
        
        # 基础凯利仓位
        q = 1 - win_rate
        if win_rate <= 0 or current_profit_ratio <= 0:
            kelly = self.min_position_size
        else:
            kelly = (win_rate * current_profit_ratio - q) / current_profit_ratio
        
        # 波动率调整因子 (波动率越大，仓位越小)
        volatility_factor = max(0.2, 1 - volatility * 10)
        
        # 连续亏损调整因子
        loss_factor = max(0.2, 1 - (self.consecutive_losses / self.consecutive_loss_limit))
        
        # 综合计算最终仓位比例
        position_size = kelly * volatility_factor * loss_factor
        
        # 确保仓位在允许范围内
        position_size = max(self.min_position_size, 
                          min(position_size, self.max_position_size))
        
        # 计算实际金额
        position_amount = capital * position_size
        
        # 根据单次风险限制调整
        risk_amount = capital * self.risk_per_trade
        max_position = risk_amount / (self.base_stop_loss * current_price)
        position_amount = min(position_amount, max_position)
        
        return position_amount
    
    def calculate_dynamic_profit_ratio(self, volatility):
        """计算动态盈亏比"""
        # 基于波动率调整止盈止损比例
        if self.position == 1:  # 多头
            take_profit = self.base_take_profit * (1 + volatility * self.max_profit_multiplier)
            stop_loss = self.base_stop_loss * (1 - volatility * self.min_loss_multiplier)
        else:  # 空头
            take_profit = self.base_take_profit * (1 - volatility * self.min_loss_multiplier)
            stop_loss = self.base_stop_loss * (1 + volatility * self.max_profit_multiplier)
        
        return take_profit / stop_loss if stop_loss > 0 else 2.0
    
    def add_monthly_capital(self, date, capital):
        """
        每月增加资金
        
        参数:
            date: 当前日期 (pandas Timestamp)
            capital: 当前资金
            
        返回:
            更新后的资金
        """
        # 确保date是pandas Timestamp
        if not hasattr(self, 'last_month'):
            self.last_month = None

        try:
            # 统一转换为pandas Timestamp
            if isinstance(date, str):
                date_ts = pd.to_datetime(date)
            elif isinstance(date, pd.Timestamp):
                date_ts = date
            else:
                date_ts = pd.Timestamp(date)

            # 获取当前月份的第一天
            current_month = pd.Timestamp(year=date_ts.year, month=date_ts.month, day=1)

            # 检查是否已经处理过这个月
            if self.last_month is None or current_month > self.last_month:
                self.last_month = current_month
                # 只在月初增加资金
                if date_ts.day <= 5:  # 允许在月初5天内增加资金，避免错过月初第一个交易日
                    print(f"{date_ts.strftime('%Y-%m-%d')}: 增加资金 {self.monthly_addition:.2f}")
                    return capital + self.monthly_addition

            return capital
        except Exception as e:
            print(f"处理日期时出错: {e}, date: {date}")
            return capital
    
    def detect_market_trend(self, df, current_idx, window=60):
        """
        增强版市场趋势检测，综合考虑：
        1. 价格趋势（回归线）
        2. 波动率
        3. 成交量
        4. 均线系统
        5. 趋势强度
        
        返回:
            1: 强势上升
            0.5: 弱势上升
            0: 盘整
            -0.5: 弱势下降
            -1: 强势下降
        """
        if current_idx < window:
            return 0
            
        try:
            # 获取窗口数据
            window_data = df.iloc[current_idx-window:current_idx].copy()
            
            # 1. 均线系统分析
            window_data['ma5'] = window_data['close'].rolling(5).mean()
            window_data['ma10'] = window_data['close'].rolling(10).mean()
            window_data['ma20'] = window_data['close'].rolling(20).mean()
            window_data['ma60'] = window_data['close'].rolling(60).mean()
            
            latest = window_data.iloc[-1]

            # 确保ma值不是NaN
            ma5_val = latest['ma5'] if not pd.isna(latest['ma5']) else latest['close']
            ma10_val = latest['ma10'] if not pd.isna(latest['ma10']) else latest['close']
            ma20_val = latest['ma20'] if not pd.isna(latest['ma20']) else latest['close']
            ma60_val = latest['ma60'] if not pd.isna(latest['ma60']) else latest['close']

            ma_trend = (
                (ma5_val > ma10_val) * 0.3 +
                (ma10_val > ma20_val) * 0.3 +
                (ma20_val > ma60_val) * 0.4
            )
            
            # 2. 价格动量
            price_change = (latest['close'] - window_data['close'].iloc[0]) / window_data['close'].iloc[0]
            momentum = np.tanh(price_change * 10)  # 使用tanh归一化
            
            # 3. 波动率分析
            window_data['returns'] = window_data['close'].pct_change()
            volatility = window_data['returns'].std() * np.sqrt(252)  # 年化波动率
            vol_factor = 1 - min(volatility, 0.5)  # 波动率调整因子
            
            # 4. 成交量分析
            volume_ma = window_data['volume'].rolling(20).mean()
            volume_trend = (window_data['volume'].iloc[-1] / volume_ma.iloc[-1] - 1)
            volume_factor = np.tanh(volume_trend * 5)
            
            # 5. 趋势强度计算
            # 使用回归线斜率
            x = np.arange(len(window_data))
            slope, _, r_value, _, _ = stats.linregress(x, window_data['close'])
            trend_strength = slope * len(window_data) / window_data['close'].mean()
            trend_quality = abs(r_value)  # R值的绝对值表示趋势质量
            
            # 综合评分 (-1 到 1)
            trend_score = (
                ma_trend * 0.3 +          # 均线权重
                momentum * 0.25 +         # 动量权重
                volume_factor * 0.15 +    # 成交量权重
                trend_strength * 0.2 +    # 趋势强度权重
                trend_quality * 0.1       # 趋势质量权重
            ) * vol_factor                # 波动率调整
            
            # 返回趋势信号
            if trend_score > 0.6:
                return 1        # 强势上升
            elif trend_score > 0.2:
                return 0.5      # 弱势上升
            elif trend_score < -0.6:
                return -1       # 强势下降
            elif trend_score < -0.2:
                return -0.5     # 弱势下降
            else:
                return 0        # 盘整
                
        except Exception as e:
            print(f"趋势检测出错: {str(e)}")
            return 0
            
    def calculate_technical_score(self, row):
        """
        计算技术分析综合评分 (0-100)
        """
        score = 0
        
        # 1. 趋势得分 (0-30)
        if row['price_position'] > 0:
            if row['close'] > row['ma20'] > row['ma60']:
                score += 30
            elif row['close'] > row['ma20']:
                score += 20
            elif row['close'] > row['ma60']:
                score += 10
        else:
            if row['close'] < row['ma20'] < row['ma60']:
                score += 30
            elif row['close'] < row['ma20']:
                score += 20
            elif row['close'] < row['ma60']:
                score += 10
                
        # 2. 动量得分 (0-20)
        rsi = row['rsi']
        if 40 <= rsi <= 60:
            score += 20
        elif 30 <= rsi <= 70:
            score += 15
        elif 20 <= rsi <= 80:
            score += 10
        
        # 3. 成交量得分 (0-20)
        # 使用预计算的x_value作为成交量比率
        volume_ratio = row.get('x_value', 1.0)
        if volume_ratio > 1.5:
            score += 20
        elif volume_ratio > 1.2:
            score += 15
        elif volume_ratio > 1:
            score += 10
        
        # 4. XYE系统得分 (0-30)
        if row['e_value'] > 0.6:
            score += 15
        elif row['e_value'] > 0.3:
            score += 10
        
        if row['x_value'] > 0.45 and row['y_value'] > 0.45:
            score += 15
        elif row['x_value'] > 0.4 and row['y_value'] > 0.4:
            score += 10
        
        return score
        
    def calculate_risk_level(self, volatility, technical_score):
        """
        计算风险等级
        返回: 低/中/高
        """
        risk_score = volatility * 100 + (100 - technical_score) * 0.5
        
        if risk_score < 30:
            return "低"
        elif risk_score < 60:
            return "中"
        else:
            return "高"
            
    def calculate_dynamic_tp_sl(self, volatility):
        """
        根据波动率计算动态止盈止损点位
        """
        # 基础止盈止损
        base_tp = self.base_take_profit
        base_sl = self.base_stop_loss
        
        # 波动率调整
        vol_factor = min(2.0, 1 + volatility * 5)  # 最大2倍
        
        if self.position == 1:  # 多头
            take_profit = base_tp * vol_factor
            stop_loss = base_sl / vol_factor
        else:  # 空头
            take_profit = base_tp / vol_factor
            stop_loss = base_sl * vol_factor
            
        return {
            'take_profit': min(take_profit, base_tp * self.max_profit_multiplier),
            'stop_loss': max(stop_loss, base_sl * self.min_loss_multiplier)
        }
        
    def update_trailing_stop(self, current_price):
        """
        更新追踪止损
        """
        if self.trailing_stop is None:
            if self.position == 1:  # 多头
                self.trailing_stop = current_price * (1 - self.base_stop_loss)
            elif self.position == -1:  # 空头
                self.trailing_stop = current_price * (1 + self.base_stop_loss)
        else:
            if self.position == 1 and current_price > self.current_price:
                # 更新多头追踪止损
                new_stop = current_price * (1 - self.base_stop_loss)
                self.trailing_stop = max(self.trailing_stop, new_stop)
            elif self.position == -1 and current_price < self.current_price:
                # 更新空头追踪止损
                new_stop = current_price * (1 + self.base_stop_loss)
                self.trailing_stop = min(self.trailing_stop, new_stop)
                
    def calculate_trend_score(self, row):
        """计算趋势得分 (0-100)"""
        score = 50  # 基础得分

        # 1. 价格相对回归线位置 (0-30)
        price_pos = row.get('price_position', 0)
        if price_pos > 0.05:
            score += 30
        elif price_pos > 0.02:
            score += 20
        elif price_pos > -0.02:
            score += 10
        elif price_pos > -0.05:
            score -= 10
        else:
            score -= 30

        # 2. E值得分 (0-20)
        e_val = row.get('e_value', 0)
        if e_val > 0.5:
            score += 20
        elif e_val > 0:
            score += 10
        elif e_val > -0.5:
            score -= 10
        else:
            score -= 20

        return max(0, min(100, score))

    def adjust_xy_thresholds(self, trend_score, volatility):
        """根据市场状态和波动率动态调整XY阈值"""
        # 基础阈值
        base_threshold = 0.45
        
        # 趋势调整
        trend_adjustment = {
            1: -0.05,     # 强势上升：降低多头门槛
            0.5: -0.02,   # 弱势上升
            0: 0,         # 盘整
            -0.5: 0.02,   # 弱势下降
            -1: 0.05      # 强势下降：提高多头门槛
        }.get(trend_score, 0)
        
        # 波动率调整（高波动率增加门槛）
        vol_adjustment = min(0.1, volatility)
        
        # 设置动态阈值
        self.x_threshold_long = base_threshold + trend_adjustment + vol_adjustment
        self.y_threshold_long = base_threshold + trend_adjustment + vol_adjustment
        self.x_threshold_short = base_threshold - trend_adjustment + vol_adjustment
        self.y_threshold_short = base_threshold - trend_adjustment + vol_adjustment
        
        # 确保阈值在合理范围内
        for attr in ['x_threshold_long', 'y_threshold_long', 
                    'x_threshold_short', 'y_threshold_short']:
            setattr(self, attr, max(0.2, min(0.8, getattr(self, attr))))

    def generate_daily_signals(self, row, current_idx, capital):
        """
        增强版信号生成器，整合：
        1. 技术分析
        2. 市场状态
        3. 动态止盈止损
        4. 仓位管理
        5. 风险评估
        """
        # 获取当前波动率
        volatility = self.df['returns'].rolling(20).std() * np.sqrt(252)
        current_volatility = volatility.iloc[current_idx]
        
        # 计算动态止盈止损
        dynamic_tp_sl = self.calculate_dynamic_tp_sl(current_volatility)
        
        # 计算建议仓位
        position_amount = self.calculate_position_size(row['close'], capital, current_volatility)
        position_ratio = position_amount / capital
        
        # 市场趋势强度
        trend = self.detect_market_trend(self.df, current_idx)
        trend_states = {
            1: '强势上升',
            0.5: '弱势上升',
            0: '盘整',
            -0.5: '弱势下降',
            -1: '强势下降'
        }
        
        # 计算综合评分 (0-100)
        technical_score = self.calculate_technical_score(row)
        
        signal = {
            'date': row['date'],
            'close': row['close'],
            'x_value': row['money_flow_ratio'],
            'y_value': row['full_y'],
            'e_value': row['e_value'],
            'price_position': row['price_position'],
            'market_state': trend_states.get(trend, '未知'),
            'volatility': f"{current_volatility*100:.1f}%",
            'technical_score': f"{technical_score:.0f}/100",
            'signal': '持有',
            'position': self.position,
            'position_ratio': f"{position_ratio*100:.1f}%",
            'suggested_action': '无',
            'stop_loss': None,
            'take_profit': None,
            'risk_level': self.calculate_risk_level(current_volatility, technical_score)
        }
        
        # 计算凯利仓位
        if self.total_trades > 0:
            win_rate = self.win_count / self.total_trades
            profit_ratio = 2.0  # 默认盈亏比
            signal['kelly_fraction'] = self.calculate_kelly(win_rate, profit_ratio)
        
        # 生成信号 (按照您的方法)
        if self.position == 0:  # 空仓状态
            # 买涨条件: E>0 且 X>0.45 且 Y>0.45 且价格低于回归线
            if (row['e_value'] > 0 and
                row['money_flow_ratio'] > 0.45 and
                row['full_y'] > 0.45 and
                row['price_position'] < 0):
                signal['signal'] = '买涨信号'
                signal['suggested_action'] = f'开多仓 (仓位: {signal["kelly_fraction"]*100:.1f}%)'
                signal['stop_loss'] = row['close'] * (1 - self.stop_loss_long)
                signal['take_profit'] = row['close'] * (1 + self.take_profit_long)

            # 买跌条件: (Y<0.25 或 X<0.25) 且价格高于回归线
            elif ((row['full_y'] < 0.25 or row['money_flow_ratio'] < 0.25) and
                  row['price_position'] > 0):
                signal['signal'] = '买跌信号'
                signal['suggested_action'] = f'开空仓 (仓位: {signal["kelly_fraction"]*100:.1f}%)'
                signal['stop_loss'] = row['close'] * (1 + self.stop_loss_short)
                signal['take_profit'] = row['close'] * (1 - self.take_profit_short)
                
        else:  # 持仓状态
            if self.position == 1:  # 持有多仓
                signal['stop_loss'] = self.current_price * (1 - self.stop_loss_long)
                signal['take_profit'] = self.current_price * (1 + self.take_profit_long)
                signal['suggested_action'] = f'持有 (止盈: {signal["take_profit"]:.2f}, 止损: {signal["stop_loss"]:.2f})'
            else:  # 持有空仓
                signal['stop_loss'] = self.current_price * (1 + self.stop_loss_short)
                signal['take_profit'] = self.current_price * (1 - self.take_profit_short)
                signal['suggested_action'] = f'持有 (止盈: {signal["take_profit"]:.2f}, 止损: {signal["stop_loss"]:.2f})'
        
        return signal

    def run_backtest(self):
        """运行回测"""
        print("\n3. 开始回测...")
        try:
            # 初始化信号记录
            self.daily_signals = []
            # 准备结果记录
            self.trades = []
            capital = self.initial_capital
            self.equity_curve = []
            
            # 初始化交易统计
            self.win_count = 0
            self.loss_count = 0
            self.total_trades = 0
            self.win_rate = 0.0
            
            # 计算ATR用于仓位管理
            self.df['high_low'] = self.df['high'] - self.df['low']
            self.df['high_close'] = (self.df['high'] - self.df['close'].shift()).abs()
            self.df['low_close'] = (self.df['low'] - self.df['close'].shift()).abs()
            self.df['tr'] = self.df[['high_low', 'high_close', 'low_close']].max(axis=1)
            self.df['atr'] = self.df['tr'].rolling(window=14).mean()
            
            for i in range(60, len(self.df)):  # 从第60天开始，确保有足够的历史数据
                row = self.df.iloc[i]

                # 从row中获取日期，如果没有date列则使用索引
                if 'date' in row.index:
                    date = row['date']
                else:
                    date = self.df.index[i]

                # 确保date是datetime对象
                if isinstance(date, str):
                    date = pd.to_datetime(date)
                elif isinstance(date, pd.Timestamp):
                    pass  # 已经是正确的格式
                else:
                    date = pd.Timestamp(date)
                
                # 每月增加资金
                capital = self.add_monthly_capital(date, capital)
                
                # 生成每日信号
                signal = self.generate_daily_signals(row, i, capital)
                self.daily_signals.append(signal)
                
                # 记录权益
                self.equity_curve.append({
                    'date': date,
                    'equity': capital,
                    'position': self.position
                })
                
                # 如果有持仓，检查止盈止损
                if self.position != 0:
                    if self.position == 1:  # 多头
                        profit_ratio = (row['high'] - self.current_price) / self.current_price
                        loss_ratio = (self.current_price - row['low']) / self.current_price
                        
                        if profit_ratio >= self.take_profit_long:  # 止盈
                            exit_price = self.current_price * (1 + self.take_profit_long)
                            position_size = self.trades[-1]['position_size']  # 获取开仓时的仓位大小
                            profit = (exit_price - self.current_price) / self.current_price * position_size
                            capital += profit
                            self.position = 0
                            self.win_count += 1
                            self.trades.append({
                                'date': date,
                                'type': 'long_exit_tp',
                                'price': exit_price,
                                'profit': profit,
                                'capital': capital,
                                'position_size': position_size,
                                'kelly': self.trades[-1]['kelly'],
                                'market_state': self.market_state
                            })
                        elif loss_ratio >= self.stop_loss_long:  # 止损
                            exit_price = self.current_price * (1 - self.stop_loss_long)
                            position_size = self.trades[-1]['position_size']  # 获取开仓时的仓位大小
                            loss = (exit_price - self.current_price) / self.current_price * position_size
                            capital += loss
                            self.position = 0
                            self.loss_count += 1
                            self.trades.append({
                                'date': date,
                                'type': 'long_exit_sl',
                                'price': exit_price,
                                'profit': loss,
                                'capital': capital,
                                'position_size': position_size,
                                'kelly': self.trades[-1]['kelly'],
                                'market_state': self.market_state
                            })
                    
                    elif self.position == -1:  # 空头
                        profit_ratio = (self.current_price - row['low']) / self.current_price
                        loss_ratio = (row['high'] - self.current_price) / self.current_price
                        
                        if profit_ratio >= self.take_profit_short:  # 止盈
                            exit_price = self.current_price * (1 - self.take_profit_short)
                            position_size = self.trades[-1]['position_size']  # 获取开仓时的仓位大小
                            profit = (self.current_price - exit_price) / self.current_price * position_size
                            capital += profit
                            self.position = 0
                            self.win_count += 1
                            self.trades.append({
                                'date': date,
                                'type': 'short_exit_tp',
                                'price': exit_price,
                                'profit': profit,
                                'capital': capital,
                                'position_size': position_size,
                                'kelly': self.trades[-1]['kelly'],
                                'market_state': self.market_state
                            })
                        elif loss_ratio >= self.stop_loss_short:  # 止损
                            exit_price = self.current_price * (1 + self.stop_loss_short)
                            position_size = self.trades[-1]['position_size']  # 获取开仓时的仓位大小
                            loss = (self.current_price - exit_price) / self.current_price * position_size
                            capital += loss
                            self.position = 0
                            self.loss_count += 1
                            self.trades.append({
                                'date': date,
                                'type': 'short_exit_sl',
                                'price': exit_price,
                                'profit': loss,
                                'capital': capital,
                                'position_size': position_size,
                                'kelly': self.trades[-1]['kelly'],
                                'market_state': self.market_state
                            })
                
                # 检测市场状态
                if i >= 60:  # 确保有足够的数据点
                    self.market_state = self.detect_market_trend(self.df, i)

                    # 计算趋势得分和波动率
                    trend_score = self.calculate_trend_score(row)
                    current_volatility = self.df['returns'].rolling(20).std().iloc[i] * np.sqrt(252)
                    if pd.isna(current_volatility):
                        current_volatility = 0.2  # 默认波动率

                    self.adjust_xy_thresholds(trend_score, current_volatility)
                
                # 更新胜率
                if self.total_trades > 0:
                    self.win_rate = self.win_count / self.total_trades
                
                # 如果空仓，判断是否开仓
                if self.position == 0:
                    # 计算凯利仓位
                    kelly_position = self.calculate_kelly()
                    position_size = capital * kelly_position
                    
                    # 使用动态阈值的Cosmoon策略判断
                    long_condition = (row['e_value'] > 0 and 
                                    row['x_value'] > self.x_threshold_long and 
                                    row['y_value'] > self.y_threshold_long)
                    
                    short_condition = (row['y_value'] < self.y_threshold_short or 
                                     row['x_value'] < self.x_threshold_short)
                    
                    # 多头信号
                    if long_condition and row['price_position'] < 0:  # 价格低于回归线
                        self.position = 1
                        self.current_price = row['close']
                        self.trades.append({
                            'date': date,
                            'type': 'long_entry',
                            'price': self.current_price,
                            'capital': capital,
                            'position_size': position_size,
                            'kelly': kelly_position,
                            'market_state': self.market_state
                        })
                        self.total_trades += 1
                    
                    # 空头信号
                    elif short_condition and row['price_position'] > 0:  # 价格高于回归线
                        self.position = -1
                        self.current_price = row['close']
                        self.trades.append({
                            'date': date,
                            'type': 'short_entry',
                            'price': self.current_price,
                            'capital': capital,
                            'position_size': position_size,
                            'kelly': kelly_position,
                            'market_state': self.market_state
                        })
                        self.total_trades += 1
            
            self.final_capital = capital
            print(f"✓ 回测完成！最终资金：{self.final_capital:,.2f}")
            
        except Exception as e:
            print(f"❌ 回测执行失败: {str(e)}")
            raise
    
    def save_daily_signals(self):
        """保存每日信号到Excel文件"""
        try:
            signals_df = pd.DataFrame(self.daily_signals)

            # 生成带时间戳的文件名，避免文件被占用
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

            # 尝试多个文件名，避免权限问题
            for i in range(5):
                try:
                    if i == 0:
                        output_file = f'daily_signals_{timestamp}.xlsx'
                    else:
                        output_file = f'daily_signals_{timestamp}_{i}.xlsx'

                    signals_df.to_excel(output_file, index=False)
                    print(f"✓ 每日信号已保存到 {output_file}")
                    return output_file
                except PermissionError:
                    continue

            # 如果Excel保存失败，尝试保存为CSV
            csv_file = f'daily_signals_{timestamp}.csv'
            signals_df.to_csv(csv_file, index=False, encoding='utf-8-sig')
            print(f"✓ 每日信号已保存到 {csv_file} (CSV格式)")
            return csv_file

        except Exception as e:
            print(f"❌ 保存每日信号失败: {str(e)}")
            return None
    
    def analyze_results(self):
        """分析回测结果"""
        print("\n4. 回测分析...")
        
        # 保存每日信号
        output_file = self.save_daily_signals()

        try:
            # 转换交易记录为DataFrame
            trades_df = pd.DataFrame(self.trades)
            if len(trades_df) == 0:
                print("没有产生任何交易")
                return
            
            # 计算基本统计数据
            total_trades = len(trades_df[trades_df['type'].str.contains('entry')])
            winning_trades = len(trades_df[trades_df['profit'] > 0]) if 'profit' in trades_df.columns else 0
            profit_trades = trades_df[trades_df['profit'] > 0] if 'profit' in trades_df.columns else pd.DataFrame()
            loss_trades = trades_df[trades_df['profit'] < 0] if 'profit' in trades_df.columns else pd.DataFrame()
            
            print(f"\n交易统计：")
            print(f"总交易次数：{total_trades}")
            print(f"盈利交易：{winning_trades}")
            print(f"亏损交易：{total_trades - winning_trades}")
            print(f"胜率：{winning_trades/total_trades*100:.2f}%")
            
            if len(profit_trades) > 0:
                print(f"平均盈利：{profit_trades['profit'].mean():,.2f}")
                print(f"最大盈利：{profit_trades['profit'].max():,.2f}")
            
            if len(loss_trades) > 0:
                print(f"平均亏损：{loss_trades['profit'].mean():,.2f}")
                print(f"最大亏损：{loss_trades['profit'].min():,.2f}")
            
            # 计算收益率
            initial_equity = self.initial_capital
            final_equity = self.final_capital
            total_days = (self.df['date'].max() - self.df['date'].min()).days
            total_years = total_days / 365
            
            total_return = (final_equity - initial_equity) / initial_equity
            annual_return = (1 + total_return) ** (1/total_years) - 1
            
            print(f"\n收益统计：")
            print(f"初始资金：{initial_equity:,.2f}")
            print(f"最终资金：{final_equity:,.2f}")
            print(f"总收益率：{total_return*100:.2f}%")
            print(f"年化收益率：{annual_return*100:.2f}%")
            
            # 计算复利考虑每月追加资金
            total_additions = (total_days // 30) * self.monthly_addition
            actual_return = (final_equity - initial_equity - total_additions) / (initial_equity + total_additions)
            print(f"考虑每月追加后的实际收益率：{actual_return*100:.2f}%")
            
            # 创建子图
            fig = plt.figure(figsize=(20, 15))
            
            # 1. 权益曲线（带持仓标记）
            ax1 = plt.subplot(221)
            equity_df = pd.DataFrame(self.equity_curve)
            equity_df['date'] = pd.to_datetime(equity_df['date'])
            
            # 绘制基础权益曲线
            ax1.plot(equity_df['date'], equity_df['equity'], 'b-', label='权益曲线', linewidth=1.5)
            
            # 标记多空持仓
            long_periods = equity_df[equity_df['position'] == 1]
            short_periods = equity_df[equity_df['position'] == -1]
            
            if len(long_periods) > 0:
                ax1.plot(long_periods['date'], long_periods['equity'], 'g.', label='多头', markersize=3)
            if len(short_periods) > 0:
                ax1.plot(short_periods['date'], short_periods['equity'], 'r.', label='空头', markersize=3)
            
            ax1.set_title('权益曲线与持仓分析')
            ax1.set_xlabel('日期')
            ax1.set_ylabel('资金')
            ax1.grid(True)
            ax1.legend()
            
            # 2. 月度收益分布
            ax2 = plt.subplot(222)
            monthly_returns = []
            
            for year in equity_df['date'].dt.year.unique():
                for month in range(1, 13):
                    month_data = equity_df[
                        (equity_df['date'].dt.year == year) & 
                        (equity_df['date'].dt.month == month)
                    ]
                    if len(month_data) > 0:
                        start_equity = month_data['equity'].iloc[0]
                        end_equity = month_data['equity'].iloc[-1]
                        monthly_return = (end_equity - start_equity) / start_equity * 100
                        monthly_returns.append(monthly_return)
            
            ax2.hist(monthly_returns, bins=50, color='blue', alpha=0.7)
            ax2.axvline(x=0, color='r', linestyle='--')
            ax2.set_title('月度收益分布')
            ax2.set_xlabel('收益率 (%)')
            ax2.set_ylabel('频次')
            
            # 3. 累计收益与回撤分析
            ax3 = plt.subplot(223)
            equity_df['return'] = equity_df['equity'].pct_change()
            equity_df['cum_return'] = (1 + equity_df['return']).cumprod()
            equity_df['cum_roll_max'] = equity_df['cum_return'].cummax()
            equity_df['drawdown'] = equity_df['cum_roll_max'] - equity_df['cum_return']
            
            ax3.plot(equity_df['date'], equity_df['cum_return'], 'b-', label='累计收益')
            ax3.plot(equity_df['date'], equity_df['cum_roll_max'], 'g--', label='历史新高')
            ax3.fill_between(equity_df['date'], 
                           equity_df['cum_return'], 
                           equity_df['cum_roll_max'], 
                           alpha=0.3, 
                           color='red', 
                           label='回撤')
            ax3.set_title('累计收益与回撤分析')
            ax3.set_xlabel('日期')
            ax3.set_ylabel('累计收益倍数')
            ax3.grid(True)
            ax3.legend()
            
            # 4. 交易盈亏分布
            ax4 = plt.subplot(224)
            trades_df['profit'].hist(bins=50, ax=ax4, color='blue', alpha=0.7)
            ax4.axvline(x=0, color='r', linestyle='--')
            ax4.set_title('交易盈亏分布')
            ax4.set_xlabel('盈亏金额')
            ax4.set_ylabel('频次')
            
            # 调整布局并保存
            plt.tight_layout()
            plt.savefig('trading_analysis.png', dpi=300, bbox_inches='tight')
            plt.close()
            
            # 保存交易记录
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

            # 尝试保存交易记录，避免权限问题
            trade_file_saved = False
            for i in range(5):
                try:
                    if i == 0:
                        trade_file = f'trade_records_{timestamp}.xlsx'
                    else:
                        trade_file = f'trade_records_{timestamp}_{i}.xlsx'

                    trades_df.to_excel(trade_file, index=False)
                    trade_file_saved = True
                    break
                except PermissionError:
                    continue

            if not trade_file_saved:
                # 如果Excel保存失败，保存为CSV
                trade_file = f'trade_records_{timestamp}.csv'
                trades_df.to_csv(trade_file, index=False, encoding='utf-8-sig')

            print("\n✓ 分析完成")
            print(f"• 交易记录已保存到 {trade_file}")
            print("• 详细分析图表已保存到 trading_analysis.png")
            
        except Exception as e:
            print(f"❌ 分析失败: {str(e)}")
            raise

        return output_file

def main():
    """主函数"""
    print("\n" + "="*50)
    print("0023.HK (东亚银行BEA) 3年回测系统")
    print("💰 初始资金: 3,000港币")
    print("📅 每月定投: 3,000港币")
    print("📊 策略: 回归线 + XY确认 + 不对称止盈止损")
    print("="*50 + "\n")
    
    try:
        # 创建回测实例
        backtest = BEABankBacktest()
        
        # 加载数据
        backtest.load_data()
        
        # 计算回归线
        backtest.calculate_regression_line()
        
        # 运行回测
        backtest.run_backtest()
        
        # 分析结果
        output_file = backtest.analyze_results()
        
        # 如果成功生成Excel文件，则尝试打开
        if output_file and os.path.exists(output_file):
            try:
                os.startfile(os.path.abspath(output_file))
                print(f"✓ 已自动打开: {os.path.abspath(output_file)}")
            except Exception as e:
                print(f"❌ 无法自动打开Excel文件，请手动打开: {os.path.abspath(output_file)}")
        
    except Exception as e:
        print(f"\n❌ 程序运行失败: {str(e)}")
    else:
        print("\n✅ 程序运行完成")

if __name__ == "__main__":
    main()
