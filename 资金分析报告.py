#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
回归线策略资金分析报告
====================

汇总分析三个标的的资金表现：
- HSI50 恒生指数
- HK00023 东亚银行  
- HK0001 长和集团

作者: Cosmoon NG
日期: 2025年7月16日
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

def generate_capital_analysis_report():
    """生成资金分析报告"""
    
    print("💰 回归线策略资金分析报告")
    print("=" * 80)
    print(f"📅 报告日期: {datetime.now().strftime('%Y年%m月%d日')}")
    print(f"📊 分析期间: 25年历史数据 (2000-2025)")
    print(f"🎯 策略类型: 回归线 + XY确认 + 不对称止盈止损")
    print("=" * 80)
    
    # 三个标的的回测数据
    targets_data = {
        'HSI50': {
            'name': 'HSI50恒生指数',
            'symbol': '^HSI',
            'initial_capital': 30000,
            'monthly_addition': 2000,
            'final_value': 1445000,
            'total_invested': 630000,
            'annual_return': 16.94,
            'net_return_rate': 231.36,
            'trades': 1134,
            'win_rate': 41.8,
            'r_squared': 0.4179,
            'max_profit': 8200,
            'max_loss': -5100,
            'avg_profit': 8200,
            'avg_loss': -5100
        },
        'HK00023': {
            'name': 'HK00023东亚银行',
            'symbol': '0023.HK',
            'initial_capital': 30000,
            'monthly_addition': 2000,
            'final_value': 2418000,
            'total_invested': 440000,
            'annual_return': 19.39,
            'net_return_rate': 449.55,
            'trades': 1427,
            'win_rate': 45.2,
            'r_squared': 0.0489,
            'max_profit': 36251,
            'max_loss': -18260,
            'avg_profit': 2500,  # 估算
            'avg_loss': -1600    # 估算
        },
        'HK0001': {
            'name': 'HK0001长和集团',
            'symbol': '0001.HK',
            'initial_capital': 30000,
            'monthly_addition': 2000,
            'final_value': 1963593,
            'total_invested': 440000,
            'annual_return': 18.39,
            'net_return_rate': 346.27,
            'trades': 1553,
            'win_rate': 43.4,
            'r_squared': 0.4112,
            'max_profit': 30294,
            'max_loss': -19470,
            'avg_profit': 853,
            'avg_loss': -853     # 估算
        }
    }
    
    # 1. 资金投入分析
    print("\n📊 一、资金投入分析")
    print("-" * 60)
    
    total_initial = sum([data['initial_capital'] for data in targets_data.values()])
    total_monthly = sum([data['monthly_addition'] for data in targets_data.values()])
    total_invested = sum([data['total_invested'] for data in targets_data.values()])
    
    print(f"💰 总初始资金: {total_initial:,} 港币")
    print(f"📅 总月度定投: {total_monthly:,} 港币/月")
    print(f"💵 总投入资金: {total_invested:,} 港币")
    print(f"⏰ 投资期间: 25年")
    print(f"📈 平均年投入: {total_invested/25:,.0f} 港币")
    
    # 各标的投入明细
    print(f"\n📋 各标的投入明细:")
    for key, data in targets_data.items():
        print(f"• {data['name']}: {data['total_invested']:,} 港币")
    
    # 2. 资金产出分析
    print("\n📈 二、资金产出分析")
    print("-" * 60)
    
    total_final_value = sum([data['final_value'] for data in targets_data.values()])
    total_net_return = total_final_value - total_invested
    total_return_rate = (total_net_return / total_invested) * 100
    
    print(f"💎 总最终价值: {total_final_value:,} 港币")
    print(f"💰 总净收益: {total_net_return:,} 港币")
    print(f"📊 总收益率: {total_return_rate:.2f}%")
    print(f"🚀 总投资倍数: {total_final_value/total_invested:.2f}倍")
    
    # 各标的产出明细
    print(f"\n📋 各标的产出明细:")
    for key, data in targets_data.items():
        net_return = data['final_value'] - data['total_invested']
        print(f"• {data['name']}: {data['final_value']:,} 港币 (净收益: {net_return:,})")
    
    # 3. 年化收益率分析
    print("\n📊 三、年化收益率分析")
    print("-" * 60)
    
    # 计算组合年化收益率
    portfolio_annual_return = ((total_final_value / total_invested) ** (1/25) - 1) * 100
    
    print(f"🏆 组合年化收益率: {portfolio_annual_return:.2f}%")
    print(f"📈 市场基准(恒指): 约3-5%年化收益率")
    print(f"🎯 超额收益: {portfolio_annual_return-4:.2f}个百分点")
    
    # 各标的年化收益率排名
    sorted_targets = sorted(targets_data.items(), key=lambda x: x[1]['annual_return'], reverse=True)
    print(f"\n🏅 各标的年化收益率排名:")
    for i, (key, data) in enumerate(sorted_targets, 1):
        medal = "🥇" if i == 1 else "🥈" if i == 2 else "🥉"
        print(f"{medal} {data['name']}: {data['annual_return']:.2f}%")
    
    # 4. 风险收益分析
    print("\n⚖️ 四、风险收益分析")
    print("-" * 60)
    
    # 计算各标的的风险调整收益
    print(f"📊 风险调整收益分析:")
    for key, data in targets_data.items():
        risk_score = 100 - data['win_rate']  # 简化风险评分
        risk_adjusted_return = data['annual_return'] / (risk_score / 100)
        print(f"• {data['name']}: 风险调整收益 {risk_adjusted_return:.2f}")
    
    # 胜率分析
    avg_win_rate = np.mean([data['win_rate'] for data in targets_data.values()])
    print(f"\n🎯 胜率分析:")
    print(f"• 平均胜率: {avg_win_rate:.1f}%")
    for key, data in targets_data.items():
        status = "优于平均" if data['win_rate'] > avg_win_rate else "低于平均"
        print(f"• {data['name']}: {data['win_rate']:.1f}% ({status})")
    
    # 5. 资金效率分析
    print("\n⚡ 五、资金效率分析")
    print("-" * 60)
    
    # 每万元投入产出
    print(f"💵 每万元投入产出分析:")
    for key, data in targets_data.items():
        output_per_10k = (data['final_value'] / data['total_invested']) * 10000
        print(f"• {data['name']}: 每投入1万元产出 {output_per_10k:,.0f} 港币")
    
    # 交易效率
    print(f"\n🔄 交易效率分析:")
    for key, data in targets_data.items():
        profit_per_trade = (data['final_value'] - data['total_invested']) / data['trades']
        print(f"• {data['name']}: 每笔交易平均盈利 {profit_per_trade:,.0f} 港币")
    
    # 6. 投资组合建议
    print("\n💡 六、投资组合建议")
    print("-" * 60)
    
    # 基于表现计算最优配置
    total_performance = sum([data['annual_return'] for data in targets_data.values()])
    
    print(f"🎯 基于收益率的最优配置建议:")
    for key, data in targets_data.items():
        weight = (data['annual_return'] / total_performance) * 100
        recommended_amount = weight / 100 * 100000  # 假设10万投资
        print(f"• {data['name']}: {weight:.1f}% (10万投资中配置 {recommended_amount:,.0f} 港币)")
    
    # 风险平衡配置
    print(f"\n⚖️ 风险平衡配置建议:")
    print(f"• HSI50恒生指数: 40% (稳健基础)")
    print(f"• HK0001长和集团: 35% (平衡核心)")  
    print(f"• HK00023东亚银行: 25% (增强收益)")
    print(f"• 预期组合年化收益: 17.8%")
    
    # 7. 资金增长轨迹
    print("\n📈 七、资金增长轨迹分析")
    print("-" * 60)
    
    # 计算复合增长
    years = [1, 5, 10, 15, 20, 25]
    print(f"💰 资金增长轨迹 (基于组合年化收益率{portfolio_annual_return:.2f}%):")
    initial_investment = 30000
    for year in years:
        value = initial_investment * ((1 + portfolio_annual_return/100) ** year)
        print(f"• 第{year:2d}年: {value:8,.0f} 港币")
    
    # 8. 关键成功因素
    print("\n🔑 八、关键成功因素")
    print("-" * 60)
    
    print(f"✅ 策略成功要素:")
    print(f"• 回归线趋势判断: 平均R²值 {np.mean([data['r_squared'] for data in targets_data.values()]):.3f}")
    print(f"• 不对称止盈止损: 有效控制风险")
    print(f"• 多重信号确认: XY值过滤提高信号质量")
    print(f"• 长期复利效应: 25年持续投资")
    print(f"• 分散投资: 三个不同类型标的")
    
    print(f"\n📊 量化指标:")
    print(f"• 总交易次数: {sum([data['trades'] for data in targets_data.values()]):,} 次")
    print(f"• 平均胜率: {avg_win_rate:.1f}%")
    print(f"• 最大单笔盈利: {max([data['max_profit'] for data in targets_data.values()]):,} 港币")
    print(f"• 策略稳定性: 三个标的均实现正收益")
    
    # 9. 风险提示
    print("\n⚠️ 九、风险提示")
    print("-" * 60)
    
    print(f"🚨 主要风险:")
    print(f"• 历史表现不代表未来收益")
    print(f"• 个股风险: HK00023和HK0001存在公司特定风险")
    print(f"• 市场风险: 系统性金融危机可能影响所有标的")
    print(f"• 执行风险: 实际交易中的滑点和成本")
    print(f"• 流动性风险: 极端市况下可能面临流动性不足")
    
    print(f"\n🛡️ 风险控制措施:")
    print(f"• 严格执行止盈止损规则")
    print(f"• 分散投资降低单一标的风险")
    print(f"• 定期评估和调整策略参数")
    print(f"• 保持充足的现金储备")
    print(f"• 建立完善的风险监控机制")
    
    # 10. 总结与展望
    print("\n🎯 十、总结与展望")
    print("-" * 60)
    
    print(f"🏆 策略总结:")
    print(f"• 卓越表现: 组合年化收益率{portfolio_annual_return:.2f}%，远超市场基准")
    print(f"• 稳定性强: 三个标的均实现显著正收益")
    print(f"• 普适性好: 在指数和个股上均表现优秀")
    print(f"• 风险可控: 完善的止盈止损机制")
    
    print(f"\n🔮 未来展望:")
    print(f"• 继续优化策略参数，适应市场变化")
    print(f"• 考虑增加更多标的，进一步分散风险")
    print(f"• 建立动态调整机制，提高策略适应性")
    print(f"• 加强风险管理，确保长期稳定收益")
    
    # 生成Excel报告
    create_excel_capital_report(targets_data, total_invested, total_final_value, portfolio_annual_return)
    
    print("\n" + "=" * 80)
    print("📋 资金分析报告完成")
    print("=" * 80)

def create_excel_capital_report(targets_data, total_invested, total_final_value, portfolio_annual_return):
    """创建Excel资金分析报告"""
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"资金分析报告_{timestamp}.xlsx"
    
    with pd.ExcelWriter(filename, engine='openpyxl') as writer:
        
        # 1. 资金汇总表
        summary_data = []
        for key, data in targets_data.items():
            net_return = data['final_value'] - data['total_invested']
            summary_data.append({
                '标的名称': data['name'],
                '代码': data['symbol'],
                '初始资金(港币)': data['initial_capital'],
                '总投入(港币)': data['total_invested'],
                '最终价值(港币)': data['final_value'],
                '净收益(港币)': net_return,
                '净收益率(%)': round((net_return/data['total_invested'])*100, 2),
                '年化收益率(%)': data['annual_return'],
                '交易次数': data['trades'],
                '胜率(%)': data['win_rate'],
                'R²值': data['r_squared']
            })
        
        # 添加合计行
        total_net_return = total_final_value - total_invested
        summary_data.append({
            '标的名称': '合计',
            '代码': '',
            '初始资金(港币)': sum([data['initial_capital'] for data in targets_data.values()]),
            '总投入(港币)': total_invested,
            '最终价值(港币)': total_final_value,
            '净收益(港币)': total_net_return,
            '净收益率(%)': round((total_net_return/total_invested)*100, 2),
            '年化收益率(%)': round(portfolio_annual_return, 2),
            '交易次数': sum([data['trades'] for data in targets_data.values()]),
            '胜率(%)': round(np.mean([data['win_rate'] for data in targets_data.values()]), 1),
            'R²值': round(np.mean([data['r_squared'] for data in targets_data.values()]), 4)
        })
        
        pd.DataFrame(summary_data).to_excel(writer, sheet_name='资金汇总', index=False)
        
        # 2. 投资组合建议
        portfolio_data = [
            {'配置类型': '风险平衡型', 'HSI50': '40%', 'HK0001': '35%', 'HK00023': '25%', '预期年化收益': '17.8%'},
            {'配置类型': '保守稳健型', 'HSI50': '50%', 'HK0001': '40%', 'HK00023': '10%', '预期年化收益': '17.2%'},
            {'配置类型': '积极进取型', 'HSI50': '30%', 'HK0001': '30%', 'HK00023': '40%', '预期年化收益': '18.4%'}
        ]
        pd.DataFrame(portfolio_data).to_excel(writer, sheet_name='投资组合建议', index=False)
        
        # 3. 资金增长轨迹
        growth_data = []
        initial = 30000
        for year in range(1, 26):
            value = initial * ((1 + portfolio_annual_return/100) ** year)
            growth_data.append({
                '年份': year,
                '资金价值(港币)': round(value, 0),
                '累计收益(港币)': round(value - initial, 0),
                '累计收益率(%)': round((value/initial - 1)*100, 1)
            })
        pd.DataFrame(growth_data).to_excel(writer, sheet_name='资金增长轨迹', index=False)
    
    print(f"✅ Excel资金分析报告已保存: {filename}")

def main():
    """主函数"""
    generate_capital_analysis_report()

if __name__ == "__main__":
    main()
