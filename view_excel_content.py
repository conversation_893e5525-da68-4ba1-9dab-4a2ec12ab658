#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
查看Excel文件内容
================
读取并显示Excel文件的内容
"""

import pandas as pd
import os
from datetime import datetime

def view_excel_file(filename):
    """查看Excel文件内容"""
    
    if not os.path.exists(filename):
        print(f"❌ 文件不存在: {filename}")
        return
    
    try:
        print(f"📄 读取Excel文件: {filename}")
        print("=" * 80)
        
        # 读取Excel文件
        df = pd.read_excel(filename)
        
        print(f"📊 文件信息:")
        print(f"   记录数量: {len(df)} 条")
        print(f"   字段数量: {len(df.columns)} 个")
        print(f"   文件大小: {os.path.getsize(filename):,} 字节")
        
        print(f"\n📋 字段列表:")
        for i, col in enumerate(df.columns, 1):
            print(f"   {i:2d}. {col}")
        
        print(f"\n📅 前10条记录:")
        print("-" * 120)
        
        # 显示前10条记录
        display_df = df.head(10)
        
        # 格式化显示
        pd.set_option('display.max_columns', None)
        pd.set_option('display.width', None)
        pd.set_option('display.max_colwidth', 20)
        
        print(display_df.to_string(index=False))
        
        print(f"\n📊 数据统计:")
        print("-" * 60)
        
        # 统计信息
        if '方向' in df.columns:
            direction_counts = df['方向'].value_counts()
            print(f"交易方向统计:")
            for direction, count in direction_counts.items():
                print(f"   {direction}: {count}次")
        
        if '收益' in df.columns:
            total_profit = df['收益'].sum()
            profitable_trades = len(df[df['收益'] > 0])
            total_trades = len(df[df['收益'] != 0])
            
            print(f"\n收益统计:")
            print(f"   总收益: {total_profit:.2f}")
            print(f"   盈利交易: {profitable_trades}次")
            print(f"   总交易: {total_trades}次")
            if total_trades > 0:
                win_rate = profitable_trades / total_trades * 100
                print(f"   胜率: {win_rate:.1f}%")
        
        if '总资本' in df.columns:
            initial_capital = df['总资本'].iloc[0]
            final_capital = df['总资本'].iloc[-1]
            total_return = (final_capital - initial_capital) / initial_capital * 100
            
            print(f"\n资本统计:")
            print(f"   初始资本: {initial_capital:,.2f}")
            print(f"   最终资本: {final_capital:,.2f}")
            print(f"   总收益率: {total_return:.2f}%")
        
        print(f"\n📅 最后10条记录:")
        print("-" * 120)
        
        # 显示最后10条记录
        display_df = df.tail(10)
        print(display_df.to_string(index=False))
        
    except Exception as e:
        print(f"❌ 读取文件失败: {e}")

def list_excel_files():
    """列出所有Excel文件"""
    
    print("📁 当前目录中的Excel文件:")
    print("=" * 60)
    
    excel_files = []
    for file in os.listdir('.'):
        if file.endswith('.xlsx') or file.endswith('.xls'):
            excel_files.append(file)
    
    if not excel_files:
        print("❌ 未找到Excel文件")
        return []
    
    # 按修改时间排序
    excel_files.sort(key=lambda x: os.path.getmtime(x), reverse=True)
    
    for i, file in enumerate(excel_files[:20], 1):  # 只显示前20个
        mtime = datetime.fromtimestamp(os.path.getmtime(file))
        size = os.path.getsize(file)
        print(f"   {i:2d}. {file}")
        print(f"       修改时间: {mtime.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"       文件大小: {size:,} 字节")
        print()
    
    return excel_files

def main():
    """主函数"""
    print("📊 Excel文件查看器")
    print("=" * 80)
    
    # 列出Excel文件
    excel_files = list_excel_files()
    
    if not excel_files:
        return
    
    # 查看最新的东亚银行文件
    eab_files = [f for f in excel_files if '东亚银行' in f or 'HK00023' in f]
    
    if eab_files:
        latest_eab_file = eab_files[0]  # 最新的东亚银行文件
        print(f"\n🏦 查看最新的东亚银行文件: {latest_eab_file}")
        view_excel_file(latest_eab_file)
    else:
        print(f"\n❌ 未找到东亚银行相关的Excel文件")
        
        # 查看最新的Excel文件
        if excel_files:
            latest_file = excel_files[0]
            print(f"\n📄 查看最新的Excel文件: {latest_file}")
            view_excel_file(latest_file)

if __name__ == "__main__":
    main()
