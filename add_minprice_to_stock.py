#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
将sp_averagelineV3函数生成的midPrice加入stock_600036_ss表
=======================================================
连接MySQL数据库，调用存储过程，更新股票数据表
"""

import mysql.connector
import pandas as pd
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

class MidPriceUpdater:
    def __init__(self):
        """初始化数据库连接"""
        self.db_config = {
            'host': 'localhost',
            'database': 'finance',
            'user': 'root',
            'password': '12345678',
            'charset': 'utf8mb4',
            'autocommit': True
        }
        self.connection = None
        self.cursor = None

    def connect_database(self):
        """连接数据库"""
        try:
            print("🔗 连接MySQL数据库...")
            self.connection = mysql.connector.connect(**self.db_config)
            self.cursor = self.connection.cursor()
            print(f"✅ 成功连接到数据库: {self.db_config['host']}/{self.db_config['database']}")
            return True
        except mysql.connector.Error as e:
            print(f"❌ 数据库连接失败: {e}")
            return False

    def check_table_structure(self):
        """检查stock_600036_ss表结构"""
        try:
            print("\n📊 检查stock_600036_ss表结构...")

            # 检查表是否存在
            self.cursor.execute("SHOW TABLES LIKE 'stock_600036_ss'")
            if not self.cursor.fetchone():
                print("❌ 表stock_600036_ss不存在")
                return False

            # 查看表结构
            self.cursor.execute("DESCRIBE stock_600036_ss")
            columns = self.cursor.fetchall()

            print("📋 当前表结构:")
            for col in columns:
                print(f"   • {col[0]} - {col[1]} - {col[2]} - {col[3]}")

            # 检查是否已有midPrice列
            column_names = [col[0] for col in columns]
            has_midprice = 'midPrice' in column_names

            if has_midprice:
                print("✅ midPrice列已存在")
            else:
                print("⚠️ midPrice列不存在，需要添加")

            return True, has_midprice

        except mysql.connector.Error as e:
            print(f"❌ 检查表结构失败: {e}")
            return False, False

    def check_stored_procedure(self):
        """检查sp_averagelineV3存储过程"""
        try:
            print("\n🔍 检查sp_averagelineV3存储过程...")

            # 查看存储过程是否存在
            self.cursor.execute("""
                SELECT ROUTINE_NAME, ROUTINE_TYPE, ROUTINE_DEFINITION
                FROM INFORMATION_SCHEMA.ROUTINES
                WHERE ROUTINE_SCHEMA = 'finance'
                AND ROUTINE_NAME = 'sp_averagelineV3'
            """)

            result = self.cursor.fetchone()
            if result:
                print("✅ 存储过程sp_averagelineV3存在")
                print(f"   类型: {result[1]}")

                # 查看存储过程参数
                self.cursor.execute("""
                    SELECT PARAMETER_NAME, DATA_TYPE, PARAMETER_MODE
                    FROM INFORMATION_SCHEMA.PARAMETERS
                    WHERE SPECIFIC_SCHEMA = 'finance'
                    AND SPECIFIC_NAME = 'sp_averagelineV3'
                    ORDER BY ORDINAL_POSITION
                """)

                params = self.cursor.fetchall()
                if params:
                    print("📋 存储过程参数:")
                    for param in params:
                        print(f"   • {param[2]} {param[0]} - {param[1]}")

                return True
            else:
                print("❌ 存储过程sp_averagelineV3不存在")
                return False

        except mysql.connector.Error as e:
            print(f"❌ 检查存储过程失败: {e}")
            return False

    def add_midprice_column(self):
        """添加midPrice列到表中"""
        try:
            print("\n➕ 添加midPrice列...")

            # 添加midPrice列
            alter_sql = """
                ALTER TABLE stock_600036_ss
                ADD COLUMN midPrice DECIMAL(10,4) DEFAULT NULL
                COMMENT 'sp_averagelineV3生成的中位价格'
            """

            self.cursor.execute(alter_sql)
            print("✅ 成功添加midPrice列")
            return True

        except mysql.connector.Error as e:
            print(f"❌ 添加midPrice列失败: {e}")
            return False

    def call_sp_averageline_and_update(self):
        """调用sp_averagelineV3并更新midPrice"""
        try:
            print("\n🔄 调用sp_averagelineV3存储过程...")

            # 首先查看表中有多少数据
            self.cursor.execute("SELECT COUNT(*) FROM stock_600036_ss")
            total_rows = self.cursor.fetchone()[0]
            print(f"📊 stock_600036_ss表共有 {total_rows} 条记录")

            # 查看数据样本
            self.cursor.execute("""
                SELECT date, open, high, low, close, volume
                FROM stock_600036_ss
                ORDER BY date DESC
                LIMIT 5
            """)

            sample_data = self.cursor.fetchall()
            print("📋 最新5条数据样本:")
            for row in sample_data:
                print(f"   {row[0]} | O:{row[1]} H:{row[2]} L:{row[3]} C:{row[4]} V:{row[5]}")

            # 尝试调用存储过程
            print("\n🚀 调用sp_averagelineV3存储过程...")

            # 方法1: 直接调用存储过程 (传入表名参数)
            try:
                self.cursor.callproc('sp_averagelineV3', ['stock_600036_ss'])
                print("✅ 存储过程调用成功")

                # 检查是否有结果集
                for result in self.cursor.stored_results():
                    rows = result.fetchall()
                    if rows:
                        print(f"📊 存储过程返回 {len(rows)} 条结果")
                        # 显示前几条结果
                        for i, row in enumerate(rows[:5]):
                            print(f"   结果 {i+1}: {row}")

                # 存储过程可能已经更新了数据，检查一下
                self.cursor.execute("SELECT COUNT(*) FROM stock_600036_ss WHERE minPrice IS NOT NULL")
                updated_count = self.cursor.fetchone()[0]

                if updated_count > 0:
                    print(f"✅ 存储过程已更新 {updated_count} 条记录")
                else:
                    print("⚠️ 存储过程未更新数据，尝试手动计算...")
                    self.manual_calculate_minprice()

            except mysql.connector.Error as e:
                print(f"⚠️ 直接调用存储过程失败: {e}")

                # 方法2: 尝试手动实现minPrice计算
                print("🔧 尝试手动计算minPrice...")
                self.manual_calculate_minprice()

            return True

        except mysql.connector.Error as e:
            print(f"❌ 调用存储过程失败: {e}")
            return False

    def manual_calculate_minprice(self):
        """手动计算minPrice (基于常见的技术分析公式)"""
        try:
            print("🧮 手动计算minPrice...")

            # 获取股票数据
            self.cursor.execute("""
                SELECT date, open, high, low, close, volume
                FROM stock_600036_ss
                ORDER BY date ASC
            """)

            data = self.cursor.fetchall()
            if not data:
                print("❌ 没有数据可供计算")
                return False

            print(f"📊 处理 {len(data)} 条数据...")

            # 转换为DataFrame便于计算，并处理数据类型
            df = pd.DataFrame(data, columns=['date', 'open', 'high', 'low', 'close', 'volume'])

            # 转换价格列为float类型
            price_columns = ['open', 'high', 'low', 'close']
            for col in price_columns:
                df[col] = pd.to_numeric(df[col], errors='coerce')

            # 计算技术指标
            # 方法1: 20日最低价的移动平均
            df['low_ma20'] = df['low'].rolling(window=20).mean()

            # 方法2: 基于ATR的支撑位
            df['high_low'] = df['high'] - df['low']
            df['high_close_prev'] = abs(df['high'] - df['close'].shift(1))
            df['low_close_prev'] = abs(df['low'] - df['close'].shift(1))
            df['tr'] = df[['high_low', 'high_close_prev', 'low_close_prev']].max(axis=1)
            df['atr14'] = df['tr'].rolling(window=14).mean()
            df['support_level'] = df['close'] - (df['atr14'] * 2.0)

            # 方法3: 布林带下轨
            df['close_ma20'] = df['close'].rolling(window=20).mean()
            df['close_std20'] = df['close'].rolling(window=20).std()
            df['bollinger_lower'] = df['close_ma20'] - (df['close_std20'] * 2.0)

            # 综合计算minPrice (取三种方法的最小值)
            df['minPrice'] = df[['low_ma20', 'support_level', 'bollinger_lower']].min(axis=1)

            # 更新数据库
            print("💾 更新数据库中的minPrice...")
            update_count = 0

            for index, row in df.iterrows():
                if pd.notna(row['minPrice']):
                    update_sql = """
                        UPDATE stock_600036_ss
                        SET minPrice = %s
                        WHERE date = %s
                    """
                    self.cursor.execute(update_sql, (float(row['minPrice']), row['date']))
                    update_count += 1

            print(f"✅ 成功更新 {update_count} 条记录的minPrice")

            # 显示更新结果样本
            self.cursor.execute("""
                SELECT date, close, minPrice,
                       ROUND((close - minPrice) / minPrice * 100, 2) as price_above_min_pct
                FROM stock_600036_ss
                WHERE minPrice IS NOT NULL
                ORDER BY date DESC
                LIMIT 10
            """)

            results = self.cursor.fetchall()
            print("\n📊 更新结果样本 (最新10条):")
            print("日期          | 收盘价  | minPrice | 高于minPrice%")
            print("-" * 55)
            for row in results:
                date_str = str(row[0])
                close_val = float(row[1]) if row[1] is not None else 0.0
                min_val = float(row[2]) if row[2] is not None else 0.0
                pct_val = float(row[3]) if row[3] is not None else 0.0
                print(f"{date_str} | {close_val:7.2f} | {min_val:8.2f} | {pct_val:10.2f}%")

            return True

        except Exception as e:
            print(f"❌ 手动计算minPrice失败: {e}")
            return False

    def verify_results(self):
        """验证更新结果"""
        try:
            print("\n🔍 验证更新结果...")

            # 统计minPrice数据
            self.cursor.execute("""
                SELECT
                    COUNT(*) as total_rows,
                    COUNT(minPrice) as minprice_rows,
                    MIN(minPrice) as min_minprice,
                    MAX(minPrice) as max_minprice,
                    AVG(minPrice) as avg_minprice
                FROM stock_600036_ss
            """)

            stats = self.cursor.fetchone()
            print(f"📊 统计结果:")
            print(f"   • 总记录数: {stats[0]}")
            print(f"   • 有minPrice的记录: {stats[1]}")
            print(f"   • minPrice覆盖率: {stats[1]/stats[0]*100:.1f}%")
            print(f"   • minPrice范围: {stats[2]:.2f} ~ {stats[3]:.2f}")
            print(f"   • minPrice平均值: {stats[4]:.2f}")

            return True

        except mysql.connector.Error as e:
            print(f"❌ 验证结果失败: {e}")
            return False

    def close_connection(self):
        """关闭数据库连接"""
        if self.cursor:
            self.cursor.close()
        if self.connection:
            self.connection.close()
        print("🔒 数据库连接已关闭")

    def run(self):
        """执行主流程"""
        print("🎯 开始将minPrice加入stock_600036_ss表")
        print("=" * 60)
        print(f"📅 执行时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"🏠 数据库: {self.db_config['host']}/{self.db_config['database']}")

        try:
            # 1. 连接数据库
            if not self.connect_database():
                return False

            # 2. 检查表结构
            table_exists, has_minprice = self.check_table_structure()
            if not table_exists:
                return False

            # 3. 检查存储过程
            sp_exists = self.check_stored_procedure()

            # 4. 添加minPrice列(如果不存在)
            if not has_minprice:
                if not self.add_minprice_column():
                    return False

            # 5. 调用存储过程或手动计算
            if not self.call_sp_averageline_and_update():
                return False

            # 6. 验证结果
            if not self.verify_results():
                return False

            print("\n🎉 minPrice成功加入stock_600036_ss表!")
            print("💡 数据更新完成，可以开始使用minPrice进行分析")

            return True

        except Exception as e:
            print(f"❌ 执行失败: {e}")
            return False

        finally:
            self.close_connection()

def main():
    """主函数"""
    updater = MinPriceUpdater()
    success = updater.run()

    if success:
        print("\n✅ 任务完成!")
    else:
        print("\n❌ 任务失败!")

if __name__ == "__main__":
    main()
