#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复sp_updatecontroller中的参数错误
=================================
修复调用sp_averagelineV3时的参数数量错误
"""

import mysql.connector
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

class ParameterFixer:
    def __init__(self):
        """初始化数据库连接"""
        self.db_config = {
            'host': 'localhost',
            'database': 'finance',
            'user': 'root',
            'password': '12345678',
            'charset': 'utf8mb4',
            'autocommit': True
        }
        self.connection = None
        self.cursor = None
        
    def connect_database(self):
        """连接数据库"""
        try:
            print("🔗 连接MySQL数据库...")
            self.connection = mysql.connector.connect(**self.db_config)
            self.cursor = self.connection.cursor()
            print(f"✅ 成功连接到数据库: {self.db_config['host']}/{self.db_config['database']}")
            return True
        except mysql.connector.Error as e:
            print(f"❌ 数据库连接失败: {e}")
            return False
    
    def backup_current_controller(self):
        """备份当前的sp_updatecontroller"""
        try:
            print("🔄 备份当前sp_updatecontroller...")
            
            # 检查存储过程是否存在
            self.cursor.execute("""
                SELECT COUNT(*) FROM information_schema.ROUTINES 
                WHERE ROUTINE_SCHEMA = 'finance' 
                AND ROUTINE_NAME = 'sp_updatecontroller'
            """)
            
            exists = self.cursor.fetchone()[0]
            
            if exists > 0:
                # 获取当前存储过程定义
                self.cursor.execute("SHOW CREATE PROCEDURE sp_updatecontroller")
                result = self.cursor.fetchone()
                
                if result:
                    # 创建备份
                    backup_name = f"sp_updatecontroller_backup_param_fix_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
                    backup_sql = result[2].replace(
                        'CREATE DEFINER=`root`@`%` PROCEDURE `sp_updatecontroller`',
                        f'CREATE PROCEDURE `{backup_name}`'
                    ).replace(
                        'CREATE PROCEDURE `sp_updatecontroller`',
                        f'CREATE PROCEDURE `{backup_name}`'
                    )
                    
                    self.cursor.execute(backup_sql)
                    print(f"✅ 已备份为: {backup_name}")
                    
                    return True
                else:
                    print("❌ 无法获取当前存储过程定义")
                    return False
            else:
                print("⚠️ sp_updatecontroller不存在，将直接创建新的")
                return True
                
        except mysql.connector.Error as e:
            print(f"❌ 备份检查失败: {e}")
            return False
    
    def create_fixed_controller(self):
        """创建修复参数错误的sp_updatecontroller"""
        try:
            print("🔧 创建修复参数错误的sp_updatecontroller...")
            
            # 删除现有的存储过程
            self.cursor.execute("DROP PROCEDURE IF EXISTS sp_updatecontroller")
            
            # 创建修复后的存储过程
            fixed_procedure = """
CREATE PROCEDURE sp_updatecontroller(
    IN tablename VARCHAR(64),
    OUT result_k DECIMAL(20,6)
)
BEGIN
    DECLARE col_exists INT DEFAULT 0;
    DECLARE v_start_time DATETIME DEFAULT NOW();
    
    -- 1. 首先更新midprice (调用sp_averagelineV3) - 只传递1个参数
    SELECT CONCAT('开始更新midprice - 表: ', tablename, ' 时间: ', v_start_time) AS start_message;
    CALL sp_averagelineV3(tablename);
    SELECT 'midprice更新完成' AS midprice_status;
    
    -- 2. 检查controller列是否存在
    SET @sql = CONCAT(
        'SELECT COUNT(*) INTO @col_exists FROM information_schema.COLUMNS ',
        'WHERE TABLE_SCHEMA=DATABASE() AND TABLE_NAME=''', tablename, ''' AND COLUMN_NAME=''controller'''
    );
    PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;
    SET col_exists = @col_exists;

    IF col_exists = 0 THEN
        SET @sql = CONCAT('ALTER TABLE `', tablename, '` ADD COLUMN `controller` INT DEFAULT NULL');
        PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;
        SELECT 'controller列已添加' AS controller_status;
    END IF;
    
    -- 3. 检查Full_Y列是否存在
    SET @sql = CONCAT(
        'SELECT COUNT(*) INTO @col_exists FROM information_schema.COLUMNS ',
        'WHERE TABLE_SCHEMA=DATABASE() AND TABLE_NAME=''', tablename, ''' AND COLUMN_NAME=''Full_Y'''
    );
    PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;
    SET col_exists = @col_exists;

    IF col_exists = 0 THEN
        SET @sql = CONCAT('ALTER TABLE `', tablename, '` ADD COLUMN `Full_Y` DECIMAL(20,10) DEFAULT NULL');
        PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;
        SELECT 'Full_Y列已添加' AS full_y_status;
    END IF;

    -- 4. 计算controller和Full_Y (新逻辑)
    SELECT '开始计算controller和Full_Y...' AS calc_status;
    
    -- 使用窗口函数计算累积controller和Full_Y
    SET @sql = CONCAT(
        'UPDATE `', tablename, '` t1 ',
        'JOIN (',
        '  SELECT date, ',
        '    SUM(CASE WHEN (close - midprice) > 0 THEN 1 ELSE 0 END) ',
        '    OVER (ORDER BY date ASC ROWS UNBOUNDED PRECEDING) as cumulative_controller, ',
        '    ROW_NUMBER() OVER (ORDER BY date ASC) as row_num ',
        '  FROM `', tablename, '` ',
        '  WHERE midprice IS NOT NULL ',
        '  ORDER BY date ASC',
        ') t2 ON t1.date = t2.date ',
        'SET t1.controller = t2.cumulative_controller, ',
        '    t1.Full_Y = t2.cumulative_controller / t2.row_num'
    );
    PREPARE stmt FROM @sql;
    EXECUTE stmt;
    DEALLOCATE PREPARE stmt;
    
    SELECT 'controller和Full_Y计算完成' AS calc_complete_status;

    -- 5. 计算k值 (最终controller值除以总记录数)
    SET @sql = CONCAT(
        'SELECT MAX(controller) / COUNT(*) INTO @k_value FROM `', tablename, '` WHERE controller IS NOT NULL'
    );
    PREPARE stmt FROM @sql;
    EXECUTE stmt;
    DEALLOCATE PREPARE stmt;
    
    SET result_k = @k_value;
    
    -- 6. 返回统计信息
    SELECT 
        CONCAT('更新完成 - 表: ', tablename) AS completion_message,
        CONCAT('k值: ', IFNULL(result_k, 0)) AS k_value_message,
        CONCAT('耗时: ', TIMESTAMPDIFF(SECOND, v_start_time, NOW()), ' 秒') AS duration;
        
END
            """
            
            self.cursor.execute(fixed_procedure)
            print("✅ 成功创建修复后的sp_updatecontroller")
            
            # 保存新的存储过程定义到文件
            with open('sp_updatecontroller_fixed.sql', 'w', encoding='utf-8') as f:
                f.write(fixed_procedure)
            print("📄 修复后定义已保存到 sp_updatecontroller_fixed.sql")
            
            return True
            
        except mysql.connector.Error as e:
            print(f"❌ 创建修复后存储过程失败: {e}")
            return False
    
    def test_fixed_controller(self, table_name):
        """测试修复后的控制器"""
        try:
            print(f"\n🧪 测试修复后的sp_updatecontroller - 表: {table_name}")
            
            # 调用存储过程
            args = [table_name, 0]  # 第二个参数是OUT参数的占位符
            result = self.cursor.callproc('sp_updatecontroller', args)
            
            # 获取结果集
            print("📊 sp_updatecontroller执行过程:")
            for result_set in self.cursor.stored_results():
                rows = result_set.fetchall()
                for row in rows:
                    print(f"   {row}")
            
            # 显示OUT参数结果
            k_value = result[1]  # OUT参数result_k的值
            print(f"📊 k值结果: {k_value}")
            
            return True
            
        except mysql.connector.Error as e:
            print(f"❌ 测试修复后控制器失败: {e}")
            return False
    
    def verify_fix(self, table_name):
        """验证修复结果"""
        try:
            print(f"\n🔍 验证修复结果 - 表: {table_name}")
            
            # 检查数据完整性
            self.cursor.execute(f"""
                SELECT 
                    COUNT(*) as total_rows,
                    COUNT(Full_Y) as full_y_count,
                    COUNT(controller) as controller_count,
                    COUNT(midprice) as midprice_count,
                    MAX(controller) as max_controller,
                    MAX(Full_Y) as max_full_y
                FROM {table_name}
            """)
            
            stats = self.cursor.fetchone()
            total_rows = stats[0]
            full_y_count = stats[1]
            controller_count = stats[2]
            midprice_count = stats[3]
            max_controller = stats[4] if stats[4] is not None else 0
            max_full_y = float(stats[5]) if stats[5] is not None else 0.0
            
            print(f"📊 修复后统计:")
            print(f"   • 总记录数: {total_rows}")
            print(f"   • Full_Y非空记录: {full_y_count}")
            print(f"   • controller非空记录: {controller_count}")
            print(f"   • midprice非空记录: {midprice_count}")
            print(f"   • 最大controller值: {max_controller}")
            print(f"   • 最大Full_Y值: {max_full_y:.6f}")
            
            # 检查覆盖率
            if total_rows > 0:
                full_y_coverage = (full_y_count / total_rows * 100)
                controller_coverage = (controller_count / total_rows * 100)
                midprice_coverage = (midprice_count / total_rows * 100)
                
                print(f"   • Full_Y覆盖率: {full_y_coverage:.1f}%")
                print(f"   • controller覆盖率: {controller_coverage:.1f}%")
                print(f"   • midprice覆盖率: {midprice_coverage:.1f}%")
                
                if full_y_coverage >= 99 and controller_coverage >= 99:
                    print("✅ 数据完整性验证通过")
                    return True
                else:
                    print("⚠️ 数据完整性需要改进")
                    return False
            else:
                print("❌ 表中没有数据")
                return False
            
        except mysql.connector.Error as e:
            print(f"❌ 验证修复结果失败: {e}")
            return False
    
    def test_multiple_tables(self):
        """测试多个表"""
        test_tables = [
            'stock_600887_ss',  # 伊利股份
            'stock_600036_ss',  # 招商银行
            'stock_2318_hk'     # 中国平安
        ]
        
        print(f"\n🧪 测试多个表的修复效果...")
        
        success_count = 0
        for table_name in test_tables:
            try:
                print(f"\n📊 测试表: {table_name}")
                
                # 检查表是否存在
                self.cursor.execute(f"""
                    SELECT COUNT(*) FROM information_schema.tables 
                    WHERE table_schema = 'finance' AND table_name = '{table_name}'
                """)
                
                if self.cursor.fetchone()[0] == 0:
                    print(f"⚠️ 表 {table_name} 不存在，跳过")
                    continue
                
                # 测试sp_updatecontroller
                if self.test_fixed_controller(table_name):
                    if self.verify_fix(table_name):
                        print(f"✅ {table_name} 测试通过")
                        success_count += 1
                    else:
                        print(f"⚠️ {table_name} 验证未完全通过")
                else:
                    print(f"❌ {table_name} 测试失败")
                    
            except Exception as e:
                print(f"❌ 测试 {table_name} 时出错: {e}")
        
        print(f"\n📊 测试总结: {success_count}/{len(test_tables)} 个表测试成功")
        return success_count == len(test_tables)
    
    def close_connection(self):
        """关闭数据库连接"""
        if self.cursor:
            self.cursor.close()
        if self.connection:
            self.connection.close()
        print("🔒 数据库连接已关闭")
    
    def run(self):
        """执行主流程"""
        print("🎯 修复sp_updatecontroller参数错误")
        print("=" * 60)
        print(f"📅 执行时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"🏠 数据库: {self.db_config['host']}/{self.db_config['database']}")
        print("\n💡 问题: sp_averagelineV3只需要1个参数，但代码传递了2个")
        print("🔧 解决: 修复sp_updatecontroller中的调用")
        
        try:
            # 1. 连接数据库
            if not self.connect_database():
                return False
            
            # 2. 备份当前存储过程
            if not self.backup_current_controller():
                return False
            
            # 3. 创建修复后的存储过程
            if not self.create_fixed_controller():
                return False
            
            # 4. 测试多个表
            if not self.test_multiple_tables():
                print("⚠️ 部分表测试未完全通过，但基本功能正常")
            
            print("\n🎉 参数错误修复完成!")
            print("💡 现在sp_updatecontroller正确调用sp_averagelineV3")
            print("📝 使用方法: CALL sp_updatecontroller('table_name', @k_value);")
            
            return True
            
        except Exception as e:
            print(f"❌ 执行失败: {e}")
            return False
        
        finally:
            self.close_connection()

def main():
    """主函数"""
    fixer = ParameterFixer()
    success = fixer.run()
    
    if success:
        print("\n✅ 修复完成!")
        print("📝 现在可以正常使用sp_updatecontroller了")
    else:
        print("\n❌ 修复失败!")

if __name__ == "__main__":
    main()
