#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查数据库更新状态
"""

import pymysql
from datetime import datetime, <PERSON><PERSON><PERSON>

def check_database_status():
    """检查数据库更新状态"""
    
    db_config = {
        'host': '************',
        'port': 3306,
        'database': 'finance',
        'user': 'root',
        'password': '',
        'charset': 'utf8mb4'
    }
    
    try:
        connection = pymysql.connect(**db_config)
        cursor = connection.cursor()
        
        print("📊 数据库更新状态检查")
        print("="*60)
        
        # 1. 检查HK00023表状态
        print("🔍 检查HK00023表:")
        print("-" * 30)
        
        cursor.execute("""
            SELECT 
                COUNT(*) as total_records,
                MIN(date) as earliest_date,
                MAX(date) as latest_date,
                COUNT(CASE WHEN y_probability IS NOT NULL THEN 1 END) as y_records,
                COUNT(CASE WHEN inflow_ratio IS NOT NULL THEN 1 END) as x_records
            FROM hk00023
        """)
        
        hk23_stats = cursor.fetchone()
        total, earliest, latest, y_count, x_count = hk23_stats
        
        print(f"   总记录数: {total}")
        print(f"   数据范围: {earliest} 到 {latest}")
        print(f"   Y值记录: {y_count} ({y_count/total*100:.1f}%)")
        print(f"   X值记录: {x_count} ({x_count/total*100:.1f}%)")
        
        # 检查最新数据
        today = datetime.now().date()
        recent_date = today - timedelta(days=7)
        
        cursor.execute("""
            SELECT COUNT(*) FROM hk00023 
            WHERE date >= %s
        """, (recent_date,))
        
        recent_count = cursor.fetchone()[0]
        print(f"   最近7天数据: {recent_count}条")
        
        # 2. 检查HK2800表状态
        print(f"\n🔍 检查HK2800表:")
        print("-" * 30)
        
        cursor.execute("SHOW TABLES LIKE 'hk2800'")
        hk2800_exists = cursor.fetchone()
        
        if hk2800_exists:
            cursor.execute("""
                SELECT 
                    COUNT(*) as total_records,
                    MIN(date) as earliest_date,
                    MAX(date) as latest_date,
                    COUNT(CASE WHEN y_probability IS NOT NULL THEN 1 END) as y_records,
                    COUNT(CASE WHEN inflow_ratio IS NOT NULL THEN 1 END) as x_records
                FROM hk2800
            """)
            
            hk2800_stats = cursor.fetchone()
            total_2800, earliest_2800, latest_2800, y_count_2800, x_count_2800 = hk2800_stats
            
            print(f"   总记录数: {total_2800}")
            print(f"   数据范围: {earliest_2800} 到 {latest_2800}")
            print(f"   Y值记录: {y_count_2800} ({y_count_2800/total_2800*100:.1f}%)")
            print(f"   X值记录: {x_count_2800} ({x_count_2800/total_2800*100:.1f}%)")
            
            cursor.execute("""
                SELECT COUNT(*) FROM hk2800 
                WHERE date >= %s
            """, (recent_date,))
            
            recent_count_2800 = cursor.fetchone()[0]
            print(f"   最近7天数据: {recent_count_2800}条")
        else:
            print("   ❌ HK2800表不存在")
        
        # 3. 检查数据完整性
        print(f"\n📊 数据完整性检查:")
        print("-" * 30)
        
        # 检查HK00023最新数据样本
        cursor.execute("""
            SELECT date, close, y_probability, inflow_ratio
            FROM hk00023 
            ORDER BY date DESC 
            LIMIT 5
        """)
        
        latest_data = cursor.fetchall()
        
        print("HK00023最新5天数据:")
        print("日期         价格    Y值    X值")
        print("-" * 35)
        
        for row in latest_data:
            date, close, y_val, x_val = row
            y_str = f"{float(y_val):.3f}" if y_val else "None"
            x_str = f"{float(x_val):.3f}" if x_val else "None"
            print(f"{date}  {float(close):>6.2f}  {y_str:>5}  {x_str:>5}")
        
        # 检查HK2800最新数据样本
        if hk2800_exists:
            cursor.execute("""
                SELECT date, close, y_probability, inflow_ratio
                FROM hk2800 
                ORDER BY date DESC 
                LIMIT 5
            """)
            
            latest_data_2800 = cursor.fetchall()
            
            print(f"\nHK2800最新5天数据:")
            print("日期         价格    Y值    X值")
            print("-" * 35)
            
            for row in latest_data_2800:
                date, close, y_val, x_val = row
                y_str = f"{float(y_val):.3f}" if y_val else "None"
                x_str = f"{float(x_val):.3f}" if x_val else "None"
                print(f"{date}  {float(close):>6.2f}  {y_str:>5}  {x_str:>5}")
        
        # 4. 更新建议
        print(f"\n💡 更新建议:")
        print("-" * 20)
        
        days_since_update = (today - latest).days if latest else 999
        
        if days_since_update <= 1:
            print("✅ 数据很新，无需更新")
        elif days_since_update <= 7:
            print("⚠️ 数据稍旧，建议更新")
        else:
            print("❌ 数据过旧，需要更新")
        
        if y_count / total < 0.9:
            print("⚠️ Y值数据不完整，需要重新计算")
        
        if x_count / total < 0.9:
            print("⚠️ X值数据不完整，需要重新计算")
        
        # 5. 策略可用性
        print(f"\n🎯 策略可用性:")
        print("-" * 20)
        
        if y_count > 1000 and x_count > 1000:
            print("✅ 数据充足，可以运行所有策略")
            print("✅ Y≥X≥0.4策略 - 可用")
            print("✅ Y>0.4且X>0.4策略 - 可用")
            print("✅ 网格+凯利策略 - 可用")
        else:
            print("❌ 数据不足，需要更新后才能运行策略")
        
        return {
            'hk00023_total': total,
            'hk00023_latest': latest,
            'hk2800_exists': bool(hk2800_exists),
            'hk2800_total': total_2800 if hk2800_exists else 0,
            'days_since_update': days_since_update,
            'data_complete': (y_count / total > 0.9) and (x_count / total > 0.9)
        }
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return None
    finally:
        if 'connection' in locals():
            connection.close()

if __name__ == "__main__":
    status = check_database_status()
