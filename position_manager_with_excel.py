#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
持仓管理器 - 包含持仓判断和Excel更新
功能：
1. 判断当前持仓状态
2. 根据信号决定交易动作
3. 更新Excel交易记录
4. 执行"尽量不持仓"策略
"""

import pandas as pd
import mysql.connector
from datetime import datetime, timedelta
import os
import shutil

class PositionManager:
    def __init__(self, excel_file="交易记录追踪0023HK.xlsx"):
        self.excel_file = excel_file
        self.backup_file = f"交易记录追踪0023HK_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"

        # 数据库配置
        self.db_config = {
            'host': 'localhost',
            'port': 3306,
            'user': 'root',
            'password': '12345678',
            'database': 'finance',
            'charset': 'utf8mb4'
        }

        # 交易参数
        self.initial_capital = 10000  # 初始资金
        self.position_size = 800      # 标准持仓数量
        self.max_holding_days = 3     # 最大持仓天数（尽量不持仓策略）

        # 复利计算参数
        self.compound_interest_enabled = True  # 启用复利计算
        self.position_ratio = 0.8             # 仓位比例（80%）
        self.transaction_cost_rate = 0.001     # 交易成本率（0.1%）

    def get_latest_market_data(self):
        """从数据库获取最新市场数据 - 包含两套XYE系统"""
        try:
            connection = mysql.connector.connect(**self.db_config)
            cursor = connection.cursor()

            cursor.execute("""
                SELECT Date, Close, Y_Value, X_Value, E_Value, MFI, RSI,
                       Full_Y, E, Controller, midprice
                FROM eab_0023hk
                ORDER BY Date DESC
                LIMIT 1
            """)

            result = cursor.fetchone()
            cursor.close()
            connection.close()

            if result:
                date, close, y_value, x_value, e_value, mfi, rsi, full_y, e, controller, midprice = result
                return {
                    'date': date,
                    'close': float(close),
                    # 第一套XYE系统
                    'y_value': float(y_value),
                    'x_value': float(x_value),
                    'e_value': float(e_value),
                    # 第二套XYE系统
                    'full_y': float(full_y) if full_y else 0.5,
                    'e': float(e) if e else 0,
                    'controller': int(controller) if controller else 0,
                    'midprice': float(midprice) if midprice else float(close),
                    # 基础指标
                    'mfi': float(mfi),
                    'rsi': float(rsi)
                }
            return None

        except Exception as e:
            print(f"❌ 获取市场数据失败: {e}")
            return None

    def get_current_position_from_excel(self):
        """从Excel获取当前持仓状态"""
        try:
            if not os.path.exists(self.excel_file):
                print(f"⚠️ Excel文件不存在: {self.excel_file}")
                return self.create_empty_position()

            df = pd.read_excel(self.excel_file)
            if len(df) == 0:
                return self.create_empty_position()

            # 获取最新记录
            latest = df.iloc[-1]

            # 解析持仓状态
            position_status = {
                'date': latest.get('交易日期'),
                'position_type': latest.get('交易类型', ''),
                'position_direction': latest.get('交易方向', ''),
                'entry_price': float(latest.get('持仓成本', 0)),
                'current_price': float(latest.get('交易价格', 0)),
                'position_quantity': int(latest.get('持仓数量', 0)),
                'current_capital': float(latest.get('账户余额', self.initial_capital)),
                'total_assets': float(latest.get('总资产', self.initial_capital)),
                'unrealized_pnl': float(latest.get('浮动盈亏', 0)),
                'realized_pnl': float(latest.get('实现盈亏', 0)),
                'cumulative_return': float(latest.get('累计收益率', 0))
            }

            # 判断是否有持仓
            if position_status['position_quantity'] > 0 and position_status['entry_price'] > 0:
                position_status['has_position'] = True
                position_status['holding_days'] = self.calculate_holding_days(df, latest.get('交易日期'))
            else:
                position_status['has_position'] = False
                position_status['holding_days'] = 0

            return position_status

        except Exception as e:
            print(f"❌ 读取Excel持仓状态失败: {e}")
            return self.create_empty_position()

    def create_empty_position(self):
        """创建空仓状态"""
        return {
            'date': datetime.now().strftime('%Y-%m-%d'),
            'position_type': '',
            'position_direction': '',
            'entry_price': 0,
            'current_price': 0,
            'position_quantity': 0,
            'current_capital': self.initial_capital,
            'total_assets': self.initial_capital,
            'unrealized_pnl': 0,
            'realized_pnl': 0,
            'cumulative_return': 0,
            'has_position': False,
            'holding_days': 0
        }

    def calculate_holding_days(self, df, current_date):
        """计算持仓天数"""
        try:
            # 从最新记录向前查找开仓记录
            for i in range(len(df)-1, -1, -1):
                if df.iloc[i].get('交易类型') == '开仓':
                    entry_date = pd.to_datetime(df.iloc[i].get('交易日期'))
                    current_date = pd.to_datetime(current_date)
                    return (current_date - entry_date).days + 1
            return 1
        except:
            return 1

    def generate_trading_signal(self, market_data):
        """生成交易信号 - 综合两套XYE系统"""
        # 第一套系统
        y1 = market_data['y_value']
        x1 = market_data['x_value']
        e1 = market_data['e_value']

        # 第二套系统
        y2 = market_data['full_y']  # Full_Y (K值)
        x2 = market_data['mfi'] / 100  # MFI/100
        e2 = market_data['e']  # 第二套E值
        controller = market_data['controller']

        # 价格偏离分析
        close = market_data['close']
        midprice = market_data['midprice']
        price_deviation = (close - midprice) / midprice if midprice > 0 else 0

        # 第一套系统信号
        if 0.333 < y1 < 0.4:
            signal1 = '观望'
            strength1 = 1
        elif e1 > 0.1:
            signal1 = '强烈买入'
            strength1 = 5
        elif e1 < -0.1:
            signal1 = '强烈卖出'
            strength1 = 5
        elif e1 > 0:
            signal1 = '买入'
            strength1 = 3
        elif e1 < 0:
            signal1 = '卖出'
            strength1 = 3
        else:
            signal1 = '观望'
            strength1 = 1

        # 第二套系统信号 (基于Controller和价格偏离)
        if controller == 1 and price_deviation < 0.05:  # 买入信号且价格不太高
            signal2 = '买入'
            strength2 = 4
        elif controller == -1 and price_deviation > -0.05:  # 卖出信号且价格不太低
            signal2 = '卖出'
            strength2 = 4
        elif abs(price_deviation) > 0.1:  # 价格偏离过大
            if price_deviation > 0.1:
                signal2 = '卖出'  # 价格过高
                strength2 = 3
            else:
                signal2 = '买入'  # 价格过低
                strength2 = 3
        else:
            signal2 = '观望'
            strength2 = 1

        # 综合两套系统
        if signal1 == signal2 and signal1 != '观望':
            # 两套系统一致
            final_signal = signal1
            final_strength = min(strength1 + strength2, 5)
        elif '强烈' in signal1 or '强烈' in signal2:
            # 有强烈信号优先
            if '强烈' in signal1:
                final_signal = signal1
                final_strength = strength1
            else:
                final_signal = signal2
                final_strength = strength2
        elif signal1 != '观望' and signal2 == '观望':
            # 第一套有信号，第二套观望
            final_signal = signal1
            final_strength = max(strength1 - 1, 1)
        elif signal1 == '观望' and signal2 != '观望':
            # 第一套观望，第二套有信号
            final_signal = signal2
            final_strength = max(strength2 - 1, 1)
        else:
            # 默认观望
            final_signal = '观望'
            final_strength = 1

        return {
            'signal': final_signal,
            'strength': final_strength,
            'signal1': signal1,
            'signal2': signal2,
            'controller': controller,
            'price_deviation': price_deviation,
            'reason': f'系统1: Y1={y1:.4f}, E1={e1:.4f} -> {signal1}; 系统2: Y2={y2:.4f}, E2={e2:.4f}, Controller={controller} -> {signal2}; 偏离={price_deviation:+.2%}'
        }

    def decide_trading_action(self, position_status, market_data, signal):
        """决定交易动作 - 核心持仓判断逻辑"""
        print("🤔 持仓决策分析:")
        print(f"   当前持仓: {'是' if position_status['has_position'] else '否'}")
        print(f"   持仓天数: {position_status['holding_days']}天")
        print(f"   当前信号: {signal['signal']}")
        print(f"   信号强度: {signal['strength']}/5")

        # 1. 如果当前有持仓
        if position_status['has_position']:
            # 1.1 检查持仓时间 - 尽量不持仓策略
            if position_status['holding_days'] >= self.max_holding_days:
                return {
                    'action': '强制平仓',
                    'reason': f'持仓{position_status["holding_days"]}天超过最大持仓期限{self.max_holding_days}天',
                    'priority': 'HIGH'
                }

            # 1.2 检查盈亏情况
            pnl_ratio = position_status['unrealized_pnl'] / (position_status['entry_price'] * position_status['position_quantity']) * 100

            # 止盈条件
            if pnl_ratio >= 2.0:
                return {
                    'action': '止盈平仓',
                    'reason': f'盈利{pnl_ratio:.2f}%达到止盈条件',
                    'priority': 'HIGH'
                }

            # 止损条件
            if pnl_ratio <= -1.5:
                return {
                    'action': '止损平仓',
                    'reason': f'亏损{pnl_ratio:.2f}%达到止损条件',
                    'priority': 'HIGH'
                }

            # 1.3 检查信号变化
            current_direction = position_status['position_direction']
            if ((current_direction == '多头' and signal['signal'] in ['卖出', '强烈卖出']) or
                (current_direction == '空头' and signal['signal'] in ['买入', '强烈买入'])):
                return {
                    'action': '信号平仓',
                    'reason': f'持仓方向{current_direction}与信号{signal["signal"]}相反',
                    'priority': 'MEDIUM'
                }

            # 1.4 继续持有
            return {
                'action': '继续持有',
                'reason': f'持仓{position_status["holding_days"]}天，盈亏{pnl_ratio:.2f}%，信号{signal["signal"]}',
                'priority': 'LOW'
            }

        # 2. 如果当前空仓
        else:
            # 2.1 尽量不持仓策略 - 只在强信号时开仓
            if signal['signal'] == '强烈买入' and signal['strength'] >= 4:
                return {
                    'action': '开多仓',
                    'reason': f'空仓状态，{signal["signal"]}信号强度{signal["strength"]}/5',
                    'priority': 'MEDIUM'
                }
            elif signal['signal'] == '强烈卖出' and signal['strength'] >= 4:
                return {
                    'action': '开空仓',
                    'reason': f'空仓状态，{signal["signal"]}信号强度{signal["strength"]}/5',
                    'priority': 'MEDIUM'
                }
            else:
                return {
                    'action': '保持空仓',
                    'reason': f'信号{signal["signal"]}强度{signal["strength"]}/5不足，执行不持仓策略',
                    'priority': 'LOW'
                }

    def execute_trading_action(self, action_decision, position_status, market_data):
        """执行交易动作并更新Excel"""
        action = action_decision['action']
        current_price = market_data['close']
        current_date = market_data['date'].strftime('%Y-%m-%d')

        print(f"\n📋 执行交易动作: {action}")
        print(f"   原因: {action_decision['reason']}")
        print(f"   优先级: {action_decision['priority']}")

        # 创建交易记录
        if action in ['开多仓', '开空仓']:
            # 开仓
            trade_record = self.create_open_position_record(
                action, current_price, current_date, position_status, market_data
            )
        elif action in ['强制平仓', '止盈平仓', '止损平仓', '信号平仓']:
            # 平仓
            trade_record = self.create_close_position_record(
                action, current_price, current_date, position_status, market_data
            )
        elif action == '继续持有':
            # 更新持仓
            trade_record = self.create_update_position_record(
                current_price, current_date, position_status, market_data
            )
        else:  # 保持空仓
            # 空仓记录
            trade_record = self.create_empty_position_record(
                current_price, current_date, position_status, market_data
            )

        # 更新Excel
        success = self.update_excel_file(trade_record)

        if success:
            print(f"✅ Excel更新成功")
            print(f"   交易类型: {trade_record['交易类型']}")
            print(f"   交易方向: {trade_record['交易方向']}")
            print(f"   持仓数量: {trade_record['持仓数量']}")
            print(f"   账户余额: {trade_record['账户余额']:,.2f}")
        else:
            print(f"❌ Excel更新失败")

        return success

    def calculate_compound_position_size(self, current_capital, current_price):
        """计算复利仓位大小"""
        if not self.compound_interest_enabled:
            return self.position_size

        # 复利计算：基于当前总资本的固定比例
        available_capital = current_capital * self.position_ratio
        compound_position_size = int(available_capital / current_price)

        # 确保至少有最小仓位
        min_position = 100  # 最小100股
        compound_position_size = max(compound_position_size, min_position)

        return compound_position_size

    def create_open_position_record(self, action, current_price, current_date, position_status, market_data):
        """创建开仓记录 - 支持复利计算"""
        direction = '多头' if action == '开多仓' else '空头'

        # 复利计算仓位大小
        if self.compound_interest_enabled:
            actual_position_size = self.calculate_compound_position_size(
                position_status['current_capital'], current_price
            )
        else:
            actual_position_size = self.position_size

        trade_amount = actual_position_size * current_price
        commission = trade_amount * self.transaction_cost_rate

        new_capital = position_status['current_capital'] - trade_amount - commission
        unrealized_pnl = 0  # 开仓时浮动盈亏为0

        return {
            '交易日期': current_date,
            '交易类型': '开仓',
            '交易方向': direction,
            '交易价格': current_price,
            '持仓数量': actual_position_size,
            '交易金额': trade_amount,
            '手续费': commission,
            '净交易额': trade_amount + commission,
            '持仓成本': current_price,
            '当前市值': trade_amount,
            '浮动盈亏': unrealized_pnl,
            '实现盈亏': position_status['realized_pnl'],
            '累计盈亏': position_status['realized_pnl'] + unrealized_pnl,
            '账户余额': new_capital,
            '总资产': new_capital + trade_amount,
            '收益率': 0,
            '累计收益率': position_status['cumulative_return'],
            'Y值': market_data['y_value'],
            'X值': market_data['x_value'],
            'E值': market_data['e_value'],
            '复利仓位': f'{actual_position_size}股 (复利计算)' if self.compound_interest_enabled else f'{actual_position_size}股 (固定仓位)',
            '备注': f'开{direction}仓'
        }

    def create_close_position_record(self, action, current_price, current_date, position_status, market_data):
        """创建平仓记录 - 支持复利计算"""
        trade_amount = position_status['position_quantity'] * current_price
        commission = trade_amount * self.transaction_cost_rate

        # 计算实现盈亏
        if position_status['position_direction'] == '多头':
            realized_pnl = (current_price - position_status['entry_price']) * position_status['position_quantity']
        else:
            realized_pnl = (position_status['entry_price'] - current_price) * position_status['position_quantity']

        realized_pnl -= commission  # 扣除手续费

        # 复利关键：盈亏直接加入总资本
        new_capital = position_status['current_capital'] + trade_amount - commission
        new_realized_pnl = position_status['realized_pnl'] + realized_pnl

        # 复利收益率计算
        if self.compound_interest_enabled:
            # 真复利：基于初始资金的累积增长率
            compound_growth_rate = (new_capital / self.initial_capital - 1) * 100
            trade_return_rate = realized_pnl / (position_status['entry_price'] * position_status['position_quantity']) * 100
        else:
            compound_growth_rate = (new_capital - self.initial_capital) / self.initial_capital * 100
            trade_return_rate = realized_pnl / (position_status['entry_price'] * position_status['position_quantity']) * 100

        return {
            '交易日期': current_date,
            '交易类型': '平仓',
            '交易方向': position_status['position_direction'],
            '交易价格': current_price,
            '持仓数量': 0,  # 平仓后持仓为0
            '交易金额': trade_amount,
            '手续费': commission,
            '净交易额': trade_amount - commission,
            '持仓成本': 0,  # 平仓后成本为0
            '当前市值': 0,  # 平仓后市值为0
            '浮动盈亏': 0,  # 平仓后浮动盈亏为0
            '实现盈亏': new_realized_pnl,
            '累计盈亏': new_realized_pnl,
            '账户余额': new_capital,
            '总资产': new_capital,
            '收益率': trade_return_rate,
            '累计收益率': compound_growth_rate,
            '复利增长': f'{compound_growth_rate:.2f}%' if self.compound_interest_enabled else f'{compound_growth_rate:.2f}%',
            'Y值': market_data['y_value'],
            'X值': market_data['x_value'],
            'E值': market_data['e_value'],
            '备注': f'{action} (复利)' if self.compound_interest_enabled else action
        }

    def create_update_position_record(self, current_price, current_date, position_status, market_data):
        """创建持仓更新记录"""
        # 计算当前浮动盈亏
        if position_status['position_direction'] == '多头':
            unrealized_pnl = (current_price - position_status['entry_price']) * position_status['position_quantity']
        else:
            unrealized_pnl = (position_status['entry_price'] - current_price) * position_status['position_quantity']

        current_market_value = position_status['position_quantity'] * current_price
        total_assets = position_status['current_capital'] + current_market_value
        daily_return = (current_price - position_status['current_price']) / position_status['current_price'] * 100

        return {
            '交易日期': current_date,
            '交易类型': '持仓',
            '交易方向': position_status['position_direction'],
            '交易价格': current_price,
            '持仓数量': position_status['position_quantity'],
            '交易金额': 0,  # 持仓更新无交易金额
            '手续费': 0,
            '净交易额': 0,
            '持仓成本': position_status['entry_price'],
            '当前市值': current_market_value,
            '浮动盈亏': unrealized_pnl,
            '实现盈亏': position_status['realized_pnl'],
            '累计盈亏': position_status['realized_pnl'] + unrealized_pnl,
            '账户余额': position_status['current_capital'],
            '总资产': total_assets,
            '收益率': daily_return,
            '累计收益率': (total_assets - self.initial_capital) / self.initial_capital * 100,
            'Y值': market_data['y_value'],
            'X值': market_data['x_value'],
            'E值': market_data['e_value'],
            '备注': f'持仓第{position_status["holding_days"]}天'
        }

    def create_empty_position_record(self, current_price, current_date, position_status, market_data):
        """创建空仓记录"""
        return {
            '交易日期': current_date,
            '交易类型': '空仓',
            '交易方向': '无',
            '交易价格': current_price,
            '持仓数量': 0,
            '交易金额': 0,
            '手续费': 0,
            '净交易额': 0,
            '持仓成本': 0,
            '当前市值': 0,
            '浮动盈亏': 0,
            '实现盈亏': position_status['realized_pnl'],
            '累计盈亏': position_status['realized_pnl'],
            '账户余额': position_status['current_capital'],
            '总资产': position_status['current_capital'],
            '收益率': 0,
            '累计收益率': position_status['cumulative_return'],
            'Y值': market_data['y_value'],
            'X值': market_data['x_value'],
            'E值': market_data['e_value'],
            '备注': '空仓观望'
        }

    def update_excel_file(self, new_record):
        """更新Excel文件"""
        try:
            # 备份原文件
            if os.path.exists(self.excel_file):
                shutil.copy2(self.excel_file, self.backup_file)
                print(f"📦 创建备份: {self.backup_file}")

            # 读取现有数据
            if os.path.exists(self.excel_file):
                df = pd.read_excel(self.excel_file)
            else:
                df = pd.DataFrame()

            # 添加新记录
            new_df = pd.DataFrame([new_record])
            df = pd.concat([df, new_df], ignore_index=True)

            # 保存到Excel
            df.to_excel(self.excel_file, index=False)

            return True

        except Exception as e:
            print(f"❌ 更新Excel失败: {e}")
            return False

    def run_daily_position_management(self):
        """运行每日持仓管理"""
        print("🎯 每日持仓管理系统")
        print("=" * 50)
        print(f"🕐 执行时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

        # 1. 获取市场数据
        print("\n1️⃣ 获取最新市场数据...")
        market_data = self.get_latest_market_data()
        if not market_data:
            print("❌ 无法获取市场数据")
            return False

        print(f"✅ 市场数据获取成功:")
        print(f"   日期: {market_data['date']}")
        print(f"   收盘价: {market_data['close']:.2f} 港元")
        print(f"   XYE: Y={market_data['y_value']:.4f}, X={market_data['x_value']:.4f}, E={market_data['e_value']:.4f}")

        # 2. 获取当前持仓状态
        print("\n2️⃣ 分析当前持仓状态...")
        position_status = self.get_current_position_from_excel()

        print(f"✅ 持仓状态分析完成:")
        print(f"   持仓状态: {'有持仓' if position_status['has_position'] else '空仓'}")
        if position_status['has_position']:
            print(f"   持仓方向: {position_status['position_direction']}")
            print(f"   持仓天数: {position_status['holding_days']}天")
            print(f"   持仓数量: {position_status['position_quantity']}股")
            print(f"   持仓成本: {position_status['entry_price']:.2f}")
        print(f"   账户余额: {position_status['current_capital']:,.2f}")

        # 3. 生成交易信号
        print("\n3️⃣ 生成交易信号...")
        signal = self.generate_trading_signal(market_data)

        print(f"✅ 交易信号生成:")
        print(f"   信号: {signal['signal']}")
        print(f"   强度: {signal['strength']}/5")
        print(f"   依据: {signal['reason']}")

        # 4. 决定交易动作
        print("\n4️⃣ 持仓决策分析...")
        action_decision = self.decide_trading_action(position_status, market_data, signal)

        print(f"✅ 决策完成:")
        print(f"   动作: {action_decision['action']}")
        print(f"   原因: {action_decision['reason']}")
        print(f"   优先级: {action_decision['priority']}")

        # 5. 执行交易动作
        print("\n5️⃣ 执行交易并更新Excel...")
        success = self.execute_trading_action(action_decision, position_status, market_data)

        # 6. 总结
        print("\n" + "=" * 50)
        print("📊 每日持仓管理总结")
        print("=" * 50)
        print(f"🕐 完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"📈 市场信号: {signal['signal']}")
        print(f"💼 执行动作: {action_decision['action']}")
        print(f"📄 Excel更新: {'成功' if success else '失败'}")
        print(f"🎯 策略执行: {'✅ 符合尽量不持仓策略' if action_decision['action'] in ['保持空仓', '强制平仓', '止盈平仓', '止损平仓'] else '⚠️ 开仓操作'}")

        # 显示复利统计
        if success and self.compound_interest_enabled:
            # 获取最新记录进行统计
            latest_position = self.get_current_position_from_excel()
            self.display_compound_statistics({'总资产': latest_position['total_assets']})

        return success

    def display_compound_statistics(self, latest_record):
        """显示复利统计信息"""
        try:
            # 读取Excel文件获取历史数据
            if os.path.exists(self.excel_file):
                df = pd.read_excel(self.excel_file)

                if len(df) > 1:
                    # 计算复利统计
                    initial_capital = self.initial_capital
                    current_capital = latest_record['总资产']

                    # 计算年化复利收益率（假设数据跨度）
                    days_elapsed = len(df)  # 简化计算
                    if days_elapsed > 0:
                        years_elapsed = days_elapsed / 365.25
                        if years_elapsed > 0:
                            annual_compound_rate = (current_capital / initial_capital) ** (1/years_elapsed) - 1
                        else:
                            annual_compound_rate = 0
                    else:
                        annual_compound_rate = 0

                    # 统计交易次数
                    trades_df = df[df['交易类型'].isin(['开仓', '平仓'])]
                    total_trades = len(trades_df)
                    winning_trades = len(trades_df[trades_df['收益率'] > 0])
                    win_rate = winning_trades / total_trades * 100 if total_trades > 0 else 0

                    print(f"\n📊 复利统计报告:")
                    print(f"   💰 初始资金: {initial_capital:,.2f}")
                    print(f"   💰 当前资金: {current_capital:,.2f}")
                    print(f"   📈 总收益率: {(current_capital/initial_capital-1)*100:+.2f}%")
                    print(f"   📅 交易天数: {days_elapsed}天")
                    print(f"   🔄 年化复利: {annual_compound_rate*100:.2f}%")
                    print(f"   🎯 交易次数: {total_trades}次")
                    print(f"   🏆 胜率: {win_rate:.1f}%")
                    print(f"   💡 复利效应: {'启用' if self.compound_interest_enabled else '禁用'}")

        except Exception as e:
            print(f"⚠️ 复利统计计算失败: {e}")

def main():
    """主函数"""
    manager = PositionManager()
    success = manager.run_daily_position_management()
    return 0 if success else 1

if __name__ == "__main__":
    import sys
    sys.exit(main())
