#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MT5交易监控启动器
=================

这是一个简化的MT5交易监控系统启动器，用于：
1. 监控MT5交易
2. 连接数据库记录交易数据
3. 实时分析博弈论指标
4. 风险管理和报警

作者: 博弈论投资策略团队
日期: 2025年7月
"""

import os
import sys
import json
import time
import threading
from datetime import datetime
import MetaTrader5 as mt5
import pymysql
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('mt5_trading.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class MT5TradingMonitor:
    def __init__(self, config_file='system_config.json'):
        """初始化MT5交易监控器"""
        self.config = self.load_config(config_file)
        self.running = False
        self.db_connection = None
        self.mt5_connected = False
        
    def load_config(self, config_file):
        """加载配置文件"""
        default_config = {
            "database": {
                "host": "localhost",
                "database": "game_theory_trading",
                "user": "root",
                "password": "12345678",
                "charset": "utf8mb4",
                "port": 3306
            },
            "trading": {
                "symbol": "HSI50",
                "magic_number": 20250713,
                "monitor_interval": 30,  # 30秒检查一次
                "max_positions": 1
            },
            "strategy": {
                "y_buy_threshold": 0.55,
                "x_buy_threshold": 0.5,
                "y_sell_threshold": 0.45,
                "x_sell_threshold": 0.4
            }
        }
        
        if os.path.exists(config_file):
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    user_config = json.load(f)
                    default_config.update(user_config)
            except Exception as e:
                logger.error(f"❌ 加载配置文件失败: {e}")
        
        return default_config
    
    def connect_mt5(self):
        """连接MT5"""
        try:
            if not mt5.initialize():
                logger.error("❌ MT5初始化失败")
                return False
            
            # 获取账户信息
            account_info = mt5.account_info()
            if account_info is None:
                logger.error("❌ 无法获取账户信息")
                return False
            
            logger.info(f"✅ MT5连接成功")
            logger.info(f"📊 账户: {account_info.login}")
            logger.info(f"💰 余额: {account_info.balance}")
            logger.info(f"💎 净值: {account_info.equity}")
            
            self.mt5_connected = True
            return True
            
        except Exception as e:
            logger.error(f"❌ MT5连接失败: {e}")
            return False
    
    def connect_database(self):
        """连接数据库"""
        try:
            self.db_connection = pymysql.connect(**self.config['database'])
            logger.info("✅ 数据库连接成功")
            return True
        except Exception as e:
            logger.error(f"❌ 数据库连接失败: {e}")
            return False
    
    def get_current_positions(self):
        """获取当前持仓"""
        try:
            positions = mt5.positions_get(symbol=self.config['trading']['symbol'])
            return positions if positions else []
        except Exception as e:
            logger.error(f"❌ 获取持仓失败: {e}")
            return []
    
    def get_current_orders(self):
        """获取当前挂单"""
        try:
            orders = mt5.orders_get(symbol=self.config['trading']['symbol'])
            return orders if orders else []
        except Exception as e:
            logger.error(f"❌ 获取挂单失败: {e}")
            return []
    
    def get_latest_trades(self, limit=10):
        """获取最新交易记录"""
        try:
            # 获取最近的交易历史
            from_date = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
            deals = mt5.history_deals_get(from_date, datetime.now())
            
            if deals:
                # 过滤指定品种和魔术号
                filtered_deals = [
                    deal for deal in deals 
                    if deal.symbol == self.config['trading']['symbol'] 
                    and deal.magic == self.config['trading']['magic_number']
                ]
                return filtered_deals[-limit:] if filtered_deals else []
            return []
        except Exception as e:
            logger.error(f"❌ 获取交易记录失败: {e}")
            return []
    
    def save_trade_to_database(self, trade_data):
        """保存交易数据到数据库"""
        try:
            if not self.db_connection:
                return False
            
            cursor = self.db_connection.cursor()
            
            # 插入交易记录
            sql = """
                INSERT INTO trades (
                    ticket, symbol, trade_type, action, volume, 
                    open_price, close_price, profit, commission, swap,
                    open_time, close_time, magic_number, comment
                ) VALUES (
                    %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s
                )
                ON DUPLICATE KEY UPDATE
                    close_price = VALUES(close_price),
                    profit = VALUES(profit),
                    close_time = VALUES(close_time)
            """
            
            cursor.execute(sql, trade_data)
            self.db_connection.commit()
            return True
            
        except Exception as e:
            logger.error(f"❌ 保存交易数据失败: {e}")
            return False
    
    def save_account_status(self):
        """保存账户状态"""
        try:
            if not self.mt5_connected or not self.db_connection:
                return False
            
            account_info = mt5.account_info()
            if not account_info:
                return False
            
            cursor = self.db_connection.cursor()
            
            sql = """
                INSERT INTO account_status (
                    datetime, balance, equity, margin, free_margin, 
                    margin_level, profit, positions_count, orders_count
                ) VALUES (
                    %s, %s, %s, %s, %s, %s, %s, %s, %s
                )
            """
            
            positions_count = len(self.get_current_positions())
            orders_count = len(self.get_current_orders())
            
            data = (
                datetime.now(),
                account_info.balance,
                account_info.equity,
                account_info.margin,
                account_info.margin_free,
                account_info.margin_level,
                account_info.profit,
                positions_count,
                orders_count
            )
            
            cursor.execute(sql, data)
            self.db_connection.commit()
            return True
            
        except Exception as e:
            logger.error(f"❌ 保存账户状态失败: {e}")
            return False
    
    def monitor_trades(self):
        """监控交易"""
        logger.info("🔍 开始监控交易...")
        
        while self.running:
            try:
                # 获取当前持仓
                positions = self.get_current_positions()
                
                # 获取最新交易
                recent_trades = self.get_latest_trades(5)
                
                # 保存账户状态
                self.save_account_status()
                
                # 显示状态信息
                account_info = mt5.account_info()
                if account_info:
                    logger.info(f"📊 余额: {account_info.balance:.2f}, 净值: {account_info.equity:.2f}, 持仓: {len(positions)}")
                
                # 处理新交易
                for trade in recent_trades:
                    # 这里可以添加交易数据处理逻辑
                    pass
                
                # 等待下次检查
                time.sleep(self.config['trading']['monitor_interval'])
                
            except Exception as e:
                logger.error(f"❌ 监控过程出错: {e}")
                time.sleep(10)
    
    def start_monitoring(self):
        """启动监控"""
        logger.info("🚀 启动MT5交易监控系统...")
        
        # 连接MT5
        if not self.connect_mt5():
            logger.error("❌ MT5连接失败，无法启动监控")
            return False
        
        # 连接数据库
        if not self.connect_database():
            logger.error("❌ 数据库连接失败，无法启动监控")
            return False
        
        # 设置运行标志
        self.running = True
        
        # 启动监控线程
        monitor_thread = threading.Thread(target=self.monitor_trades)
        monitor_thread.daemon = True
        monitor_thread.start()
        
        logger.info("✅ MT5交易监控系统启动成功")
        
        try:
            while self.running:
                time.sleep(1)
        except KeyboardInterrupt:
            logger.info("🛑 接收到停止信号")
            self.stop_monitoring()
        
        return True
    
    def stop_monitoring(self):
        """停止监控"""
        logger.info("🛑 停止MT5交易监控...")
        
        self.running = False
        
        # 关闭MT5连接
        if self.mt5_connected:
            mt5.shutdown()
            logger.info("✅ MT5连接已关闭")
        
        # 关闭数据库连接
        if self.db_connection:
            self.db_connection.close()
            logger.info("✅ 数据库连接已关闭")
        
        logger.info("✅ 监控系统已停止")

def print_menu():
    """打印菜单"""
    print("\n" + "="*60)
    print("🎯 MT5交易监控系统")
    print("="*60)
    print("📅 当前时间:", datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
    print("-"*60)
    print("1. 🚀 启动交易监控")
    print("2. 🔍 检查MT5连接")
    print("3. 🗄️ 检查数据库连接")
    print("4. 📊 查看系统状态")
    print("5. ⚙️ 查看配置信息")
    print("6. 🚪 退出系统")
    print("-"*60)

def main():
    """主函数"""
    monitor = MT5TradingMonitor()
    
    while True:
        print_menu()
        
        try:
            choice = input("请选择功能 (1-6): ").strip()
            
            if choice == "1":
                print("\n🚀 启动交易监控...")
                monitor.start_monitoring()
                
            elif choice == "2":
                print("\n🔍 检查MT5连接...")
                if monitor.connect_mt5():
                    print("✅ MT5连接正常")
                    mt5.shutdown()
                else:
                    print("❌ MT5连接失败")
                    
            elif choice == "3":
                print("\n🗄️ 检查数据库连接...")
                if monitor.connect_database():
                    print("✅ 数据库连接正常")
                    monitor.db_connection.close()
                else:
                    print("❌ 数据库连接失败")
                    
            elif choice == "4":
                print("\n📊 系统状态:")
                print(f"MT5连接: {'✅' if monitor.mt5_connected else '❌'}")
                print(f"数据库连接: {'✅' if monitor.db_connection else '❌'}")
                print(f"监控运行: {'✅' if monitor.running else '❌'}")
                
            elif choice == "5":
                print("\n⚙️ 配置信息:")
                print(f"数据库主机: {monitor.config['database']['host']}")
                print(f"交易品种: {monitor.config['trading']['symbol']}")
                print(f"魔术号: {monitor.config['trading']['magic_number']}")
                print(f"监控间隔: {monitor.config['trading']['monitor_interval']}秒")
                
            elif choice == "6":
                print("\n👋 感谢使用MT5交易监控系统!")
                break
                
            else:
                print("\n❌ 无效选择，请输入1-6之间的数字")
            
            if choice != "1":
                input("\n按回车键继续...")
                
        except KeyboardInterrupt:
            print("\n\n👋 用户中断，退出系统")
            break
        except Exception as e:
            print(f"\n❌ 系统错误: {str(e)}")
            input("按回车键继续...")

if __name__ == "__main__":
    main()
