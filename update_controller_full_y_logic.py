#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
更新controller和Full_Y的计算逻辑
===============================
新逻辑：
- controller: 累积计数器，当(close-midprice>0)时controller++
- Full_Y: controller/行号，即累积强势次数除以当前行号
"""

import mysql.connector
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

class UpdatedControllerLogic:
    def __init__(self):
        """初始化数据库连接"""
        self.db_config = {
            'host': 'localhost',
            'database': 'finance',
            'user': 'root',
            'password': '12345678',
            'charset': 'utf8mb4',
            'autocommit': True
        }
        self.connection = None
        self.cursor = None
        
    def connect_database(self):
        """连接数据库"""
        try:
            print("🔗 连接MySQL数据库...")
            self.connection = mysql.connector.connect(**self.db_config)
            self.cursor = self.connection.cursor()
            print(f"✅ 成功连接到数据库: {self.db_config['host']}/{self.db_config['database']}")
            return True
        except mysql.connector.Error as e:
            print(f"❌ 数据库连接失败: {e}")
            return False
    
    def backup_current_controller(self):
        """备份当前的sp_updatecontroller"""
        try:
            print("🔄 备份当前sp_updatecontroller...")
            
            # 检查存储过程是否存在
            self.cursor.execute("""
                SELECT COUNT(*) FROM information_schema.ROUTINES 
                WHERE ROUTINE_SCHEMA = DATABASE() 
                AND ROUTINE_NAME = 'sp_updatecontroller'
            """)
            
            exists = self.cursor.fetchone()[0]
            
            if exists > 0:
                # 获取当前存储过程定义
                self.cursor.execute("SHOW CREATE PROCEDURE sp_updatecontroller")
                result = self.cursor.fetchone()
                
                if result:
                    # 创建备份
                    backup_name = f"sp_updatecontroller_backup_new_logic_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
                    backup_sql = result[2].replace(
                        'CREATE DEFINER=`root`@`%` PROCEDURE `sp_updatecontroller`',
                        f'CREATE PROCEDURE `{backup_name}`'
                    ).replace(
                        'CREATE PROCEDURE `sp_updatecontroller`',
                        f'CREATE PROCEDURE `{backup_name}`'
                    )
                    
                    self.cursor.execute(backup_sql)
                    print(f"✅ 已备份为: {backup_name}")
                    
                    return True
                else:
                    print("❌ 无法获取当前存储过程定义")
                    return False
            else:
                print("⚠️ sp_updatecontroller不存在，将直接创建新的")
                return True
                
        except mysql.connector.Error as e:
            print(f"❌ 备份检查失败: {e}")
            return False
    
    def create_new_controller_logic(self):
        """创建新的controller和Full_Y计算逻辑"""
        try:
            print("🔧 创建新的controller和Full_Y计算逻辑...")
            
            # 删除现有的存储过程
            self.cursor.execute("DROP PROCEDURE IF EXISTS sp_updatecontroller")
            
            # 创建新的存储过程
            new_procedure = """
CREATE PROCEDURE sp_updatecontroller(
    IN tablename VARCHAR(64),
    OUT result_k DECIMAL(20,6)
)
BEGIN
    DECLARE col_exists INT DEFAULT 0;
    DECLARE v_start_time DATETIME DEFAULT NOW();
    
    -- 1. 首先更新midprice (调用sp_averagelineV3)
    SELECT CONCAT('开始更新midprice - 表: ', tablename, ' 时间: ', v_start_time) AS start_message;
    CALL sp_averagelineV3(tablename);
    SELECT 'midprice更新完成' AS midprice_status;
    
    -- 2. 检查controller列是否存在
    SET @sql = CONCAT(
        'SELECT COUNT(*) INTO @col_exists FROM information_schema.COLUMNS ',
        'WHERE TABLE_SCHEMA=DATABASE() AND TABLE_NAME=''', tablename, ''' AND COLUMN_NAME=''controller'''
    );
    PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;
    SET col_exists = @col_exists;

    IF col_exists = 0 THEN
        SET @sql = CONCAT('ALTER TABLE `', tablename, '` ADD COLUMN `controller` INT DEFAULT 0');
        PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;
        SELECT 'controller列已添加' AS controller_status;
    END IF;
    
    -- 3. 检查Full_Y列是否存在
    SET @sql = CONCAT(
        'SELECT COUNT(*) INTO @col_exists FROM information_schema.COLUMNS ',
        'WHERE TABLE_SCHEMA=DATABASE() AND TABLE_NAME=''', tablename, ''' AND COLUMN_NAME=''Full_Y'''
    );
    PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;
    SET col_exists = @col_exists;

    IF col_exists = 0 THEN
        SET @sql = CONCAT('ALTER TABLE `', tablename, '` ADD COLUMN `Full_Y` DECIMAL(20,10) DEFAULT NULL');
        PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;
        SELECT 'Full_Y列已添加' AS full_y_status;
    END IF;

    -- 4. 计算新的controller和Full_Y逻辑
    SELECT '开始计算controller和Full_Y...' AS calc_status;
    
    -- 使用窗口函数计算累积controller和Full_Y
    SET @sql = CONCAT(
        'UPDATE `', tablename, '` t1 ',
        'JOIN (',
        '  SELECT date, ',
        '    SUM(CASE WHEN (close - midprice) > 0 THEN 1 ELSE 0 END) ',
        '    OVER (ORDER BY date ASC ROWS UNBOUNDED PRECEDING) as cumulative_controller, ',
        '    ROW_NUMBER() OVER (ORDER BY date ASC) as row_num ',
        '  FROM `', tablename, '` ',
        '  WHERE midprice IS NOT NULL ',
        '  ORDER BY date ASC',
        ') t2 ON t1.date = t2.date ',
        'SET t1.controller = t2.cumulative_controller, ',
        '    t1.Full_Y = t2.cumulative_controller / t2.row_num'
    );
    PREPARE stmt FROM @sql;
    EXECUTE stmt;
    DEALLOCATE PREPARE stmt;
    
    SELECT 'controller和Full_Y计算完成' AS calc_complete_status;

    -- 5. 计算k值 (最终controller值除以总记录数)
    SET @sql = CONCAT(
        'SELECT MAX(controller) / COUNT(*) INTO @k_value FROM `', tablename, '` WHERE controller IS NOT NULL'
    );
    PREPARE stmt FROM @sql;
    EXECUTE stmt;
    DEALLOCATE PREPARE stmt;
    
    SET result_k = @k_value;
    
    -- 6. 返回统计信息
    SELECT 
        CONCAT('更新完成 - 表: ', tablename) AS completion_message,
        CONCAT('k值: ', IFNULL(result_k, 0)) AS k_value_message,
        CONCAT('耗时: ', TIMESTAMPDIFF(SECOND, v_start_time, NOW()), ' 秒') AS duration;
        
END
            """
            
            self.cursor.execute(new_procedure)
            print("✅ 成功创建新逻辑的sp_updatecontroller")
            
            # 保存新的存储过程定义到文件
            with open('sp_updatecontroller_new_logic.sql', 'w', encoding='utf-8') as f:
                f.write(new_procedure)
            print("📄 新定义已保存到 sp_updatecontroller_new_logic.sql")
            
            return True
            
        except mysql.connector.Error as e:
            print(f"❌ 创建新逻辑存储过程失败: {e}")
            return False
    
    def test_new_logic(self, table_name):
        """测试新逻辑"""
        try:
            print(f"\n🧪 测试新逻辑的sp_updatecontroller - 表: {table_name}")
            
            # 调用存储过程
            args = [table_name, 0]  # 第二个参数是OUT参数的占位符
            result = self.cursor.callproc('sp_updatecontroller', args)
            
            # 获取结果集
            print("📊 sp_updatecontroller执行过程:")
            for result_set in self.cursor.stored_results():
                rows = result_set.fetchall()
                for row in rows:
                    print(f"   {row}")
            
            # 显示OUT参数结果
            k_value = result[1]  # OUT参数result_k的值
            print(f"📊 k值结果: {k_value}")
            
            return True
            
        except mysql.connector.Error as e:
            print(f"❌ 测试新逻辑失败: {e}")
            return False
    
    def verify_new_logic(self, table_name):
        """验证新逻辑的计算结果"""
        try:
            print(f"\n🔍 验证新逻辑计算结果 - 表: {table_name}")
            
            # 统计基本信息
            self.cursor.execute(f"""
                SELECT 
                    COUNT(*) as total_rows,
                    COUNT(controller) as controller_rows,
                    COUNT(Full_Y) as full_y_rows,
                    MIN(controller) as min_controller,
                    MAX(controller) as max_controller,
                    MIN(Full_Y) as min_full_y,
                    MAX(Full_Y) as max_full_y,
                    AVG(Full_Y) as avg_full_y
                FROM {table_name}
            """)
            
            stats = self.cursor.fetchone()
            print(f"📊 新逻辑统计结果:")
            print(f"   • 总记录数: {stats[0]}")
            print(f"   • controller记录数: {stats[1]}")
            print(f"   • Full_Y记录数: {stats[2]}")
            print(f"   • controller范围: {stats[3]} ~ {stats[4]}")
            if stats[5] is not None:
                print(f"   • Full_Y范围: {float(stats[5]):.6f} ~ {float(stats[6]):.6f}")
                print(f"   • Full_Y平均值: {float(stats[7]):.6f}")
            
            # 查看前20行的计算过程
            self.cursor.execute(f"""
                SELECT 
                    ROW_NUMBER() OVER (ORDER BY date ASC) as row_num,
                    date, 
                    close,
                    midprice,
                    CASE WHEN (close - midprice) > 0 THEN 'YES' ELSE 'NO' END as is_strong,
                    controller,
                    Full_Y
                FROM {table_name} 
                WHERE controller IS NOT NULL
                ORDER BY date ASC 
                LIMIT 20
            """)
            
            results = self.cursor.fetchall()
            print(f"\n📊 前20行新逻辑计算过程:")
            print("行号 | 日期          | 收盘价  | midprice | 强势? | controller | Full_Y")
            print("-" * 80)
            for row in results:
                row_num = row[0]
                date_str = str(row[1])
                close_val = float(row[2]) if row[2] is not None else 0.0
                mid_val = float(row[3]) if row[3] is not None else 0.0
                is_strong = row[4]
                controller_val = row[5] if row[5] is not None else 0
                full_y_val = float(row[6]) if row[6] is not None else 0.0
                print(f"{row_num:4d} | {date_str} | {close_val:7.2f} | {mid_val:8.4f} | {is_strong:5s} | {controller_val:10d} | {full_y_val:10.6f}")
            
            # 查看最新20行数据
            self.cursor.execute(f"""
                SELECT 
                    date, 
                    close,
                    midprice,
                    CASE WHEN (close - midprice) > 0 THEN 'YES' ELSE 'NO' END as is_strong,
                    controller,
                    Full_Y,
                    ROUND((close - midprice), 4) as price_diff
                FROM {table_name} 
                WHERE controller IS NOT NULL
                ORDER BY date DESC 
                LIMIT 20
            """)
            
            latest_results = self.cursor.fetchall()
            print(f"\n📊 最新20条数据:")
            print("日期          | 收盘价  | midprice | 强势? | controller | Full_Y     | 价差")
            print("-" * 85)
            for row in latest_results:
                date_str = str(row[0])
                close_val = float(row[1]) if row[1] is not None else 0.0
                mid_val = float(row[2]) if row[2] is not None else 0.0
                is_strong = row[3]
                controller_val = row[4] if row[4] is not None else 0
                full_y_val = float(row[5]) if row[5] is not None else 0.0
                price_diff = float(row[6]) if row[6] is not None else 0.0
                print(f"{date_str} | {close_val:7.2f} | {mid_val:8.4f} | {is_strong:5s} | {controller_val:10d} | {full_y_val:10.6f} | {price_diff:7.4f}")
            
            # 验证controller的单调性（应该是递增的）
            self.cursor.execute(f"""
                SELECT COUNT(*) as violations
                FROM (
                    SELECT 
                        controller,
                        LAG(controller) OVER (ORDER BY date ASC) as prev_controller
                    FROM {table_name}
                    WHERE controller IS NOT NULL
                ) t
                WHERE controller < prev_controller
            """)
            
            violations = self.cursor.fetchone()[0]
            if violations == 0:
                print(f"\n🔍 验证结果:")
                print("   ✅ controller单调递增，计算正确")
            else:
                print(f"\n🔍 验证结果:")
                print(f"   ⚠️ 发现{violations}处controller非递增")
            
            # 验证Full_Y = controller / 行号
            print(f"\n🔍 验证Full_Y计算公式:")
            self.cursor.execute(f"""
                SELECT 
                    COUNT(*) as total_checked,
                    SUM(CASE WHEN ABS(Full_Y - (controller / ROW_NUMBER() OVER (ORDER BY date ASC))) < 0.000001 THEN 1 ELSE 0 END) as correct_count
                FROM {table_name}
                WHERE controller IS NOT NULL AND Full_Y IS NOT NULL
            """)
            
            formula_check = self.cursor.fetchone()
            total_checked = formula_check[0]
            correct_count = formula_check[1]
            
            if correct_count == total_checked:
                print(f"   ✅ Full_Y = controller/行号 公式验证正确 ({correct_count}/{total_checked})")
            else:
                print(f"   ⚠️ Full_Y公式验证有误差 ({correct_count}/{total_checked})")
            
            return True
            
        except mysql.connector.Error as e:
            print(f"❌ 验证新逻辑失败: {e}")
            return False
    
    def generate_usage_examples(self, table_name):
        """生成使用示例"""
        print(f"\n📝 新逻辑使用示例 (表: {table_name}):")
        print("=" * 60)
        
        print("💡 新逻辑说明:")
        print("   • controller: 累积计数器，当(close-midprice>0)时递增")
        print("   • Full_Y: controller/行号，表示累积强势比例")
        print("   • k值: 最终controller值/总记录数，表示整体强势比例")
        
        print("\n1️⃣ 完整更新所有字段:")
        print(f"   CALL sp_updatecontroller('{table_name}', @k_value);")
        print("   SELECT @k_value AS k_result;")
        
        print("\n2️⃣ 查看controller累积过程:")
        print(f"""   SELECT 
       ROW_NUMBER() OVER (ORDER BY date ASC) as row_num,
       date, close, midprice,
       CASE WHEN (close - midprice) > 0 THEN 'YES' ELSE 'NO' END as is_strong,
       controller,
       Full_Y
   FROM {table_name} 
   ORDER BY date ASC 
   LIMIT 20;""")
        
        print("\n3️⃣ 分析强势比例趋势:")
        print(f"""   SELECT 
       YEAR(date) as year,
       COUNT(*) as total_days,
       MAX(controller) as year_end_controller,
       ROUND(MAX(Full_Y), 6) as year_end_full_y,
       ROUND(MAX(controller) / COUNT(*), 6) as year_strong_ratio
   FROM {table_name}
   WHERE controller IS NOT NULL
   GROUP BY YEAR(date)
   ORDER BY year DESC;""")
        
        print("\n4️⃣ 查看最新强势情况:")
        print(f"""   SELECT date, close, midprice,
          ROUND(close - midprice, 4) as price_diff,
          CASE WHEN (close - midprice) > 0 THEN 'STRONG' ELSE 'WEAK' END as status,
          controller, Full_Y
   FROM {table_name} 
   ORDER BY date DESC 
   LIMIT 10;""")
    
    def close_connection(self):
        """关闭数据库连接"""
        if self.cursor:
            self.cursor.close()
        if self.connection:
            self.connection.close()
        print("🔒 数据库连接已关闭")
    
    def run(self, table_name='stock_600887_ss'):
        """执行主流程"""
        print("🎯 更新controller和Full_Y的计算逻辑")
        print("=" * 60)
        print(f"📅 执行时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"🏠 数据库: {self.db_config['host']}/{self.db_config['database']}")
        print(f"📊 测试表: {table_name}")
        print("\n💡 新逻辑:")
        print("   • controller: 累积计数器，当(close-midprice>0)时controller++")
        print("   • Full_Y: controller/行号，累积强势比例")
        
        try:
            # 1. 连接数据库
            if not self.connect_database():
                return False
            
            # 2. 备份当前存储过程
            if not self.backup_current_controller():
                return False
            
            # 3. 创建新逻辑
            if not self.create_new_controller_logic():
                return False
            
            # 4. 测试新逻辑
            if not self.test_new_logic(table_name):
                return False
            
            # 5. 验证新逻辑
            if not self.verify_new_logic(table_name):
                return False
            
            # 6. 生成使用示例
            self.generate_usage_examples(table_name)
            
            print("\n🎉 新逻辑成功集成到sp_updatecontroller!")
            print("💡 现在controller表示累积强势次数，Full_Y表示累积强势比例")
            
            return True
            
        except Exception as e:
            print(f"❌ 执行失败: {e}")
            return False
        
        finally:
            self.close_connection()

def main():
    """主函数"""
    updater = UpdatedControllerLogic()
    success = updater.run('stock_600887_ss')
    
    if success:
        print("\n✅ 新逻辑集成完成!")
        print("📝 使用方法: CALL sp_updatecontroller('stock_600887_ss', @k_value);")
    else:
        print("\n❌ 新逻辑集成失败!")

if __name__ == "__main__":
    main()
