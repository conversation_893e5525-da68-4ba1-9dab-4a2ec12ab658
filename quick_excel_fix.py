#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd
import yfinance as yf
from datetime import datetime
import os

def quick_excel_fix():
    """快速修复Excel记录填充问题"""
    
    excel_file = "交易记录追踪0023HK.xlsx"
    
    print("🔧 快速修复Excel记录...")
    
    # 检查文件是否存在
    if os.path.exists(excel_file):
        try:
            df = pd.read_excel(excel_file)
            print(f"📋 当前记录数: {len(df)}")
        except:
            df = pd.DataFrame()
            print("📋 创建新记录")
    else:
        df = pd.DataFrame()
        print("📋 创建新记录")
    
    # 获取当前价格
    try:
        ticker = yf.Ticker("0023.HK")
        hist = ticker.history(period="1d")
        current_price = hist['Close'].iloc[-1]
        print(f"📈 当前价格: {current_price:.4f} 港元")
    except:
        current_price = 12.12  # 您提到的价格
        print(f"📈 使用默认价格: {current_price:.4f} 港元")
    
    # 创建完整的记录
    record = {
        '交易日期': datetime.now().strftime('%Y-%m-%d'),
        '交易类型': '观察',
        '交易方向': '无',
        '交易价格': current_price,
        '持仓数量': 0,
        '交易金额': 0.00,
        '手续费': 0.00,
        '净交易额': 0.00,
        '持仓成本': 0.00,
        '当前市值': 0.00,
        '浮动盈亏': 0.00,
        '实现盈亏': 0.00,
        '累计盈亏': 0.00,
        '账户余额': 10000.00,
        '总资产': 10000.00,
        '收益率': 0.00,
        '累计收益率': 0.00,
        'Y值': 0.5000,
        'X值': 0.2965,
        'E值': -0.2035,
        '信号强度': '强烈卖出',
        '风险等级': '中风险',
        '备注': f'快速修复记录收盘价{current_price:.2f}港元所有字段已填充'
    }
    
    # 添加记录
    new_df = pd.concat([df, pd.DataFrame([record])], ignore_index=True)
    
    # 保存
    new_df.to_excel(excel_file, index=False)
    
    print("✅ Excel记录修复完成")
    print(f"📊 总记录数: {len(new_df)}")
    print(f"💾 文件: {excel_file}")
    
    # 显示最新记录
    print("\n📋 最新记录详情:")
    latest = new_df.iloc[-1]
    for col, value in latest.items():
        print(f"   {col}: {value}")
    
    return True

if __name__ == "__main__":
    quick_excel_fix()
