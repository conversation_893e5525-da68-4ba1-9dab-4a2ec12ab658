#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd
from datetime import datetime

def quick_fix_capital():
    """快速修正资金数据"""
    
    excel_file = "交易记录追踪0023HK.xlsx"
    
    print("🔧 快速修正Excel资金数据...")
    print("💰 实际总资本: 2,500 港元")
    print("📊 最大持仓: 200股")
    
    # 用户实际参数
    total_capital = 2500.00
    max_shares = 200
    current_price = 12.18  # 当前价格
    
    # 计算实际交易数据
    trade_amount = current_price * max_shares  # 2436港元
    commission = trade_amount * 0.001          # 2.44港元手续费
    account_balance = total_capital - trade_amount - commission  # 剩余现金
    current_market_value = trade_amount        # 当前市值
    total_assets = account_balance + current_market_value  # 总资产
    
    # 创建修正记录
    corrected_record = {
        '交易日期': datetime.now().strftime('%Y-%m-%d'),
        '交易类型': '开仓',
        '交易方向': '空头',
        '交易价格': current_price,
        '持仓数量': max_shares,
        '交易金额': trade_amount,
        '手续费': commission,
        '净交易额': trade_amount - commission,
        '持仓成本': current_price,
        '当前市值': current_market_value,
        '浮动盈亏': 0.00,
        '实现盈亏': 0.00,
        '累计盈亏': 0.00,
        '账户余额': account_balance,
        '总资产': total_assets,
        '收益率': 0.00,
        '累计收益率': (total_assets - total_capital) / total_capital * 100,
        'Y值': 0.2069,
        'X值': 0.3211,
        'E值': -0.0525,
        '信号强度': '强烈卖出',
        '风险等级': '高风险',
        '备注': f'资金修正 总资本{total_capital:,.0f}港元 持仓{max_shares}股 价格{current_price:.2f}港元'
    }
    
    try:
        # 读取现有Excel
        df = pd.read_excel(excel_file)
        
        # 替换最新记录
        df.iloc[-1] = corrected_record
        
        # 保存
        df.to_excel(excel_file, index=False)
        
        print("✅ Excel文件已修正")
        
        # 显示结果
        print(f"\n📋 修正后的数据:")
        print(f"   💰 总资本: {total_capital:,.2f} 港元")
        print(f"   📊 持仓: {max_shares} 股")
        print(f"   💵 交易金额: {trade_amount:,.2f} 港元")
        print(f"   💸 手续费: {commission:.2f} 港元")
        print(f"   💰 账户余额: {account_balance:,.2f} 港元")
        print(f"   📈 当前市值: {current_market_value:,.2f} 港元")
        print(f"   💎 总资产: {total_assets:,.2f} 港元")
        print(f"   📊 累计收益率: {corrected_record['累计收益率']:.2f}%")
        
        return True
        
    except Exception as e:
        print(f"❌ 修正失败: {e}")
        return False

if __name__ == "__main__":
    quick_fix_capital()
