#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
将EAB 1年回测记录转换为标准交易记录追踪格式
"""

import pandas as pd
import numpy as np
from datetime import datetime
import os
import shutil

def convert_backtest_to_tracking():
    """转换回测记录为交易追踪格式"""
    
    print("🔄 转换EAB回测记录为交易记录追踪格式")
    print("=" * 60)
    
    # 读取回测记录
    backtest_file = "EAB_1年交易记录_20250727_232235.xlsx"
    target_file = "交易记录追踪0023HK.xlsx"
    
    if not os.path.exists(backtest_file):
        print(f"❌ 回测文件不存在: {backtest_file}")
        return False
    
    try:
        # 读取回测数据
        backtest_df = pd.read_excel(backtest_file)
        print(f"✅ 读取回测记录: {len(backtest_df)} 条")
        
        # 备份原文件
        if os.path.exists(target_file):
            backup_file = f"交易记录追踪0023HK_备份_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
            shutil.copy2(target_file, backup_file)
            print(f"✅ 原文件已备份: {backup_file}")
        
        # 转换为标准格式
        tracking_records = []
        
        # 初始状态
        current_capital = 2500.00
        total_assets = 2500.00
        cumulative_pnl = 0.00
        position_quantity = 0
        position_direction = "无"
        position_status = "空仓"
        entry_price = 0.00
        holding_days = 0
        
        for i, row in backtest_df.iterrows():
            trade_date = pd.to_datetime(row['date']).strftime('%Y-%m-%d')
            action = row['action']
            price = row['price']
            size = row.get('size', 0)
            pnl = row.get('pnl', 0)
            reason = row.get('reason', '')
            
            # 根据交易类型设置字段
            if '开多仓' in action:
                # 开多仓
                trade_type = "开仓"
                direction = "多头"
                position_quantity = size
                position_direction = "多头"
                position_status = "持仓中"
                entry_price = price
                holding_days = 1
                
                trade_amount = size * price
                commission = trade_amount * 0.001
                current_capital -= (trade_amount + commission)
                total_assets = current_capital + trade_amount
                
                tracking_records.append({
                    '交易日期': trade_date,
                    '交易类型': trade_type,
                    '交易方向': direction,
                    '交易价格': price,
                    '入场价格': entry_price,
                    '持仓数量': position_quantity,
                    '交易金额': trade_amount,
                    '手续费': commission,
                    '止盈价': price * 1.025,  # 2.5%止盈
                    '止损价': price * 0.98,   # 2%止损
                    '净交易额': trade_amount + commission,
                    '持仓成本': entry_price,
                    '当前市值': trade_amount,
                    '浮动盈亏': 0.00,
                    '实现盈亏': cumulative_pnl,
                    '累计盈亏': cumulative_pnl,
                    '收益率': 0.00,
                    '累计收益率': (total_assets / 2500 - 1) * 100,
                    '账户余额': current_capital,
                    '总资产': total_assets,
                    'Y值': 0.000,
                    'X值': 0.000,
                    'E值': 0.000,
                    'Full_Y': 0.000,
                    'MoneyFlowRatio': 0.000,
                    'MyE': 0.000,
                    '信号强度': 3,
                    '风险等级': '中风险',
                    '备注': reason,
                    'RSI': 50.0,
                    'MFI': 50.0,
                    '信号图标': '📈',
                    '持仓状态': position_status,
                    '持仓方向': position_direction,
                    '持仓天数': holding_days,
                    '持仓比例%': (trade_amount / total_assets) * 100,
                    '盈亏比例%': 0.00,
                    '止盈距离%': 2.50,
                    '止损距离%': 2.00,
                    '风险收益比': 1.25,
                    '持仓强度': '重仓',
                    '资金利用率%': (trade_amount / total_assets) * 100
                })
                
            elif '开空仓' in action:
                # 开空仓
                trade_type = "开仓"
                direction = "空头"
                position_quantity = size
                position_direction = "空头"
                position_status = "持仓中"
                entry_price = price
                holding_days = 1
                
                trade_amount = size * price
                commission = trade_amount * 0.001
                current_capital += (trade_amount - commission)
                total_assets = current_capital + trade_amount
                
                tracking_records.append({
                    '交易日期': trade_date,
                    '交易类型': trade_type,
                    '交易方向': direction,
                    '交易价格': price,
                    '入场价格': entry_price,
                    '持仓数量': position_quantity,
                    '交易金额': trade_amount,
                    '手续费': commission,
                    '止盈价': price * 0.975,  # 2.5%止盈
                    '止损价': price * 1.02,   # 2%止损
                    '净交易额': trade_amount - commission,
                    '持仓成本': entry_price,
                    '当前市值': trade_amount,
                    '浮动盈亏': 0.00,
                    '实现盈亏': cumulative_pnl,
                    '累计盈亏': cumulative_pnl,
                    '收益率': 0.00,
                    '累计收益率': (total_assets / 2500 - 1) * 100,
                    '账户余额': current_capital,
                    '总资产': total_assets,
                    'Y值': 0.000,
                    'X值': 0.000,
                    'E值': 0.000,
                    'Full_Y': 0.000,
                    'MoneyFlowRatio': 0.000,
                    'MyE': 0.000,
                    '信号强度': 3,
                    '风险等级': '中风险',
                    '备注': reason,
                    'RSI': 50.0,
                    'MFI': 50.0,
                    '信号图标': '📉',
                    '持仓状态': position_status,
                    '持仓方向': position_direction,
                    '持仓天数': holding_days,
                    '持仓比例%': (trade_amount / total_assets) * 100,
                    '盈亏比例%': 0.00,
                    '止盈距离%': 2.50,
                    '止损距离%': 2.00,
                    '风险收益比': 1.25,
                    '持仓强度': '重仓',
                    '资金利用率%': (trade_amount / total_assets) * 100
                })
                
            elif '平' in action:
                # 平仓
                trade_type = "平仓"
                direction = "平多" if "多" in action else "平空"
                
                trade_amount = position_quantity * price
                commission = trade_amount * 0.001
                
                if "多" in action:
                    current_capital += (trade_amount - commission)
                else:
                    current_capital -= (trade_amount + commission)
                
                cumulative_pnl += pnl
                trade_return = (pnl / (position_quantity * entry_price)) * 100 if position_quantity > 0 else 0
                
                total_assets = current_capital
                
                tracking_records.append({
                    '交易日期': trade_date,
                    '交易类型': trade_type,
                    '交易方向': direction,
                    '交易价格': price,
                    '入场价格': entry_price,
                    '持仓数量': 0,  # 平仓后为0
                    '交易金额': trade_amount,
                    '手续费': commission,
                    '止盈价': 0.00,
                    '止损价': 0.00,
                    '净交易额': trade_amount - commission if "多" in action else -(trade_amount + commission),
                    '持仓成本': 0.00,
                    '当前市值': 0.00,
                    '浮动盈亏': 0.00,
                    '实现盈亏': cumulative_pnl,
                    '累计盈亏': cumulative_pnl,
                    '收益率': trade_return,
                    '累计收益率': (total_assets / 2500 - 1) * 100,
                    '账户余额': current_capital,
                    '总资产': total_assets,
                    'Y值': 0.000,
                    'X值': 0.000,
                    'E值': 0.000,
                    'Full_Y': 0.000,
                    'MoneyFlowRatio': 0.000,
                    'MyE': 0.000,
                    '信号强度': 2,
                    '风险等级': '低风险' if pnl > 0 else '高风险',
                    '备注': reason,
                    'RSI': 50.0,
                    'MFI': 50.0,
                    '信号图标': '✅' if pnl > 0 else '❌',
                    '持仓状态': '空仓',
                    '持仓方向': '无',
                    '持仓天数': 0,
                    '持仓比例%': 0.00,
                    '盈亏比例%': trade_return,
                    '止盈距离%': 0.00,
                    '止损距离%': 0.00,
                    '风险收益比': 0.00,
                    '持仓强度': '无',
                    '资金利用率%': 0.00
                })
                
                # 重置持仓状态
                position_quantity = 0
                position_direction = "无"
                position_status = "空仓"
                entry_price = 0.00
                holding_days = 0
        
        # 创建DataFrame并保存
        tracking_df = pd.DataFrame(tracking_records)
        
        # 保存为标准交易记录追踪文件
        tracking_df.to_excel(target_file, index=False)
        
        print(f"✅ 转换完成: {len(tracking_records)} 条记录")
        print(f"✅ 已保存为: {target_file}")
        
        # 显示转换摘要
        print(f"\n📊 转换摘要:")
        print(f"   原始回测记录: {len(backtest_df)} 条")
        print(f"   转换后记录: {len(tracking_records)} 条")
        print(f"   开仓记录: {len([r for r in tracking_records if r['交易类型'] == '开仓'])}")
        print(f"   平仓记录: {len([r for r in tracking_records if r['交易类型'] == '平仓'])}")
        print(f"   最终资金: {current_capital:.2f}")
        print(f"   累计盈亏: {cumulative_pnl:.2f}")
        print(f"   总收益率: {(total_assets/2500-1)*100:.2f}%")
        
        return True
        
    except Exception as e:
        print(f"❌ 转换失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🔄 EAB回测记录转换工具")
    print("=" * 50)
    print("📋 功能:")
    print("   • 读取EAB 1年回测记录")
    print("   • 转换为标准交易追踪格式")
    print("   • 替换现有交易记录追踪文件")
    print("   • 自动备份原文件")
    
    try:
        success = convert_backtest_to_tracking()
        
        if success:
            print(f"\n🎉 转换成功完成！")
            print(f"📄 新的交易记录追踪文件已生成")
            print(f"🔄 现在可以使用标准的持仓管理系统")
        else:
            print(f"\n❌ 转换失败")
            
    except Exception as e:
        print(f"\n❌ 程序执行失败: {e}")

if __name__ == "__main__":
    main()
