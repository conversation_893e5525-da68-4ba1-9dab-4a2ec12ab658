-- 数据库清理脚本
-- 生成时间: 2025-07-20 12:37:57
-- 当前数据库大小: 60.30 MB

-- 1. 清理更多临时数据 (如果需要)
-- DELETE FROM table_name WHERE date < DATE_SUB(NOW(), INTERVAL 2 YEAR);

-- 2. 重建索引 (对大表谨慎使用)
-- ALTER TABLE large_table_name ENGINE=InnoDB;

-- 3. 清理更多二进制日志
-- PURGE BINARY LOGS BEFORE DATE_SUB(NOW(), INTERVAL 3 DAY);

-- 4. 检查表空间
-- SELECT table_name, 
--        ROUND(((data_length + index_length) / 1024 / 1024), 2) as size_mb
-- FROM information_schema.TABLES 
-- WHERE table_schema = 'finance'
-- ORDER BY (data_length + index_length) DESC;
