#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
更新MySQL数据库中的stock_0002_hk表 (修复版)
==========================================

使用yfinance获取0002.HK (中电控股) 的最新数据
并更新到MySQL数据库的stock_0002_hk表中

根据现有表结构适配:
- id, date, open, high, low, close, volume
- symbol, name, market
- y_probability, inflow_ratio, Full_Y
- i, midprice, Controller, E

作者: Cosmoon NG
日期: 2025年7月24日
"""

import yfinance as yf
import pandas as pd
import numpy as np
import mysql.connector
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

class Stock0002HKDatabaseUpdaterFixed:
    def __init__(self):
        """初始化数据库更新器"""
        self.symbol = "0002.HK"  # 中电控股
        self.db_config = {
            'host': 'localhost',
            'user': 'root',
            'password': '12345678',
            'database': 'finance',
            'charset': 'utf8mb4',
            'autocommit': True
        }
        self.connection = None
        self.cursor = None
        self.data = None
        self.today = datetime.now().strftime('%Y-%m-%d')
        
    def connect_database(self):
        """连接MySQL数据库"""
        try:
            print(f"🔗 连接MySQL数据库...")
            self.connection = mysql.connector.connect(**self.db_config)
            self.cursor = self.connection.cursor()
            print(f"✅ 成功连接到数据库: {self.db_config['host']}/{self.db_config['database']}")
            return True
        except mysql.connector.Error as e:
            print(f"❌ 数据库连接失败: {e}")
            return False
    
    def check_table_structure(self):
        """检查现有表结构"""
        try:
            self.cursor.execute("DESCRIBE stock_0002_hk")
            columns = self.cursor.fetchall()
            print("📋 现有表结构:")
            self.existing_columns = {}
            for col in columns:
                print(f"   {col[0]} - {col[1]}")
                self.existing_columns[col[0]] = col[1]
            
            # 查看记录数和最新日期
            self.cursor.execute("SELECT COUNT(*) FROM stock_0002_hk")
            count = self.cursor.fetchone()[0]
            print(f"📊 当前记录数: {count}")
            
            self.cursor.execute("SELECT MAX(date) FROM stock_0002_hk")
            latest_date = self.cursor.fetchone()[0]
            if latest_date:
                print(f"📅 最新日期: {latest_date}")
            
            return True
                
        except mysql.connector.Error as e:
            print(f"❌ 检查表结构失败: {e}")
            return False
    
    def fetch_latest_data(self, period="25y"):
        """获取最新的股票数据"""
        print(f"📊 获取{self.symbol} (中电控股) 最新数据...")
        print(f"📅 更新日期: {self.today}")
        
        try:
            # 获取股票数据
            ticker = yf.Ticker(self.symbol)
            print(f"   🔄 正在从Yahoo Finance获取{self.symbol}数据...")
            
            # 获取历史数据
            end_date = datetime.now()
            start_date = end_date - timedelta(days=25*365)
            
            hist_data = ticker.history(start=start_date, end=end_date, auto_adjust=True, prepost=True)
            
            if hist_data.empty:
                print(f"   ❌ 无法获取{self.symbol}数据，尝试备用方法...")
                hist_data = yf.download(self.symbol, start=start_date, end=end_date, progress=False)
            
            if hist_data.empty:
                print(f"   ❌ 仍无法获取数据")
                return False
            
            # 数据预处理
            self.data = pd.DataFrame({
                'date': hist_data.index.date,  # 只保留日期部分
                'open': hist_data['Open'],
                'high': hist_data['High'],
                'low': hist_data['Low'],
                'close': hist_data['Close'],
                'volume': hist_data['Volume']
            })
            
            # 清理数据
            self.data = self.data.dropna().reset_index(drop=True)
            
            print(f"   ✅ 成功获取 {len(self.data)} 条数据")
            print(f"   📅 数据范围: {self.data['date'].min()} 至 {self.data['date'].max()}")
            print(f"   📈 最新价格: {self.data['close'].iloc[-1]:.2f} 港元")
            
            return True
            
        except Exception as e:
            print(f"   ❌ 数据获取失败: {e}")
            return False
    
    def calculate_technical_indicators(self):
        """计算技术指标 (适配现有表结构)"""
        print(f"\n🧮 计算技术指标...")
        
        # 添加固定字段
        self.data['symbol'] = '0002.HK'
        self.data['name'] = '中电控股'
        self.data['market'] = 'HK'
        
        # === 计算MFI和相关指标 ===
        print("   计算MFI和资金流指标...")
        
        # 1. 计算典型价格和资金流
        self.data['typical_price'] = (self.data['high'] + self.data['low'] + self.data['close']) / 3
        self.data['money_flow'] = self.data['typical_price'] * self.data['volume']
        self.data['price_change'] = self.data['typical_price'].diff()
        
        # 2. 分离正负资金流
        self.data['positive_mf'] = np.where(self.data['price_change'] > 0, self.data['money_flow'], 0)
        self.data['negative_mf'] = np.where(self.data['price_change'] < 0, self.data['money_flow'], 0)
        
        # 3. 计算14日正负资金流总和
        period = 14
        self.data['positive_mf_14'] = self.data['positive_mf'].rolling(period).sum()
        self.data['negative_mf_14'] = self.data['negative_mf'].rolling(period).sum()
        
        # 4. 计算资金流比率 (对应inflow_ratio)
        total_mf = self.data['positive_mf_14'] + self.data['negative_mf_14']
        self.data['inflow_ratio'] = self.data['positive_mf_14'] / (total_mf + 1e-10)
        self.data['inflow_ratio'] = self.data['inflow_ratio'].fillna(0.5).clip(0, 1)
        
        # === 计算RSI和Y值 ===
        print("   计算RSI和Y值...")
        
        # 1. 计算RSI
        delta = self.data['close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        rsi = rsi.fillna(50)
        
        # 2. 计算价格动量
        price_momentum = self.data['close'].pct_change(10).fillna(0)
        
        # 3. Y值计算 (y_probability)
        self.data['y_probability'] = (rsi / 100 + np.tanh(price_momentum * 5) + 1) / 2
        self.data['y_probability'] = np.clip(self.data['y_probability'], 0.1, 0.9)
        
        # === 计算中位价格和控制系数 ===
        print("   计算中位价格和控制系数...")
        
        # 中位价格 (22日滚动中位数)
        self.data['midprice'] = self.data['close'].rolling(22).median()
        
        # 控股商标记 (价格高于中位数为1，否则为0)
        self.data['Controller'] = np.where(self.data['close'] > self.data['midprice'], 1, 0)
        
        # Full_Y计算 (累积控制系数)
        cumulative_controller = self.data['Controller'].cumsum()
        row_numbers = np.arange(1, len(self.data) + 1)
        self.data['Full_Y'] = cumulative_controller / row_numbers
        
        # 行号
        self.data['i'] = row_numbers
        
        # === E值计算 ===
        print("   计算E值...")
        # E值 = (8 * inflow_ratio - 3) * y_probability - 3 * inflow_ratio + 1
        self.data['E'] = (8 * self.data['inflow_ratio'] - 3) * self.data['y_probability'] - 3 * self.data['inflow_ratio'] + 1
        
        print(f"   ✅ 技术指标计算完成")
        print(f"   📊 inflow_ratio范围: {self.data['inflow_ratio'].min():.3f} - {self.data['inflow_ratio'].max():.3f}")
        print(f"   📊 y_probability范围: {self.data['y_probability'].min():.3f} - {self.data['y_probability'].max():.3f}")
        print(f"   📊 Full_Y范围: {self.data['Full_Y'].min():.3f} - {self.data['Full_Y'].max():.3f}")
        print(f"   📊 E值范围: {self.data['E'].min():.3f} - {self.data['E'].max():.3f}")
    
    def get_existing_dates(self):
        """获取数据库中已存在的日期"""
        try:
            self.cursor.execute("SELECT date FROM stock_0002_hk ORDER BY date")
            existing_dates = [row[0] for row in self.cursor.fetchall()]
            return set(existing_dates)
        except mysql.connector.Error as e:
            print(f"❌ 获取已存在日期失败: {e}")
            return set()
    
    def update_database(self):
        """更新数据库 (适配现有表结构)"""
        print(f"\n💾 更新数据库...")
        
        try:
            # 获取已存在的日期
            existing_dates = self.get_existing_dates()
            print(f"   📊 数据库中已有 {len(existing_dates)} 条记录")
            
            # 筛选新数据
            new_data = self.data[~self.data['date'].isin(existing_dates)]
            print(f"   📊 需要插入 {len(new_data)} 条新记录")
            
            if len(new_data) == 0:
                print("   ✅ 数据已是最新，无需更新")
                return True
            
            # 准备插入SQL (根据现有表结构)
            insert_sql = """
            INSERT INTO stock_0002_hk (
                date, open, high, low, close, volume,
                symbol, name, market,
                y_probability, inflow_ratio, Full_Y,
                i, midprice, Controller, E
            ) VALUES (
                %s, %s, %s, %s, %s, %s,
                %s, %s, %s,
                %s, %s, %s,
                %s, %s, %s, %s
            )
            """
            
            # 批量插入数据
            insert_count = 0
            for _, row in new_data.iterrows():
                try:
                    values = (
                        row['date'], 
                        float(row['open']), float(row['high']), float(row['low']), 
                        float(row['close']), int(row['volume']),
                        row['symbol'], row['name'], row['market'],
                        float(row['y_probability']), float(row['inflow_ratio']), float(row['Full_Y']),
                        int(row['i']),
                        float(row['midprice']) if pd.notna(row['midprice']) else None,
                        int(row['Controller']), float(row['E'])
                    )
                    
                    self.cursor.execute(insert_sql, values)
                    insert_count += 1
                    
                except mysql.connector.Error as e:
                    print(f"   ⚠️ 插入记录失败 {row['date']}: {e}")
                    continue
            
            print(f"   ✅ 成功插入 {insert_count} 条记录")
            
            # 显示最新记录
            self.cursor.execute("""
                SELECT date, close, y_probability, inflow_ratio, Full_Y, E
                FROM stock_0002_hk 
                ORDER BY date DESC 
                LIMIT 5
            """)
            
            latest_records = self.cursor.fetchall()
            print(f"\n📈 最新5条记录:")
            for record in latest_records:
                print(f"   {record[0]}: 收盘={record[1]:.2f}, Y={record[2]:.3f}, 资金流={record[3]:.3f}, Full_Y={record[4]:.3f}, E={record[5]:.3f}")
            
            return True
            
        except mysql.connector.Error as e:
            print(f"❌ 更新数据库失败: {e}")
            return False
    
    def get_latest_info(self):
        """获取最新股票信息"""
        try:
            self.cursor.execute("""
                SELECT date, close, y_probability, inflow_ratio, Full_Y, E
                FROM stock_0002_hk 
                ORDER BY date DESC 
                LIMIT 1
            """)
            
            result = self.cursor.fetchone()
            if result:
                return {
                    'date': result[0].strftime('%Y-%m-%d'),
                    'close': result[1],
                    'y_probability': result[2],
                    'inflow_ratio': result[3],
                    'full_y': result[4],
                    'e_value': result[5]
                }
            return None
            
        except mysql.connector.Error as e:
            print(f"❌ 获取最新信息失败: {e}")
            return None
    
    def close_connection(self):
        """关闭数据库连接"""
        if self.cursor:
            self.cursor.close()
        if self.connection:
            self.connection.close()
        print("🔌 数据库连接已关闭")

def main():
    """主函数"""
    print("🎯 中电控股0002.HK数据库更新工具 (修复版)")
    print("=" * 55)
    print("📋 功能:")
    print("   📊 从Yahoo Finance获取最新数据")
    print("   🧮 计算技术指标 (适配现有表结构)")
    print("   💾 更新MySQL数据库")
    print("   📈 显示最新市场信息")
    
    updater = Stock0002HKDatabaseUpdaterFixed()
    
    try:
        # 连接数据库
        if not updater.connect_database():
            return
        
        # 检查表结构
        if not updater.check_table_structure():
            return
        
        # 获取最新数据
        if not updater.fetch_latest_data():
            return
        
        # 计算技术指标
        updater.calculate_technical_indicators()
        
        # 更新数据库
        if updater.update_database():
            # 显示最新信息
            latest_info = updater.get_latest_info()
            if latest_info:
                print(f"\n🎯 最新市场信息 ({latest_info['date']}):")
                print(f"   💰 收盘价: {latest_info['close']:.2f} 港元")
                print(f"   📊 Y概率: {latest_info['y_probability']:.3f}")
                print(f"   📊 资金流比率: {latest_info['inflow_ratio']:.3f}")
                print(f"   📊 Full_Y: {latest_info['full_y']:.3f}")
                print(f"   📊 E值: {latest_info['e_value']:.3f}")
            
            print(f"\n🎉 0002.HK数据库更新完成!")
            print(f"💡 数据已保存到MySQL数据库的stock_0002_hk表中")
        
    finally:
        updater.close_connection()

if __name__ == "__main__":
    main()
