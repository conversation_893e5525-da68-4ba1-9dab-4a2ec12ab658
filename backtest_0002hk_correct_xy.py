#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
中电控股0002.HK - 使用正确X值(MFI归一化)和Y值(Full_Y)的分段策略
===========================================================

验证您的分段规则在正确XY指标下的表现:
- X值 = MFI归一化 (14日资金流比率)
- Y值 = Full_Y (RSI + 价格动量组合)
- 观望: 0.333 < y < 0.4
- 做多: x>=0.5且y>=0.5 或 x<0.25且y<0.25
- 做空: x>0.45且y<0.35 或 x<0.45且y>0.35

作者: Cosmoon NG
"""

import yfinance as yf
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

class CorrectXYStrategy0002HK:
    def __init__(self):
        """初始化策略"""
        self.symbol = "0002.HK"  # 中电控股
        self.initial_capital = 10000.00
        self.monthly_addition = 3000.00

        # 止盈止损参数
        self.take_profit_long = 0.012
        self.stop_loss_long = 0.006
        self.take_profit_short = 0.012
        self.stop_loss_short = 0.006

        # 凯利公式参数
        self.win_probability = 0.5  # 预期胜率50%
        self.profit_loss_ratio = 2  # 盈亏比2:1 (1.2%止盈 vs 0.6%止损)
        self.kelly_fraction = (self.win_probability * (self.profit_loss_ratio + 1) - 1) / self.profit_loss_ratio
        self.kelly_fraction = max(0, min(self.kelly_fraction, 0.25))  # 限制在0-25%之间

        # 交易状态
        self.position = 0
        self.current_price = 0
        self.trades = []
        self.equity_curve = []

    def load_data(self):
        """加载0002.HK数据并更新yfinance数据库"""
        print(f"📊 加载{self.symbol}历史数据 (更新yfinance数据库)...")

        try:
            end_date = datetime.now()
            start_date = end_date - timedelta(days=25*365)

            # 强制更新yfinance数据库
            ticker = yf.Ticker(self.symbol)
            print(f"   🔄 正在从yfinance更新{self.symbol}数据库...")
            hist_data = ticker.history(start=start_date, end=end_date, auto_adjust=True, prepost=True)

            if len(hist_data) == 0:
                print(f"   ❌ 无法获取{self.symbol}数据，尝试备用方法...")
                # 备用方法：分段获取数据
                hist_data = yf.download(self.symbol, start=start_date, end=end_date, progress=False)

            self.df = pd.DataFrame({
                'date': hist_data.index,
                'open': hist_data['Open'],
                'high': hist_data['High'],
                'low': hist_data['Low'],
                'close': hist_data['Close'],
                'volume': hist_data['Volume']
            })

            self.df = self.df.dropna().sort_values('date').reset_index(drop=True)

            print(f"   ✅ 成功获取 {len(self.df)} 条数据")
            print(f"   📅 数据范围: {self.df['date'].min().strftime('%Y-%m-%d')} 至 {self.df['date'].max().strftime('%Y-%m-%d')}")
            print(f"   📈 价格范围: {self.df['close'].min():.2f} - {self.df['close'].max():.2f} 港元")

            return True

        except Exception as e:
            print(f"   ❌ 数据加载失败: {e}")
            return False

    def calculate_correct_indicators(self):
        """计算正确的X值(MFI归一化)和Y值(Full_Y)"""
        print(f"\n🧮 计算正确的X值和Y值指标...")

        # === 正确的X值计算 (MFI归一化) ===
        print("   计算X值 (MFI归一化)...")

        # 1. 计算典型价格和资金流
        self.df['typical_price'] = (self.df['high'] + self.df['low'] + self.df['close']) / 3
        self.df['money_flow'] = self.df['typical_price'] * self.df['volume']
        self.df['price_change'] = self.df['typical_price'].diff()

        # 2. 分离正负资金流
        self.df['positive_mf'] = np.where(self.df['price_change'] > 0, self.df['money_flow'], 0)
        self.df['negative_mf'] = np.where(self.df['price_change'] < 0, self.df['money_flow'], 0)

        # 3. 计算14日正负资金流总和
        period = 14
        self.df['positive_mf_14'] = self.df['positive_mf'].rolling(period).sum()
        self.df['negative_mf_14'] = self.df['negative_mf'].rolling(period).sum()

        # 4. 计算资金流比率
        self.df['money_flow_ratio'] = self.df['positive_mf_14'] / (self.df['negative_mf_14'] + 1e-10)

        # 5. 计算MFI (Money Flow Index)
        self.df['mfi'] = 100 - (100 / (1 + self.df['money_flow_ratio']))

        # 6. X值 = MFI归一化到0-1
        self.df['x_value'] = self.df['mfi'] / 100
        self.df['x_value'] = self.df['x_value'].fillna(0.5).clip(0.1, 0.9)

        # === 正确的Y值计算 (Full_Y) ===
        print("   计算Y值 (Full_Y)...")

        # 1. 计算RSI
        delta = self.df['close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
        rs = gain / loss
        self.df['rsi'] = 100 - (100 / (1 + rs))
        self.df['rsi'] = self.df['rsi'].fillna(50)

        # 2. 计算价格动量
        self.df['price_momentum'] = self.df['close'].pct_change(10)
        self.df['price_momentum'] = self.df['price_momentum'].fillna(0)

        # 3. Full_Y计算 (控制系数)
        self.df['y_value'] = (self.df['rsi'] / 100 + np.tanh(self.df['price_momentum'] * 5) + 1) / 2
        self.df['y_value'] = np.clip(self.df['y_value'], 0.1, 0.9)

        # === 计算E值 (用于参考) ===
        self.df['e_value'] = (8 * self.df['x_value'] - 3) * self.df['y_value'] - 3 * self.df['x_value'] + 1

        print(f"   ✅ 正确指标计算完成")
        print(f"   📊 X值(MFI归一化)范围: {self.df['x_value'].min():.3f} - {self.df['x_value'].max():.3f}")
        print(f"   📊 Y值(Full_Y)范围: {self.df['y_value'].min():.3f} - {self.df['y_value'].max():.3f}")
        print(f"   📊 E值范围: {self.df['e_value'].min():.3f} - {self.df['e_value'].max():.3f}")
        print(f"   📊 MFI范围: {self.df['mfi'].min():.1f} - {self.df['mfi'].max():.1f}")
        print(f"   📊 RSI范围: {self.df['rsi'].min():.1f} - {self.df['rsi'].max():.1f}")

    def get_trading_signal(self, x_val, y_val):
        """根据您的分段规则获取交易信号"""

        # 观望条件 (优先级最高)
        if 0.333 < y_val < 0.4:
            return "HOLD"   # 观望: 0.333 < y < 0.4

        # 做多条件
        if x_val >= 0.5 and y_val >= 0.5:
            return "LONG"   # x>=0.5 且 y>=0.5, 做多
        elif x_val < 0.25 and y_val < 0.25:
            return "LONG"   # x<0.25 且 y<0.25, 做多

        # 做空条件
        if x_val > 0.45 and y_val < 0.35:
            return "SHORT"  # x>0.45 且 y<0.35, 做空
        elif x_val < 0.45 and y_val > 0.35:
            return "SHORT"  # x<0.45 且 y>0.35, 做空

        return "HOLD"  # 其他情况持有

    def add_monthly_capital(self, date, total_capital, monthly_invested):
        """每月增加资金 - 真正的复利版本"""
        last_month = getattr(self, 'last_month', None)
        current_month = date.replace(day=1)

        if last_month is None or current_month > last_month:
            self.last_month = current_month
            # 复利关键: 追加资金加入总资本
            total_capital += self.monthly_addition
            monthly_invested += self.monthly_addition
            return total_capital, monthly_invested

        return total_capital, monthly_invested

    def run_backtest(self):
        """运行回测 - 正确XY指标 + 凯利公式 + 真正的复利逻辑"""
        print(f"\n💼 开始回测 (正确XY指标 + 凯利公式 + 真正的复利逻辑)...")

        # 真正的复利计算变量
        total_capital = self.initial_capital  # 总资本 (包括追加资金和累计盈利)
        monthly_invested = self.initial_capital  # 累计投入 (用于计算收益率)
        signal_stats = {"LONG": 0, "SHORT": 0, "HOLD": 0}

        for i in range(60, len(self.df)):  # 从第60天开始，确保指标稳定
            row = self.df.iloc[i]
            date = row['date']
            current_price = row['close']

            # 每月增加资金 (真正的复利逻辑)
            total_capital, monthly_invested = self.add_monthly_capital(date, total_capital, monthly_invested)

            # 获取交易信号
            signal = self.get_trading_signal(row['x_value'], row['y_value'])
            signal_stats[signal] += 1

            # 记录权益 (真正的复利版本)
            self.equity_curve.append({
                'date': date,
                'equity': total_capital,
                'monthly_invested': monthly_invested,
                'position': self.position,
                'signal': signal,
                'x_value': row['x_value'],
                'y_value': row['y_value'],
                'e_value': row['e_value']
            })

            # 观望信号 - 强制平仓
            if signal == "HOLD" and self.position != 0:
                if self.position == 1:  # 平多头仓
                    # 凯利公式: 只投资总资本的kelly_fraction比例
                    invested_amount = total_capital * self.kelly_fraction
                    profit = (current_price - self.current_price) / self.current_price * invested_amount
                    # 真正的复利: 盈利加入总资本
                    total_capital += profit
                    self.trades.append({
                        'date': date,
                        'type': 'long_exit_hold',
                        'entry_price': self.current_price,
                        'exit_price': current_price,
                        'profit': profit,
                        'invested_amount': invested_amount,
                        'total_capital': total_capital
                    })
                elif self.position == -1:  # 平空头仓
                    # 凯利公式: 只投资总资本的kelly_fraction比例
                    invested_amount = total_capital * self.kelly_fraction
                    profit = (self.current_price - current_price) / self.current_price * invested_amount
                    # 真正的复利: 盈利加入总资本
                    total_capital += profit
                    self.trades.append({
                        'date': date,
                        'type': 'short_exit_hold',
                        'entry_price': self.current_price,
                        'exit_price': current_price,
                        'profit': profit,
                        'invested_amount': invested_amount,
                        'total_capital': total_capital
                    })

                self.position = 0

            # 检查止盈止损 (700HK.py逻辑)
            if self.position != 0:
                if self.position == 1:  # 多头
                    profit_ratio = (row['high'] - self.current_price) / self.current_price
                    loss_ratio = (self.current_price - row['low']) / self.current_price

                    if profit_ratio >= self.take_profit_long:  # 止盈
                        exit_price = self.current_price * (1 + self.take_profit_long)
                        # 凯利公式: 只投资总资本的kelly_fraction比例
                        invested_amount = total_capital * self.kelly_fraction
                        profit = (exit_price - self.current_price) / self.current_price * invested_amount
                        # 真正的复利: 盈利加入总资本
                        total_capital += profit
                        self.position = 0
                        self.trades.append({
                            'date': date,
                            'type': 'long_exit_tp',
                            'entry_price': self.current_price,
                            'exit_price': exit_price,
                            'profit': profit,
                            'invested_amount': invested_amount,
                            'total_capital': total_capital
                        })
                    elif loss_ratio >= self.stop_loss_long:  # 止损
                        exit_price = self.current_price * (1 - self.stop_loss_long)
                        # 凯利公式: 只投资总资本的kelly_fraction比例
                        invested_amount = total_capital * self.kelly_fraction
                        loss = (exit_price - self.current_price) / self.current_price * invested_amount
                        # 真正的复利: 亏损也影响总资本
                        total_capital += loss
                        self.position = 0
                        self.trades.append({
                            'date': date,
                            'type': 'long_exit_sl',
                            'entry_price': self.current_price,
                            'exit_price': exit_price,
                            'profit': loss,
                            'invested_amount': invested_amount,
                            'total_capital': total_capital
                        })

                elif self.position == -1:  # 空头
                    profit_ratio = (self.current_price - row['low']) / self.current_price
                    loss_ratio = (row['high'] - self.current_price) / self.current_price

                    if profit_ratio >= self.take_profit_short:  # 止盈
                        exit_price = self.current_price * (1 - self.take_profit_short)
                        # 凯利公式: 只投资总资本的kelly_fraction比例
                        invested_amount = total_capital * self.kelly_fraction
                        profit = (self.current_price - exit_price) / self.current_price * invested_amount
                        # 真正的复利: 盈利加入总资本
                        total_capital += profit
                        self.position = 0
                        self.trades.append({
                            'date': date,
                            'type': 'short_exit_tp',
                            'entry_price': self.current_price,
                            'exit_price': exit_price,
                            'profit': profit,
                            'invested_amount': invested_amount,
                            'total_capital': total_capital
                        })
                    elif loss_ratio >= self.stop_loss_short:  # 止损
                        exit_price = self.current_price * (1 + self.stop_loss_short)
                        # 凯利公式: 只投资总资本的kelly_fraction比例
                        invested_amount = total_capital * self.kelly_fraction
                        loss = (exit_price - self.current_price) / self.current_price * invested_amount * -1
                        # 真正的复利: 亏损也影响总资本
                        total_capital += loss
                        self.position = 0
                        self.trades.append({
                            'date': date,
                            'type': 'short_exit_sl',
                            'entry_price': self.current_price,
                            'exit_price': exit_price,
                            'profit': loss,
                            'invested_amount': invested_amount,
                            'total_capital': total_capital
                        })

            # 开仓信号判断
            if self.position == 0:
                if signal == "LONG":
                    self.position = 1
                    self.current_price = current_price
                    invested_amount = total_capital * self.kelly_fraction
                    self.trades.append({
                        'date': date,
                        'type': 'long_entry',
                        'price': self.current_price,
                        'total_capital': total_capital,
                        'invested_amount': invested_amount,
                        'kelly_fraction': self.kelly_fraction,
                        'signal': f"X={row['x_value']:.3f},Y={row['y_value']:.3f},E={row['e_value']:.3f}"
                    })

                elif signal == "SHORT":
                    self.position = -1
                    self.current_price = current_price
                    invested_amount = total_capital * self.kelly_fraction
                    self.trades.append({
                        'date': date,
                        'type': 'short_entry',
                        'price': self.current_price,
                        'total_capital': total_capital,
                        'invested_amount': invested_amount,
                        'kelly_fraction': self.kelly_fraction,
                        'signal': f"X={row['x_value']:.3f},Y={row['y_value']:.3f},E={row['e_value']:.3f}"
                    })

        self.final_capital = total_capital
        self.total_invested = monthly_invested
        self.signal_stats = signal_stats
        print(f"   ✅ 回测完成！最终资金：{self.final_capital:,.2f}")

    def analyze_results(self):
        """分析回测结果"""
        print(f"\n📈 中电控股0002.HK - 正确XY指标分段策略回测结果")
        print("=" * 60)

        # 基础统计
        total_days = (self.df['date'].max() - self.df['date'].min()).days
        total_years = total_days / 365

        print(f"📊 基础信息:")
        print(f"   标的: {self.symbol} (中电控股)")
        print(f"   回测期间: {total_years:.1f}年")
        print(f"   策略: 正确XY指标+凯利公式分段策略")
        print(f"   凯利比例: {self.kelly_fraction:.1%} (每次投资总资本的{self.kelly_fraction:.1%})")

        # 价格表现
        start_price = self.df['close'].iloc[0]
        end_price = self.df['close'].iloc[-1]
        price_return = (end_price - start_price) / start_price * 100

        print(f"\n💰 价格表现:")
        print(f"   起始价格: {start_price:.2f}港元")
        print(f"   最终价格: {end_price:.2f}港元")
        print(f"   价格涨幅: {price_return:.2f}%")

        # 策略表现 (真正的复利版本)
        net_profit = self.final_capital - self.total_invested
        total_return = net_profit / self.total_invested * 100
        annual_return = ((self.final_capital / self.total_invested) ** (1/total_years) - 1) * 100

        print(f"\n🚀 策略表现:")
        print(f"   总投入: {self.total_invested:,.0f}港元")
        print(f"   最终资金: {self.final_capital:,.0f}港元")
        print(f"   净收益: {net_profit:,.0f}港元")
        print(f"   总收益率: {total_return:.2f}%")
        print(f"   年化收益率: {annual_return:.2f}%")

        # 信号分布统计
        total_signals = sum(self.signal_stats.values())
        print(f"\n📊 信号分布:")
        for signal, count in self.signal_stats.items():
            percentage = count / total_signals * 100
            print(f"   {signal}: {count}次 ({percentage:.1f}%)")

        # 交易统计
        trades_df = pd.DataFrame(self.trades)
        if len(trades_df) > 0:
            entry_trades = trades_df[trades_df['type'].str.contains('entry')]
            exit_trades = trades_df[trades_df['type'].str.contains('exit')]
            profit_trades = exit_trades[exit_trades['profit'] > 0]

            print(f"\n📋 交易统计:")
            print(f"   总交易次数: {len(entry_trades)}")
            print(f"   盈利交易: {len(profit_trades)}")
            print(f"   亏损交易: {len(exit_trades) - len(profit_trades)}")
            print(f"   胜率: {len(profit_trades)/len(exit_trades)*100:.2f}%" if len(exit_trades) > 0 else "   胜率: 0%")

            if len(profit_trades) > 0:
                avg_profit = profit_trades['profit'].mean()
                print(f"   平均盈利: {avg_profit:,.0f}港元")

            loss_trades = exit_trades[exit_trades['profit'] <= 0]
            if len(loss_trades) > 0:
                avg_loss = loss_trades['profit'].mean()
                print(f"   平均亏损: {avg_loss:,.0f}港元")

                if len(profit_trades) > 0:
                    profit_loss_ratio = abs(avg_profit / avg_loss)
                    print(f"   盈亏比: {profit_loss_ratio:.2f}")

        # 买入持有对比
        buy_hold_shares = int(self.initial_capital / start_price)
        buy_hold_final = buy_hold_shares * end_price
        buy_hold_return = (buy_hold_final - self.initial_capital) / self.initial_capital * 100

        print(f"\n🆚 买入持有对比:")
        print(f"   买入持有收益: {buy_hold_return:.2f}%")
        print(f"   策略收益: {total_return:.2f}%")
        print(f"   策略优势: {total_return - buy_hold_return:.2f}%")

        # 指标分析
        print(f"\n🔍 正确XY指标分析:")
        print(f"   X值(MFI)平均: {self.df['x_value'].mean():.3f}")
        print(f"   Y值(Full_Y)平均: {self.df['y_value'].mean():.3f}")
        print(f"   E值平均: {self.df['e_value'].mean():.3f}")
        print(f"   MFI平均: {self.df['mfi'].mean():.1f}")
        print(f"   RSI平均: {self.df['rsi'].mean():.1f}")

        # 条件触发分析
        long_entries = trades_df[trades_df['type'] == 'long_entry']
        short_entries = trades_df[trades_df['type'] == 'short_entry']

        if len(long_entries) > 0:
            print(f"\n📈 多头交易:")
            print(f"   多头开仓: {len(long_entries)}次")
            print(f"   多头示例: {long_entries.iloc[0]['signal'] if len(long_entries) > 0 else 'N/A'}")

        if len(short_entries) > 0:
            print(f"\n📉 空头交易:")
            print(f"   空头开仓: {len(short_entries)}次")
            print(f"   空头示例: {short_entries.iloc[0]['signal'] if len(short_entries) > 0 else 'N/A'}")

def main():
    """主函数"""
    print("🎯 中电控股0002.HK - 正确XY指标+凯利公式+真正复利策略验证")
    print("=" * 60)
    print("📋 完整策略框架:")
    print("   📊 X值 = MFI归一化 (14日资金流比率)")
    print("   📊 Y值 = Full_Y (RSI + 价格动量组合)")
    print("   💰 凯利公式: f = (p×(b+1)-1)/b = (0.5×3-1)/2 = 0.25 (25%)")
    print("   💎 真正复利: 盈利加入总资本，动态增长投资额")
    print("   🔄 观望: 0.333 < y < 0.4")
    print("   📈 做多: x>=0.5且y>=0.5 或 x<0.25且y<0.25")
    print("   📉 做空: x>0.45且y<0.35 或 x<0.45且y>0.35")
    print("   ⚠️  观望时强制平仓，每次投资总资本的25%，盈利再投资")

    backtest = CorrectXYStrategy0002HK()

    if not backtest.load_data():
        return

    backtest.calculate_correct_indicators()
    backtest.run_backtest()
    backtest.analyze_results()

    print(f"\n🎉 0002.HK正确XY指标分段策略验证完成!")
    print(f"📊 这验证了正确XY指标定义的重要性")
    print(f"💡 X值=MFI归一化, Y值=Full_Y 是策略成功的关键")

if __name__ == "__main__":
    main()
