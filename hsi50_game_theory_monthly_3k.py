#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
HSI50博弈论回测系统 - 30K初始+每月3K资金20年历史
==============================================

策略特点：
- 很少持仓，极其保守
- 严格回归中线策略
- 凯利公式精确计算
- 博弈论方法 (Game Theory)
- 复利计算
- 每月定投3K港币
- 20年历史数据

博弈论核心：
- 市场参与者博弈分析
- 期望值计算 E = p*W - (1-p)*L
- 只在正期望值时交易
- 考虑对手策略和市场情绪

作者: Cosmoon NG
日期: 2025年7月
"""

import pandas as pd
import yfinance as yf
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

class HSI50GameTheoryMonthly3K:
    def __init__(self):
        """初始化HSI50博弈论+每月3K回测系统"""
        self.symbol = "^HSI"  # 恒生指数
        self.initial_capital = 30000  # 30K港币初始资金
        self.monthly_investment = 3000  # 每月定投3K港币
        self.data = None
        
        # 博弈论策略参数
        self.strategy_params = {
            # 博弈论参数
            'min_expected_value': 0.15,     # 最小期望值15%
            'win_probability_threshold': 0.7, # 胜率阈值70%
            'risk_reward_ratio': 3.0,       # 风险回报比1:3
            'market_sentiment_weight': 0.3,  # 市场情绪权重
            'opponent_strategy_weight': 0.2, # 对手策略权重
            
            # 严格回归中线参数
            'median_period': 120,           # 回归中线计算周期120天
            'deviation_threshold': 0.08,    # 偏离阈值8%
            'extreme_deviation': 0.15,     # 极端偏离阈值15%
            'trend_confirmation_days': 5,   # 趋势确认天数
            
            # 凯利公式参数 - 博弈论优化
            'kelly_multiplier': 0.5,        # 凯利系数乘数（保守）
            'max_position_ratio': 0.08,     # 最大仓位8%
            'max_positions': 1,             # 最多1个仓位
            
            # 持仓参数 - 很少持仓
            'min_holding_days': 90,         # 最少持有90天
            'max_holding_days': 365,        # 最多持有365天
            'position_cooldown': 60,        # 平仓后60天冷却期
            
            # 止盈止损参数 - 博弈论优化
            'take_profit': 0.25,            # 止盈25%
            'stop_loss': 0.08,              # 止损8%
            'trailing_stop': 0.12,          # 移动止损12%
            
            # 交易参数
            'transaction_cost': 30,         # 每笔交易成本30港币
            'compound_interest': True,      # 复利计算
        }
        
        self.trades = []
        self.monthly_investments = []
        self.daily_portfolio = []
        self.current_positions = []
        self.current_capital = self.initial_capital
        self.last_trade_date = None
        self.total_invested = self.initial_capital
        self.game_theory_signals = []
    
    def fetch_hsi_data(self):
        """获取恒生指数20年历史数据"""
        print("📈 获取恒生指数20年历史数据...")
        
        try:
            # 获取20年数据
            end_date = datetime.now()
            start_date = end_date - timedelta(days=20*365)
            
            ticker = yf.Ticker(self.symbol)
            self.data = ticker.history(start=start_date, end=end_date)
            
            if self.data.empty:
                print("❌ 数据获取失败：返回空数据")
                return False
            
            self.data.reset_index(inplace=True)
            self.data.columns = [col.lower() for col in self.data.columns]
            
            print(f"✅ 成功获取恒生指数数据:")
            print(f"   • 数据期间: {self.data['date'].min().strftime('%Y-%m-%d')} 至 {self.data['date'].max().strftime('%Y-%m-%d')}")
            print(f"   • 总记录数: {len(self.data):,} 天")
            
            return True
            
        except Exception as e:
            print(f"❌ 获取数据失败: {e}")
            return False
    
    def calculate_indicators(self):
        """计算技术指标和博弈论参数"""
        print("📊 计算技术指标和博弈论策略参数...")
        
        # 移动平均线
        self.data['ma_20'] = self.data['close'].rolling(window=20).mean()
        self.data['ma_60'] = self.data['close'].rolling(window=60).mean()
        self.data['ma_120'] = self.data['close'].rolling(window=120).mean()
        
        # 严格回归中线计算
        self.data['median_price'] = self.data['close'].rolling(window=self.strategy_params['median_period']).median()
        self.data['price_deviation'] = (self.data['close'] - self.data['median_price']) / self.data['median_price']
        
        # 波动率计算
        self.data['volatility'] = self.data['close'].pct_change().rolling(window=20).std()
        self.data['volatility_percentile'] = self.data['volatility'].rolling(window=252).rank(pct=True)
        
        # RSI
        delta = self.data['close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
        rs = gain / loss
        self.data['rsi'] = 100 - (100 / (1 + rs))
        
        # 成交量指标
        self.data['volume_ma'] = self.data['volume'].rolling(window=20).mean()
        self.data['volume_ratio'] = self.data['volume'] / self.data['volume_ma']
        
        # 市场情绪指标
        self.data['price_momentum'] = self.data['close'].pct_change(10)
        self.data['volume_momentum'] = self.data['volume'].pct_change(10)
        
        # 博弈论市场情绪计算
        sentiment_factors = [
            self.data['rsi'] / 100 - 0.5,  # RSI偏离中性
            np.tanh(self.data['price_momentum'] * 10),  # 价格动量
            np.tanh(self.data['volume_momentum']),  # 成交量动量
            (self.data['volatility_percentile'] - 0.5) * 2  # 波动率位置
        ]
        
        self.data['market_sentiment'] = np.mean(sentiment_factors, axis=0)
        self.data['market_sentiment'] = np.clip(self.data['market_sentiment'], -1, 1)
        
        # 对手策略分析（基于技术指标反向思维）
        self.data['opponent_bullish'] = (
            (self.data['rsi'] > 70) |  # 超买
            (self.data['close'] > self.data['ma_20'] * 1.1) |  # 价格远高于均线
            (self.data['volume_ratio'] > 2)  # 成交量异常
        ).astype(float)
        
        self.data['opponent_bearish'] = (
            (self.data['rsi'] < 30) |  # 超卖
            (self.data['close'] < self.data['ma_20'] * 0.9) |  # 价格远低于均线
            (self.data['volatility_percentile'] > 0.8)  # 高波动率
        ).astype(float)
        
        # 趋势确认
        self.data['strong_uptrend'] = (
            (self.data['ma_20'] > self.data['ma_60']) & 
            (self.data['ma_60'] > self.data['ma_120']) &
            (self.data['close'] > self.data['ma_20']) &
            (self.data['price_momentum'] > 0)
        )
        
        self.data['strong_downtrend'] = (
            (self.data['ma_20'] < self.data['ma_60']) & 
            (self.data['ma_60'] < self.data['ma_120']) &
            (self.data['close'] < self.data['ma_20']) &
            (self.data['price_momentum'] < 0)
        )
        
        print("✅ 博弈论指标计算完成")
    
    def calculate_game_theory_expected_value(self, row):
        """计算博弈论期望值"""
        deviation = row['price_deviation']
        market_sentiment = row['market_sentiment']
        opponent_bullish = row['opponent_bullish']
        opponent_bearish = row['opponent_bearish']
        volatility_percentile = row['volatility_percentile']
        
        # 基础胜率计算（基于回归中线）
        if abs(deviation) < self.strategy_params['deviation_threshold']:
            base_win_prob = 0.5  # 中性
        elif deviation > self.strategy_params['extreme_deviation']:
            # 价格远高于中线，做空胜率高
            base_win_prob = 0.7 + min(0.2, abs(deviation) - self.strategy_params['extreme_deviation'])
        elif deviation < -self.strategy_params['extreme_deviation']:
            # 价格远低于中线，做多胜率高
            base_win_prob = 0.7 + min(0.2, abs(deviation) - self.strategy_params['extreme_deviation'])
        else:
            base_win_prob = 0.5 + abs(deviation) * 2
        
        # 市场情绪调整
        sentiment_adjustment = market_sentiment * self.strategy_params['market_sentiment_weight']
        
        # 对手策略调整（反向思维）
        if opponent_bullish > 0.5:
            # 大多数人看涨，我们看跌
            opponent_adjustment = -self.strategy_params['opponent_strategy_weight']
        elif opponent_bearish > 0.5:
            # 大多数人看跌，我们看涨
            opponent_adjustment = self.strategy_params['opponent_strategy_weight']
        else:
            opponent_adjustment = 0
        
        # 波动率调整
        volatility_adjustment = (volatility_percentile - 0.5) * 0.1
        
        # 最终胜率
        win_probability = base_win_prob + sentiment_adjustment + opponent_adjustment + volatility_adjustment
        win_probability = np.clip(win_probability, 0.1, 0.9)
        
        # 期望收益和损失
        expected_win = self.strategy_params['take_profit']
        expected_loss = self.strategy_params['stop_loss']
        
        # 博弈论期望值 E = p*W - (1-p)*L
        expected_value = win_probability * expected_win - (1 - win_probability) * expected_loss
        
        return expected_value, win_probability
    
    def calculate_kelly_position(self, win_prob, win_amount, loss_amount):
        """计算凯利公式仓位"""
        # 凯利公式: f = (bp - q) / b
        # 其中 b = 赔率, p = 胜率, q = 败率
        b = win_amount / loss_amount
        p = win_prob
        q = 1 - p
        
        kelly_fraction = (b * p - q) / b
        
        # 应用保守乘数
        kelly_fraction *= self.strategy_params['kelly_multiplier']
        
        # 限制最大仓位
        return max(0, min(kelly_fraction, self.strategy_params['max_position_ratio']))
    
    def get_game_theory_signal(self, row):
        """获取博弈论交易信号"""
        deviation = row['price_deviation']
        strong_uptrend = row['strong_uptrend']
        strong_downtrend = row['strong_downtrend']
        
        # 检查冷却期
        if self.last_trade_date:
            days_since_last_trade = (row['date'] - self.last_trade_date).days
            if days_since_last_trade < self.strategy_params['position_cooldown']:
                return 'COOLDOWN', 0, 0
        
        # 计算博弈论期望值
        expected_value, win_probability = self.calculate_game_theory_expected_value(row)
        
        # 记录信号分析
        self.game_theory_signals.append({
            'date': row['date'].strftime('%Y-%m-%d'),  # 转换为字符串避免时区问题
            'expected_value': expected_value,
            'win_probability': win_probability,
            'deviation': deviation,
            'signal': 'HOLD'
        })
        
        # 只在期望值足够高且胜率足够时交易
        if (expected_value > self.strategy_params['min_expected_value'] and 
            win_probability > self.strategy_params['win_probability_threshold']):
            
            # 极端做多信号
            if (deviation < -self.strategy_params['extreme_deviation'] and 
                strong_uptrend):
                self.game_theory_signals[-1]['signal'] = 'GAME_BUY'
                return 'GAME_BUY', expected_value, win_probability

            # 极端做空信号
            elif (deviation > self.strategy_params['extreme_deviation'] and
                  strong_downtrend):
                self.game_theory_signals[-1]['signal'] = 'GAME_SELL'
                return 'GAME_SELL', expected_value, win_probability
        
        # 其他情况：很少持仓
        return 'MINIMAL_HOLD', expected_value, win_probability

    def check_exit_conditions(self, position, current_price, current_date):
        """检查退出条件 - 博弈论优化"""
        entry_price = position['entry_price']
        direction = position['direction']
        entry_date = position['entry_date']
        highest_price = position.get('highest_price', entry_price)
        lowest_price = position.get('lowest_price', entry_price)

        # 更新最高最低价
        if current_price > highest_price:
            position['highest_price'] = current_price
            highest_price = current_price
        if current_price < lowest_price:
            position['lowest_price'] = current_price
            lowest_price = current_price

        # 计算持仓天数
        holding_days = (current_date - entry_date).days

        # 计算收益率
        if direction == 'LONG':
            profit_pct = (current_price - entry_price) / entry_price
            # 移动止损
            trailing_stop_price = highest_price * (1 - self.strategy_params['trailing_stop'])
        else:  # SHORT
            profit_pct = (entry_price - current_price) / entry_price
            # 移动止损
            trailing_stop_price = lowest_price * (1 + self.strategy_params['trailing_stop'])

        # 时间限制
        if holding_days >= self.strategy_params['max_holding_days']:
            return True, '时间止损', profit_pct

        # 最少持有期内不退出（除非巨亏）
        if holding_days < self.strategy_params['min_holding_days']:
            if profit_pct < -self.strategy_params['stop_loss'] * 1.5:
                return True, '巨亏止损', profit_pct
            else:
                return False, '', profit_pct

        # 止盈
        if profit_pct >= self.strategy_params['take_profit']:
            return True, '止盈', profit_pct

        # 止损
        elif profit_pct <= -self.strategy_params['stop_loss']:
            return True, '止损', profit_pct

        # 移动止损
        elif direction == 'LONG' and current_price <= trailing_stop_price:
            return True, '移动止损', profit_pct
        elif direction == 'SHORT' and current_price >= trailing_stop_price:
            return True, '移动止损', profit_pct

        return False, '', profit_pct

    def backtest_game_theory_strategy(self):
        """执行博弈论+每月3K策略回测"""
        print("\n🚀 开始博弈论+每月3K策略回测...")
        print("="*60)
        print(f"💰 初始资金: {self.initial_capital:,} 港币")
        print(f"📅 每月定投: {self.monthly_investment:,} 港币")
        print(f"📊 策略: 博弈论 + 很少持仓 + 严格回归中线 + 凯利公式")
        print(f"🎯 期望值阈值: {self.strategy_params['min_expected_value']*100}%")
        print(f"🎲 胜率阈值: {self.strategy_params['win_probability_threshold']*100}%")
        print(f"📈 风险回报比: 1:{self.strategy_params['risk_reward_ratio']}")
        print(f"⏰ 持仓时间: {self.strategy_params['min_holding_days']}-{self.strategy_params['max_holding_days']}天")
        print(f"🎯 止盈: {self.strategy_params['take_profit']*100}%, 止损: {self.strategy_params['stop_loss']*100}%")
        print("="*60)

        # 统计变量
        total_trades = 0
        winning_trades = 0
        losing_trades = 0
        minimal_hold_days = 0

        # 记录每月定投
        last_investment_month = None

        # 跳过前120天用于指标计算
        for i in range(120, len(self.data)):
            row = self.data.iloc[i]
            date = row['date']
            price = row['close']

            # 检查是否需要每月定投
            current_month = date.strftime('%Y-%m')
            if last_investment_month != current_month:
                self.current_capital += self.monthly_investment
                self.total_invested += self.monthly_investment
                self.monthly_investments.append({
                    'date': date.strftime('%Y-%m-%d'),
                    'amount': self.monthly_investment,
                    'total_invested': self.total_invested,
                    'current_capital': self.current_capital
                })
                last_investment_month = current_month

            # 获取博弈论交易信号
            signal, expected_value, win_probability = self.get_game_theory_signal(row)

            # 检查现有持仓的退出条件
            positions_to_close = []
            for j, position in enumerate(self.current_positions):
                should_exit, exit_reason, profit_pct = self.check_exit_conditions(position, price, date)

                if should_exit:
                    # 计算实际盈亏
                    if position['direction'] == 'LONG':
                        price_diff = price - position['entry_price']
                    else:  # SHORT
                        price_diff = position['entry_price'] - price

                    gross_profit = price_diff * position['shares']
                    net_profit = gross_profit - self.strategy_params['transaction_cost']

                    # 复利计算
                    if self.strategy_params['compound_interest']:
                        self.current_capital += net_profit

                    # 记录交易
                    trade_record = {
                        'entry_date': position['entry_date'].strftime('%Y-%m-%d'),
                        'exit_date': date.strftime('%Y-%m-%d'),
                        'direction': position['direction'],
                        'entry_price': round(position['entry_price'], 2),
                        'exit_price': round(price, 2),
                        'shares': round(position['shares'], 2),
                        'investment': round(position['investment'], 2),
                        'gross_profit': round(gross_profit, 2),
                        'transaction_cost': self.strategy_params['transaction_cost'],
                        'net_profit': round(net_profit, 2),
                        'profit_pct': round(profit_pct * 100, 2),
                        'holding_days': (date - position['entry_date']).days,
                        'capital_after': round(self.current_capital, 2),
                        'expected_value': round(position.get('expected_value', 0), 3),
                        'win_probability': round(position.get('win_probability', 0), 3),
                        'exit_reason': exit_reason
                    }

                    self.trades.append(trade_record)

                    if net_profit > 0:
                        winning_trades += 1
                    else:
                        losing_trades += 1

                    total_trades += 1
                    self.last_trade_date = date
                    positions_to_close.append(j)

            # 移除已平仓的持仓
            for j in sorted(positions_to_close, reverse=True):
                del self.current_positions[j]

            # 根据博弈论信号决定开仓
            if signal in ['MINIMAL_HOLD', 'COOLDOWN']:
                minimal_hold_days += 1
                action = '很少持仓' if signal == 'MINIMAL_HOLD' else '冷却期'
            elif len(self.current_positions) < self.strategy_params['max_positions']:
                # 计算凯利公式仓位
                kelly_position = self.calculate_kelly_position(
                    win_probability,
                    self.strategy_params['take_profit'],
                    self.strategy_params['stop_loss']
                )

                if kelly_position > 0:
                    # 计算投资金额
                    investment_amount = self.current_capital * kelly_position

                    if investment_amount >= self.strategy_params['transaction_cost'] * 2:
                        # 计算股数
                        shares = (investment_amount - self.strategy_params['transaction_cost']) / price

                        # 创建持仓记录
                        if signal == 'GAME_BUY':
                            direction = 'LONG'
                            action = '博弈买涨'
                        else:  # GAME_SELL
                            direction = 'SHORT'
                            action = '博弈买跌'

                        position = {
                            'entry_date': date,
                            'entry_price': price,
                            'shares': shares,
                            'direction': direction,
                            'investment': investment_amount,
                            'expected_value': expected_value,
                            'win_probability': win_probability,
                            'highest_price': price,
                            'lowest_price': price
                        }

                        self.current_positions.append(position)
                        self.last_trade_date = date
                    else:
                        action = '资金不足'
                        minimal_hold_days += 1
                else:
                    action = '凯利系数不足'
                    minimal_hold_days += 1
            else:
                action = '仓位已满'
                minimal_hold_days += 1

            # 记录每日组合价值
            position_value = 0
            for position in self.current_positions:
                if position['direction'] == 'LONG':
                    unrealized_profit = (price - position['entry_price']) * position['shares']
                else:  # SHORT
                    unrealized_profit = (position['entry_price'] - price) * position['shares']
                position_value += position['investment'] + unrealized_profit

            total_value = self.current_capital + position_value

            self.daily_portfolio.append({
                'date': date.strftime('%Y-%m-%d'),
                'price': price,
                'capital': self.current_capital,
                'position_value': position_value,
                'total_value': total_value,
                'total_invested': self.total_invested,
                'action': action,
                'signal': signal,
                'expected_value': expected_value,
                'win_probability': win_probability,
                'deviation': row['price_deviation'],
                'median_price': row['median_price'],
                'market_sentiment': row['market_sentiment'],
                'positions_count': len(self.current_positions)
            })

        print(f"\n✅ 博弈论+每月3K策略回测完成!")
        print(f"📊 总交易次数: {total_trades}")
        print(f"📈 盈利次数: {winning_trades}")
        print(f"📉 亏损次数: {losing_trades}")
        print(f"⏸️ 很少持仓天数: {minimal_hold_days}")
        print(f"💰 总投入资金: {self.total_invested:,} 港币")
        if total_trades > 0:
            print(f"🎯 胜率: {winning_trades/total_trades*100:.1f}%")

        return (pd.DataFrame(self.trades),
                pd.DataFrame(self.monthly_investments),
                pd.DataFrame(self.daily_portfolio),
                pd.DataFrame(self.game_theory_signals))

    def analyze_results(self, trades_df, monthly_df, daily_df, signals_df):
        """分析博弈论回测结果"""
        print("\n📊 博弈论+每月3K策略回测结果分析")
        print("=" * 60)

        # 基本统计
        total_trades = len(trades_df)
        final_capital = self.current_capital
        final_total_value = daily_df['total_value'].iloc[-1] if len(daily_df) > 0 else self.initial_capital
        total_invested = self.total_invested
        net_return = final_total_value - total_invested
        net_return_rate = (net_return / total_invested) * 100

        # 年化收益率
        years = 20
        annual_return_rate = ((final_total_value / total_invested) ** (1/years) - 1) * 100

        # 定投统计
        total_monthly_investments = len(monthly_df)
        monthly_investment_total = monthly_df['amount'].sum() if len(monthly_df) > 0 else 0

        # 持仓统计
        total_days = len(daily_df)
        minimal_hold_count = len(daily_df[daily_df['action'] == '很少持仓'])
        minimal_hold_rate = minimal_hold_count / total_days * 100

        # 博弈论信号统计
        if len(signals_df) > 0:
            avg_expected_value = signals_df['expected_value'].mean()
            avg_win_probability = signals_df['win_probability'].mean()
            positive_ev_signals = len(signals_df[signals_df['expected_value'] > 0])
            high_prob_signals = len(signals_df[signals_df['win_probability'] > 0.7])
            actual_trades = len(signals_df[signals_df['signal'].isin(['GAME_BUY', 'GAME_SELL'])])
        else:
            avg_expected_value = 0
            avg_win_probability = 0
            positive_ev_signals = 0
            high_prob_signals = 0
            actual_trades = 0

        if total_trades > 0:
            winning_trades = len(trades_df[trades_df['net_profit'] > 0])
            win_rate = winning_trades / total_trades * 100
            max_profit = trades_df['net_profit'].max()
            max_loss = trades_df['net_profit'].min()
            avg_profit = trades_df['net_profit'].mean()
            avg_holding_days = trades_df['holding_days'].mean()
            total_trading_profit = trades_df['net_profit'].sum()
            avg_expected_value_trades = trades_df['expected_value'].mean()
            avg_win_prob_trades = trades_df['win_probability'].mean()

            # 按方向分析
            direction_stats = trades_df.groupby('direction').agg({
                'net_profit': ['count', 'sum', 'mean'],
                'profit_pct': 'mean',
                'holding_days': 'mean',
                'expected_value': 'mean',
                'win_probability': 'mean'
            }).round(3)

            # 按退出原因分析
            exit_reason_stats = trades_df.groupby('exit_reason').agg({
                'net_profit': ['count', 'sum', 'mean'],
                'profit_pct': 'mean'
            }).round(2)
        else:
            winning_trades = 0
            win_rate = 0
            max_profit = 0
            max_loss = 0
            avg_profit = 0
            avg_holding_days = 0
            total_trading_profit = 0
            avg_expected_value_trades = 0
            avg_win_prob_trades = 0
            direction_stats = pd.DataFrame()
            exit_reason_stats = pd.DataFrame()

        # 输出结果
        print(f"💰 资金统计:")
        print(f"• 初始资金: {self.initial_capital:,} 港元")
        print(f"• 总投入资金: {total_invested:,} 港元")
        print(f"• 最终现金: {final_capital:,.0f} 港元")
        print(f"• 最终总价值: {final_total_value:,.0f} 港元")
        print(f"• 净收益: {net_return:,.0f} 港元")
        print(f"• 净收益率: {net_return_rate:.2f}%")
        print(f"• 年化收益率: {annual_return_rate:.2f}%")

        print(f"\n📅 定投统计:")
        print(f"• 定投次数: {total_monthly_investments}")
        print(f"• 定投总额: {monthly_investment_total:,} 港元")
        print(f"• 月均定投: {self.monthly_investment:,} 港元")

        print(f"\n🎲 博弈论信号统计:")
        print(f"• 总信号数: {len(signals_df)}")
        print(f"• 平均期望值: {avg_expected_value:.3f}")
        print(f"• 平均胜率: {avg_win_probability:.3f}")
        print(f"• 正期望值信号: {positive_ev_signals}")
        print(f"• 高胜率信号: {high_prob_signals}")
        print(f"• 实际交易信号: {actual_trades}")

        print(f"\n⏸️ 持仓统计:")
        print(f"• 总天数: {total_days}")
        print(f"• 很少持仓天数: {minimal_hold_count}")
        print(f"• 很少持仓比例: {minimal_hold_rate:.1f}%")

        if total_trades > 0:
            print(f"\n📈 交易统计:")
            print(f"• 总交易次数: {total_trades}")
            print(f"• 盈利次数: {winning_trades}")
            print(f"• 实际胜率: {win_rate:.1f}%")
            print(f"• 最大单笔盈利: {max_profit:,.0f} 港元")
            print(f"• 最大单笔亏损: {max_loss:,.0f} 港元")
            print(f"• 平均每笔盈亏: {avg_profit:,.0f} 港元")
            print(f"• 平均持仓天数: {avg_holding_days:.1f} 天")
            print(f"• 交易总盈亏: {total_trading_profit:,.0f} 港元")
            print(f"• 交易平均期望值: {avg_expected_value_trades:.3f}")
            print(f"• 交易平均预测胜率: {avg_win_prob_trades:.3f}")

            print(f"\n📊 方向分析:")
            print(direction_stats)

            print(f"\n📊 退出原因分析:")
            print(exit_reason_stats)

            # 博弈论验证
            print(f"\n🎯 博弈论策略验证:")
            print(f"• 预测胜率 vs 实际胜率: {avg_win_prob_trades:.1%} vs {win_rate/100:.1%}")
            if avg_expected_value_trades > 0:
                print(f"• 期望值预测: {avg_expected_value_trades:.3f} (正期望值)")
            else:
                print(f"• 期望值预测: {avg_expected_value_trades:.3f} (负期望值)")

            actual_risk_reward = abs(max_profit / max_loss) if max_loss != 0 else 0
            print(f"• 实际风险回报比: 1:{actual_risk_reward:.2f}")
            print(f"• 目标风险回报比: 1:{self.strategy_params['risk_reward_ratio']:.2f}")

        # 保存到Excel
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"HSI50博弈论每月3K回测结果_{timestamp}.xlsx"

        with pd.ExcelWriter(filename, engine='openpyxl') as writer:
            if len(trades_df) > 0:
                trades_df.to_excel(writer, sheet_name='交易记录', index=False)
            if len(monthly_df) > 0:
                monthly_df.to_excel(writer, sheet_name='每月定投', index=False)
            daily_df.to_excel(writer, sheet_name='每日数据', index=False)
            if len(signals_df) > 0:
                signals_df.to_excel(writer, sheet_name='博弈论信号', index=False)

            # 汇总统计
            summary_data = {
                '项目': ['初始资金(港元)', '总投入资金(港元)', '最终现金(港元)', '最终总价值(港元)',
                        '净收益(港元)', '净收益率(%)', '年化收益率(%)',
                        '总交易次数', '盈利次数', '实际胜率(%)',
                        '很少持仓天数', '很少持仓比例(%)', '平均持仓天数',
                        '定投次数', '定投总额(港元)', '博弈论信号数', '实际交易信号'],
                '数值': [self.initial_capital, total_invested, round(final_capital, 0), round(final_total_value, 0),
                        round(net_return, 0), round(net_return_rate, 2), round(annual_return_rate, 2),
                        total_trades, winning_trades, round(win_rate, 1),
                        minimal_hold_count, round(minimal_hold_rate, 1),
                        round(avg_holding_days, 1) if total_trades > 0 else 0,
                        total_monthly_investments, monthly_investment_total, len(signals_df), actual_trades]
            }
            pd.DataFrame(summary_data).to_excel(writer, sheet_name='汇总统计', index=False)

            if len(direction_stats) > 0:
                direction_stats.to_excel(writer, sheet_name='方向分析')
            if len(exit_reason_stats) > 0:
                exit_reason_stats.to_excel(writer, sheet_name='退出原因分析')

        print(f"\n✅ 详细结果已保存至: {filename}")
        return filename

def main():
    """主函数"""
    print("🏢 HSI50博弈论+每月3K策略回测系统")
    print("=" * 60)
    print("💰 初始资金: 30,000港元")
    print("📅 每月定投: 3,000港元")
    print("📊 分析周期: 20年历史数据")
    print("🎯 策略: 博弈论 + 很少持仓 + 严格回归中线 + 凯利公式")
    print("🎲 博弈论核心:")
    print("   • 期望值计算: E = p*W - (1-p)*L")
    print("   • 市场情绪分析")
    print("   • 对手策略反向思维")
    print("   • 只在正期望值时交易")
    print("📈 交易条件:")
    print("   • 期望值 > 15%")
    print("   • 胜率 > 70%")
    print("   • 价格极端偏离中线")
    print("   • 强趋势确认")
    print("⏰ 持仓时间: 90-365天")
    print("🎯 止盈: 25%, 止损: 8%, 移动止损: 12%")
    print("🔄 复利计算: 启用")
    print("📊 最大仓位: 8%")
    print("❄️ 冷却期: 60天")

    # 创建回测器
    backtester = HSI50GameTheoryMonthly3K()

    # 获取数据
    if not backtester.fetch_hsi_data():
        return

    # 计算指标
    backtester.calculate_indicators()

    # 执行回测
    trades_df, monthly_df, daily_df, signals_df = backtester.backtest_game_theory_strategy()

    # 分析结果
    backtester.analyze_results(trades_df, monthly_df, daily_df, signals_df)

if __name__ == "__main__":
    main()
