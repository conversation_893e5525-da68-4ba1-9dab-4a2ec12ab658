#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
长江和记实业0001.HK - 完全按照700HK.py复利逻辑
===============================================

完全复制700HK.py的复利逻辑，只是换标的测试

作者: Cosmoon NG
"""

import yfinance as yf
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from scipy import stats
import warnings
warnings.filterwarnings('ignore')

class HSI50Backtest0001HK:
    def __init__(self):
        """初始化回测系统 - 完全按照700HK.py"""
        self.symbol = "0001.HK"  # 长江和记实业
        self.initial_capital = 10000  # 初始资金
        self.monthly_addition = 3000  # 每月追加资金
        self.take_profit_long = 0.012  # 多头止盈 1.2%
        self.stop_loss_long = 0.006    # 多头止损 0.6%
        self.take_profit_short = 0.012  # 空头止盈 1.2%
        self.stop_loss_short = 0.006   # 空头止损 0.6%
        self.position = 0  # 当前持仓，1为多头，-1为空头，0为空仓
        self.current_price = 0  # 当前持仓价格

    def load_data(self):
        """从yfinance加载0001.HK数据 - 完全按照700HK.py"""
        print(f"\n1. 加载{self.symbol}数据...")
        try:
            # 获取尽可能多的历史数据
            end_date = datetime.now()
            start_date = end_date - timedelta(days=25*365)  # 25年数据

            ticker = yf.Ticker(self.symbol)
            hist_data = ticker.history(start=start_date, end=end_date)

            if len(hist_data) == 0:
                print(f"❌ 无法获取{self.symbol}的历史数据")
                return False

            # 数据预处理
            self.df = pd.DataFrame({
                'date': hist_data.index,
                'open': hist_data['Open'],
                'high': hist_data['High'],
                'low': hist_data['Low'],
                'close': hist_data['Close'],
                'volume': hist_data['Volume']
            })

            # 清理数据
            self.df = self.df.dropna()
            self.df = self.df.sort_values('date').reset_index(drop=True)

            print(f"✓ 成功获取 {len(self.df)} 条数据")
            print(f"数据范围: {self.df['date'].min().strftime('%Y-%m-%d')} 至 {self.df['date'].max().strftime('%Y-%m-%d')}")
            print(f"价格范围: {self.df['close'].min():.2f} - {self.df['close'].max():.2f} 港元")

            return True

        except Exception as e:
            print(f"❌ 数据加载失败: {str(e)}")
            return False

    def calculate_indicators(self):
        """计算技术指标 - 完全按照700HK.py"""
        print("\n2. 计算技术指标...")
        try:
            # Y值计算 (价格在20日区间的位置)
            window = 20
            self.df['high_20'] = self.df['high'].rolling(window).max()
            self.df['low_20'] = self.df['low'].rolling(window).min()
            self.df['y_value'] = (self.df['close'] - self.df['low_20']) / (self.df['high_20'] - self.df['low_20'])
            self.df['y_value'] = self.df['y_value'].fillna(0.5).clip(0, 1)

            # X值计算 (资金流强度指标)
            self.df['typical_price'] = (self.df['high'] + self.df['low'] + self.df['close']) / 3
            self.df['money_flow'] = self.df['typical_price'] * self.df['volume']
            self.df['price_change'] = self.df['typical_price'].diff()

            self.df['positive_mf'] = np.where(self.df['price_change'] > 0, self.df['money_flow'], 0)
            self.df['negative_mf'] = np.where(self.df['price_change'] < 0, self.df['money_flow'], 0)

            period = 14
            self.df['positive_mf_14'] = self.df['positive_mf'].rolling(period).sum()
            self.df['negative_mf_14'] = self.df['negative_mf'].rolling(period).sum()
            self.df['money_flow_ratio'] = self.df['positive_mf_14'] / (self.df['negative_mf_14'] + 1e-10)
            self.df['mfi'] = 100 - (100 / (1 + self.df['money_flow_ratio']))
            self.df['x_value'] = self.df['mfi'] / 100

            # E值计算 (Cosmoon公式)
            self.df['e_value'] = (8 * self.df['x_value'] - 3) * self.df['y_value'] - 3 * self.df['x_value'] + 1

            print(f"✓ 技术指标计算完成")
            print(f"XYE指标范围: Y({self.df['y_value'].min():.3f}-{self.df['y_value'].max():.3f}) "
                  f"X({self.df['x_value'].min():.3f}-{self.df['x_value'].max():.3f}) "
                  f"E({self.df['e_value'].min():.3f}-{self.df['e_value'].max():.3f})")

        except Exception as e:
            print(f"❌ 技术指标计算失败: {str(e)}")
            raise

    def calculate_regression_line(self):
        """计算回归线 - 完全按照700HK.py"""
        print("\n2. 计算回归线...")
        try:
            # 添加序号
            self.df['i'] = range(1, len(self.df) + 1)

            # 计算回归参数
            slope, intercept, r_value, _, _ = stats.linregress(
                self.df['i'], self.df['close']
            )

            # 计算回归线
            self.df['regression_line'] = intercept + slope * self.df['i']

            # 计算价格相对回归线的位置
            self.df['price_position'] = (self.df['close'] - self.df['regression_line']) / self.df['regression_line']

            print(f"✓ 回归线计算完成 (R² = {r_value**2:.4f})")
            print(f"回归斜率: {slope:.6f} (每日变化)")
            print(f"年化趋势: {slope*365:.2f} 港元/年")

        except Exception as e:
            print(f"❌ 回归线计算失败: {str(e)}")
            raise

    def add_monthly_capital(self, date, capital):
        """每月增加资金 - 完全按照700HK.py"""
        last_month = getattr(self, 'last_month', None)
        current_month = date.replace(day=1)

        if last_month is None or current_month > last_month:
            self.last_month = current_month
            return capital + self.monthly_addition

        return capital

    def run_backtest(self):
        """运行回测 - 完全按照700HK.py复利逻辑"""
        print("\n3. 开始回测...")
        try:
            # 准备结果记录
            self.trades = []
            capital = self.initial_capital
            self.equity_curve = []

            for i in range(60, len(self.df)):  # 从第60天开始，确保有足够的历史数据
                row = self.df.iloc[i]
                date = row['date']

                # 每月增加资金 - 700HK.py复利逻辑
                capital = self.add_monthly_capital(date, capital)

                # 记录权益
                self.equity_curve.append({
                    'date': date,
                    'equity': capital,
                    'position': self.position
                })

                # 如果有持仓，检查止盈止损 - 700HK.py复利逻辑
                if self.position != 0:
                    if self.position == 1:  # 多头
                        profit_ratio = (row['high'] - self.current_price) / self.current_price
                        loss_ratio = (self.current_price - row['low']) / self.current_price

                        if profit_ratio >= self.take_profit_long:  # 止盈
                            exit_price = self.current_price * (1 + self.take_profit_long)
                            profit = (exit_price - self.current_price) / self.current_price * capital
                            capital += profit  # 700HK.py复利核心
                            self.position = 0
                            self.trades.append({
                                'date': date,
                                'type': 'long_exit_tp',
                                'price': exit_price,
                                'profit': profit,
                                'capital': capital
                            })
                        elif loss_ratio >= self.stop_loss_long:  # 止损
                            exit_price = self.current_price * (1 - self.stop_loss_long)
                            loss = (exit_price - self.current_price) / self.current_price * capital
                            capital += loss  # 700HK.py复利核心
                            self.position = 0
                            self.trades.append({
                                'date': date,
                                'type': 'long_exit_sl',
                                'price': exit_price,
                                'profit': loss,
                                'capital': capital
                            })

                    elif self.position == -1:  # 空头
                        profit_ratio = (self.current_price - row['low']) / self.current_price
                        loss_ratio = (row['high'] - self.current_price) / self.current_price

                        if profit_ratio >= self.take_profit_short:  # 止盈
                            exit_price = self.current_price * (1 - self.take_profit_short)
                            profit = (self.current_price - exit_price) / self.current_price * capital
                            capital += profit  # 700HK.py复利核心
                            self.position = 0
                            self.trades.append({
                                'date': date,
                                'type': 'short_exit_tp',
                                'price': exit_price,
                                'profit': profit,
                                'capital': capital
                            })
                        elif loss_ratio >= self.stop_loss_short:  # 止损
                            exit_price = self.current_price * (1 + self.stop_loss_short)
                            loss = (exit_price - self.current_price) / self.current_price * capital * -1
                            capital += loss  # 700HK.py复利核心
                            self.position = 0
                            self.trades.append({
                                'date': date,
                                'type': 'short_exit_sl',
                                'price': exit_price,
                                'profit': loss,
                                'capital': capital
                            })

                # 如果空仓，判断是否开仓 - 700HK.py原始逻辑
                if self.position == 0:
                    # 使用Cosmoon策略判断
                    if row['e_value'] > 0 and row['x_value'] > 0.45 and row['y_value'] > 0.45:
                        # 多头信号
                        if row['price_position'] < 0:  # 价格低于回归线
                            self.position = 1
                            self.current_price = row['close']
                            self.trades.append({
                                'date': date,
                                'type': 'long_entry',
                                'price': self.current_price,
                                'capital': capital
                            })

                    elif (row['y_value'] < 0.3 or row['x_value'] < 0.3 or
                          (row['x_value'] > 0.45 and row['y_value'] < 0.35) or
                          (row['x_value'] < 0.45 and row['y_value'] > 0.35)):
                        # 空头信号
                        if row['price_position'] > 0:  # 价格高于回归线
                            self.position = -1
                            self.current_price = row['close']
                            self.trades.append({
                                'date': date,
                                'type': 'short_entry',
                                'price': self.current_price,
                                'capital': capital
                            })

            self.final_capital = capital
            print(f"✓ 回测完成！最终资金：{self.final_capital:,.2f}")

        except Exception as e:
            print(f"❌ 回测执行失败: {str(e)}")
            raise

    def analyze_results(self):
        """分析回测结果 - 完全按照700HK.py"""
        print("\n4. 回测分析...")
        try:
            # 转换交易记录为DataFrame
            trades_df = pd.DataFrame(self.trades)
            if len(trades_df) == 0:
                print("没有产生任何交易")
                return

            # 计算基本统计数据
            total_trades = len(trades_df[trades_df['type'].str.contains('entry')])
            exit_trades = trades_df[trades_df['type'].str.contains('exit')]
            profit_trades = exit_trades[exit_trades['profit'] > 0]

            # 时间统计
            start_date = self.df['date'].min()
            end_date = self.df['date'].max()
            total_days = (end_date - start_date).days
            total_years = total_days / 365.25

            # 投资统计
            months_passed = int(total_days / 30)
            total_invested = self.initial_capital + months_passed * self.monthly_addition
            net_profit = self.final_capital - total_invested
            total_return = (net_profit / total_invested) * 100
            annual_return = ((self.final_capital / total_invested) ** (1/total_years) - 1) * 100

            # 价格表现
            start_price = self.df['close'].iloc[0]
            end_price = self.df['close'].iloc[-1]
            price_return = ((end_price - start_price) / start_price) * 100

            print(f"\n📈 700HK.py完整复利逻辑回测结果")
            print("=" * 60)

            print(f"📊 基础信息:")
            print(f"   标的: {self.symbol} (长江和记实业)")
            print(f"   回测期间: {total_years:.1f}年")
            print(f"   数据天数: {total_days}天")

            print(f"\n💰 价格表现:")
            print(f"   起始价格: {start_price:.2f}港元")
            print(f"   最终价格: {end_price:.2f}港元")
            print(f"   价格涨幅: {price_return:.2f}%")

            print(f"\n🚀 策略表现 (700HK.py复利逻辑):")
            print(f"   总投入: {total_invested:,.0f}港元")
            print(f"   最终资金: {self.final_capital:,.0f}港元")
            print(f"   净收益: {net_profit:,.0f}港元")
            print(f"   总收益率: {total_return:.2f}%")
            print(f"   年化收益率: {annual_return:.2f}%")

            if len(exit_trades) > 0:
                win_rate = len(profit_trades) / len(exit_trades) * 100
                avg_profit = profit_trades['profit'].mean() if len(profit_trades) > 0 else 0
                avg_loss = exit_trades[exit_trades['profit'] <= 0]['profit'].mean() if len(exit_trades[exit_trades['profit'] <= 0]) > 0 else 0
                profit_factor = abs(avg_profit / avg_loss) if avg_loss != 0 else 0

                print(f"\n📋 交易统计:")
                print(f"   总交易次数: {total_trades}")
                print(f"   盈利交易: {len(profit_trades)}")
                print(f"   亏损交易: {len(exit_trades) - len(profit_trades)}")
                print(f"   胜率: {win_rate:.2f}%")
                print(f"   平均盈利: {avg_profit:,.0f}港元")
                print(f"   平均亏损: {avg_loss:,.0f}港元")
                print(f"   盈亏比: {profit_factor:.2f}")

            # 买入持有对比
            buy_hold_shares = int(self.initial_capital / start_price)
            buy_hold_final = buy_hold_shares * end_price
            buy_hold_return = ((buy_hold_final - self.initial_capital) / self.initial_capital) * 100

            print(f"\n🆚 买入持有对比:")
            print(f"   买入持有收益: {buy_hold_return:.2f}%")
            print(f"   策略收益: {total_return:.2f}%")
            print(f"   策略优势: {total_return - buy_hold_return:.2f}%")

        except Exception as e:
            print(f"❌ 结果分析失败: {str(e)}")
            raise

def main():
    """主函数"""
    print("🎯 长江和记实业0001.HK - 700HK.py完整复利逻辑测试")
    print("=" * 60)
    print("📋 700HK.py完整复利逻辑:")
    print("   💰 复利核心: profit = ratio * capital; capital += profit")
    print("   🎯 多头条件: E>0 且 X>0.45 且 Y>0.45 且 价格<回归线")
    print("   📉 空头条件: (Y<0.3 或 X<0.3 或 特定XY组合) 且 价格>回归线")
    print("   ⚖️  止盈止损: 多头+1.2%/-0.6%, 空头+1.2%/-0.6%")
    print("   📈 当日触发: 基于最高最低价判断止盈止损")

    backtest = HSI50Backtest0001HK()

    if not backtest.load_data():
        return

    backtest.calculate_indicators()
    backtest.calculate_regression_line()
    backtest.run_backtest()
    backtest.analyze_results()

    print(f"\n🎉 700HK.py完整复利逻辑测试完成!")
    print(f"📈 这是700HK.py完整逻辑在0001HK上的表现")
    print(f"💰 复利效应: 每次盈亏都基于当前总资本计算")
    print(f"🔄 比例计算: profit = ratio * capital; capital += profit")

if __name__ == "__main__":
    main()
