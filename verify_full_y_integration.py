#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证Full_Y集成结果
================
检查Full_Y字段的计算是否正确
"""

import mysql.connector
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

def verify_full_y_integration():
    """验证Full_Y集成结果"""
    
    # 数据库配置
    db_config = {
        'host': 'localhost',
        'database': 'finance',
        'user': 'root',
        'password': '12345678',
        'charset': 'utf8mb4'
    }
    
    try:
        print("🔍 验证Full_Y集成结果")
        print("=" * 60)
        print(f"📅 验证时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        # 连接数据库
        connection = mysql.connector.connect(**db_config)
        cursor = connection.cursor()
        print(f"✅ 成功连接到数据库: {db_config['host']}/{db_config['database']}")
        
        table_name = 'stock_600887_ss'
        
        # 1. 基本统计信息
        print(f"\n📊 {table_name} Full_Y基本统计:")
        cursor.execute(f"""
            SELECT 
                COUNT(*) as total_rows,
                COUNT(Full_Y) as full_y_rows,
                MIN(Full_Y) as min_full_y,
                MAX(Full_Y) as max_full_y,
                AVG(Full_Y) as avg_full_y
            FROM {table_name}
        """)
        
        stats = cursor.fetchone()
        print(f"   • 总记录数: {stats[0]}")
        print(f"   • Full_Y记录数: {stats[1]}")
        print(f"   • 覆盖率: {stats[1]/stats[0]*100:.1f}%")
        print(f"   • Full_Y范围: {float(stats[2]):.6f} ~ {float(stats[3]):.6f}")
        print(f"   • Full_Y平均值: {float(stats[4]):.6f}")
        
        # 2. 验证前20行的计算逻辑
        print(f"\n📊 前20行Full_Y计算验证:")
        cursor.execute(f"""
            SELECT 
                ROW_NUMBER() OVER (ORDER BY date ASC) as row_num,
                date, 
                controller, 
                Full_Y
            FROM {table_name} 
            ORDER BY date ASC 
            LIMIT 20
        """)
        
        results = cursor.fetchall()
        print("行号 | 日期          | controller | Full_Y     | 预期计算")
        print("-" * 65)
        
        manual_sum = 0
        for row in results:
            row_num = row[0]
            date_str = str(row[1])
            controller_val = row[2] if row[2] is not None else 0
            full_y_val = float(row[3]) if row[3] is not None else 0.0
            
            # 手动计算预期值
            if controller_val == 1:
                manual_sum += 1.0 / row_num
            
            expected_str = f"累计={manual_sum:.6f}" if controller_val == 1 else "不累加"
            
            print(f"{row_num:4d} | {date_str} | {controller_val:10} | {full_y_val:10.6f} | {expected_str}")
        
        # 3. 检查最后几行的Full_Y值
        print(f"\n📊 最新10条数据:")
        cursor.execute(f"""
            SELECT 
                date, 
                close,
                midprice,
                controller, 
                Full_Y,
                ROUND((close - midprice) / midprice * 100, 2) as deviation_pct
            FROM {table_name} 
            ORDER BY date DESC 
            LIMIT 10
        """)
        
        latest_results = cursor.fetchall()
        print("日期          | 收盘价  | midprice | controller | Full_Y     | 偏差%")
        print("-" * 75)
        for row in latest_results:
            date_str = str(row[0])
            close_val = float(row[1]) if row[1] is not None else 0.0
            mid_val = float(row[2]) if row[2] is not None else 0.0
            controller_val = row[3] if row[3] is not None else 'N/A'
            full_y_val = float(row[4]) if row[4] is not None else 0.0
            deviation = float(row[5]) if row[5] is not None else 0.0
            print(f"{date_str} | {close_val:7.2f} | {mid_val:8.4f} | {controller_val:10} | {full_y_val:10.6f} | {deviation:6.2f}%")
        
        # 4. 验证Full_Y的单调性（应该是递增的）
        print(f"\n🔍 验证Full_Y单调性:")
        cursor.execute(f"""
            SELECT COUNT(*) as violations
            FROM (
                SELECT 
                    Full_Y,
                    LAG(Full_Y) OVER (ORDER BY date ASC) as prev_full_y
                FROM {table_name}
                WHERE Full_Y IS NOT NULL
            ) t
            WHERE Full_Y < prev_full_y
        """)
        
        violations = cursor.fetchone()[0]
        if violations == 0:
            print("   ✅ Full_Y单调递增，计算正确")
        else:
            print(f"   ⚠️ 发现{violations}处Full_Y非递增")
        
        # 5. 计算controller=1的总数和比例
        cursor.execute(f"""
            SELECT 
                COUNT(*) as total_records,
                SUM(CASE WHEN controller = 1 THEN 1 ELSE 0 END) as controller_1_count,
                ROUND(SUM(CASE WHEN controller = 1 THEN 1 ELSE 0 END) / COUNT(*), 6) as k_value,
                MAX(Full_Y) as final_full_y
            FROM {table_name}
        """)
        
        summary = cursor.fetchone()
        total_records = summary[0]
        controller_1_count = summary[1]
        k_value = float(summary[2])
        final_full_y = float(summary[3])
        
        print(f"\n📊 总体统计:")
        print(f"   • 总记录数: {total_records}")
        print(f"   • controller=1的记录数: {controller_1_count}")
        print(f"   • k值 (controller=1比例): {k_value:.6f}")
        print(f"   • 最终Full_Y值: {final_full_y:.6f}")
        
        # 6. 测试sp_updatecontroller调用
        print(f"\n🧪 测试sp_updatecontroller调用:")
        args = [table_name, 0]
        result = cursor.callproc('sp_updatecontroller', args)
        
        # 获取结果集
        for result_set in cursor.stored_results():
            rows = result_set.fetchall()
            for row in rows:
                print(f"   📊 {row}")
        
        # 显示OUT参数结果
        k_value_result = result[1]
        print(f"   📊 返回的k值: {k_value_result}")
        
        # 7. 生成使用示例
        print(f"\n📝 Full_Y字段使用示例:")
        print("=" * 60)
        
        print("1️⃣ 完整更新所有字段:")
        print(f"   CALL sp_updatecontroller('{table_name}', @k_value);")
        print("   SELECT @k_value AS k_result;")
        
        print("\n2️⃣ 查看Full_Y趋势:")
        print(f"""   SELECT 
       YEAR(date) as year,
       COUNT(*) as total_days,
       SUM(CASE WHEN controller = 1 THEN 1 ELSE 0 END) as strong_days,
       MIN(Full_Y) as year_start_full_y,
       MAX(Full_Y) as year_end_full_y,
       ROUND(MAX(Full_Y) - MIN(Full_Y), 6) as yearly_increase
   FROM {table_name}
   GROUP BY YEAR(date)
   ORDER BY year DESC
   LIMIT 5;""")
        
        print("\n3️⃣ 分析Full_Y与收盘价关系:")
        print(f"""   SELECT 
       CASE 
           WHEN Full_Y < 0.1 THEN '低累积(0-0.1)'
           WHEN Full_Y < 0.2 THEN '中低累积(0.1-0.2)'
           WHEN Full_Y < 0.3 THEN '中等累积(0.2-0.3)'
           WHEN Full_Y < 0.4 THEN '中高累积(0.3-0.4)'
           ELSE '高累积(0.4+)'
       END as full_y_range,
       COUNT(*) as days,
       AVG(close) as avg_close,
       AVG((close - midprice) / midprice * 100) as avg_deviation_pct
   FROM {table_name}
   WHERE Full_Y IS NOT NULL AND midprice IS NOT NULL
   GROUP BY 1
   ORDER BY MIN(Full_Y);""")
        
        print("\n💡 Full_Y字段含义:")
        print("   • Full_Y是累积概率指标")
        print("   • 每当controller=1时，累加 1/行号")
        print("   • 反映历史上收盘价高于midprice的累积强度")
        print("   • 数值越大，表示历史表现越强势")
        print("   • 可用于判断股票的长期趋势强度")
        
        # 关闭连接
        cursor.close()
        connection.close()
        
        print("\n✅ Full_Y集成验证完成!")
        print("🎉 所有功能正常工作，可以开始使用Full_Y进行分析")
        
    except mysql.connector.Error as e:
        print(f"❌ 验证失败: {e}")
    except Exception as e:
        print(f"❌ 验证失败: {e}")

if __name__ == "__main__":
    verify_full_y_integration()
