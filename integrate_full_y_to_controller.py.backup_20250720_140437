#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
将Full_Y计算集成到sp_updatecontroller函数
========================================
Full_Y逻辑：
- 第1行: if(controller=1) 除以 1
- 第2行: if(controller=1) 除以 2
- 第3行: if(controller=1) 除以 3
- ...以此类推
"""

import mysql.connector
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

class FullYIntegrator:
    def __init__(self):
        """初始化数据库连接"""
        self.db_config = {
            'host': '************',
            'database': 'finance',
            'user': 'root',
            'password': '',
            'charset': 'utf8mb4',
            'autocommit': True
        }
        self.connection = None
        self.cursor = None

    def connect_database(self):
        """连接数据库"""
        try:
            print("🔗 连接MySQL数据库...")
            self.connection = mysql.connector.connect(**self.db_config)
            self.cursor = self.connection.cursor()
            print(f"✅ 成功连接到数据库: {self.db_config['host']}/{self.db_config['database']}")
            return True
        except mysql.connector.Error as e:
            print(f"❌ 数据库连接失败: {e}")
            return False

    def backup_current_controller(self):
        """备份当前的sp_updatecontroller"""
        try:
            print("🔄 检查当前sp_updatecontroller...")

            # 检查存储过程是否存在
            self.cursor.execute("""
                SELECT COUNT(*) FROM information_schema.ROUTINES
                WHERE ROUTINE_SCHEMA = DATABASE()
                AND ROUTINE_NAME = 'sp_updatecontroller'
            """)

            exists = self.cursor.fetchone()[0]

            if exists > 0:
                # 获取当前存储过程定义
                self.cursor.execute("SHOW CREATE PROCEDURE sp_updatecontroller")
                result = self.cursor.fetchone()

                if result:
                    # 创建备份
                    backup_name = f"sp_updatecontroller_backup_full_y_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
                    backup_sql = result[2].replace(
                        'CREATE DEFINER=`root`@`%` PROCEDURE `sp_updatecontroller`',
                        f'CREATE PROCEDURE `{backup_name}`'
                    ).replace(
                        'CREATE PROCEDURE `sp_updatecontroller`',
                        f'CREATE PROCEDURE `{backup_name}`'
                    )

                    self.cursor.execute(backup_sql)
                    print(f"✅ 已备份为: {backup_name}")

                    # 保存到文件
                    with open(f'{backup_name}.sql', 'w', encoding='utf-8') as f:
                        f.write(backup_sql)
                    print(f"📄 备份已保存到文件: {backup_name}.sql")

                    return True
                else:
                    print("❌ 无法获取当前存储过程定义")
                    return False
            else:
                print("⚠️ sp_updatecontroller不存在，将直接创建新的")
                return True

        except mysql.connector.Error as e:
            print(f"❌ 备份检查失败: {e}")
            return False

    def create_enhanced_controller_with_full_y(self):
        """创建集成Full_Y的增强版sp_updatecontroller"""
        try:
            print("🔧 创建集成Full_Y的增强版sp_updatecontroller...")

            # 删除现有的存储过程
            self.cursor.execute("DROP PROCEDURE IF EXISTS sp_updatecontroller")

            # 创建新的存储过程
            enhanced_procedure = """
CREATE PROCEDURE sp_updatecontroller(
    IN tablename VARCHAR(64),
    OUT result_k DECIMAL(20,6)
)
BEGIN
    DECLARE col_exists INT DEFAULT 0;
    DECLARE v_start_time DATETIME DEFAULT NOW();
    DECLARE done INT DEFAULT FALSE;
    DECLARE current_row_num INT DEFAULT 0;
    DECLARE current_controller INT;
    DECLARE record_date DATE;
    DECLARE running_sum DECIMAL(20,10) DEFAULT 0;

    -- 游标声明 - 使用动态表名
    DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;

    -- 1. 首先更新midprice (调用sp_averagelineV3)
    SELECT CONCAT('开始更新midprice - 表: ', tablename, ' 时间: ', v_start_time) AS start_message;
    CALL sp_averagelineV3(tablename);
    SELECT 'midprice更新完成' AS midprice_status;

    -- 2. 检查controller列是否存在
    SET @sql = CONCAT(
        'SELECT COUNT(*) INTO @col_exists FROM information_schema.COLUMNS ',
        'WHERE TABLE_SCHEMA=DATABASE() AND TABLE_NAME=''', tablename, ''' AND COLUMN_NAME=''controller'''
    );
    PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;
    SET col_exists = @col_exists;

    IF col_exists = 0 THEN
        SET @sql = CONCAT('ALTER TABLE `', tablename, '` ADD COLUMN `controller` INT');
        PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;
        SELECT 'controller列已添加' AS controller_status;
    END IF;

    -- 3. 更新controller字段
    SET @sql = CONCAT(
        'UPDATE `',
        tablename,
        '` SET controller = CASE WHEN close < midprice THEN 0 WHEN close > midprice THEN 1 ELSE 3 END'
    );
    PREPARE stmt FROM @sql;
    EXECUTE stmt;
    DEALLOCATE PREPARE stmt;
    SELECT 'controller字段更新完成' AS controller_update_status;

    -- 4. 检查Full_Y列是否存在
    SET @sql = CONCAT(
        'SELECT COUNT(*) INTO @col_exists FROM information_schema.COLUMNS ',
        'WHERE TABLE_SCHEMA=DATABASE() AND TABLE_NAME=''', tablename, ''' AND COLUMN_NAME=''Full_Y'''
    );
    PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;
    SET col_exists = @col_exists;

    IF col_exists = 0 THEN
        SET @sql = CONCAT('ALTER TABLE `', tablename, '` ADD COLUMN `Full_Y` DECIMAL(20,10) DEFAULT NULL');
        PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;
        SELECT 'Full_Y列已添加' AS full_y_status;
    END IF;

    -- 5. 计算Full_Y字段
    SELECT '开始计算Full_Y...' AS full_y_calc_status;

    -- 先创建临时表来计算Full_Y
    SET @sql = CONCAT('DROP TEMPORARY TABLE IF EXISTS temp_full_y');
    PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;

    SET @sql = CONCAT(
        'CREATE TEMPORARY TABLE temp_full_y AS ',
        'SELECT date, ',
        '  @running_sum := @running_sum + ',
        '    CASE WHEN controller = 1 THEN 1.0/@row_num ELSE 0 END as Full_Y ',
        'FROM (',
        '  SELECT date, controller, ',
        '    @row_num := @row_num + 1 as row_num ',
        '  FROM `', tablename, '` ',
        '  CROSS JOIN (SELECT @row_num := 0, @running_sum := 0) r ',
        '  ORDER BY date ASC',
        ') t'
    );
    PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;

    -- 更新原表
    SET @sql = CONCAT(
        'UPDATE `', tablename, '` t1 ',
        'JOIN temp_full_y t2 ON t1.date = t2.date ',
        'SET t1.Full_Y = t2.Full_Y'
    );
    PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;

    -- 清理临时表
    DROP TEMPORARY TABLE IF EXISTS temp_full_y;

    SELECT 'Full_Y计算完成' AS full_y_complete_status;

    -- 6. 计算k值并存入OUT参数
    SET @sql = CONCAT(
        'SELECT SUM(CASE WHEN controller = 1 THEN 1 ELSE 0 END) / NULLIF(COUNT(*), 0) INTO @k_value ',
        'FROM `', tablename, '`'
    );
    PREPARE stmt FROM @sql;
    EXECUTE stmt;
    DEALLOCATE PREPARE stmt;

    SET result_k = @k_value;

    -- 7. 返回统计信息
    SELECT
        CONCAT('更新完成 - 表: ', tablename) AS completion_message,
        CONCAT('k值: ', IFNULL(result_k, 0)) AS k_value_message,
        CONCAT('耗时: ', TIMESTAMPDIFF(SECOND, v_start_time, NOW()), ' 秒') AS duration;

END
            """

            self.cursor.execute(enhanced_procedure)
            print("✅ 成功创建集成Full_Y的sp_updatecontroller")

            # 保存新的存储过程定义到文件
            with open('sp_updatecontroller_with_full_y.sql', 'w', encoding='utf-8') as f:
                f.write(enhanced_procedure)
            print("📄 新定义已保存到 sp_updatecontroller_with_full_y.sql")

            return True

        except mysql.connector.Error as e:
            print(f"❌ 创建增强版存储过程失败: {e}")
            return False

    def test_enhanced_controller(self, table_name):
        """测试增强版控制器"""
        try:
            print(f"\n🧪 测试集成Full_Y的sp_updatecontroller - 表: {table_name}")

            # 调用存储过程
            args = [table_name, 0]  # 第二个参数是OUT参数的占位符
            result = self.cursor.callproc('sp_updatecontroller', args)

            # 获取结果集
            print("📊 sp_updatecontroller执行过程:")
            for result_set in self.cursor.stored_results():
                rows = result_set.fetchall()
                for row in rows:
                    print(f"   {row}")

            # 显示OUT参数结果
            k_value = result[1]  # OUT参数result_k的值
            print(f"📊 k值结果: {k_value}")

            return True

        except mysql.connector.Error as e:
            print(f"❌ 测试增强版控制器失败: {e}")
            return False

    def verify_full_y_results(self, table_name):
        """验证Full_Y计算结果"""
        try:
            print(f"\n🔍 验证Full_Y计算结果 - 表: {table_name}")

            # 统计Full_Y数据
            self.cursor.execute(f"""
                SELECT
                    COUNT(*) as total_rows,
                    COUNT(Full_Y) as full_y_rows,
                    MIN(Full_Y) as min_full_y,
                    MAX(Full_Y) as max_full_y,
                    AVG(Full_Y) as avg_full_y
                FROM {table_name}
            """)

            stats = self.cursor.fetchone()
            print(f"📊 Full_Y统计结果:")
            print(f"   • 总记录数: {stats[0]}")
            print(f"   • Full_Y记录数: {stats[1]}")
            print(f"   • Full_Y覆盖率: {stats[1]/stats[0]*100:.1f}%")
            if stats[2] is not None:
                print(f"   • Full_Y范围: {float(stats[2]):.6f} ~ {float(stats[3]):.6f}")
                print(f"   • Full_Y平均值: {float(stats[4]):.6f}")

            # 查看前20行的Full_Y计算过程
            self.cursor.execute(f"""
                SELECT
                    ROW_NUMBER() OVER (ORDER BY date ASC) as row_num,
                    date,
                    controller,
                    Full_Y,
                    CASE WHEN controller = 1 THEN CONCAT('1/', ROW_NUMBER() OVER (ORDER BY date ASC), ' = ', ROUND(1.0/ROW_NUMBER() OVER (ORDER BY date ASC), 6)) ELSE 'skip' END as calculation
                FROM {table_name}
                WHERE Full_Y IS NOT NULL
                ORDER BY date ASC
                LIMIT 20
            """)

            results = self.cursor.fetchall()
            print(f"\n📊 前20行Full_Y计算过程:")
            print("行号 | 日期          | controller | Full_Y     | 计算过程")
            print("-" * 65)
            for row in results:
                row_num = row[0]
                date_str = str(row[1])
                controller_val = row[2] if row[2] is not None else 'N/A'
                full_y_val = float(row[3]) if row[3] is not None else 0.0
                calc_str = row[4] if row[4] is not None else ''
                print(f"{row_num:4d} | {date_str} | {controller_val:10} | {full_y_val:10.6f} | {calc_str}")

            # 查看最新20行数据
            self.cursor.execute(f"""
                SELECT
                    date,
                    close,
                    midprice,
                    controller,
                    Full_Y,
                    ROUND((close - midprice) / midprice * 100, 2) as deviation_pct
                FROM {table_name}
                WHERE Full_Y IS NOT NULL
                ORDER BY date DESC
                LIMIT 20
            """)

            latest_results = self.cursor.fetchall()
            print(f"\n📊 最新20条数据:")
            print("日期          | 收盘价  | midprice | controller | Full_Y     | 偏差%")
            print("-" * 75)
            for row in latest_results:
                date_str = str(row[0])
                close_val = float(row[1]) if row[1] is not None else 0.0
                mid_val = float(row[2]) if row[2] is not None else 0.0
                controller_val = row[3] if row[3] is not None else 'N/A'
                full_y_val = float(row[4]) if row[4] is not None else 0.0
                deviation = float(row[5]) if row[5] is not None else 0.0
                print(f"{date_str} | {close_val:7.2f} | {mid_val:8.4f} | {controller_val:10} | {full_y_val:10.6f} | {deviation:6.2f}%")

            # 验证Full_Y计算逻辑
            print(f"\n🔍 验证Full_Y计算逻辑:")
            self.cursor.execute(f"""
                SELECT
                    SUM(CASE WHEN controller = 1 THEN 1.0/ROW_NUMBER() OVER (ORDER BY date ASC) ELSE 0 END) as manual_calculation,
                    MAX(Full_Y) as stored_full_y
                FROM {table_name}
                WHERE Full_Y IS NOT NULL
            """)

            verification = self.cursor.fetchone()
            manual_calc = float(verification[0]) if verification[0] is not None else 0.0
            stored_calc = float(verification[1]) if verification[1] is not None else 0.0

            print(f"   • 手动计算结果: {manual_calc:.6f}")
            print(f"   • 存储的Full_Y最大值: {stored_calc:.6f}")
            print(f"   • 差异: {abs(manual_calc - stored_calc):.6f}")

            if abs(manual_calc - stored_calc) < 0.000001:
                print("   ✅ Full_Y计算正确!")
            else:
                print("   ⚠️ Full_Y计算可能有误差")

            return True

        except mysql.connector.Error as e:
            print(f"❌ 验证Full_Y结果失败: {e}")
            return False

    def generate_sql_examples(self, table_name):
        """生成SQL使用示例"""
        print(f"\n📝 Full_Y字段使用示例 (表: {table_name}):")
        print("=" * 60)

        print("1️⃣ 完整更新 (midprice + controller + Full_Y + k值):")
        print(f"   CALL sp_updatecontroller('{table_name}', @k_value);")
        print("   SELECT @k_value AS k_result;")

        print("\n2️⃣ 查看Full_Y计算过程:")
        print(f"""   SELECT
       ROW_NUMBER() OVER (ORDER BY date ASC) as row_num,
       date, controller, Full_Y,
       CASE WHEN controller = 1
            THEN CONCAT('1/', ROW_NUMBER() OVER (ORDER BY date ASC), ' = ',
                       ROUND(1.0/ROW_NUMBER() OVER (ORDER BY date ASC), 6))
            ELSE 'skip'
       END as calculation
   FROM {table_name}
   ORDER BY date ASC
   LIMIT 20;""")

        print("\n3️⃣ 查看最新数据:")
        print(f"""   SELECT date, close, midprice, controller, Full_Y,
          ROUND((close - midprice) / midprice * 100, 2) as deviation_pct
   FROM {table_name}
   ORDER BY date DESC
   LIMIT 10;""")

        print("\n4️⃣ 分析Full_Y趋势:")
        print(f"""   SELECT
       YEAR(date) as year,
       COUNT(*) as total_days,
       SUM(CASE WHEN controller = 1 THEN 1 ELSE 0 END) as controller_1_days,
       MAX(Full_Y) as max_full_y,
       ROUND(MAX(Full_Y) - LAG(MAX(Full_Y)) OVER (ORDER BY YEAR(date)), 6) as yearly_increase
   FROM {table_name}
   GROUP BY YEAR(date)
   ORDER BY year;""")

        print("\n💡 Full_Y字段含义:")
        print("   • Full_Y是累积计算的结果")
        print("   • 每当controller=1时，累加 1/行号")
        print("   • 反映了历史上收盘价高于midprice的累积概率")
        print("   • 数值越大，表示历史表现越强势")

    def close_connection(self):
        """关闭数据库连接"""
        if self.cursor:
            self.cursor.close()
        if self.connection:
            self.connection.close()
        print("🔒 数据库连接已关闭")

    def run(self, table_name='stock_600887_ss'):
        """执行主流程"""
        print("🎯 将Full_Y计算集成到sp_updatecontroller")
        print("=" * 60)
        print(f"📅 执行时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"🏠 数据库: {self.db_config['host']}/{self.db_config['database']}")
        print(f"📊 测试表: {table_name}")
        print("\n💡 Full_Y计算逻辑:")
        print("   • 第1行: if(controller=1) 累加 1/1")
        print("   • 第2行: if(controller=1) 累加 1/2")
        print("   • 第3行: if(controller=1) 累加 1/3")
        print("   • ...以此类推")

        try:
            # 1. 连接数据库
            if not self.connect_database():
                return False

            # 2. 备份当前存储过程
            if not self.backup_current_controller():
                return False

            # 3. 创建集成Full_Y的增强版
            if not self.create_enhanced_controller_with_full_y():
                return False

            # 4. 测试增强版控制器
            if not self.test_enhanced_controller(table_name):
                return False

            # 5. 验证Full_Y结果
            if not self.verify_full_y_results(table_name):
                return False

            # 6. 生成使用示例
            self.generate_sql_examples(table_name)

            print("\n🎉 Full_Y成功集成到sp_updatecontroller!")
            print("💡 现在调用sp_updatecontroller会自动计算:")
            print("   • midprice (sp_averagelineV3)")
            print("   • controller (收盘价与midprice比较)")
            print("   • Full_Y (累积概率计算)")
            print("   • k值 (controller=1的比例)")

            return True

        except Exception as e:
            print(f"❌ 执行失败: {e}")
            return False

        finally:
            self.close_connection()

def main():
    """主函数"""
    integrator = FullYIntegrator()
    success = integrator.run('stock_600887_ss')

    if success:
        print("\n✅ 集成完成!")
        print("📝 使用方法: CALL sp_updatecontroller('stock_600887_ss', @k_value);")
    else:
        print("\n❌ 集成失败!")

if __name__ == "__main__":
    main()
