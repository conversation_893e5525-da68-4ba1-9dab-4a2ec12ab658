#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
HSI50指数20年回测系统（修正版）
==========================

特点：
1. 使用回归中线作为趋势判断
2. 使用凯利公式优化仓位
3. 采用Cosmoon XYE方法
4. 每月复利加入3000

作者: Cosmoon NG
日期: 2025年7月
"""

import pandas as pd
import numpy as np
from datetime import datetime
import matplotlib.pyplot as plt
from scipy import stats
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False



class EABBacktest:
    def __init__(self):
        """初始化EAB_0023HK回测系统"""
        self.symbol = "0023.HK"  # EAB (东亚银行)
        self.initial_capital = 2500  # 初始资金
        self.monthly_addition = 0  # 每月追加资金
        self.take_profit_long = 0.012  # 多头止盈 1.2%
        self.stop_loss_long = 0.006    # 多头止损 0.6%
        self.take_profit_short = 0.012  # 空头止盈 1.2%
        self.stop_loss_short = 0.006   # 空头止损 0.6%
        self.position = 0  # 当前持仓，1为多头，-1为空头，0为空仓
        self.current_price = 0  # 当前持仓价格

        # 不长期持仓机制参数
        self.max_holding_days = 5  # 最大持仓天数
        self.max_holding_hours = 4  # 最大持仓小时数（日内交易）
        self.daily_close_time = "15:25"  # 每日强制平仓时间（收市前5分钟）
        self.position_entry_date = None  # 持仓开始日期
        self.position_entry_time = None  # 持仓开始时间
        self.daily_high_price = 0  # 当日最高价
        self.daily_low_price = float('inf')  # 当日最低价
        self.trailing_stop_ratio = 0.008  # 追踪止损比例 0.8%
        self.max_profit_achieved = 0  # 持仓期间达到的最大盈利

        # 信号反转平仓
        self.signal_reversal_exit = True  # 是否启用信号反转平仓
        self.volatility_exit = True  # 是否启用波动率平仓
        self.volume_exit = True  # 是否启用成交量异常平仓

    def load_data(self):
        """从数据库加载EAB_0023HK数据"""
        print(f"\n1. 加载{self.symbol}数据...")

        try:
            import mysql.connector

            # 数据库连接配置
            config = {
                'host': 'localhost',
                'port': 3306,
                'user': 'root',
                'password': '12345678',
                'database': 'finance',
                'charset': 'utf8mb4'
            }

            print("   从数据库读取EAB数据...")

            # 连接数据库
            conn = mysql.connector.connect(**config)
            cursor = conn.cursor()

            # 查询EAB数据
            query = """
            SELECT
                Date, Open, High, Low, Close, Volume,
                Y_Value, X_Value, E_Value, RSI, MFI,
                MoneyFlowRatio, TradingSignal
            FROM eab_0023hk
            ORDER BY Date ASC
            """

            cursor.execute(query)
            results = cursor.fetchall()

            if not results:
                raise ValueError("数据库中没有EAB数据")

            # 转换为DataFrame
            columns = ['date', 'open', 'high', 'low', 'close', 'volume',
                      'y_value', 'x_value', 'e_value', 'rsi', 'mfi',
                      'money_flow_ratio', 'trading_signal']

            self.df = pd.DataFrame(results, columns=columns)
            self.df['date'] = pd.to_datetime(self.df['date'])

            # 转换数值列为float类型
            numeric_columns = ['open', 'high', 'low', 'close', 'volume',
                             'y_value', 'x_value', 'e_value', 'rsi', 'mfi',
                             'money_flow_ratio', 'trading_signal']

            for col in numeric_columns:
                if col in self.df.columns:
                    self.df[col] = pd.to_numeric(self.df[col], errors='coerce')

            # 数据质量检查
            null_counts = self.df[['close', 'y_value', 'x_value', 'e_value']].isnull().sum()
            if null_counts.sum() > 0:
                print(f"   ⚠️ 发现空值: {null_counts.to_dict()}")
                # 填充空值
                self.df = self.df.fillna(method='ffill').fillna(method='bfill')

            # 关闭数据库连接
            cursor.close()
            conn.close()

            # 显示XYE指标范围
            print(f"   XYE指标范围: Y({self.df['y_value'].min():.3f}-{self.df['y_value'].max():.3f}) "
                  f"X({self.df['x_value'].min():.3f}-{self.df['x_value'].max():.3f}) "
                  f"E({self.df['e_value'].min():.3f}-{self.df['e_value'].max():.3f})")

            print(f"✓ 加载了 {len(self.df)} 条数据")
            print(f"数据范围：{self.df['date'].min().date()} 至 {self.df['date'].max().date()}")
            print(f"起始价格：{self.df['close'].iloc[0]:.2f} 港元")
            print(f"最新价格：{self.df['close'].iloc[-1]:.2f} 港元")

        except Exception as e:
            print(f"❌ 数据加载失败: {str(e)}")
            raise

    def calculate_xye_indicators(self):
        """计算Cosmoon XYE指标"""
        print("   计算XYE指标...")

        # 计算Y指标 (价格在20日区间的位置)
        window = 20
        self.df['high_20'] = self.df['high'].rolling(window).max()
        self.df['low_20'] = self.df['low'].rolling(window).min()
        self.df['y_value'] = (self.df['close'] - self.df['low_20']) / (self.df['high_20'] - self.df['low_20'])
        self.df['y_value'] = self.df['y_value'].fillna(0.5).clip(0, 1)

        # 计算X指标 (资金流强度)
        self.df['typical_price'] = (self.df['high'] + self.df['low'] + self.df['close']) / 3
        self.df['money_flow'] = self.df['typical_price'] * self.df['volume']
        self.df['price_change'] = self.df['typical_price'].diff()

        # 正负资金流
        self.df['positive_mf'] = np.where(self.df['price_change'] > 0, self.df['money_flow'], 0)
        self.df['negative_mf'] = np.where(self.df['price_change'] < 0, self.df['money_flow'], 0)

        # 14日资金流比率
        period = 14
        self.df['positive_mf_14'] = self.df['positive_mf'].rolling(period).sum()
        self.df['negative_mf_14'] = self.df['negative_mf'].rolling(period).sum()
        self.df['money_flow_ratio'] = self.df['positive_mf_14'] / (self.df['negative_mf_14'] + 1e-10)

        # MFI和X值
        self.df['mfi'] = 100 - (100 / (1 + self.df['money_flow_ratio']))
        self.df['x_value'] = self.df['mfi'] / 100  # 归一化到0-1

        # 计算E指标 (Cosmoon公式)
        self.df['e_value'] = (8 * self.df['x_value'] - 3) * self.df['y_value'] - 3 * self.df['x_value'] + 1

        print(f"   XYE指标范围: Y({self.df['y_value'].min():.3f}-{self.df['y_value'].max():.3f}) "
              f"X({self.df['x_value'].min():.3f}-{self.df['x_value'].max():.3f}) "
              f"E({self.df['e_value'].min():.3f}-{self.df['e_value'].max():.3f})")

    def calculate_regression_line(self):
        """计算回归线"""
        print("\n2. 计算回归线...")
        try:
            # 添加序号
            self.df['i'] = range(1, len(self.df) + 1)

            # 确保数据类型正确，并去除NaN值
            x_data = self.df['i'].values.astype(float)
            y_data = self.df['close'].values.astype(float)

            # 去除NaN值
            valid_mask = ~(np.isnan(x_data) | np.isnan(y_data))
            x_clean = x_data[valid_mask]
            y_clean = y_data[valid_mask]

            # 计算回归参数
            slope, intercept, r_value, _, _ = stats.linregress(x_clean, y_clean)

            # 计算回归线
            self.df['regression_line'] = intercept + slope * self.df['i']

            # 计算价格相对回归线的位置
            self.df['price_position'] = (self.df['close'] - self.df['regression_line']) / self.df['regression_line']

            print(f"✓ 回归线计算完成 (R² = {r_value**2:.4f})")
            print(f"回归斜率: {slope:.6f} (每日变化)")
            print(f"年化趋势: {slope*365:.2f} 港元/年")

        except Exception as e:
            print(f"❌ 回归线计算失败: {str(e)}")
            raise

    def calculate_kelly(self, win_rate, profit_ratio):
        """计算凯利公式建议仓位"""
        if win_rate <= 0 or profit_ratio <= 0:
            return 0
        q = 1 - win_rate
        kelly = (win_rate/q * profit_ratio - 1) / profit_ratio
        return max(0, min(kelly, 1))  # 限制在0-1之间

    def add_monthly_capital(self, date, capital):
        """每月增加资金"""
        last_month = getattr(self, 'last_month', None)
        current_month = date.replace(day=1)

        if last_month is None or current_month > last_month:
            self.last_month = current_month
            return capital + self.monthly_addition

        return capital

    def check_no_long_term_holding(self, current_date, row, capital):
        """检查不长期持仓机制 - 核心函数"""
        if self.position == 0:
            return False, capital, "无持仓", 0, 0

        exit_reason = ""
        should_exit = False
        exit_price = row['close']

        # 1. 时间限制检查 - 最大持仓天数
        if self.position_entry_date:
            holding_days = (current_date - self.position_entry_date).days

            if holding_days >= self.max_holding_days:
                should_exit = True
                exit_reason = f"超过最大持仓天数({self.max_holding_days}天)"
                # 多头用当日最高价的98%，空头用当日最低价的102%
                if self.position == 1:
                    exit_price = row['high'] * 0.98
                else:
                    exit_price = row['low'] * 1.02

        # 2. 收市前强制平仓机制
        if not should_exit:
            # 模拟每日15:25强制平仓（收市前5分钟）
            should_exit = True
            exit_reason = "收市前强制平仓"
            # 多头用当日最高价，空头用当日最低价
            if self.position == 1:
                exit_price = max(self.daily_high_price, row['high'])
            else:
                exit_price = min(self.daily_low_price, row['low'])

        # 3. 信号反转平仓
        if self.signal_reversal_exit and not should_exit:
            current_signal = self.get_current_signal(row)
            if (self.position == 1 and current_signal in ['强烈卖出', '卖出']) or \
               (self.position == -1 and current_signal in ['强烈买入', '买入']):
                should_exit = True
                exit_reason = f"信号反转平仓(信号:{current_signal})"
                exit_price = row['close']

        # 4. 追踪止损机制
        if not should_exit:
            current_profit_ratio = 0
            if self.position == 1:
                current_profit_ratio = (row['close'] - self.current_price) / self.current_price
            else:
                current_profit_ratio = (self.current_price - row['close']) / self.current_price

            # 更新最大盈利
            if current_profit_ratio > self.max_profit_achieved:
                self.max_profit_achieved = current_profit_ratio

            # 如果盈利回撤超过追踪止损比例
            if self.max_profit_achieved > 0.01:  # 盈利超过1%才启用追踪止损
                drawdown = self.max_profit_achieved - current_profit_ratio
                if drawdown >= self.trailing_stop_ratio:
                    should_exit = True
                    exit_reason = f"追踪止损(回撤{drawdown*100:.2f}%)"
                    exit_price = row['close']

        if should_exit:
            # 计算盈亏
            if self.position == 1:
                profit = (exit_price - self.current_price) / self.current_price * capital
            else:
                profit = (self.current_price - exit_price) / self.current_price * capital

            capital += profit

            # 重置持仓相关变量
            self.position = 0
            self.position_entry_date = None
            self.max_profit_achieved = 0
            self.daily_high_price = 0
            self.daily_low_price = float('inf')

            return True, capital, exit_reason, exit_price, profit

        return False, capital, exit_reason, 0, 0

    def get_current_signal(self, row):
        """获取当前交易信号（基于EAB数据库的TradingSignal）"""
        # 使用数据库中的TradingSignal字段
        if 'trading_signal' in row and pd.notna(row['trading_signal']):
            signal = row['trading_signal']
            if signal == 1:
                return "买入"
            elif signal == -1:
                return "卖出"
            else:
                return "观望"

        # 备用：基于XYE指标的信号判断
        if row['e_value'] > 0.1 and row['x_value'] > 0.6 and row['y_value'] > 0.7:
            return "强烈买入"
        elif row['e_value'] > 0 and row['x_value'] > 0.45 and row['y_value'] > 0.45:
            return "买入"
        elif row['e_value'] < -0.1 and (row['x_value'] < 0.3 or row['y_value'] < 0.3):
            return "强烈卖出"
        elif (row['y_value'] < 0.3 or row['x_value'] < 0.3 or
              (row['x_value'] > 0.45 and row['y_value'] < 0.35) or
              (row['x_value'] < 0.45 and row['y_value'] > 0.35)):
            return "卖出"
        else:
            return "观望"

    def update_daily_extremes(self, row):
        """更新当日最高最低价"""
        if self.position != 0:
            self.daily_high_price = max(self.daily_high_price, row['high'])
            self.daily_low_price = min(self.daily_low_price, row['low'])




    def run_backtest(self):
        """运行回测（集成不长期持仓机制）"""
        print("\n3. 开始回测...")
        try:
            # 准备结果记录
            self.trades = []
            capital = self.initial_capital
            self.equity_curve = []

            # 计算平均成交量（用于成交量异常检测）
            self.avg_volume = self.df['volume'].rolling(20).mean()

            for i in range(60, len(self.df)):  # 从第60天开始，确保有足够的历史数据
                row = self.df.iloc[i]
                date = row['date']

                # 每月增加资金
                capital = self.add_monthly_capital(date, capital)

                # 更新当日最高最低价（用于收市前平仓）
                self.update_daily_extremes(row)

                # 记录权益
                self.equity_curve.append({
                    'date': date,
                    'equity': capital,
                    'position': self.position
                })

                # 🔥 核心：不长期持仓检查（优先级最高）
                if self.position != 0:
                    should_exit, capital, exit_reason, exit_price, profit = self.check_no_long_term_holding(date, row, capital)

                    if should_exit:
                        self.trades.append({
                            'date': date,
                            'type': f'{"long" if self.position == 1 else "short"}_exit_no_long_term',
                            'price': exit_price,
                            'profit': profit,
                            'capital': capital,
                            'reason': exit_reason
                        })
                        print(f"  📅 {date.date()} - {exit_reason}: {exit_price:.2f} 盈亏:{profit:.2f}")

                # 如果仍有持仓（没有被不长期持仓机制平仓），检查传统止盈止损
                if self.position != 0:
                    if self.position == 1:  # 多头
                        profit_ratio = (row['high'] - self.current_price) / self.current_price
                        loss_ratio = (self.current_price - row['low']) / self.current_price

                        if profit_ratio >= self.take_profit_long:  # 止盈
                            exit_price = self.current_price * (1 + self.take_profit_long)
                            profit = (exit_price - self.current_price) / self.current_price * capital
                            capital += profit
                            self.position = 0
                            self.trades.append({
                                'date': date,
                                'type': 'long_exit_tp',
                                'price': exit_price,
                                'profit': profit,
                                'capital': capital
                            })
                        elif loss_ratio >= self.stop_loss_long:  # 止损
                            exit_price = self.current_price * (1 - self.stop_loss_long)
                            loss = (exit_price - self.current_price) / self.current_price * capital
                            capital += loss
                            self.position = 0
                            self.trades.append({
                                'date': date,
                                'type': 'long_exit_sl',
                                'price': exit_price,
                                'profit': loss,
                                'capital': capital
                            })

                    elif self.position == -1:  # 空头
                        profit_ratio = (self.current_price - row['low']) / self.current_price
                        loss_ratio = (row['high'] - self.current_price) / self.current_price

                        if profit_ratio >= self.take_profit_short:  # 止盈
                            exit_price = self.current_price * (1 - self.take_profit_short)
                            profit = (self.current_price - exit_price) / self.current_price * capital
                            capital += profit
                            self.position = 0
                            self.trades.append({
                                'date': date,
                                'type': 'short_exit_tp',
                                'price': exit_price,
                                'profit': profit,
                                'capital': capital
                            })
                        elif loss_ratio >= self.stop_loss_short:  # 止损
                            exit_price = self.current_price * (1 + self.stop_loss_short)
                            loss = (self.current_price - exit_price) / self.current_price * capital
                            capital += loss
                            self.position = 0
                            self.trades.append({
                                'date': date,
                                'type': 'short_exit_sl',
                                'price': exit_price,
                                'profit': loss,
                                'capital': capital
                            })

                # 如果空仓，判断是否开仓
                if self.position == 0:
                    # 使用EAB数据库的交易信号
                    current_signal = self.get_current_signal(row)

                    # 多头开仓条件：买入信号 + 价格低于回归线
                    if current_signal in ["买入", "强烈买入"] and row['price_position'] < 0:
                        self.position = 1
                        self.current_price = row['close']
                        # 🔥 记录开仓时间和初始化不长期持仓参数
                        self.position_entry_date = date
                        self.max_profit_achieved = 0
                        self.daily_high_price = row['high']
                        self.daily_low_price = row['low']

                        self.trades.append({
                            'date': date,
                            'type': 'long_entry',
                            'price': self.current_price,
                            'capital': capital,
                            'signal': current_signal,
                            'trading_signal': row.get('trading_signal', 0)
                        })
                        print(f"  📈 {date.date()} - 多头开仓: {self.current_price:.2f} (信号:{current_signal})")

                    # 空头开仓条件：卖出信号 + 价格高于回归线
                    elif current_signal in ["卖出", "强烈卖出"] and row['price_position'] > 0:
                        self.position = -1
                        self.current_price = row['close']
                        # 🔥 记录开仓时间和初始化不长期持仓参数
                        self.position_entry_date = date
                        self.max_profit_achieved = 0
                        self.daily_high_price = row['high']
                        self.daily_low_price = row['low']

                        self.trades.append({
                            'date': date,
                            'type': 'short_entry',
                            'price': self.current_price,
                            'capital': capital,
                            'signal': current_signal,
                            'trading_signal': row.get('trading_signal', 0)
                        })
                        print(f"  📉 {date.date()} - 空头开仓: {self.current_price:.2f} (信号:{current_signal})")

            self.final_capital = capital
            print(f"✓ 回测完成！最终资金：{self.final_capital:,.2f}")

            # 分析不长期持仓机制效果
            self.analyze_no_long_term_holding()

        except Exception as e:
            print(f"❌ 回测执行失败: {str(e)}")
            raise

    def analyze_no_long_term_holding(self):
        """分析不长期持仓机制的效果"""
        print("\n📊 不长期持仓机制分析:")

        trades_df = pd.DataFrame(self.trades)
        if len(trades_df) == 0:
            print("   无交易记录")
            return

        # 统计各种平仓原因
        exit_trades = trades_df[trades_df['type'].str.contains('exit')]
        if len(exit_trades) > 0:
            print("   平仓原因统计:")

            # 不长期持仓相关的平仓
            no_long_term_exits = exit_trades[exit_trades['type'].str.contains('no_long_term')]
            if len(no_long_term_exits) > 0:
                reason_counts = no_long_term_exits['reason'].value_counts()
                for reason, count in reason_counts.items():
                    percentage = count / len(exit_trades) * 100
                    print(f"     🔄 {reason}: {count}次 ({percentage:.1f}%)")

            # 传统止盈止损
            tp_exits = len(exit_trades[exit_trades['type'].str.contains('tp')])
            sl_exits = len(exit_trades[exit_trades['type'].str.contains('sl')])

            if tp_exits > 0:
                print(f"     ✅ 传统止盈: {tp_exits}次 ({tp_exits/len(exit_trades)*100:.1f}%)")
            if sl_exits > 0:
                print(f"     ❌ 传统止损: {sl_exits}次 ({sl_exits/len(exit_trades)*100:.1f}%)")

        # 计算平均持仓时间
        entry_trades = trades_df[trades_df['type'].str.contains('entry')]
        if len(entry_trades) > 0 and len(exit_trades) > 0:
            holding_periods = []
            for i in range(min(len(entry_trades), len(exit_trades))):
                if i < len(exit_trades):
                    entry_date = entry_trades.iloc[i]['date']
                    exit_date = exit_trades.iloc[i]['date']
                    holding_days = (exit_date - entry_date).days
                    holding_periods.append(holding_days)

            if holding_periods:
                avg_holding = np.mean(holding_periods)
                max_holding = max(holding_periods)
                print(f"   ⏱️  平均持仓时间: {avg_holding:.1f}天")
                print(f"   ⏱️  最长持仓时间: {max_holding}天")
                print(f"   ✅ 成功控制持仓时间在{self.max_holding_days}天以内: {max_holding <= self.max_holding_days}")

        # 分析不长期持仓机制的盈利贡献
        if 'profit' in trades_df.columns:
            no_long_term_profit = no_long_term_exits['profit'].sum() if len(no_long_term_exits) > 0 else 0
            total_profit = exit_trades['profit'].sum() if len(exit_trades) > 0 else 0

            if total_profit != 0:
                contribution = no_long_term_profit / total_profit * 100
                print(f"   💰 不长期持仓机制盈利贡献: {contribution:.1f}%")
                print(f"   💰 不长期持仓机制总盈利: {no_long_term_profit:,.2f}")

        print(f"   🎯 不长期持仓机制有效避免了长期套牢风险！")

    def analyze_results(self):
        """分析回测结果"""
        print("\n4. 回测分析...")
        try:
            # 转换交易记录为DataFrame
            trades_df = pd.DataFrame(self.trades)
            if len(trades_df) == 0:
                print("没有产生任何交易")
                return

            # 计算基本统计数据
            total_trades = len(trades_df[trades_df['type'].str.contains('entry')])
            winning_trades = len(trades_df[trades_df['profit'] > 0]) if 'profit' in trades_df.columns else 0
            profit_trades = trades_df[trades_df['profit'] > 0] if 'profit' in trades_df.columns else pd.DataFrame()
            loss_trades = trades_df[trades_df['profit'] < 0] if 'profit' in trades_df.columns else pd.DataFrame()

            print(f"\n📊 {self.symbol}交易统计：")
            print(f"总交易次数：{total_trades}")
            print(f"盈利交易：{winning_trades}")
            print(f"亏损交易：{total_trades - winning_trades}")
            if total_trades > 0:
                exit_trades = len(trades_df[trades_df['type'].str.contains('exit')])
                if exit_trades > 0:
                    print(f"胜率：{winning_trades/exit_trades*100:.2f}%")

            if len(profit_trades) > 0:
                print(f"平均盈利：{profit_trades['profit'].mean():,.2f}")
                print(f"最大盈利：{profit_trades['profit'].max():,.2f}")

            if len(loss_trades) > 0:
                print(f"平均亏损：{loss_trades['profit'].mean():,.2f}")
                print(f"最大亏损：{loss_trades['profit'].min():,.2f}")

            # 计算收益率
            initial_equity = self.initial_capital
            final_equity = self.final_capital
            total_days = (self.df['date'].max() - self.df['date'].min()).days
            total_years = total_days / 365

            total_return = (final_equity - initial_equity) / initial_equity
            annual_return = (1 + total_return) ** (1/total_years) - 1

            print(f"\n💰 收益统计：")
            print(f"回测期间：{total_years:.1f}年")
            print(f"初始资金：{initial_equity:,.2f}")
            print(f"最终资金：{final_equity:,.2f}")
            print(f"总收益率：{total_return*100:.2f}%")
            print(f"年化收益率：{annual_return*100:.2f}%")

            # 计算复利考虑每月追加资金
            total_additions = (total_days // 30) * self.monthly_addition
            total_invested = initial_equity + total_additions
            actual_return = (final_equity - total_invested) / total_invested
            print(f"总投入资金：{total_invested:,.2f}")
            print(f"考虑每月追加后的实际收益率：{actual_return*100:.2f}%")

            # 买入持有策略对比
            start_price = self.df['close'].iloc[0]
            end_price = self.df['close'].iloc[-1]
            buy_hold_return = (end_price - start_price) / start_price
            buy_hold_final = total_invested * (1 + buy_hold_return)

            print(f"\n🆚 买入持有对比：")
            print(f"买入持有收益率：{buy_hold_return*100:.2f}%")
            print(f"买入持有最终资金：{buy_hold_final:,.2f}")
            print(f"策略超额收益：{final_equity - buy_hold_final:,.2f}")

            # 创建子图
            plt.figure(figsize=(20, 15))

            # 1. 权益曲线（带持仓标记）
            ax1 = plt.subplot(221)
            equity_df = pd.DataFrame(self.equity_curve)
            equity_df['date'] = pd.to_datetime(equity_df['date'])

            # 绘制基础权益曲线
            ax1.plot(equity_df['date'], equity_df['equity'], 'b-', label='权益曲线', linewidth=1.5)

            # 标记多空持仓
            long_periods = equity_df[equity_df['position'] == 1]
            short_periods = equity_df[equity_df['position'] == -1]

            if len(long_periods) > 0:
                ax1.plot(long_periods['date'], long_periods['equity'], 'g.', label='多头', markersize=3)
            if len(short_periods) > 0:
                ax1.plot(short_periods['date'], short_periods['equity'], 'r.', label='空头', markersize=3)

            ax1.set_title('权益曲线与持仓分析')
            ax1.set_xlabel('日期')
            ax1.set_ylabel('资金')
            ax1.grid(True)
            ax1.legend()

            # 2. 月度收益分布
            ax2 = plt.subplot(222)
            monthly_returns = []

            for year in equity_df['date'].dt.year.unique():
                for month in range(1, 13):
                    month_data = equity_df[
                        (equity_df['date'].dt.year == year) &
                        (equity_df['date'].dt.month == month)
                    ]
                    if len(month_data) > 0:
                        start_equity = month_data['equity'].iloc[0]
                        end_equity = month_data['equity'].iloc[-1]
                        monthly_return = (end_equity - start_equity) / start_equity * 100
                        monthly_returns.append(monthly_return)

            ax2.hist(monthly_returns, bins=50, color='blue', alpha=0.7)
            ax2.axvline(x=0, color='r', linestyle='--')
            ax2.set_title('月度收益分布')
            ax2.set_xlabel('收益率 (%)')
            ax2.set_ylabel('频次')

            # 3. 累计收益与回撤分析
            ax3 = plt.subplot(223)
            equity_df['return'] = equity_df['equity'].pct_change()
            equity_df['cum_return'] = (1 + equity_df['return']).cumprod()
            equity_df['cum_roll_max'] = equity_df['cum_return'].cummax()
            equity_df['drawdown'] = equity_df['cum_roll_max'] - equity_df['cum_return']

            ax3.plot(equity_df['date'], equity_df['cum_return'], 'b-', label='累计收益')
            ax3.plot(equity_df['date'], equity_df['cum_roll_max'], 'g--', label='历史新高')
            ax3.fill_between(equity_df['date'],
                           equity_df['cum_return'],
                           equity_df['cum_roll_max'],
                           alpha=0.3,
                           color='red',
                           label='回撤')
            ax3.set_title('累计收益与回撤分析')
            ax3.set_xlabel('日期')
            ax3.set_ylabel('累计收益倍数')
            ax3.grid(True)
            ax3.legend()

            # 4. 交易盈亏分布
            ax4 = plt.subplot(224)
            trades_df['profit'].hist(bins=50, ax=ax4, color='blue', alpha=0.7)
            ax4.axvline(x=0, color='r', linestyle='--')
            ax4.set_title('交易盈亏分布')
            ax4.set_xlabel('盈亏金额')
            ax4.set_ylabel('频次')

            # 调整布局并保存
            plt.tight_layout()
            chart_filename = f'{self.symbol.replace(".", "_")}_分析图表_{datetime.now().strftime("%Y%m%d_%H%M%S")}.png'
            plt.savefig(chart_filename, dpi=300, bbox_inches='tight')
            plt.close()

            # 保存交易记录
            trades_df_save = trades_df.copy()
            if 'date' in trades_df_save.columns:
                trades_df_save['date'] = pd.to_datetime(trades_df_save['date']).dt.tz_localize(None)
            excel_filename = f'{self.symbol.replace(".", "_")}_交易记录_{datetime.now().strftime("%Y%m%d_%H%M%S")}.xlsx'
            trades_df_save.to_excel(excel_filename, index=False)

            print("\n✓ 分析完成")
            print(f"• 交易记录已保存到 {excel_filename}")
            print(f"• 详细分析图表已保存到 {chart_filename}")

        except Exception as e:
            print(f"❌ 分析失败: {str(e)}")
            raise

def main():
    """主函数"""
    print(f"\nEAB_0023HK回测系统（基于Cosmoon XYE策略 + 不长期持仓机制）")
    print("="*60)

    try:
        # 创建回测实例
        backtest = EABBacktest()

        # 加载数据
        backtest.load_data()

        # 计算回归线（用于价格位置判断）
        backtest.calculate_regression_line()

        # 运行回测
        backtest.run_backtest()

        # 分析结果
        backtest.analyze_results()

    except Exception as e:
        print(f"\n❌ 程序运行失败: {str(e)}")
    else:
        print(f"\n✅ {backtest.symbol}回测程序运行完成")

if __name__ == "__main__":
    main()
