@echo off
title Test Daily Update

echo Starting daily update system...
echo.

cd /d "D:\Users\Cosmoon NG\Documents\VSCode\Python\Finance\EAs\Investment02"

echo Current directory: %cd%
echo.

if not exist "complete_daily_update_with_position.py" (
    echo ERROR: Main script not found
    pause
    exit /b 1
)

echo Found main script: complete_daily_update_with_position.py
echo.

python --version
echo.

echo Executing Python script...
echo.

python complete_daily_update_with_position.py

echo.
echo Script execution completed
echo.

pause
