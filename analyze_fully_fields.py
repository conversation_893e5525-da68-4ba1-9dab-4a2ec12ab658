#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析数据库中所有表的Full_Y字段
"""

import pymysql
import pandas as pd
import numpy as np
from datetime import datetime

def get_db_connection():
    """获取数据库连接"""
    return pymysql.connect(
        host='************',
        user='root',
        password='',
        database='finance',
        charset='utf8mb4',
        cursorclass=pymysql.cursors.DictCursor
    )

def get_all_tables(conn):
    """获取所有表名"""
    try:
        with conn.cursor() as cursor:
            cursor.execute("SHOW TABLES")
            return [list(table.values())[0] for table in cursor.fetchall()]
    except Exception as e:
        print(f"获取表信息时出错: {e}")
        return []

def get_tables_with_fully(conn):
    """获取包含Full_Y字段的表"""
    tables_with_fully = []
    try:
        tables = get_all_tables(conn)
        with conn.cursor() as cursor:
            # 检查每个表是否包含Full_Y字段
            for table in tables:
                cursor.execute(f"SHOW COLUMNS FROM `{table}` LIKE 'Full_Y'")
                if cursor.fetchone():
                    tables_with_fully.append(table)
    except Exception as e:
        print(f"获取表信息时出错: {e}")
    
    return tables_with_fully

def add_fully_field(conn, table_name):
    """为表添加Full_Y字段并计算初始值"""
    try:
        with conn.cursor() as cursor:
            # 添加Full_Y字段
            print(f"正在为表 {table_name} 添加Full_Y字段...")
            cursor.execute(f"""
                ALTER TABLE `{table_name}`
                ADD COLUMN IF NOT EXISTS `Full_Y` DECIMAL(15,4) COMMENT '控制系数'
            """)
            
            # 检查是否包含必要的字段（不区分大小写）
            cursor.execute(f"SHOW COLUMNS FROM `{table_name}`")
            columns = [col['Field'] for col in cursor.fetchall()]
            columns_lower = [col.lower() for col in columns]
            
            # 检查收盘价列（不区分大小写）
            close_col = None
            if 'close' in columns_lower:
                close_col = columns[columns_lower.index('close')]
            elif 'Close' in columns:
                close_col = 'Close'
            else:
                print(f"  警告: 表 {table_name} 缺少收盘价字段(close/Close)，无法计算Full_Y")
                return False
                
            # 检查成交量列（不区分大小写）
            use_default_volume = False
            volume_col = None
            if 'volume' in columns_lower:
                volume_col = columns[columns_lower.index('volume')]
            elif 'Volume' in columns:
                volume_col = 'Volume'
            else:
                # 如果没有成交量列，使用默认值1（相当于不考虑成交量影响）
                print(f"  提示: 表 {table_name} 缺少成交量字段(volume/Volume)，将使用默认值")
                use_default_volume = True
            
            # 检查是否已有Full_Y列
            has_fully = 'Full_Y' in columns or 'full_y' in columns_lower
            fully_col = 'Full_Y' if 'Full_Y' in columns else 'full_y'
            
            # 构建查询语句
            if use_default_volume:
                query = f"""
                    SELECT Date, `{close_col}`, 1 as Volume 
                    FROM `{table_name}` 
                    ORDER BY Date
                    FOR UPDATE
                """
            else:
                query = f"""
                    SELECT Date, `{close_col}`, `{volume_col}` as Volume 
                    FROM `{table_name}` 
                    ORDER BY Date
                    FOR UPDATE
                """
            
            cursor.execute(query)
            data = cursor.fetchall()
            
            if not data:
                print(f"  警告: 表 {table_name} 没有数据")
                return False
                
            # 转换为DataFrame以便计算
            import pandas as pd
            df = pd.DataFrame(data)
            df['Date'] = pd.to_datetime(df['Date'])
            df.set_index('Date', inplace=True)
            
            # 计算移动平均
            df['ma20'] = df['Close'].rolling(window=20, min_periods=10).mean()
            df['ma60'] = df['Close'].rolling(window=60, min_periods=30).mean()
            
            # 计算Full_Y
            price_ma20_ratio = df['Close'] / df['ma20']
            price_ma20_ratio = price_ma20_ratio.fillna(1)
            
            # 基础Y值
            mask = price_ma20_ratio >= 1
            base_y = np.where(
                mask,
                0.5 + 0.4 * np.tanh((price_ma20_ratio - 1) * 3),
                0.5 - 0.4 * np.tanh((1 - price_ma20_ratio) * 3)
            )
            
            # 趋势调整
            ma_trend = df['ma20'] / df['ma60']
            ma_trend = ma_trend.fillna(1)
            trend_adj = 0.1 * np.tanh((ma_trend - 1) * 2)
            
            # 成交量调整
            volume_ma20 = df['Volume'].rolling(window=20, min_periods=10).mean()
            volume_ratio = df['Volume'] / volume_ma20
            volume_ratio = volume_ratio.fillna(1)
            vol_adj = 0.05 * np.tanh((volume_ratio - 1))
            
            df['Full_Y'] = (base_y + trend_adj + vol_adj).clip(0.1, 0.9)
            
            # 更新数据库
            print(f"  正在更新表 {table_name} 的{fully_col}值...")
            
            # 构建更新语句
            update_sql = f"""
                UPDATE `{table_name}`
                SET `{fully_col}` = %s
                WHERE Date = %s
            """
            
            # 准备批量更新数据
            update_data = []
            for date, row in df[df['Full_Y'].notna()].iterrows():
                update_data.append((float(row['Full_Y']), date))
            
            # 批量更新
            if update_data:
                cursor.executemany(update_sql, update_data)
            
            conn.commit()
            print(f"  成功更新表 {table_name} 的Full_Y值")
            return True
            
    except Exception as e:
        print(f"  更新表 {table_name} 时出错: {e}")
        conn.rollback()
        return False

def analyze_fully_field(conn, table_name):
    """分析指定表的Full_Y字段"""
    try:
        with conn.cursor() as cursor:
            # 获取总行数
            cursor.execute(f"SELECT COUNT(*) as total FROM `{table_name}`")
            total = cursor.fetchone()['total']
            
            # 获取Full_Y的统计信息
            cursor.execute(f"""
                SELECT 
                    COUNT(Full_Y) as non_null_count,
                    MIN(Full_Y) as min_value,
                    MAX(Full_Y) as max_value,
                    AVG(Full_Y) as avg_value,
                    STD(Full_Y) as std_value
                FROM `{table_name}`
                WHERE Full_Y IS NOT NULL
            """)
            stats = cursor.fetchone()
            
            # 获取Full_Y的分布情况
            cursor.execute(f"""
                SELECT 
                    SUM(CASE WHEN Full_Y < 0.25 THEN 1 ELSE 0 END) as count_lt_025,
                    SUM(CASE WHEN Full_Y >= 0.25 AND Full_Y < 0.333 THEN 1 ELSE 0 END) as count_025_0333,
                    SUM(CASE WHEN Full_Y >= 0.333 AND Full_Y < 0.4 THEN 1 ELSE 0 END) as count_0333_04,
                    SUM(CASE WHEN Full_Y >= 0.4 AND Full_Y <= 0.6 THEN 1 ELSE 0 END) as count_04_06,
                    SUM(CASE WHEN Full_Y > 0.6 AND Full_Y <= 1.0 THEN 1 ELSE 0 END) as count_gt_06
                FROM `{table_name}`
            """)
            distribution = cursor.fetchone()
            
            # 获取最近10条记录的Full_Y值
            cursor.execute(f"""
                SELECT Date, Full_Y 
                FROM `{table_name}` 
                WHERE Full_Y IS NOT NULL 
                ORDER BY Date DESC 
                LIMIT 10
            """)
            recent_values = cursor.fetchall()
            
            return {
                'table_name': table_name,
                'total_rows': total,
                'non_null_count': stats['non_null_count'],
                'null_count': total - stats['non_null_count'],
                'min_value': float(stats['min_value']) if stats['min_value'] is not None else None,
                'max_value': float(stats['max_value']) if stats['max_value'] is not None else None,
                'avg_value': float(stats['avg_value']) if stats['avg_value'] is not None else None,
                'std_value': float(stats['std_value']) if stats['std_value'] is not None else None,
                'distribution': {
                    '<0.25 (强亏损区)': distribution['count_lt_025'],
                    '0.25-0.333': distribution['count_025_0333'],
                    '0.333-0.4 (控股商控制区)': distribution['count_0333_04'],
                    '0.4-0.6 (中性区)': distribution['count_04_06'],
                    '>0.6 (高值区)': distribution['count_gt_06']
                },
                'recent_values': [
                    (row['Date'].strftime('%Y-%m-%d') if isinstance(row['Date'], datetime) else str(row['Date']), 
                     float(row['Full_Y'])) 
                    for row in recent_values
                ]
            }
            
    except Exception as e:
        print(f"分析表 {table_name} 时出错: {e}")
        return None

def generate_report(analysis_results):
    """生成分析报告"""
    report = "# Full_Y 字段分析报告\n"
    report += f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n"
    
    for result in analysis_results:
        if not result:
            continue
            
        report += f"## 表: `{result['table_name']}`\n"
        report += f"- 总行数: {result['total_rows']:,}\n"
        report += f"- 非空值: {result['non_null_count']:,} ({result['non_null_count']/result['total_rows']*100:.1f}%)\n"
        report += f"- 空值: {result['null_count']:,} ({result['null_count']/result['total_rows']*100:.1f}%)\n"
        report += f"- 最小值: {result['min_value']:.4f}\n"
        report += f"- 最大值: {result['max_value']:.4f}\n"
        report += f"- 平均值: {result['avg_value']:.4f} ± {result['std_value']:.4f}\n\n"
        
        report += "### 数值分布\n"
        for range_name, count in result['distribution'].items():
            if result['non_null_count'] > 0:
                percentage = count / result['non_null_count'] * 100
                report += f"- {range_name}: {count:,} 行 ({percentage:.1f}%)\n"
            else:
                report += f"- {range_name}: {count:,} 行\n"
        
        report += "\n### 最近10条记录的Full_Y值\n"
        for date, value in result['recent_values']:
            report += f"- {date}: {value:.4f}\n"
        report += "\n" + "-"*80 + "\n"
    
    return report

def main():
    print("开始处理数据库中的Full_Y字段...")
    
    try:
        # 连接数据库
        conn = get_db_connection()
        print("成功连接到数据库")
        
        # 获取所有表
        all_tables = get_all_tables(conn)
        if not all_tables:
            print("未找到任何表")
            return
            
        # 获取已包含Full_Y字段的表
        tables_with_fully = get_tables_with_fully(conn)
        tables_without_fully = [t for t in all_tables if t not in tables_with_fully]
        
        # 处理没有Full_Y字段或Full_Y为NULL的表
        if tables_without_fully:
            print(f"\n发现 {len(tables_without_fully)} 个表没有Full_Y字段")
            success_count = 0
            
            # 只处理股票数据表，排除其他表
            stock_tables = [t for t in tables_without_fully if t.startswith(('stock_', 'hk')) and not t.endswith('$')]
            print(f"\n将为以下 {len(stock_tables)} 个股票数据表添加Full_Y字段:")
            for i, table in enumerate(stock_tables, 1):
                print(f"{i}. {table}")
            
            for table in stock_tables:
                if add_fully_field(conn, table):
                    success_count += 1
            
            print(f"\n成功为 {success_count}/{len(stock_tables)} 个表添加了Full_Y字段")
        
        # 检查已有Full_Y字段但值为NULL的表
        print("\n检查已有Full_Y字段但值为NULL的表...")
        with conn.cursor() as cursor:
            cursor.execute("""
                SELECT TABLE_NAME 
                FROM INFORMATION_SCHEMA.COLUMNS 
                WHERE TABLE_SCHEMA = DATABASE() 
                AND COLUMN_NAME IN ('Full_Y', 'full_y')
                AND TABLE_NAME NOT IN (
                    SELECT DISTINCT TABLE_NAME 
                    FROM INFORMATION_SCHEMA.COLUMNS 
                    WHERE TABLE_SCHEMA = DATABASE() 
                    AND COLUMN_NAME IN ('Full_Y', 'full_y')
                    AND TABLE_NAME IN (
                        SELECT TABLE_NAME 
                        FROM INFORMATION_SCHEMA.TABLES 
                        WHERE TABLE_SCHEMA = DATABASE()
                        AND TABLE_TYPE = 'BASE TABLE'
                    )
                    GROUP BY TABLE_NAME
                    HAVING COUNT(IF(COLUMN_NAME IN ('Full_Y', 'full_y'), 1, NULL)) > 0
                    AND COUNT(IF(COLUMN_NAME IN ('Close', 'close'), 1, NULL)) > 0
                )
            """)
            tables_with_null_fully = [row['TABLE_NAME'] for row in cursor.fetchall()]
        
        if tables_with_null_fully:
            print(f"\n发现 {len(tables_with_null_fully)} 个表的Full_Y字段为NULL，将重新计算:")
            for i, table in enumerate(tables_with_null_fully, 1):
                print(f"{i}. {table}")
                
            for table in tables_with_null_fully:
                print(f"\n处理表: {table}")
                if add_fully_field(conn, table):
                    print(f"  成功更新表 {table} 的Full_Y值")
        
        # 更新已包含Full_Y字段的表列表
        tables_with_fully = get_tables_with_fully(conn)
        
        # 如果没有包含Full_Y字段的表，则退出
        if not tables_with_fully:
            print("\n没有找到包含Full_Y字段的表")
            return
            
        print(f"\n找到 {len(tables_with_fully)} 个包含Full_Y字段的表: {', '.join(tables_with_fully)}")
        
        # 分析每个表的Full_Y字段
        results = []
        for table in tables_with_fully:
            print(f"\n正在分析表: {table}...")
            result = analyze_fully_field(conn, table)
            if result:
                results.append(result)
        
        # 生成并保存报告
        if results:
            report = generate_report(results)
            report_file = f"Full_Y_Analysis_Report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.md"
            with open(report_file, 'w', encoding='utf-8') as f:
                f.write(report)
            print(f"\n分析完成！报告已保存至: {report_file}")
        
    except Exception as e:
        print(f"\n发生错误: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        if 'conn' in locals() and conn:
            conn.close()

if __name__ == "__main__":
    main()
