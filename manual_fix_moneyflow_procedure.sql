-- 手动修复MoneyFlowRatio存储过程错误
-- =======================================

-- 1. 首先查看问题存储过程
SHOW CREATE PROCEDURE sp_combined_stock_analysis;

-- 2. 备份原存储过程（重命名）
DROP PROCEDURE IF EXISTS sp_combined_stock_analysis_backup_20250720;

-- 获取原存储过程定义并创建备份
-- (需要手动复制SHOW CREATE PROCEDURE的结果，然后修改名称)

-- 3. 删除有问题的存储过程
DROP PROCEDURE IF EXISTS sp_combined_stock_analysis;

-- 4. 创建修复后的存储过程
-- 注意：这里需要根据实际的存储过程逻辑进行修复
-- 主要是将对eab_0023hk表的MoneyFlowRatio引用改为eab_0023hk_moneyflow表

DELIMITER //

CREATE PROCEDURE sp_combined_stock_analysis()
BEGIN
    -- 修复后的存储过程
    -- 如果原来引用eab_0023hk.MoneyFlowRatio，改为：
    
    SELECT 
        h.Date,
        h.Close as HSI_Close,
        h.MoneyFlowRatio as HSI_MoneyFlowRatio,
        e.Close as EAB_Close,
        e.MoneyFlowRatio as EAB_MoneyFlowRatio,
        e.MFI as EAB_MFI,
        e.Y_Value,
        e.X_Value,
        e.TradingSignal
    FROM hkhsi50 h
    LEFT JOIN eab_0023hk_moneyflow e ON h.Date = e.Date
    WHERE h.Date >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
    ORDER BY h.Date DESC;
    
END //

DELIMITER ;

-- 5. 创建安全的MoneyFlow查询存储过程
DELIMITER //

CREATE PROCEDURE sp_get_eab_moneyflow_data(
    IN start_date DATE,
    IN end_date DATE
)
BEGIN
    SELECT 
        Date,
        Close,
        Volume,
        MoneyFlowRatio,
        MFI,
        Y_Value,
        X_Value,
        E_Value,
        TradingSignal
    FROM eab_0023hk_moneyflow
    WHERE Date BETWEEN start_date AND end_date
    ORDER BY Date DESC;
END //

DELIMITER ;

-- 6. 创建MoneyFlow分析存储过程
DELIMITER //

CREATE PROCEDURE sp_analyze_moneyflow_signals(
    IN days_back INT
)
BEGIN
    SELECT 
        TradingSignal,
        COUNT(*) as signal_count,
        AVG(MoneyFlowRatio) as avg_money_flow_ratio,
        AVG(MFI) as avg_mfi,
        AVG(Close) as avg_price
    FROM eab_0023hk_moneyflow
    WHERE Date >= DATE_SUB(CURDATE(), INTERVAL days_back DAY)
    GROUP BY TradingSignal
    ORDER BY TradingSignal;
END //

DELIMITER ;

-- 7. 创建高级MoneyFlow分析存储过程
DELIMITER //

CREATE PROCEDURE sp_moneyflow_trend_analysis(
    IN symbol_type VARCHAR(10) -- 'EAB' 或 'HSI'
)
BEGIN
    IF symbol_type = 'EAB' THEN
        SELECT 
            Date,
            Close,
            MoneyFlowRatio,
            MFI,
            CASE 
                WHEN MoneyFlowRatio > 5.0 THEN '极高'
                WHEN MoneyFlowRatio > 2.0 THEN '高'
                WHEN MoneyFlowRatio > 0.5 THEN '正常'
                WHEN MoneyFlowRatio > 0.1 THEN '低'
                ELSE '极低'
            END as mfr_level,
            CASE 
                WHEN MFI > 80 THEN '超买'
                WHEN MFI > 70 THEN '偏高'
                WHEN MFI < 20 THEN '超卖'
                WHEN MFI < 30 THEN '偏低'
                ELSE '正常'
            END as mfi_level,
            TradingSignal
        FROM eab_0023hk_moneyflow
        WHERE Date >= DATE_SUB(CURDATE(), INTERVAL 90 DAY)
        ORDER BY Date DESC;
        
    ELSEIF symbol_type = 'HSI' THEN
        SELECT 
            Date,
            Close,
            MoneyFlowRatio,
            CASE 
                WHEN MoneyFlowRatio > 5.0 THEN '极高'
                WHEN MoneyFlowRatio > 2.0 THEN '高'
                WHEN MoneyFlowRatio > 0.5 THEN '正常'
                WHEN MoneyFlowRatio > 0.1 THEN '低'
                ELSE '极低'
            END as mfr_level
        FROM hkhsi50
        WHERE Date >= DATE_SUB(CURDATE(), INTERVAL 90 DAY)
        ORDER BY Date DESC;
    END IF;
END //

DELIMITER ;

-- 8. 测试新创建的存储过程
-- 测试EAB MoneyFlow数据查询
CALL sp_get_eab_moneyflow_data('2025-07-01', '2025-07-18');

-- 测试信号分析
CALL sp_analyze_moneyflow_signals(30);

-- 测试趋势分析
CALL sp_moneyflow_trend_analysis('EAB');

-- 9. 验证修复结果
SELECT 'MoneyFlow存储过程修复完成' as status;

-- 10. 显示所有MoneyFlow相关的存储过程
SELECT ROUTINE_NAME, ROUTINE_TYPE, CREATED
FROM information_schema.ROUTINES 
WHERE ROUTINE_SCHEMA = 'finance' 
AND (ROUTINE_NAME LIKE '%moneyflow%' OR ROUTINE_NAME LIKE '%money_flow%')
ORDER BY CREATED DESC;
