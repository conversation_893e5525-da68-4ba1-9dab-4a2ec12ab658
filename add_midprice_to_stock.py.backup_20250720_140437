#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
将sp_averagelineV3函数生成的midPrice加入stock_600036_ss表
=======================================================
连接MySQL数据库，调用存储过程，更新股票数据表
"""

import mysql.connector
import pandas as pd
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

class MidPriceUpdater:
    def __init__(self):
        """初始化数据库连接"""
        self.db_config = {
            'host': '************',
            'database': 'finance',
            'user': 'root',
            'password': '',
            'charset': 'utf8mb4',
            'autocommit': True
        }
        self.connection = None
        self.cursor = None

    def connect_database(self):
        """连接数据库"""
        try:
            print("🔗 连接MySQL数据库...")
            self.connection = mysql.connector.connect(**self.db_config)
            self.cursor = self.connection.cursor()
            print(f"✅ 成功连接到数据库: {self.db_config['host']}/{self.db_config['database']}")
            return True
        except mysql.connector.Error as e:
            print(f"❌ 数据库连接失败: {e}")
            return False

    def check_table_structure(self):
        """检查stock_600036_ss表结构"""
        try:
            print("\n📊 检查stock_600036_ss表结构...")

            # 检查表是否存在
            self.cursor.execute("SHOW TABLES LIKE 'stock_600036_ss'")
            if not self.cursor.fetchone():
                print("❌ 表stock_600036_ss不存在")
                return False

            # 查看表结构
            self.cursor.execute("DESCRIBE stock_600036_ss")
            columns = self.cursor.fetchall()

            print("📋 当前表结构:")
            for col in columns:
                print(f"   • {col[0]} - {col[1]} - {col[2]} - {col[3]}")

            # 检查是否已有midPrice或midprice列
            column_names = [col[0] for col in columns]
            has_midprice = 'midPrice' in column_names
            has_midprice_lower = 'midprice' in column_names

            if has_midprice:
                print("✅ midPrice列已存在")
                self.midprice_column = 'midPrice'
            elif has_midprice_lower:
                print("✅ midprice列已存在，将使用此列")
                self.midprice_column = 'midprice'
                has_midprice = True  # 设为True以跳过添加列的步骤
            else:
                print("⚠️ midPrice列不存在，需要添加")
                self.midprice_column = 'midPrice'

            return True, has_midprice

        except mysql.connector.Error as e:
            print(f"❌ 检查表结构失败: {e}")
            return False, False

    def add_midprice_column(self):
        """添加midPrice列到表中"""
        try:
            print("\n➕ 添加midPrice列...")

            # 添加midPrice列
            alter_sql = """
                ALTER TABLE stock_600036_ss
                ADD COLUMN midPrice DECIMAL(10,4) DEFAULT NULL
                COMMENT 'sp_averagelineV3生成的中位价格'
            """

            self.cursor.execute(alter_sql)
            print("✅ 成功添加midPrice列")
            return True

        except mysql.connector.Error as e:
            print(f"❌ 添加midPrice列失败: {e}")
            return False

    def call_sp_averageline_and_update(self):
        """调用sp_averagelineV3并更新midPrice"""
        try:
            print("\n🔄 调用sp_averagelineV3存储过程...")

            # 首先查看表中有多少数据
            self.cursor.execute("SELECT COUNT(*) FROM stock_600036_ss")
            total_rows = self.cursor.fetchone()[0]
            print(f"📊 stock_600036_ss表共有 {total_rows} 条记录")

            # 尝试调用存储过程
            print("\n🚀 调用sp_averagelineV3存储过程...")

            try:
                self.cursor.callproc('sp_averagelineV3', ['stock_600036_ss'])
                print("✅ 存储过程调用成功")

                # 检查是否有结果集
                for result in self.cursor.stored_results():
                    rows = result.fetchall()
                    if rows:
                        print(f"📊 存储过程返回 {len(rows)} 条结果")
                        for i, row in enumerate(rows[:3]):
                            print(f"   结果 {i+1}: {row}")

                # 检查是否更新了midPrice数据
                check_sql = f"SELECT COUNT(*) FROM stock_600036_ss WHERE {self.midprice_column} IS NOT NULL"
                self.cursor.execute(check_sql)
                updated_count = self.cursor.fetchone()[0]

                if updated_count > 0:
                    print(f"✅ 存储过程已更新 {updated_count} 条记录的midPrice")
                else:
                    print("⚠️ 存储过程未更新midPrice数据，尝试手动计算...")
                    self.manual_calculate_midprice()

            except mysql.connector.Error as e:
                print(f"⚠️ 调用存储过程失败: {e}")
                print("🔧 尝试手动计算midPrice...")
                self.manual_calculate_midprice()

            return True

        except mysql.connector.Error as e:
            print(f"❌ 调用存储过程失败: {e}")
            return False

    def manual_calculate_midprice(self):
        """手动计算midPrice (中位价格 = (high + low) / 2)"""
        try:
            print("🧮 手动计算midPrice...")

            # 获取股票数据
            self.cursor.execute("""
                SELECT date, high, low
                FROM stock_600036_ss
                ORDER BY date ASC
            """)

            data = self.cursor.fetchall()
            if not data:
                print("❌ 没有数据可供计算")
                return False

            print(f"📊 处理 {len(data)} 条数据...")

            # 计算midPrice = (high + low) / 2
            update_count = 0

            for row in data:
                date_val = row[0]
                high_val = float(row[1]) if row[1] is not None else 0.0
                low_val = float(row[2]) if row[2] is not None else 0.0

                if high_val > 0 and low_val > 0:
                    mid_price = (high_val + low_val) / 2.0

                    update_sql = f"""
                        UPDATE stock_600036_ss
                        SET {self.midprice_column} = %s
                        WHERE date = %s
                    """
                    self.cursor.execute(update_sql, (mid_price, date_val))
                    update_count += 1

            print(f"✅ 成功更新 {update_count} 条记录的midPrice")

            # 显示更新结果样本
            sample_sql = f"""
                SELECT date, high, low, {self.midprice_column}, close,
                       ROUND((close - {self.midprice_column}) / {self.midprice_column} * 100, 2) as price_vs_mid_pct
                FROM stock_600036_ss
                WHERE {self.midprice_column} IS NOT NULL
                ORDER BY date DESC
                LIMIT 10
            """
            self.cursor.execute(sample_sql)

            results = self.cursor.fetchall()
            print("\n📊 更新结果样本 (最新10条):")
            print("日期          | 最高   | 最低   | midPrice | 收盘   | 收盘vs中位%")
            print("-" * 75)
            for row in results:
                date_str = str(row[0])
                high_val = float(row[1]) if row[1] is not None else 0.0
                low_val = float(row[2]) if row[2] is not None else 0.0
                mid_val = float(row[3]) if row[3] is not None else 0.0
                close_val = float(row[4]) if row[4] is not None else 0.0
                pct_val = float(row[5]) if row[5] is not None else 0.0
                print(f"{date_str} | {high_val:6.2f} | {low_val:6.2f} | {mid_val:8.2f} | {close_val:6.2f} | {pct_val:8.2f}%")

            return True

        except Exception as e:
            print(f"❌ 手动计算midPrice失败: {e}")
            return False

    def verify_results(self):
        """验证更新结果"""
        try:
            print("\n🔍 验证更新结果...")

            # 统计midPrice数据
            stats_sql = f"""
                SELECT
                    COUNT(*) as total_rows,
                    COUNT({self.midprice_column}) as midprice_rows,
                    MIN({self.midprice_column}) as min_midprice,
                    MAX({self.midprice_column}) as max_midprice,
                    AVG({self.midprice_column}) as avg_midprice
                FROM stock_600036_ss
            """
            self.cursor.execute(stats_sql)

            stats = self.cursor.fetchone()
            print(f"📊 统计结果:")
            print(f"   • 总记录数: {stats[0]}")
            print(f"   • 有midPrice的记录: {stats[1]}")
            print(f"   • midPrice覆盖率: {stats[1]/stats[0]*100:.1f}%")
            if stats[2] is not None:
                print(f"   • midPrice范围: {float(stats[2]):.2f} ~ {float(stats[3]):.2f}")
                print(f"   • midPrice平均值: {float(stats[4]):.2f}")

            return True

        except mysql.connector.Error as e:
            print(f"❌ 验证结果失败: {e}")
            return False

    def close_connection(self):
        """关闭数据库连接"""
        if self.cursor:
            self.cursor.close()
        if self.connection:
            self.connection.close()
        print("🔒 数据库连接已关闭")

    def run(self):
        """执行主流程"""
        print("🎯 开始将midPrice加入stock_600036_ss表")
        print("=" * 60)
        print(f"📅 执行时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"🏠 数据库: {self.db_config['host']}/{self.db_config['database']}")

        try:
            # 1. 连接数据库
            if not self.connect_database():
                return False

            # 2. 检查表结构
            table_exists, has_midprice = self.check_table_structure()
            if not table_exists:
                return False

            # 3. 添加midPrice列(如果不存在)
            if not has_midprice:
                if not self.add_midprice_column():
                    return False

            # 4. 调用存储过程或手动计算
            if not self.call_sp_averageline_and_update():
                return False

            # 5. 验证结果
            if not self.verify_results():
                return False

            print("\n🎉 midPrice成功加入stock_600036_ss表!")
            print("💡 数据更新完成，可以开始使用midPrice进行分析")

            return True

        except Exception as e:
            print(f"❌ 执行失败: {e}")
            return False

        finally:
            self.close_connection()

def main():
    """主函数"""
    updater = MidPriceUpdater()
    success = updater.run()

    if success:
        print("\n✅ 任务完成!")
    else:
        print("\n❌ 任务失败!")

if __name__ == "__main__":
    main()
