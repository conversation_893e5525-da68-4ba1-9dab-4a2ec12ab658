#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import mysql.connector

def create_monitor_table():
    """创建实时监控表"""
    
    config = {
        'host': 'localhost',
        'port': 3306,
        'user': 'root',
        'password': '12345678',
        'database': 'finance'
    }
    
    try:
        connection = mysql.connector.connect(**config)
        cursor = connection.cursor()
        
        print("连接到finance数据库成功")
        
        # 简单的表结构
        create_sql = """
        CREATE TABLE IF NOT EXISTS realtime_monitor_0023hk (
            id INT AUTO_INCREMENT PRIMARY KEY,
            timestamp DATETIME NOT NULL,
            price DECIMAL(10,4) NOT NULL,
            open_price DECIMAL(10,4) DEFAULT 0,
            high_price DECIMAL(10,4) DEFAULT 0,
            low_price DECIMAL(10,4) DEFAULT 0,
            volume BIGINT DEFAULT 0,
            previous_close DECIMAL(10,4) DEFAULT 0,
            ma5 DECIMAL(10,4) DEFAULT 0,
            ma10 DECIMAL(10,4) DEFAULT 0,
            ma20 DECIMAL(10,4) DEFAULT 0,
            rsi DECIMAL(8,4) DEFAULT 50,
            bb_upper DECIMAL(10,4) DEFAULT 0,
            bb_middle DECIMAL(10,4) DEFAULT 0,
            bb_lower DECIMAL(10,4) DEFAULT 0,
            trading_signal VARCHAR(255) DEFAULT '',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        """
        
        cursor.execute(create_sql)
        connection.commit()
        
        print("realtime_monitor_0023hk表创建成功")
        
        # 检查表
        cursor.execute("SHOW TABLES LIKE 'realtime_monitor_0023hk'")
        result = cursor.fetchone()
        
        if result:
            print("表已存在，可以开始监控")
        else:
            print("表创建失败")
        
        cursor.close()
        connection.close()
        
        return True
        
    except Exception as e:
        print(f"创建表失败: {e}")
        return False

if __name__ == "__main__":
    create_monitor_table()
