#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
EAB_0023HK历史数据回测系统 V2.0
基于真实数据库数据，集成最新的双XYE系统和复利计算
"""

import pandas as pd
import numpy as np
import mysql.connector
from datetime import datetime
import matplotlib.pyplot as plt
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class EABBacktestV2:
    def __init__(self):
        """初始化回测系统"""
        self.initial_capital = 10000
        self.transaction_cost = 0.001  # 0.1%
        self.position_ratio = 0.8      # 80%仓位
        self.max_holding_days = 3      # 最大持仓天数
        
        # 当前状态
        self.position = 0  # 0=空仓, 1=多头, -1=空头
        self.position_size = 0
        self.entry_price = 0
        self.entry_date = None
        self.current_capital = self.initial_capital
        
        # 记录
        self.trades = []
        self.equity_curve = []
        
    def load_data(self):
        """从数据库加载数据"""
        print("📊 加载EAB_0023HK历史数据...")
        
        try:
            config = {
                'host': 'localhost',
                'port': 3306,
                'user': 'root',
                'password': '12345678',
                'database': 'finance',
                'charset': 'utf8mb4'
            }
            
            conn = mysql.connector.connect(**config)
            cursor = conn.cursor()
            
            query = """
            SELECT 
                Date, Close, High, Low, Volume,
                Y_Value, X_Value, E_Value, 
                Full_Y, E as E2, Controller,
                RSI, MFI, midprice
            FROM eab_0023hk 
            WHERE Date IS NOT NULL 
            AND Close IS NOT NULL
            ORDER BY Date ASC
            """
            
            cursor.execute(query)
            results = cursor.fetchall()
            
            columns = ['date', 'close', 'high', 'low', 'volume',
                      'y_value', 'x_value', 'e_value', 
                      'full_y', 'e2', 'controller',
                      'rsi', 'mfi', 'midprice']
            
            self.df = pd.DataFrame(results, columns=columns)
            self.df['date'] = pd.to_datetime(self.df['date'])
            
            # 转换数值列
            numeric_cols = ['close', 'high', 'low', 'volume', 'y_value', 'x_value', 
                           'e_value', 'full_y', 'e2', 'controller', 'rsi', 'mfi', 'midprice']
            
            for col in numeric_cols:
                if col in self.df.columns:
                    self.df[col] = pd.to_numeric(self.df[col], errors='coerce')
            
            cursor.close()
            conn.close()
            
            print(f"✅ 加载 {len(self.df)} 条数据")
            print(f"📅 {self.df['date'].min().date()} 到 {self.df['date'].max().date()}")
            print(f"💰 价格: {self.df['close'].min():.2f} - {self.df['close'].max():.2f}")
            
            return True
            
        except Exception as e:
            print(f"❌ 数据加载失败: {e}")
            return False
    
    def calculate_position_size(self, capital, price):
        """复利仓位计算"""
        available = capital * self.position_ratio
        size = int(available / price)
        return max(size, 100)  # 最小100股
    
    def generate_signal(self, row):
        """双XYE系统信号生成"""
        # 系统1: Y_Value, X_Value, E_Value
        y1, x1, e1 = row['y_value'], row['x_value'], row['e_value']
        
        # 系统2: Full_Y, E2, Controller  
        y2, e2, controller = row['full_y'], row['e2'], row['controller']
        
        # 价格偏离
        deviation = 0
        if pd.notna(row['midprice']) and row['midprice'] > 0:
            deviation = (row['close'] - row['midprice']) / row['midprice']
        
        # 系统1信号
        signal1 = "观望"
        if pd.notna(e1) and pd.notna(y1):
            if e1 > 0 and y1 > 0.6:
                signal1 = "买入"
            elif e1 < -0.1 and y1 < 0.4:
                signal1 = "卖出"
        
        # 系统2信号
        signal2 = "观望"
        if pd.notna(controller):
            if controller == 1 and deviation < -0.1:  # 买入信号且价格低
                signal2 = "买入"
            elif controller == 0 and deviation > 0.1:  # 中性但价格高
                signal2 = "卖出"
        
        # 综合判断
        if signal1 == "买入" and signal2 == "买入":
            return {"signal": "买入", "strength": 4}
        elif signal1 == "买入" or signal2 == "买入":
            return {"signal": "买入", "strength": 2}
        elif signal1 == "卖出" and signal2 == "卖出":
            return {"signal": "卖出", "strength": 4}
        elif signal1 == "卖出" or signal2 == "卖出":
            return {"signal": "卖出", "strength": 2}
        else:
            return {"signal": "观望", "strength": 0}
    
    def check_exit(self, row, signal):
        """检查平仓条件"""
        if self.position == 0:
            return False, ""
        
        # 时间止损
        if self.entry_date:
            days = (row['date'] - self.entry_date).days
            if days >= self.max_holding_days:
                return True, f"时间止损({days}天)"
        
        # 盈亏止损
        pnl_ratio = 0
        if self.position == 1:
            pnl_ratio = (row['close'] - self.entry_price) / self.entry_price
        else:
            pnl_ratio = (self.entry_price - row['close']) / self.entry_price
        
        if pnl_ratio >= 0.02:
            return True, "止盈"
        elif pnl_ratio <= -0.015:
            return True, "止损"
        
        # 信号反转
        if signal['strength'] >= 3:
            if (self.position == 1 and signal['signal'] == "卖出") or \
               (self.position == -1 and signal['signal'] == "买入"):
                return True, "信号反转"
        
        return False, ""
    
    def execute_trade(self, row, action, reason=""):
        """执行交易"""
        price = row['close']
        
        if action == "开多":
            self.position_size = self.calculate_position_size(self.current_capital, price)
            cost = self.position_size * price * (1 + self.transaction_cost)
            self.current_capital -= cost
            self.position = 1
            self.entry_price = price
            self.entry_date = row['date']
            
        elif action == "开空":
            self.position_size = self.calculate_position_size(self.current_capital, price)
            revenue = self.position_size * price * (1 - self.transaction_cost)
            self.current_capital += revenue
            self.position = -1
            self.entry_price = price
            self.entry_date = row['date']
            
        elif action == "平仓":
            if self.position == 1:  # 平多
                revenue = self.position_size * price * (1 - self.transaction_cost)
                self.current_capital += revenue
                pnl = (price - self.entry_price) * self.position_size
            else:  # 平空
                cost = self.position_size * price * (1 + self.transaction_cost)
                self.current_capital -= cost
                pnl = (self.entry_price - price) * self.position_size
            
            self.trades.append({
                'date': row['date'],
                'action': f'平{"多" if self.position == 1 else "空"}',
                'price': price,
                'pnl': pnl - self.position_size * self.entry_price * self.transaction_cost * 2,
                'reason': reason
            })
            
            self.position = 0
            self.position_size = 0
            self.entry_price = 0
            self.entry_date = None
        
        if action in ["开多", "开空"]:
            self.trades.append({
                'date': row['date'],
                'action': action,
                'price': price,
                'size': self.position_size,
                'reason': reason
            })
    
    def run_backtest(self):
        """运行回测"""
        print("\n🚀 开始回测...")
        
        for i, row in self.df.iterrows():
            # 记录权益
            total_value = self.current_capital
            if self.position != 0:
                position_value = self.position_size * row['close']
                if self.position == 1:
                    total_value += position_value
                else:
                    total_value += (self.position_size * self.entry_price * 2 - position_value)
            
            self.equity_curve.append({
                'date': row['date'],
                'equity': total_value,
                'position': self.position
            })
            
            # 生成信号
            signal = self.generate_signal(row)
            
            # 检查平仓
            should_exit, reason = self.check_exit(row, signal)
            if should_exit:
                self.execute_trade(row, "平仓", reason)
            
            # 检查开仓 (只在强信号时)
            if self.position == 0 and signal['strength'] >= 4:
                if signal['signal'] == "买入":
                    self.execute_trade(row, "开多", f"双系统买入(强度{signal['strength']})")
                elif signal['signal'] == "卖出":
                    self.execute_trade(row, "开空", f"双系统卖出(强度{signal['strength']})")
        
        # 强制平仓
        if self.position != 0:
            self.execute_trade(self.df.iloc[-1], "平仓", "回测结束")
        
        print(f"✅ 回测完成")
        print(f"📊 交易次数: {len([t for t in self.trades if '开' in t['action']])}")
        print(f"💰 最终资金: {self.current_capital:,.2f}")
        
    def analyze_results(self):
        """分析结果"""
        print("\n📊 回测分析")
        print("=" * 40)
        
        if not self.trades:
            print("无交易记录")
            return
        
        # 统计
        entries = [t for t in self.trades if '开' in t['action']]
        exits = [t for t in self.trades if '平' in t['action']]
        
        profitable = len([t for t in exits if t.get('pnl', 0) > 0])
        
        print(f"交易次数: {len(entries)}")
        print(f"盈利次数: {profitable}")
        print(f"胜率: {profitable/len(exits)*100:.1f}%" if exits else "0%")
        
        # 收益
        total_return = (self.current_capital / self.initial_capital - 1) * 100
        days = (self.df['date'].max() - self.df['date'].min()).days
        annual_return = (1 + total_return/100) ** (365/days) - 1
        
        print(f"\n收益统计:")
        print(f"总收益率: {total_return:+.2f}%")
        print(f"年化收益率: {annual_return*100:+.2f}%")
        
        # 对比买入持有
        buy_hold = (self.df['close'].iloc[-1] / self.df['close'].iloc[0] - 1) * 100
        print(f"买入持有: {buy_hold:+.2f}%")
        print(f"超额收益: {total_return - buy_hold:+.2f}%")
        
        # 保存结果
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        trades_df = pd.DataFrame(self.trades)
        trades_df.to_excel(f"EAB_回测交易_{timestamp}.xlsx", index=False)
        
        equity_df = pd.DataFrame(self.equity_curve)
        equity_df.to_excel(f"EAB_权益曲线_{timestamp}.xlsx", index=False)
        
        print(f"\n✅ 结果已保存")

def main():
    """主函数"""
    print("🏦 EAB_0023HK历史回测系统 V2.0")
    print("=" * 50)
    
    try:
        backtest = EABBacktestV2()
        
        if not backtest.load_data():
            return
        
        backtest.run_backtest()
        backtest.analyze_results()
        
    except Exception as e:
        print(f"❌ 回测失败: {e}")
        import traceback
        traceback.print_exc()
    else:
        print("\n🎉 回测完成！")

if __name__ == "__main__":
    main()
