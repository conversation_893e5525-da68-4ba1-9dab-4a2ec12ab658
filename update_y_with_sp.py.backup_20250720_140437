#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
使用存储过程更新Y值
==================

调用存储过程 sp_stock_ansisyle("hkhsi50") 来计算和更新Y值
- 连接MariaDB数据库
- 调用存储过程
- 验证Y值更新结果
- 显示更新统计

作者: Cosmoon NG
日期: 2025年7月
"""

import pymysql
from datetime import datetime
import pandas as pd

# 数据库配置
db_config = {
    'host': '************',
    'user': 'root',
    'password': '',
    'database': 'finance',
    'charset': 'utf8mb4'
}

def check_stored_procedure():
    """检查存储过程是否存在"""
    print("🔍 检查存储过程 sp_stock_ansisyle...")
    
    try:
        conn = pymysql.connect(**db_config)
        cursor = conn.cursor()
        
        # 检查存储过程是否存在
        cursor.execute("""
            SELECT ROUTINE_NAME, ROUTINE_TYPE, CREATED, LAST_ALTERED 
            FROM information_schema.ROUTINES 
            WHERE ROUTINE_SCHEMA = 'finance' 
            AND ROUTINE_NAME = 'sp_stock_ansisyle'
        """)
        
        result = cursor.fetchone()
        
        if result:
            print(f"✅ 找到存储过程:")
            print(f"   • 名称: {result[0]}")
            print(f"   • 类型: {result[1]}")
            print(f"   • 创建时间: {result[2]}")
            print(f"   • 最后修改: {result[3]}")
            
            # 获取存储过程定义
            cursor.execute("SHOW CREATE PROCEDURE sp_stock_ansisyle")
            proc_def = cursor.fetchone()
            if proc_def:
                print(f"\n📋 存储过程定义:")
                print(f"   • 参数: 表名 (如 'hkhsi50')")
                print(f"   • 功能: 计算股票技术分析指标")
        else:
            print("❌ 未找到存储过程 sp_stock_ansisyle")
            return False
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 检查存储过程失败: {e}")
        return False

def check_before_update():
    """更新前检查数据状态"""
    print("\n📊 更新前数据状态检查...")
    
    try:
        conn = pymysql.connect(**db_config)
        cursor = conn.cursor()
        
        # 检查表是否存在
        cursor.execute("SHOW TABLES LIKE 'hkhsi50'")
        if not cursor.fetchone():
            print("❌ hkhsi50表不存在！")
            return False
        
        # 检查记录数
        cursor.execute("SELECT COUNT(*) FROM hkhsi50")
        total_count = cursor.fetchone()[0]
        
        # 检查Y值相关字段
        cursor.execute("""
            SELECT COUNT(*) FROM hkhsi50 
            WHERE y_value IS NOT NULL
        """)
        y_count = cursor.fetchone()[0]
        
        # 检查日期范围
        cursor.execute("SELECT MIN(date), MAX(date) FROM hkhsi50")
        date_range = cursor.fetchone()
        
        print(f"📈 当前数据状态:")
        print(f"   • 总记录数: {total_count}")
        print(f"   • 有Y值记录: {y_count}")
        print(f"   • 日期范围: {date_range[0]} 至 {date_range[1]}")
        print(f"   • Y值覆盖率: {y_count/total_count*100:.1f}%")
        
        # 显示最近几条记录的Y值
        cursor.execute("""
            SELECT date, close, y_value 
            FROM hkhsi50 
            ORDER BY date DESC 
            LIMIT 5
        """)
        
        recent_data = cursor.fetchall()
        print(f"\n📋 最近5条记录的Y值:")
        print(f"{'日期':<12} {'收盘价':<10} {'Y值':<8}")
        print("-" * 35)
        for row in recent_data:
            y_val = f"{row[2]:.3f}" if row[2] is not None else "NULL"
            print(f"{row[0]:<12} {row[1]:<10.2f} {y_val:<8}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 检查数据状态失败: {e}")
        return False

def call_stored_procedure():
    """调用存储过程更新Y值"""
    print("\n🚀 调用存储过程 sp_stock_ansisyle('hkhsi50')...")
    
    try:
        conn = pymysql.connect(**db_config)
        cursor = conn.cursor()
        
        # 记录开始时间
        start_time = datetime.now()
        print(f"   • 开始时间: {start_time.strftime('%Y-%m-%d %H:%M:%S')}")
        
        # 调用存储过程
        cursor.callproc('sp_stock_ansisyle', ['hkhsi50'])
        
        # 获取存储过程的结果（如果有）
        results = []
        try:
            for result in cursor.stored_results():
                results.extend(result.fetchall())
        except:
            pass  # 有些存储过程可能不返回结果集
        
        # 提交事务
        conn.commit()
        
        # 记录结束时间
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        
        print(f"   • 结束时间: {end_time.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"   • 执行耗时: {duration:.2f} 秒")
        
        if results:
            print(f"   • 存储过程返回: {len(results)} 条结果")
        
        print("✅ 存储过程执行完成")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 调用存储过程失败: {e}")
        return False

def check_after_update():
    """更新后检查数据状态"""
    print("\n📊 更新后数据状态检查...")
    
    try:
        conn = pymysql.connect(**db_config)
        cursor = conn.cursor()
        
        # 检查Y值更新情况
        cursor.execute("""
            SELECT COUNT(*) FROM hkhsi50 
            WHERE y_value IS NOT NULL
        """)
        y_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM hkhsi50")
        total_count = cursor.fetchone()[0]
        
        # 检查Y值统计
        cursor.execute("""
            SELECT 
                MIN(y_value) as min_y,
                MAX(y_value) as max_y,
                AVG(y_value) as avg_y,
                STDDEV(y_value) as std_y
            FROM hkhsi50 
            WHERE y_value IS NOT NULL
        """)
        
        stats = cursor.fetchone()
        
        print(f"📈 更新后数据状态:")
        print(f"   • 总记录数: {total_count}")
        print(f"   • 有Y值记录: {y_count}")
        print(f"   • Y值覆盖率: {y_count/total_count*100:.1f}%")
        
        if stats and stats[0] is not None:
            print(f"\n📊 Y值统计:")
            print(f"   • 最小值: {stats[0]:.4f}")
            print(f"   • 最大值: {stats[1]:.4f}")
            print(f"   • 平均值: {stats[2]:.4f}")
            print(f"   • 标准差: {stats[3]:.4f}")
        
        # 显示最新的Y值
        cursor.execute("""
            SELECT date, close, y_value, x_value, e_value
            FROM hkhsi50 
            WHERE y_value IS NOT NULL
            ORDER BY date DESC 
            LIMIT 10
        """)
        
        recent_data = cursor.fetchall()
        print(f"\n📋 最新10条记录:")
        print(f"{'日期':<12} {'收盘价':<10} {'Y值':<8} {'X值':<8} {'E值':<8}")
        print("-" * 55)
        for row in recent_data:
            y_val = f"{row[2]:.3f}" if row[2] is not None else "NULL"
            x_val = f"{row[3]:.3f}" if row[3] is not None else "NULL"
            e_val = f"{row[4]:.3f}" if row[4] is not None else "NULL"
            print(f"{row[0]:<12} {row[1]:<10.2f} {y_val:<8} {x_val:<8} {e_val:<8}")
        
        # 检查策略区域分布
        cursor.execute("""
            SELECT 
                CASE 
                    WHEN y_value > 0.4 AND x_value > 0.4 THEN '高值盈利区'
                    WHEN y_value > 0.333 AND y_value < 0.4 THEN '控股商控制区'
                    WHEN y_value < 0.25 OR x_value < 0.25 THEN '强亏损区'
                    ELSE '其他区域'
                END AS 策略区域,
                COUNT(*) AS 数量
            FROM hkhsi50 
            WHERE y_value IS NOT NULL AND x_value IS NOT NULL
            GROUP BY 策略区域
            ORDER BY 数量 DESC
        """)
        
        zone_data = cursor.fetchall()
        if zone_data:
            print(f"\n🎯 策略区域分布:")
            for zone, count in zone_data:
                percentage = count / y_count * 100 if y_count > 0 else 0
                print(f"   • {zone}: {count} 条 ({percentage:.1f}%)")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 检查更新后状态失败: {e}")
        return False

def export_updated_data():
    """导出更新后的数据"""
    print("\n💾 导出更新后的数据...")
    
    try:
        conn = pymysql.connect(**db_config)
        
        # 查询最新数据
        query = """
            SELECT date, open, high, low, close, volume,
                   ma20, ma60, rsi, x_value, y_value, e_value
            FROM hkhsi50 
            WHERE y_value IS NOT NULL
            ORDER BY date DESC
            LIMIT 100
        """
        
        df = pd.read_sql(query, conn)
        
        # 保存到Excel
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"hkhsi50_updated_y_values_{timestamp}.xlsx"
        
        df.to_excel(filename, index=False)
        
        print(f"✅ 数据已导出到: {filename}")
        print(f"   • 导出记录数: {len(df)}")
        
        conn.close()
        return filename
        
    except Exception as e:
        print(f"❌ 导出数据失败: {e}")
        return None

def main():
    """主函数"""
    print("🎯 使用存储过程更新hkhsi50的Y值")
    print("="*50)
    print("📋 将调用: sp_stock_ansisyle('hkhsi50')")
    
    # 1. 检查存储过程
    if not check_stored_procedure():
        return
    
    # 2. 检查更新前状态
    if not check_before_update():
        return
    
    # 3. 确认执行
    print(f"\n⚠️  即将调用存储过程更新Y值")
    confirm = input("是否继续？(y/N): ").lower().strip()
    if confirm != 'y':
        print("❌ 操作已取消")
        return
    
    # 4. 调用存储过程
    if not call_stored_procedure():
        return
    
    # 5. 检查更新后状态
    if not check_after_update():
        return
    
    # 6. 导出数据
    export_updated_data()
    
    print(f"\n🎉 Y值更新完成！")
    print(f"💡 存储过程 sp_stock_ansisyle('hkhsi50') 已成功执行")

if __name__ == "__main__":
    main()
