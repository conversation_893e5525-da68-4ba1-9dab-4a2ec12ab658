#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复hkhsi50表列名问题
====================

存储过程需要的列名与实际表结构不匹配
需要添加或重命名列以匹配存储过程的期望

作者: Cosmoon NG
日期: 2025年7月
"""

import pymysql

# 数据库配置
db_config = {
    'host': 'localhost',
    'user': 'root',
    'password': '12345678',
    'database': 'finance',
    'charset': 'utf8mb4'
}

def check_procedure_requirements():
    """检查存储过程需要的列"""
    print("🔍 检查存储过程需要的列...")
    
    try:
        conn = pymysql.connect(**db_config)
        cursor = conn.cursor()
        
        # 获取存储过程定义
        cursor.execute("SHOW CREATE PROCEDURE sp_stock_analysis_with_row_coefficients")
        result = cursor.fetchone()
        
        if result:
            procedure_body = result[2]  # 存储过程的SQL定义
            
            # 查找可能需要的列名
            required_columns = []
            if '控制系数' in procedure_body:
                required_columns.append('控制系数')
            if '资金流比例' in procedure_body:
                required_columns.append('资金流比例')
            if 'MyY' in procedure_body:
                required_columns.append('MyY')
            if 'MyX' in procedure_body:
                required_columns.append('MyX')
            
            print(f"📋 存储过程可能需要的列:")
            for col in required_columns:
                print(f"   • {col}")
            
            # 显示部分存储过程内容（前500字符）
            print(f"\n📄 存储过程部分内容:")
            print(procedure_body[:500] + "..." if len(procedure_body) > 500 else procedure_body)
            
            return required_columns
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 检查存储过程失败: {e}")
        return []

def check_current_columns():
    """检查当前表的列"""
    print("\n📊 检查当前hkhsi50表的列...")
    
    try:
        conn = pymysql.connect(**db_config)
        cursor = conn.cursor()
        
        cursor.execute("DESCRIBE hkhsi50")
        columns = cursor.fetchall()
        
        current_columns = [col[0] for col in columns]
        
        print(f"📋 当前表的列:")
        for col in current_columns:
            print(f"   • {col}")
        
        conn.close()
        return current_columns
        
    except Exception as e:
        print(f"❌ 检查表列失败: {e}")
        return []

def add_missing_columns():
    """添加缺失的列"""
    print("\n🔧 添加缺失的列...")
    
    try:
        conn = pymysql.connect(**db_config)
        cursor = conn.cursor()
        
        # 需要添加的列
        columns_to_add = [
            ('控制系数', 'DECIMAL(15,4)'),
            ('资金流比例', 'DECIMAL(15,4)'),
            ('MyY', 'DECIMAL(15,4)'),
            ('MyX', 'DECIMAL(15,4)')
        ]
        
        for col_name, col_type in columns_to_add:
            try:
                # 检查列是否已存在
                cursor.execute(f"SELECT COUNT(*) FROM information_schema.COLUMNS WHERE TABLE_SCHEMA='finance' AND TABLE_NAME='hkhsi50' AND COLUMN_NAME='{col_name}'")
                exists = cursor.fetchone()[0] > 0
                
                if not exists:
                    sql = f"ALTER TABLE hkhsi50 ADD COLUMN `{col_name}` {col_type}"
                    cursor.execute(sql)
                    print(f"✅ 添加列: {col_name} ({col_type})")
                else:
                    print(f"ℹ️  列已存在: {col_name}")
                    
            except Exception as e:
                print(f"❌ 添加列 {col_name} 失败: {e}")
        
        conn.commit()
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 添加列失败: {e}")
        return False

def update_column_mappings():
    """更新列映射"""
    print("\n🔄 更新列映射...")
    
    try:
        conn = pymysql.connect(**db_config)
        cursor = conn.cursor()
        
        # 将现有的y_value映射到控制系数
        cursor.execute("UPDATE hkhsi50 SET `控制系数` = y_value WHERE y_value IS NOT NULL")
        y_updated = cursor.rowcount
        
        # 将现有的x_value映射到资金流比例
        cursor.execute("UPDATE hkhsi50 SET `资金流比例` = x_value WHERE x_value IS NOT NULL")
        x_updated = cursor.rowcount
        
        # 复制Y值到MyY
        cursor.execute("UPDATE hkhsi50 SET MyY = y_value WHERE y_value IS NOT NULL")
        myy_updated = cursor.rowcount
        
        # 复制X值到MyX
        cursor.execute("UPDATE hkhsi50 SET MyX = x_value WHERE x_value IS NOT NULL")
        myx_updated = cursor.rowcount
        
        conn.commit()
        
        print(f"✅ 列映射更新完成:")
        print(f"   • 控制系数: {y_updated} 条记录")
        print(f"   • 资金流比例: {x_updated} 条记录")
        print(f"   • MyY: {myy_updated} 条记录")
        print(f"   • MyX: {myx_updated} 条记录")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 更新列映射失败: {e}")
        return False

def verify_columns():
    """验证列添加结果"""
    print("\n✅ 验证列添加结果...")
    
    try:
        conn = pymysql.connect(**db_config)
        cursor = conn.cursor()
        
        # 检查新列的数据
        new_columns = ['控制系数', '资金流比例', 'MyY', 'MyX']
        
        for col in new_columns:
            try:
                cursor.execute(f"SELECT COUNT(*) FROM hkhsi50 WHERE `{col}` IS NOT NULL")
                count = cursor.fetchone()[0]
                
                cursor.execute(f"SELECT MIN(`{col}`), MAX(`{col}`), AVG(`{col}`) FROM hkhsi50 WHERE `{col}` IS NOT NULL")
                stats = cursor.fetchone()
                
                print(f"📊 {col}:")
                print(f"   • 非空记录: {count}")
                if stats and stats[0] is not None:
                    print(f"   • 范围: {stats[0]:.4f} - {stats[1]:.4f}")
                    print(f"   • 平均值: {stats[2]:.4f}")
                
            except Exception as e:
                print(f"❌ 检查列 {col} 失败: {e}")
        
        # 显示最新几条记录
        cursor.execute("""
            SELECT date, close, `控制系数`, `资金流比例`, MyY, MyX
            FROM hkhsi50 
            WHERE `控制系数` IS NOT NULL
            ORDER BY date DESC 
            LIMIT 5
        """)
        
        recent_data = cursor.fetchall()
        if recent_data:
            print(f"\n📋 最新5条记录:")
            print(f"{'日期':<12} {'收盘价':<10} {'控制系数':<10} {'资金流比例':<12} {'MyY':<8} {'MyX':<8}")
            print("-" * 70)
            for row in recent_data:
                print(f"{row[0]:<12} {row[1]:<10.2f} {row[2]:<10.4f} {row[3]:<12.4f} {row[4]:<8.4f} {row[5]:<8.4f}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False

def test_procedure_again():
    """再次测试存储过程"""
    print("\n🧪 再次测试存储过程...")
    
    try:
        conn = pymysql.connect(**db_config)
        cursor = conn.cursor()
        
        print("   • 调用存储过程...")
        cursor.callproc('sp_stock_analysis_with_row_coefficients', ['hkhsi50'])
        
        # 获取结果
        for result in cursor.stored_results():
            rows = result.fetchall()
            for row in rows:
                print(f"   • 存储过程输出: {row}")
        
        conn.commit()
        print("✅ 存储过程执行成功")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 存储过程仍然失败: {e}")
        return False

def main():
    """主函数"""
    print("🔧 修复hkhsi50表列名问题")
    print("="*50)
    
    # 1. 检查存储过程需要的列
    required_columns = check_procedure_requirements()
    
    # 2. 检查当前表的列
    current_columns = check_current_columns()
    
    # 3. 添加缺失的列
    if not add_missing_columns():
        return
    
    # 4. 更新列映射
    if not update_column_mappings():
        return
    
    # 5. 验证结果
    if not verify_columns():
        return
    
    # 6. 再次测试存储过程
    test_procedure_again()
    
    print(f"\n🎉 列修复完成！")
    print(f"💡 现在可以正常调用 sp_stock_analysis_with_row_coefficients('hkhsi50')")

if __name__ == "__main__":
    main()
