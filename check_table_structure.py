#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pymysql

def get_db_connection():
    """获取数据库连接"""
    return pymysql.connect(
        host='************',
        user='root',
        password='',
        database='finance',
        charset='utf8mb4',
        cursorclass=pymysql.cursors.DictCursor
    )

def check_table_columns(conn, table_name):
    """检查表的列结构"""
    try:
        with conn.cursor() as cursor:
            cursor.execute(f"SHOW COLUMNS FROM `{table_name}`")
            columns = [col['Field'] for col in cursor.fetchall()]
            return columns
    except Exception as e:
        print(f"检查表 {table_name} 结构时出错: {e}")
        return []

def check_table_data(conn, table_name):
    """检查表的数据"""
    try:
        with conn.cursor() as cursor:
            cursor.execute(f"SELECT * FROM `{table_name}` LIMIT 1")
            data = cursor.fetchone()
            return data
    except Exception as e:
        print(f"检查表 {table_name} 数据时出错: {e}")
        return None

def main():
    print("开始检查数据库表结构...")
    
    try:
        # 连接数据库
        conn = get_db_connection()
        print("成功连接到数据库")
        
        # 获取所有表名
        with conn.cursor() as cursor:
            cursor.execute("SHOW TABLES")
            tables = [list(table.values())[0] for table in cursor.fetchall()]
        
        print(f"\n数据库中共有 {len(tables)} 个表")
        
        # 检查每个表的结构
        for table in tables:
            print(f"\n检查表: {table}")
            print("-" * 50)
            
            # 检查表结构
            columns = check_table_columns(conn, table)
            if columns:
                print(f"字段: {', '.join(columns)}")
            
            # 检查表数据
            data = check_table_data(conn, table)
            if data:
                print("示例数据:")
                for key, value in data.items():
                    print(f"  {key}: {value}")
            
            print("-" * 50)
        
        print("\n检查完成！")
        
    except Exception as e:
        print(f"\n发生错误: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        if 'conn' in locals() and conn:
            conn.close()

if __name__ == "__main__":
    main()
