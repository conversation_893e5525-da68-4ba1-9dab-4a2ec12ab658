#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
东亚银行(0023.HK)20年回测系统
===========================

基于backtest_hsi50_final.py的Cosmoon XYE策略
针对东亚银行(0023.HK)进行20年历史数据回测

特点：
1. 使用回归中线作为趋势判断
2. 采用Cosmoon XYE方法
3. 每月复利加入3000港元
4. 20年超长期回测验证

作者: Cosmoon NG
日期: 2025年7月
"""

import yfinance as yf
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import matplotlib.pyplot as plt
from scipy import stats
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class EAB20YearBacktest:
    def __init__(self):
        """初始化东亚银行20年回测系统"""
        self.symbol = "0023.HK"
        self.initial_capital = 10000  # 初始资金
        self.monthly_addition = 3000  # 每月追加资金

        # 针对东亚银行优化的参数
        self.take_profit_long = 0.025   # 多头止盈 2.5%
        self.stop_loss_long = 0.015     # 多头止损 1.5%
        self.take_profit_short = 0.015  # 空头止盈 1.5%
        self.stop_loss_short = 0.025    # 空头止损 2.5%

        self.position = 0  # 当前持仓，1为多头，-1为空头，0为空仓
        self.current_price = 0  # 当前持仓价格

    def load_20year_data(self):
        """从Yahoo Finance加载东亚银行20年数据"""
        print(f"\n1. 加载{self.symbol}20年数据...")
        try:
            # 获取20年数据
            end_date = datetime.now()
            start_date = end_date - timedelta(days=20*365)

            print(f"   数据期间: {start_date.date()} 至 {end_date.date()}")

            # 从Yahoo Finance获取数据
            eab = yf.Ticker(self.symbol)
            hist_data = eab.history(start=start_date, end=end_date)

            if hist_data.empty:
                raise ValueError(f"无法获取{self.symbol}的数据")

            # 转换为标准格式
            self.df = pd.DataFrame({
                'date': hist_data.index,
                'open': hist_data['Open'],
                'high': hist_data['High'],
                'low': hist_data['Low'],
                'close': hist_data['Close'],
                'volume': hist_data['Volume']
            })

            # 数据清理
            self.df = self.df.dropna()
            self.df = self.df.sort_values('date').reset_index(drop=True)

            print(f"✓ 加载了 {len(self.df)} 条数据")
            print(f"实际数据范围：{self.df['date'].min().date()} 至 {self.df['date'].max().date()}")
            print(f"起始价格：{self.df['close'].iloc[0]:.2f} 港元")
            print(f"最新价格：{self.df['close'].iloc[-1]:.2f} 港元")
            print(f"20年价格变化：{((self.df['close'].iloc[-1] / self.df['close'].iloc[0]) - 1) * 100:.2f}%")

        except Exception as e:
            print(f"❌ 数据加载失败: {str(e)}")
            raise

    def calculate_xye_indicators(self):
        """计算Cosmoon XYE指标"""
        print("\n2. 计算Cosmoon XYE指标...")
        try:
            # 计算Y指标 (价格在20日区间的位置)
            window = 20
            self.df['high_20'] = self.df['high'].rolling(window).max()
            self.df['low_20'] = self.df['low'].rolling(window).min()
            self.df['y_value'] = (self.df['close'] - self.df['low_20']) / (self.df['high_20'] - self.df['low_20'])
            self.df['y_value'] = self.df['y_value'].fillna(0.5).clip(0, 1)

            # 计算X指标 (资金流强度)
            self.df['typical_price'] = (self.df['high'] + self.df['low'] + self.df['close']) / 3
            self.df['money_flow'] = self.df['typical_price'] * self.df['volume']
            self.df['price_change'] = self.df['typical_price'].diff()

            # 正负资金流
            self.df['positive_mf'] = np.where(self.df['price_change'] > 0, self.df['money_flow'], 0)
            self.df['negative_mf'] = np.where(self.df['price_change'] < 0, self.df['money_flow'], 0)

            # 14日资金流比率
            period = 14
            self.df['positive_mf_14'] = self.df['positive_mf'].rolling(period).sum()
            self.df['negative_mf_14'] = self.df['negative_mf'].rolling(period).sum()
            self.df['money_flow_ratio'] = self.df['positive_mf_14'] / (self.df['negative_mf_14'] + 1e-10)

            # MFI和X值
            self.df['mfi'] = 100 - (100 / (1 + self.df['money_flow_ratio']))
            self.df['x_value'] = self.df['mfi'] / 100  # 归一化到0-1

            # 计算E指标 (Cosmoon公式)
            self.df['e_value'] = (8 * self.df['x_value'] - 3) * self.df['y_value'] - 3 * self.df['x_value'] + 1

            print(f"✓ XYE指标计算完成")
            print(f"Y值范围: {self.df['y_value'].min():.4f} - {self.df['y_value'].max():.4f}")
            print(f"X值范围: {self.df['x_value'].min():.4f} - {self.df['x_value'].max():.4f}")
            print(f"E值范围: {self.df['e_value'].min():.4f} - {self.df['e_value'].max():.4f}")

        except Exception as e:
            print(f"❌ XYE指标计算失败: {str(e)}")
            raise

    def calculate_regression_line(self):
        """计算20年回归线"""
        print("\n3. 计算20年回归线...")
        try:
            # 添加序号
            self.df['i'] = range(1, len(self.df) + 1)

            # 计算回归参数
            slope, intercept, r_value, p_value, std_err = stats.linregress(
                self.df['i'], self.df['close']
            )

            # 计算回归线
            self.df['regression_line'] = intercept + slope * self.df['i']

            # 计算价格相对回归线的位置
            self.df['price_position'] = (self.df['close'] - self.df['regression_line']) / self.df['regression_line']

            print(f"✓ 20年回归线计算完成 (R² = {r_value**2:.4f})")
            print(f"回归斜率: {slope:.6f} (每日变化)")
            print(f"年化趋势: {slope*365:.2f} 港元/年")
            print(f"20年总趋势: {slope*365*20:.2f} 港元")

        except Exception as e:
            print(f"❌ 回归线计算失败: {str(e)}")
            raise

    def add_monthly_capital(self, date, capital):
        """每月增加资金"""
        last_month = getattr(self, 'last_month', None)
        current_month = date.replace(day=1)

        if last_month is None or current_month > last_month:
            self.last_month = current_month
            return capital + self.monthly_addition

        return capital

    def run_20year_backtest(self):
        """运行20年回测"""
        print("\n4. 开始20年回测...")
        try:
            # 准备结果记录
            self.trades = []
            capital = self.initial_capital
            self.equity_curve = []

            # 统计变量
            max_capital = capital
            max_drawdown = 0
            monthly_additions_count = 0

            for i in range(60, len(self.df)):  # 从第60天开始，确保有足够的历史数据
                row = self.df.iloc[i]
                date = row['date']

                # 每月增加资金
                old_capital = capital
                capital = self.add_monthly_capital(date, capital)
                if capital > old_capital:
                    monthly_additions_count += 1

                # 计算当前权益
                current_equity = capital
                if self.position != 0:
                    if self.position == 1:  # 多头
                        current_equity += (row['close'] - self.current_price) / self.current_price * capital * 0.8
                    else:  # 空头
                        current_equity += (self.current_price - row['close']) / self.current_price * capital * 0.8

                # 更新最大回撤
                if current_equity > max_capital:
                    max_capital = current_equity

                drawdown = (max_capital - current_equity) / max_capital
                if drawdown > max_drawdown:
                    max_drawdown = drawdown

                # 记录权益
                self.equity_curve.append({
                    'date': date,
                    'equity': current_equity,
                    'position': self.position,
                    'price': row['close'],
                    'drawdown': drawdown
                })

                # 如果有持仓，检查止盈止损
                if self.position != 0:
                    if self.position == 1:  # 多头
                        profit_ratio = (row['high'] - self.current_price) / self.current_price
                        loss_ratio = (self.current_price - row['low']) / self.current_price

                        if profit_ratio >= self.take_profit_long:  # 止盈
                            exit_price = self.current_price * (1 + self.take_profit_long)
                            profit = (exit_price - self.current_price) / self.current_price * capital * 0.8
                            capital += profit
                            self.position = 0
                            self.trades.append({
                                'date': date,
                                'type': 'long_exit_tp',
                                'entry_price': self.current_price,
                                'exit_price': exit_price,
                                'profit': profit,
                                'capital': capital,
                                'reason': '多头止盈'
                            })
                        elif loss_ratio >= self.stop_loss_long:  # 止损
                            exit_price = self.current_price * (1 - self.stop_loss_long)
                            loss = (exit_price - self.current_price) / self.current_price * capital * 0.8
                            capital += loss
                            self.position = 0
                            self.trades.append({
                                'date': date,
                                'type': 'long_exit_sl',
                                'entry_price': self.current_price,
                                'exit_price': exit_price,
                                'profit': loss,
                                'capital': capital,
                                'reason': '多头止损'
                            })

                    elif self.position == -1:  # 空头
                        profit_ratio = (self.current_price - row['low']) / self.current_price
                        loss_ratio = (row['high'] - self.current_price) / self.current_price

                        if profit_ratio >= self.take_profit_short:  # 止盈
                            exit_price = self.current_price * (1 - self.take_profit_short)
                            profit = (self.current_price - exit_price) / self.current_price * capital * 0.8
                            capital += profit
                            self.position = 0
                            self.trades.append({
                                'date': date,
                                'type': 'short_exit_tp',
                                'entry_price': self.current_price,
                                'exit_price': exit_price,
                                'profit': profit,
                                'capital': capital,
                                'reason': '空头止盈'
                            })
                        elif loss_ratio >= self.stop_loss_short:  # 止损
                            exit_price = self.current_price * (1 + self.stop_loss_short)
                            loss = (exit_price - self.current_price) / self.current_price * capital * 0.8 * -1
                            capital += loss
                            self.position = 0
                            self.trades.append({
                                'date': date,
                                'type': 'short_exit_sl',
                                'entry_price': self.current_price,
                                'exit_price': exit_price,
                                'profit': loss,
                                'capital': capital,
                                'reason': '空头止损'
                            })

                # 如果空仓，判断是否开仓 (使用Cosmoon XYE策略)
                if self.position == 0:
                    # 多头信号
                    if (row['e_value'] > 0 and
                        row['x_value'] > 0.45 and
                        row['y_value'] > 0.45 and
                        row['price_position'] < 0):  # 价格低于回归线

                        self.position = 1
                        self.current_price = row['close']
                        self.trades.append({
                            'date': date,
                            'type': 'long_entry',
                            'entry_price': self.current_price,
                            'capital': capital,
                            'y_value': row['y_value'],
                            'x_value': row['x_value'],
                            'e_value': row['e_value'],
                            'reason': '多头开仓'
                        })

                    # 空头信号
                    elif ((row['y_value'] < 0.3 or row['x_value'] < 0.3 or
                           (row['x_value'] > 0.45 and row['y_value'] < 0.35) or
                           (row['x_value'] < 0.45 and row['y_value'] > 0.35)) and
                          row['price_position'] > 0):  # 价格高于回归线

                        self.position = -1
                        self.current_price = row['close']
                        self.trades.append({
                            'date': date,
                            'type': 'short_entry',
                            'entry_price': self.current_price,
                            'capital': capital,
                            'y_value': row['y_value'],
                            'x_value': row['x_value'],
                            'e_value': row['e_value'],
                            'reason': '空头开仓'
                        })

            self.final_capital = capital
            self.max_drawdown = max_drawdown
            self.monthly_additions_count = monthly_additions_count

            print(f"✓ 20年回测完成！")
            print(f"最终资金：{self.final_capital:,.2f} 港元")
            print(f"最大回撤：{self.max_drawdown*100:.2f}%")
            print(f"月度追加次数：{self.monthly_additions_count}次")

        except Exception as e:
            print(f"❌ 回测执行失败: {str(e)}")
            raise

    def analyze_20year_results(self):
        """分析20年回测结果"""
        print("\n5. 20年回测分析...")
        try:
            # 转换交易记录为DataFrame
            trades_df = pd.DataFrame(self.trades)
            if len(trades_df) == 0:
                print("没有产生任何交易")
                return

            # 计算基本统计数据
            entry_trades = trades_df[trades_df['type'].str.contains('entry')]
            exit_trades = trades_df[trades_df['type'].str.contains('exit')]

            total_trades = len(entry_trades)
            winning_trades = len(exit_trades[exit_trades['profit'] > 0]) if len(exit_trades) > 0 else 0
            profit_trades = exit_trades[exit_trades['profit'] > 0] if len(exit_trades) > 0 else pd.DataFrame()
            loss_trades = exit_trades[exit_trades['profit'] < 0] if len(exit_trades) > 0 else pd.DataFrame()

            print(f"\n📊 东亚银行(0023.HK)20年交易统计：")
            print(f"总交易次数：{total_trades}")
            print(f"盈利交易：{winning_trades}")
            print(f"亏损交易：{len(exit_trades) - winning_trades}")
            if len(exit_trades) > 0:
                print(f"胜率：{winning_trades/len(exit_trades)*100:.2f}%")

            if len(profit_trades) > 0:
                print(f"平均盈利：{profit_trades['profit'].mean():,.2f} 港元")
                print(f"最大盈利：{profit_trades['profit'].max():,.2f} 港元")

            if len(loss_trades) > 0:
                print(f"平均亏损：{loss_trades['profit'].mean():,.2f} 港元")
                print(f"最大亏损：{loss_trades['profit'].min():,.2f} 港元")

            # 计算收益率
            initial_equity = self.initial_capital
            final_equity = self.final_capital
            total_days = (self.df['date'].max() - self.df['date'].min()).days
            total_years = total_days / 365

            total_return = (final_equity - initial_equity) / initial_equity
            annual_return = (1 + total_return) ** (1/total_years) - 1

            print(f"\n💰 20年收益统计：")
            print(f"回测期间：{total_years:.1f}年")
            print(f"初始资金：{initial_equity:,.2f} 港元")
            print(f"最终资金：{final_equity:,.2f} 港元")
            print(f"总收益：{final_equity - initial_equity:,.2f} 港元")
            print(f"总收益率：{total_return*100:.2f}%")
            print(f"年化收益率：{annual_return*100:.2f}%")
            print(f"最大回撤：{self.max_drawdown*100:.2f}%")

            # 计算考虑每月追加资金的实际收益率
            total_additions = self.monthly_additions_count * self.monthly_addition
            total_invested = initial_equity + total_additions
            actual_return = (final_equity - total_invested) / total_invested
            print(f"总投入资金：{total_invested:,.2f} 港元")
            print(f"实际收益率：{actual_return*100:.2f}%")

            # 买入持有策略对比
            start_price = self.df['close'].iloc[0]
            end_price = self.df['close'].iloc[-1]
            buy_hold_return = (end_price - start_price) / start_price
            buy_hold_final = total_invested * (1 + buy_hold_return)

            print(f"\n🆚 20年买入持有对比：")
            print(f"买入持有收益率：{buy_hold_return*100:.2f}%")
            print(f"买入持有最终资金：{buy_hold_final:,.2f} 港元")
            print(f"策略超额收益：{final_equity - buy_hold_final:,.2f} 港元")
            print(f"策略优势倍数：{final_equity / buy_hold_final:.2f}x")

            # 年度收益分析
            print(f"\n📅 年度收益分析：")
            equity_df = pd.DataFrame(self.equity_curve)
            equity_df['date'] = pd.to_datetime(equity_df['date'])
            equity_df['year'] = equity_df['date'].dt.year

            yearly_returns = []
            for year in sorted(equity_df['year'].unique()):
                year_data = equity_df[equity_df['year'] == year]
                if len(year_data) > 1:
                    start_equity = year_data['equity'].iloc[0]
                    end_equity = year_data['equity'].iloc[-1]
                    year_return = (end_equity - start_equity) / start_equity * 100
                    yearly_returns.append(year_return)
                    print(f"   {year}年：{year_return:+.2f}%")

            if yearly_returns:
                print(f"平均年收益：{np.mean(yearly_returns):.2f}%")
                print(f"收益标准差：{np.std(yearly_returns):.2f}%")
                print(f"夏普比率：{np.mean(yearly_returns)/np.std(yearly_returns):.2f}")

            # 显示关键交易
            print(f"\n🎯 关键交易分析：")
            if len(profit_trades) > 0:
                best_trade = profit_trades.loc[profit_trades['profit'].idxmax()]
                print(f"最佳交易：{best_trade['date'].strftime('%Y-%m-%d')} | "
                      f"{best_trade['reason']} | 收益：{best_trade['profit']:,.2f}港元")

            if len(loss_trades) > 0:
                worst_trade = loss_trades.loc[loss_trades['profit'].idxmin()]
                print(f"最差交易：{worst_trade['date'].strftime('%Y-%m-%d')} | "
                      f"{worst_trade['reason']} | 亏损：{worst_trade['profit']:,.2f}港元")

            # 创建可视化图表
            self.create_20year_charts(trades_df)

            # 保存交易记录
            trades_df_save = trades_df.copy()
            if 'date' in trades_df_save.columns:
                trades_df_save['date'] = pd.to_datetime(trades_df_save['date']).dt.tz_localize(None)
            trades_df_save.to_excel(f'东亚银行_20年交易记录_{datetime.now().strftime("%Y%m%d_%H%M%S")}.xlsx', index=False)

            print(f"\n✓ 20年分析完成")
            print(f"• 交易记录已保存到Excel文件")

        except Exception as e:
            print(f"❌ 分析失败: {str(e)}")
            raise

    def create_20year_charts(self, trades_df):
        """创建20年分析图表"""
        try:
            # 创建子图
            fig = plt.figure(figsize=(24, 18))

            # 1. 权益曲线与价格走势 (20年全景)
            ax1 = plt.subplot(231)
            equity_df = pd.DataFrame(self.equity_curve)
            equity_df['date'] = pd.to_datetime(equity_df['date'])

            # 绘制权益曲线
            ax1_twin = ax1.twinx()
            ax1.plot(equity_df['date'], equity_df['equity'], 'b-', label='权益曲线', linewidth=2)
            ax1_twin.plot(equity_df['date'], equity_df['price'], 'gray', alpha=0.7, label='0023.HK价格')

            ax1.set_title('东亚银行20年权益曲线与股价走势')
            ax1.set_ylabel('资金 (港元)', color='blue')
            ax1_twin.set_ylabel('股价 (港元)', color='gray')
            ax1.grid(True, alpha=0.3)
            ax1.legend(loc='upper left')
            ax1_twin.legend(loc='upper right')

            # 2. 回撤分析
            ax2 = plt.subplot(232)
            ax2.fill_between(equity_df['date'], 0, equity_df['drawdown']*100,
                           alpha=0.3, color='red', label='回撤')
            ax2.plot(equity_df['date'], equity_df['drawdown']*100, 'r-', alpha=0.8)
            ax2.set_title(f'20年回撤分析 (最大回撤: {self.max_drawdown*100:.2f}%)')
            ax2.set_ylabel('回撤 (%)')
            ax2.grid(True, alpha=0.3)
            ax2.legend()

            # 3. 年度收益分布
            ax3 = plt.subplot(233)
            equity_df['year'] = equity_df['date'].dt.year
            yearly_returns = []
            years = []

            for year in sorted(equity_df['year'].unique()):
                year_data = equity_df[equity_df['year'] == year]
                if len(year_data) > 1:
                    start_equity = year_data['equity'].iloc[0]
                    end_equity = year_data['equity'].iloc[-1]
                    year_return = (end_equity - start_equity) / start_equity * 100
                    yearly_returns.append(year_return)
                    years.append(year)

            colors = ['green' if x > 0 else 'red' for x in yearly_returns]
            ax3.bar(years, yearly_returns, color=colors, alpha=0.7)
            ax3.axhline(y=0, color='black', linestyle='-', alpha=0.5)
            ax3.set_title('年度收益分布')
            ax3.set_ylabel('年收益率 (%)')
            ax3.grid(True, alpha=0.3)

            # 4. XYE指标长期走势
            ax4 = plt.subplot(234)
            sample_data = self.df.iloc[::50]  # 采样显示，避免过密
            ax4.plot(sample_data['date'], sample_data['y_value'], 'g-', label='Y值', alpha=0.8)
            ax4.plot(sample_data['date'], sample_data['x_value'], 'r-', label='X值', alpha=0.8)
            ax4.axhline(y=0.45, color='orange', linestyle='--', alpha=0.5, label='0.45阈值')
            ax4.axhline(y=0.3, color='purple', linestyle='--', alpha=0.5, label='0.3阈值')
            ax4.set_title('20年XYE指标走势')
            ax4.set_ylabel('指标值')
            ax4.legend()
            ax4.grid(True, alpha=0.3)

            # 5. 交易盈亏分布
            ax5 = plt.subplot(235)
            exit_trades = trades_df[trades_df['type'].str.contains('exit')]
            if len(exit_trades) > 0:
                profits = exit_trades['profit']
                ax5.hist(profits, bins=50, color='blue', alpha=0.7, edgecolor='black')
                ax5.axvline(x=0, color='red', linestyle='--', linewidth=2)
                ax5.axvline(x=profits.mean(), color='green', linestyle='-', linewidth=2,
                           label=f'平均: {profits.mean():.0f}')
                ax5.set_title('20年交易盈亏分布')
                ax5.set_xlabel('盈亏金额 (港元)')
                ax5.set_ylabel('频次')
                ax5.legend()
                ax5.grid(True, alpha=0.3)

            # 6. 累计收益对比
            ax6 = plt.subplot(236)

            # 策略累计收益
            strategy_returns = equity_df['equity'] / self.initial_capital
            ax6.plot(equity_df['date'], strategy_returns, 'b-', label='Cosmoon XYE策略', linewidth=2)

            # 买入持有累计收益
            start_price = self.df['close'].iloc[0]
            buy_hold_returns = self.df['close'] / start_price
            ax6.plot(self.df['date'], buy_hold_returns, 'gray', label='买入持有', alpha=0.7)

            ax6.set_title('20年累计收益对比')
            ax6.set_ylabel('累计收益倍数')
            ax6.legend()
            ax6.grid(True, alpha=0.3)
            ax6.set_yscale('log')  # 使用对数坐标更好显示

            # 调整布局并保存
            plt.tight_layout()
            chart_filename = f'东亚银行_20年分析图表_{datetime.now().strftime("%Y%m%d_%H%M%S")}.png'
            plt.savefig(chart_filename, dpi=300, bbox_inches='tight')
            plt.close()

            print(f"• 20年分析图表已保存到 {chart_filename}")

        except Exception as e:
            print(f"❌ 图表创建失败: {str(e)}")

def main():
    """主函数"""
    print("🏦 东亚银行(0023.HK)20年回测系统")
    print("基于backtest_hsi50_final.py的Cosmoon XYE策略")
    print("="*70)

    try:
        # 创建回测实例
        backtest = EAB20YearBacktest()

        # 加载20年数据
        backtest.load_20year_data()

        # 计算XYE指标
        backtest.calculate_xye_indicators()

        # 计算回归线
        backtest.calculate_regression_line()

        # 运行20年回测
        backtest.run_20year_backtest()

        # 分析结果
        backtest.analyze_20year_results()

    except Exception as e:
        print(f"\n❌ 程序运行失败: {str(e)}")
    else:
        print(f"\n✅ 东亚银行20年回测完成")
        print(f"💡 基于Cosmoon XYE策略的超长期验证")

if __name__ == "__main__":
    main()
