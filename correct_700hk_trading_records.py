#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
修正腾讯700HK交易记录
====================

修正以下问题:
1. 初始资本: 20,000港元
2. 凯利公式: f=0.25, 每次投资5,000港元
3. 止盈止损逻辑修正

作者: Cosmoon NG
"""

import pandas as pd
import mysql.connector
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

def create_corrected_trading_records():
    """创建修正后的交易记录"""
    
    print("🔧 创建修正后的腾讯700HK交易记录...")
    print("=" * 60)
    
    # 修正后的参数
    initial_capital = 20000.00  # 修正: 2万港元
    kelly_fraction = 0.25       # 凯利公式结果
    investment_per_trade = initial_capital * kelly_fraction  # 5000港元
    monthly_addition = 3000.00
    commission_rate = 0.001
    
    # 修正后的止盈止损率
    long_take_profit = 0.012    # 多头止盈 1.2%
    long_stop_loss = 0.006      # 多头止损 0.6%
    short_take_profit = 0.006   # 空头止盈 0.6%
    short_stop_loss = 0.012     # 空头止损 1.2%
    
    print(f"📊 修正后的参数:")
    print(f"   初始资本: {initial_capital:,.2f}港元")
    print(f"   凯利公式: f = (p(b+1)-1)/b = (0.5×3-1)/2 = 0.25")
    print(f"   每次投资: {investment_per_trade:,.2f}港元")
    print(f"   多头止盈: +{long_take_profit*100:.1f}%, 止损: -{long_stop_loss*100:.1f}%")
    print(f"   空头止盈: +{short_take_profit*100:.1f}%, 止损: -{short_stop_loss*100:.1f}%")
    
    # 从数据库获取数据
    config = {
        'host': 'localhost',
        'port': 3306,
        'user': 'root',
        'password': '12345678',
        'database': 'finance'
    }
    
    try:
        connection = mysql.connector.connect(**config)
        
        # 查询完整数据
        df = pd.read_sql("""
            SELECT Date, Open, High, Low, Close, Volume,
                   Y_Value, X_Value, E_Value, Full_Y, MoneyFlowRatio, MyE
            FROM eab_0700hk
            ORDER BY Date
        """, connection)
        
        connection.close()
        
        print(f"   📊 从数据库读取 {len(df)} 条记录")
        
        # 创建交易记录
        trading_records = []
        
        current_capital = initial_capital
        position = 0  # 0=空仓, 1=多头, -1=空头
        entry_price = 0
        shares = 0
        trade_count = 0
        
        for i, row in df.iterrows():
            current_date = row['Date']
            current_price = row['Close']
            y_value = row['Y_Value']
            x_value = row['X_Value']
            e_value = row['E_Value']
            full_y = row['Full_Y']
            mfr = row['MoneyFlowRatio']
            mye = row['MyE']
            
            # 每月追加资金
            if i > 0 and i % 22 == 0:
                current_capital += monthly_addition
            
            # 交易逻辑
            signal = "观望"
            trade_type = "持仓"
            trade_direction = "空仓"
            
            if position == 0:  # 空仓状态
                if e_value > 0 and x_value > 0.45 and y_value > 0.45:
                    # 多头开仓
                    signal = "强烈买入"
                    trade_type = "开仓"
                    trade_direction = "多头"
                    position = 1
                    entry_price = current_price
                    # 使用凯利公式: 固定投资5000港元
                    shares = int(investment_per_trade / current_price) if current_price > 0 else 0
                    if shares > 0:
                        cost = shares * current_price * (1 + commission_rate)
                        current_capital -= cost
                        trade_count += 1
                        
                elif (y_value < 0.3 or x_value < 0.3):
                    # 空头开仓
                    signal = "强烈卖出"
                    trade_type = "开仓"
                    trade_direction = "空头"
                    position = -1
                    entry_price = current_price
                    # 使用凯利公式: 固定投资5000港元
                    shares = int(investment_per_trade / current_price) if current_price > 0 else 0
                    if shares > 0:
                        cost = shares * current_price * commission_rate
                        current_capital -= cost
                        trade_count += 1
                        
            else:  # 有持仓
                price_change = (current_price - entry_price) / entry_price if entry_price > 0 else 0
                
                if position == 1:  # 多头持仓
                    trade_direction = "多头"
                    # 多头止盈止损逻辑
                    if price_change >= long_take_profit:  # +1.2%止盈
                        signal = "止盈平仓"
                        trade_type = "平仓"
                        if shares > 0:
                            current_capital += shares * current_price * (1 - commission_rate)
                        position = 0
                        shares = 0
                    elif price_change <= -long_stop_loss:  # -0.6%止损
                        signal = "止损平仓"
                        trade_type = "平仓"
                        if shares > 0:
                            current_capital += shares * current_price * (1 - commission_rate)
                        position = 0
                        shares = 0
                    else:
                        signal = "持有多头"
                        
                elif position == -1:  # 空头持仓
                    trade_direction = "空头"
                    # 空头止盈止损逻辑
                    if price_change <= -short_take_profit:  # -0.6%止盈 (价格下跌)
                        signal = "止盈平仓"
                        trade_type = "平仓"
                        pnl = shares * (entry_price - current_price) * (1 - commission_rate)
                        current_capital += pnl
                        position = 0
                        shares = 0
                    elif price_change >= short_stop_loss:  # +1.2%止损 (价格上涨)
                        signal = "止损平仓"
                        trade_type = "平仓"
                        pnl = shares * (entry_price - current_price) * (1 - commission_rate)
                        current_capital += pnl
                        position = 0
                        shares = 0
                    else:
                        signal = "持有空头"
            
            # 计算当前状态
            if position != 0 and shares > 0:
                if position == 1:  # 多头
                    unrealized_pnl = shares * (current_price - entry_price)
                    current_market_value = shares * current_price
                else:  # 空头
                    unrealized_pnl = shares * (entry_price - current_price)
                    current_market_value = shares * current_price
                total_assets = current_capital + current_market_value + unrealized_pnl
            else:
                unrealized_pnl = 0
                current_market_value = 0
                total_assets = current_capital
            
            # 计算收益率
            months_passed = i // 22
            total_invested = initial_capital + months_passed * monthly_addition
            daily_return = (total_assets - total_invested) / total_invested * 100 if total_invested > 0 else 0
            
            # 修正后的止盈止损价格
            if position == 1:  # 多头
                take_profit_price = entry_price * (1 + long_take_profit) if entry_price > 0 else current_price * (1 + long_take_profit)
                stop_loss_price = entry_price * (1 - long_stop_loss) if entry_price > 0 else current_price * (1 - long_stop_loss)
            elif position == -1:  # 空头
                take_profit_price = entry_price * (1 + short_take_profit) if entry_price > 0 else current_price * (1 + short_take_profit)
                stop_loss_price = entry_price * (1 - short_stop_loss) if entry_price > 0 else current_price * (1 - short_stop_loss)
            else:  # 空仓
                take_profit_price = current_price * (1 + long_take_profit)
                stop_loss_price = current_price * (1 - long_stop_loss)
            
            # 风险等级
            if abs(e_value) > 0.5:
                risk_level = "高风险"
            elif abs(e_value) > 0.2:
                risk_level = "中风险"
            else:
                risk_level = "低风险"
            
            # 创建完整记录
            record = {
                '交易日期': current_date.strftime('%Y-%m-%d') if hasattr(current_date, 'strftime') else str(current_date),
                '交易类型': trade_type,
                '交易方向': trade_direction,
                '交易价格': round(current_price, 2),
                '入场价格': round(entry_price, 2) if entry_price > 0 else round(current_price, 2),
                '止盈价': round(take_profit_price, 2),
                '止损价': round(stop_loss_price, 2),
                '持仓数量': shares,
                '交易金额': round(shares * current_price, 2) if shares > 0 else 0,
                '手续费': round(shares * current_price * commission_rate, 2) if shares > 0 else 0,
                '净交易额': round(shares * current_price * (1 - commission_rate), 2) if shares > 0 else 0,
                '持仓成本': round(entry_price, 2) if entry_price > 0 else 0,
                '当前市值': round(current_market_value, 2),
                '浮动盈亏': round(unrealized_pnl, 2),
                '实现盈亏': 0.00,
                '累计盈亏': round(unrealized_pnl, 2),
                '账户余额': round(current_capital, 2),
                '总资产': round(total_assets, 2),
                '收益率': round(daily_return, 2),
                '累计收益率': round(daily_return, 2),
                'Y值': round(y_value, 4),
                'Full_Y': round(full_y, 4),
                'X值': round(x_value, 4),
                'MoneyFlowRatio': round(mfr, 4),
                'E值': round(e_value, 4),
                'MyE': round(mye, 4),
                '信号强度': signal,
                '风险等级': risk_level,
                '备注': f'修正版 凯利公式{investment_per_trade:,.0f}港元 价格{current_price:.2f} {signal} 资产{total_assets:,.0f}港元'
            }
            
            trading_records.append(record)
        
        # 创建DataFrame
        final_df = pd.DataFrame(trading_records)
        
        # 保存Excel文件
        filename = f'交易记录追踪0700HK_修正版_{datetime.now().strftime("%Y%m%d_%H%M%S")}.xlsx'
        final_df.to_excel(filename, index=False)
        
        print(f"\n✅ 修正版Excel已导出: {filename}")
        print(f"📊 包含 {len(final_df)} 条记录，29个完整字段")
        
        # 显示修正效果
        print(f"\n📋 修正效果验证:")
        print(f"   初始资本: {initial_capital:,.2f}港元 ✅")
        print(f"   凯利投资: {investment_per_trade:,.2f}港元 ✅")
        print(f"   交易次数: {trade_count}")
        print(f"   最终资产: {trading_records[-1]['总资产']:,.2f}港元")
        
        # 显示止盈止损示例
        print(f"\n🎯 止盈止损示例 (以0.71港元为例):")
        example_price = 0.71
        print(f"   多头开仓价格: {example_price:.2f}港元")
        print(f"   多头止盈价: {example_price * (1 + long_take_profit):.5f}港元 (+1.2%)")
        print(f"   多头止损价: {example_price * (1 - long_stop_loss):.5f}港元 (-0.6%)")
        print(f"   空头开仓价格: {example_price:.2f}港元")
        print(f"   空头止盈价: {example_price * (1 + short_take_profit):.5f}港元 (+0.6%)")
        print(f"   空头止损价: {example_price * (1 - short_stop_loss):.5f}港元 (-1.2%)")
        
        return filename, final_df
        
    except Exception as e:
        print(f"❌ 修正失败: {e}")
        return None, None

def verify_corrections():
    """验证修正结果"""
    
    print(f"\n🔍 验证修正结果...")
    
    # 凯利公式验证
    p = 0.5  # 胜率
    b = 2    # 盈亏比
    f = (p * (b + 1) - 1) / b
    
    print(f"📊 凯利公式验证:")
    print(f"   p (胜率): {p}")
    print(f"   b (盈亏比): {b}")
    print(f"   f = (p(b+1)-1)/b = ({p}×{b+1}-1)/{b} = {f:.2f}")
    print(f"   投资比例: {f*100:.0f}%")
    print(f"   每次投资: 20,000 × {f:.2f} = {20000*f:,.0f}港元 ✅")
    
    # 止盈止损验证
    print(f"\n🎯 止盈止损逻辑验证:")
    print(f"   多头 (看涨): 升得多，跌得少")
    print(f"      止盈: 入场价 × (1+0.012) = 入场价 × 1.012")
    print(f"      止损: 入场价 × (1-0.006) = 入场价 × 0.994")
    print(f"   空头 (看跌): 跌得多，升得少")
    print(f"      止盈: 入场价 × (1+0.006) = 入场价 × 1.006")
    print(f"      止损: 入场价 × (1-0.012) = 入场价 × 0.988")
    
    # 具体示例
    entry_price = 0.71
    print(f"\n💡 具体示例 (入场价 {entry_price}港元):")
    print(f"   多头止盈价: {entry_price} × 1.012 = {entry_price * 1.012:.5f}港元")
    print(f"   多头止损价: {entry_price} × 0.994 = {entry_price * 0.994:.5f}港元")
    print(f"   空头止盈价: {entry_price} × 1.006 = {entry_price * 1.006:.5f}港元 ✅")
    print(f"   空头止损价: {entry_price} × 0.988 = {entry_price * 0.988:.5f}港元 ✅")

def main():
    """主函数"""
    
    print("🔧 腾讯700HK交易记录修正系统")
    print("=" * 60)
    print("📋 修正内容:")
    print("   1. 初始资本: 10,000 → 20,000港元")
    print("   2. 凯利公式: f=0.25, 每次投资5,000港元")
    print("   3. 多头止盈止损: +1.2%/-0.6%")
    print("   4. 空头止盈止损: +0.6%/-1.2%")
    
    # 验证修正逻辑
    verify_corrections()
    
    # 创建修正版交易记录
    filename, df = create_corrected_trading_records()
    
    if filename:
        print(f"\n🎉 修正完成!")
        print(f"✅ 修正版文件: {filename}")
        print(f"📊 所有参数已按要求修正")
        print(f"🧮 基于数据库的完整29字段技术指标")
        print(f"💰 正确的凯利公式资金管理")
        print(f"🎯 正确的多空止盈止损逻辑")
    else:
        print(f"\n❌ 修正失败")

if __name__ == "__main__":
    main()
