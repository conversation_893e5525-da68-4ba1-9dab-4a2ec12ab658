#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
将midPrice更新集成到sp_updatecontroller函数
==========================================
查看并修改sp_updatecontroller存储过程，添加midPrice更新功能
"""

import mysql.connector
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

class ControllerIntegrator:
    def __init__(self):
        """初始化数据库连接"""
        self.db_config = {
            'host': 'localhost',
            'database': 'finance',
            'user': 'root',
            'password': '12345678',
            'charset': 'utf8mb4',
            'autocommit': True
        }
        self.connection = None
        self.cursor = None

    def connect_database(self):
        """连接数据库"""
        try:
            print("🔗 连接MySQL数据库...")
            self.connection = mysql.connector.connect(**self.db_config)
            self.cursor = self.connection.cursor()
            print(f"✅ 成功连接到数据库: {self.db_config['host']}/{self.db_config['database']}")
            return True
        except mysql.connector.Error as e:
            print(f"❌ 数据库连接失败: {e}")
            return False

    def check_existing_procedures(self):
        """检查现有存储过程"""
        try:
            print("\n🔍 检查现有存储过程...")

            # 查看sp_updatecontroller存储过程
            self.cursor.execute("""
                SELECT ROUTINE_NAME, ROUTINE_TYPE, ROUTINE_DEFINITION
                FROM INFORMATION_SCHEMA.ROUTINES
                WHERE ROUTINE_SCHEMA = 'finance'
                AND ROUTINE_NAME IN ('sp_updatecontroller', 'sp_averagelineV3')
                ORDER BY ROUTINE_NAME
            """)

            procedures = self.cursor.fetchall()

            found_controller = False
            found_averageline = False

            for proc in procedures:
                proc_name = proc[0]
                proc_type = proc[1]
                proc_def = proc[2]

                print(f"✅ 找到存储过程: {proc_name} ({proc_type})")

                if proc_name == 'sp_updatecontroller':
                    found_controller = True
                    print("📋 sp_updatecontroller 当前定义:")
                    # 显示前500个字符
                    definition_preview = proc_def[:500] if proc_def else "无定义"
                    print(f"   {definition_preview}...")

                elif proc_name == 'sp_averagelineV3':
                    found_averageline = True

                # 查看存储过程参数
                self.cursor.execute("""
                    SELECT PARAMETER_NAME, DATA_TYPE, PARAMETER_MODE
                    FROM INFORMATION_SCHEMA.PARAMETERS
                    WHERE SPECIFIC_SCHEMA = 'finance'
                    AND SPECIFIC_NAME = %s
                    ORDER BY ORDINAL_POSITION
                """, (proc_name,))

                params = self.cursor.fetchall()
                if params:
                    print(f"📋 {proc_name} 参数:")
                    for param in params:
                        print(f"   • {param[2]} {param[0]} - {param[1]}")
                else:
                    print(f"📋 {proc_name} 无参数")
                print()

            return found_controller, found_averageline

        except mysql.connector.Error as e:
            print(f"❌ 检查存储过程失败: {e}")
            return False, False

    def get_controller_definition(self):
        """获取sp_updatecontroller的完整定义"""
        try:
            print("📖 获取sp_updatecontroller完整定义...")

            self.cursor.execute("""
                SHOW CREATE PROCEDURE sp_updatecontroller
            """)

            result = self.cursor.fetchone()
            if result:
                procedure_definition = result[2]  # CREATE PROCEDURE语句
                print("✅ 成功获取存储过程定义")

                # 保存到文件以便查看
                with open('sp_updatecontroller_original.sql', 'w', encoding='utf-8') as f:
                    f.write(procedure_definition)
                print("📄 原始定义已保存到 sp_updatecontroller_original.sql")

                return procedure_definition
            else:
                print("❌ 无法获取存储过程定义")
                return None

        except mysql.connector.Error as e:
            print(f"❌ 获取存储过程定义失败: {e}")
            return None

    def create_enhanced_controller(self):
        """创建增强版的sp_updatecontroller，集成midPrice更新"""
        try:
            print("\n🔧 创建增强版sp_updatecontroller...")
            print("💡 发现原sp_updatecontroller已经使用midprice字段")
            print("📋 原逻辑: close与midprice比较 -> 设置controller字段 -> 计算k值")

            # 新的存储过程定义 - 在原逻辑前先更新midprice
            enhanced_procedure = """
CREATE PROCEDURE sp_updatecontroller_enhanced(
    IN tablename VARCHAR(64),
    OUT result_k DECIMAL(20,6)
)
BEGIN
    DECLARE col_exists INT DEFAULT 0;
    DECLARE v_start_time DATETIME DEFAULT NOW();

    -- 1. 首先更新midprice (调用sp_averagelineV3)
    SELECT CONCAT('开始更新midprice - 表: ', tablename, ' 时间: ', v_start_time) AS start_message;
    CALL sp_averagelineV3(tablename);
    SELECT 'midprice更新完成' AS midprice_status;

    -- 2. 检查controller列是否存在（原逻辑）
    SET @sql = CONCAT(
        'SELECT COUNT(*) INTO @col_exists FROM information_schema.COLUMNS ',
        'WHERE TABLE_SCHEMA=DATABASE() AND TABLE_NAME=''', tablename, ''' AND COLUMN_NAME=''controller'''
    );
    PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;
    SET col_exists = @col_exists;

    IF col_exists = 0 THEN
        SET @sql = CONCAT('ALTER TABLE `', tablename, '` ADD COLUMN `controller` INT');
        PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;
        SELECT 'controller列已添加' AS controller_status;
    END IF;

    -- 3. 更新controller字段（原逻辑）
    SET @sql = CONCAT(
        'UPDATE `',
        tablename,
        '` SET controller = CASE WHEN close < midprice THEN 0 WHEN close > midprice THEN 1 ELSE 3 END'
    );
    PREPARE stmt FROM @sql;
    EXECUTE stmt;
    DEALLOCATE PREPARE stmt;
    SELECT 'controller字段更新完成' AS controller_update_status;

    -- 4. 计算k值并存入OUT参数（原逻辑）
    SET @sql = CONCAT(
        'SELECT SUM(CASE WHEN controller = 1 THEN 1 ELSE 0 END) / NULLIF(COUNT(*), 0) INTO @k_value ',
        'FROM `', tablename, '`'
    );
    PREPARE stmt FROM @sql;
    EXECUTE stmt;
    DEALLOCATE PREPARE stmt;

    SET result_k = @k_value;

    -- 5. 返回统计信息
    SELECT
        CONCAT('更新完成 - 表: ', tablename) AS completion_message,
        CONCAT('k值: ', IFNULL(result_k, 0)) AS k_value_message,
        CONCAT('耗时: ', TIMESTAMPDIFF(SECOND, v_start_time, NOW()), ' 秒') AS duration;

END
            """

            # 删除旧的增强版（如果存在）
            self.cursor.execute("DROP PROCEDURE IF EXISTS sp_updatecontroller_enhanced")

            # 创建新的增强版
            self.cursor.execute(enhanced_procedure)

            print("✅ 成功创建sp_updatecontroller_enhanced存储过程")

            # 保存新的存储过程定义到文件
            with open('sp_updatecontroller_enhanced.sql', 'w', encoding='utf-8') as f:
                f.write(enhanced_procedure)
            print("📄 新定义已保存到 sp_updatecontroller_enhanced.sql")

            return True

        except mysql.connector.Error as e:
            print(f"❌ 创建增强版存储过程失败: {e}")
            return False

    def test_enhanced_controller(self):
        """测试增强版控制器"""
        try:
            print("\n🧪 测试增强版sp_updatecontroller_enhanced...")

            # 测试调用 - 需要包含OUT参数
            args = ['stock_600036_ss', 0]  # 第二个参数是OUT参数的占位符
            result = self.cursor.callproc('sp_updatecontroller_enhanced', args)

            # 获取结果集
            for result_set in self.cursor.stored_results():
                rows = result_set.fetchall()
                for row in rows:
                    print(f"📊 {row}")

            # 显示OUT参数结果
            k_value = result[1]  # OUT参数result_k的值
            print(f"📊 测试k值结果: {k_value}")

            print("✅ 增强版控制器测试成功")
            return True

        except mysql.connector.Error as e:
            print(f"❌ 测试增强版控制器失败: {e}")
            return False

    def backup_and_replace_controller(self):
        """备份原控制器并替换为增强版"""
        try:
            print("\n🔄 备份并替换sp_updatecontroller...")

            # 1. 备份原存储过程
            backup_name = f"sp_updatecontroller_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

            # 获取原存储过程定义
            original_def = self.get_controller_definition()
            if not original_def:
                print("⚠️ 无法获取原存储过程定义，跳过备份")
            else:
                # 创建备份存储过程
                backup_sql = original_def.replace(
                    'CREATE DEFINER=`root`@`%` PROCEDURE `sp_updatecontroller`',
                    f'CREATE PROCEDURE `{backup_name}`'
                )

                self.cursor.execute(backup_sql)
                print(f"✅ 原存储过程已备份为: {backup_name}")

            # 2. 删除原存储过程
            self.cursor.execute("DROP PROCEDURE IF EXISTS sp_updatecontroller")
            print("🗑️ 已删除原sp_updatecontroller")

            # 3. 基于增强版创建新的sp_updatecontroller
            enhanced_def = """
CREATE PROCEDURE sp_updatecontroller(
    IN tablename VARCHAR(64),
    OUT result_k DECIMAL(20,6)
)
BEGIN
    DECLARE col_exists INT DEFAULT 0;
    DECLARE v_start_time DATETIME DEFAULT NOW();

    -- 1. 首先更新midprice (调用sp_averagelineV3)
    SELECT CONCAT('开始更新midprice - 表: ', tablename, ' 时间: ', v_start_time) AS start_message;
    CALL sp_averagelineV3(tablename);
    SELECT 'midprice更新完成' AS midprice_status;

    -- 2. 检查controller列是否存在（原逻辑）
    SET @sql = CONCAT(
        'SELECT COUNT(*) INTO @col_exists FROM information_schema.COLUMNS ',
        'WHERE TABLE_SCHEMA=DATABASE() AND TABLE_NAME=''', tablename, ''' AND COLUMN_NAME=''controller'''
    );
    PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;
    SET col_exists = @col_exists;

    IF col_exists = 0 THEN
        SET @sql = CONCAT('ALTER TABLE `', tablename, '` ADD COLUMN `controller` INT');
        PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;
        SELECT 'controller列已添加' AS controller_status;
    END IF;

    -- 3. 更新controller字段（原逻辑）
    SET @sql = CONCAT(
        'UPDATE `',
        tablename,
        '` SET controller = CASE WHEN close < midprice THEN 0 WHEN close > midprice THEN 1 ELSE 3 END'
    );
    PREPARE stmt FROM @sql;
    EXECUTE stmt;
    DEALLOCATE PREPARE stmt;
    SELECT 'controller字段更新完成' AS controller_update_status;

    -- 4. 计算k值并存入OUT参数（原逻辑）
    SET @sql = CONCAT(
        'SELECT SUM(CASE WHEN controller = 1 THEN 1 ELSE 0 END) / NULLIF(COUNT(*), 0) INTO @k_value ',
        'FROM `', tablename, '`'
    );
    PREPARE stmt FROM @sql;
    EXECUTE stmt;
    DEALLOCATE PREPARE stmt;

    SET result_k = @k_value;

    -- 5. 返回统计信息
    SELECT
        CONCAT('更新完成 - 表: ', tablename) AS completion_message,
        CONCAT('k值: ', IFNULL(result_k, 0)) AS k_value_message,
        CONCAT('耗时: ', TIMESTAMPDIFF(SECOND, v_start_time, NOW()), ' 秒') AS duration;

END
            """

            self.cursor.execute(enhanced_def)
            print("✅ 新的sp_updatecontroller创建成功")

            return True

        except mysql.connector.Error as e:
            print(f"❌ 备份和替换失败: {e}")
            return False

    def verify_integration(self):
        """验证集成结果"""
        try:
            print("\n🔍 验证集成结果...")

            # 测试新的sp_updatecontroller
            print("🧪 测试新的sp_updatecontroller...")

            # 调用存储过程并获取OUT参数
            args = ['stock_600036_ss', 0]  # 第二个参数是OUT参数的占位符
            result = self.cursor.callproc('sp_updatecontroller', args)

            # 获取结果集
            for result_set in self.cursor.stored_results():
                rows = result_set.fetchall()
                for row in rows:
                    print(f"📊 {row}")

            # 显示OUT参数结果
            k_value = result[1]  # OUT参数result_k的值
            print(f"📊 k值结果: {k_value}")

            # 验证数据更新情况
            self.cursor.execute("""
                SELECT
                    COUNT(*) as total_records,
                    COUNT(midprice) as midprice_records,
                    COUNT(controller) as controller_records,
                    MAX(date) as latest_date
                FROM stock_600036_ss
            """)

            result = self.cursor.fetchone()
            print(f"\n📊 验证结果:")
            print(f"   • 总记录数: {result[0]}")
            print(f"   • midprice记录数: {result[1]}")
            print(f"   • controller记录数: {result[2]}")
            print(f"   • 最新日期: {result[3]}")
            print(f"   • midprice覆盖率: {result[1]/result[0]*100:.1f}%")
            print(f"   • controller覆盖率: {result[2]/result[0]*100:.1f}%")

            # 查看controller字段的分布
            self.cursor.execute("""
                SELECT
                    controller,
                    COUNT(*) as count,
                    ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM stock_600036_ss), 2) as percentage
                FROM stock_600036_ss
                WHERE controller IS NOT NULL
                GROUP BY controller
                ORDER BY controller
            """)

            controller_stats = self.cursor.fetchall()
            print(f"\n📊 controller字段分布:")
            print("值 | 数量   | 百分比")
            print("-" * 20)
            for row in controller_stats:
                print(f"{row[0]}  | {row[1]:6d} | {row[2]:6.2f}%")

            return True

        except mysql.connector.Error as e:
            print(f"❌ 验证失败: {e}")
            return False

    def close_connection(self):
        """关闭数据库连接"""
        if self.cursor:
            self.cursor.close()
        if self.connection:
            self.connection.close()
        print("🔒 数据库连接已关闭")

    def run(self):
        """执行主流程"""
        print("🎯 将midPrice更新集成到sp_updatecontroller")
        print("=" * 60)
        print(f"📅 执行时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"🏠 数据库: {self.db_config['host']}/{self.db_config['database']}")

        try:
            # 1. 连接数据库
            if not self.connect_database():
                return False

            # 2. 检查现有存储过程
            has_controller, has_averageline = self.check_existing_procedures()

            if not has_averageline:
                print("❌ sp_averagelineV3不存在，无法集成")
                return False

            # 3. 获取原控制器定义
            if has_controller:
                original_def = self.get_controller_definition()

            # 4. 创建增强版控制器
            if not self.create_enhanced_controller():
                return False

            # 5. 测试增强版控制器
            if not self.test_enhanced_controller():
                return False

            # 6. 询问是否替换原控制器
            print("\n❓ 是否要替换原sp_updatecontroller？")
            print("   这将备份原存储过程并用集成midPrice的版本替换")

            # 在实际环境中，这里应该有用户确认
            # 为了演示，我们直接进行替换
            print("🔄 自动进行替换...")

            if not self.backup_and_replace_controller():
                return False

            # 7. 验证集成结果
            if not self.verify_integration():
                return False

            print("\n🎉 midPrice更新成功集成到sp_updatecontroller!")
            print("💡 现在调用sp_updatecontroller时会自动更新midPrice")

            return True

        except Exception as e:
            print(f"❌ 执行失败: {e}")
            return False

        finally:
            self.close_connection()

def main():
    """主函数"""
    integrator = ControllerIntegrator()
    success = integrator.run()

    if success:
        print("\n✅ 集成完成!")
        print("📝 使用方法: CALL sp_updatecontroller('stock_600036_ss');")
    else:
        print("\n❌ 集成失败!")

if __name__ == "__main__":
    main()
