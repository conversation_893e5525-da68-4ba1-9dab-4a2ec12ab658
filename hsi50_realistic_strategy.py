#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
HSI50现实策略测试
================
基于HSI50实际波动特性调整的策略
"""

import mysql.connector
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

class HSI50RealisticStrategy:
    def __init__(self):
        """初始化策略"""
        self.db_config = {
            'host': 'localhost',
            'database': 'finance',
            'user': 'root',
            'password': '12345678',
            'charset': 'utf8mb4'
        }
        
        # 基于实际数据调整的策略参数
        self.strategy_params = {
            'initial_capital': 30000,      # 初始资金30K港元
            'monthly_investment': 3000,    # 每月定投3K
            'min_expected_value': -0.02,   # 最小期望值-2% (基于75%分位数)
            'win_probability_threshold': 0.5,  # 胜率阈值50% (基于实际数据)
            'take_profit': 0.08,           # 止盈8% (降低)
            'stop_loss': 0.04,             # 止损4% (降低)
            'max_position_ratio': 0.15,    # 最大仓位15%
            'min_holding_days': 10,        # 最小持仓10天
            'max_holding_days': 90,        # 最大持仓90天
            'position_cooldown': 15,       # 冷却期15天
            'deviation_threshold': 0.01,   # 偏离阈值1%
            'extreme_deviation': 0.02,     # 极端偏离2% (基于实际数据)
        }
        
        self.data = None
        self.trades = []
        self.current_positions = []
        self.daily_capital = []
        
    def load_hsi50_data(self):
        """加载HSI50数据"""
        try:
            print("📊 加载HSI50数据...")
            
            connection = mysql.connector.connect(**self.db_config)
            
            query = """
                SELECT Date, Open, High, Low, Close, Volume,
                       ma_20, ma_60, y_probability, 
                       new_midprice, new_controller, new_Full_Y
                FROM hkhsi50 
                WHERE Date >= '2020-01-01'
                AND y_probability IS NOT NULL
                ORDER BY Date ASC
            """
            
            self.data = pd.read_sql(query, connection)
            connection.close()
            
            self.data['Date'] = pd.to_datetime(self.data['Date'])
            self.data.set_index('Date', inplace=True)
            
            print(f"✅ 成功加载 {len(self.data)} 条数据")
            print(f"📅 数据范围: {self.data.index.min().strftime('%Y-%m-%d')} 到 {self.data.index.max().strftime('%Y-%m-%d')}")
            
            return True
            
        except Exception as e:
            print(f"❌ 加载数据失败: {e}")
            return False
    
    def calculate_indicators(self):
        """计算技术指标"""
        try:
            print("📈 计算技术指标...")
            
            # 价格偏离 (相对于new_midprice)
            self.data['price_deviation'] = (self.data['Close'] - self.data['new_midprice']) / self.data['new_midprice']
            
            # 价格相对于移动平均线
            self.data['price_vs_ma20'] = self.data['Close'] / self.data['ma_20']
            self.data['price_vs_ma60'] = self.data['Close'] / self.data['ma_60']
            
            # 趋势判断
            self.data['uptrend'] = (self.data['ma_20'] > self.data['ma_60']) & (self.data['Close'] > self.data['ma_20'])
            self.data['downtrend'] = (self.data['ma_20'] < self.data['ma_60']) & (self.data['Close'] < self.data['ma_20'])
            
            # 波动率 (20日滚动标准差)
            self.data['volatility'] = self.data['Close'].pct_change().rolling(20).std()
            
            # RSI指标
            delta = self.data['Close'].diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
            rs = gain / loss
            self.data['rsi'] = 100 - (100 / (1 + rs))
            
            print("✅ 技术指标计算完成")
            return True
            
        except Exception as e:
            print(f"❌ 计算技术指标失败: {e}")
            return False
    
    def calculate_signal_strength(self, row):
        """计算信号强度"""
        deviation = row['price_deviation']
        y_prob = row['y_probability']
        rsi = row['rsi'] if not pd.isna(row['rsi']) else 50
        volatility = row['volatility'] if not pd.isna(row['volatility']) else 0.01
        
        # 基础胜率 (基于偏离度)
        if abs(deviation) < self.strategy_params['deviation_threshold']:
            base_win_prob = 0.5
        else:
            # 偏离越大，反转概率越高
            base_win_prob = 0.5 + min(0.3, abs(deviation) * 15)
        
        # y_probability调整
        y_adjustment = (y_prob - 0.5) * 0.2
        
        # RSI调整
        if rsi > 70:  # 超买
            rsi_adjustment = 0.1 if deviation > 0 else -0.1
        elif rsi < 30:  # 超卖
            rsi_adjustment = 0.1 if deviation < 0 else -0.1
        else:
            rsi_adjustment = 0
        
        # 最终胜率
        win_probability = np.clip(base_win_prob + y_adjustment + rsi_adjustment, 0.1, 0.9)
        
        # 期望收益 (基于偏离度和波动率)
        expected_return = abs(deviation) * 3 + volatility * 2
        
        # 期望值计算
        expected_value = win_probability * expected_return - (1 - win_probability) * self.strategy_params['stop_loss']
        
        return expected_value, win_probability
    
    def get_trading_signal(self, row):
        """获取交易信号"""
        expected_value, win_probability = self.calculate_signal_strength(row)
        
        # 检查基本条件
        if (expected_value >= self.strategy_params['min_expected_value'] and 
            win_probability >= self.strategy_params['win_probability_threshold']):
            
            deviation = row['price_deviation']
            
            # 基于偏离方向决定交易方向
            if deviation > self.strategy_params['extreme_deviation']:
                return 'SHORT', expected_value, win_probability  # 价格偏高，买跌
            elif deviation < -self.strategy_params['extreme_deviation']:
                return 'LONG', expected_value, win_probability   # 价格偏低，买涨
            elif abs(deviation) > self.strategy_params['deviation_threshold']:
                # 中等偏离，基于y_probability决定
                if row['y_probability'] > 0.52 and deviation < 0:
                    return 'LONG', expected_value, win_probability
                elif row['y_probability'] < 0.48 and deviation > 0:
                    return 'SHORT', expected_value, win_probability
        
        return 'HOLD', expected_value, win_probability
    
    def run_backtest(self):
        """运行回测"""
        try:
            print("\n🎯 开始运行HSI50现实策略回测...")
            
            current_capital = self.strategy_params['initial_capital']
            last_trade_date = None
            monthly_investment_date = None
            total_invested = self.strategy_params['initial_capital']
            
            for date, row in self.data.iterrows():
                # 每月定投
                if monthly_investment_date is None or (date - monthly_investment_date).days >= 30:
                    current_capital += self.strategy_params['monthly_investment']
                    total_invested += self.strategy_params['monthly_investment']
                    monthly_investment_date = date
                
                # 检查现有持仓
                positions_to_close = []
                for i, position in enumerate(self.current_positions):
                    holding_days = (date - position['entry_date']).days
                    current_price = row['Close']
                    
                    # 计算盈亏
                    if position['direction'] == 'LONG':
                        pnl_ratio = (current_price - position['entry_price']) / position['entry_price']
                    else:  # SHORT
                        pnl_ratio = (position['entry_price'] - current_price) / position['entry_price']
                    
                    # 平仓条件
                    should_close = False
                    close_reason = ""
                    
                    if pnl_ratio >= self.strategy_params['take_profit']:
                        should_close = True
                        close_reason = "止盈"
                    elif pnl_ratio <= -self.strategy_params['stop_loss']:
                        should_close = True
                        close_reason = "止损"
                    elif holding_days >= self.strategy_params['max_holding_days']:
                        should_close = True
                        close_reason = "到期"
                    elif holding_days >= self.strategy_params['min_holding_days']:
                        # 检查是否有反向信号
                        signal, _, _ = self.get_trading_signal(row)
                        if (position['direction'] == 'LONG' and signal == 'SHORT') or \
                           (position['direction'] == 'SHORT' and signal == 'LONG'):
                            should_close = True
                            close_reason = "反向信号"
                    
                    if should_close:
                        # 平仓
                        pnl = position['amount'] * pnl_ratio
                        current_capital += position['amount'] + pnl
                        
                        trade_record = {
                            'entry_date': position['entry_date'],
                            'exit_date': date,
                            'direction': position['direction'],
                            'entry_price': position['entry_price'],
                            'exit_price': current_price,
                            'amount': position['amount'],
                            'pnl': pnl,
                            'pnl_ratio': pnl_ratio,
                            'holding_days': holding_days,
                            'close_reason': close_reason,
                            'expected_value': position['expected_value'],
                            'win_probability': position['win_probability']
                        }
                        
                        self.trades.append(trade_record)
                        positions_to_close.append(i)
                
                # 移除已平仓的持仓
                for i in sorted(positions_to_close, reverse=True):
                    del self.current_positions[i]
                
                # 检查开仓条件
                if len(self.current_positions) < 2:  # 最多2个持仓
                    # 检查冷却期
                    if last_trade_date is None or (date - last_trade_date).days >= self.strategy_params['position_cooldown']:
                        
                        signal, expected_value, win_probability = self.get_trading_signal(row)
                        
                        if signal in ['LONG', 'SHORT']:
                            # 计算仓位大小
                            available_capital = current_capital * 0.8  # 保留20%现金
                            position_size = min(
                                available_capital * self.strategy_params['max_position_ratio'],
                                available_capital / 2  # 最多用一半可用资金
                            )
                            
                            if position_size >= 1000:  # 最小1000港元
                                # 开仓
                                position = {
                                    'entry_date': date,
                                    'direction': signal,
                                    'entry_price': row['Close'],
                                    'amount': position_size,
                                    'expected_value': expected_value,
                                    'win_probability': win_probability
                                }
                                
                                self.current_positions.append(position)
                                current_capital -= position_size
                                last_trade_date = date
                
                # 记录每日资金
                total_value = current_capital
                for position in self.current_positions:
                    if position['direction'] == 'LONG':
                        pnl_ratio = (row['Close'] - position['entry_price']) / position['entry_price']
                    else:
                        pnl_ratio = (position['entry_price'] - row['Close']) / position['entry_price']
                    total_value += position['amount'] * (1 + pnl_ratio)
                
                self.daily_capital.append({
                    'date': date,
                    'capital': total_value,
                    'positions': len(self.current_positions),
                    'invested': total_invested
                })
            
            print(f"✅ 回测完成！")
            print(f"📊 总交易次数: {len(self.trades)}")
            
            return True
            
        except Exception as e:
            print(f"❌ 回测失败: {e}")
            return False
    
    def analyze_results(self):
        """分析回测结果"""
        try:
            print("\n📊 分析回测结果...")
            
            if not self.trades:
                print("⚠️ 没有交易记录")
                return False
            
            trades_df = pd.DataFrame(self.trades)
            capital_df = pd.DataFrame(self.daily_capital)
            
            # 基本统计
            total_trades = len(trades_df)
            winning_trades = len(trades_df[trades_df['pnl'] > 0])
            losing_trades = len(trades_df[trades_df['pnl'] <= 0])
            win_rate = winning_trades / total_trades if total_trades > 0 else 0
            
            total_pnl = trades_df['pnl'].sum()
            avg_pnl = trades_df['pnl'].mean()
            max_win = trades_df['pnl'].max()
            max_loss = trades_df['pnl'].min()
            
            initial_capital = self.strategy_params['initial_capital']
            final_capital = capital_df['capital'].iloc[-1]
            total_invested = capital_df['invested'].iloc[-1]
            
            # 计算收益率
            net_profit = final_capital - total_invested
            total_return = net_profit / total_invested if total_invested > 0 else 0
            
            # 年化收益率 (假设5年数据)
            years = len(capital_df) / 252  # 252个交易日/年
            annual_return = (final_capital / total_invested) ** (1/years) - 1 if years > 0 else 0
            
            print(f"\n🎯 HSI50现实策略表现:")
            print(f"=" * 50)
            print(f"💰 初始资金: {initial_capital:,.2f} 港元")
            print(f"💰 总投入: {total_invested:,.2f} 港元")
            print(f"💰 最终资金: {final_capital:,.2f} 港元")
            print(f"📈 净收益: {net_profit:,.2f} 港元")
            print(f"📊 总收益率: {total_return*100:.2f}%")
            print(f"📊 年化收益率: {annual_return*100:.2f}%")
            
            print(f"\n📊 交易统计:")
            print(f"🔢 总交易次数: {total_trades}")
            print(f"✅ 盈利交易: {winning_trades} ({win_rate*100:.1f}%)")
            print(f"❌ 亏损交易: {losing_trades}")
            print(f"💰 平均盈亏: {avg_pnl:,.2f} 港元")
            print(f"🎯 最大盈利: {max_win:,.2f} 港元")
            print(f"💔 最大亏损: {max_loss:,.2f} 港元")
            
            # 分析交易方向
            long_trades = trades_df[trades_df['direction'] == 'LONG']
            short_trades = trades_df[trades_df['direction'] == 'SHORT']
            
            if len(long_trades) > 0:
                long_win_rate = len(long_trades[long_trades['pnl'] > 0]) / len(long_trades)
                long_avg_pnl = long_trades['pnl'].mean()
                print(f"\n📈 多头交易:")
                print(f"   🔢 次数: {len(long_trades)}")
                print(f"   ✅ 胜率: {long_win_rate*100:.1f}%")
                print(f"   💰 平均盈亏: {long_avg_pnl:,.2f} 港元")
            
            if len(short_trades) > 0:
                short_win_rate = len(short_trades[short_trades['pnl'] > 0]) / len(short_trades)
                short_avg_pnl = short_trades['pnl'].mean()
                print(f"\n📉 空头交易:")
                print(f"   🔢 次数: {len(short_trades)}")
                print(f"   ✅ 胜率: {short_win_rate*100:.1f}%")
                print(f"   💰 平均盈亏: {short_avg_pnl:,.2f} 港元")
            
            # 显示最近的交易
            print(f"\n📋 最近5笔交易:")
            recent_trades = trades_df.tail(5)
            for _, trade in recent_trades.iterrows():
                direction_emoji = "📈" if trade['direction'] == 'LONG' else "📉"
                pnl_emoji = "💰" if trade['pnl'] > 0 else "💔"
                print(f"   {direction_emoji} {trade['entry_date'].strftime('%Y-%m-%d')} ~ {trade['exit_date'].strftime('%Y-%m-%d')}")
                print(f"      价格: {trade['entry_price']:.2f} → {trade['exit_price']:.2f}")
                print(f"      {pnl_emoji} 盈亏: {trade['pnl']:,.2f} 港元 ({trade['pnl_ratio']*100:+.2f}%)")
                print(f"      原因: {trade['close_reason']}, 持仓: {trade['holding_days']}天")
            
            # 与买入持有策略比较
            hsi_start = self.data['Close'].iloc[0]
            hsi_end = self.data['Close'].iloc[-1]
            buy_hold_return = (hsi_end - hsi_start) / hsi_start
            
            print(f"\n📊 策略对比:")
            print(f"🎯 我们的策略年化收益: {annual_return*100:.2f}%")
            print(f"📈 买入持有年化收益: {(buy_hold_return/years)*100:.2f}%")
            print(f"🏆 超额收益: {(annual_return - buy_hold_return/years)*100:+.2f}%")
            
            return True
            
        except Exception as e:
            print(f"❌ 分析结果失败: {e}")
            return False
    
    def run(self):
        """运行完整策略测试"""
        print("🎯 HSI50现实策略测试")
        print("=" * 50)
        print(f"📅 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"💡 基于HSI50实际波动特性调整的策略参数")
        
        try:
            if not self.load_hsi50_data():
                return False
            
            if not self.calculate_indicators():
                return False
            
            if not self.run_backtest():
                return False
            
            if not self.analyze_results():
                return False
            
            print("\n🎉 HSI50现实策略测试完成!")
            print("💡 这个策略更适合HSI50的实际波动特性")
            
            return True
            
        except Exception as e:
            print(f"❌ 策略测试失败: {e}")
            return False

def main():
    """主函数"""
    strategy = HSI50RealisticStrategy()
    success = strategy.run()
    
    if success:
        print("\n✅ 测试完成!")
        print("📝 HSI50现实策略表现已分析完毕")
    else:
        print("\n❌ 测试失败!")

if __name__ == "__main__":
    main()
