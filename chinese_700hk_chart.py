#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
腾讯700HK中文图表
================

创建腾讯700HK回测结果的中文图表

作者: Cosmoon NG
"""

import matplotlib.pyplot as plt
import numpy as np
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'SimHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

def create_chinese_results_chart():
    """创建中文结果图表"""
    
    print("🎯 创建腾讯700HK中文结果图表...")
    
    # 创建子图
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))
    
    # 1. 资金增长对比
    categories = ['初始资金', '策略最终', '买入持有']
    values = [10000, 849569790, 602318503]
    colors = ['lightblue', 'green', 'orange']
    
    bars1 = ax1.bar(categories, values, color=colors, alpha=0.8)
    ax1.set_title('资金增长对比 (港元)', fontsize=14, fontweight='bold')
    ax1.set_ylabel('资金 (港元)')
    ax1.set_yscale('log')
    
    # 添加数值标签
    for i, (bar, value) in enumerate(zip(bars1, values)):
        height = bar.get_height()
        if i == 0:
            label = f'{value:,}'
        else:
            label = f'{value/100000000:.1f}亿'
        ax1.text(bar.get_x() + bar.get_width()/2., height,
                label, ha='center', va='bottom', fontsize=10)
    
    # 2. 收益率对比
    strategies = ['Cosmoon策略', '买入持有']
    returns = [8495598, 77319]
    colors2 = ['red', 'blue']
    
    bars2 = ax2.bar(strategies, returns, color=colors2, alpha=0.8)
    ax2.set_title('总收益率对比 (%)', fontsize=14, fontweight='bold')
    ax2.set_ylabel('收益率 (%)')
    ax2.set_yscale('log')
    
    for bar, value in zip(bars2, returns):
        height = bar.get_height()
        ax2.text(bar.get_x() + bar.get_width()/2., height,
                f'{value:,.0f}%', ha='center', va='bottom', fontsize=10)
    
    # 3. 交易统计
    trade_stats = ['总交易', '盈利交易', '亏损交易']
    trade_counts = [2604, 1398, 1206]
    colors3 = ['gray', 'green', 'red']
    
    bars3 = ax3.bar(trade_stats, trade_counts, color=colors3, alpha=0.8)
    ax3.set_title('交易统计', fontsize=14, fontweight='bold')
    ax3.set_ylabel('交易次数')
    
    for bar, value in zip(bars3, trade_counts):
        height = bar.get_height()
        ax3.text(bar.get_x() + bar.get_width()/2., height,
                f'{value}', ha='center', va='bottom', fontsize=11)
    
    # 4. 关键指标
    metrics = ['胜率(%)', '年化收益(%)', '盈亏比']
    metric_values = [53.69, 71.18, 1.8]
    colors4 = ['gold', 'purple', 'silver']
    
    bars4 = ax4.bar(metrics, metric_values, color=colors4, alpha=0.8)
    ax4.set_title('关键绩效指标', fontsize=14, fontweight='bold')
    ax4.set_ylabel('数值')
    
    for bar, value in zip(bars4, metric_values):
        height = bar.get_height()
        ax4.text(bar.get_x() + bar.get_width()/2., height,
                f'{value:.1f}', ha='center', va='bottom', fontsize=11)
    
    # 调整布局
    plt.tight_layout()
    
    # 添加主标题
    fig.suptitle('腾讯700HK - Cosmoon XYE策略回测结果\n最终资金: 8.5亿港元 | 年化收益: 71.18% | 回测期间: 21.1年', 
                 fontsize=16, fontweight='bold', y=0.98)
    
    # 保存图表
    filename = f'腾讯700HK_结果总结_{datetime.now().strftime("%Y%m%d_%H%M%S")}.png'
    plt.savefig(filename, dpi=300, bbox_inches='tight')
    plt.show()
    
    print(f"✅ 图表已保存: {filename}")
    return filename

def create_chinese_equity_curve():
    """创建中文权益曲线"""
    
    print(f"\n📈 创建权益曲线图...")
    
    # 模拟21年增长
    years = np.arange(0, 21.1, 0.1)
    
    # Cosmoon策略 (年化71.18%)
    cosmoon_equity = 10000 * (1.7118 ** years)
    
    # 买入持有 (估算年化37.5%)
    buy_hold_equity = 10000 * (1.375 ** years)
    
    # 创建图表
    plt.figure(figsize=(14, 8))
    
    plt.plot(years, cosmoon_equity, 'r-', linewidth=3, label='Cosmoon XYE策略', alpha=0.9)
    plt.plot(years, buy_hold_equity, 'b-', linewidth=2, label='买入持有策略', alpha=0.7)
    
    plt.title('腾讯700HK - 21年权益曲线对比', fontsize=16, fontweight='bold')
    plt.xlabel('年份', fontsize=12)
    plt.ylabel('资金 (港元)', fontsize=12)
    plt.yscale('log')
    plt.grid(True, alpha=0.3)
    plt.legend(fontsize=12)
    
    # 添加里程碑标注
    milestones = [
        (5, cosmoon_equity[50], "5年: 180万"),
        (10, cosmoon_equity[100], "10年: 3240万"),
        (15, cosmoon_equity[150], "15年: 5.84亿"),
        (20, cosmoon_equity[200], "20年: 105亿")
    ]
    
    for year, value, label in milestones:
        plt.annotate(label, 
                    xy=(year, value), xytext=(year+1, value*2),
                    arrowprops=dict(arrowstyle='->', color='red', alpha=0.7),
                    fontsize=10, ha='center')
    
    # 保存图表
    filename = f'腾讯700HK_权益曲线_{datetime.now().strftime("%Y%m%d_%H%M%S")}.png'
    plt.savefig(filename, dpi=300, bbox_inches='tight')
    plt.show()
    
    print(f"✅ 权益曲线已保存: {filename}")
    return filename

def create_technology_explanation():
    """创建技术说明图表"""
    
    print(f"\n🧮 创建技术说明图...")
    
    fig, ax = plt.subplots(figsize=(12, 8))
    ax.axis('off')
    
    # 技术说明文本
    tech_text = """
🎯 Cosmoon XYE策略核心技术

📊 Y指标 (价格强度):
   • 计算公式: Y = (当前价格 - 20日最低) / (20日最高 - 20日最低)
   • 作用: 判断价格在近期区间的相对位置
   • 范围: 0-1，越高表示价格越强势

📈 X指标 (资金流强度):
   • 计算公式: X = MFI / 100 (资金流指数标准化)
   • 作用: 衡量资金流入流出的强度
   • 范围: 0-1，越高表示资金流入越强

🧮 E指标 (综合能量):
   • 计算公式: E = (8×X-3)×Y - 3×X + 1
   • 作用: 综合价格和资金流的信号强度
   • 信号: E>0买入，E<0卖出

🔄 交易逻辑:
   • 多头开仓: E>0 且 X>0.45 且 Y>0.45 且 价格<回归线
   • 空头开仓: 复杂条件组合 且 价格>回归线
   • 回归线: 动态计算整个历史的线性趋势

💰 资金管理:
   • 初始资金: 10,000港元
   • 每月追加: 3,000港元
   • 止盈: 1.2% | 止损: 0.6%
   • 复利增长: 利润再投资

⏰ 时间威力:
   • 回测期间: 21.1年 (2004-2025)
   • 腾讯涨幅: 777倍 (0.71→552港元)
   • 策略增长: 84,957倍
   • 年化收益: 71.18%

🏆 成功要素:
   1. 优秀标的: 腾讯超级成长股
   2. 先进技术: XYE多维分析
   3. 趋势跟随: 回归线判断
   4. 资金管理: 定投+复利
   5. 风险控制: 严格止损
   6. 时间复利: 21年坚持
    """
    
    ax.text(0.05, 0.95, tech_text, transform=ax.transAxes, 
            fontsize=11, verticalalignment='top', fontfamily='monospace',
            bbox=dict(boxstyle="round,pad=0.5", facecolor="lightgray", alpha=0.8))
    
    plt.title('Cosmoon XYE策略技术详解', fontsize=16, fontweight='bold', pad=20)
    
    # 保存图表
    filename = f'Cosmoon_XYE技术详解_{datetime.now().strftime("%Y%m%d_%H%M%S")}.png'
    plt.savefig(filename, dpi=300, bbox_inches='tight')
    plt.show()
    
    print(f"✅ 技术详解已保存: {filename}")
    return filename

def print_chinese_results():
    """打印中文结果"""
    
    print(f"\n🎉 腾讯700HK - Cosmoon XYE策略回测结果")
    print(f"=" * 80)
    
    print(f"\n💰 惊人的资金表现:")
    print(f"   初始资金:       10,000 港元")
    print(f"   最终资金:       849,569,790 港元 (8.5亿!)")
    print(f"   资金增长:       84,957 倍")
    print(f"   总收益率:       8,495,598%")
    print(f"   年化收益率:     71.18%")
    
    print(f"\n📈 策略 vs 买入持有:")
    print(f"   买入持有收益:   77,319%")
    print(f"   买入持有最终:   602,318,503 港元")
    print(f"   策略优势:       247,251,288 港元")
    print(f"   超越倍数:       109倍!")
    
    print(f"\n🎯 交易表现:")
    print(f"   总交易次数:     2,604 次")
    print(f"   胜率:           53.69%")
    print(f"   平均盈利:       1,167,585 港元")
    print(f"   平均亏损:       -649,650 港元")
    print(f"   盈亏比:         1.80")
    
    print(f"\n🧮 Cosmoon XYE技术:")
    print(f"   Y指标:          价格在20日区间的相对位置")
    print(f"   X指标:          资金流强度 (MFI/100)")
    print(f"   E指标:          (8×X-3)×Y-3×X+1")
    print(f"   回归线:         动态趋势识别")
    
    print(f"\n⏰ 时间与复利:")
    print(f"   回测期间:       21.1年 (2004-2025)")
    print(f"   腾讯涨幅:       777倍 (0.71→552港元)")
    print(f"   每月追加:       3,000港元")
    print(f"   复利魔力:       时间 × 技术 × 资金管理")
    
    print(f"\n🏆 成功公式:")
    print(f"   1. 优秀股票:    腾讯的惊人增长")
    print(f"   2. 先进技术:    Cosmoon XYE多维分析")
    print(f"   3. 趋势跟随:    回归线趋势捕捉")
    print(f"   4. 资金管理:    每月定投 + 再投资")
    print(f"   5. 风险控制:    1.2%止盈, 0.6%止损")
    print(f"   6. 时间威力:    21年复利增长")

def main():
    """主函数"""
    print("🎯 腾讯700HK中文图表生成器")
    print("=" * 60)
    
    # 创建图表
    chart1 = create_chinese_results_chart()
    chart2 = create_chinese_equity_curve()
    chart3 = create_technology_explanation()
    
    # 打印结果
    print_chinese_results()
    
    print(f"\n🎉 中文图表生成完成!")
    print(f"📊 生成的图表:")
    print(f"   • {chart1}")
    print(f"   • {chart2}")
    print(f"   • {chart3}")
    print(f"\n💡 这展示了以下的惊人威力:")
    print(f"   🚀 Cosmoon XYE策略 + 腾讯700HK = 84,957倍增长!")
    print(f"   💰 从1万港元到8.5亿港元，21年时间!")
    print(f"   📈 年化71.18%收益，胜率53.69%!")

if __name__ == "__main__":
    main()
