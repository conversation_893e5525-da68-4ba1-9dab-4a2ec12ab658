#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
生成优化的高回报腾讯700HK交易记录
===============================

优化策略逻辑，减少交易频率，提高回报率
重点关注长期趋势和复利效应

作者: Cosmoon NG
"""

import pandas as pd
import numpy as np
import yfinance as yf
from datetime import datetime, timedelta
from scipy import stats
import warnings
warnings.filterwarnings('ignore')

def generate_optimized_high_return_excel():
    """生成优化的高回报交易记录"""
    
    print("🚀 生成优化高回报腾讯700HK交易记录...")
    print("=" * 60)
    
    # 优化参数
    initial_capital = 10000.00
    monthly_addition = 3000.00
    commission_rate = 0.001
    
    # 更宽松的止盈止损 (减少频繁交易)
    take_profit_long = 0.05     # 多头止盈5%
    stop_loss_long = 0.02       # 多头止损2%
    take_profit_short = 0.03    # 空头止盈3%
    stop_loss_short = 0.015     # 空头止损1.5%
    
    print(f"📊 优化策略参数:")
    print(f"   初始资金: {initial_capital:,.0f}港元")
    print(f"   每月追加: {monthly_addition:,.0f}港元")
    print(f"   多头止盈/止损: +{take_profit_long*100:.1f}%/-{stop_loss_long*100:.1f}%")
    print(f"   空头止盈/止损: +{take_profit_short*100:.1f}%/-{stop_loss_short*100:.1f}%")
    print(f"   策略特点: 减少交易频率，注重长期趋势")
    
    # 获取数据
    print(f"\n📈 获取腾讯历史数据...")
    try:
        end_date = datetime.now()
        start_date = end_date - timedelta(days=25*365)
        
        ticker = yf.Ticker("0700.HK")
        hist_data = ticker.history(start=start_date, end=end_date)
        
        df = pd.DataFrame({
            'date': hist_data.index,
            'open': hist_data['Open'],
            'high': hist_data['High'],
            'low': hist_data['Low'],
            'close': hist_data['Close'],
            'volume': hist_data['Volume']
        })
        
        df = df.dropna().sort_values('date').reset_index(drop=True)
        
        print(f"   ✅ 获取 {len(df)} 条数据")
        print(f"   📈 价格增长: {df['close'].iloc[0]:.2f} → {df['close'].iloc[-1]:.2f} 港元")
        print(f"   🚀 总涨幅: {(df['close'].iloc[-1]/df['close'].iloc[0]-1)*100:.0f}%")
        
    except Exception as e:
        print(f"   ❌ 数据获取失败: {e}")
        return None
    
    # 计算技术指标
    print(f"\n🧮 计算优化技术指标...")
    
    # Y指标 (使用更长周期)
    window = 50  # 增加到50日
    df['high_50'] = df['high'].rolling(window).max()
    df['low_50'] = df['low'].rolling(window).min()
    df['y_value'] = (df['close'] - df['low_50']) / (df['high_50'] - df['low_50'])
    df['y_value'] = df['y_value'].fillna(0.5).clip(0, 1)
    
    # X指标 (资金流)
    df['typical_price'] = (df['high'] + df['low'] + df['close']) / 3
    df['money_flow'] = df['typical_price'] * df['volume']
    df['price_change'] = df['typical_price'].diff()
    
    df['positive_mf'] = np.where(df['price_change'] > 0, df['money_flow'], 0)
    df['negative_mf'] = np.where(df['price_change'] < 0, df['money_flow'], 0)
    
    # 使用更长周期
    period = 30  # 增加到30日
    df['positive_mf_30'] = df['positive_mf'].rolling(period).sum()
    df['negative_mf_30'] = df['negative_mf'].rolling(period).sum()
    df['money_flow_ratio'] = df['positive_mf_30'] / (df['negative_mf_30'] + 1e-10)
    
    df['mfi'] = 100 - (100 / (1 + df['money_flow_ratio']))
    df['x_value'] = df['mfi'] / 100
    
    # E指标
    df['e_value'] = (8 * df['x_value'] - 3) * df['y_value'] - 3 * df['x_value'] + 1
    
    # 长期移动平均线 (趋势判断)
    df['ma_200'] = df['close'].rolling(200).mean()
    df['ma_50'] = df['close'].rolling(50).mean()
    df['trend'] = np.where(df['ma_50'] > df['ma_200'], 1, -1)  # 1=上升趋势, -1=下降趋势
    
    # 回归线
    df['i'] = range(1, len(df) + 1)
    slope, intercept, r_value, _, _ = stats.linregress(df['i'], df['close'])
    df['regression_line'] = intercept + slope * df['i']
    df['price_position'] = (df['close'] - df['regression_line']) / df['regression_line']
    
    print(f"   ✅ 技术指标计算完成")
    print(f"   📊 回归线斜率: {slope:.6f} (年化: {slope*365:.2f}港元)")
    
    # 优化交易回测
    print(f"\n💼 运行优化交易回测...")
    
    trading_records = []
    capital = initial_capital
    position = 0
    entry_price = 0
    shares = 0
    trade_count = 0
    winning_trades = 0
    last_month = None
    hold_days = 0  # 持仓天数
    
    for i in range(200, len(df)):  # 从第200天开始，确保指标稳定
        row = df.iloc[i]
        current_date = row['date']
        current_price = row['close']
        
        # 每月追加资金
        current_month = current_date.replace(day=1)
        if last_month is None or current_month > last_month:
            capital += monthly_addition
            last_month = current_month
        
        # 检查止盈止损
        if position != 0:
            hold_days += 1
            
            if position == 1:  # 多头
                profit_ratio = (current_price - entry_price) / entry_price
                
                if profit_ratio >= take_profit_long:  # 止盈
                    profit = shares * (current_price - entry_price) * (1 - commission_rate)
                    capital += shares * current_price * (1 - commission_rate)
                    if profit > 0:
                        winning_trades += 1
                    position = 0
                    shares = 0
                    trade_count += 1
                    hold_days = 0
                    
                elif profit_ratio <= -stop_loss_long:  # 止损
                    loss = shares * (current_price - entry_price) * (1 - commission_rate)
                    capital += shares * current_price * (1 - commission_rate)
                    position = 0
                    shares = 0
                    trade_count += 1
                    hold_days = 0
                    
            elif position == -1:  # 空头
                profit_ratio = (entry_price - current_price) / entry_price
                
                if profit_ratio >= take_profit_short:  # 止盈
                    profit = shares * (entry_price - current_price) * (1 - commission_rate)
                    capital += profit
                    if profit > 0:
                        winning_trades += 1
                    position = 0
                    shares = 0
                    trade_count += 1
                    hold_days = 0
                    
                elif profit_ratio <= -stop_loss_short:  # 止损
                    loss = shares * (entry_price - current_price) * (1 - commission_rate)
                    capital += loss
                    position = 0
                    shares = 0
                    trade_count += 1
                    hold_days = 0
        
        # 优化开仓条件 (更严格的条件，减少交易频率)
        if position == 0:
            # 强势多头信号 (只在明确上升趋势中做多)
            if (row['trend'] == 1 and  # 上升趋势
                row['e_value'] > 0.5 and  # 强势E信号
                row['x_value'] > 0.6 and  # 强势资金流
                row['y_value'] > 0.7 and  # 价格位置较高
                row['price_position'] < -0.05):  # 价格明显低于回归线
                
                position = 1
                entry_price = current_price
                shares = int(capital * 0.9 / current_price)  # 90%仓位
                cost = shares * current_price * (1 + commission_rate)
                capital -= cost
                
            # 强势空头信号 (只在明确下降趋势中做空)
            elif (row['trend'] == -1 and  # 下降趋势
                  row['e_value'] < -0.5 and  # 弱势E信号
                  row['x_value'] < 0.4 and  # 弱势资金流
                  row['y_value'] < 0.3 and  # 价格位置较低
                  row['price_position'] > 0.05):  # 价格明显高于回归线
                
                position = -1
                entry_price = current_price
                shares = int(capital * 0.9 / current_price)  # 90%仓位
                cost = shares * current_price * commission_rate
                capital -= cost
        
        # 计算当前状态
        if position != 0 and shares > 0:
            if position == 1:
                unrealized_pnl = shares * (current_price - entry_price)
                current_market_value = shares * current_price
            else:
                unrealized_pnl = shares * (entry_price - current_price)
                current_market_value = shares * current_price
            total_assets = capital + current_market_value + unrealized_pnl
        else:
            unrealized_pnl = 0
            current_market_value = 0
            total_assets = capital
        
        # 计算收益率
        months_passed = (current_date.year - df['date'].iloc[0].year) * 12 + (current_date.month - df['date'].iloc[0].month)
        total_invested = initial_capital + months_passed * monthly_addition
        daily_return = (total_assets - total_invested) / total_invested * 100 if total_invested > 0 else 0
        cumulative_return = (total_assets - initial_capital) / initial_capital * 100
        
        # 止盈止损价格
        if position == 1:
            take_profit_price = entry_price * (1 + take_profit_long) if entry_price > 0 else current_price * (1 + take_profit_long)
            stop_loss_price = entry_price * (1 - stop_loss_long) if entry_price > 0 else current_price * (1 - stop_loss_long)
        elif position == -1:
            take_profit_price = entry_price * (1 - take_profit_short) if entry_price > 0 else current_price * (1 - take_profit_short)
            stop_loss_price = entry_price * (1 + stop_loss_short) if entry_price > 0 else current_price * (1 + stop_loss_short)
        else:
            take_profit_price = current_price * (1 + take_profit_long)
            stop_loss_price = current_price * (1 - stop_loss_long)
        
        # 交易信号
        if position == 1:
            signal = f"持有多头({hold_days}天)"
            trade_type = "持仓"
            trade_direction = "多头"
        elif position == -1:
            signal = f"持有空头({hold_days}天)"
            trade_type = "持仓"
            trade_direction = "空头"
        else:
            if row['trend'] == 1:
                signal = "上升趋势观望"
            else:
                signal = "下降趋势观望"
            trade_type = "空仓"
            trade_direction = "空仓"
        
        # 风险等级
        if abs(row['e_value']) > 1.0:
            risk_level = "高风险"
        elif abs(row['e_value']) > 0.5:
            risk_level = "中风险"
        else:
            risk_level = "低风险"
        
        # 创建29字段记录
        record = {
            '交易日期': current_date.strftime('%Y-%m-%d'),
            '交易类型': trade_type,
            '交易方向': trade_direction,
            '交易价格': round(current_price, 2),
            '入场价格': round(entry_price, 2) if entry_price > 0 else round(current_price, 2),
            '止盈价': round(take_profit_price, 2),
            '止损价': round(stop_loss_price, 2),
            '持仓数量': shares,
            '交易金额': round(shares * current_price, 2) if shares > 0 else 0,
            '手续费': round(shares * current_price * commission_rate, 2) if shares > 0 else 0,
            '净交易额': round(shares * current_price * (1 - commission_rate), 2) if shares > 0 else 0,
            '持仓成本': round(entry_price, 2) if entry_price > 0 else 0,
            '当前市值': round(current_market_value, 2),
            '浮动盈亏': round(unrealized_pnl, 2),
            '实现盈亏': 0.00,
            '累计盈亏': round(unrealized_pnl, 2),
            '账户余额': round(capital, 2),
            '总资产': round(total_assets, 2),
            '收益率': round(daily_return, 2),
            '累计收益率': round(cumulative_return, 2),
            'Y值': round(row['y_value'], 4),
            'Full_Y': round(row['y_value'] * 0.9 + 0.05, 4),
            'X值': round(row['x_value'], 4),
            'MoneyFlowRatio': round(row['money_flow_ratio'], 4),
            'E值': round(row['e_value'], 4),
            'MyE': round(row['e_value'] * 1.1, 4),
            '信号强度': signal,
            '风险等级': risk_level,
            '备注': f'优化版 趋势{row["trend"]} 价格{current_price:.2f} {signal} 资产{total_assets:,.0f}港元'
        }
        
        trading_records.append(record)
    
    # 保存Excel
    final_df = pd.DataFrame(trading_records)
    filename = f'交易记录追踪0700HK_优化高回报版_{datetime.now().strftime("%Y%m%d_%H%M%S")}.xlsx'
    final_df.to_excel(filename, index=False)
    
    # 统计结果
    final_record = trading_records[-1]
    total_months = months_passed
    total_invested = initial_capital + total_months * monthly_addition
    final_assets = final_record['总资产']
    total_return = (final_assets - total_invested) / total_invested * 100
    asset_multiple = final_assets / initial_capital
    annual_return = ((final_assets / total_invested) ** (12 / total_months) - 1) * 100
    
    print(f"\n✅ 优化高回报Excel已生成: {filename}")
    print(f"📊 包含 {len(final_df)} 条记录，29个完整字段")
    
    print(f"\n🚀 优化策略统计:")
    print(f"   交易次数: {trade_count} (大幅减少)")
    print(f"   盈利交易: {winning_trades}")
    print(f"   胜率: {winning_trades/trade_count*100:.1f}%" if trade_count > 0 else "   胜率: 0%")
    print(f"   投资期间: {total_months}个月 ({total_months/12:.1f}年)")
    print(f"   初始资金: {initial_capital:,.0f}港元")
    print(f"   总投入: {total_invested:,.0f}港元")
    print(f"   最终资产: {final_assets:,.0f}港元")
    print(f"   净收益: {final_assets - total_invested:,.0f}港元")
    print(f"   总收益率: {total_return:.1f}%")
    print(f"   资产倍数: {asset_multiple:.1f}倍")
    print(f"   年化收益: {annual_return:.1f}%")
    
    return filename

def main():
    """主函数"""
    print("🚀 腾讯700HK优化高回报交易记录生成器")
    print("=" * 60)
    print("📈 优化策略: 减少交易频率，注重长期趋势")
    print("💰 更宽松的止盈止损，更严格的开仓条件")
    print("🎯 充分利用腾讯777倍增长的历史机会")
    
    filename = generate_optimized_high_return_excel()
    
    if filename:
        print(f"\n🎉 优化高回报Excel生成成功!")
        print(f"📁 文件: {filename}")
        print(f"🚀 基于优化策略的高回报交易记录")
        print(f"📈 减少交易频率，提高盈利质量")
        print(f"💰 完整的29字段交易记录")
    else:
        print(f"\n❌ 生成失败")

if __name__ == "__main__":
    main()
