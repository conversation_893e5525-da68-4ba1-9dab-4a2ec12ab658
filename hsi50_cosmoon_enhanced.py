#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
HSI50增强版Cosmoon策略
====================
基于reference.py，结合新数据库的y_probability数据

特点：
1. 使用回归中线作为趋势判断
2. 使用凯利公式优化仓位
3. 采用Cosmoon XYE方法 + y_probability增强
4. 每月复利加入3000

作者: 基于Cosmoon NG的reference.py改进
日期: 2025年7月
"""

import mysql.connector
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import matplotlib.pyplot as plt
from scipy import stats
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

class HSI50CosmoonEnhanced:
    def __init__(self):
        """初始化回测系统"""
        # 数据库配置
        self.db_config = {
            'host': 'localhost',
            'database': 'finance',
            'user': 'root',
            'password': '12345678',
            'charset': 'utf8mb4'
        }
        
        # 策略参数 (基于reference.py)
        self.initial_capital = 30000      # 初始资金
        self.monthly_addition = 3000      # 每月追加资金
        self.take_profit_long = 0.012     # 多头止盈 1.2%
        self.stop_loss_long = 0.006       # 多头止损 0.6%
        self.take_profit_short = 0.008    # 空头止盈 0.8%
        self.stop_loss_short = 0.012      # 空头止损 1.2%
        self.regression_threshold = 0.002 # 回归线阈值 0.2%
        self.kelly_fraction = 0.5         # 凯利公式分数
        
        # 状态变量
        self.position = 0                 # 当前持仓
        self.current_price = 0            # 当前持仓价格
        self.trades = []
        self.equity_curve = []
        
    def load_data(self):
        """从新数据库加载数据"""
        print("\n1. 从新数据库加载数据...")
        try:
            connection = mysql.connector.connect(**self.db_config)
            
            # 加载最近5年数据，包含y_probability
            query = """
                SELECT Date, Open, High, Low, Close, Volume,
                       ma_20, ma_60, y_probability, 
                       new_midprice, new_controller, new_Full_Y
                FROM hkhsi50 
                WHERE Date >= '2020-01-01'
                AND y_probability IS NOT NULL
                ORDER BY Date ASC
            """
            
            self.df = pd.read_sql(query, connection)
            connection.close()
            
            # 数据预处理
            self.df['date'] = pd.to_datetime(self.df['Date'])
            self.df.rename(columns={
                'Open': 'open', 'High': 'high', 'Low': 'low', 
                'Close': 'close', 'Volume': 'volume'
            }, inplace=True)
            
            # 计算缺失的技术指标
            self.calculate_additional_indicators()
            
            print(f"✓ 成功加载 {len(self.df)} 条数据")
            print(f"数据范围：{self.df['date'].min().date()} 至 {self.df['date'].max().date()}")
            
            return True
            
        except Exception as e:
            print(f"❌ 数据加载失败: {e}")
            return False
    
    def calculate_additional_indicators(self):
        """计算额外的技术指标"""
        print("   计算技术指标...")
        
        # RSI指标
        delta = self.df['close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
        rs = gain / loss
        self.df['rsi'] = 100 - (100 / (1 + rs))
        
        # 成交量比率 (当前成交量/20日平均成交量)
        self.df['volume_ratio'] = self.df['volume'] / self.df['volume'].rolling(20).mean()
        
        # 资金流入比率 (基于y_probability)
        self.df['inflow_ratio'] = self.df['y_probability']
        
        # 添加序号用于回归计算
        self.df['i'] = range(1, len(self.df) + 1)
        
    def calculate_regression_line(self):
        """计算回归线和技术指标 (基于reference.py)"""
        print("\n2. 计算回归线和技术指标...")
        
        # 计算60日回归线
        window = 60
        self.df['regression_line'] = self.df['close'].rolling(window=window).apply(
            lambda x: stats.linregress(range(len(x)), x)[0] * (window-1) + stats.linregress(range(len(x)), x)[1]
        )
        
        # 计算价格相对回归线的位置（百分比）
        self.df['price_position'] = (self.df['close'] - self.df['regression_line']) / self.df['regression_line']
        
        # 计算趋势强度（回归线斜率）
        self.df['trend_strength'] = self.df['regression_line'].diff() / self.df['regression_line'].shift(1)
        
        # 计算波动率（20日）
        self.df['volatility'] = self.df['close'].pct_change().rolling(window=20).std()
        
        # 计算RSI的变化率
        self.df['rsi_change'] = self.df['rsi'].diff()
        
        print("✓ 技术指标计算完成")
    
    def calculate_kelly(self, win_rate, profit_ratio, loss_ratio):
        """计算凯利公式建议仓位 (来自reference.py)"""
        if win_rate <= 0 or profit_ratio <= 0:
            return 0
        q = 1 - win_rate
        kelly = (win_rate/q * profit_ratio - 1) / profit_ratio
        kelly *= self.kelly_fraction
        return max(0, min(kelly, 0.5))  # 最大仓位限制在50%
    
    def get_position_size(self, price, capital, volatility):
        """计算仓位大小 (来自reference.py，增强版)"""
        # 基于历史数据计算胜率和盈亏比
        if len(self.trades) < 10:
            win_rate = 0.5
        else:
            trades_df = pd.DataFrame(self.trades)
            profit_trades = trades_df[trades_df.get('profit', 0) > 0]
            win_rate = len(profit_trades) / len(trades_df)
        
        # 根据持仓方向设置盈亏比
        if self.position == 1:  # 多头
            profit_ratio = self.take_profit_long
            loss_ratio = self.stop_loss_long
        else:  # 空头
            profit_ratio = self.take_profit_short
            loss_ratio = self.stop_loss_short
            
        # 使用凯利公式计算仓位比例
        kelly = self.calculate_kelly(win_rate, profit_ratio, loss_ratio)
        
        # 根据波动率调整仓位
        volatility_factor = 1 - min(volatility * 100, 0.5)
        
        return capital * kelly * volatility_factor
    
    def add_monthly_capital(self, date, capital):
        """每月增加资金 (来自reference.py)"""
        last_month = getattr(self, 'last_month', None)
        current_month = date.replace(day=1)
        
        if last_month is None or current_month > last_month:
            self.last_month = current_month
            return capital + self.monthly_addition
        
        return capital
    
    def check_entry_conditions(self, row):
        """检查入场条件 (基于reference.py，增强y_probability)"""
        # 检查趋势强度
        trend_is_strong = abs(row['trend_strength']) > 0.0005
        
        # 检查RSI条件
        rsi_condition = (row['rsi'] < 30 and row['rsi_change'] > 0) or (row['rsi'] > 70 and row['rsi_change'] < 0)
        
        # 检查成交量条件
        volume_active = row['volume_ratio'] > 1.2
        
        # y_probability增强条件
        y_prob_bullish = row['y_probability'] > 0.52  # 控股商偏向托价
        y_prob_bearish = row['y_probability'] < 0.48  # 控股商偏向压价
        
        # 多头条件 (增强版)
        if (row['price_position'] < -self.regression_threshold and  # 价格显著低于回归线
            row['rsi'] < 30 and  # RSI超卖
            row['rsi_change'] > 0 and  # RSI开始上升
            trend_is_strong and  # 趋势强
            volume_active and  # 成交量活跃
            (row['inflow_ratio'] > 0.6 or y_prob_bullish)):  # 资金流入强势或y_probability看涨
            return 1
        
        # 空头条件 (增强版)
        elif (row['price_position'] > self.regression_threshold and  # 价格显著高于回归线
              row['rsi'] > 70 and  # RSI超买
              row['rsi_change'] < 0 and  # RSI开始下降
              trend_is_strong and  # 趋势强
              volume_active and  # 成交量活跃
              (row['inflow_ratio'] < 0.4 or y_prob_bearish)):  # 资金流出强势或y_probability看跌
            return -1
        
        return 0
    
    def run_backtest(self):
        """运行回测 (基于reference.py)"""
        print("\n3. 开始回测...")
        
        capital = self.initial_capital
        last_trade_date = None
        min_trade_interval = timedelta(days=5)
        
        for i in range(60, len(self.df)):  # 从第60天开始
            row = self.df.iloc[i]
            date = pd.to_datetime(row['date'])
            
            # 每月增加资金
            capital = self.add_monthly_capital(date, capital)
            
            # 记录权益
            self.equity_curve.append({
                'date': date,
                'equity': capital,
                'position': self.position
            })
            
            # 检查交易间隔
            if last_trade_date and (date - last_trade_date) < min_trade_interval:
                continue
            
            # 检查止盈止损
            if self.position != 0:
                if self.position == 1:  # 多头
                    profit_ratio = (row['high'] - self.current_price) / self.current_price
                    loss_ratio = (self.current_price - row['low']) / self.current_price
                    
                    if profit_ratio >= self.take_profit_long:  # 止盈
                        exit_price = self.current_price * (1 + self.take_profit_long)
                        profit = (exit_price - self.current_price) / self.current_price * capital
                        capital += profit
                        self.position = 0
                        self.trades.append({
                            'date': date,
                            'type': 'long_exit_tp',
                            'price': exit_price,
                            'profit': profit,
                            'capital': capital
                        })
                    elif loss_ratio >= self.stop_loss_long:  # 止损
                        exit_price = self.current_price * (1 - self.stop_loss_long)
                        loss = (exit_price - self.current_price) / self.current_price * capital
                        capital += loss
                        self.position = 0
                        self.trades.append({
                            'date': date,
                            'type': 'long_exit_sl',
                            'price': exit_price,
                            'profit': loss,
                            'capital': capital
                        })
                
                elif self.position == -1:  # 空头
                    profit_ratio = (self.current_price - row['low']) / self.current_price
                    loss_ratio = (row['high'] - self.current_price) / self.current_price
                    
                    if profit_ratio >= self.take_profit_short:  # 止盈
                        exit_price = self.current_price * (1 - self.take_profit_short)
                        profit = (self.current_price - exit_price) / self.current_price * capital
                        capital += profit
                        self.position = 0
                        self.trades.append({
                            'date': date,
                            'type': 'short_exit_tp',
                            'price': exit_price,
                            'profit': profit,
                            'capital': capital
                        })
                    elif loss_ratio >= self.stop_loss_short:  # 止损
                        exit_price = self.current_price * (1 + self.stop_loss_short)
                        loss = (exit_price - self.current_price) / self.current_price * capital * -1
                        capital += loss
                        self.position = 0
                        self.trades.append({
                            'date': date,
                            'type': 'short_exit_sl',
                            'price': exit_price,
                            'profit': loss,
                            'capital': capital
                        })
            
            # 检查开仓条件
            if self.position == 0:
                position_signal = self.check_entry_conditions(row)
                
                if position_signal != 0:
                    position_size = self.get_position_size(row['close'], capital, row['volatility'])
                    
                    if position_size > 1000:  # 最小交易金额限制
                        self.position = position_signal
                        self.current_price = row['close']
                        last_trade_date = date
                        
                        self.trades.append({
                            'date': date,
                            'type': 'long_entry' if position_signal == 1 else 'short_entry',
                            'price': self.current_price,
                            'size': position_size,
                            'capital': capital,
                            'y_probability': row['y_probability']  # 记录y_probability
                        })
        
        self.final_capital = capital
        print(f"✓ 回测完成！最终资金：{self.final_capital:,.2f}")
    
    def analyze_results(self):
        """分析回测结果 (基于reference.py，增强版)"""
        print("\n=== Cosmoon增强版回测分析 ===")
        
        trades_df = pd.DataFrame(self.trades)
        if len(trades_df) == 0:
            print("没有产生任何交易")
            return
        
        # 基本统计
        entry_trades = trades_df[trades_df['type'].str.contains('entry')]
        exit_trades = trades_df[trades_df['type'].str.contains('exit')]
        
        total_trades = len(entry_trades)
        winning_trades = len(exit_trades[exit_trades['profit'] > 0]) if 'profit' in exit_trades.columns else 0
        
        print(f"\n📊 交易统计：")
        print(f"总交易次数：{total_trades}")
        print(f"盈利交易：{winning_trades}")
        print(f"亏损交易：{total_trades - winning_trades}")
        if total_trades > 0:
            print(f"胜率：{winning_trades/total_trades*100:.2f}%")
        
        # 盈亏分析
        if 'profit' in exit_trades.columns and len(exit_trades) > 0:
            profit_trades = exit_trades[exit_trades['profit'] > 0]
            loss_trades = exit_trades[exit_trades['profit'] < 0]
            
            if len(profit_trades) > 0:
                print(f"平均盈利：{profit_trades['profit'].mean():.2f}")
                print(f"最大盈利：{profit_trades['profit'].max():.2f}")
            
            if len(loss_trades) > 0:
                print(f"平均亏损：{loss_trades['profit'].mean():.2f}")
                print(f"最大亏损：{loss_trades['profit'].min():.2f}")
        
        # 收益率分析
        initial_equity = self.initial_capital
        final_equity = self.final_capital
        total_days = (self.df['date'].max() - self.df['date'].min()).days
        total_years = total_days / 365
        
        # 计算总投入 (包括定投)
        months = total_days / 30
        total_invested = initial_equity + months * self.monthly_addition
        
        net_profit = final_equity - total_invested
        total_return = net_profit / total_invested if total_invested > 0 else 0
        annual_return = (final_equity / total_invested) ** (1/total_years) - 1 if total_years > 0 else 0
        
        print(f"\n💰 收益统计：")
        print(f"初始资金：{initial_equity:,.2f}")
        print(f"总投入：{total_invested:,.2f}")
        print(f"最终资金：{final_equity:,.2f}")
        print(f"净收益：{net_profit:,.2f}")
        print(f"总收益率：{total_return*100:.2f}%")
        print(f"年化收益率：{annual_return*100:.2f}%")
        
        # y_probability分析
        if 'y_probability' in entry_trades.columns:
            print(f"\n🎯 y_probability分析：")
            avg_y_prob = entry_trades['y_probability'].mean()
            print(f"平均入场y_probability：{avg_y_prob:.4f}")
            
            # 按y_probability分组分析
            high_y_trades = entry_trades[entry_trades['y_probability'] > 0.52]
            low_y_trades = entry_trades[entry_trades['y_probability'] < 0.48]
            
            print(f"高y_probability交易(>0.52)：{len(high_y_trades)} 笔")
            print(f"低y_probability交易(<0.48)：{len(low_y_trades)} 笔")
        
        # 与买入持有比较
        hsi_start = self.df['close'].iloc[60]  # 从回测开始点
        hsi_end = self.df['close'].iloc[-1]
        buy_hold_return = (hsi_end - hsi_start) / hsi_start
        buy_hold_annual = (buy_hold_return + 1) ** (1/total_years) - 1
        
        print(f"\n📊 策略对比：")
        print(f"Cosmoon增强策略年化收益：{annual_return*100:.2f}%")
        print(f"买入持有年化收益：{buy_hold_annual*100:.2f}%")
        print(f"超额收益：{(annual_return - buy_hold_annual)*100:+.2f}%")
        
        # 保存结果
        try:
            equity_df = pd.DataFrame(self.equity_curve)
            plt.figure(figsize=(15, 7))
            plt.plot(equity_df['date'], equity_df['equity'])
            plt.title('Cosmoon增强版权益曲线')
            plt.xlabel('日期')
            plt.ylabel('资金')
            plt.grid(True)
            plt.savefig('cosmoon_enhanced_equity_curve.png')
            plt.close()
            
            if len(trades_df) > 0:
                trades_df.to_excel('cosmoon_enhanced_trades.xlsx', index=False)
                print("\n📄 交易记录已保存到 cosmoon_enhanced_trades.xlsx")
            
            print("📈 权益曲线已保存到 cosmoon_enhanced_equity_curve.png")
            
        except Exception as e:
            print(f"保存文件时出错: {e}")

def main():
    """主函数"""
    print("🎯 HSI50 Cosmoon增强版策略")
    print("基于reference.py + y_probability数据")
    print("="*60)
    
    try:
        backtest = HSI50CosmoonEnhanced()
        
        if not backtest.load_data():
            return
        
        backtest.calculate_regression_line()
        backtest.run_backtest()
        backtest.analyze_results()
        
        print("\n🎉 Cosmoon增强版策略测试完成！")
        
    except Exception as e:
        print(f"❌ 运行失败: {e}")

if __name__ == "__main__":
    main()
