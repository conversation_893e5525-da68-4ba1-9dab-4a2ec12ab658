#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
修复版数据库更新脚本 - 无Unicode字符
"""

import yfinance as yf
import pandas as pd
import numpy as np
import mysql.connector
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

def get_eab_table_structure():
    """获取eab_0023hk表结构"""
    try:
        config = {
            'host': 'localhost',
            'port': 3306,
            'user': 'root',
            'password': '12345678',
            'database': 'finance'
        }

        connection = mysql.connector.connect(**config)
        cursor = connection.cursor()

        cursor.execute("DESCRIBE eab_0023hk")
        columns = cursor.fetchall()

        print("eab_0023hk表结构:")
        for col in columns:
            print(f"   {col[0]} - {col[1]} - {col[2]} - {col[3]}")

        cursor.close()
        connection.close()
        return True

    except Exception as e:
        print(f"获取表结构失败: {e}")
        return False

def fetch_and_calculate_data():
    """从yfinance获取数据并计算技术指标"""
    try:
        print("从yfinance获取0023.HK数据...")
        ticker = yf.Ticker("0023.HK")
        hist = ticker.history(period="60d")

        if hist.empty:
            print("无法获取数据")
            return None

        print(f"获取到 {len(hist)} 天的历史数据")

        # 计算技术指标
        print("计算技术指标...")

        # 基础数据
        df = hist.copy()
        df['Date'] = df.index.date

        # 计算TypicalPrice
        df['TypicalPrice'] = (df['High'] + df['Low'] + df['Close']) / 3

        # 计算MoneyFlow
        df['MoneyFlow'] = df['TypicalPrice'] * df['Volume']

        # 计算价格变化
        df['PriceChange'] = df['TypicalPrice'].diff()

        # 计算正负资金流
        df['PositiveMoneyFlow'] = np.where(df['PriceChange'] > 0, df['MoneyFlow'], 0)
        df['NegativeMoneyFlow'] = np.where(df['PriceChange'] < 0, df['MoneyFlow'], 0)

        # 计算14日资金流
        df['PositiveMoneyFlow_14'] = df['PositiveMoneyFlow'].rolling(window=14).sum()
        df['NegativeMoneyFlow_14'] = df['NegativeMoneyFlow'].rolling(window=14).sum()

        # 计算MoneyFlowRatio
        df['MoneyFlowRatio'] = df['PositiveMoneyFlow_14'] / df['NegativeMoneyFlow_14']
        df['MoneyFlowRatio'] = df['MoneyFlowRatio'].fillna(0)

        # 计算MFI
        df['MFI'] = 100 - (100 / (1 + df['MoneyFlowRatio']))
        df['MFI'] = df['MFI'].fillna(50)

        # 计算Y_Value (20日价格位置)
        df['High_20'] = df['High'].rolling(window=20).max()
        df['Low_20'] = df['Low'].rolling(window=20).min()
        df['Y_Value'] = (df['Close'] - df['Low_20']) / (df['High_20'] - df['Low_20'])
        df['Y_Value'] = df['Y_Value'].fillna(0.5)

        # 计算X_Value (基于MFI)
        df['X_Value'] = df['MFI'] / 100

        # 计算E_Value (Cosmoon综合能量指标)
        df['E_Value'] = (8 * df['X_Value'] - 3) * df['Y_Value'] - 3 * df['X_Value'] + 1

        # 计算RSI
        delta = df['Close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
        rs = gain / loss
        df['RSI'] = 100 - (100 / (1 + rs))
        df['RSI'] = df['RSI'].fillna(50)

        # 获取最新数据
        latest = df.iloc[-1]

        result = {
            'Date': latest['Date'],
            'Open': float(latest['Open']),
            'High': float(latest['High']),
            'Low': float(latest['Low']),
            'Close': float(latest['Close']),
            'Volume': int(latest['Volume']),
            'TypicalPrice': float(latest['TypicalPrice']),
            'MoneyFlow': float(latest['MoneyFlow']),
            'PositiveMoneyFlow': float(latest['PositiveMoneyFlow']),
            'NegativeMoneyFlow': float(latest['NegativeMoneyFlow']),
            'MoneyFlowRatio': float(latest['MoneyFlowRatio']),
            'MFI': float(latest['MFI']),
            'Y_Value': float(latest['Y_Value']),
            'X_Value': float(latest['X_Value']),
            'E_Value': float(latest['E_Value']),
            'RSI': float(latest['RSI'])
        }

        print("技术指标计算完成")
        return result

    except Exception as e:
        print(f"数据获取失败: {e}")
        return None

def update_eab_table(data):
    """更新eab_0023hk表"""
    try:
        print("更新finance.eab_0023hk表...")

        config = {
            'host': 'localhost',
            'port': 3306,
            'user': 'root',
            'password': '12345678',
            'database': 'finance'
        }

        connection = mysql.connector.connect(**config)
        cursor = connection.cursor()

        # 获取当前最大i值
        cursor.execute("SELECT MAX(i) FROM eab_0023hk")
        max_i = cursor.fetchone()[0]
        if max_i is None:
            max_i = 0

        new_i = max_i + 1
        print(f"当前最大i值: {max_i}, 新记录i值: {new_i}")

        # 检查今日记录是否已存在
        cursor.execute("SELECT COUNT(*) FROM eab_0023hk WHERE Date = %s", (data['Date'],))
        exists = cursor.fetchone()[0]

        if exists > 0:
            # 更新现有记录
            update_sql = """
            UPDATE eab_0023hk SET
                Open = %s, High = %s, Low = %s, Close = %s, Volume = %s,
                TypicalPrice = %s, MoneyFlow = %s, PositiveMoneyFlow = %s,
                NegativeMoneyFlow = %s, MoneyFlowRatio = %s, MFI = %s,
                Y_Value = %s, X_Value = %s, E_Value = %s, RSI = %s,
                updated_at = NOW()
            WHERE Date = %s
            """

            cursor.execute(update_sql, (
                data['Open'], data['High'], data['Low'], data['Close'], data['Volume'],
                data['TypicalPrice'], data['MoneyFlow'], data['PositiveMoneyFlow'],
                data['NegativeMoneyFlow'], data['MoneyFlowRatio'], data['MFI'],
                data['Y_Value'], data['X_Value'], data['E_Value'], data['RSI'],
                data['Date']
            ))

            print(f"更新了 {data['Date']} 的记录")
        else:
            # 插入新记录
            insert_sql = """
            INSERT INTO eab_0023hk (
                Date, Open, High, Low, Close, Volume,
                TypicalPrice, MoneyFlow, PositiveMoneyFlow, NegativeMoneyFlow,
                MoneyFlowRatio, MFI, Y_Value, X_Value, E_Value, RSI,
                i, created_at, updated_at
            ) VALUES (
                %s, %s, %s, %s, %s, %s,
                %s, %s, %s, %s,
                %s, %s, %s, %s, %s, %s,
                %s, NOW(), NOW()
            )
            """

            cursor.execute(insert_sql, (
                data['Date'], data['Open'], data['High'], data['Low'], data['Close'], data['Volume'],
                data['TypicalPrice'], data['MoneyFlow'], data['PositiveMoneyFlow'], data['NegativeMoneyFlow'],
                data['MoneyFlowRatio'], data['MFI'], data['Y_Value'], data['X_Value'], data['E_Value'], data['RSI'],
                new_i
            ))

            print(f"插入了 {data['Date']} 的新记录")

        connection.commit()
        cursor.close()
        connection.close()

        return True

    except Exception as e:
        print(f"数据库更新失败: {e}")
        return False

def call_stored_procedure():
    """调用存储过程"""
    try:
        print("调用存储过程...")

        config = {
            'host': 'localhost',
            'port': 3306,
            'user': 'root',
            'password': '12345678',
            'database': 'finance'
        }

        connection = mysql.connector.connect(**config)
        cursor = connection.cursor()

        cursor.callproc('sp_combined_stock_analysis', ['eab_0023hk'])

        # 清理存储过程结果 - 修复弃用警告
        results_count = 0
        try:
            while cursor.nextset():
                results_count += 1
        except Exception:
            # 没有更多结果集时会抛出异常，这是正常的
            pass

        connection.commit()
        cursor.close()
        connection.close()

        print(f"存储过程执行成功")
        return True

    except Exception as e:
        print(f"存储过程执行失败: {e}")
        return False

def main():
    """主函数"""
    print("香港交易所交易时间结束 下午5:00")
    print("开始每日更新流程...")
    print(f"更新时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)

    try:
        # 1. 验证数据库连接和表结构
        print("1. 验证数据库连接和表结构...")
        get_eab_table_structure()

        # 2. 从yfinance获取数据并计算指标
        print("\n2. 获取市场数据和计算技术指标...")
        data = fetch_and_calculate_data()

        if data:
            # 显示今日数据
            print(f"\n今日数据 ({data['Date']}):")
            print(f"   收盘价: {data['Close']:.2f} 港元")
            print(f"   成交量: {data['Volume']:,}")
            print(f"   Y值: {data['Y_Value']:.4f}")
            print(f"   X值: {data['X_Value']:.4f}")
            print(f"   E值: {data['E_Value']:.4f}")
            print(f"   RSI: {data['RSI']:.2f}")
            print(f"   MFI: {data['MFI']:.2f}")

            # 3. 更新eab_0023hk表
            print("\n3. 更新数据库...")
            db_success = update_eab_table(data)

            # 4. 调用存储过程
            print("\n4. 执行存储过程...")
            sp_success = call_stored_procedure()

            # 显示最终结果
            print("\n" + "=" * 60)
            print("更新结果汇总:")
            print(f"   数据库更新: {'成功' if db_success else '失败'}")
            print(f"   存储过程: {'成功' if sp_success else '失败'}")

            # 计算成功率
            success_count = sum([db_success, sp_success])
            success_rate = (success_count / 2) * 100
            print(f"   成功率: {success_rate:.1f}% ({success_count}/2)")

            if success_rate == 100:
                print("所有更新任务完成！")
            elif success_rate >= 50:
                print("主要任务完成，部分功能需要检查")
            else:
                print("多个任务失败，需要手动检查")

            print(f"\n更新完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            print("下次更新: 明日下午5:00")
            print("=" * 60)

        else:
            print("无法获取市场数据，更新失败")

    except Exception as e:
        print(f"程序执行失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
