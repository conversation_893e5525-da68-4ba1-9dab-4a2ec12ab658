
# 新的数据库配置模板
# ==================

# 标准配置
db_config = {
    'host': 'localhost',
    'database': 'finance',
    'user': 'root',
    'password': '12345678',
    'charset': 'utf8mb4',
    'autocommit': True
}

# 连接示例
import mysql.connector

try:
    connection = mysql.connector.connect(**db_config)
    cursor = connection.cursor()
    print("✅ 数据库连接成功")
    
    # 测试查询
    cursor.execute("SELECT DATABASE()")
    current_db = cursor.fetchone()[0]
    print(f"📊 当前数据库: {current_db}")
    
    cursor.close()
    connection.close()
    
except mysql.connector.Error as e:
    print(f"❌ 数据库连接失败: {e}")
