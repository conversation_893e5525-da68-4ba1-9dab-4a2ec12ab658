#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
验证和修复腾讯700HK数据库数据
============================

检查并修复eab_0700hk表中的技术指标计算

作者: Cosmoon NG
"""

import mysql.connector
import pandas as pd
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

def check_database_data():
    """检查数据库数据状态"""
    
    print("🔍 检查数据库数据状态...")
    
    config = {
        'host': 'localhost',
        'port': 3306,
        'user': 'root',
        'password': '12345678',
        'database': 'finance'
    }
    
    try:
        connection = mysql.connector.connect(**config)
        cursor = connection.cursor()
        
        # 检查数据完整性
        cursor.execute("""
            SELECT 
                COUNT(*) as total_rows,
                COUNT(Y_Value) as y_count,
                COUNT(X_Value) as x_count,
                COUNT(E_Value) as e_count,
                COUNT(Full_Y) as full_y_count,
                COUNT(MoneyFlowRatio) as mfr_count,
                COUNT(MyE) as mye_count,
                MIN(Date) as min_date,
                MAX(Date) as max_date,
                MIN(Close) as min_price,
                MAX(Close) as max_price
            FROM eab_0700hk
        """)
        
        result = cursor.fetchone()
        total, y_count, x_count, e_count, full_y_count, mfr_count, mye_count, min_date, max_date, min_price, max_price = result
        
        print(f"   📊 数据统计:")
        print(f"      总行数: {total}")
        print(f"      日期范围: {min_date} 至 {max_date}")
        print(f"      价格范围: {min_price:.2f} - {max_price:.2f} 港元")
        print(f"      Y值: {y_count}/{total} ({y_count/total*100:.1f}%)")
        print(f"      X值: {x_count}/{total} ({x_count/total*100:.1f}%)")
        print(f"      E值: {e_count}/{total} ({e_count/total*100:.1f}%)")
        print(f"      Full_Y: {full_y_count}/{total} ({full_y_count/total*100:.1f}%)")
        print(f"      MoneyFlowRatio: {mfr_count}/{total} ({mfr_count/total*100:.1f}%)")
        print(f"      MyE: {mye_count}/{total} ({mye_count/total*100:.1f}%)")
        
        # 检查最新几条记录
        cursor.execute("""
            SELECT Date, Close, Y_Value, X_Value, E_Value, Full_Y, MoneyFlowRatio, MyE
            FROM eab_0700hk
            ORDER BY Date DESC
            LIMIT 5
        """)
        
        latest_records = cursor.fetchall()
        print(f"\n   📋 最新5条记录:")
        for record in latest_records:
            date, close, y_val, x_val, e_val, full_y, mfr, mye = record
            print(f"      {date}: 价格{close:.2f} Y={y_val} X={x_val} E={e_val} Full_Y={full_y} MFR={mfr} MyE={mye}")
        
        cursor.close()
        connection.close()
        
        return total, y_count, x_count, e_count
        
    except Exception as e:
        print(f"   ❌ 检查失败: {e}")
        return 0, 0, 0, 0

def run_additional_procedures():
    """运行额外的存储过程来完善数据"""
    
    print("🔧 运行额外存储过程完善数据...")
    
    config = {
        'host': 'localhost',
        'port': 3306,
        'user': 'root',
        'password': '12345678',
        'database': 'finance'
    }
    
    try:
        connection = mysql.connector.connect(**config)
        cursor = connection.cursor()
        
        # 尝试运行其他可能的存储过程
        procedures_to_try = [
            ('sp_averagelineV3', ['eab_0700hk']),
            ('sp_updatecontroller', ['eab_0700hk']),
        ]
        
        for proc_name, params in procedures_to_try:
            try:
                print(f"   🔄 尝试执行 {proc_name}...")
                cursor.callproc(proc_name, params)
                
                for result in cursor.stored_results():
                    rows = result.fetchall()
                    if rows:
                        print(f"      📈 {proc_name} 处理了 {len(rows)} 行数据")
                
                connection.commit()
                print(f"   ✅ {proc_name} 执行完成")
                
            except Exception as e:
                print(f"   ⚠️ {proc_name} 执行失败: {e}")
                continue
        
        cursor.close()
        connection.close()
        
        return True
        
    except Exception as e:
        print(f"   ❌ 存储过程执行失败: {e}")
        return False

def manual_calculate_indicators():
    """手动计算缺失的技术指标"""
    
    print("🧮 手动计算缺失的技术指标...")
    
    config = {
        'host': 'localhost',
        'port': 3306,
        'user': 'root',
        'password': '12345678',
        'database': 'finance'
    }
    
    try:
        connection = mysql.connector.connect(**config)
        
        # 读取所有数据
        df = pd.read_sql("""
            SELECT Date, Open, High, Low, Close, Volume, Full_Y
            FROM eab_0700hk
            ORDER BY Date
        """, connection)
        
        print(f"   📊 读取 {len(df)} 条记录进行计算")
        
        # 计算Y值 (价格在20日区间的相对位置)
        df['Low_20'] = df['Low'].rolling(window=20, min_periods=1).min()
        df['High_20'] = df['High'].rolling(window=20, min_periods=1).max()
        df['Y_Value'] = (df['Close'] - df['Low_20']) / (df['High_20'] - df['Low_20'] + 0.0001)
        df['Y_Value'] = df['Y_Value'].fillna(0).clip(0, 1)
        
        # 计算典型价格和资金流
        df['TypicalPrice'] = (df['High'] + df['Low'] + df['Close']) / 3
        df['MoneyFlow'] = df['TypicalPrice'] * df['Volume']
        df['Price_Change'] = df['TypicalPrice'].diff()
        
        # 计算正负资金流
        df['PositiveMoneyFlow'] = df.apply(lambda x: x['MoneyFlow'] if x['Price_Change'] > 0 else 0, axis=1)
        df['NegativeMoneyFlow'] = df.apply(lambda x: x['MoneyFlow'] if x['Price_Change'] < 0 else 0, axis=1)
        
        # 14日资金流总和
        df['PositiveMoneyFlow_14'] = df['PositiveMoneyFlow'].rolling(window=14, min_periods=1).sum()
        df['NegativeMoneyFlow_14'] = df['NegativeMoneyFlow'].rolling(window=14, min_periods=1).sum()
        
        # MoneyFlowRatio和X值
        df['MoneyFlowRatio'] = df['PositiveMoneyFlow_14'] / (df['NegativeMoneyFlow_14'] + 0.0001)
        df['X_Value'] = df['MoneyFlowRatio'] / 100
        df['X_Value'] = df['X_Value'].clip(0, 1)
        
        # E值 (Cosmoon公式)
        df['E_Value'] = (8 * df['X_Value'] - 3) * df['Y_Value'] - 3 * df['X_Value'] + 1
        
        # MyE值
        df['MyE'] = 8 * df['MoneyFlowRatio'] * df['Full_Y'] - 3 * df['MoneyFlowRatio'] - 3 * df['Full_Y'] + 1
        
        print(f"   ✅ 技术指标计算完成")
        
        # 更新数据库
        cursor = connection.cursor()
        
        update_sql = """
        UPDATE eab_0700hk 
        SET Y_Value = %s, X_Value = %s, E_Value = %s, MoneyFlowRatio = %s, MyE = %s
        WHERE Date = %s
        """
        
        update_data = []
        for _, row in df.iterrows():
            update_data.append((
                float(row['Y_Value']),
                float(row['X_Value']),
                float(row['E_Value']),
                float(row['MoneyFlowRatio']),
                float(row['MyE']),
                row['Date']
            ))
        
        cursor.executemany(update_sql, update_data)
        connection.commit()
        
        print(f"   ✅ 已更新 {len(update_data)} 条记录")
        
        cursor.close()
        connection.close()
        
        return True
        
    except Exception as e:
        print(f"   ❌ 手动计算失败: {e}")
        return False

def export_final_excel():
    """导出最终的完整Excel"""
    
    print("📋 导出最终完整Excel...")
    
    config = {
        'host': 'localhost',
        'port': 3306,
        'user': 'root',
        'password': '12345678',
        'database': 'finance'
    }
    
    try:
        connection = mysql.connector.connect(**config)
        
        # 查询完整数据
        df = pd.read_sql("""
            SELECT Date, Open, High, Low, Close, Volume,
                   Y_Value, X_Value, E_Value, Full_Y, MoneyFlowRatio, MyE
            FROM eab_0700hk
            WHERE Y_Value IS NOT NULL AND X_Value IS NOT NULL
            ORDER BY Date
        """, connection)
        
        connection.close()
        
        print(f"   📊 查询到 {len(df)} 条完整记录")
        
        # 创建交易记录格式
        trading_records = []
        
        # 交易参数
        initial_capital = 10000.00
        monthly_addition = 3000.00
        commission_rate = 0.001
        
        current_capital = initial_capital
        position = 0
        entry_price = 0
        shares = 0
        
        for i, row in df.iterrows():
            current_date = row['Date']
            current_price = row['Close']
            y_value = row['Y_Value']
            x_value = row['X_Value']
            e_value = row['E_Value']
            full_y = row['Full_Y']
            mfr = row['MoneyFlowRatio']
            mye = row['MyE']
            
            # 每月追加资金
            if i > 0 and i % 22 == 0:
                current_capital += monthly_addition
            
            # 交易逻辑
            signal = "观望"
            trade_type = "持仓"
            trade_direction = "空仓"
            
            if position == 0:  # 空仓
                if e_value > 0 and x_value > 0.45 and y_value > 0.45:
                    signal = "强烈买入"
                    trade_type = "开仓"
                    trade_direction = "多头"
                    position = 1
                    entry_price = current_price
                    shares = int(current_capital * 0.8 / current_price) if current_price > 0 else 0
                    if shares > 0:
                        cost = shares * current_price * (1 + commission_rate)
                        current_capital -= cost
                elif (y_value < 0.3 or x_value < 0.3):
                    signal = "强烈卖出"
                    trade_type = "开仓"
                    trade_direction = "空头"
                    position = -1
                    entry_price = current_price
                    shares = int(current_capital * 0.8 / current_price) if current_price > 0 else 0
            else:  # 有持仓
                if position == 1:
                    trade_direction = "多头"
                    price_change = (current_price - entry_price) / entry_price if entry_price > 0 else 0
                    if price_change >= 0.012:
                        signal = "止盈平仓"
                        trade_type = "平仓"
                        if shares > 0:
                            current_capital += shares * current_price * (1 - commission_rate)
                        position = 0
                        shares = 0
                    elif price_change <= -0.006:
                        signal = "止损平仓"
                        trade_type = "平仓"
                        if shares > 0:
                            current_capital += shares * current_price * (1 - commission_rate)
                        position = 0
                        shares = 0
                    else:
                        signal = "持有多头"
                elif position == -1:
                    trade_direction = "空头"
                    price_change = (current_price - entry_price) / entry_price if entry_price > 0 else 0
                    if price_change <= -0.012:
                        signal = "止盈平仓"
                        trade_type = "平仓"
                        position = 0
                        shares = 0
                    elif price_change >= 0.006:
                        signal = "止损平仓"
                        trade_type = "平仓"
                        position = 0
                        shares = 0
                    else:
                        signal = "持有空头"
            
            # 计算当前状态
            if position != 0 and shares > 0:
                if position == 1:
                    unrealized_pnl = shares * (current_price - entry_price)
                    current_market_value = shares * current_price
                else:
                    unrealized_pnl = shares * (entry_price - current_price)
                    current_market_value = shares * current_price
                total_assets = current_capital + current_market_value + unrealized_pnl
            else:
                unrealized_pnl = 0
                current_market_value = 0
                total_assets = current_capital
            
            # 计算收益率
            months_passed = i // 22
            total_invested = initial_capital + months_passed * monthly_addition
            daily_return = (total_assets - total_invested) / total_invested * 100 if total_invested > 0 else 0
            
            # 止盈止损价格
            if position == 1:
                take_profit_price = entry_price * 1.012 if entry_price > 0 else current_price * 1.012
                stop_loss_price = entry_price * 0.994 if entry_price > 0 else current_price * 0.994
            elif position == -1:
                take_profit_price = entry_price * 0.988 if entry_price > 0 else current_price * 0.988
                stop_loss_price = entry_price * 1.006 if entry_price > 0 else current_price * 1.006
            else:
                take_profit_price = current_price * 1.012
                stop_loss_price = current_price * 0.994
            
            # 风险等级
            if abs(e_value) > 0.5:
                risk_level = "高风险"
            elif abs(e_value) > 0.2:
                risk_level = "中风险"
            else:
                risk_level = "低风险"
            
            # 创建完整记录
            record = {
                '交易日期': current_date.strftime('%Y-%m-%d') if hasattr(current_date, 'strftime') else str(current_date),
                '交易类型': trade_type,
                '交易方向': trade_direction,
                '交易价格': round(current_price, 2),
                '入场价格': round(entry_price, 2) if entry_price > 0 else round(current_price, 2),
                '止盈价': round(take_profit_price, 2),
                '止损价': round(stop_loss_price, 2),
                '持仓数量': shares,
                '交易金额': round(shares * current_price, 2) if shares > 0 else 0,
                '手续费': round(shares * current_price * commission_rate, 2) if shares > 0 else 0,
                '净交易额': round(shares * current_price * (1 - commission_rate), 2) if shares > 0 else 0,
                '持仓成本': round(entry_price, 2) if entry_price > 0 else 0,
                '当前市值': round(current_market_value, 2),
                '浮动盈亏': round(unrealized_pnl, 2),
                '实现盈亏': 0.00,
                '累计盈亏': round(unrealized_pnl, 2),
                '账户余额': round(current_capital, 2),
                '总资产': round(total_assets, 2),
                '收益率': round(daily_return, 2),
                '累计收益率': round(daily_return, 2),
                'Y值': round(y_value, 4),
                'Full_Y': round(full_y, 4),
                'X值': round(x_value, 4),
                'MoneyFlowRatio': round(mfr, 4),
                'E值': round(e_value, 4),
                'MyE': round(mye, 4),
                '信号强度': signal,
                '风险等级': risk_level,
                '备注': f'数据库完整计算 价格{current_price:.2f}港元 {signal} 资产{total_assets:,.0f}港元'
            }
            
            trading_records.append(record)
        
        # 创建最终DataFrame
        final_df = pd.DataFrame(trading_records)
        
        # 保存Excel文件
        filename = f'交易记录追踪0700HK_最终完整版_{datetime.now().strftime("%Y%m%d_%H%M%S")}.xlsx'
        final_df.to_excel(filename, index=False)
        
        print(f"   ✅ 最终Excel已导出: {filename}")
        print(f"   📊 包含 {len(final_df)} 条记录，29个完整字段")
        print(f"   🧮 所有技术指标均已正确计算")
        
        return filename
        
    except Exception as e:
        print(f"   ❌ 导出失败: {e}")
        return None

def main():
    """主函数"""
    
    print("🔧 腾讯700HK数据验证和修复系统")
    print("=" * 60)
    
    # 步骤1: 检查当前数据状态
    total, y_count, x_count, e_count = check_database_data()
    
    if y_count == 0 or x_count == 0 or e_count == 0:
        print("\n⚠️ 发现技术指标缺失，开始修复...")
        
        # 步骤2: 运行额外存储过程
        run_additional_procedures()
        
        # 步骤3: 手动计算缺失指标
        if not manual_calculate_indicators():
            print("❌ 手动计算失败")
            return
        
        # 步骤4: 重新检查
        print("\n🔍 重新检查数据...")
        check_database_data()
    
    # 步骤5: 导出最终Excel
    filename = export_final_excel()
    
    if filename:
        print(f"\n🎉 验证和修复完成!")
        print(f"✅ 数据库: eab_0700hk (技术指标已完善)")
        print(f"✅ Excel文件: {filename}")
        print(f"📊 现在您有了完整的29字段腾讯700HK交易记录!")
    else:
        print(f"\n❌ 最终导出失败")

if __name__ == "__main__":
    main()
