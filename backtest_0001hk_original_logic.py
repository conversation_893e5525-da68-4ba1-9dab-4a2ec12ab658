#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
长江和记实业0001.HK - 严格按照700HK.py原始逻辑
===============================================

完全按照700HK.py的原始交易逻辑，只是换标的测试

作者: Cosmoon NG
"""

import yfinance as yf
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from scipy import stats
import warnings
warnings.filterwarnings('ignore')

class Original700HKLogic0001HK:
    def __init__(self):
        """初始化 - 严格按照700HK.py参数"""
        self.symbol = "0001.HK"  # 长江和记实业
        self.initial_capital = 10000.00
        self.monthly_addition = 3000.00

        # 700HK.py原始止盈止损参数
        self.take_profit_long = 0.012   # 1.2%
        self.stop_loss_long = 0.006     # 0.6%
        self.take_profit_short = 0.012  # 1.2%
        self.stop_loss_short = 0.006    # 0.6%

        # 交易状态
        self.position = 0  # 0=空仓, 1=多头, -1=空头
        self.current_price = 0
        self.trades = []
        self.equity_curve = []

    def load_data(self):
        """加载数据"""
        print(f"📊 加载{self.symbol}历史数据...")

        try:
            end_date = datetime.now()
            start_date = end_date - timedelta(days=20*365)

            ticker = yf.Ticker(self.symbol)
            hist_data = ticker.history(start=start_date, end=end_date)

            self.df = pd.DataFrame({
                'date': hist_data.index,
                'open': hist_data['Open'],
                'high': hist_data['High'],
                'low': hist_data['Low'],
                'close': hist_data['Close'],
                'volume': hist_data['Volume']
            })

            self.df = self.df.dropna().sort_values('date').reset_index(drop=True)

            print(f"   ✅ 成功获取 {len(self.df)} 条数据")
            print(f"   📈 价格范围: {self.df['close'].min():.2f} - {self.df['close'].max():.2f} 港元")

            return True

        except Exception as e:
            print(f"   ❌ 数据加载失败: {e}")
            return False

    def calculate_indicators(self):
        """计算指标 - 严格按照700HK.py逻辑"""
        print(f"\n🧮 计算技术指标 (700HK.py原始逻辑)...")

        # Y值计算 (价格在20日区间的位置)
        window = 20
        self.df['high_20'] = self.df['high'].rolling(window).max()
        self.df['low_20'] = self.df['low'].rolling(window).min()
        self.df['y_value'] = (self.df['close'] - self.df['low_20']) / (self.df['high_20'] - self.df['low_20'])
        self.df['y_value'] = self.df['y_value'].fillna(0.5).clip(0, 1)

        # X值计算 (资金流强度)
        self.df['typical_price'] = (self.df['high'] + self.df['low'] + self.df['close']) / 3
        self.df['money_flow'] = self.df['typical_price'] * self.df['volume']
        self.df['price_change'] = self.df['typical_price'].diff()

        self.df['positive_mf'] = np.where(self.df['price_change'] > 0, self.df['money_flow'], 0)
        self.df['negative_mf'] = np.where(self.df['price_change'] < 0, self.df['money_flow'], 0)

        period = 14
        self.df['positive_mf_14'] = self.df['positive_mf'].rolling(period).sum()
        self.df['negative_mf_14'] = self.df['negative_mf'].rolling(period).sum()
        self.df['money_flow_ratio'] = self.df['positive_mf_14'] / (self.df['negative_mf_14'] + 1e-10)
        self.df['mfi'] = 100 - (100 / (1 + self.df['money_flow_ratio']))
        self.df['x_value'] = self.df['mfi'] / 100

        # E值计算 (Cosmoon公式)
        self.df['e_value'] = (8 * self.df['x_value'] - 3) * self.df['y_value'] - 3 * self.df['x_value'] + 1

        # 回归线计算 (700HK.py原始逻辑)
        self.df['i'] = range(1, len(self.df) + 1)
        slope, intercept, r_value, _, _ = stats.linregress(self.df['i'], self.df['close'])
        self.df['regression_line'] = intercept + slope * self.df['i']
        self.df['price_position'] = (self.df['close'] - self.df['regression_line']) / self.df['regression_line']

        print(f"   ✅ 指标计算完成")
        print(f"   📊 Y值范围: {self.df['y_value'].min():.3f} - {self.df['y_value'].max():.3f}")
        print(f"   📊 X值范围: {self.df['x_value'].min():.3f} - {self.df['x_value'].max():.3f}")
        print(f"   📊 E值范围: {self.df['e_value'].min():.3f} - {self.df['e_value'].max():.3f}")
        print(f"   📈 回归线R²: {r_value**2:.4f}")

    def add_monthly_capital(self, date, total_capital, monthly_invested):
        """每月增加资金 - 复利版本"""
        last_month = getattr(self, 'last_month', None)
        current_month = date.replace(day=1)

        if last_month is None or current_month > last_month:
            self.last_month = current_month
            # 复利关键: 追加资金加入总资本
            total_capital += self.monthly_addition
            monthly_invested += self.monthly_addition
            return total_capital, monthly_invested

        return total_capital, monthly_invested

    def run_backtest(self):
        """运行回测 - 700HK.py原始逻辑 + 复利计算"""
        print(f"\n💼 开始回测 (700HK.py原始逻辑 + 复利)...")

        # 复利计算的关键变量
        total_capital = self.initial_capital  # 总资本 (包括追加资金和累计盈利)
        monthly_invested = self.initial_capital  # 累计投入 (用于计算收益率)

        for i in range(60, len(self.df)):  # 从第60天开始，确保有足够的历史数据
            row = self.df.iloc[i]
            date = row['date']

            # 每月增加资金 (复利版本)
            total_capital, monthly_invested = self.add_monthly_capital(date, total_capital, monthly_invested)

            # 记录权益 (复利版本)
            self.equity_curve.append({
                'date': date,
                'equity': total_capital,
                'monthly_invested': monthly_invested,
                'position': self.position
            })

            # 如果有持仓，检查止盈止损 - 700HK.py原始逻辑
            if self.position != 0:
                if self.position == 1:  # 多头
                    profit_ratio = (row['high'] - self.current_price) / self.current_price
                    loss_ratio = (self.current_price - row['low']) / self.current_price

                    if profit_ratio >= self.take_profit_long:  # 止盈
                        exit_price = self.current_price * (1 + self.take_profit_long)
                        profit = (exit_price - self.current_price) / self.current_price * total_capital
                        # 复利关键: 盈利加入总资本
                        total_capital += profit
                        self.position = 0
                        self.trades.append({
                            'date': date,
                            'type': 'long_exit_tp',
                            'price': exit_price,
                            'profit': profit,
                            'total_capital': total_capital
                        })
                    elif loss_ratio >= self.stop_loss_long:  # 止损
                        exit_price = self.current_price * (1 - self.stop_loss_long)
                        loss = (exit_price - self.current_price) / self.current_price * total_capital
                        # 复利关键: 亏损也影响总资本
                        total_capital += loss
                        self.position = 0
                        self.trades.append({
                            'date': date,
                            'type': 'long_exit_sl',
                            'price': exit_price,
                            'profit': loss,
                            'total_capital': total_capital
                        })

                elif self.position == -1:  # 空头
                    profit_ratio = (self.current_price - row['low']) / self.current_price
                    loss_ratio = (row['high'] - self.current_price) / self.current_price

                    if profit_ratio >= self.take_profit_short:  # 止盈
                        exit_price = self.current_price * (1 - self.take_profit_short)
                        profit = (self.current_price - exit_price) / self.current_price * total_capital
                        # 复利关键: 盈利加入总资本
                        total_capital += profit
                        self.position = 0
                        self.trades.append({
                            'date': date,
                            'type': 'short_exit_tp',
                            'price': exit_price,
                            'profit': profit,
                            'total_capital': total_capital
                        })
                    elif loss_ratio >= self.stop_loss_short:  # 止损
                        exit_price = self.current_price * (1 + self.stop_loss_short)
                        loss = (exit_price - self.current_price) / self.current_price * total_capital * -1
                        # 复利关键: 亏损也影响总资本
                        total_capital += loss
                        self.position = 0
                        self.trades.append({
                            'date': date,
                            'type': 'short_exit_sl',
                            'price': exit_price,
                            'profit': loss,
                            'total_capital': total_capital
                        })

            # 如果空仓，判断是否开仓 - 700HK.py原始逻辑
            if self.position == 0:
                # 多头信号: E>0 且 X>0.45 且 Y>0.45 且 价格<回归线
                if row['e_value'] > 0 and row['x_value'] > 0.45 and row['y_value'] > 0.45:
                    if row['price_position'] < 0:  # 价格低于回归线
                        self.position = 1
                        self.current_price = row['close']
                        self.trades.append({
                            'date': date,
                            'type': 'long_entry',
                            'price': self.current_price,
                            'total_capital': total_capital
                        })

                # 空头信号: 复杂的XY组合 且 价格>回归线
                elif (row['y_value'] < 0.3 or row['x_value'] < 0.3 or
                      (row['x_value'] > 0.45 and row['y_value'] < 0.35) or
                      (row['x_value'] < 0.45 and row['y_value'] > 0.35)):
                    if row['price_position'] > 0:  # 价格高于回归线
                        self.position = -1
                        self.current_price = row['close']
                        self.trades.append({
                            'date': date,
                            'type': 'short_entry',
                            'price': self.current_price,
                            'total_capital': total_capital
                        })

        self.final_capital = total_capital
        self.total_invested = monthly_invested
        print(f"   ✅ 回测完成！最终资金：{self.final_capital:,.2f}")

    def analyze_results(self):
        """分析回测结果"""
        print(f"\n📈 700HK.py原始逻辑回测结果")
        print("=" * 60)

        # 基础统计
        total_days = (self.df['date'].max() - self.df['date'].min()).days
        total_years = total_days / 365

        print(f"📊 基础信息:")
        print(f"   标的: {self.symbol} (长江和记实业)")
        print(f"   回测期间: {total_years:.1f}年")
        print(f"   策略: 700HK.py原始逻辑")

        # 价格表现
        start_price = self.df['close'].iloc[0]
        end_price = self.df['close'].iloc[-1]
        price_return = (end_price - start_price) / start_price * 100

        print(f"\n💰 价格表现:")
        print(f"   起始价格: {start_price:.2f}港元")
        print(f"   最终价格: {end_price:.2f}港元")
        print(f"   价格涨幅: {price_return:.2f}%")

        # 策略表现 (复利版本)
        net_profit = self.final_capital - self.total_invested
        total_return = net_profit / self.total_invested * 100
        annual_return = ((self.final_capital / self.total_invested) ** (1/total_years) - 1) * 100

        print(f"\n🚀 策略表现:")
        print(f"   总投入: {self.total_invested:,.0f}港元")
        print(f"   最终资金: {self.final_capital:,.0f}港元")
        print(f"   净收益: {net_profit:,.0f}港元")
        print(f"   总收益率: {total_return:.2f}%")
        print(f"   年化收益率: {annual_return:.2f}%")

        # 交易统计
        trades_df = pd.DataFrame(self.trades)
        if len(trades_df) > 0:
            entry_trades = trades_df[trades_df['type'].str.contains('entry')]
            exit_trades = trades_df[trades_df['type'].str.contains('exit')]
            profit_trades = exit_trades[exit_trades['profit'] > 0]

            print(f"\n📋 交易统计:")
            print(f"   总交易次数: {len(entry_trades)}")
            print(f"   盈利交易: {len(profit_trades)}")
            print(f"   亏损交易: {len(exit_trades) - len(profit_trades)}")
            print(f"   胜率: {len(profit_trades)/len(exit_trades)*100:.2f}%" if len(exit_trades) > 0 else "   胜率: 0%")

        # 买入持有对比
        buy_hold_shares = int(self.initial_capital / start_price)
        buy_hold_final = buy_hold_shares * end_price
        buy_hold_return = (buy_hold_final - self.initial_capital) / self.initial_capital * 100

        print(f"\n🆚 买入持有对比:")
        print(f"   买入持有收益: {buy_hold_return:.2f}%")
        print(f"   策略收益: {total_return:.2f}%")
        print(f"   策略优势: {total_return - buy_hold_return:.2f}%")

def main():
    """主函数"""
    print("🎯 长江和记实业0001.HK - 700HK.py原始逻辑+复利测试")
    print("=" * 60)
    print("📋 700HK.py原始交易逻辑 + 复利计算:")
    print("   💰 复利计算: 盈利加入总资本，实现复利增长")
    print("   🎯 多头条件: E>0 且 X>0.45 且 Y>0.45 且 价格<回归线")
    print("   📉 空头条件: (Y<0.3 或 X<0.3 或 特定XY组合) 且 价格>回归线")
    print("   ⚖️  止盈止损: 多头+1.2%/-0.6%, 空头+1.2%/-0.6%")
    print("   💵 资金管理: 基于总资本比例计算，每月追加3000港元")

    backtest = Original700HKLogic0001HK()

    if not backtest.load_data():
        return

    backtest.calculate_indicators()
    backtest.run_backtest()
    backtest.analyze_results()

    print(f"\n🎉 700HK.py原始逻辑+复利测试完成!")
    print(f"📈 这是700HK.py+复利在0001HK上的真实表现")
    print(f"💰 复利效应: 盈利再投资，实现财富复合增长")

if __name__ == "__main__":
    main()
