#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
优化版Y>0.4且X>0.4策略回测
5万港币，10年历史，简化计算
"""

import pymysql
import pandas as pd
import numpy as np
from datetime import datetime, timed<PERSON>ta

def optimized_y_x_04_backtest():
    """优化版Y>0.4且X>0.4策略回测"""
    
    db_config = {
        'host': 'localhost',
        'port': 3306,
        'database': 'finance',
        'user': 'root',
        'password': '12345678',
        'charset': 'utf8mb4'
    }
    
    try:
        connection = pymysql.connect(**db_config)
        cursor = connection.cursor()
        
        print("💰 Y>0.4 且 X>0.4 策略回测 (优化版)")
        print("="*60)
        print("📊 数据源: HK2800 (盈富基金)")
        print("💰 初始资金: 50,000 港币")
        print("📅 回测期间: 最近10年")
        
        # 1. 获取最近10年数据
        end_date = datetime.now()
        start_date = end_date - timedelta(days=365*10)
        
        cursor.execute("""
            SELECT date, close, adj_close, y_probability, inflow_ratio
            FROM hk2800 
            WHERE date >= %s 
            AND y_probability IS NOT NULL 
            AND inflow_ratio IS NOT NULL
            ORDER BY date ASC
        """, (start_date.date(),))
        
        data = cursor.fetchall()
        df = pd.DataFrame(data, columns=['date', 'close', 'adj_close', 'y_probability', 'inflow_ratio'])
        
        for col in ['close', 'adj_close', 'y_probability', 'inflow_ratio']:
            df[col] = pd.to_numeric(df[col])
        df['date'] = pd.to_datetime(df['date'])
        
        print(f"📊 数据范围: {len(df)} 条记录")
        print(f"📅 时间跨度: {df['date'].min().strftime('%Y-%m-%d')} 到 {df['date'].max().strftime('%Y-%m-%d')}")
        
        # 2. 策略条件分析
        df['signal'] = (df['y_probability'] > 0.4) & (df['inflow_ratio'] > 0.4)
        
        total_days = len(df)
        signal_days = df['signal'].sum()
        years = (df['date'].max() - df['date'].min()).days / 365.25
        
        print(f"\n📊 策略统计:")
        print("="*40)
        print(f"   总交易日: {total_days}")
        print(f"   信号天数: {signal_days}")
        print(f"   信号频率: {signal_days/total_days*100:.2f}%")
        print(f"   年均信号: {signal_days/years:.1f}次")
        
        # 3. 由于信号频率过高，采用采样策略
        print(f"\n⚠️ 信号频率过高 ({signal_days/total_days*100:.1f}%)，采用优化策略:")
        print("="*60)
        
        # 策略1: 每周最多交易1次
        df['week'] = df['date'].dt.isocalendar().week
        df['year'] = df['date'].dt.year
        df['year_week'] = df['year'].astype(str) + '_' + df['week'].astype(str)
        
        # 每周选择第一个信号
        weekly_signals = df[df['signal']].groupby('year_week').first().reset_index()
        
        print(f"策略1 - 每周交易:")
        print(f"   每周最多1次交易")
        print(f"   总信号数: {len(weekly_signals)}")
        print(f"   年均交易: {len(weekly_signals)/years:.1f}次")
        
        # 策略2: 每月最多交易2次
        df['year_month'] = df['date'].dt.to_period('M')
        monthly_signals = df[df['signal']].groupby('year_month').head(2).reset_index(drop=True)
        
        print(f"\n策略2 - 每月交易:")
        print(f"   每月最多2次交易")
        print(f"   总信号数: {len(monthly_signals)}")
        print(f"   年均交易: {len(monthly_signals)/years:.1f}次")
        
        # 4. 简化回测计算
        def simple_backtest(signal_data, strategy_name, position_size=0.15, holding_days=60):
            """简化回测计算"""
            
            initial_capital = 50000
            capital = initial_capital
            trades = []
            
            print(f"\n💰 {strategy_name} 回测:")
            print("="*50)
            
            for _, row in signal_data.iterrows():
                entry_date = row['date']
                entry_price = row['adj_close']
                
                # 计算投资金额
                investment = capital * position_size
                shares = investment / entry_price
                
                # 找到退出价格
                exit_date = entry_date + timedelta(days=holding_days)
                future_data = df[df['date'] >= exit_date]
                
                if not future_data.empty:
                    exit_price = future_data.iloc[0]['adj_close']
                    
                    # 计算收益
                    pnl = shares * (exit_price - entry_price)
                    return_rate = pnl / investment
                    
                    capital += pnl
                    
                    trades.append({
                        'entry_date': entry_date,
                        'entry_price': entry_price,
                        'exit_price': exit_price,
                        'return_rate': return_rate,
                        'pnl': pnl,
                        'capital': capital
                    })
            
            if trades:
                trades_df = pd.DataFrame(trades)
                
                # 统计结果
                total_return = (capital - initial_capital) / initial_capital
                annual_return = ((capital / initial_capital) ** (1/years)) - 1
                win_rate = (trades_df['return_rate'] > 0).mean()
                avg_return = trades_df['return_rate'].mean()
                max_gain = trades_df['return_rate'].max()
                max_loss = trades_df['return_rate'].min()
                
                print(f"   交易次数: {len(trades)}")
                print(f"   最终金额: {capital:,.0f} 港币")
                print(f"   总收益: {capital - initial_capital:+,.0f} 港币")
                print(f"   总收益率: {total_return*100:+.2f}%")
                print(f"   年化收益: {annual_return*100:+.2f}%")
                print(f"   胜率: {win_rate*100:.1f}%")
                print(f"   平均收益: {avg_return*100:+.2f}%")
                print(f"   最大盈利: {max_gain*100:+.2f}%")
                print(f"   最大亏损: {max_loss*100:+.2f}%")
                
                return {
                    'final_capital': capital,
                    'total_return': total_return,
                    'annual_return': annual_return,
                    'win_rate': win_rate,
                    'trades_count': len(trades),
                    'avg_return': avg_return
                }
            
            return None
        
        # 5. 执行回测
        weekly_result = simple_backtest(weekly_signals, "每周交易策略")
        monthly_result = simple_backtest(monthly_signals, "每月交易策略")
        
        # 6. 不同仓位对比 (使用每月策略)
        print(f"\n📊 不同仓位对比 (每月策略, 60天持有):")
        print("="*60)
        print("仓位     最终金额   总收益    年化收益   胜率")
        print("-" * 60)
        
        positions = [0.10, 0.15, 0.20, 0.25]
        best_result = None
        best_annual = 0
        
        for pos in positions:
            result = simple_backtest(monthly_signals, f"{pos*100:.0f}%仓位", pos, 60)
            if result:
                print(f"{pos*100:>3.0f}%    {result['final_capital']:>9,.0f}  {result['final_capital']-50000:>+8,.0f}  {result['annual_return']*100:>+8.2f}%  {result['win_rate']*100:>5.1f}%")
                
                if result['annual_return'] > best_annual:
                    best_annual = result['annual_return']
                    best_result = result
                    best_result['position'] = pos
        
        # 7. 不同持有期对比 (使用最佳仓位)
        if best_result:
            print(f"\n📊 不同持有期对比 ({best_result['position']*100:.0f}%仓位):")
            print("="*60)
            print("持有期   最终金额   总收益    年化收益   胜率")
            print("-" * 60)
            
            holding_periods = [30, 60, 90, 120]
            final_best = None
            final_best_annual = 0
            
            for days in holding_periods:
                result = simple_backtest(monthly_signals, f"{days}天持有", best_result['position'], days)
                if result:
                    print(f"{days:>3}天   {result['final_capital']:>9,.0f}  {result['final_capital']-50000:>+8,.0f}  {result['annual_return']*100:>+8.2f}%  {result['win_rate']*100:>5.1f}%")
                    
                    if result['annual_return'] > final_best_annual:
                        final_best_annual = result['annual_return']
                        final_best = result
                        final_best['holding_days'] = days
        
        # 8. 买入持有基准
        start_price = df.iloc[0]['adj_close']
        end_price = df.iloc[-1]['adj_close']
        buy_hold_return = (end_price - start_price) / start_price
        buy_hold_annual = ((1 + buy_hold_return) ** (1/years)) - 1
        buy_hold_final = 50000 * (1 + buy_hold_return)
        
        print(f"\n📊 策略对比:")
        print("="*50)
        print(f"   买入持有:")
        print(f"     最终金额: {buy_hold_final:,.0f} 港币")
        print(f"     年化收益: {buy_hold_annual*100:+.2f}%")
        
        if final_best:
            print(f"   最佳策略:")
            print(f"     最终金额: {final_best['final_capital']:,.0f} 港币")
            print(f"     年化收益: {final_best['annual_return']*100:+.2f}%")
            print(f"     超额收益: {(final_best['annual_return'] - buy_hold_annual)*100:+.2f}%")
        
        # 9. 年度收益预测
        if final_best:
            print(f"\n📅 5年收益预测 (最佳策略):")
            print("="*40)
            
            annual_rate = final_best['annual_return']
            for year in range(1, 6):
                projected = 50000 * ((1 + annual_rate) ** year)
                profit = projected - 50000
                print(f"   第{year}年末: {projected:,.0f} 港币 (收益{profit:+,.0f})")
        
        # 10. 实战建议
        print(f"\n💡 实战建议:")
        print("="*30)
        
        if final_best:
            monthly_profit = (final_best['final_capital'] - 50000) / (years * 12)
            single_investment = 50000 * best_result['position']
            
            print(f"✅ 推荐策略: 每月交易")
            print(f"✅ 推荐仓位: {best_result['position']*100:.0f}%")
            print(f"✅ 推荐持有期: {final_best['holding_days']}天")
            print(f"✅ 单次投资: {single_investment:,.0f} 港币")
            print(f"✅ 月均收益: {monthly_profit:+,.0f} 港币")
            print(f"✅ 年化收益: {final_best['annual_return']*100:+.2f}%")
            print(f"✅ 胜率: {final_best['win_rate']*100:.1f}%")
        
        print(f"\n🎯 策略特点:")
        print("="*20)
        print("✅ Y>0.4且X>0.4条件简单明确")
        print("✅ 信号频率高，机会多")
        print("✅ 需要控制交易频率避免过度交易")
        print("✅ 适合中长期持有策略")
        
        return True
        
    except Exception as e:
        print(f"❌ 回测失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        if 'connection' in locals():
            connection.close()

if __name__ == "__main__":
    optimized_y_x_04_backtest()
