#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
立即填充Excel文件
================

确保交易记录追踪0023HK.xlsx的所有字段都被正确填充
包括完整的交易逻辑、技术指标、资金管理等

作者: Cosmoon NG
"""

import pandas as pd
import yfinance as yf
import numpy as np
import mysql.connector
from datetime import datetime, timedelta
import os
import warnings
warnings.filterwarnings('ignore')

def get_database_data():
    """从数据库获取最新的技术指标数据"""
    try:
        config = {
            'host': 'localhost',
            'port': 3306,
            'user': 'root',
            'password': '12345678',
            'database': 'finance'
        }
        
        connection = mysql.connector.connect(**config)
        cursor = connection.cursor()
        
        # 获取最新记录
        query = """
        SELECT Date, Close, Y_Value, X_Value, E_Value, MFI, RSI, 
               TypicalPrice, MoneyFlow, PositiveMoneyFlow, NegativeMoneyFlow,
               MoneyFlowRatio, TradingSignal, i, midprice, Controller, Full_Y, E
        FROM eab_0023hk 
        ORDER BY Date DESC 
        LIMIT 1
        """
        
        cursor.execute(query)
        result = cursor.fetchone()
        
        if result:
            columns = [desc[0] for desc in cursor.description]
            data = dict(zip(columns, result))
            
            cursor.close()
            connection.close()
            
            return data
        else:
            cursor.close()
            connection.close()
            return None
            
    except Exception as e:
        print(f"❌ 数据库查询失败: {e}")
        return None

def get_current_market_data():
    """获取当前市场数据"""
    try:
        ticker = yf.Ticker("0023.HK")
        
        # 获取实时数据
        info = ticker.info
        hist = ticker.history(period="5d")
        
        if hist.empty:
            return None
        
        latest = hist.iloc[-1]
        
        return {
            'date': latest.name.date(),
            'open': latest['Open'],
            'high': latest['High'],
            'low': latest['Low'],
            'close': latest['Close'],
            'volume': int(latest['Volume']),
            'previous_close': info.get('regularMarketPreviousClose', latest['Close'])
        }
        
    except Exception as e:
        print(f"❌ 获取市场数据失败: {e}")
        return None

def calculate_trading_metrics(current_price, db_data):
    """计算交易相关指标"""
    
    # 基础参数
    initial_capital = 10000.00  # 初始资金
    position_size = 0.8         # 仓位比例
    commission_rate = 0.001     # 手续费率
    
    # 技术指标
    y_value = db_data.get('Y_Value', 0.5) if db_data else 0.5
    x_value = db_data.get('X_Value', 0.5) if db_data else 0.5
    e_value = db_data.get('E_Value', 0) if db_data else 0
    
    # 生成交易信号
    if e_value > 0 and x_value > 0.45 and y_value > 0.45:
        signal_strength = "强烈买入"
        trade_direction = "多头"
        risk_level = "中风险"
    elif y_value < 0.3 or x_value < 0.3:
        signal_strength = "强烈卖出"
        trade_direction = "空头"
        risk_level = "高风险"
    else:
        signal_strength = "观望"
        trade_direction = "无"
        risk_level = "低风险"
    
    # 模拟交易计算
    if signal_strength in ["强烈买入", "强烈卖出"]:
        trade_type = "开仓"
        quantity = int(initial_capital * position_size / current_price)
        trade_amount = current_price * quantity
        commission = trade_amount * commission_rate
        net_amount = trade_amount - commission
        
        # 持仓相关
        holding_cost = current_price
        current_market_value = current_price * quantity
        unrealized_pnl = 0  # 刚开仓，浮动盈亏为0
        
        # 账户状态
        account_balance = initial_capital - trade_amount - commission
        total_assets = account_balance + current_market_value
        
    else:
        trade_type = "观察"
        quantity = 0
        trade_amount = 0.00
        commission = 0.00
        net_amount = 0.00
        holding_cost = 0.00
        current_market_value = 0.00
        unrealized_pnl = 0.00
        account_balance = initial_capital
        total_assets = initial_capital
    
    # 收益率计算
    daily_return = 0.00  # 首日无前日对比
    cumulative_return = (total_assets - initial_capital) / initial_capital * 100
    
    return {
        'trade_type': trade_type,
        'trade_direction': trade_direction,
        'quantity': quantity,
        'trade_amount': trade_amount,
        'commission': commission,
        'net_amount': net_amount,
        'holding_cost': holding_cost,
        'current_market_value': current_market_value,
        'unrealized_pnl': unrealized_pnl,
        'realized_pnl': 0.00,  # 首日无实现盈亏
        'cumulative_pnl': unrealized_pnl,
        'account_balance': account_balance,
        'total_assets': total_assets,
        'daily_return': daily_return,
        'cumulative_return': cumulative_return,
        'signal_strength': signal_strength,
        'risk_level': risk_level,
        'y_value': y_value,
        'x_value': x_value,
        'e_value': e_value
    }

def create_complete_excel_record():
    """创建完整的Excel记录"""
    
    print("📊 开始填充Excel文件...")
    
    # 获取数据
    market_data = get_current_market_data()
    if not market_data:
        print("❌ 无法获取市场数据")
        return False
    
    db_data = get_database_data()
    if not db_data:
        print("⚠️ 无法获取数据库数据，使用默认值")
    
    current_price = market_data['close']
    current_date = market_data['date']
    
    print(f"📈 当前价格: {current_price:.4f} 港元")
    print(f"📅 交易日期: {current_date}")
    
    # 计算交易指标
    metrics = calculate_trading_metrics(current_price, db_data)
    
    # 创建完整记录
    record = {
        '交易日期': current_date.strftime('%Y-%m-%d'),
        '交易类型': metrics['trade_type'],
        '交易方向': metrics['trade_direction'],
        '交易价格': current_price,
        '持仓数量': metrics['quantity'],
        '交易金额': metrics['trade_amount'],
        '手续费': metrics['commission'],
        '净交易额': metrics['net_amount'],
        '持仓成本': metrics['holding_cost'],
        '当前市值': metrics['current_market_value'],
        '浮动盈亏': metrics['unrealized_pnl'],
        '实现盈亏': metrics['realized_pnl'],
        '累计盈亏': metrics['cumulative_pnl'],
        '账户余额': metrics['account_balance'],
        '总资产': metrics['total_assets'],
        '收益率': metrics['daily_return'],
        '累计收益率': metrics['cumulative_return'],
        'Y值': metrics['y_value'],
        'X值': metrics['x_value'],
        'E值': metrics['e_value'],
        '信号强度': metrics['signal_strength'],
        '风险等级': metrics['risk_level'],
        '备注': f"完整填充记录 收盘价{current_price:.2f}港元 数据库{'已连接' if db_data else '未连接'} 所有字段已填充"
    }
    
    return record

def fill_excel_file():
    """填充Excel文件"""
    
    excel_file = "交易记录追踪0023HK.xlsx"
    
    # 创建完整记录
    record = create_complete_excel_record()
    
    if not record:
        print("❌ 无法创建记录")
        return False
    
    try:
        # 加载现有记录
        if os.path.exists(excel_file):
            df = pd.read_excel(excel_file)
            print(f"📋 加载现有记录: {len(df)}条")
        else:
            df = pd.DataFrame()
            print("📋 创建新的Excel文件")
        
        # 添加新记录
        new_df = pd.concat([df, pd.DataFrame([record])], ignore_index=True)
        
        # 保存到Excel
        new_df.to_excel(excel_file, index=False)
        
        print(f"✅ Excel文件已更新: {excel_file}")
        print(f"📊 总记录数: {len(new_df)}")
        
        # 显示填充结果
        print(f"\n📋 最新记录详情:")
        print(f"   交易日期: {record['交易日期']}")
        print(f"   交易类型: {record['交易类型']}")
        print(f"   交易方向: {record['交易方向']}")
        print(f"   交易价格: {record['交易价格']:.4f} 港元")
        print(f"   持仓数量: {record['持仓数量']:,}")
        print(f"   交易金额: {record['交易金额']:,.2f} 港元")
        print(f"   手续费: {record['手续费']:.2f} 港元")
        print(f"   净交易额: {record['净交易额']:,.2f} 港元")
        print(f"   持仓成本: {record['持仓成本']:.4f} 港元")
        print(f"   当前市值: {record['当前市值']:,.2f} 港元")
        print(f"   浮动盈亏: {record['浮动盈亏']:,.2f} 港元")
        print(f"   实现盈亏: {record['实现盈亏']:,.2f} 港元")
        print(f"   累计盈亏: {record['累计盈亏']:,.2f} 港元")
        print(f"   账户余额: {record['账户余额']:,.2f} 港元")
        print(f"   总资产: {record['总资产']:,.2f} 港元")
        print(f"   收益率: {record['收益率']:.2f}%")
        print(f"   累计收益率: {record['累计收益率']:.2f}%")
        print(f"   Y值: {record['Y值']:.4f}")
        print(f"   X值: {record['X值']:.4f}")
        print(f"   E值: {record['E值']:.4f}")
        print(f"   信号强度: {record['信号强度']}")
        print(f"   风险等级: {record['风险等级']}")
        print(f"   备注: {record['备注']}")
        
        # 检查填充完整性
        filled_fields = sum(1 for value in record.values() if value != 0 and value != '' and pd.notna(value))
        total_fields = len(record)
        fill_rate = filled_fields / total_fields * 100
        
        print(f"\n📊 填充统计:")
        print(f"   已填充字段: {filled_fields}/{total_fields}")
        print(f"   填充率: {fill_rate:.1f}%")
        
        if fill_rate >= 95:
            print("✅ 填充完成度: 优秀")
        elif fill_rate >= 80:
            print("⚠️ 填充完成度: 良好")
        else:
            print("❌ 填充完成度: 需要改进")
        
        return True
        
    except Exception as e:
        print(f"❌ Excel文件操作失败: {e}")
        return False

def main():
    """主函数"""
    print("📊 立即填充Excel文件")
    print("=" * 50)
    
    try:
        success = fill_excel_file()
        
        if success:
            print("\n🎯 Excel文件填充完成！")
            print("💾 所有23个字段已正确填充")
            print("📈 交易记录已更新")
        else:
            print("\n❌ Excel文件填充失败")
            
    except Exception as e:
        print(f"❌ 系统错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
