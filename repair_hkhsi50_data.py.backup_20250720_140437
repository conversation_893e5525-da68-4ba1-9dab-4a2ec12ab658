#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复hkhsi50数据缺损
==================

检测并修复hkhsi50表中的数据缺损问题：
1. 检查数据完整性
2. 识别缺失的日期
3. 使用yfinance补充缺失数据
4. 重新计算技术指标
5. 验证修复结果

作者: Cosmoon NG
日期: 2025年7月
"""

import yfinance as yf
import pandas as pd
import pymysql
from datetime import datetime, timedelta, date
import numpy as np
import warnings
warnings.filterwarnings('ignore')

# 数据库配置
db_config = {
    'host': '************',
    'user': 'root',
    'password': '',
    'database': 'finance',
    'charset': 'utf8mb4'
}

def check_data_integrity():
    """检查数据完整性"""
    print("🔍 检查hkhsi50数据完整性...")
    
    try:
        conn = pymysql.connect(**db_config)
        cursor = conn.cursor()
        
        # 检查表是否存在
        cursor.execute("SHOW TABLES LIKE 'hkhsi50'")
        if not cursor.fetchone():
            print("❌ hkhsi50表不存在！")
            return None, None, None
        
        # 获取数据统计
        cursor.execute("SELECT COUNT(*) FROM hkhsi50")
        total_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT MIN(date), MAX(date) FROM hkhsi50")
        date_range = cursor.fetchone()
        min_date, max_date = date_range
        
        # 检查缺失数据
        cursor.execute("""
            SELECT COUNT(*) FROM hkhsi50 
            WHERE close IS NULL OR volume IS NULL 
               OR x_value IS NULL OR y_value IS NULL 
               OR e_value IS NULL
        """)
        null_count = cursor.fetchone()[0]
        
        print(f"📊 数据统计:")
        print(f"   • 总记录数: {total_count}")
        print(f"   • 日期范围: {min_date} 至 {max_date}")
        print(f"   • 缺失数据: {null_count} 条")
        
        # 检查日期连续性
        if min_date and max_date:
            expected_days = (max_date - min_date).days + 1
            working_days = expected_days * 5 // 7  # 估算工作日
            missing_days = working_days - total_count
            
            print(f"   • 预期工作日: ~{working_days}")
            print(f"   • 可能缺失: ~{missing_days} 天")
        
        conn.close()
        return total_count, date_range, null_count
        
    except Exception as e:
        print(f"❌ 检查数据完整性失败: {e}")
        return None, None, None

def find_missing_dates():
    """查找缺失的日期"""
    print("\n🔍 查找缺失的日期...")
    
    try:
        conn = pymysql.connect(**db_config)
        
        # 获取现有日期
        existing_dates = pd.read_sql("""
            SELECT date FROM hkhsi50 
            ORDER BY date
        """, conn)
        
        conn.close()
        
        if existing_dates.empty:
            print("❌ 没有找到任何数据")
            return []
        
        existing_dates['date'] = pd.to_datetime(existing_dates['date'])
        min_date = existing_dates['date'].min()
        max_date = existing_dates['date'].max()
        
        # 生成完整的日期范围（工作日）
        full_range = pd.bdate_range(start=min_date, end=max_date)
        existing_set = set(existing_dates['date'].dt.date)
        full_set = set(full_range.date)
        
        missing_dates = sorted(full_set - existing_set)
        
        print(f"📅 发现 {len(missing_dates)} 个缺失的工作日")
        if missing_dates:
            print(f"   • 最早缺失: {missing_dates[0]}")
            print(f"   • 最晚缺失: {missing_dates[-1]}")
            
            # 显示前10个缺失日期
            if len(missing_dates) <= 10:
                print(f"   • 缺失日期: {missing_dates}")
            else:
                print(f"   • 前10个缺失: {missing_dates[:10]}")
        
        return missing_dates
        
    except Exception as e:
        print(f"❌ 查找缺失日期失败: {e}")
        return []

def calculate_indicators(df):
    """计算技术指标"""
    print("🧮 计算技术指标...")
    
    # 计算移动平均线
    df['ma20'] = df['Close'].rolling(window=20).mean()
    df['ma60'] = df['Close'].rolling(window=60).mean()
    
    # 计算RSI
    delta = df['Close'].diff()
    gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
    rs = gain / loss
    df['rsi'] = 100 - (100 / (1 + rs))
    
    # 计算资金流
    df['money_flow'] = df['Volume'] * (df['Close'] - df['Open']) / df['Open']
    
    # 计算基础X值（20日资金流向）
    pos_flow = df['money_flow'].where(df['money_flow'] > 0, 0).rolling(window=20).sum()
    neg_flow = (-df['money_flow'].where(df['money_flow'] < 0, 0)).rolling(window=20).sum()
    df['base_x'] = pos_flow / (pos_flow + neg_flow)
    
    # 计算最终X值
    rsi_adj = 0.3 * (df['rsi']/100 - 0.5)
    df['x_value'] = df['base_x'] + rsi_adj
    df['x_value'] = df['x_value'].clip(0.1, 0.9)
    
    # 计算基础Y值和最终Y值
    price_ma20_ratio = df['Close'] / df['ma20']
    df['base_y'] = pd.Series(index=df.index)
    mask = price_ma20_ratio >= 1
    df.loc[mask, 'base_y'] = 0.5 + 0.4 * np.tanh((price_ma20_ratio[mask] - 1) * 3)
    df.loc[~mask, 'base_y'] = 0.5 - 0.4 * np.tanh((1 - price_ma20_ratio[~mask]) * 3)
    
    # 趋势调整
    trend_adj = 0.1 * np.tanh((df['ma20'] / df['ma60'] - 1) * 2)
    
    # 成交量调整
    volume_ma20 = df['Volume'].rolling(window=20).mean()
    vol_adj = 0.05 * np.tanh((df['Volume'] / volume_ma20 - 1))
    
    df['y_value'] = (df['base_y'] + trend_adj + vol_adj).clip(0.1, 0.9)
    
    # 计算E值
    df['e_value'] = 8 * df['x_value'] * df['y_value'] - 3 * df['x_value'] - 3 * df['y_value'] + 1
    
    # 替换 NaN 为 None（MySQL 不接受 NaN）
    df = df.replace({np.nan: None})
    
    return df

def fetch_missing_data(missing_dates):
    """获取缺失日期的数据"""
    if not missing_dates:
        print("✅ 没有缺失数据需要补充")
        return None
    
    print(f"\n📥 从Yahoo Finance获取缺失数据...")
    
    try:
        # 扩展日期范围以确保获取足够数据
        start_date = min(missing_dates) - timedelta(days=100)  # 提前100天以计算指标
        end_date = max(missing_dates) + timedelta(days=1)
        
        print(f"   • 获取日期范围: {start_date} 至 {end_date}")
        
        # 获取恒生指数数据
        ticker = yf.Ticker("^HSI")
        df = ticker.history(start=start_date, end=end_date)
        
        if df.empty:
            print("❌ 未能从Yahoo Finance获取数据")
            return None
        
        print(f"   • 获取到 {len(df)} 条原始数据")
        
        # 计算技术指标
        df = calculate_indicators(df)
        
        # 只保留缺失日期的数据
        df.reset_index(inplace=True)
        df['Date'] = pd.to_datetime(df['Date']).dt.date
        missing_data = df[df['Date'].isin(missing_dates)]
        
        print(f"   • 找到 {len(missing_data)} 条缺失日期的数据")
        
        return missing_data
        
    except Exception as e:
        print(f"❌ 获取缺失数据失败: {e}")
        return None

def insert_missing_data(missing_data):
    """插入缺失数据到数据库"""
    if missing_data is None or missing_data.empty:
        print("❌ 没有数据需要插入")
        return False
    
    print(f"\n💾 插入 {len(missing_data)} 条缺失数据...")
    
    try:
        conn = pymysql.connect(**db_config)
        cursor = conn.cursor()
        
        # 准备插入SQL
        sql = """
        INSERT INTO hkhsi50 (
            date, open, high, low, close, volume, 
            ma20, ma60, rsi, money_flow, 
            base_x, x_value, base_y, y_value, e_value
        ) VALUES (
            %s, %s, %s, %s, %s, %s, 
            %s, %s, %s, %s, 
            %s, %s, %s, %s, %s
        )
        ON DUPLICATE KEY UPDATE
            open = VALUES(open),
            high = VALUES(high),
            low = VALUES(low),
            close = VALUES(close),
            volume = VALUES(volume),
            ma20 = VALUES(ma20),
            ma60 = VALUES(ma60),
            rsi = VALUES(rsi),
            money_flow = VALUES(money_flow),
            base_x = VALUES(base_x),
            x_value = VALUES(x_value),
            base_y = VALUES(base_y),
            y_value = VALUES(y_value),
            e_value = VALUES(e_value)
        """
        
        # 准备数据
        data = []
        for _, row in missing_data.iterrows():
            data.append((
                row['Date'], 
                row['Open'], row['High'], row['Low'], row['Close'], row['Volume'],
                row['ma20'], row['ma60'], row['rsi'], row['money_flow'],
                row['base_x'], row['x_value'], row['base_y'], row['y_value'], row['e_value']
            ))
        
        # 批量插入
        cursor.executemany(sql, data)
        conn.commit()
        
        print(f"✅ 成功插入 {len(data)} 条数据")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 插入数据失败: {e}")
        return False

def update_all_indicators():
    """重新计算所有数据的技术指标"""
    print("\n🔄 重新计算所有技术指标...")
    
    try:
        conn = pymysql.connect(**db_config)
        
        # 获取所有基础数据
        df = pd.read_sql("""
            SELECT date, open, high, low, close, volume 
            FROM hkhsi50 
            ORDER BY date
        """, conn)
        
        if df.empty:
            print("❌ 没有找到基础数据")
            return False
        
        print(f"   • 处理 {len(df)} 条记录")
        
        # 重命名列以匹配计算函数
        df.columns = ['Date', 'Open', 'High', 'Low', 'Close', 'Volume']
        df['Date'] = pd.to_datetime(df['Date'])
        df.set_index('Date', inplace=True)
        
        # 计算指标
        df = calculate_indicators(df)
        
        # 更新数据库
        cursor = conn.cursor()
        
        update_sql = """
        UPDATE hkhsi50 SET 
            ma20 = %s, ma60 = %s, rsi = %s, money_flow = %s,
            base_x = %s, x_value = %s, base_y = %s, y_value = %s, e_value = %s
        WHERE date = %s
        """
        
        df.reset_index(inplace=True)
        df['Date'] = df['Date'].dt.date
        
        update_data = []
        for _, row in df.iterrows():
            update_data.append((
                row['ma20'], row['ma60'], row['rsi'], row['money_flow'],
                row['base_x'], row['x_value'], row['base_y'], row['y_value'], row['e_value'],
                row['Date']
            ))
        
        cursor.executemany(update_sql, update_data)
        conn.commit()
        
        print(f"✅ 成功更新 {len(update_data)} 条记录的技术指标")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 更新技术指标失败: {e}")
        return False

def verify_repair():
    """验证修复结果"""
    print("\n✅ 验证修复结果...")
    
    try:
        conn = pymysql.connect(**db_config)
        cursor = conn.cursor()
        
        # 检查总记录数
        cursor.execute("SELECT COUNT(*) FROM hkhsi50")
        total_count = cursor.fetchone()[0]
        
        # 检查缺失数据
        cursor.execute("""
            SELECT COUNT(*) FROM hkhsi50 
            WHERE close IS NULL OR volume IS NULL 
               OR x_value IS NULL OR y_value IS NULL 
               OR e_value IS NULL
        """)
        null_count = cursor.fetchone()[0]
        
        # 获取最新数据
        cursor.execute("""
            SELECT date, close, x_value, y_value, e_value 
            FROM hkhsi50 
            ORDER BY date DESC 
            LIMIT 1
        """)
        latest = cursor.fetchone()
        
        print(f"📊 修复后统计:")
        print(f"   • 总记录数: {total_count}")
        print(f"   • 缺失数据: {null_count} 条")
        
        if latest:
            print(f"\n📈 最新数据:")
            print(f"   • 日期: {latest[0]}")
            print(f"   • 收盘价: {latest[1]:.2f}")
            print(f"   • X值: {latest[2]:.3f}")
            print(f"   • Y值: {latest[3]:.3f}")
            print(f"   • E值: {latest[4]:.3f}")
        
        conn.close()
        
        if null_count == 0:
            print("🎉 数据修复完成！没有发现缺失数据")
            return True
        else:
            print(f"⚠️ 仍有 {null_count} 条缺失数据")
            return False
            
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False

def main():
    """主函数"""
    print("🔧 hkhsi50数据修复工具")
    print("="*50)
    
    # 1. 检查数据完整性
    total_count, date_range, null_count = check_data_integrity()
    if total_count is None:
        return
    
    # 2. 查找缺失日期
    missing_dates = find_missing_dates()
    
    # 3. 获取并插入缺失数据
    if missing_dates:
        missing_data = fetch_missing_data(missing_dates)
        if missing_data is not None:
            insert_missing_data(missing_data)
    
    # 4. 重新计算所有技术指标
    if null_count > 0 or missing_dates:
        update_all_indicators()
    
    # 5. 验证修复结果
    verify_repair()
    
    print("\n🎉 数据修复流程完成！")

if __name__ == "__main__":
    main()
