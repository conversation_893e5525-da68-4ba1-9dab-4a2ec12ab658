#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
将东亚银行(00023.HK)数据保存到MySQL数据库
=====================================
从Yahoo Finance获取数据并保存到localhost MySQL
"""

import yfinance as yf
import mysql.connector
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

class EABDataManager:
    """东亚银行数据管理类"""
    
    def __init__(self):
        """初始化数据库连接参数"""
        self.host = "localhost"
        self.user = "root"
        self.password = "12345678"
        self.database = "finance"
        self.table_name = "eab_0023hk"
        
    def connect_database(self):
        """连接MySQL数据库"""
        try:
            self.conn = mysql.connector.connect(
                host=self.host,
                user=self.user,
                password=self.password,
                database=self.database
            )
            self.cursor = self.conn.cursor()
            print("✅ MySQL数据库连接成功")
            return True
        except mysql.connector.Error as e:
            print(f"❌ MySQL连接失败: {e}")
            return False
    
    def create_table(self):
        """创建东亚银行数据表"""
        try:
            create_table_sql = f"""
            CREATE TABLE IF NOT EXISTS {self.table_name} (
                id INT AUTO_INCREMENT PRIMARY KEY,
                Date DATE NOT NULL UNIQUE,
                Open DECIMAL(10,4) NOT NULL,
                High DECIMAL(10,4) NOT NULL,
                Low DECIMAL(10,4) NOT NULL,
                Close DECIMAL(10,4) NOT NULL,
                Volume BIGINT NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                INDEX idx_date (Date),
                INDEX idx_close (Close)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            """
            
            self.cursor.execute(create_table_sql)
            self.conn.commit()
            print(f"✅ 数据表 {self.table_name} 创建成功")
            return True
            
        except mysql.connector.Error as e:
            print(f"❌ 创建数据表失败: {e}")
            return False
    
    def get_eab_data_from_yahoo(self):
        """从Yahoo Finance获取东亚银行数据"""
        print("📊 从Yahoo Finance获取东亚银行(00023.HK)数据...")
        
        try:
            # 东亚银行股票代码
            symbol = "0023.HK"
            
            # 创建股票对象
            eab = yf.Ticker(symbol)
            
            # 获取股票基本信息
            info = eab.info
            print(f"🏦 股票名称: {info.get('longName', '东亚银行有限公司')}")
            print(f"📊 股票代码: {symbol}")
            
            # 获取历史数据 - 最近5年
            end_date = datetime.now()
            start_date = end_date - timedelta(days=5*365)
            
            print(f"📅 获取数据期间: {start_date.strftime('%Y-%m-%d')} 至 {end_date.strftime('%Y-%m-%d')}")
            
            # 下载历史数据
            hist_data = eab.history(start=start_date, end=end_date)
            
            if hist_data.empty:
                print("❌ 未能获取到历史数据")
                return None
            
            # 数据预处理
            hist_data.reset_index(inplace=True)
            
            # 重命名列以匹配数据库结构
            df = pd.DataFrame({
                'Date': hist_data['Date'].dt.date,
                'Open': hist_data['Open'].round(4),
                'High': hist_data['High'].round(4),
                'Low': hist_data['Low'].round(4),
                'Close': hist_data['Close'].round(4),
                'Volume': hist_data['Volume'].astype(int)
            })
            
            # 数据清理
            df = df.dropna()
            df = df.sort_values('Date')
            
            print(f"✅ 成功获取 {len(df):,} 条历史记录")
            print(f"📊 数据期间: {df['Date'].min()} 至 {df['Date'].max()}")
            print(f"💰 最新价格: {df['Close'].iloc[-1]:.2f} 港元")
            
            return df
            
        except Exception as e:
            print(f"❌ 获取Yahoo Finance数据失败: {e}")
            print(f"💡 请确保已安装yfinance: pip install yfinance")
            return None
    
    def check_existing_data(self):
        """检查数据库中已有的数据"""
        try:
            check_sql = f"""
            SELECT 
                COUNT(*) as total_records,
                MIN(Date) as earliest_date,
                MAX(Date) as latest_date,
                MAX(Close) as max_price,
                MIN(Close) as min_price
            FROM {self.table_name}
            """
            
            self.cursor.execute(check_sql)
            result = self.cursor.fetchone()
            
            if result and result[0] > 0:
                print(f"📊 数据库中已有数据:")
                print(f"   记录数量: {result[0]:,} 条")
                print(f"   数据期间: {result[1]} 至 {result[2]}")
                print(f"   价格区间: {result[4]:.2f} - {result[3]:.2f} 港元")
                return result[0], result[2]  # 返回记录数和最新日期
            else:
                print(f"📊 数据库中暂无数据")
                return 0, None
                
        except mysql.connector.Error as e:
            print(f"❌ 检查现有数据失败: {e}")
            return 0, None
    
    def save_data_to_mysql(self, df):
        """保存数据到MySQL数据库"""
        if df is None or df.empty:
            print("❌ 没有数据需要保存")
            return False
        
        try:
            print(f"💾 开始保存数据到MySQL数据库...")
            
            # 使用INSERT IGNORE避免重复数据
            insert_sql = f"""
            INSERT IGNORE INTO {self.table_name} 
            (Date, Open, High, Low, Close, Volume) 
            VALUES (%s, %s, %s, %s, %s, %s)
            """
            
            # 准备数据
            data_tuples = []
            for _, row in df.iterrows():
                data_tuples.append((
                    row['Date'],
                    float(row['Open']),
                    float(row['High']),
                    float(row['Low']),
                    float(row['Close']),
                    int(row['Volume'])
                ))
            
            # 批量插入数据
            self.cursor.executemany(insert_sql, data_tuples)
            self.conn.commit()
            
            inserted_rows = self.cursor.rowcount
            print(f"✅ 成功保存 {inserted_rows:,} 条新记录到数据库")
            
            # 验证保存结果
            self.verify_saved_data()
            
            return True
            
        except mysql.connector.Error as e:
            print(f"❌ 保存数据失败: {e}")
            self.conn.rollback()
            return False
    
    def verify_saved_data(self):
        """验证保存的数据"""
        try:
            # 获取最新的数据统计
            verify_sql = f"""
            SELECT 
                COUNT(*) as total_records,
                MIN(Date) as earliest_date,
                MAX(Date) as latest_date,
                AVG(Close) as avg_price,
                MAX(Volume) as max_volume
            FROM {self.table_name}
            """
            
            self.cursor.execute(verify_sql)
            result = self.cursor.fetchone()
            
            print(f"\n📊 数据验证结果:")
            print(f"   总记录数: {result[0]:,} 条")
            print(f"   数据期间: {result[1]} 至 {result[2]}")
            print(f"   平均价格: {result[3]:.2f} 港元")
            print(f"   最大成交量: {result[4]:,} 股")
            
            # 显示最近5天的数据
            recent_sql = f"""
            SELECT Date, Open, High, Low, Close, Volume 
            FROM {self.table_name} 
            ORDER BY Date DESC 
            LIMIT 5
            """
            
            self.cursor.execute(recent_sql)
            recent_data = self.cursor.fetchall()
            
            print(f"\n📅 最近5天数据:")
            print(f"   {'日期':<12} {'开盘':<8} {'最高':<8} {'最低':<8} {'收盘':<8} {'成交量':<12}")
            print(f"   " + "-" * 65)
            for row in recent_data:
                print(f"   {row[0]:<12} {row[1]:<8.2f} {row[2]:<8.2f} {row[3]:<8.2f} {row[4]:<8.2f} {row[5]:<12,}")
            
        except mysql.connector.Error as e:
            print(f"❌ 数据验证失败: {e}")
    
    def update_data(self):
        """更新数据 - 获取最新数据并保存"""
        print("🔄 开始更新东亚银行数据...")
        
        # 检查现有数据
        existing_count, latest_date = self.check_existing_data()
        
        # 获取新数据
        df = self.get_eab_data_from_yahoo()
        
        if df is not None:
            # 如果有现有数据，只保存新数据
            if latest_date:
                df = df[df['Date'] > latest_date]
                if df.empty:
                    print("📊 数据已是最新，无需更新")
                    return True
                else:
                    print(f"📊 发现 {len(df)} 条新数据需要更新")
            
            # 保存数据
            return self.save_data_to_mysql(df)
        
        return False
    
    def get_data_for_analysis(self, days=1000):
        """从数据库获取数据用于分析"""
        try:
            query_sql = f"""
            SELECT Date, Open, High, Low, Close, Volume 
            FROM {self.table_name} 
            ORDER BY Date DESC 
            LIMIT {days}
            """
            
            df = pd.read_sql_query(query_sql, self.conn)
            df = df.sort_values('Date').reset_index(drop=True)
            
            print(f"📊 从数据库获取 {len(df)} 条记录用于分析")
            return df
            
        except Exception as e:
            print(f"❌ 从数据库获取数据失败: {e}")
            return None
    
    def close_connection(self):
        """关闭数据库连接"""
        if hasattr(self, 'cursor') and self.cursor:
            self.cursor.close()
        if hasattr(self, 'conn') and self.conn.is_connected():
            self.conn.close()
            print("🔗 MySQL连接已关闭")
    
    def run_complete_process(self):
        """运行完整的数据获取和保存流程"""
        print("🏦 东亚银行(00023.HK)数据管理系统")
        print("=" * 80)
        
        try:
            # 连接数据库
            if not self.connect_database():
                return False
            
            # 创建数据表
            if not self.create_table():
                return False
            
            # 更新数据
            if not self.update_data():
                return False
            
            print(f"\n🎉 东亚银行数据处理完成！")
            print(f"💡 数据已保存到MySQL数据库: {self.database}.{self.table_name}")
            
            return True
            
        except Exception as e:
            print(f"❌ 处理过程中发生错误: {e}")
            return False
        
        finally:
            self.close_connection()

def test_algorithm_with_eab_data():
    """使用东亚银行数据测试算法"""
    print(f"\n🎯 使用东亚银行数据测试我的算法")
    print("=" * 60)
    
    try:
        # 创建数据管理器
        eab_manager = EABDataManager()
        
        # 连接数据库
        if not eab_manager.connect_database():
            return
        
        # 获取数据
        df = eab_manager.get_data_for_analysis(500)  # 获取最近500天数据
        
        if df is not None and len(df) > 0:
            # 导入算法
            from my_trading_algorithm_core import MyTradingAlgorithmCore
            
            # 创建算法实例
            algo = MyTradingAlgorithmCore()
            
            # 处理数据
            df_processed = algo.process_market_data(df)
            
            # 获取最新信号
            signal_info = algo.get_latest_signal(df_processed)
            
            print(f"📅 分析日期: {signal_info['date']}")
            print(f"💰 东亚银行收盘价: {signal_info['close_price']:.2f} 港元")
            print(f"📊 Y值: {signal_info['y_value']:.4f}")
            print(f"📊 X值: {signal_info['x_value']:.4f}")
            
            # 交易信号
            signal = signal_info['signal']
            if signal == 1:
                print(f"🟢 交易建议: 做多东亚银行")
            elif signal == -1:
                print(f"🔴 交易建议: 做空东亚银行")
            else:
                print(f"⚪ 交易建议: 观望东亚银行")
            
            # 统计信号分布
            signals = df_processed['signal'].value_counts()
            print(f"\n📊 最近500天信号统计:")
            for sig, count in signals.items():
                sig_name = "🟢做多" if sig == 1 else "🔴做空" if sig == -1 else "⚪观望"
                print(f"   {sig_name}: {count}次 ({count/len(df_processed)*100:.1f}%)")
        
        eab_manager.close_connection()
        
    except ImportError:
        print("❌ 无法导入算法模块")
    except Exception as e:
        print(f"❌ 算法测试失败: {e}")

def main():
    """主函数"""
    # 运行完整的数据处理流程
    eab_manager = EABDataManager()
    success = eab_manager.run_complete_process()
    
    if success:
        # 测试算法
        test_algorithm_with_eab_data()

if __name__ == "__main__":
    main()
