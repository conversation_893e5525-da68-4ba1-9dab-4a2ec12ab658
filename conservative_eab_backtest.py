#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
保守版东亚银行回测系统
====================
采用更保守的策略，重点关注资本保护
"""

import yfinance as yf
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

class ConservativeEABBacktester:
    """保守版东亚银行回测器"""
    
    def __init__(self):
        """初始化回测参数"""
        self.symbol = "0023.HK"
        self.initial_capital = 2500.0
        self.position_size = 0.5       # 保守仓位50%
        
        # 保守的止盈止损参数
        self.take_profit_long = 0.03    # 多头止盈3%
        self.stop_loss_long = 0.02      # 多头止损2%
        self.take_profit_short = 0.02   # 空头止盈2%
        self.stop_loss_short = 0.03     # 空头止损3%
        
        self.commission_rate = 0.0025
        
    def get_data(self):
        """获取数据"""
        print("📊 获取东亚银行历史数据...")
        
        try:
            end_date = datetime.now()
            start_date = end_date - timedelta(days=10*365)
            
            eab = yf.Ticker(self.symbol)
            hist_data = eab.history(start=start_date, end=end_date)
            
            hist_data.reset_index(inplace=True)
            df = pd.DataFrame({
                'Date': hist_data['Date'].dt.date,
                'Open': hist_data['Open'],
                'High': hist_data['High'],
                'Low': hist_data['Low'],
                'Close': hist_data['Close'],
                'Volume': hist_data['Volume']
            })
            
            df = df.dropna().sort_values('Date').reset_index(drop=True)
            
            print(f"✅ 获取 {len(df):,} 条记录")
            return df
            
        except Exception as e:
            print(f"❌ 获取数据失败: {e}")
            return None
    
    def calculate_conservative_indicators(self, df):
        """计算保守版技术指标"""
        print("📊 计算保守版技术指标...")
        
        df = df.copy()
        
        # 1. 移动平均线系统
        df['MA10'] = df['Close'].rolling(10).mean()
        df['MA20'] = df['Close'].rolling(20).mean()
        df['MA50'] = df['Close'].rolling(50).mean()
        df['MA200'] = df['Close'].rolling(200).mean()
        
        # 2. 简化的Y指标
        window = 20
        df['High_20'] = df['High'].rolling(window).max()
        df['Low_20'] = df['Low'].rolling(window).min()
        df['Y'] = (df['Close'] - df['Low_20']) / (df['High_20'] - df['Low_20'])
        df['Y'] = df['Y'].fillna(0.5).clip(0, 1)
        
        # 3. 简化的X指标 (基于RSI)
        delta = df['Close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(14).mean()
        rs = gain / (loss + 1e-10)
        df['RSI'] = 100 - (100 / (1 + rs))
        df['X'] = df['RSI'] / 100  # 归一化到0-1
        
        # 4. E指标
        df['E'] = (8 * df['X'] - 3) * df['Y'] - 3 * df['X'] + 1
        
        # 5. 趋势确认指标
        df['Trend_Short'] = np.where(df['MA10'] > df['MA20'], 1, -1)
        df['Trend_Medium'] = np.where(df['MA20'] > df['MA50'], 1, -1)
        df['Trend_Long'] = np.where(df['MA50'] > df['MA200'], 1, -1)
        df['Trend_Score'] = df['Trend_Short'] + df['Trend_Medium'] + df['Trend_Long']
        
        # 6. 波动率指标
        df['Volatility'] = df['Close'].rolling(20).std() / df['Close'].rolling(20).mean()
        
        print("✅ 保守版指标计算完成")
        return df
    
    def generate_conservative_signals(self, df):
        """生成保守版交易信号"""
        print("🎯 生成保守版交易信号...")
        
        df = df.copy()
        signals = []
        
        for i, row in df.iterrows():
            if i < 200:  # 需要足够的历史数据
                signals.append(0)
                continue
            
            if (pd.isna(row['Y']) or pd.isna(row['X']) or pd.isna(row['E']) or 
                pd.isna(row['RSI']) or pd.isna(row['MA200'])):
                signals.append(0)
                continue
            
            y_val = row['Y']
            x_val = row['X']
            e_val = row['E']
            rsi = row['RSI']
            price = row['Close']
            ma10 = row['MA10']
            ma20 = row['MA20']
            ma50 = row['MA50']
            ma200 = row['MA200']
            trend_score = row['Trend_Score']
            volatility = row['Volatility']
            
            # 非常保守的多头信号 (只在强势上升趋势中)
            long_condition = (
                y_val > 0.7 and           # 价格位置很高
                x_val > 0.5 and           # RSI适中偏高
                e_val > 0.3 and           # E值明显为正
                rsi > 45 and rsi < 65 and # RSI在合理区间
                price > ma10 > ma20 > ma50 > ma200 and  # 完美多头排列
                trend_score == 3 and      # 所有趋势都向上
                volatility < 0.03         # 低波动率
            )
            
            # 保守的空头信号 (只在明确下降趋势中)
            short_condition = (
                y_val < 0.3 and           # 价格位置很低
                x_val < 0.4 and           # RSI偏低
                e_val < -0.3 and          # E值明显为负
                rsi > 35 and rsi < 55 and # 避免极端区域
                price < ma10 < ma20 < ma50 < ma200 and  # 完美空头排列
                trend_score == -3 and     # 所有趋势都向下
                volatility < 0.03         # 低波动率
            )
            
            if long_condition:
                signals.append(1)
            elif short_condition:
                signals.append(-1)
            else:
                signals.append(0)
        
        df['Signal'] = signals
        
        # 信号过滤 (更严格的确认)
        filtered_signals = []
        for i, signal in enumerate(df['Signal']):
            if signal != 0:
                # 需要连续5天确认
                if i >= 4:
                    recent_signals = df['Signal'].iloc[i-4:i+1].tolist()
                    if recent_signals.count(signal) >= 3:  # 5天中至少3天同向信号
                        filtered_signals.append(signal)
                    else:
                        filtered_signals.append(0)
                else:
                    filtered_signals.append(0)
            else:
                filtered_signals.append(0)
        
        df['Filtered_Signal'] = filtered_signals
        
        # 统计信号
        signal_counts = df['Filtered_Signal'].value_counts()
        print(f"📊 保守版信号统计:")
        for signal, count in signal_counts.items():
            signal_name = "🟢 做多" if signal == 1 else "🔴 做空" if signal == -1 else "⚪ 观望"
            print(f"   {signal_name}: {count}次 ({count/len(df)*100:.1f}%)")
        
        return df
    
    def run_conservative_backtest(self, df):
        """运行保守版回测"""
        print(f"\n💰 开始保守版回测 (初始资本: {self.initial_capital:,.0f}港元)")
        print("=" * 80)
        
        capital = self.initial_capital
        position = 0
        shares = 0
        entry_price = 0
        entry_date = None
        take_profit_price = 0
        stop_loss_price = 0
        
        trades = []
        equity_curve = []
        max_capital = capital
        max_drawdown = 0
        
        # 添加资金管理规则
        consecutive_losses = 0
        max_consecutive_losses = 3
        
        for i, row in df.iterrows():
            current_date = row['Date']
            current_price = row['Close']
            signal = row['Filtered_Signal']
            
            # 计算当前权益
            if position == 0:
                current_equity = capital
            else:
                if position == 1:
                    current_equity = capital + shares * (current_price - entry_price)
                else:
                    current_equity = capital + shares * (entry_price - current_price)
            
            equity_curve.append({
                'Date': current_date,
                'Equity': current_equity,
                'Price': current_price,
                'Position': position
            })
            
            # 更新最大回撤
            if current_equity > max_capital:
                max_capital = current_equity
            
            drawdown = (max_capital - current_equity) / max_capital
            if drawdown > max_drawdown:
                max_drawdown = drawdown
            
            # 检查平仓条件
            if position != 0:
                should_exit = False
                exit_reason = ""
                exit_price = current_price
                
                if position == 1:  # 多头
                    if row['High'] >= take_profit_price:
                        should_exit = True
                        exit_reason = "止盈"
                        exit_price = take_profit_price
                    elif row['Low'] <= stop_loss_price:
                        should_exit = True
                        exit_reason = "止损"
                        exit_price = stop_loss_price
                    elif signal == -1:
                        should_exit = True
                        exit_reason = "反向信号"
                        exit_price = current_price
                
                elif position == -1:  # 空头
                    if row['Low'] <= take_profit_price:
                        should_exit = True
                        exit_reason = "止盈"
                        exit_price = take_profit_price
                    elif row['High'] >= stop_loss_price:
                        should_exit = True
                        exit_reason = "止损"
                        exit_price = stop_loss_price
                    elif signal == 1:
                        should_exit = True
                        exit_reason = "反向信号"
                        exit_price = current_price
                
                if should_exit:
                    # 计算收益
                    if position == 1:
                        gross_profit = shares * (exit_price - entry_price)
                    else:
                        gross_profit = shares * (entry_price - exit_price)
                    
                    commission = shares * exit_price * self.commission_rate
                    net_profit = gross_profit - commission
                    capital += net_profit
                    
                    # 更新连续亏损计数
                    if net_profit < 0:
                        consecutive_losses += 1
                    else:
                        consecutive_losses = 0
                    
                    holding_days = (current_date - entry_date).days
                    
                    trades.append({
                        'entry_date': entry_date,
                        'exit_date': current_date,
                        'direction': '多头' if position == 1 else '空头',
                        'entry_price': entry_price,
                        'exit_price': exit_price,
                        'shares': shares,
                        'gross_profit': gross_profit,
                        'commission': commission,
                        'net_profit': net_profit,
                        'holding_days': holding_days,
                        'exit_reason': exit_reason,
                        'capital_after': capital
                    })
                    
                    position = 0
                    shares = 0
            
            # 检查开仓条件 (加入资金管理)
            if (position == 0 and signal != 0 and capital > 500 and 
                consecutive_losses < max_consecutive_losses):  # 连续亏损保护
                
                position = signal
                entry_price = current_price
                entry_date = current_date
                
                # 根据连续亏损调整仓位
                adjusted_position_size = self.position_size * (0.8 ** consecutive_losses)
                position_value = capital * adjusted_position_size
                shares = int(position_value / entry_price)
                
                if shares > 0:
                    # 扣除开仓手续费
                    open_commission = shares * entry_price * self.commission_rate
                    capital -= open_commission
                    
                    # 计算止盈止损位
                    if position == 1:
                        take_profit_price = entry_price * (1 + self.take_profit_long)
                        stop_loss_price = entry_price * (1 - self.stop_loss_long)
                    else:
                        take_profit_price = entry_price * (1 - self.take_profit_short)
                        stop_loss_price = entry_price * (1 + self.stop_loss_short)
                else:
                    position = 0
        
        # 强制平仓最后持仓
        if position != 0:
            final_price = df['Close'].iloc[-1]
            if position == 1:
                gross_profit = shares * (final_price - entry_price)
            else:
                gross_profit = shares * (entry_price - final_price)
            
            commission = shares * final_price * self.commission_rate
            net_profit = gross_profit - commission
            capital += net_profit
            
            trades.append({
                'entry_date': entry_date,
                'exit_date': df['Date'].iloc[-1],
                'direction': '多头' if position == 1 else '空头',
                'entry_price': entry_price,
                'exit_price': final_price,
                'shares': shares,
                'gross_profit': gross_profit,
                'commission': commission,
                'net_profit': net_profit,
                'holding_days': (df['Date'].iloc[-1] - entry_date).days,
                'exit_reason': '强制平仓',
                'capital_after': capital
            })
        
        return trades, equity_curve, max_drawdown
    
    def analyze_conservative_results(self, trades, equity_curve, max_drawdown, df):
        """分析保守版回测结果"""
        print(f"\n📊 保守版回测结果分析")
        print("=" * 80)
        
        if not trades:
            print("❌ 没有交易记录")
            return {'final_capital': self.initial_capital}
        
        # 基本统计
        final_capital = trades[-1]['capital_after']
        total_return = (final_capital - self.initial_capital) / self.initial_capital * 100
        
        print(f"💰 资金表现:")
        print(f"   初始资金: {self.initial_capital:,.2f} 港元")
        print(f"   最终资金: {final_capital:,.2f} 港元")
        print(f"   总收益: {final_capital - self.initial_capital:+,.2f} 港元")
        print(f"   总收益率: {total_return:+.2f}%")
        print(f"   最大回撤: {max_drawdown*100:.2f}%")
        
        # 交易统计
        total_trades = len(trades)
        winning_trades = len([t for t in trades if t['net_profit'] > 0])
        win_rate = winning_trades / total_trades * 100 if total_trades > 0 else 0
        
        print(f"\n📊 交易统计:")
        print(f"   总交易次数: {total_trades}")
        print(f"   盈利交易: {winning_trades}次")
        print(f"   胜率: {win_rate:.1f}%")
        
        if trades:
            profits = [t['net_profit'] for t in trades]
            print(f"   平均每笔收益: {np.mean(profits):+.2f} 港元")
            print(f"   最大盈利: {max(profits):+.2f} 港元")
            print(f"   最大亏损: {min(profits):+.2f} 港元")
            
            # 风险指标
            if len(profits) > 1:
                sharpe_ratio = np.mean(profits) / (np.std(profits) + 1e-10)
                print(f"   夏普比率: {sharpe_ratio:.2f}")
        
        # 年化收益率
        years = (df['Date'].iloc[-1] - df['Date'].iloc[0]).days / 365.25
        annual_return = (final_capital / self.initial_capital) ** (1/years) - 1
        
        print(f"\n📈 年化表现:")
        print(f"   回测期间: {years:.1f}年")
        print(f"   年化收益率: {annual_return*100:+.2f}%")
        
        # 买入持有对比
        start_price = df['Close'].iloc[0]
        end_price = df['Close'].iloc[-1]
        buy_hold_return = (end_price - start_price) / start_price * 100
        
        print(f"\n🆚 买入持有对比:")
        print(f"   买入持有收益率: {buy_hold_return:+.2f}%")
        print(f"   策略超额收益: {total_return - buy_hold_return:+.2f}%")
        
        # 显示所有交易
        if trades:
            print(f"\n📅 所有交易记录:")
            print("-" * 120)
            for trade in trades:
                print(f"   {trade['entry_date']} → {trade['exit_date']} | "
                      f"{trade['direction']} | {trade['entry_price']:.2f}→{trade['exit_price']:.2f} | "
                      f"收益:{trade['net_profit']:+.2f} | {trade['exit_reason']} | "
                      f"资金:{trade['capital_after']:,.0f}")
        
        return {
            'final_capital': final_capital,
            'total_return': total_return,
            'annual_return': annual_return,
            'max_drawdown': max_drawdown,
            'total_trades': total_trades,
            'win_rate': win_rate,
            'buy_hold_return': buy_hold_return
        }
    
    def run_complete_conservative_backtest(self):
        """运行完整保守版回测"""
        print("🎯 保守版东亚银行10年回测系统")
        print("=" * 80)
        
        try:
            df = self.get_data()
            if df is None:
                return None
            
            df = self.calculate_conservative_indicators(df)
            df = self.generate_conservative_signals(df)
            trades, equity_curve, max_drawdown = self.run_conservative_backtest(df)
            results = self.analyze_conservative_results(trades, equity_curve, max_drawdown, df)
            
            print(f"\n🎉 保守版回测完成！")
            
            return {
                'results': results,
                'trades': trades,
                'equity_curve': equity_curve,
                'data': df
            }
            
        except Exception as e:
            print(f"❌ 回测失败: {e}")
            return None

def main():
    """主函数"""
    backtester = ConservativeEABBacktester()
    results = backtester.run_complete_conservative_backtest()
    
    if results:
        print(f"\n💡 保守版回测总结:")
        res = results['results']
        print(f"   📊 10年投资2500港元的保守结果:")
        print(f"   💰 最终资金: {res['final_capital']:,.2f}港元")
        print(f"   📈 总收益率: {res['total_return']:+.2f}%")
        print(f"   📊 年化收益率: {res['annual_return']*100:+.2f}%")
        print(f"   🎯 胜率: {res['win_rate']:.1f}%")
        print(f"   📉 最大回撤: {res['max_drawdown']*100:.2f}%")
        print(f"   🆚 超越买入持有: {res['total_return'] - res['buy_hold_return']:+.2f}%")

if __name__ == "__main__":
    main()
