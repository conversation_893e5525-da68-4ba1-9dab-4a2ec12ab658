#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
长江和记实业0001.HK - 严格分段Cosmoon XYE策略
==========================================

严格按照指定的分段条件执行交易策略

分段规则:
E<0, x>0.45 且 y<0.35, 做空
E<0, x<0.45 且 y>0.35, 做空
E>0 -> x>=0.5 且 y>=0.5, 做多
E>0 -> x<0.25 且 y<0.25, 做多
E<=0 -> 0.333<y<0.4, x为[0,1], 做多
E<=0 -> y<0.5 且 x<0.5, 做多

作者: Cosmoon NG
"""

import yfinance as yf
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from scipy import stats
import warnings
warnings.filterwarnings('ignore')

class StrictSegmentXYE0001HK:
    def __init__(self):
        """初始化严格分段XYE策略"""
        self.symbol = "0700.HK"
        self.initial_capital = 10000.00
        self.monthly_addition = 3000.00
        self.commission_rate = 0.001

        # 止盈止损参数
        self.take_profit_long = 0.012
        self.stop_loss_long = 0.006
        self.take_profit_short = 0.012
        self.stop_loss_short = 0.006

        # 凯利公式参数
        self.win_probability = 0.5  # 预期胜率50%
        self.profit_loss_ratio = 2  # 盈亏比2:1 (1.2%止盈 vs 0.6%止损)
        self.kelly_fraction = (self.win_probability * (self.profit_loss_ratio + 1) - 1) / self.profit_loss_ratio
        self.kelly_fraction = max(0, min(self.kelly_fraction, 0.25))  # 限制在0-25%之间

        # 交易状态
        self.position = 0
        self.entry_price = 0
        self.shares = 0
        self.trades = []
        self.equity_curve = []

    def load_data(self):
        """加载数据"""
        print(f"📊 加载{self.symbol}历史数据...")

        try:
            end_date = datetime.now()
            start_date = end_date - timedelta(days=20*365)

            ticker = yf.Ticker(self.symbol)
            hist_data = ticker.history(start=start_date, end=end_date)

            self.df = pd.DataFrame({
                'date': hist_data.index,
                'open': hist_data['Open'],
                'high': hist_data['High'],
                'low': hist_data['Low'],
                'close': hist_data['Close'],
                'volume': hist_data['Volume']
            })

            self.df = self.df.dropna().sort_values('date').reset_index(drop=True)

            print(f"   ✅ 成功获取 {len(self.df)} 条数据")
            print(f"   📈 价格范围: {self.df['close'].min():.2f} - {self.df['close'].max():.2f} 港元")

            return True

        except Exception as e:
            print(f"   ❌ 数据加载失败: {e}")
            return False

    def calculate_xye_indicators(self):
        """计算XYE指标"""
        print(f"\n🧮 计算XYE指标...")

        # Y值计算
        window = 20
        self.df['high_20'] = self.df['high'].rolling(window).max()
        self.df['low_20'] = self.df['low'].rolling(window).min()
        self.df['y_value'] = (self.df['close'] - self.df['low_20']) / (self.df['high_20'] - self.df['low_20'])
        self.df['y_value'] = self.df['y_value'].fillna(0.5).clip(0, 1)

        # X值计算
        self.df['typical_price'] = (self.df['high'] + self.df['low'] + self.df['close']) / 3
        self.df['money_flow'] = self.df['typical_price'] * self.df['volume']
        self.df['price_change'] = self.df['typical_price'].diff()

        self.df['positive_mf'] = np.where(self.df['price_change'] > 0, self.df['money_flow'], 0)
        self.df['negative_mf'] = np.where(self.df['price_change'] < 0, self.df['money_flow'], 0)

        period = 14
        self.df['positive_mf_14'] = self.df['positive_mf'].rolling(period).sum()
        self.df['negative_mf_14'] = self.df['negative_mf'].rolling(period).sum()
        self.df['money_flow_ratio'] = self.df['positive_mf_14'] / (self.df['negative_mf_14'] + 1e-10)
        self.df['mfi'] = 100 - (100 / (1 + self.df['money_flow_ratio']))
        self.df['x_value'] = self.df['mfi'] / 100

        # E值计算
        self.df['e_value'] = (8 * self.df['x_value'] - 3) * self.df['y_value'] - 3 * self.df['x_value'] + 1

        # === 中值回归计算 (策略精髓) ===
        print("   计算中值回归...")

        # 添加序号
        self.df['i'] = range(1, len(self.df) + 1)

        # 计算线性回归线
        slope, intercept, r_value, _, _ = stats.linregress(self.df['i'], self.df['close'])
        self.df['regression_line'] = intercept + slope * self.df['i']

        # 计算价格相对回归线的位置 (中值回归核心)
        self.df['price_position'] = (self.df['close'] - self.df['regression_line']) / self.df['regression_line']

        # 计算回归偏离度 (绝对值)
        self.df['regression_deviation'] = abs(self.df['price_position'])

        print(f"   ✅ XYE指标计算完成")
        print(f"   📈 中值回归: R²={r_value**2:.4f}, 斜率={slope:.6f}")
        print(f"   📊 价格偏离范围: {self.df['price_position'].min():.3f} 至 {self.df['price_position'].max():.3f}")

    def get_trading_signal(self, x_val, y_val, price_position):
        """根据分段规则+中值回归获取交易信号 (策略精髓)"""

        # 观望条件 (优先级最高)
        if 0.333 < y_val < 0.4:  # 只看Y值，不限制X值
            return "HOLD"   # 观望: 0.333 < y < 0.4

        # === 中值回归逻辑 (策略精髓) ===
        # 价格远高于回归线时，倾向于做空 (回归压力)
        # 价格远低于回归线时，倾向于做多 (回归机会)

        # 做多条件 (多头，看涨) - 结合中值回归
        if x_val >= 0.5 and y_val >= 0.5:
            # 强势信号，但如果价格过高于回归线，减少做多
            if price_position > 0.2:  # 价格高于回归线20%以上，谨慎做多
                return "HOLD"
            return "LONG"   # x>=0.5 且 y>=0.5, 做多
        elif x_val < 0.25 and y_val < 0.25:
            # 弱势信号，但如果价格远低于回归线，是回归机会
            if price_position < -0.1:  # 价格低于回归线10%以上，回归机会
                return "LONG"
            return "LONG"   # x<0.25 且 y<0.25, 做多

        # 做空条件 (空头，看跌) - 结合中值回归
        if x_val > 0.45 and y_val < 0.35:
            # 弱势信号，如果价格高于回归线，增强做空
            if price_position > 0.1:  # 价格高于回归线10%以上，回归压力
                return "SHORT"
            return "SHORT"  # x>0.45 且 y<0.35, 做空
        elif x_val < 0.45 and y_val > 0.35:
            # 混合信号，如果价格过高于回归线，倾向做空
            if price_position > 0.15:  # 价格高于回归线15%以上
                return "SHORT"
            return "SHORT"  # x<0.45 且 y>0.35, 做空

        # === 纯中值回归信号 ===
        # 当XY信号不明确时，依靠中值回归
        if price_position < -0.2:  # 价格低于回归线20%以上，强烈回归机会
            return "LONG"
        elif price_position > 0.25:  # 价格高于回归线25%以上，强烈回归压力
            return "SHORT"

        return "HOLD"  # 其他情况持有

    def add_monthly_capital(self, current_date, total_capital):
        """每月追加资金到总资本 (复利基础)"""
        if not hasattr(self, 'last_addition_month'):
            self.last_addition_month = current_date.replace(day=1)
            return total_capital

        current_month = current_date.replace(day=1)
        if current_month > self.last_addition_month:
            total_capital += self.monthly_addition
            self.last_addition_month = current_month

        return total_capital

    def run_backtest(self):
        """运行严格分段策略回测 (真正的复利计算)"""
        print(f"\n💼 开始中值回归+复利策略回测...")

        # 复利计算的关键变量
        total_capital = self.initial_capital  # 总资本 (包括追加资金和累计盈利)
        available_cash = self.initial_capital  # 可用现金
        trade_count = 0
        winning_trades = 0
        losing_trades = 0

        # 统计信号分布
        signal_stats = {"LONG": 0, "SHORT": 0, "HOLD": 0}

        for i in range(60, len(self.df)):
            row = self.df.iloc[i]
            current_date = row['date']
            current_price = row['close']

            # 每月追加资金到总资本 (复利基础)
            total_capital = self.add_monthly_capital(current_date, total_capital)
            available_cash = self.add_monthly_capital(current_date, available_cash)

            # 获取交易信号 (XY值 + 中值回归)
            signal = self.get_trading_signal(row['x_value'], row['y_value'], row['price_position'])
            signal_stats[signal] += 1

            # 观望信号 - 强制平仓，不持仓
            if signal == "HOLD" and self.position != 0:
                if self.position == 1:  # 平多头仓
                    proceeds = self.shares * current_price * (1 - self.commission_rate)
                    available_cash += proceeds
                    profit = proceeds - (self.shares * self.entry_price * (1 + self.commission_rate))

                    # 复利关键: 盈利加入总资本
                    total_capital += profit

                    self.trades.append({
                        'date': current_date,
                        'type': 'long_exit_hold',
                        'entry_price': self.entry_price,
                        'exit_price': current_price,
                        'profit': profit,
                        'signal': 'HOLD_SIGNAL'
                    })

                    if profit > 0:
                        winning_trades += 1
                    else:
                        losing_trades += 1

                elif self.position == -1:  # 平空头仓
                    profit = self.shares * (self.entry_price - current_price) * (1 - self.commission_rate)
                    available_cash += profit

                    # 复利关键: 盈利加入总资本
                    total_capital += profit

                    self.trades.append({
                        'date': current_date,
                        'type': 'short_exit_hold',
                        'entry_price': self.entry_price,
                        'exit_price': current_price,
                        'profit': profit,
                        'signal': 'HOLD_SIGNAL'
                    })

                    if profit > 0:
                        winning_trades += 1
                    else:
                        losing_trades += 1

                self.position = 0
                self.shares = 0
                trade_count += 1

            # 检查止盈止损
            if self.position != 0:
                if self.position == 1:  # 多头持仓
                    profit_ratio = (current_price - self.entry_price) / self.entry_price

                    if profit_ratio >= self.take_profit_long:  # 止盈
                        proceeds = self.shares * current_price * (1 - self.commission_rate)
                        available_cash += proceeds
                        profit = proceeds - (self.shares * self.entry_price * (1 + self.commission_rate))

                        # 复利关键: 盈利加入总资本
                        total_capital += profit

                        self.trades.append({
                            'date': current_date,
                            'type': 'long_exit_profit',
                            'entry_price': self.entry_price,
                            'exit_price': current_price,
                            'profit': profit,
                            'signal': 'TAKE_PROFIT'
                        })

                        if profit > 0:
                            winning_trades += 1
                        else:
                            losing_trades += 1

                        self.position = 0
                        self.shares = 0
                        trade_count += 1

                    elif profit_ratio <= -self.stop_loss_long:  # 止损
                        proceeds = self.shares * current_price * (1 - self.commission_rate)
                        available_cash += proceeds
                        profit = proceeds - (self.shares * self.entry_price * (1 + self.commission_rate))

                        # 复利关键: 亏损也影响总资本
                        total_capital += profit

                        self.trades.append({
                            'date': current_date,
                            'type': 'long_exit_loss',
                            'entry_price': self.entry_price,
                            'exit_price': current_price,
                            'profit': profit,
                            'signal': 'STOP_LOSS'
                        })

                        losing_trades += 1
                        self.position = 0
                        self.shares = 0
                        trade_count += 1

                elif self.position == -1:  # 空头持仓
                    profit_ratio = (self.entry_price - current_price) / self.entry_price

                    if profit_ratio >= self.take_profit_short:  # 止盈
                        profit = self.shares * (self.entry_price - current_price) * (1 - self.commission_rate)
                        available_cash += profit

                        # 复利关键: 盈利加入总资本
                        total_capital += profit

                        self.trades.append({
                            'date': current_date,
                            'type': 'short_exit_profit',
                            'entry_price': self.entry_price,
                            'exit_price': current_price,
                            'profit': profit,
                            'signal': 'TAKE_PROFIT'
                        })

                        if profit > 0:
                            winning_trades += 1
                        else:
                            losing_trades += 1

                        self.position = 0
                        self.shares = 0
                        trade_count += 1

                    elif profit_ratio <= -self.stop_loss_short:  # 止损
                        loss = self.shares * (self.entry_price - current_price) * (1 - self.commission_rate)
                        available_cash += loss

                        # 复利关键: 亏损也影响总资本
                        total_capital += loss

                        self.trades.append({
                            'date': current_date,
                            'type': 'short_exit_loss',
                            'entry_price': self.entry_price,
                            'exit_price': current_price,
                            'profit': loss,
                            'signal': 'STOP_LOSS'
                        })

                        losing_trades += 1
                        self.position = 0
                        self.shares = 0
                        trade_count += 1

            # 开仓信号判断
            if self.position == 0:
                if signal == "LONG":
                    self.position = 1
                    self.entry_price = current_price
                    # 复利关键: 基于总资本计算凯利投资额
                    investment_amount = total_capital * self.kelly_fraction
                    self.shares = int(investment_amount / current_price)
                    cost = self.shares * current_price * (1 + self.commission_rate)
                    available_cash -= cost

                    self.trades.append({
                        'date': current_date,
                        'type': 'long_entry',
                        'entry_price': self.entry_price,
                        'shares': self.shares,
                        'investment_amount': investment_amount,
                        'total_capital': total_capital,
                        'signal': f"E={row['e_value']:.3f},X={row['x_value']:.3f},Y={row['y_value']:.3f}"
                    })

                elif signal == "SHORT":
                    self.position = -1
                    self.entry_price = current_price
                    # 复利关键: 基于总资本计算凯利投资额
                    investment_amount = total_capital * self.kelly_fraction
                    self.shares = int(investment_amount / current_price)
                    cost = self.shares * current_price * self.commission_rate
                    available_cash -= cost

                    self.trades.append({
                        'date': current_date,
                        'type': 'short_entry',
                        'entry_price': self.entry_price,
                        'shares': self.shares,
                        'investment_amount': investment_amount,
                        'total_capital': total_capital,
                        'signal': f"E={row['e_value']:.3f},X={row['x_value']:.3f},Y={row['y_value']:.3f}"
                    })

            # 记录权益曲线 (复利版本)
            current_equity = available_cash
            if self.position != 0 and self.shares > 0:
                if self.position == 1:
                    unrealized_pnl = self.shares * (current_price - self.entry_price)
                else:
                    unrealized_pnl = self.shares * (self.entry_price - current_price)
                current_equity += unrealized_pnl

            self.equity_curve.append({
                'date': current_date,
                'equity': current_equity,
                'total_capital': total_capital,
                'available_cash': available_cash,
                'position': self.position,
                'signal': signal,
                'e_value': row['e_value'],
                'x_value': row['x_value'],
                'y_value': row['y_value']
            })

        self.final_capital = available_cash
        self.final_total_capital = total_capital
        self.total_trades = trade_count
        self.winning_trades = winning_trades
        self.losing_trades = losing_trades
        self.signal_stats = signal_stats

        print(f"   ✅ 回测完成")

    def analyze_results(self):
        """分析回测结果"""
        print(f"\n📈 严格分段XYE策略回测结果")
        print("=" * 60)

        # 基础统计
        total_days = (self.df['date'].max() - self.df['date'].min()).days
        total_years = total_days / 365

        print(f"📊 基础信息:")
        print(f"   标的: {self.symbol} (腾讯控股)")
        print(f"   回测期间: {total_years:.1f}年")

        # 信号分布统计
        total_signals = sum(self.signal_stats.values())
        print(f"\n📋 信号分布统计:")
        print(f"   做多信号: {self.signal_stats['LONG']} ({self.signal_stats['LONG']/total_signals*100:.1f}%)")
        print(f"   做空信号: {self.signal_stats['SHORT']} ({self.signal_stats['SHORT']/total_signals*100:.1f}%)")
        print(f"   持有信号: {self.signal_stats['HOLD']} ({self.signal_stats['HOLD']/total_signals*100:.1f}%)")

        # 策略表现
        months_passed = int(total_days / 30)
        total_invested = self.initial_capital + months_passed * self.monthly_addition
        net_profit = self.final_capital - total_invested
        total_return = net_profit / total_invested * 100
        annual_return = ((self.final_capital / total_invested) ** (1/total_years) - 1) * 100

        print(f"\n🚀 策略表现:")
        print(f"   总投入: {total_invested:,.0f}港元")
        print(f"   最终资金: {self.final_capital:,.0f}港元")
        print(f"   净收益: {net_profit:,.0f}港元")
        print(f"   总收益率: {total_return:.2f}%")
        print(f"   年化收益率: {annual_return:.2f}%")

        # 交易统计
        win_rate = self.winning_trades / self.total_trades * 100 if self.total_trades > 0 else 0

        print(f"\n📋 交易统计:")
        print(f"   总交易次数: {self.total_trades}")
        print(f"   盈利交易: {self.winning_trades}")
        print(f"   亏损交易: {self.losing_trades}")
        print(f"   胜率: {win_rate:.2f}%")

        # 分段条件触发统计
        print(f"\n🎯 分段条件触发分析:")
        long_entries = [t for t in self.trades if t['type'] == 'long_entry']
        short_entries = [t for t in self.trades if t['type'] == 'short_entry']

        print(f"   多头开仓: {len(long_entries)}次")
        print(f"   空头开仓: {len(short_entries)}次")

        if len(long_entries) > 0:
            print(f"   多头开仓示例: {long_entries[0]['signal']}")
        if len(short_entries) > 0:
            print(f"   空头开仓示例: {short_entries[0]['signal']}")

def main():
    """主函数"""
    print("🎯 腾讯控股0700.HK - 复利+中值回归+凯利公式+分段XY策略")
    print("=" * 60)
    print("📋 策略核心 (四大精髓):")
    print("   💰 复利计算 (财富增长精髓):")
    print("   • 总资本 = 初始资金 + 追加资金 + 累计盈利")
    print("   • 凯利投资额 = 总资本 × 25% (动态增长)")
    print("   • 盈利再投资: 每次盈利都加入总资本")
    print("   🎯 中值回归 (策略精髓):")
    print("   • 价格低于回归线20%以上 → 强烈做多 (回归机会)")
    print("   • 价格高于回归线25%以上 → 强烈做空 (回归压力)")
    print("   💰 凯利公式资金管理:")
    print("   • 胜率: 50%, 盈亏比: 2:1, 凯利比例: 25%")
    print("   📋 分段规则:")
    print("   • 观望 (强制平仓): 0.333 < y < 0.4")
    print("   • 做多: x>=0.5且y>=0.5 或 x<0.25且y<0.25")
    print("   • 做空: x>0.45且y<0.35 或 x<0.45且y>0.35")

    backtest = StrictSegmentXYE0001HK()

    if not backtest.load_data():
        return

    backtest.calculate_xye_indicators()
    backtest.run_backtest()
    backtest.analyze_results()

    print(f"\n🎉 严格分段策略回测完成!")

if __name__ == "__main__":
    main()
