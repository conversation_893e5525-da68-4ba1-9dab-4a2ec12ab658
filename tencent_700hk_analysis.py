#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
腾讯700HK股票分析系统
====================

基于Cosmoon XYE系统分析腾讯控股(0700.HK)
包含完整的技术指标计算和交易信号生成

作者: Cosmoon NG
"""

import yfinance as yf
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

def get_tencent_data(period="1y"):
    """获取腾讯700HK数据"""
    try:
        ticker = yf.Ticker("0700.HK")
        data = ticker.history(period=period)

        if data.empty:
            print("❌ 无法获取腾讯数据")
            return None

        print(f"✅ 成功获取腾讯数据: {len(data)}天")
        return data
    except Exception as e:
        print(f"❌ 获取数据失败: {e}")
        return None

def calculate_technical_indicators(data):
    """计算技术指标"""
    df = data.copy()

    # 基础计算
    df['TypicalPrice'] = (df['High'] + df['Low'] + df['Close']) / 3
    df['Volume_MA'] = df['Volume'].rolling(window=14).mean()

    # 计算MFI (Money Flow Index)
    df['MoneyFlow'] = df['TypicalPrice'] * df['Volume']

    # 价格变化
    df['Price_Change'] = df['TypicalPrice'].diff()

    # 正负资金流
    df['PositiveMoneyFlow'] = np.where(df['Price_Change'] > 0, df['MoneyFlow'], 0)
    df['NegativeMoneyFlow'] = np.where(df['Price_Change'] < 0, df['MoneyFlow'], 0)

    # 14日资金流总和
    df['PositiveMoneyFlow_14'] = df['PositiveMoneyFlow'].rolling(window=14).sum()
    df['NegativeMoneyFlow_14'] = df['NegativeMoneyFlow'].rolling(window=14).sum()

    # MoneyFlowRatio
    df['MoneyFlowRatio'] = df['PositiveMoneyFlow_14'] / (df['NegativeMoneyFlow_14'] + 1e-10)

    # MFI
    df['MFI'] = 100 - (100 / (1 + df['MoneyFlowRatio']))

    # 计算RSI
    delta = df['Close'].diff()
    gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
    rs = gain / loss
    df['RSI'] = 100 - (100 / (1 + rs))

    # 计算移动平均线
    df['MA5'] = df['Close'].rolling(window=5).mean()
    df['MA10'] = df['Close'].rolling(window=10).mean()
    df['MA20'] = df['Close'].rolling(window=20).mean()

    # 计算布林带
    df['BB_Middle'] = df['Close'].rolling(window=20).mean()
    bb_std = df['Close'].rolling(window=20).std()
    df['BB_Upper'] = df['BB_Middle'] + (bb_std * 2)
    df['BB_Lower'] = df['BB_Middle'] - (bb_std * 2)

    return df

def calculate_cosmoon_xye(df):
    """计算Cosmoon XYE指标"""

    # X值 (资金流强度) - 基于MoneyFlowRatio标准化
    mfr_mean = df['MoneyFlowRatio'].rolling(window=50).mean()
    mfr_std = df['MoneyFlowRatio'].rolling(window=50).std()
    df['X_Value'] = (df['MoneyFlowRatio'] - mfr_mean) / (mfr_std + 1e-10)
    df['X_Value'] = (df['X_Value'] + 3) / 6  # 标准化到0-1
    df['X_Value'] = df['X_Value'].clip(0, 1)

    # Y值 (价格强度) - 基于价格相对位置
    price_min = df['Close'].rolling(window=50).min()
    price_max = df['Close'].rolling(window=50).max()
    df['Y_Value'] = (df['Close'] - price_min) / (price_max - price_min + 1e-10)

    # Full_Y (行控制系数) - 基于价格趋势强度
    df['Price_Trend'] = df['Close'].rolling(window=10).apply(
        lambda x: np.polyfit(range(len(x)), x, 1)[0] if len(x) == 10 else 0
    )
    trend_mean = df['Price_Trend'].rolling(window=50).mean()
    trend_std = df['Price_Trend'].rolling(window=50).std()
    df['Full_Y'] = (df['Price_Trend'] - trend_mean) / (trend_std + 1e-10)
    df['Full_Y'] = (df['Full_Y'] + 3) / 6  # 标准化到0-1
    df['Full_Y'] = df['Full_Y'].clip(0, 1)

    # E值 (综合能量) - 基于价格和成交量的综合指标
    volume_norm = df['Volume'] / df['Volume'].rolling(window=50).mean()
    price_change_norm = abs(df['Close'].pct_change()) * 100
    df['E_Value'] = (volume_norm * price_change_norm).rolling(window=5).mean()
    e_mean = df['E_Value'].rolling(window=50).mean()
    e_std = df['E_Value'].rolling(window=50).std()
    df['E_Value'] = (df['E_Value'] - e_mean) / (e_std + 1e-10)
    df['E_Value'] = df['E_Value'] / 10  # 调整范围

    # MyE (Cosmoon公式)
    df['MyE'] = 8 * df['MoneyFlowRatio'] * df['Full_Y'] - 3 * df['MoneyFlowRatio'] - 3 * df['Full_Y'] + 1

    return df

def generate_trading_signals(df):
    """生成交易信号"""

    signals = []

    for i in range(len(df)):
        row = df.iloc[i]

        # 获取指标值
        mye = row['MyE']
        y_val = row['Y_Value']
        x_val = row['X_Value']
        e_val = row['E_Value']
        rsi = row['RSI']
        mfi = row['MFI']

        # 交易信号逻辑
        if pd.isna(mye) or pd.isna(y_val) or pd.isna(x_val):
            signal = "数据不足"
            strength = "无"
        elif mye > 0.1 and y_val > 0.6 and x_val > 0.6:
            signal = "强烈买入"
            strength = "强"
        elif mye > 0 and y_val > 0.5 and x_val > 0.5:
            signal = "买入"
            strength = "中"
        elif mye < -0.1 and (y_val < 0.3 or x_val < 0.3):
            signal = "强烈卖出"
            strength = "强"
        elif mye < 0 and (y_val < 0.4 or x_val < 0.4):
            signal = "卖出"
            strength = "中"
        elif rsi < 30 and mfi < 30:
            signal = "超卖买入"
            strength = "中"
        elif rsi > 70 and mfi > 70:
            signal = "超买卖出"
            strength = "中"
        else:
            signal = "观望"
            strength = "弱"

        signals.append({
            'signal': signal,
            'strength': strength
        })

    signal_df = pd.DataFrame(signals)
    df['TradingSignal'] = signal_df['signal']
    df['SignalStrength'] = signal_df['strength']

    return df

def analyze_tencent_700hk():
    """分析腾讯700HK"""

    print("🎯 腾讯700HK股票分析系统")
    print("=" * 60)

    # 获取数据
    data = get_tencent_data("6mo")  # 获取6个月数据
    if data is None:
        return

    # 计算技术指标
    print("📊 计算技术指标...")
    df = calculate_technical_indicators(data)

    # 计算Cosmoon XYE指标
    print("🧮 计算Cosmoon XYE指标...")
    df = calculate_cosmoon_xye(df)

    # 生成交易信号
    print("🎯 生成交易信号...")
    df = generate_trading_signals(df)

    # 获取最新数据
    latest = df.iloc[-1]
    prev = df.iloc[-2] if len(df) > 1 else latest

    print(f"\n📈 腾讯控股 (0700.HK) 分析结果")
    print(f"📅 分析日期: {latest.name.strftime('%Y-%m-%d')}")
    print(f"💰 当前价格: {latest['Close']:.2f} 港元")
    print(f"📊 价格变化: {latest['Close'] - prev['Close']:+.2f} ({(latest['Close'] - prev['Close'])/prev['Close']*100:+.2f}%)")
    print(f"📈 今日区间: {latest['Low']:.2f} - {latest['High']:.2f} 港元")
    print(f"📊 成交量: {latest['Volume']:,.0f}")

    print(f"\n🧮 Cosmoon XYE技术指标:")
    print(f"   Y值 (价格强度): {latest['Y_Value']:.4f}")
    print(f"   Full_Y (行控制): {latest['Full_Y']:.4f}")
    print(f"   X值 (资金流强度): {latest['X_Value']:.4f}")
    print(f"   MoneyFlowRatio: {latest['MoneyFlowRatio']:.4f}")
    print(f"   E值 (综合能量): {latest['E_Value']:.4f}")
    print(f"   MyE (Cosmoon公式): {latest['MyE']:.4f}")

    print(f"\n📊 传统技术指标:")
    print(f"   RSI (14): {latest['RSI']:.2f}")
    print(f"   MFI (14): {latest['MFI']:.2f}")
    print(f"   MA5: {latest['MA5']:.2f}")
    print(f"   MA10: {latest['MA10']:.2f}")
    print(f"   MA20: {latest['MA20']:.2f}")

    print(f"\n🎯 交易信号分析:")
    print(f"   信号: {latest['TradingSignal']}")
    print(f"   强度: {latest['SignalStrength']}")

    # 信号解释
    signal = latest['TradingSignal']
    if pd.isna(signal):
        signal = "数据不足"

    if "买入" in str(signal):
        print(f"   📈 建议: 考虑买入机会")
        if latest['MyE'] > 0:
            print(f"   💡 理由: MyE为正值({latest['MyE']:.4f})，市场情绪偏多")
    elif "卖出" in str(signal):
        print(f"   📉 建议: 考虑卖出机会")
        if latest['MyE'] < 0:
            print(f"   💡 理由: MyE为负值({latest['MyE']:.4f})，市场情绪偏空")
    else:
        print(f"   ⚪ 建议: 保持观望")
        print(f"   💡 理由: 指标信号不明确，等待更好时机")

    # 风险提示
    print(f"\n⚠️ 风险评估:")
    if latest['RSI'] > 70:
        print(f"   🔴 RSI超买 ({latest['RSI']:.1f})，注意回调风险")
    elif latest['RSI'] < 30:
        print(f"   🟢 RSI超卖 ({latest['RSI']:.1f})，可能存在反弹机会")

    if latest['MFI'] > 70:
        print(f"   🔴 MFI超买 ({latest['MFI']:.1f})，资金流入过热")
    elif latest['MFI'] < 30:
        print(f"   🟢 MFI超卖 ({latest['MFI']:.1f})，资金流出过度")

    # 保存结果
    output_file = f"腾讯700HK分析_{datetime.now().strftime('%Y%m%d')}.csv"
    df_output = df[['Close', 'Volume', 'Y_Value', 'Full_Y', 'X_Value', 'MoneyFlowRatio',
                    'E_Value', 'MyE', 'RSI', 'MFI', 'TradingSignal', 'SignalStrength']].tail(30)
    df_output.to_csv(output_file)
    print(f"\n💾 分析结果已保存: {output_file}")

    return df

if __name__ == "__main__":
    result = analyze_tencent_700hk()
