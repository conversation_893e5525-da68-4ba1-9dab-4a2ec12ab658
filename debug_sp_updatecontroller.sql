-- 诊断sp_updatecontroller_enhanced问题的SQL脚本

-- 1. 检查存储过程是否存在
SELECT 
    ROUTINE_NAME,
    ROUTINE_TYPE,
    CREATED,
    LAST_ALTERED
FROM information_schema.ROUTINES 
WHERE ROUTINE_SCHEMA = DATABASE() 
AND ROUTINE_NAME = 'sp_updatecontroller_enhanced';

-- 2. 检查表结构
-- 请将 'your_table_name' 替换为实际的表名
DESCRIBE your_table_name;

-- 3. 检查表中是否有数据
SELECT COUNT(*) as total_records FROM your_table_name;

-- 4. 检查关键字段是否存在
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT
FROM information_schema.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'your_table_name'  -- 请替换为实际表名
AND COLUMN_NAME IN ('close', 'midprice', 'controller', 'Full_Y', 'E', 'MFI', 'date', 'id');

-- 5. 检查midprice是否有值
SELECT 
    COUNT(*) as total_records,
    COUNT(CASE WHEN midprice IS NOT NULL THEN 1 END) as midprice_not_null,
    COUNT(CASE WHEN close IS NOT NULL THEN 1 END) as close_not_null
FROM your_table_name;

-- 6. 检查sp_averagelineV3存储过程是否存在
SELECT 
    ROUTINE_NAME,
    ROUTINE_TYPE
FROM information_schema.ROUTINES 
WHERE ROUTINE_SCHEMA = DATABASE() 
AND ROUTINE_NAME = 'sp_averagelineV3';

-- 7. 手动测试controller更新
-- 请将 'your_table_name' 替换为实际的表名
SELECT 
    COUNT(*) as total_records,
    COUNT(CASE WHEN close < midprice THEN 1 END) as should_be_0,
    COUNT(CASE WHEN close > midprice THEN 1 END) as should_be_1,
    COUNT(CASE WHEN close = midprice THEN 1 END) as should_be_3,
    COUNT(CASE WHEN controller IS NOT NULL THEN 1 END) as controller_not_null
FROM your_table_name
WHERE midprice IS NOT NULL AND close IS NOT NULL;

-- 8. 检查是否有错误日志
SHOW WARNINGS;

-- 9. 测试简单的UPDATE语句
-- 请将 'your_table_name' 替换为实际的表名
-- UPDATE your_table_name SET controller = 999 WHERE id = 1;
-- SELECT controller FROM your_table_name WHERE id = 1;

-- 10. 检查MySQL版本和权限
SELECT VERSION() as mysql_version;
SELECT USER() as current_user;
SHOW GRANTS FOR CURRENT_USER();

-- 11. 检查表是否被锁定
SHOW OPEN TABLES WHERE In_use > 0;

-- 12. 检查事务状态
SELECT 
    trx_id,
    trx_state,
    trx_started,
    trx_isolation_level
FROM information_schema.INNODB_TRX;

-- 调试用的简化存储过程
DELIMITER $$

CREATE PROCEDURE `sp_debug_update`(IN tablename VARCHAR(64))
BEGIN
    DECLARE v_count INT DEFAULT 0;
    
    -- 检查表是否存在
    SET @sql = CONCAT(
        'SELECT COUNT(*) INTO @table_count FROM information_schema.TABLES ',
        'WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = ''', tablename, ''''
    );
    PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;
    
    IF @table_count = 0 THEN
        SELECT CONCAT('错误: 表 ', tablename, ' 不存在') AS error_message;
    ELSE
        SELECT CONCAT('表 ', tablename, ' 存在') AS success_message;
        
        -- 检查记录数
        SET @sql = CONCAT('SELECT COUNT(*) INTO @record_count FROM `', tablename, '`');
        PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;
        SELECT CONCAT('表中有 ', @record_count, ' 条记录') AS record_info;
        
        -- 尝试简单更新
        SET @sql = CONCAT('UPDATE `', tablename, '` SET controller = 999 LIMIT 1');
        PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;
        SELECT ROW_COUNT() AS updated_rows;
        
        -- 检查更新结果
        SET @sql = CONCAT('SELECT COUNT(*) INTO @updated_count FROM `', tablename, '` WHERE controller = 999');
        PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;
        SELECT CONCAT('更新了 ', @updated_count, ' 条记录') AS update_result;
    END IF;
    
END$$

DELIMITER ;
