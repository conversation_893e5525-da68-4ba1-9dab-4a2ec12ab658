#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
HSI50策略测试 - 使用新数据库的y_probability
==========================================
基于您的博弈论策略，测试HSI50的最新表现
"""

import mysql.connector
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import matplotlib.pyplot as plt
import warnings
warnings.filterwarnings('ignore')

class HSI50StrategyTester:
    def __init__(self):
        """初始化策略测试器"""
        self.db_config = {
            'host': 'localhost',
            'database': 'finance',
            'user': 'root',
            'password': '12345678',
            'charset': 'utf8mb4'
        }

        # 策略参数 (调整为更实用的版本)
        self.strategy_params = {
            'initial_capital': 30000,      # 初始资金30K港元
            'monthly_investment': 3000,    # 每月定投3K
            'min_expected_value': 0.05,    # 最小期望值5% (降低)
            'win_probability_threshold': 0.6,  # 胜率阈值60% (降低)
            'take_profit': 0.15,           # 止盈15% (降低)
            'stop_loss': 0.06,             # 止损6% (降低)
            'max_position_ratio': 0.1,     # 最大仓位10% (提高)
            'min_holding_days': 30,        # 最小持仓30天 (降低)
            'max_holding_days': 180,       # 最大持仓180天 (降低)
            'position_cooldown': 30,       # 冷却期30天 (降低)
            'deviation_threshold': 0.03,   # 偏离阈值3% (降低)
            'extreme_deviation': 0.05,     # 极端偏离5% (降低)
        }

        self.data = None
        self.trades = []
        self.current_positions = []
        self.daily_capital = []

    def load_hsi50_data(self):
        """从新数据库加载HSI50数据"""
        try:
            print("📊 从新数据库加载HSI50数据...")

            connection = mysql.connector.connect(**self.db_config)

            # 加载完整的HSI50数据，包括新的y_probability
            query = """
                SELECT Date, Open, High, Low, Close, Volume,
                       ma_20, ma_60, y_probability,
                       new_midprice, new_controller, new_Full_Y
                FROM hkhsi50
                WHERE Date >= '2020-01-01'  -- 最近5年数据
                AND y_probability IS NOT NULL
                ORDER BY Date ASC
            """

            self.data = pd.read_sql(query, connection)
            connection.close()

            # 数据预处理
            self.data['Date'] = pd.to_datetime(self.data['Date'])
            self.data.set_index('Date', inplace=True)

            print(f"✅ 成功加载 {len(self.data)} 条HSI50数据")
            print(f"📅 数据范围: {self.data.index.min().strftime('%Y-%m-%d')} 到 {self.data.index.max().strftime('%Y-%m-%d')}")

            # 显示数据概览
            print(f"\n📊 数据概览:")
            print(f"   • 收盘价范围: {self.data['Close'].min():.2f} ~ {self.data['Close'].max():.2f}")
            print(f"   • y_probability范围: {self.data['y_probability'].min():.4f} ~ {self.data['y_probability'].max():.4f}")
            print(f"   • 平均y_probability: {self.data['y_probability'].mean():.4f}")
            print(f"   • 强势比例(new_controller=1): {(self.data['new_controller']==1).mean()*100:.2f}%")

            return True

        except Exception as e:
            print(f"❌ 加载数据失败: {e}")
            return False

    def calculate_technical_indicators(self):
        """计算技术指标"""
        try:
            print("📈 计算技术指标...")

            # 价格变化
            self.data['price_change'] = self.data['Close'].pct_change()

            # 价格相对于移动平均线的位置
            self.data['price_vs_ma20'] = self.data['Close'] / self.data['ma_20']
            self.data['price_vs_ma60'] = self.data['Close'] / self.data['ma_60']

            # 趋势判断
            self.data['uptrend'] = (self.data['ma_20'] > self.data['ma_60']) & (self.data['Close'] > self.data['ma_20'])
            self.data['downtrend'] = (self.data['ma_20'] < self.data['ma_60']) & (self.data['Close'] < self.data['ma_20'])

            # 价格偏离度 (相对于new_midprice)
            self.data['price_deviation'] = (self.data['Close'] - self.data['new_midprice']) / self.data['new_midprice']

            # 市场情绪 (基于y_probability)
            self.data['market_sentiment'] = np.where(self.data['y_probability'] > 0.5, 'BULLISH', 'BEARISH')

            # 极端偏离
            self.data['extreme_high'] = self.data['price_deviation'] > self.strategy_params['extreme_deviation']
            self.data['extreme_low'] = self.data['price_deviation'] < -self.strategy_params['extreme_deviation']

            print("✅ 技术指标计算完成")
            return True

        except Exception as e:
            print(f"❌ 计算技术指标失败: {e}")
            return False

    def calculate_game_theory_expected_value(self, row):
        """计算博弈论期望值"""
        deviation = row['price_deviation']
        y_prob = row['y_probability']

        # 基础胜率计算
        if abs(deviation) < self.strategy_params['deviation_threshold']:
            base_win_prob = 0.5
        elif deviation > self.strategy_params['extreme_deviation']:
            # 价格极高，买跌的胜率
            base_win_prob = 0.7 + min(0.2, abs(deviation) - self.strategy_params['extreme_deviation']) * 10
        elif deviation < -self.strategy_params['extreme_deviation']:
            # 价格极低，买涨的胜率
            base_win_prob = 0.7 + min(0.2, abs(deviation) - self.strategy_params['extreme_deviation']) * 10
        else:
            base_win_prob = 0.5 + abs(deviation) * 5

        # y_probability调整
        y_adjustment = (y_prob - 0.5) * 0.3

        # 最终胜率
        win_probability = np.clip(base_win_prob + y_adjustment, 0.1, 0.95)

        # 期望收益率
        if deviation > 0:
            # 价格偏高，预期下跌
            expected_return = abs(deviation) * 2
        else:
            # 价格偏低，预期上涨
            expected_return = abs(deviation) * 2

        # 期望值计算: E = p*W - (1-p)*L
        expected_value = win_probability * expected_return - (1 - win_probability) * self.strategy_params['stop_loss']

        return expected_value, win_probability

    def get_trading_signal(self, row):
        """获取交易信号"""
        expected_value, win_probability = self.calculate_game_theory_expected_value(row)

        # 检查是否满足交易条件
        if (expected_value >= self.strategy_params['min_expected_value'] and
            win_probability >= self.strategy_params['win_probability_threshold']):

            deviation = row['price_deviation']

            if deviation > self.strategy_params['extreme_deviation']:
                return 'SHORT', expected_value, win_probability  # 买跌
            elif deviation < -self.strategy_params['extreme_deviation']:
                return 'LONG', expected_value, win_probability   # 买涨

        return 'HOLD', expected_value, win_probability

    def run_backtest(self):
        """运行回测"""
        try:
            print("\n🎯 开始运行HSI50策略回测...")

            current_capital = self.strategy_params['initial_capital']
            last_trade_date = None
            monthly_investment_date = None

            for date, row in self.data.iterrows():
                # 每月定投
                if monthly_investment_date is None or (date - monthly_investment_date).days >= 30:
                    current_capital += self.strategy_params['monthly_investment']
                    monthly_investment_date = date

                # 检查现有持仓
                positions_to_close = []
                for i, position in enumerate(self.current_positions):
                    holding_days = (date - position['entry_date']).days
                    current_price = row['Close']

                    # 计算盈亏
                    if position['direction'] == 'LONG':
                        pnl_ratio = (current_price - position['entry_price']) / position['entry_price']
                    else:  # SHORT
                        pnl_ratio = (position['entry_price'] - current_price) / position['entry_price']

                    # 平仓条件
                    should_close = False
                    close_reason = ""

                    if pnl_ratio >= self.strategy_params['take_profit']:
                        should_close = True
                        close_reason = "止盈"
                    elif pnl_ratio <= -self.strategy_params['stop_loss']:
                        should_close = True
                        close_reason = "止损"
                    elif holding_days >= self.strategy_params['max_holding_days']:
                        should_close = True
                        close_reason = "到期"

                    if should_close:
                        # 平仓
                        pnl = position['amount'] * pnl_ratio
                        current_capital += position['amount'] + pnl

                        trade_record = {
                            'entry_date': position['entry_date'],
                            'exit_date': date,
                            'direction': position['direction'],
                            'entry_price': position['entry_price'],
                            'exit_price': current_price,
                            'amount': position['amount'],
                            'pnl': pnl,
                            'pnl_ratio': pnl_ratio,
                            'holding_days': holding_days,
                            'close_reason': close_reason,
                            'expected_value': position['expected_value'],
                            'win_probability': position['win_probability']
                        }

                        self.trades.append(trade_record)
                        positions_to_close.append(i)

                # 移除已平仓的持仓
                for i in sorted(positions_to_close, reverse=True):
                    del self.current_positions[i]

                # 检查开仓条件
                if len(self.current_positions) == 0:  # 只允许一个持仓
                    # 检查冷却期
                    if last_trade_date is None or (date - last_trade_date).days >= self.strategy_params['position_cooldown']:

                        signal, expected_value, win_probability = self.get_trading_signal(row)

                        if signal in ['LONG', 'SHORT']:
                            # 计算仓位大小
                            position_size = current_capital * self.strategy_params['max_position_ratio']

                            # 开仓
                            position = {
                                'entry_date': date,
                                'direction': signal,
                                'entry_price': row['Close'],
                                'amount': position_size,
                                'expected_value': expected_value,
                                'win_probability': win_probability
                            }

                            self.current_positions.append(position)
                            current_capital -= position_size
                            last_trade_date = date

                # 记录每日资金
                total_value = current_capital
                for position in self.current_positions:
                    if position['direction'] == 'LONG':
                        pnl_ratio = (row['Close'] - position['entry_price']) / position['entry_price']
                    else:
                        pnl_ratio = (position['entry_price'] - row['Close']) / position['entry_price']
                    total_value += position['amount'] * (1 + pnl_ratio)

                self.daily_capital.append({
                    'date': date,
                    'capital': total_value,
                    'positions': len(self.current_positions)
                })

            print(f"✅ 回测完成！")
            print(f"📊 总交易次数: {len(self.trades)}")
            print(f"💰 最终资金: {total_value:,.2f} 港元")

            return True

        except Exception as e:
            print(f"❌ 回测失败: {e}")
            return False

    def analyze_results(self):
        """分析回测结果"""
        try:
            print("\n📊 分析回测结果...")

            if not self.trades:
                print("⚠️ 没有交易记录")
                return

            trades_df = pd.DataFrame(self.trades)
            capital_df = pd.DataFrame(self.daily_capital)

            # 基本统计
            total_trades = len(trades_df)
            winning_trades = len(trades_df[trades_df['pnl'] > 0])
            losing_trades = len(trades_df[trades_df['pnl'] <= 0])
            win_rate = winning_trades / total_trades if total_trades > 0 else 0

            total_pnl = trades_df['pnl'].sum()
            avg_pnl = trades_df['pnl'].mean()
            max_win = trades_df['pnl'].max()
            max_loss = trades_df['pnl'].min()

            initial_capital = self.strategy_params['initial_capital']
            final_capital = capital_df['capital'].iloc[-1]
            total_return = (final_capital - initial_capital) / initial_capital

            # 计算总投入 (包括定投)
            months = len(capital_df) / 30  # 大概月数
            total_invested = initial_capital + months * self.strategy_params['monthly_investment']

            print(f"\n🎯 策略表现总结:")
            print(f"=" * 50)
            print(f"💰 初始资金: {initial_capital:,.2f} 港元")
            print(f"💰 总投入: {total_invested:,.2f} 港元")
            print(f"💰 最终资金: {final_capital:,.2f} 港元")
            print(f"📈 总收益: {total_pnl:,.2f} 港元")
            print(f"📊 总收益率: {total_return*100:.2f}%")
            print(f"📊 年化收益率: {(total_return/5)*100:.2f}%")

            print(f"\n📊 交易统计:")
            print(f"🔢 总交易次数: {total_trades}")
            print(f"✅ 盈利交易: {winning_trades} ({win_rate*100:.1f}%)")
            print(f"❌ 亏损交易: {losing_trades}")
            print(f"💰 平均盈亏: {avg_pnl:,.2f} 港元")
            print(f"🎯 最大盈利: {max_win:,.2f} 港元")
            print(f"💔 最大亏损: {max_loss:,.2f} 港元")

            # 分析交易方向
            long_trades = trades_df[trades_df['direction'] == 'LONG']
            short_trades = trades_df[trades_df['direction'] == 'SHORT']

            if len(long_trades) > 0:
                long_win_rate = len(long_trades[long_trades['pnl'] > 0]) / len(long_trades)
                long_avg_pnl = long_trades['pnl'].mean()
                print(f"\n📈 多头交易:")
                print(f"   🔢 次数: {len(long_trades)}")
                print(f"   ✅ 胜率: {long_win_rate*100:.1f}%")
                print(f"   💰 平均盈亏: {long_avg_pnl:,.2f} 港元")

            if len(short_trades) > 0:
                short_win_rate = len(short_trades[short_trades['pnl'] > 0]) / len(short_trades)
                short_avg_pnl = short_trades['pnl'].mean()
                print(f"\n📉 空头交易:")
                print(f"   🔢 次数: {len(short_trades)}")
                print(f"   ✅ 胜率: {short_win_rate*100:.1f}%")
                print(f"   💰 平均盈亏: {short_avg_pnl:,.2f} 港元")

            # 显示最近的交易
            print(f"\n📋 最近5笔交易:")
            recent_trades = trades_df.tail(5)
            for _, trade in recent_trades.iterrows():
                direction_emoji = "📈" if trade['direction'] == 'LONG' else "📉"
                pnl_emoji = "💰" if trade['pnl'] > 0 else "💔"
                print(f"   {direction_emoji} {trade['entry_date'].strftime('%Y-%m-%d')} ~ {trade['exit_date'].strftime('%Y-%m-%d')}")
                print(f"      价格: {trade['entry_price']:.2f} → {trade['exit_price']:.2f}")
                print(f"      {pnl_emoji} 盈亏: {trade['pnl']:,.2f} 港元 ({trade['pnl_ratio']*100:+.2f}%)")
                print(f"      原因: {trade['close_reason']}, 持仓: {trade['holding_days']}天")

            return True

        except Exception as e:
            print(f"❌ 分析结果失败: {e}")
            return False

    def run(self):
        """运行完整的策略测试"""
        print("🎯 HSI50策略测试 - 使用新数据库的y_probability")
        print("=" * 70)
        print(f"📅 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"🏠 数据库: {self.db_config['host']}/{self.db_config['database']}")
        print(f"📊 测试标的: HSI50 (恒生指数50)")

        print(f"\n💡 策略参数:")
        print(f"   💰 初始资金: {self.strategy_params['initial_capital']:,} 港元")
        print(f"   📅 每月定投: {self.strategy_params['monthly_investment']:,} 港元")
        print(f"   🎯 期望值阈值: {self.strategy_params['min_expected_value']*100}%")
        print(f"   🎲 胜率阈值: {self.strategy_params['win_probability_threshold']*100}%")
        print(f"   📈 止盈: {self.strategy_params['take_profit']*100}%")
        print(f"   📉 止损: {self.strategy_params['stop_loss']*100}%")

        try:
            # 1. 加载数据
            if not self.load_hsi50_data():
                return False

            # 2. 计算技术指标
            if not self.calculate_technical_indicators():
                return False

            # 3. 运行回测
            if not self.run_backtest():
                return False

            # 4. 分析结果
            if not self.analyze_results():
                return False

            print("\n🎉 HSI50策略测试完成!")
            print("💡 基于您的博弈论策略和新的y_probability数据")
            print("📊 策略表现已详细分析，可用于实盘参考")

            return True

        except Exception as e:
            print(f"❌ 策略测试失败: {e}")
            return False

def main():
    """主函数"""
    tester = HSI50StrategyTester()
    success = tester.run()

    if success:
        print("\n✅ 测试完成!")
        print("📝 您的HSI50策略表现已分析完毕")
    else:
        print("\n❌ 测试失败!")

if __name__ == "__main__":
    main()
