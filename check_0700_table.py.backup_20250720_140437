#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查腾讯0700表结构
================

检查数据库中 stock_0700_hk 表的结构和数据

作者: Cosmoon NG
日期: 2025年7月16日
"""

import pymysql
import pandas as pd

# 数据库配置
db_config = {
    'host': '************',
    'user': 'root',
    'password': '',
    'database': 'finance',
    'charset': 'utf8mb4'
}

def check_table_structure():
    """检查表结构"""
    print("🔍 检查腾讯0700表结构")
    print("=" * 60)
    
    try:
        conn = pymysql.connect(**db_config)
        cursor = conn.cursor()
        
        # 1. 检查表是否存在
        cursor.execute("""
            SELECT COUNT(*) FROM information_schema.tables 
            WHERE table_schema = 'finance' AND table_name = 'stock_0700_hk'
        """)
        
        if cursor.fetchone()[0] == 0:
            print("❌ 表 stock_0700_hk 不存在")
            
            # 查找相关表
            cursor.execute("""
                SELECT table_name FROM information_schema.tables 
                WHERE table_schema = 'finance' AND table_name LIKE '%0700%'
            """)
            
            related_tables = cursor.fetchall()
            if related_tables:
                print("📋 找到相关表:")
                for table in related_tables:
                    print(f"   • {table[0]}")
            else:
                print("❌ 未找到任何0700相关表")
            
            conn.close()
            return False
        
        print("✅ 找到表 stock_0700_hk")
        
        # 2. 查看表结构
        cursor.execute("DESCRIBE stock_0700_hk")
        columns = cursor.fetchall()
        
        print(f"\n📊 表结构:")
        print(f"{'字段名':<20} {'类型':<15} {'是否为空':<10} {'键':<10} {'默认值':<15}")
        print("-" * 80)
        for col in columns:
            field, type_info, null, key, default, extra = col
            print(f"{field:<20} {type_info:<15} {null:<10} {key:<10} {str(default):<15}")
        
        # 3. 查看数据量
        cursor.execute("SELECT COUNT(*) FROM stock_0700_hk")
        record_count = cursor.fetchone()[0]
        print(f"\n📈 数据量: {record_count:,} 条记录")
        
        # 4. 查看日期范围
        cursor.execute("SELECT MIN(Date), MAX(Date) FROM stock_0700_hk")
        date_range = cursor.fetchone()
        print(f"📅 日期范围: {date_range[0]} 至 {date_range[1]}")
        
        # 5. 查看前几条记录
        cursor.execute("SELECT * FROM stock_0700_hk ORDER BY Date DESC LIMIT 5")
        recent_data = cursor.fetchall()
        
        print(f"\n📋 最新5条记录:")
        column_names = [desc[0] for desc in cursor.description]
        df = pd.DataFrame(recent_data, columns=column_names)
        print(df.to_string(index=False))
        
        # 6. 检查是否有计算字段
        available_columns = [col[0] for col in columns]
        required_fields = ['MoneyFlowRatio', 'Full_Y', 'E', 'Controller']
        missing_fields = [field for field in required_fields if field not in available_columns]
        
        if missing_fields:
            print(f"\n⚠️  缺少字段: {missing_fields}")
            print("💡 需要运行存储过程计算这些字段")
        else:
            print(f"\n✅ 所有必需字段都存在")
        
        conn.close()
        return True, available_columns
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False, []

def check_stored_procedures():
    """检查相关存储过程"""
    print(f"\n🔧 检查相关存储过程")
    print("-" * 40)
    
    try:
        conn = pymysql.connect(**db_config)
        cursor = conn.cursor()
        
        # 查找股票相关存储过程
        cursor.execute("""
            SELECT ROUTINE_NAME, ROUTINE_COMMENT 
            FROM information_schema.ROUTINES
            WHERE ROUTINE_SCHEMA = 'finance'
            AND (ROUTINE_NAME LIKE '%stock%' OR ROUTINE_NAME LIKE '%0700%')
        """)
        
        procedures = cursor.fetchall()
        
        if procedures:
            print("📋 找到相关存储过程:")
            for proc_name, comment in procedures:
                print(f"   • {proc_name}: {comment}")
        else:
            print("❌ 未找到相关存储过程")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 检查存储过程失败: {e}")

def suggest_solution(available_columns):
    """建议解决方案"""
    print(f"\n💡 解决方案建议")
    print("-" * 40)
    
    required_fields = ['MoneyFlowRatio', 'Full_Y', 'E', 'Controller']
    missing_fields = [field for field in required_fields if field not in available_columns]
    
    if not missing_fields:
        print("✅ 所有字段都存在，可以直接运行回测")
        return
    
    print(f"⚠️  缺少字段: {', '.join(missing_fields)}")
    
    if 'MoneyFlowRatio' in missing_fields:
        print("\n📊 MoneyFlowRatio 计算方法:")
        print("   • 基于成交量和价格变化")
        print("   • 公式: 资金流入 / (资金流入 + 资金流出)")
    
    if 'Full_Y' in missing_fields:
        print("\n📈 Full_Y 计算方法:")
        print("   • 控制系数，基于RSI和价格动量")
        print("   • 需要14日RSI和10日价格动量")
    
    if 'E' in missing_fields:
        print("\n🎯 E值 计算方法:")
        print("   • 博弈论期望值")
        print("   • 公式: E = 8*X*Y - 3*X - 3*Y + 1")
    
    if 'Controller' in missing_fields:
        print("\n🏢 Controller 计算方法:")
        print("   • 控股商标识")
        print("   • 基于价格相对中位数的位置")
    
    print(f"\n🔧 建议操作:")
    print(f"1. 运行存储过程计算缺失字段")
    print(f"2. 或者修改回测代码，在代码中计算这些指标")
    print(f"3. 或者使用基础字段 (Date, Open, High, Low, Close, Volume) 进行回测")

def main():
    """主函数"""
    success, available_columns = check_table_structure()
    
    if success:
        check_stored_procedures()
        suggest_solution(available_columns)
    
    print(f"\n" + "=" * 60)
    print("🔍 表结构检查完成")

if __name__ == "__main__":
    main()
