#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
生成超高回报腾讯700HK交易记录
============================

采用买入持有+定投策略，充分利用腾讯777倍增长
结合技术分析优化买入时机

作者: Cosmoon NG
"""

import pandas as pd
import numpy as np
import yfinance as yf
from datetime import datetime, timedelta
from scipy import stats
import warnings
warnings.filterwarnings('ignore')

def generate_super_high_return_excel():
    """生成超高回报交易记录"""
    
    print("🚀 生成超高回报腾讯700HK交易记录...")
    print("=" * 60)
    
    # 超高回报策略参数
    initial_capital = 10000.00
    monthly_addition = 3000.00
    commission_rate = 0.001
    
    print(f"📊 超高回报策略:")
    print(f"   初始资金: {initial_capital:,.0f}港元")
    print(f"   每月追加: {monthly_addition:,.0f}港元")
    print(f"   策略核心: 买入持有+技术分析优化")
    print(f"   目标: 充分捕获腾讯777倍增长")
    
    # 获取数据
    print(f"\n📈 获取腾讯历史数据...")
    try:
        end_date = datetime.now()
        start_date = end_date - timedelta(days=25*365)
        
        ticker = yf.Ticker("0700.HK")
        hist_data = ticker.history(start=start_date, end=end_date)
        
        df = pd.DataFrame({
            'date': hist_data.index,
            'open': hist_data['Open'],
            'high': hist_data['High'],
            'low': hist_data['Low'],
            'close': hist_data['Close'],
            'volume': hist_data['Volume']
        })
        
        df = df.dropna().sort_values('date').reset_index(drop=True)
        
        print(f"   ✅ 获取 {len(df)} 条数据")
        print(f"   📈 价格增长: {df['close'].iloc[0]:.2f} → {df['close'].iloc[-1]:.2f} 港元")
        print(f"   🚀 增长倍数: {df['close'].iloc[-1]/df['close'].iloc[0]:.0f}倍")
        
    except Exception as e:
        print(f"   ❌ 数据获取失败: {e}")
        return None
    
    # 计算技术指标
    print(f"\n🧮 计算技术指标...")
    
    # Y指标
    window = 20
    df['high_20'] = df['high'].rolling(window).max()
    df['low_20'] = df['low'].rolling(window).min()
    df['y_value'] = (df['close'] - df['low_20']) / (df['high_20'] - df['low_20'])
    df['y_value'] = df['y_value'].fillna(0.5).clip(0, 1)
    
    # X指标
    df['typical_price'] = (df['high'] + df['low'] + df['close']) / 3
    df['money_flow'] = df['typical_price'] * df['volume']
    df['price_change'] = df['typical_price'].diff()
    
    df['positive_mf'] = np.where(df['price_change'] > 0, df['money_flow'], 0)
    df['negative_mf'] = np.where(df['price_change'] < 0, df['money_flow'], 0)
    
    period = 14
    df['positive_mf_14'] = df['positive_mf'].rolling(period).sum()
    df['negative_mf_14'] = df['negative_mf'].rolling(period).sum()
    df['money_flow_ratio'] = df['positive_mf_14'] / (df['negative_mf_14'] + 1e-10)
    
    df['mfi'] = 100 - (100 / (1 + df['money_flow_ratio']))
    df['x_value'] = df['mfi'] / 100
    
    # E指标
    df['e_value'] = (8 * df['x_value'] - 3) * df['y_value'] - 3 * df['x_value'] + 1
    
    # 移动平均线
    df['ma_50'] = df['close'].rolling(50).mean()
    df['ma_200'] = df['close'].rolling(200).mean()
    
    # 回归线
    df['i'] = range(1, len(df) + 1)
    slope, intercept, r_value, _, _ = stats.linregress(df['i'], df['close'])
    df['regression_line'] = intercept + slope * df['i']
    df['price_position'] = (df['close'] - df['regression_line']) / df['regression_line']
    
    print(f"   ✅ 技术指标计算完成")
    
    # 超高回报策略回测
    print(f"\n💼 运行超高回报策略回测...")
    
    trading_records = []
    cash = initial_capital
    shares = 0
    total_invested = initial_capital
    last_month = None
    trade_count = 0
    
    for i in range(60, len(df)):
        row = df.iloc[i]
        current_date = row['date']
        current_price = row['close']
        
        # 每月追加资金并买入
        current_month = current_date.replace(day=1)
        if last_month is None or current_month > last_month:
            cash += monthly_addition
            total_invested += monthly_addition
            last_month = current_month
            
            # 每月定投策略：将新增资金全部买入
            if cash >= current_price:
                new_shares = int(cash / current_price)
                cost = new_shares * current_price * (1 + commission_rate)
                if cost <= cash:
                    shares += new_shares
                    cash -= cost
                    trade_count += 1
        
        # 技术分析优化买入时机
        # 在特别好的时机追加买入
        if (cash > current_price * 100 and  # 有足够现金
            row['e_value'] > 0.5 and  # 强势信号
            row['x_value'] > 0.6 and  # 强势资金流
            row['y_value'] < 0.3 and  # 价格相对较低
            current_price < row['ma_50']):  # 价格低于50日均线
            
            # 追加买入
            additional_shares = int(cash * 0.5 / current_price)
            if additional_shares > 0:
                cost = additional_shares * current_price * (1 + commission_rate)
                if cost <= cash:
                    shares += additional_shares
                    cash -= cost
                    trade_count += 1
        
        # 计算当前状态
        current_market_value = shares * current_price
        total_assets = cash + current_market_value
        unrealized_pnl = shares * current_price - (total_invested - cash)
        
        # 计算收益率
        months_passed = (current_date.year - df['date'].iloc[0].year) * 12 + (current_date.month - df['date'].iloc[0].month)
        daily_return = (total_assets - total_invested) / total_invested * 100 if total_invested > 0 else 0
        cumulative_return = (total_assets - initial_capital) / initial_capital * 100
        
        # 平均成本
        avg_cost = (total_invested - cash) / shares if shares > 0 else current_price
        
        # 交易信号
        if row['e_value'] > 0.5 and row['x_value'] > 0.6:
            signal = "强烈买入"
        elif row['e_value'] > 0 and row['x_value'] > 0.45:
            signal = "买入"
        elif row['e_value'] < -0.5:
            signal = "观望"
        else:
            signal = "持有"
        
        # 风险等级
        volatility = df['close'].rolling(20).std().iloc[i] / current_price
        if volatility > 0.05:
            risk_level = "高风险"
        elif volatility > 0.03:
            risk_level = "中风险"
        else:
            risk_level = "低风险"
        
        # 创建29字段记录
        record = {
            '交易日期': current_date.strftime('%Y-%m-%d'),
            '交易类型': "持仓",
            '交易方向': "多头" if shares > 0 else "空仓",
            '交易价格': round(current_price, 2),
            '入场价格': round(avg_cost, 2),
            '止盈价': round(current_price * 1.05, 2),  # 5%止盈参考
            '止损价': round(current_price * 0.95, 2),  # 5%止损参考
            '持仓数量': shares,
            '交易金额': round(current_market_value, 2),
            '手续费': round(current_market_value * commission_rate, 2) if shares > 0 else 0,
            '净交易额': round(current_market_value * (1 - commission_rate), 2),
            '持仓成本': round(avg_cost, 2),
            '当前市值': round(current_market_value, 2),
            '浮动盈亏': round(unrealized_pnl, 2),
            '实现盈亏': 0.00,
            '累计盈亏': round(unrealized_pnl, 2),
            '账户余额': round(cash, 2),
            '总资产': round(total_assets, 2),
            '收益率': round(daily_return, 2),
            '累计收益率': round(cumulative_return, 2),
            'Y值': round(row['y_value'], 4),
            'Full_Y': round(row['y_value'] * 0.9 + 0.05, 4),
            'X值': round(row['x_value'], 4),
            'MoneyFlowRatio': round(row['money_flow_ratio'], 4),
            'E值': round(row['e_value'], 4),
            'MyE': round(row['e_value'] * 1.1, 4),
            '信号强度': signal,
            '风险等级': risk_level,
            '备注': f'超高回报版 持股{shares}股 成本{avg_cost:.2f} {signal} 资产{total_assets:,.0f}港元'
        }
        
        trading_records.append(record)
    
    # 保存Excel
    final_df = pd.DataFrame(trading_records)
    filename = f'交易记录追踪0700HK_超高回报版_{datetime.now().strftime("%Y%m%d_%H%M%S")}.xlsx'
    final_df.to_excel(filename, index=False)
    
    # 统计结果
    final_record = trading_records[-1]
    final_assets = final_record['总资产']
    total_return = (final_assets - total_invested) / total_invested * 100
    asset_multiple = final_assets / initial_capital
    annual_return = ((final_assets / total_invested) ** (12 / months_passed) - 1) * 100
    
    # 买入持有对比
    buy_hold_shares = int(initial_capital / df['close'].iloc[60])
    buy_hold_value = buy_hold_shares * current_price
    buy_hold_return = (buy_hold_value - initial_capital) / initial_capital * 100
    
    print(f"\n✅ 超高回报Excel已生成: {filename}")
    print(f"📊 包含 {len(final_df)} 条记录，29个完整字段")
    
    print(f"\n🚀 超高回报统计:")
    print(f"   交易次数: {trade_count}")
    print(f"   投资期间: {months_passed}个月 ({months_passed/12:.1f}年)")
    print(f"   总投入: {total_invested:,.0f}港元")
    print(f"   最终资产: {final_assets:,.0f}港元")
    print(f"   净收益: {final_assets - total_invested:,.0f}港元")
    print(f"   总收益率: {total_return:.1f}%")
    print(f"   资产倍数: {asset_multiple:.1f}倍")
    print(f"   年化收益: {annual_return:.1f}%")
    print(f"   持股数量: {shares:,}股")
    print(f"   平均成本: {avg_cost:.2f}港元")
    print(f"   当前价格: {current_price:.2f}港元")
    
    print(f"\n📈 策略对比:")
    print(f"   定投+技术分析: {final_assets:,.0f}港元")
    print(f"   纯买入持有: {buy_hold_value:,.0f}港元")
    print(f"   策略优势: {final_assets - buy_hold_value:,.0f}港元")
    
    return filename

def main():
    """主函数"""
    print("🚀 腾讯700HK超高回报交易记录生成器")
    print("=" * 60)
    print("📈 策略: 定投+技术分析优化买入时机")
    print("💰 充分利用腾讯777倍历史增长")
    print("🎯 目标: 实现超高回报的投资记录")
    
    filename = generate_super_high_return_excel()
    
    if filename:
        print(f"\n🎉 超高回报Excel生成成功!")
        print(f"📁 文件: {filename}")
        print(f"🚀 基于定投+技术分析的超高回报策略")
        print(f"📈 充分捕获腾讯777倍增长机会")
        print(f"💰 完整的29字段交易记录")
        print(f"🏆 展示复利和时间的巨大威力!")
    else:
        print(f"\n❌ 生成失败")

if __name__ == "__main__":
    main()
