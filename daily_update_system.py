#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
每日更新系统 - 0023.HK
1. 更新MySQL数据库 (finance.eab_0023hk)
2. 更新Excel交易记录
"""

import mysql.connector
import pandas as pd
import yfinance as yf
import numpy as np
from datetime import datetime

def update_mysql_database():
    """更新MySQL数据库"""
    print("📊 更新MySQL数据库...")
    
    config = {
        'host': 'localhost',
        'port': 3306,
        'user': 'root',
        'password': '12345678',
        'database': 'finance'
    }
    
    try:
        connection = mysql.connector.connect(**config)
        cursor = connection.cursor()
        
        print("✅ 连接到finance数据库成功")
        print("🔄 执行存储过程: sp_combined_stock_analysis('eab_0023hk')")
        
        cursor.callproc('sp_combined_stock_analysis', ['eab_0023hk'])
        connection.commit()
        
        print("✅ 存储过程执行成功")
        
        cursor.close()
        connection.close()
        
        return True
        
    except Exception as e:
        print(f"❌ MySQL更新失败: {e}")
        return False

def get_real_market_data():
    """获取真实市场数据并计算XYE"""
    print("📈 获取0023.HK真实市场数据...")
    
    try:
        ticker = yf.Ticker("0023.HK")
        hist = ticker.history(period="60d")
        
        if hist.empty:
            return None
        
        # 计算Y值
        window = 20
        hist['high_20'] = hist['High'].rolling(window).max()
        hist['low_20'] = hist['Low'].rolling(window).min()
        hist['y_value'] = (hist['Close'] - hist['low_20']) / (hist['high_20'] - hist['low_20'])
        hist['y_value'] = hist['y_value'].fillna(0.5).clip(0, 1)
        
        # 计算X值
        hist['typical_price'] = (hist['High'] + hist['Low'] + hist['Close']) / 3
        hist['money_flow'] = hist['typical_price'] * hist['Volume']
        hist['price_change'] = hist['typical_price'].diff()
        
        hist['positive_mf'] = np.where(hist['price_change'] > 0, hist['money_flow'], 0)
        hist['negative_mf'] = np.where(hist['price_change'] < 0, hist['money_flow'], 0)
        
        period = 14
        hist['positive_mf_14'] = hist['positive_mf'].rolling(period).sum()
        hist['negative_mf_14'] = hist['negative_mf'].rolling(period).sum()
        hist['money_flow_ratio'] = hist['positive_mf_14'] / (hist['negative_mf_14'] + 1e-10)
        
        hist['mfi'] = 100 - (100 / (1 + hist['money_flow_ratio']))
        hist['x_value'] = hist['mfi'] / 100
        
        # 计算E值
        hist['e_value'] = (8 * hist['x_value'] - 3) * hist['y_value'] - 3 * hist['x_value'] + 1
        
        # 获取最新数据
        latest = hist.iloc[-1]
        
        return {
            'date': latest.name.date(),
            'close': latest['Close'],
            'volume': latest['Volume'],
            'y_value': latest['y_value'],
            'x_value': latest['x_value'],
            'e_value': latest['e_value'],
            'mfi': latest['mfi']
        }
        
    except Exception as e:
        print(f"❌ 获取市场数据失败: {e}")
        return None

def generate_trading_signal(data):
    """生成交易信号"""
    y = data['y_value']
    x = data['x_value']
    e = data['e_value']
    
    if e > 0 and x > 0.45 and y > 0.45:
        return "强烈买入", "🟢"
    elif y < 0.3 or x < 0.3:
        return "强烈卖出", "🔴"
    else:
        return "观望", "⚪"

def update_excel_record(market_data, mysql_success):
    """更新Excel记录"""
    print("📝 更新Excel交易记录...")
    
    excel_file = "交易记录追踪0023HK.xlsx"
    
    try:
        # 加载现有记录
        if os.path.exists(excel_file):
            df = pd.read_excel(excel_file)
        else:
            df = pd.DataFrame()
        
        # 生成交易信号
        signal, signal_icon = generate_trading_signal(market_data)
        
        # 创建今日记录
        record = {
            '交易日期': datetime.now().strftime('%Y-%m-%d'),
            '交易类型': '观察',
            '交易方向': '无',
            '交易价格': market_data['close'],
            '持仓数量': 0,
            '交易金额': 0.00,
            '手续费': 0.00,
            '净交易额': 0.00,
            '持仓成本': 0.00,
            '当前市值': 0.00,
            '浮动盈亏': 0.00,
            '实现盈亏': 0.00,
            '累计盈亏': 0.00,
            '账户余额': 10000.00,
            '总资产': 10000.00,
            '收益率': 0.00,
            '累计收益率': 0.00,
            'Y值': market_data['y_value'],
            'X值': market_data['x_value'],
            'E值': market_data['e_value'],
            '信号强度': signal,
            '风险等级': '中风险',
            '备注': f"MySQL{'已更新' if mysql_success else '更新失败'}收盘价{market_data['close']:.2f}港元MFI{market_data['mfi']:.1f}"
        }
        
        # 添加记录
        new_df = pd.concat([df, pd.DataFrame([record])], ignore_index=True)
        new_df.to_excel(excel_file, index=False)
        
        print(f"✅ Excel记录已更新: {excel_file}")
        return True
        
    except Exception as e:
        print(f"❌ Excel更新失败: {e}")
        return False

def main():
    """主函数"""
    print("🕔 香港交易所交易时间结束 下午5:00")
    print("📊 开始每日更新流程...")
    print("=" * 60)
    
    # 1. 更新MySQL数据库
    mysql_success = update_mysql_database()
    
    # 2. 获取市场数据
    market_data = get_real_market_data()
    
    if market_data:
        print(f"📈 今日数据:")
        print(f"   收盘价: {market_data['close']:.2f} 港元")
        print(f"   Y值: {market_data['y_value']:.4f}")
        print(f"   X值: {market_data['x_value']:.4f}")
        print(f"   E值: {market_data['e_value']:.4f}")
        
        signal, signal_icon = generate_trading_signal(market_data)
        print(f"   交易信号: {signal_icon} {signal}")
        
        # 3. 更新Excel记录
        excel_success = update_excel_record(market_data, mysql_success)
        
        print("\n📊 更新结果:")
        print(f"   MySQL数据库: {'✅ 成功' if mysql_success else '❌ 失败'}")
        print(f"   Excel记录: {'✅ 成功' if excel_success else '❌ 失败'}")
        
    else:
        print("❌ 无法获取市场数据")
    
    print("\n⏰ 下次更新: 明日下午5:00")

if __name__ == "__main__":
    import os
    main()
