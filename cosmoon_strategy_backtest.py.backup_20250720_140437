#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Cosmoon博弈论策略回测系统
========================

基于Cosmoon的博弈论策略进行8000港币20年复利回测
核心策略：
- 买入：选择高值盈利区 (Y > 0.5 且 X > 0.5)
- 避免：远离控股商控制区 (0.333 < Y < 0.4)
- 卖出：当进入亏损区时 (Y < 0.25 或 X < 0.25)

公式：E = 8xy - 3x - 3y + 1

作者: Cosmoon NG
日期: 2025年7月
"""

import pymysql
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

class CosmoonStrategyBacktest:
    def __init__(self):
        """初始化Cosmoon博弈论策略回测系统"""
        self.db_config = {
            'host': '************',
            'port': 3306,
            'database': 'finance',
            'user': 'root',
            'password': '',
            'charset': 'utf8mb4'
        }
        
        # Cosmoon策略参数
        self.strategy_params = {
            'initial_capital': 8000,         # 初始资金8000港币
            'y_buy_threshold': 0.5,          # Y买入阈值：高值盈利区
            'x_buy_threshold': 0.5,          # X买入阈值：高值盈利区
            'y_sell_threshold': 0.25,        # Y卖出阈值：进入强亏损区
            'x_sell_threshold': 0.25,        # X卖出阈值：进入强亏损区
            'y_avoid_min': 0.333,            # Y避免区间下限：控股商控制区
            'y_avoid_max': 0.4,              # Y避免区间上限：控股商控制区
            'position_ratio': 0.95,          # 仓位比例95%
            'transaction_cost': 0.0025,      # 交易成本0.25%
            'compound_interest': True,       # 复利计算
        }
        
        self.data = None
        self.trades = []
        self.daily_portfolio = []
        
    def connect_database(self):
        """连接数据库"""
        try:
            self.connection = pymysql.connect(**self.db_config)
            print("✅ 数据库连接成功")
            return True
        except Exception as e:
            print(f"❌ 数据库连接失败: {e}")
            return False
    
    def load_data(self):
        """加载20年历史数据"""
        print("📊 加载Cosmoon策略回测数据...")
        
        try:
            # 使用hk00023表（数据完整性100%）
            query = """
                SELECT 
                    date,
                    close as price,
                    y_probability,
                    inflow_ratio as x_ratio,
                    volume
                FROM hk00023 
                WHERE date >= DATE_SUB(CURDATE(), INTERVAL 20 YEAR)
                AND y_probability IS NOT NULL 
                AND inflow_ratio IS NOT NULL
                ORDER BY date ASC
            """
            
            self.data = pd.read_sql(query, self.connection)
            
            if self.data.empty:
                print("❌ 未找到可用的历史数据")
                return False
            
            # 数据预处理
            self.data['date'] = pd.to_datetime(self.data['date'])
            self.data = self.data.sort_values('date').reset_index(drop=True)
            
            # 计算E值验证
            self.data['e_value'] = (8 * self.data['x_ratio'] * self.data['y_probability'] - 
                                   3 * self.data['x_ratio'] - 3 * self.data['y_probability'] + 1)
            
            print(f"📈 数据加载完成:")
            print(f"   • 数据期间: {self.data['date'].min().strftime('%Y-%m-%d')} 至 {self.data['date'].max().strftime('%Y-%m-%d')}")
            print(f"   • 总交易日: {len(self.data)} 天")
            print(f"   • Y值范围: {self.data['y_probability'].min():.3f} - {self.data['y_probability'].max():.3f}")
            print(f"   • X值范围: {self.data['x_ratio'].min():.3f} - {self.data['x_ratio'].max():.3f}")
            print(f"   • E值范围: {self.data['e_value'].min():.3f} - {self.data['e_value'].max():.3f}")
            
            return True
            
        except Exception as e:
            print(f"❌ 数据加载失败: {e}")
            return False
    
    def analyze_signal_distribution(self):
        """分析信号分布"""
        print(f"\n🎯 Cosmoon策略信号分析:")
        print("-" * 50)
        
        # 买入信号：高值盈利区
        buy_signals = ((self.data['y_probability'] > self.strategy_params['y_buy_threshold']) & 
                      (self.data['x_ratio'] > self.strategy_params['x_buy_threshold']))
        
        # 卖出信号：亏损区
        sell_signals = ((self.data['y_probability'] < self.strategy_params['y_sell_threshold']) | 
                       (self.data['x_ratio'] < self.strategy_params['x_sell_threshold']))
        
        # 避免区间：控股商控制区
        avoid_signals = ((self.data['y_probability'] > self.strategy_params['y_avoid_min']) & 
                        (self.data['y_probability'] < self.strategy_params['y_avoid_max']))
        
        print(f"📈 买入信号 (Y>0.5且X>0.5): {buy_signals.sum():,} 天 ({buy_signals.sum()/len(self.data)*100:.1f}%)")
        print(f"📉 卖出信号 (Y<0.25或X<0.25): {sell_signals.sum():,} 天 ({sell_signals.sum()/len(self.data)*100:.1f}%)")
        print(f"⏸️ 避免交易 (0.333<Y<0.4): {avoid_signals.sum():,} 天 ({avoid_signals.sum()/len(self.data)*100:.1f}%)")
        
        # E值分布
        positive_e = (self.data['e_value'] > 0).sum()
        negative_e = (self.data['e_value'] <= 0).sum()
        print(f"✅ E>0 (理论盈利): {positive_e:,} 天 ({positive_e/len(self.data)*100:.1f}%)")
        print(f"❌ E≤0 (理论亏损): {negative_e:,} 天 ({negative_e/len(self.data)*100:.1f}%)")
    
    def run_cosmoon_backtest(self):
        """运行Cosmoon博弈论策略回测"""
        print(f"\n🚀 开始Cosmoon博弈论策略回测...")
        print("="*60)
        print(f"💰 初始资金: {self.strategy_params['initial_capital']:,} 港币")
        print(f"📊 策略核心: E = 8xy - 3x - 3y + 1")
        print(f"📈 买入条件: Y > {self.strategy_params['y_buy_threshold']} 且 X > {self.strategy_params['x_buy_threshold']} (高值盈利区)")
        print(f"📉 卖出条件: Y < {self.strategy_params['y_sell_threshold']} 或 X < {self.strategy_params['x_sell_threshold']} (强亏损区)")
        print(f"⏸️ 避免区间: {self.strategy_params['y_avoid_min']} < Y < {self.strategy_params['y_avoid_max']} (控股商控制区)")
        print("="*60)
        
        # 初始化变量
        current_cash = self.strategy_params['initial_capital']
        current_position = 0
        position_cost = 0
        
        # 统计变量
        total_trades = 0
        buy_count = 0
        sell_count = 0
        avoid_count = 0
        winning_trades = 0
        losing_trades = 0
        
        for i, row in self.data.iterrows():
            date = row['date']
            price = row['price']
            y_val = row['y_probability']
            x_val = row['x_ratio']
            e_val = row['e_value']
            
            # 计算当前总资产
            position_value = current_position * price
            total_assets = current_cash + position_value
            
            # Cosmoon策略信号判断
            # 1. 买入信号：高值盈利区
            buy_signal = (y_val > self.strategy_params['y_buy_threshold'] and 
                         x_val > self.strategy_params['x_buy_threshold'] and 
                         current_position == 0)
            
            # 2. 卖出信号：亏损区
            sell_signal = ((y_val < self.strategy_params['y_sell_threshold'] or 
                           x_val < self.strategy_params['x_sell_threshold']) and 
                          current_position > 0)
            
            # 3. 避免交易：控股商控制区
            avoid_signal = (self.strategy_params['y_avoid_min'] < y_val < self.strategy_params['y_avoid_max'])
            
            # 执行买入（高值盈利区且不在避免区间）
            if buy_signal and not avoid_signal:
                buy_amount = current_cash * self.strategy_params['position_ratio']
                transaction_cost = buy_amount * self.strategy_params['transaction_cost']
                net_buy_amount = buy_amount - transaction_cost
                
                if net_buy_amount > 0:
                    shares_to_buy = net_buy_amount / price
                    actual_cost = shares_to_buy * price + transaction_cost
                    
                    current_position = shares_to_buy
                    position_cost = shares_to_buy * price
                    current_cash -= actual_cost
                    
                    # 记录交易
                    trade_record = {
                        'date': date,
                        'action': 'BUY',
                        'price': price,
                        'shares': shares_to_buy,
                        'amount': actual_cost,
                        'cost': transaction_cost,
                        'y_value': y_val,
                        'x_value': x_val,
                        'e_value': e_val,
                        'cash_after': current_cash,
                        'total_assets': total_assets,
                        'reason': 'High_Value_Profit_Zone'
                    }
                    self.trades.append(trade_record)
                    
                    buy_count += 1
                    total_trades += 1
                    
                    print(f"📈 {date.strftime('%Y-%m-%d')} 买入: {shares_to_buy:.0f}股 @ {price:.2f}, Y={y_val:.3f}, X={x_val:.3f}, E={e_val:.3f}")
            
            # 执行卖出（进入亏损区）
            elif sell_signal:
                sell_amount = current_position * price
                transaction_cost = sell_amount * self.strategy_params['transaction_cost']
                net_sell_amount = sell_amount - transaction_cost
                
                profit = net_sell_amount - position_cost
                profit_pct = (profit / position_cost) * 100 if position_cost > 0 else 0
                
                current_cash += net_sell_amount
                
                # 记录交易
                trade_record = {
                    'date': date,
                    'action': 'SELL',
                    'price': price,
                    'shares': current_position,
                    'amount': sell_amount,
                    'cost': transaction_cost,
                    'profit': profit,
                    'profit_pct': profit_pct,
                    'y_value': y_val,
                    'x_value': x_val,
                    'e_value': e_val,
                    'cash_after': current_cash,
                    'total_assets': current_cash,
                    'reason': 'Loss_Zone_Exit'
                }
                self.trades.append(trade_record)
                
                # 统计盈亏
                if profit > 0:
                    winning_trades += 1
                else:
                    losing_trades += 1
                
                print(f"📉 {date.strftime('%Y-%m-%d')} 卖出: {current_position:.0f}股 @ {price:.2f}, 盈亏: {profit:+.0f} ({profit_pct:+.1f}%), Y={y_val:.3f}, X={x_val:.3f}")
                
                current_position = 0
                position_cost = 0
                sell_count += 1
                total_trades += 1
            
            # 避免交易计数
            elif avoid_signal:
                avoid_count += 1
            
            # 记录每日组合价值
            current_total = current_cash + (current_position * price)
            daily_record = {
                'date': date,
                'price': price,
                'cash': current_cash,
                'position': current_position,
                'position_value': current_position * price,
                'total_value': current_total,
                'y_value': y_val,
                'x_value': x_val,
                'e_value': e_val,
                'signal': 'BUY' if buy_signal and not avoid_signal else 'SELL' if sell_signal else 'AVOID' if avoid_signal else 'HOLD'
            }
            self.daily_portfolio.append(daily_record)
        
        # 最终清仓
        if current_position > 0:
            final_price = self.data['price'].iloc[-1]
            final_sell_amount = current_position * final_price
            final_cost = final_sell_amount * self.strategy_params['transaction_cost']
            current_cash += final_sell_amount - final_cost
            print(f"🔚 最终清仓: {current_position:.0f}股 @ {final_price:.2f}")
        
        # 计算最终结果
        final_value = current_cash
        total_return = (final_value / self.strategy_params['initial_capital'] - 1) * 100
        years = len(self.data) / 252
        annual_return = (final_value / self.strategy_params['initial_capital']) ** (1/years) - 1
        
        print(f"\n✅ Cosmoon博弈论策略回测完成!")
        print(f"📊 交易统计:")
        print(f"   • 买入次数: {buy_count}")
        print(f"   • 卖出次数: {sell_count}")
        print(f"   • 避免交易天数: {avoid_count}")
        print(f"   • 盈利交易: {winning_trades}")
        print(f"   • 亏损交易: {losing_trades}")
        print(f"   • 胜率: {winning_trades/(winning_trades+losing_trades)*100:.1f}%" if (winning_trades+losing_trades) > 0 else "   • 胜率: N/A")
        
        print(f"\n💰 收益统计:")
        print(f"   • 初始资金: {self.strategy_params['initial_capital']:,} 港币")
        print(f"   • 最终资金: {final_value:,.0f} 港币")
        print(f"   • 总收益: {final_value - self.strategy_params['initial_capital']:+,.0f} 港币")
        print(f"   • 总收益率: {total_return:+.2f}%")
        print(f"   • 年化收益率: {annual_return*100:.2f}%")
        
        return True
    
    def save_results(self):
        """保存回测结果"""
        if not self.daily_portfolio or not self.trades:
            print("❌ 没有数据可保存")
            return
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 保存交易记录
        trades_df = pd.DataFrame(self.trades)
        trades_filename = f"cosmoon_strategy_trades_{timestamp}.csv"
        trades_df.to_csv(trades_filename, index=False, encoding='utf-8-sig')
        
        # 保存每日组合价值
        portfolio_df = pd.DataFrame(self.daily_portfolio)
        portfolio_filename = f"cosmoon_strategy_portfolio_{timestamp}.csv"
        portfolio_df.to_csv(portfolio_filename, index=False, encoding='utf-8-sig')
        
        print(f"\n💾 结果已保存:")
        print(f"   • 交易记录: {trades_filename}")
        print(f"   • 组合价值: {portfolio_filename}")
        
        return trades_filename, portfolio_filename

def main():
    """主函数"""
    print("🎯 Cosmoon博弈论策略回测系统")
    print("="*50)
    print("📅 启动时间:", datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
    print("👨‍💼 策略作者: Cosmoon NG")
    print("📊 核心公式: E = 8xy - 3x - 3y + 1")
    
    # 创建回测实例
    backtest = CosmoonStrategyBacktest()
    
    # 连接数据库
    if not backtest.connect_database():
        return
    
    # 加载数据
    if not backtest.load_data():
        return
    
    # 分析信号分布
    backtest.analyze_signal_distribution()
    
    # 运行回测
    if not backtest.run_cosmoon_backtest():
        return
    
    # 保存结果
    backtest.save_results()
    
    print("\n🎉 Cosmoon博弈论策略回测完成!")
    print("💡 万物皆数，博弈制胜！")

if __name__ == "__main__":
    main()
