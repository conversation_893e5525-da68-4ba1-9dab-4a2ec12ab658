#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
真复利计算演示系统
展示复利在交易中的威力
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import matplotlib.pyplot as plt

class CompoundInterestCalculator:
    def __init__(self, initial_capital=10000):
        self.initial_capital = initial_capital
        self.compound_enabled = True
        self.position_ratio = 0.8  # 80%仓位
        self.transaction_cost = 0.001  # 0.1%交易成本
        
    def calculate_simple_vs_compound(self, trades_data):
        """对比简单收益 vs 复利收益"""
        
        # 简单收益计算（固定仓位）
        simple_capital = self.initial_capital
        simple_history = []
        
        # 复利收益计算（动态仓位）
        compound_capital = self.initial_capital
        compound_history = []
        
        for i, trade in enumerate(trades_data):
            profit_rate = trade['profit_rate']
            date = trade.get('date', f'Day_{i+1}')
            
            # 简单收益：固定800股
            fixed_position = 800
            simple_profit = fixed_position * 12.0 * profit_rate  # 假设12港元/股
            simple_capital += simple_profit
            
            simple_history.append({
                'date': date,
                'capital': simple_capital,
                'profit': simple_profit,
                'position': fixed_position,
                'method': '固定仓位'
            })
            
            # 复利收益：动态仓位
            available_capital = compound_capital * self.position_ratio
            compound_position = int(available_capital / 12.0)  # 动态计算股数
            compound_profit = compound_position * 12.0 * profit_rate
            compound_capital += compound_profit
            
            compound_history.append({
                'date': date,
                'capital': compound_capital,
                'profit': compound_profit,
                'position': compound_position,
                'method': '复利计算'
            })
        
        return simple_history, compound_history
    
    def simulate_trading_scenarios(self):
        """模拟不同交易场景"""
        print("💰 真复利计算演示")
        print("=" * 60)
        
        # 模拟交易数据
        scenarios = {
            "稳健策略": [0.02, 0.015, -0.005, 0.025, 0.018, 0.012, -0.008, 0.022],
            "激进策略": [0.05, 0.03, -0.02, 0.06, -0.015, 0.04, -0.025, 0.055],
            "保守策略": [0.01, 0.008, -0.002, 0.012, 0.009, 0.006, -0.003, 0.011]
        }
        
        results = {}
        
        for strategy_name, profit_rates in scenarios.items():
            trades_data = [
                {'date': f'2025-01-{i+1:02d}', 'profit_rate': rate}
                for i, rate in enumerate(profit_rates)
            ]
            
            simple_history, compound_history = self.calculate_simple_vs_compound(trades_data)
            
            simple_final = simple_history[-1]['capital']
            compound_final = compound_history[-1]['capital']
            
            results[strategy_name] = {
                'simple_final': simple_final,
                'compound_final': compound_final,
                'simple_return': (simple_final / self.initial_capital - 1) * 100,
                'compound_return': (compound_final / self.initial_capital - 1) * 100,
                'compound_advantage': compound_final - simple_final
            }
            
            print(f"\n📊 {strategy_name}策略结果:")
            print(f"   固定仓位最终: {simple_final:,.2f} 港币 ({results[strategy_name]['simple_return']:+.2f}%)")
            print(f"   复利计算最终: {compound_final:,.2f} 港币 ({results[strategy_name]['compound_return']:+.2f}%)")
            print(f"   复利优势: {results[strategy_name]['compound_advantage']:+,.2f} 港币")
        
        return results
    
    def long_term_compound_projection(self):
        """长期复利投影"""
        print(f"\n📈 长期复利投影分析:")
        print("=" * 40)
        
        # 不同年化收益率的长期表现
        annual_returns = [0.05, 0.08, 0.12, 0.15, 0.20]
        years = [1, 3, 5, 10, 20]
        
        print(f"   年化收益率 | 1年后    | 3年后    | 5年后    | 10年后   | 20年后")
        print(f"   " + "-" * 65)
        
        for annual_return in annual_returns:
            line = f"   {annual_return*100:8.0f}%     |"
            
            for year in years:
                # 复利公式: A = P(1 + r)^t
                final_amount = self.initial_capital * (1 + annual_return) ** year
                line += f" {final_amount:8,.0f} |"
            
            print(line)
        
        # 复利效应说明
        print(f"\n💡 复利效应说明:")
        print(f"   初始资金: {self.initial_capital:,} 港币")
        print(f"   公式: 最终金额 = 初始金额 × (1 + 年化收益率)^年数")
        
        # 计算翻倍时间
        print(f"\n⏰ 资金翻倍时间 (72法则):")
        for annual_return in annual_returns:
            double_time = 72 / (annual_return * 100)
            print(f"   年化{annual_return*100:.0f}%: 约{double_time:.1f}年翻倍")
    
    def real_trading_compound_example(self):
        """真实交易复利示例"""
        print(f"\n🎯 真实交易复利示例:")
        print("=" * 40)
        
        # 模拟真实交易序列
        real_trades = [
            {'date': '2025-01-01', 'price': 12.00, 'action': '买入', 'profit_rate': 0.025},
            {'date': '2025-01-03', 'price': 12.30, 'action': '卖出', 'profit_rate': 0.025},
            {'date': '2025-01-08', 'price': 12.10, 'action': '买入', 'profit_rate': 0.018},
            {'date': '2025-01-10', 'price': 12.32, 'action': '卖出', 'profit_rate': 0.018},
            {'date': '2025-01-15', 'price': 12.20, 'action': '买入', 'profit_rate': -0.012},
            {'date': '2025-01-17', 'price': 12.05, 'action': '卖出', 'profit_rate': -0.012},
            {'date': '2025-01-22', 'price': 11.95, 'action': '买入', 'profit_rate': 0.035},
            {'date': '2025-01-25', 'price': 12.37, 'action': '卖出', 'profit_rate': 0.035},
        ]
        
        capital = self.initial_capital
        
        print(f"   交易日期   | 价格  | 动作 | 股数  | 盈亏     | 累计资金")
        print(f"   " + "-" * 55)
        
        for i, trade in enumerate(real_trades):
            if trade['action'] == '买入':
                # 计算可买股数（复利）
                available_capital = capital * self.position_ratio
                shares = int(available_capital / trade['price'])
                cost = shares * trade['price'] * (1 + self.transaction_cost)
                capital -= cost
                
                print(f"   {trade['date']} | {trade['price']:5.2f} | {trade['action']} | {shares:4d} | -       | {capital:8,.0f}")
                
            else:  # 卖出
                # 计算盈亏
                profit = shares * trade['price'] * trade['profit_rate']
                revenue = shares * trade['price'] * (1 - self.transaction_cost)
                capital += revenue + profit
                
                print(f"   {trade['date']} | {trade['price']:5.2f} | {trade['action']} | {shares:4d} | {profit:+7,.0f} | {capital:8,.0f}")
        
        total_return = (capital / self.initial_capital - 1) * 100
        print(f"\n   📊 交易总结:")
        print(f"      初始资金: {self.initial_capital:,} 港币")
        print(f"      最终资金: {capital:,.0f} 港币")
        print(f"      总收益率: {total_return:+.2f}%")
        print(f"      复利效应: 每次交易的盈利都增加了下次的投入资本")

def main():
    """主函数"""
    print("🎯 真复利计算系统演示")
    print("=" * 50)
    print(f"🕐 演示时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 创建复利计算器
    calculator = CompoundInterestCalculator(initial_capital=10000)
    
    # 1. 模拟交易场景对比
    print("\n1️⃣ 不同策略的复利效果对比")
    results = calculator.simulate_trading_scenarios()
    
    # 2. 长期复利投影
    print("\n2️⃣ 长期复利投影")
    calculator.long_term_compound_projection()
    
    # 3. 真实交易复利示例
    print("\n3️⃣ 真实交易复利示例")
    calculator.real_trading_compound_example()
    
    # 4. 复利 vs 单利对比
    print(f"\n4️⃣ 复利 vs 单利对比 (10年期):")
    print("=" * 40)
    
    initial = 10000
    annual_rate = 0.12  # 12%年化收益
    years = 10
    
    # 单利计算
    simple_interest = initial * (1 + annual_rate * years)
    
    # 复利计算
    compound_interest = initial * (1 + annual_rate) ** years
    
    print(f"   初始投资: {initial:,} 港币")
    print(f"   年化收益: {annual_rate*100:.0f}%")
    print(f"   投资期限: {years}年")
    print(f"   单利结果: {simple_interest:,.0f} 港币")
    print(f"   复利结果: {compound_interest:,.0f} 港币")
    print(f"   复利优势: {compound_interest - simple_interest:+,.0f} 港币")
    print(f"   增长倍数: {compound_interest / initial:.1f}倍")
    
    print(f"\n💡 复利的威力:")
    print(f"   爱因斯坦: '复利是世界第八大奇迹'")
    print(f"   巴菲特: '我的财富来自于美国的繁荣、复利和时间'")
    print(f"   关键: 时间 + 稳定收益率 + 再投资")

if __name__ == "__main__":
    main()
