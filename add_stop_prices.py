#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
添加止盈价和止损价到Excel表格
============================

为交易记录添加重要的风险管理字段:
- 止盈价
- 止损价
- 根据实际资金情况修正数据

作者: Cosmoon NG
"""

import pandas as pd
import yfinance as yf
import mysql.connector
from datetime import datetime
import os
import warnings
warnings.filterwarnings('ignore')

def get_current_price():
    """获取当前价格"""
    try:
        ticker = yf.Ticker("0023.HK")
        hist = ticker.history(period="1d")
        return hist['Close'].iloc[-1]
    except:
        return 12.18  # 使用最近的价格

def get_database_xye():
    """从数据库获取XYE指标"""
    try:
        config = {
            'host': 'localhost',
            'port': 3306,
            'user': 'root',
            'password': '12345678',
            'database': 'finance'
        }
        
        connection = mysql.connector.connect(**config)
        cursor = connection.cursor()
        
        query = "SELECT Y_Value, X_Value, E_Value FROM eab_0023hk ORDER BY Date DESC LIMIT 1"
        cursor.execute(query)
        result = cursor.fetchone()
        
        cursor.close()
        connection.close()
        
        if result:
            return {
                'y_value': result[0],
                'x_value': result[1], 
                'e_value': result[2]
            }
    except:
        pass
    
    # 默认值
    return {
        'y_value': 0.2069,
        'x_value': 0.3211,
        'e_value': -0.0525
    }

def calculate_stop_prices(entry_price, position_type):
    """计算止盈价和止损价"""
    
    # 止盈止损比例 (根据回测系统设置)
    take_profit_rate = 0.012  # 1.2%
    stop_loss_rate = 0.006    # 0.6%
    
    if position_type == "多头":
        # 多头: 止盈价更高，止损价更低
        take_profit_price = entry_price * (1 + take_profit_rate)
        stop_loss_price = entry_price * (1 - stop_loss_rate)
    elif position_type == "空头":
        # 空头: 止盈价更低，止损价更高
        take_profit_price = entry_price * (1 - take_profit_rate)
        stop_loss_price = entry_price * (1 + stop_loss_rate)
    else:
        # 无持仓
        take_profit_price = 0.00
        stop_loss_price = 0.00
    
    return take_profit_price, stop_loss_price

def update_excel_with_stops():
    """更新Excel表格，添加止盈止损价格"""
    
    excel_file = "交易记录追踪0023HK.xlsx"
    
    print("🎯 添加止盈价和止损价到Excel表格")
    print("=" * 50)
    
    # 获取当前数据
    current_price = get_current_price()
    xye_data = get_database_xye()
    
    print(f"📈 当前价格: {current_price:.2f} 港元")
    
    # 用户实际参数
    total_capital = 2500.00      # 总资本
    max_shares = 200             # 最大持仓股数
    commission_rate = 0.001      # 手续费率
    
    # 计算交易数据
    trade_amount = current_price * max_shares
    commission = trade_amount * commission_rate
    net_amount = trade_amount - commission
    account_balance = total_capital - trade_amount - commission
    current_market_value = trade_amount
    total_assets = account_balance + current_market_value
    
    # 判断交易信号
    y = xye_data['y_value']
    x = xye_data['x_value'] 
    e = xye_data['e_value']
    
    if y < 0.3 or x < 0.3:
        signal_strength = "强烈卖出"
        trade_direction = "空头"
        trade_type = "开仓"
        risk_level = "高风险"
    else:
        signal_strength = "观望"
        trade_direction = "无"
        trade_type = "观察"
        risk_level = "中风险"
    
    # 计算止盈止损价格
    if trade_type == "开仓":
        take_profit_price, stop_loss_price = calculate_stop_prices(current_price, trade_direction)
    else:
        take_profit_price, stop_loss_price = 0.00, 0.00
    
    # 创建完整记录 (包含止盈止损价格)
    complete_record = {
        '交易日期': datetime.now().strftime('%Y-%m-%d'),
        '交易类型': trade_type,
        '交易方向': trade_direction,
        '交易价格': current_price,
        '止盈价': take_profit_price,
        '止损价': stop_loss_price,
        '持仓数量': max_shares if trade_type == "开仓" else 0,
        '交易金额': trade_amount if trade_type == "开仓" else 0.00,
        '手续费': commission if trade_type == "开仓" else 0.00,
        '净交易额': net_amount if trade_type == "开仓" else 0.00,
        '持仓成本': current_price if trade_type == "开仓" else 0.00,
        '当前市值': current_market_value if trade_type == "开仓" else 0.00,
        '浮动盈亏': 0.00,
        '实现盈亏': 0.00,
        '累计盈亏': 0.00,
        '账户余额': account_balance if trade_type == "开仓" else total_capital,
        '总资产': total_assets if trade_type == "开仓" else total_capital,
        '收益率': 0.00,
        '累计收益率': (total_assets - total_capital) / total_capital * 100 if trade_type == "开仓" else 0.00,
        'Y值': y,
        'X值': x,
        'E值': e,
        '信号强度': signal_strength,
        '风险等级': risk_level,
        '备注': f"完整记录含止盈止损 总资本{total_capital:,.0f}港元 持仓{max_shares if trade_type == '开仓' else 0}股"
    }
    
    try:
        # 加载现有记录
        if os.path.exists(excel_file):
            df = pd.read_excel(excel_file)
            print(f"📋 加载现有记录: {len(df)}条")
            
            # 检查是否已有止盈价和止损价列
            if '止盈价' not in df.columns:
                df['止盈价'] = 0.00
                print("➕ 添加止盈价列")
            
            if '止损价' not in df.columns:
                df['止损价'] = 0.00
                print("➕ 添加止损价列")
            
            # 替换最新记录
            df.iloc[-1] = complete_record
            print("🔄 已更新最新记录")
            
        else:
            df = pd.DataFrame([complete_record])
            print("📋 创建新的Excel文件")
        
        # 保存到Excel
        df.to_excel(excel_file, index=False)
        
        print(f"✅ Excel文件已更新: {excel_file}")
        
        # 显示详细信息
        print(f"\n📋 完整记录详情:")
        print(f"   📅 交易日期: {complete_record['交易日期']}")
        print(f"   🎯 交易类型: {complete_record['交易类型']}")
        print(f"   📊 交易方向: {complete_record['交易方向']}")
        print(f"   💰 交易价格: {complete_record['交易价格']:.2f} 港元")
        print(f"   🎯 止盈价: {complete_record['止盈价']:.2f} 港元")
        print(f"   🛑 止损价: {complete_record['止损价']:.2f} 港元")
        print(f"   📊 持仓数量: {complete_record['持仓数量']} 股")
        print(f"   💵 交易金额: {complete_record['交易金额']:,.2f} 港元")
        print(f"   💸 手续费: {complete_record['手续费']:.2f} 港元")
        print(f"   💰 账户余额: {complete_record['账户余额']:,.2f} 港元")
        print(f"   📈 当前市值: {complete_record['当前市值']:,.2f} 港元")
        print(f"   💎 总资产: {complete_record['总资产']:,.2f} 港元")
        print(f"   📊 累计收益率: {complete_record['累计收益率']:.2f}%")
        print(f"   🎯 信号强度: {complete_record['信号强度']}")
        print(f"   ⚠️ 风险等级: {complete_record['风险等级']}")
        
        # 风险管理提示
        if trade_type == "开仓":
            print(f"\n🎯 风险管理设置:")
            print(f"   📈 止盈幅度: +1.2% (目标价: {take_profit_price:.2f})")
            print(f"   📉 止损幅度: -0.6% (止损价: {stop_loss_price:.2f})")
            
            if trade_direction == "空头":
                profit_potential = (current_price - take_profit_price) * max_shares
                loss_potential = (stop_loss_price - current_price) * max_shares
                print(f"   💰 潜在盈利: {profit_potential:.2f} 港元")
                print(f"   ⚠️ 潜在亏损: {loss_potential:.2f} 港元")
        
        # 检查字段完整性
        total_fields = len(complete_record)
        filled_fields = sum(1 for v in complete_record.values() if v != 0 and v != '' and pd.notna(v))
        fill_rate = filled_fields / total_fields * 100
        
        print(f"\n📊 字段完整性:")
        print(f"   总字段数: {total_fields}")
        print(f"   已填充字段: {filled_fields}")
        print(f"   完整率: {fill_rate:.1f}%")
        
        return True
        
    except Exception as e:
        print(f"❌ Excel文件操作失败: {e}")
        return False

def main():
    """主函数"""
    try:
        success = update_excel_with_stops()
        
        if success:
            print(f"\n🎯 Excel表格更新完成！")
            print(f"✅ 已添加止盈价和止损价字段")
            print(f"💰 资金数据已修正为实际情况")
            print(f"📊 风险管理参数已设置")
        else:
            print(f"\n❌ Excel表格更新失败")
            
    except Exception as e:
        print(f"❌ 系统错误: {e}")

if __name__ == "__main__":
    main()
