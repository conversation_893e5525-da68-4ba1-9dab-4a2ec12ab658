#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
0023.HK离线模式系统
==================

当MySQL不可用时的备用方案:
1. 实时监控 (仅显示，不保存数据库)
2. 数据保存到本地CSV文件
3. Excel记录正常更新
4. 回测功能正常运行

作者: Cosmoon NG
"""

import yfinance as yf
import pandas as pd
import numpy as np
import os
from datetime import datetime, timedelta
from scipy import stats
import warnings
warnings.filterwarnings('ignore')

class OfflineModeSystem:
    """离线模式系统"""
    
    def __init__(self):
        self.symbol = "0023.HK"
        self.company_name = "东亚银行"
        
        # 离线数据文件
        self.csv_file = "0023hk_realtime_data.csv"
        self.excel_file = "交易记录追踪0023HK.xlsx"
        
        print(f"🔄 {self.symbol} 离线模式系统启动")
        print("📊 MySQL不可用时的备用方案")

    def check_mysql_status(self):
        """检查MySQL连接状态"""
        try:
            import mysql.connector
            config = {
                'host': 'localhost',
                'port': 3306,
                'user': 'root',
                'password': '12345678',
                'database': 'finance'
            }
            
            connection = mysql.connector.connect(**config)
            connection.close()
            return True
            
        except:
            return False

    def get_current_price(self):
        """获取当前价格"""
        try:
            ticker = yf.Ticker(self.symbol)
            
            # 尝试获取实时价格
            info = ticker.info
            current_price = info.get('currentPrice') or info.get('regularMarketPrice')
            
            if current_price is None:
                # 使用最新历史数据
                hist = ticker.history(period="1d", interval="1m")
                if not hist.empty:
                    current_price = hist['Close'].iloc[-1]
            
            if current_price:
                return {
                    'timestamp': datetime.now(),
                    'price': float(current_price),
                    'open': info.get('regularMarketOpen', 0),
                    'high': info.get('dayHigh', 0),
                    'low': info.get('dayLow', 0),
                    'volume': info.get('regularMarketVolume', 0),
                    'previous_close': info.get('regularMarketPreviousClose', 0)
                }
            
        except Exception as e:
            print(f"❌ 获取价格失败: {e}")
            
        return None

    def save_to_csv(self, data):
        """保存数据到CSV文件"""
        try:
            # 准备数据
            record = {
                'timestamp': data['timestamp'],
                'price': data['price'],
                'open': data['open'],
                'high': data['high'],
                'low': data['low'],
                'volume': data['volume'],
                'previous_close': data['previous_close']
            }
            
            # 检查文件是否存在
            if os.path.exists(self.csv_file):
                df = pd.read_csv(self.csv_file)
                df = pd.concat([df, pd.DataFrame([record])], ignore_index=True)
            else:
                df = pd.DataFrame([record])
            
            # 保存到CSV
            df.to_csv(self.csv_file, index=False)
            
            return True
            
        except Exception as e:
            print(f"❌ CSV保存失败: {e}")
            return False

    def calculate_xye_from_csv(self):
        """从CSV数据计算XYE指标"""
        try:
            if not os.path.exists(self.csv_file):
                return None
            
            df = pd.read_csv(self.csv_file)
            
            if len(df) < 20:
                return None
            
            # 计算Y值
            window = 20
            df['high_20'] = df['high'].rolling(window).max()
            df['low_20'] = df['low'].rolling(window).min()
            df['y_value'] = (df['price'] - df['low_20']) / (df['high_20'] - df['low_20'])
            df['y_value'] = df['y_value'].fillna(0.5).clip(0, 1)
            
            # 计算X值 (简化版MFI)
            df['price_change'] = df['price'].diff()
            df['positive_change'] = np.where(df['price_change'] > 0, df['price_change'], 0)
            df['negative_change'] = np.where(df['price_change'] < 0, abs(df['price_change']), 0)
            
            period = 14
            df['positive_sum'] = df['positive_change'].rolling(period).sum()
            df['negative_sum'] = df['negative_change'].rolling(period).sum()
            df['x_value'] = df['positive_sum'] / (df['positive_sum'] + df['negative_sum'] + 1e-10)
            
            # 计算E值
            df['e_value'] = (8 * df['x_value'] - 3) * df['y_value'] - 3 * df['x_value'] + 1
            
            # 返回最新值
            latest = df.iloc[-1]
            
            return {
                'y_value': latest['y_value'],
                'x_value': latest['x_value'],
                'e_value': latest['e_value']
            }
            
        except Exception as e:
            print(f"❌ XYE计算失败: {e}")
            return None

    def generate_trading_signal(self, indicators):
        """生成交易信号"""
        if not indicators:
            return "数据不足", "⚪"
        
        y = indicators['y_value']
        x = indicators['x_value']
        e = indicators['e_value']
        
        if e > 0 and x > 0.45 and y > 0.45:
            return "强烈买入", "🟢"
        elif y < 0.3 or x < 0.3:
            return "强烈卖出", "🔴"
        else:
            return "观望", "⚪"

    def update_excel_record(self, market_data, indicators, signal):
        """更新Excel记录"""
        try:
            # 加载现有记录
            if os.path.exists(self.excel_file):
                df = pd.read_excel(self.excel_file)
            else:
                df = pd.DataFrame()
            
            # 创建今日记录
            record = {
                '交易日期': market_data['timestamp'].strftime('%Y-%m-%d'),
                '交易类型': '观察',
                '交易方向': '无',
                '交易价格': market_data['price'],
                '持仓数量': 0,
                '交易金额': 0.00,
                '手续费': 0.00,
                '净交易额': 0.00,
                '持仓成本': 0.00,
                '当前市值': 0.00,
                '浮动盈亏': 0.00,
                '实现盈亏': 0.00,
                '累计盈亏': 0.00,
                '账户余额': 10000.00,
                '总资产': 10000.00,
                '收益率': 0.00,
                '累计收益率': 0.00,
                'Y值': indicators['y_value'] if indicators else 0.5,
                'X值': indicators['x_value'] if indicators else 0.5,
                'E值': indicators['e_value'] if indicators else 0,
                '信号强度': signal[0],
                '风险等级': '中风险',
                '备注': f"离线模式收盘价{market_data['price']:.2f}港元MySQL不可用"
            }
            
            # 添加记录
            new_df = pd.concat([df, pd.DataFrame([record])], ignore_index=True)
            new_df.to_excel(self.excel_file, index=False)
            
            print(f"✅ Excel记录已更新: {self.excel_file}")
            return True
            
        except Exception as e:
            print(f"❌ Excel更新失败: {e}")
            return False

    def run_offline_update(self):
        """运行离线更新"""
        print(f"\n🔄 执行离线模式更新...")
        
        # 检查MySQL状态
        mysql_available = self.check_mysql_status()
        print(f"🗄️ MySQL状态: {'✅ 可用' if mysql_available else '❌ 不可用'}")
        
        if not mysql_available:
            print("📊 使用离线模式...")
        
        # 获取市场数据
        market_data = self.get_current_price()
        
        if market_data:
            print(f"📈 当前价格: {market_data['price']:.4f} 港元")
            
            # 保存到CSV
            csv_success = self.save_to_csv(market_data)
            print(f"💾 CSV保存: {'✅ 成功' if csv_success else '❌ 失败'}")
            
            # 计算技术指标
            indicators = self.calculate_xye_from_csv()
            
            if indicators:
                print(f"🧮 技术指标:")
                print(f"   Y值: {indicators['y_value']:.4f}")
                print(f"   X值: {indicators['x_value']:.4f}")
                print(f"   E值: {indicators['e_value']:.4f}")
            
            # 生成交易信号
            signal = self.generate_trading_signal(indicators)
            print(f"🎯 交易信号: {signal[1]} {signal[0]}")
            
            # 更新Excel
            excel_success = self.update_excel_record(market_data, indicators, signal)
            
            print(f"\n📊 更新结果:")
            print(f"   CSV数据: {'✅ 成功' if csv_success else '❌ 失败'}")
            print(f"   Excel记录: {'✅ 成功' if excel_success else '❌ 失败'}")
            print(f"   MySQL数据库: {'⚠️ 跳过' if not mysql_available else '✅ 可用'}")
            
        else:
            print("❌ 无法获取市场数据")

    def show_csv_data_summary(self):
        """显示CSV数据摘要"""
        try:
            if os.path.exists(self.csv_file):
                df = pd.read_csv(self.csv_file)
                print(f"\n📊 本地数据摘要:")
                print(f"   数据点数: {len(df)}")
                print(f"   最新价格: {df['price'].iloc[-1]:.4f} 港元")
                print(f"   价格范围: {df['price'].min():.4f} - {df['price'].max():.4f}")
                print(f"   最新时间: {df['timestamp'].iloc[-1]}")
            else:
                print("📊 暂无本地数据")
                
        except Exception as e:
            print(f"❌ 数据摘要失败: {e}")

def main():
    """主函数"""
    print("🔄 0023.HK 离线模式系统")
    print("=" * 50)
    
    system = OfflineModeSystem()
    
    try:
        # 运行离线更新
        system.run_offline_update()
        
        # 显示数据摘要
        system.show_csv_data_summary()
        
        print(f"\n💡 提示:")
        print(f"   - 重启MySQL后可恢复完整功能")
        print(f"   - 离线模式数据保存在: {system.csv_file}")
        print(f"   - Excel记录正常更新")
        
    except Exception as e:
        print(f"❌ 系统运行失败: {e}")

if __name__ == "__main__":
    main()
