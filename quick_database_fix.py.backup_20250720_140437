#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速数据库空间修复工具
====================
解决数据库空间不足的问题
"""

import mysql.connector
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

def quick_database_fix():
    """快速修复数据库空间问题"""
    
    # 数据库配置
    db_config = {
        'host': '************',
        'database': 'finance',
        'user': 'root',
        'password': '',
        'charset': 'utf8mb4',
        'autocommit': True
    }
    
    try:
        print("🎯 快速数据库空间修复")
        print("=" * 50)
        print(f"📅 执行时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        # 连接数据库
        connection = mysql.connector.connect(**db_config)
        cursor = connection.cursor()
        print(f"✅ 成功连接到数据库: {db_config['host']}/{db_config['database']}")
        
        # 1. 检查数据库大小
        print("\n📊 检查数据库大小...")
        cursor.execute("""
            SELECT 
                ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) as 'Size_MB'
            FROM information_schema.tables 
            WHERE table_schema = 'finance'
        """)
        
        db_size = cursor.fetchone()[0]
        print(f"📊 当前数据库大小: {db_size} MB")
        
        # 2. 查找可删除的表
        print("\n🔍 查找可删除的表...")
        
        # 查找测试表
        cursor.execute("""
            SELECT table_name, 
                   ROUND(((data_length + index_length) / 1024 / 1024), 2) as size_mb
            FROM information_schema.TABLES 
            WHERE table_schema = 'finance'
            AND (table_name LIKE 'test%' 
                 OR table_name LIKE 'temp%' 
                 OR table_name LIKE '%copy%'
                 OR table_name LIKE '%backup%')
            ORDER BY (data_length + index_length) DESC
        """)
        
        deletable_tables = cursor.fetchall()
        total_deletable_size = 0
        
        if deletable_tables:
            print("📋 发现可删除的表:")
            for table_name, size_mb in deletable_tables:
                print(f"   • {table_name}: {size_mb} MB")
                total_deletable_size += size_mb
            
            print(f"\n📊 可释放空间: {total_deletable_size} MB")
            
            # 删除这些表
            print("\n🗑️ 删除测试表和临时表...")
            deleted_count = 0
            for table_name, size_mb in deletable_tables:
                try:
                    cursor.execute(f"DROP TABLE IF EXISTS `{table_name}`")
                    print(f"   ✅ 已删除: {table_name} ({size_mb} MB)")
                    deleted_count += 1
                except mysql.connector.Error as e:
                    print(f"   ❌ 删除失败 {table_name}: {e}")
            
            print(f"✅ 成功删除 {deleted_count} 个表")
        else:
            print("📋 没有发现明显可删除的表")
        
        # 3. 清理二进制日志
        print("\n🗑️ 清理二进制日志...")
        try:
            # 检查二进制日志是否启用
            cursor.execute("SHOW VARIABLES LIKE 'log_bin'")
            log_bin_result = cursor.fetchone()
            
            if log_bin_result and log_bin_result[1] == 'ON':
                # 清理7天前的二进制日志
                cursor.execute("PURGE BINARY LOGS BEFORE DATE_SUB(NOW(), INTERVAL 7 DAY)")
                print("✅ 已清理7天前的二进制日志")
            else:
                print("📋 二进制日志未启用或已关闭")
        except mysql.connector.Error as e:
            print(f"⚠️ 清理二进制日志失败: {e}")
        
        # 4. 优化关键表
        print("\n🔧 优化关键表...")
        
        # 获取最大的几个表
        cursor.execute("""
            SELECT table_name
            FROM information_schema.TABLES 
            WHERE table_schema = 'finance'
            ORDER BY (data_length + index_length) DESC
            LIMIT 5
        """)
        
        large_tables = cursor.fetchall()
        optimized_count = 0
        
        for (table_name,) in large_tables:
            try:
                print(f"   🔧 优化表: {table_name}")
                cursor.execute(f"OPTIMIZE TABLE `{table_name}`")
                # 消费结果集
                cursor.fetchall()
                optimized_count += 1
                print(f"   ✅ 优化完成: {table_name}")
            except mysql.connector.Error as e:
                print(f"   ⚠️ 优化失败 {table_name}: {e}")
        
        print(f"✅ 成功优化 {optimized_count} 个表")
        
        # 5. 检查修复后的大小
        print("\n📊 检查修复后的数据库大小...")
        cursor.execute("""
            SELECT 
                ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) as 'Size_MB'
            FROM information_schema.tables 
            WHERE table_schema = 'finance'
        """)
        
        new_db_size = cursor.fetchone()[0]
        space_saved = db_size - new_db_size
        
        print(f"📊 修复前大小: {db_size} MB")
        print(f"📊 修复后大小: {new_db_size} MB")
        print(f"📊 释放空间: {space_saved} MB")
        
        # 6. 生成进一步的清理建议
        print("\n💡 进一步的清理建议:")
        print("=" * 50)
        
        # 检查是否有很多重复的股票表
        cursor.execute("""
            SELECT COUNT(*) as stock_table_count
            FROM information_schema.tables 
            WHERE table_schema = 'finance' 
            AND table_name LIKE 'stock_%'
        """)
        
        stock_count = cursor.fetchone()[0]
        print(f"📊 股票表数量: {stock_count}")
        
        if stock_count > 50:
            print("💡 建议:")
            print("   1. 考虑删除不常用的股票数据")
            print("   2. 将历史数据归档到其他数据库")
            print("   3. 只保留最近2-3年的数据")
        
        # 检查数据密度
        cursor.execute("""
            SELECT 
                table_name,
                table_rows,
                ROUND(((data_length + index_length) / 1024 / 1024), 2) as size_mb,
                ROUND(table_rows / ((data_length + index_length) / 1024 / 1024), 0) as rows_per_mb
            FROM information_schema.TABLES 
            WHERE table_schema = 'finance'
            AND table_rows > 0
            ORDER BY size_mb DESC
            LIMIT 10
        """)
        
        density_stats = cursor.fetchall()
        print(f"\n📊 数据密度分析 (前10个最大表):")
        print("表名                     | 行数   | 大小(MB) | 行/MB")
        print("-" * 60)
        for table_name, rows, size_mb, density in density_stats:
            print(f"{table_name:25s} | {rows:6d} | {size_mb:8.2f} | {density:5.0f}")
        
        # 7. 生成清理SQL脚本
        cleanup_sql = f"""-- 数据库清理脚本
-- 生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
-- 当前数据库大小: {new_db_size} MB

-- 1. 清理更多临时数据 (如果需要)
-- DELETE FROM table_name WHERE date < DATE_SUB(NOW(), INTERVAL 2 YEAR);

-- 2. 重建索引 (对大表谨慎使用)
-- ALTER TABLE large_table_name ENGINE=InnoDB;

-- 3. 清理更多二进制日志
-- PURGE BINARY LOGS BEFORE DATE_SUB(NOW(), INTERVAL 3 DAY);

-- 4. 检查表空间
-- SELECT table_name, 
--        ROUND(((data_length + index_length) / 1024 / 1024), 2) as size_mb
-- FROM information_schema.TABLES 
-- WHERE table_schema = 'finance'
-- ORDER BY (data_length + index_length) DESC;
"""
        
        with open('quick_cleanup_script.sql', 'w', encoding='utf-8') as f:
            f.write(cleanup_sql)
        
        print(f"\n📝 清理脚本已生成: quick_cleanup_script.sql")
        
        # 关闭连接
        cursor.close()
        connection.close()
        
        print("\n🎉 快速修复完成!")
        
        if space_saved > 0:
            print(f"✅ 成功释放 {space_saved} MB 空间")
        else:
            print("📋 数据库大小基本正常，无需大量清理")
        
        print("\n💡 如果仍然遇到空间问题，请检查:")
        print("   1. 磁盘空间是否充足")
        print("   2. MySQL临时目录空间")
        print("   3. 是否有长时间运行的事务")
        print("   4. InnoDB缓冲池设置")
        
        return True
        
    except mysql.connector.Error as e:
        print(f"❌ 数据库操作失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 执行失败: {e}")
        return False

def main():
    """主函数"""
    success = quick_database_fix()
    
    if success:
        print("\n✅ 修复完成!")
        print("📝 如果问题仍然存在，请联系数据库管理员")
    else:
        print("\n❌ 修复失败!")
        print("📝 请手动检查数据库状态")

if __name__ == "__main__":
    main()
