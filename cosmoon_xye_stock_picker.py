#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Cosmoon XYE选股神器 (Cosmoon XYE Stock Picker)
============================================

基于Cosmoon XYE策略的智能选股和回测系统
支持多种数据源：Yahoo Finance、本地数据库、CSV文件等

🌟 核心特点：
1. 🎯 Cosmoon原创XYE策略
2. 🔍 智能选股评分系统
3. 💰 专业回测引擎
4. 📊 多维度技术分析
5. 🏆 投资组合构建
6. 📋 Excel报告导出

🧮 XYE指标体系：
- Y值: 价格在20日区间的相对位置 (0-1)
- X值: 基于MFI的资金流强度指标 (0-1)  
- E值: Cosmoon公式 E = (8x - 3)y - 3x + 1

作者: Cosmoon NG
版本: v2.0
日期: 2025年7月
"""

import yfinance as yf
import sqlite3
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import matplotlib.pyplot as plt
from scipy import stats
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class CosmoonXYEStockPicker:
    """Cosmoon XYE选股神器"""
    
    def __init__(self, initial_capital=10000, monthly_addition=3000):
        """初始化Cosmoon XYE选股系统"""
        self.initial_capital = initial_capital
        self.monthly_addition = monthly_addition
        
        # Cosmoon XYE策略参数
        self.take_profit_long = 0.025   # 多头止盈2.5%
        self.stop_loss_long = 0.015     # 多头止损1.5%
        self.take_profit_short = 0.015  # 空头止盈1.5%
        self.stop_loss_short = 0.025    # 空头止损2.5%
        
        # 选股筛选参数
        self.min_volume = 1000000       # 最小成交量
        self.min_price = 1.0           # 最小价格
        self.max_price = 1000.0        # 最大价格
        
        # 系统状态
        self.position = 0
        self.current_price = 0
        
        print("🎯 Cosmoon XYE选股神器已启动")
        print("💡 基于Cosmoon原创XYE策略的智能选股系统")
        
    def get_stock_data(self, symbol, period="2y", source="yahoo"):
        """获取股票数据"""
        print(f"\n📊 获取 {symbol} 数据...")
        
        try:
            if source == "yahoo":
                # 从Yahoo Finance获取数据
                stock = yf.Ticker(symbol)
                hist = stock.history(period=period)
                
                if hist.empty:
                    print(f"❌ 无法获取 {symbol} 的数据")
                    return None
                
                # 转换为标准格式
                df = pd.DataFrame({
                    'date': hist.index,
                    'open': hist['Open'],
                    'high': hist['High'],
                    'low': hist['Low'],
                    'close': hist['Close'],
                    'volume': hist['Volume']
                })
                
                # 获取股票信息
                info = stock.info
                company_name = info.get('longName', symbol)
                print(f"✅ 成功获取 {company_name} ({symbol}) 数据")
                print(f"   数据期间: {df['date'].min().date()} 至 {df['date'].max().date()}")
                print(f"   总计: {len(df)} 条记录")
                
            elif source == "database":
                # 从本地数据库获取数据
                conn = sqlite3.connect('stock_data.db')
                df = pd.read_sql(f"""
                    SELECT date, open, high, low, close, volume
                    FROM {symbol.replace('.', '_').replace('-', '_')}
                    ORDER BY date
                """, conn)
                conn.close()
                df['date'] = pd.to_datetime(df['date'])
                
            elif source == "csv":
                # 从CSV文件获取数据
                df = pd.read_csv(f"{symbol}.csv")
                df['date'] = pd.to_datetime(df['date'])
                
            else:
                raise ValueError(f"不支持的数据源: {source}")
            
            # 数据清理
            df = df.dropna()
            df = df.sort_values('date').reset_index(drop=True)
            
            return df
            
        except Exception as e:
            print(f"❌ 获取数据失败: {e}")
            return None
    
    def calculate_cosmoon_xye_indicators(self, df):
        """计算Cosmoon XYE核心指标"""
        print("🧮 计算Cosmoon XYE技术指标...")
        
        df = df.copy()
        
        # 1. 计算Y指标 (价格在区间的位置强度)
        window = 20
        df['high_20'] = df['high'].rolling(window).max()
        df['low_20'] = df['low'].rolling(window).min()
        df['y_value'] = (df['close'] - df['low_20']) / (df['high_20'] - df['low_20'])
        df['y_value'] = df['y_value'].fillna(0.5).clip(0, 1)
        
        # 2. 计算X指标 (资金流强度)
        df['typical_price'] = (df['high'] + df['low'] + df['close']) / 3
        df['money_flow'] = df['typical_price'] * df['volume']
        df['price_change'] = df['typical_price'].diff()
        
        # 正负资金流
        df['positive_mf'] = np.where(df['price_change'] > 0, df['money_flow'], 0)
        df['negative_mf'] = np.where(df['price_change'] < 0, df['money_flow'], 0)
        
        # 14日资金流比率
        period = 14
        df['positive_mf_14'] = df['positive_mf'].rolling(period).sum()
        df['negative_mf_14'] = df['negative_mf'].rolling(period).sum()
        df['money_flow_ratio'] = df['positive_mf_14'] / (df['negative_mf_14'] + 1e-10)
        
        # MFI和X值
        df['mfi'] = 100 - (100 / (1 + df['money_flow_ratio']))
        df['x_value'] = df['mfi'] / 100  # 归一化到0-1
        
        # 3. 计算E指标 (Cosmoon核心公式)
        df['e_value'] = (8 * df['x_value'] - 3) * df['y_value'] - 3 * df['x_value'] + 1
        
        # 4. 计算回归线 (趋势判断)
        df['i'] = range(1, len(df) + 1)
        if len(df) > 60:
            slope, intercept, r_value, p_value, std_err = stats.linregress(
                df['i'], df['close']
            )
            df['regression_line'] = intercept + slope * df['i']
            df['price_position'] = (df['close'] - df['regression_line']) / df['regression_line']
        else:
            df['regression_line'] = df['close'].rolling(20).mean()
            df['price_position'] = (df['close'] - df['regression_line']) / df['regression_line']
        
        # 5. 辅助技术指标
        df['rsi'] = self.calculate_rsi(df['close'])
        df['ma20'] = df['close'].rolling(20).mean()
        df['ma50'] = df['close'].rolling(50).mean()
        
        print("✅ Cosmoon XYE指标计算完成")
        return df
    
    def calculate_rsi(self, prices, period=14):
        """计算RSI相对强弱指标"""
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / (loss + 1e-10)
        rsi = 100 - (100 / (1 + rs))
        return rsi
    
    def generate_cosmoon_signals(self, df):
        """生成Cosmoon XYE交易信号"""
        print("🎯 生成Cosmoon XYE交易信号...")
        
        df = df.copy()
        signals = []
        
        for i, row in df.iterrows():
            if i < 60:  # 需要足够的历史数据
                signals.append(0)
                continue
            
            # 检查数据完整性
            if (pd.isna(row['y_value']) or pd.isna(row['x_value']) or 
                pd.isna(row['e_value']) or pd.isna(row['price_position'])):
                signals.append(0)
                continue
            
            # Cosmoon XYE核心策略
            # 🟢 多头信号条件
            if (row['e_value'] > 0 and 
                row['x_value'] > 0.45 and 
                row['y_value'] > 0.45 and
                row['price_position'] < 0):  # 价格低于回归线
                signals.append(1)
            
            # 🔴 空头信号条件
            elif ((row['y_value'] < 0.3 or row['x_value'] < 0.3 or
                   (row['x_value'] > 0.45 and row['y_value'] < 0.35) or
                   (row['x_value'] < 0.45 and row['y_value'] > 0.35)) and
                  row['price_position'] > 0):  # 价格高于回归线
                signals.append(-1)
            
            else:
                signals.append(0)
        
        df['signal'] = signals
        
        # 统计信号分布
        signal_counts = df['signal'].value_counts()
        print(f"📊 Cosmoon XYE信号统计:")
        for signal, count in signal_counts.items():
            signal_name = "🟢 做多" if signal == 1 else "🔴 做空" if signal == -1 else "⚪ 观望"
            print(f"   {signal_name}: {count}次 ({count/len(df)*100:.1f}%)")
        
        return df
    
    def calculate_cosmoon_score(self, analysis):
        """计算Cosmoon XYE综合评分 (0-100分)"""
        score = 50  # 基础分
        
        # Y值评分 (0-25分) - 价格位置强度
        y_val = analysis['y_value']
        if y_val > 0.8:
            score += 25
        elif y_val > 0.6:
            score += 20
        elif y_val > 0.4:
            score += 15
        elif y_val > 0.2:
            score += 10
        else:
            score -= 10
        
        # X值评分 (0-25分) - 资金流强度
        x_val = analysis['x_value']
        if x_val > 0.7:
            score += 25
        elif x_val > 0.5:
            score += 20
        elif x_val > 0.3:
            score += 15
        elif x_val > 0.1:
            score += 10
        else:
            score -= 10
        
        # E值评分 (0-25分) - Cosmoon核心指标
        e_val = analysis['e_value']
        if e_val > 1.0:
            score += 25
        elif e_val > 0.5:
            score += 20
        elif e_val > 0:
            score += 15
        elif e_val > -0.5:
            score += 5
        else:
            score -= 15
        
        # 信号评分 (0-15分) - 交易信号强度
        signal = analysis['signal']
        if signal == 1:
            score += 15
        elif signal == -1:
            score -= 15
        
        # RSI评分 (0-10分) - 超买超卖状态
        rsi = analysis['rsi']
        if 30 < rsi < 70:
            score += 10
        elif 20 < rsi < 80:
            score += 5
        elif rsi < 20 or rsi > 80:
            score -= 5
        
        return max(0, min(100, score))
    
    def analyze_stock(self, symbol, period="2y", source="yahoo"):
        """使用Cosmoon XYE策略分析单只股票"""
        print(f"\n🔍 Cosmoon XYE分析: {symbol}")
        print("=" * 60)
        
        # 获取数据
        df = self.get_stock_data(symbol, period, source)
        if df is None:
            return None
        
        # 计算Cosmoon XYE指标
        df = self.calculate_cosmoon_xye_indicators(df)
        df = self.generate_cosmoon_signals(df)
        
        # 当前状态分析
        latest = df.iloc[-1]
        
        analysis = {
            'symbol': symbol,
            'date': latest['date'],
            'price': latest['close'],
            'y_value': latest['y_value'],
            'x_value': latest['x_value'],
            'e_value': latest['e_value'],
            'signal': latest['signal'],
            'rsi': latest['rsi'],
            'price_position': latest['price_position'],
            'volume': latest['volume'],
            'data': df
        }
        
        # Cosmoon XYE评分
        score = self.calculate_cosmoon_score(analysis)
        analysis['score'] = score
        
        # 显示分析结果
        self.display_cosmoon_analysis(analysis)
        
        return analysis
    
    def display_cosmoon_analysis(self, analysis):
        """显示Cosmoon XYE分析结果"""
        print(f"\n📊 {analysis['symbol']} Cosmoon XYE分析结果:")
        print(f"   📅 分析日期: {analysis['date'].strftime('%Y-%m-%d')}")
        print(f"   💰 当前价格: {analysis['price']:.2f}")
        print(f"   🎯 Y值 (位置强度): {analysis['y_value']:.4f}")
        print(f"   💪 X值 (资金流强度): {analysis['x_value']:.4f}")
        print(f"   ⚡ E值 (Cosmoon核心): {analysis['e_value']:.4f}")
        print(f"   📈 RSI: {analysis['rsi']:.1f}")
        print(f"   📊 价格位置: {analysis['price_position']:.4f}")
        
        # Cosmoon XYE信号解读
        signal = analysis['signal']
        if signal == 1:
            print(f"   🟢 Cosmoon信号: 强烈买入 ⭐⭐⭐")
        elif signal == -1:
            print(f"   🔴 Cosmoon信号: 强烈卖出 ⭐⭐⭐")
        else:
            print(f"   ⚪ Cosmoon信号: 观望等待")
        
        # Cosmoon XYE评分
        score = analysis['score']
        if score >= 85:
            grade = "🌟 卓越 (强烈推荐)"
        elif score >= 75:
            grade = "⭐ 优秀 (推荐)"
        elif score >= 65:
            grade = "👍 良好 (可考虑)"
        elif score >= 50:
            grade = "👌 一般 (谨慎)"
        elif score >= 35:
            grade = "⚠️ 较差 (回避)"
        else:
            grade = "❌ 很差 (强烈回避)"
        
        print(f"   🏆 Cosmoon评分: {score:.0f}分 ({grade})")

def main():
    """主函数 - Cosmoon XYE选股神器"""
    print("🎯 Cosmoon XYE选股神器 (Cosmoon XYE Stock Picker)")
    print("基于Cosmoon XYE策略的智能选股系统")
    print("=" * 80)
    
    # 创建Cosmoon XYE选股器实例
    picker = CosmoonXYEStockPicker(initial_capital=10000, monthly_addition=1000)
    
    while True:
        print("\n🔧 Cosmoon XYE功能菜单:")
        print("1. 📊 XYE单股分析")
        print("2. 🔍 XYE批量筛选")
        print("3. 💰 XYE策略回测")
        print("4. 🏆 港股XYE排行")
        print("5. 🌍 美股XYE排行")
        print("6. 📋 XYE投资组合")
        print("7. 💾 导出XYE报告")
        print("0. 🚪 退出程序")
        
        choice = input("\n请输入选择 (0-7): ").strip()
        
        if choice == "0":
            print("👋 感谢使用Cosmoon XYE选股神器！")
            print("💡 愿Cosmoon XYE策略为您的投资带来丰厚回报！")
            break
        
        elif choice == "1":
            symbol = input("请输入股票代码 (如: 0700.HK, AAPL, TSLA): ").strip().upper()
            if symbol:
                result = picker.analyze_stock(symbol)
                
                if result and input("\n是否运行XYE策略回测? (y/n): ").lower() == 'y':
                    # 这里可以调用回测功能
                    print("🚀 XYE策略回测功能开发中...")
        
        elif choice == "4":
            print("\n🏆 港股Cosmoon XYE排行榜...")
            hk_stocks = [
                "0700.HK", "0941.HK", "1299.HK", "2318.HK", "0005.HK",
                "0388.HK", "1398.HK", "0939.HK", "1810.HK", "0883.HK",
                "0023.HK", "0011.HK", "0016.HK", "0002.HK", "0003.HK"
            ]
            
            print("🔍 正在使用Cosmoon XYE策略分析港股...")
            results = []
            
            for i, symbol in enumerate(hk_stocks, 1):
                print(f"\n进度: {i}/{len(hk_stocks)} - 分析 {symbol}")
                try:
                    analysis = picker.analyze_stock(symbol, period="1y")
                    if analysis:
                        results.append(analysis)
                except Exception as e:
                    print(f"❌ {symbol} 分析失败: {e}")
            
            # 按Cosmoon XYE评分排序
            if results:
                results.sort(key=lambda x: x['score'], reverse=True)
                
                print(f"\n🏆 港股Cosmoon XYE排行榜 (共{len(results)}只)")
                print("=" * 100)
                print(f"{'排名':<4} {'代码':<12} {'价格':<8} {'Y值':<8} {'X值':<8} {'E值':<8} {'XYE信号':<8} {'评分':<6}")
                print("-" * 100)
                
                for i, result in enumerate(results, 1):
                    signal_text = "买入" if result['signal'] == 1 else "卖出" if result['signal'] == -1 else "观望"
                    
                    print(f"{i:<4} {result['symbol']:<12} {result['price']:<8.2f} "
                          f"{result['y_value']:<8.4f} {result['x_value']:<8.4f} "
                          f"{result['e_value']:<8.4f} {signal_text:<8} {result['score']:<6.0f}")
                
                # Cosmoon XYE推荐
                buy_signals = [r for r in results if r['signal'] == 1 and r['score'] >= 75]
                if buy_signals:
                    print(f"\n🎯 Cosmoon XYE强烈推荐:")
                    for stock in buy_signals:
                        print(f"   ⭐ {stock['symbol']}: {stock['score']:.0f}分 - {stock['price']:.2f} (E值: {stock['e_value']:.2f})")
        
        else:
            print("🚀 该功能正在开发中，敬请期待...")
            print("💡 当前版本专注于Cosmoon XYE核心分析功能")

if __name__ == "__main__":
    main()
