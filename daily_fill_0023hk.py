#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd
import yfinance as yf
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

def fill_daily_record():
    """每日下午5点填充0023.HK交易记录"""
    
    print("香港交易所交易时间结束 下午5:00")
    print("开始填充今日0023.HK交易记录")
    print("=" * 50)
    
    try:
        # 获取0023.HK数据
        ticker = yf.Ticker("0023.HK")
        hist = ticker.history(period="2d")
        
        if not hist.empty:
            latest = hist.iloc[-1]
            today = datetime.now()
            
            # 今日交易记录
            record = {
                '交易日期': today.strftime('%Y-%m-%d'),
                '交易类型': '观察',
                '交易方向': '无',
                '交易价格': round(latest['Close'], 2),
                '持仓数量': 0,
                '交易金额': 0.00,
                '手续费': 0.00,
                '净交易额': 0.00,
                '持仓成本': 0.00,
                '当前市值': 0.00,
                '浮动盈亏': 0.00,
                '实现盈亏': 0.00,
                '累计盈亏': 0.00,
                '账户余额': 10000.00,
                '总资产': 10000.00,
                '收益率': 0.00,
                '累计收益率': 0.00,
                'Y值': 0.5000,
                'X值': 0.5000,
                'E值': 0.0000,
                '信号强度': '观望',
                '风险等级': '低风险',
                '备注': f'市场观察收盘价{latest["Close"]:.2f}港元'
            }
            
            print(f"填充日期: {today.strftime('%Y年%m月%d日')}")
            print(f"股票代码: 0023.HK 东亚银行")
            print(f"开盘价: {latest['Open']:.2f} 港元")
            print(f"最高价: {latest['High']:.2f} 港元")
            print(f"最低价: {latest['Low']:.2f} 港元")
            print(f"收盘价: {latest['Close']:.2f} 港元")
            print(f"成交量: {latest['Volume']:,.0f}")
            print(f"交易状态: {record['交易类型']}")
            print(f"总资产: {record['总资产']:,.2f} 港元")
            
            # 保存到CSV
            df = pd.DataFrame([record])
            filename = f"交易记录0023HK_{today.strftime('%Y%m%d')}.csv"
            df.to_csv(filename, index=False, encoding='utf-8-sig')
            
            print(f"记录已保存到: {filename}")
            print("下次填充: 明日下午5:00")
            print("今日交易记录填充完成")
            
            return record
            
        else:
            print("无法获取市场数据")
            return None
            
    except Exception as e:
        print(f"填充失败: {e}")
        return None

if __name__ == "__main__":
    fill_daily_record()
