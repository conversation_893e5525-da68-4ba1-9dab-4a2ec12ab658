import javax.swing.*;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;

public class GUI_Demo extends JFrame {
    private JTextField priceField;
    private JRadioButton longRadio, shortRadio;
    private JLabel resultLabel;
    
    public GUI_Demo() {
        setTitle("交易计算器 GUI演示");
        setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
        setLayout(new GridLayout(5, 2, 10, 10));
        
        // 输入价格
        add(new JLabel("入场价格:"));
        priceField = new JTextField();
        add(priceField);
        
        // 交易方向
        add(new JLabel("交易方向:"));
        JPanel radioPanel = new JPanel();
        longRadio = new JRadioButton("多头", true);
        shortRadio = new JRadioButton("空头");
        ButtonGroup group = new ButtonGroup();
        group.add(longRadio);
        group.add(shortRadio);
        radioPanel.add(longRadio);
        radioPanel.add(shortRadio);
        add(radioPanel);
        
        // 计算按钮
        JButton calcButton = new JButton("计算");
        calcButton.addActionListener(new ActionListener() {
            public void actionPerformed(ActionEvent e) {
                calculate();
            }
        });
        add(calcButton);
        
        // 清除按钮
        JButton clearButton = new JButton("清除");
        clearButton.addActionListener(e -> {
            priceField.setText("");
            resultLabel.setText("结果将显示在这里");
        });
        add(clearButton);
        
        // 结果显示
        add(new JLabel("计算结果:"));
        resultLabel = new JLabel("结果将显示在这里");
        resultLabel.setBorder(BorderFactory.createLoweredBevelBorder());
        add(resultLabel);
        
        pack();
        setLocationRelativeTo(null);
    }
    
    private void calculate() {
        try {
            double price = Double.parseDouble(priceField.getText());
            boolean isLong = longRadio.isSelected();
            
            double tp, sl;
            if (isLong) {
                tp = price * 1.0012;
                sl = price * 0.9994;
            } else {
                tp = price * 0.9988;
                sl = price * 1.0006;
            }
            
            String result = String.format("<html>%s<br>止盈: %.3f<br>止损: %.3f</html>", 
                isLong ? "多头" : "空头", tp, sl);
            resultLabel.setText(result);
            
        } catch (NumberFormatException e) {
            JOptionPane.showMessageDialog(this, "请输入有效数字！");
        }
    }
    
    public static void main(String[] args) {
        SwingUtilities.invokeLater(() -> {
            new GUI_Demo().setVisible(true);
        });
    }
}
