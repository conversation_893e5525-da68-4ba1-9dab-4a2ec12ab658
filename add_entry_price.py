#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
添加入场价格字段到Excel表格
==========================

入场价格 = 开仓时的价格
这是计算浮动盈亏的基准价格

作者: Cosmoon NG
"""

import pandas as pd
import mysql.connector
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

def add_entry_price_field():
    """添加入场价格字段"""
    
    excel_file = "交易记录追踪0023HK.xlsx"
    
    print("🎯 添加入场价格字段...")
    
    try:
        # 读取Excel文件
        df = pd.read_excel(excel_file)
        print(f"📋 当前记录数: {len(df)}")
        print(f"📊 当前列数: {len(df.columns)}")
        
        # 添加入场价格字段
        if '入场价格' not in df.columns:
            # 在交易价格后面插入入场价格
            trade_price_idx = df.columns.get_loc('交易价格')
            df.insert(trade_price_idx + 1, '入场价格', 0.00)
            print("➕ 添加 入场价格 列")
        else:
            print("✅ 入场价格 列已存在")
        
        # 获取当前交易参数
        current_price = 12.14  # 当前价格
        entry_price = current_price  # 入场价格等于开仓时的价格
        
        # 获取数据库技术指标
        try:
            config = {
                'host': 'localhost',
                'port': 3306,
                'user': 'root',
                'password': '12345678',
                'database': 'finance'
            }
            connection = mysql.connector.connect(**config)
            cursor = connection.cursor()
            query = "SELECT Y_Value, X_Value, E_Value, Full_Y, MoneyFlowRatio FROM eab_0023hk ORDER BY Date DESC LIMIT 1"
            cursor.execute(query)
            result = cursor.fetchone()
            cursor.close()
            connection.close()
            
            if result:
                y_val, x_val, e_val, full_y, mfr = result
                print(f"📈 数据库数据获取成功")
            else:
                y_val, x_val, e_val, full_y, mfr = 0.2069, 0.3211, -0.0525, 0.4586, 0.4729
                print(f"⚠️ 使用默认数据")
        except:
            y_val, x_val, e_val, full_y, mfr = 0.2069, 0.3211, -0.0525, 0.4586, 0.4729
            print(f"⚠️ 数据库连接失败，使用默认数据")
        
        # 计算MyE
        mye_value = 8 * mfr * full_y - 3 * mfr - 3 * full_y + 1
        
        # 用户实际参数
        total_capital = 2500.00
        max_shares = 200
        trade_direction = "空头"
        
        # 计算交易数据
        trade_amount = current_price * max_shares
        commission = trade_amount * 0.001
        account_balance = total_capital - trade_amount - commission
        current_market_value = current_price * max_shares
        
        # 计算浮动盈亏 (基于入场价格)
        if trade_direction == "空头":
            # 空头: 入场价格高于当前价格为盈利
            unrealized_pnl = (entry_price - current_price) * max_shares
        else:
            # 多头: 当前价格高于入场价格为盈利
            unrealized_pnl = (current_price - entry_price) * max_shares
        
        # 计算止盈止损价格
        take_profit_rate = 0.012  # 1.2%
        stop_loss_rate = 0.006    # 0.6%
        
        if trade_direction == "空头":
            take_profit_price = entry_price * (1 - take_profit_rate)
            stop_loss_price = entry_price * (1 + stop_loss_rate)
        else:
            take_profit_price = entry_price * (1 + take_profit_rate)
            stop_loss_price = entry_price * (1 - stop_loss_rate)
        
        # 计算总资产和收益率
        total_assets = account_balance + current_market_value + unrealized_pnl
        daily_return = (total_assets - total_capital) / total_capital * 100
        
        # 更新最新记录的所有字段
        latest_idx = len(df) - 1
        
        # 更新字段
        df.iloc[latest_idx, df.columns.get_loc('交易日期')] = datetime.now().strftime('%Y-%m-%d')
        df.iloc[latest_idx, df.columns.get_loc('交易类型')] = '开仓'
        df.iloc[latest_idx, df.columns.get_loc('交易方向')] = trade_direction
        df.iloc[latest_idx, df.columns.get_loc('交易价格')] = current_price
        df.iloc[latest_idx, df.columns.get_loc('入场价格')] = entry_price
        
        # 确保其他字段存在并更新
        if '止盈价' in df.columns:
            df.iloc[latest_idx, df.columns.get_loc('止盈价')] = take_profit_price
        if '止损价' in df.columns:
            df.iloc[latest_idx, df.columns.get_loc('止损价')] = stop_loss_price
        
        df.iloc[latest_idx, df.columns.get_loc('持仓数量')] = max_shares
        df.iloc[latest_idx, df.columns.get_loc('交易金额')] = trade_amount
        df.iloc[latest_idx, df.columns.get_loc('手续费')] = commission
        df.iloc[latest_idx, df.columns.get_loc('净交易额')] = trade_amount - commission
        df.iloc[latest_idx, df.columns.get_loc('持仓成本')] = entry_price
        df.iloc[latest_idx, df.columns.get_loc('当前市值')] = current_market_value
        df.iloc[latest_idx, df.columns.get_loc('浮动盈亏')] = unrealized_pnl
        df.iloc[latest_idx, df.columns.get_loc('实现盈亏')] = 0.00
        df.iloc[latest_idx, df.columns.get_loc('累计盈亏')] = unrealized_pnl
        df.iloc[latest_idx, df.columns.get_loc('账户余额')] = account_balance
        df.iloc[latest_idx, df.columns.get_loc('总资产')] = total_assets
        df.iloc[latest_idx, df.columns.get_loc('收益率')] = daily_return
        df.iloc[latest_idx, df.columns.get_loc('累计收益率')] = daily_return
        
        # 技术指标
        df.iloc[latest_idx, df.columns.get_loc('Y值')] = y_val
        if 'Full_Y' in df.columns:
            df.iloc[latest_idx, df.columns.get_loc('Full_Y')] = full_y
        df.iloc[latest_idx, df.columns.get_loc('X值')] = x_val
        if 'MoneyFlowRatio' in df.columns:
            df.iloc[latest_idx, df.columns.get_loc('MoneyFlowRatio')] = mfr
        df.iloc[latest_idx, df.columns.get_loc('E值')] = e_val
        if 'MyE' in df.columns:
            df.iloc[latest_idx, df.columns.get_loc('MyE')] = mye_value
        
        df.iloc[latest_idx, df.columns.get_loc('信号强度')] = '强烈卖出'
        df.iloc[latest_idx, df.columns.get_loc('风险等级')] = '高风险'
        df.iloc[latest_idx, df.columns.get_loc('备注')] = f'完整记录含入场价格 {trade_direction}开仓 入场价{entry_price:.2f}港元'
        
        # 保存Excel文件
        df.to_excel(excel_file, index=False)
        
        print(f"✅ Excel文件已更新: {excel_file}")
        print(f"📊 总列数: {len(df.columns)}")
        
        # 显示关键价格信息
        print(f"\n🎯 价格信息:")
        print(f"   交易价格: {current_price:.2f} 港元")
        print(f"   入场价格: {entry_price:.2f} 港元")
        print(f"   止盈价: {take_profit_price:.2f} 港元")
        print(f"   止损价: {stop_loss_price:.2f} 港元")
        
        # 显示盈亏信息
        print(f"\n💰 盈亏信息:")
        print(f"   浮动盈亏: {unrealized_pnl:.2f} 港元")
        print(f"   计算基准: 入场价格 {entry_price:.2f} 港元")
        print(f"   当前价格: {current_price:.2f} 港元")
        print(f"   价格差异: {current_price - entry_price:.2f} 港元")
        
        # 显示技术指标
        print(f"\n📈 技术指标:")
        print(f"   Y值: {y_val:.4f}")
        print(f"   Full_Y: {full_y:.4f}")
        print(f"   X值: {x_val:.4f}")
        print(f"   MoneyFlowRatio: {mfr:.4f}")
        print(f"   E值: {e_val:.4f}")
        print(f"   MyE: {mye_value:.4f}")
        
        # 显示价格相关字段的位置
        print(f"\n📋 价格相关字段:")
        price_fields = ['交易价格', '入场价格', '止盈价', '止损价', '持仓成本']
        for field in price_fields:
            if field in df.columns:
                idx = df.columns.get_loc(field) + 1
                marker = "🆕" if field == '入场价格' else "📊"
                print(f"   {idx:2d}. {marker} {field}")
        
        return True
        
    except Exception as e:
        print(f"❌ 操作失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🎯 添加入场价格字段")
    print("=" * 50)
    
    try:
        success = add_entry_price_field()
        
        if success:
            print(f"\n✅ 入场价格字段添加完成！")
            print(f"🎯 入场价格 = 开仓时的价格")
            print(f"💰 浮动盈亏 = (当前价格 - 入场价格) × 持仓数量")
            print(f"📊 这是计算盈亏的重要基准价格")
        else:
            print(f"\n❌ 入场价格字段添加失败")
            
    except Exception as e:
        print(f"❌ 系统错误: {e}")

if __name__ == "__main__":
    main()
