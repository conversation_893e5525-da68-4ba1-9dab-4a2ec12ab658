#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证新的controller和Full_Y逻辑
============================
验证新逻辑是否正确实现
"""

import mysql.connector
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

def verify_new_controller_logic():
    """验证新的controller和Full_Y逻辑"""
    
    # 数据库配置
    db_config = {
        'host': 'localhost',
        'database': 'finance',
        'user': 'root',
        'password': '12345678',
        'charset': 'utf8mb4'
    }
    
    try:
        print("🔍 验证新的controller和Full_Y逻辑")
        print("=" * 60)
        print(f"📅 验证时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        # 连接数据库
        connection = mysql.connector.connect(**db_config)
        cursor = connection.cursor()
        print(f"✅ 成功连接到数据库: {db_config['host']}/{db_config['database']}")
        
        table_name = 'stock_600887_ss'
        
        # 1. 基本统计信息
        print(f"\n📊 {table_name} 新逻辑统计:")
        cursor.execute(f"""
            SELECT 
                COUNT(*) as total_rows,
                COUNT(controller) as controller_rows,
                COUNT(Full_Y) as full_y_rows,
                MIN(controller) as min_controller,
                MAX(controller) as max_controller,
                MIN(Full_Y) as min_full_y,
                MAX(Full_Y) as max_full_y,
                AVG(Full_Y) as avg_full_y
            FROM {table_name}
        """)
        
        stats = cursor.fetchone()
        print(f"   • 总记录数: {stats[0]}")
        print(f"   • controller记录数: {stats[1]}")
        print(f"   • Full_Y记录数: {stats[2]}")
        print(f"   • controller范围: {stats[3]} ~ {stats[4]}")
        print(f"   • Full_Y范围: {float(stats[5]):.6f} ~ {float(stats[6]):.6f}")
        print(f"   • Full_Y平均值: {float(stats[7]):.6f}")
        
        # 2. 验证前30行的计算逻辑
        print(f"\n📊 前30行新逻辑验证:")
        cursor.execute(f"""
            SELECT 
                ROW_NUMBER() OVER (ORDER BY date ASC) as row_num,
                date, 
                close,
                midprice,
                CASE WHEN (close - midprice) > 0 THEN 1 ELSE 0 END as should_increment,
                controller,
                Full_Y
            FROM {table_name} 
            WHERE controller IS NOT NULL
            ORDER BY date ASC 
            LIMIT 30
        """)
        
        results = cursor.fetchall()
        print("行号 | 日期          | 收盘价  | midprice | 应增? | controller | Full_Y     | 验证")
        print("-" * 90)
        
        expected_controller = 0
        for row in results:
            row_num = row[0]
            date_str = str(row[1])
            close_val = float(row[2]) if row[2] is not None else 0.0
            mid_val = float(row[3]) if row[3] is not None else 0.0
            should_increment = row[4]
            actual_controller = row[5]
            actual_full_y = float(row[6]) if row[6] is not None else 0.0
            
            # 手动计算预期值
            expected_controller += should_increment
            expected_full_y = expected_controller / row_num
            
            # 验证
            controller_ok = "✅" if actual_controller == expected_controller else "❌"
            full_y_ok = "✅" if abs(actual_full_y - expected_full_y) < 0.000001 else "❌"
            
            print(f"{row_num:4d} | {date_str} | {close_val:7.2f} | {mid_val:8.4f} | {should_increment:5d} | {actual_controller:10d} | {actual_full_y:10.6f} | {controller_ok}{full_y_ok}")
        
        # 3. 检查最新数据
        print(f"\n📊 最新20条数据:")
        cursor.execute(f"""
            SELECT 
                date, 
                close,
                midprice,
                ROUND(close - midprice, 4) as price_diff,
                CASE WHEN (close - midprice) > 0 THEN 'STRONG' ELSE 'WEAK' END as status,
                controller,
                Full_Y
            FROM {table_name} 
            WHERE controller IS NOT NULL
            ORDER BY date DESC 
            LIMIT 20
        """)
        
        latest_results = cursor.fetchall()
        print("日期          | 收盘价  | midprice | 价差     | 状态   | controller | Full_Y")
        print("-" * 80)
        for row in latest_results:
            date_str = str(row[0])
            close_val = float(row[1]) if row[1] is not None else 0.0
            mid_val = float(row[2]) if row[2] is not None else 0.0
            price_diff = float(row[3]) if row[3] is not None else 0.0
            status = row[4]
            controller_val = row[5] if row[5] is not None else 0
            full_y_val = float(row[6]) if row[6] is not None else 0.0
            print(f"{date_str} | {close_val:7.2f} | {mid_val:8.4f} | {price_diff:8.4f} | {status:6s} | {controller_val:10d} | {full_y_val:10.6f}")
        
        # 4. 验证controller的单调性
        print(f"\n🔍 验证controller单调性:")
        cursor.execute(f"""
            SELECT COUNT(*) as violations
            FROM (
                SELECT 
                    controller,
                    LAG(controller) OVER (ORDER BY date ASC) as prev_controller
                FROM {table_name}
                WHERE controller IS NOT NULL
            ) t
            WHERE controller < prev_controller
        """)
        
        violations = cursor.fetchone()[0]
        if violations == 0:
            print("   ✅ controller单调递增，计算正确")
        else:
            print(f"   ⚠️ 发现{violations}处controller非递增")
        
        # 5. 手动验证几个关键点
        print(f"\n🔍 手动验证关键点:")
        
        # 验证最后一行
        cursor.execute(f"""
            SELECT 
                COUNT(*) as total_rows,
                SUM(CASE WHEN (close - midprice) > 0 THEN 1 ELSE 0 END) as strong_count,
                MAX(controller) as final_controller,
                MAX(Full_Y) as final_full_y
            FROM {table_name}
            WHERE controller IS NOT NULL
        """)
        
        final_check = cursor.fetchone()
        total_rows = final_check[0]
        strong_count = final_check[1]
        final_controller = final_check[2]
        final_full_y = float(final_check[3])
        
        print(f"   • 总行数: {total_rows}")
        print(f"   • 强势天数: {strong_count}")
        print(f"   • 最终controller: {final_controller}")
        print(f"   • 最终Full_Y: {final_full_y:.6f}")
        print(f"   • 预期Full_Y: {final_controller/total_rows:.6f}")
        
        if final_controller == strong_count:
            print("   ✅ controller累积计数正确")
        else:
            print(f"   ❌ controller累积计数错误 (期望{strong_count}, 实际{final_controller})")
        
        if abs(final_full_y - (final_controller/total_rows)) < 0.000001:
            print("   ✅ Full_Y = controller/行号 公式正确")
        else:
            print("   ❌ Full_Y公式计算错误")
        
        # 6. 测试sp_updatecontroller调用
        print(f"\n🧪 测试sp_updatecontroller调用:")
        args = [table_name, 0]
        result = cursor.callproc('sp_updatecontroller', args)
        
        # 显示OUT参数结果
        k_value_result = result[1]
        print(f"   📊 返回的k值: {k_value_result}")
        print(f"   📊 预期k值: {final_controller/total_rows:.6f}")
        
        if abs(float(k_value_result) - (final_controller/total_rows)) < 0.000001:
            print("   ✅ k值计算正确")
        else:
            print("   ❌ k值计算错误")
        
        # 7. 年度趋势分析
        print(f"\n📊 年度强势比例趋势:")
        cursor.execute(f"""
            SELECT 
                YEAR(date) as year,
                COUNT(*) as total_days,
                MAX(controller) - MIN(controller) as year_strong_days,
                MIN(Full_Y) as year_start_ratio,
                MAX(Full_Y) as year_end_ratio,
                ROUND(MAX(Full_Y) - MIN(Full_Y), 6) as ratio_increase
            FROM {table_name}
            WHERE controller IS NOT NULL
            GROUP BY YEAR(date)
            ORDER BY year DESC
            LIMIT 10
        """)
        
        yearly_stats = cursor.fetchall()
        print("年份 | 总天数 | 强势天数 | 年初比例 | 年末比例 | 比例增长")
        print("-" * 60)
        for row in yearly_stats:
            year = row[0]
            total_days = row[1]
            strong_days = row[2] if row[2] is not None else 0
            start_ratio = float(row[3]) if row[3] is not None else 0.0
            end_ratio = float(row[4]) if row[4] is not None else 0.0
            ratio_increase = float(row[5]) if row[5] is not None else 0.0
            print(f"{year} | {total_days:6d} | {strong_days:8d} | {start_ratio:8.6f} | {end_ratio:8.6f} | {ratio_increase:8.6f}")
        
        # 8. 生成使用示例
        print(f"\n📝 新逻辑使用示例:")
        print("=" * 60)
        
        print("💡 新逻辑说明:")
        print("   • controller: 累积强势次数，当(close-midprice>0)时递增")
        print("   • Full_Y: controller/行号，累积强势比例")
        print("   • k值: 最终controller/总行数，整体强势比例")
        
        print("\n1️⃣ 完整更新:")
        print(f"   CALL sp_updatecontroller('{table_name}', @k_value);")
        print("   SELECT @k_value AS k_result;")
        
        print("\n2️⃣ 查看强势比例趋势:")
        print(f"""   SELECT date, close, midprice,
          ROUND(close - midprice, 4) as price_diff,
          CASE WHEN (close - midprice) > 0 THEN 'STRONG' ELSE 'WEAK' END as status,
          controller, Full_Y
   FROM {table_name} 
   ORDER BY date DESC 
   LIMIT 10;""")
        
        print("\n3️⃣ 分析强势比例分布:")
        print(f"""   SELECT 
       CASE 
           WHEN Full_Y < 0.3 THEN '低比例(<30%)'
           WHEN Full_Y < 0.4 THEN '中低比例(30-40%)'
           WHEN Full_Y < 0.5 THEN '中等比例(40-50%)'
           WHEN Full_Y < 0.6 THEN '中高比例(50-60%)'
           ELSE '高比例(>60%)'
       END as ratio_range,
       COUNT(*) as days,
       AVG(close) as avg_close
   FROM {table_name}
   WHERE Full_Y IS NOT NULL
   GROUP BY 1
   ORDER BY MIN(Full_Y);""")
        
        # 关闭连接
        cursor.close()
        connection.close()
        
        print("\n✅ 新逻辑验证完成!")
        print("🎉 controller和Full_Y的新逻辑工作正常")
        
    except mysql.connector.Error as e:
        print(f"❌ 验证失败: {e}")
    except Exception as e:
        print(f"❌ 验证失败: {e}")

if __name__ == "__main__":
    verify_new_controller_logic()
