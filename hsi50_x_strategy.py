#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
HSI50 X指标策略
==============
基于reference.py，使用X = 1 - Full_Y作为核心指标

特点：
1. X = 1 - Full_Y (弱势比例指标)
2. X高位做多，X低位做空
3. 结合y_probability增强信号
4. 采用凯利公式优化仓位
5. 每月复利加入3000

基于Cosmoon NG的reference.py改进
"""

import mysql.connector
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import matplotlib.pyplot as plt
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

class HSI50XStrategy:
    def __init__(self):
        """初始化回测系统"""
        # 数据库配置
        self.db_config = {
            'host': 'localhost',
            'database': 'finance',
            'user': 'root',
            'password': '12345678',
            'charset': 'utf8mb4'
        }

        # 策略参数 (基于reference.py，调整为更实用)
        self.initial_capital = 30000      # 初始资金
        self.monthly_addition = 3000      # 每月追加资金
        self.take_profit_long = 0.06      # 多头止盈 6%
        self.stop_loss_long = 0.03        # 多头止损 3%
        self.take_profit_short = 0.04     # 空头止盈 4%
        self.stop_loss_short = 0.06       # 空头止损 6%
        self.kelly_fraction = 0.25        # 凯利公式分数 (保守)

        # X指标策略参数 (基于实际数据调整)
        self.x_high_threshold = 0.476     # X高位阈值 (做多信号) - 75%分位数
        self.x_low_threshold = 0.474      # X低位阈值 (做空信号) - 25%分位数
        self.x_change_threshold = 0.00002 # X变化阈值

        # 状态变量
        self.position = 0                 # 当前持仓
        self.current_price = 0            # 当前持仓价格
        self.trades = []
        self.equity_curve = []

    def load_data(self):
        """从新数据库加载数据"""
        print("\n1. 加载HSI50数据 (计算X指标)...")
        try:
            connection = mysql.connector.connect(**self.db_config)

            # 加载数据
            query = """
                SELECT Date, Open, High, Low, Close, Volume,
                       ma_20, ma_60, y_probability,
                       new_midprice, new_controller, new_Full_Y
                FROM hkhsi50
                WHERE Date >= '2020-01-01'
                AND new_Full_Y IS NOT NULL
                ORDER BY Date ASC
            """

            self.df = pd.read_sql(query, connection)
            connection.close()

            # 数据预处理
            self.df['date'] = pd.to_datetime(self.df['Date'])
            self.df.rename(columns={
                'Open': 'open', 'High': 'high', 'Low': 'low',
                'Close': 'close', 'Volume': 'volume',
                'new_Full_Y': 'full_y'
            }, inplace=True)

            # 计算X指标和相关技术指标
            self.calculate_x_indicators()

            print(f"✓ 成功加载 {len(self.df)} 条数据")
            print(f"数据范围：{self.df['date'].min().date()} 至 {self.df['date'].max().date()}")
            print(f"X指标范围：{self.df['X'].min():.6f} ~ {self.df['X'].max():.6f}")
            print(f"X指标均值：{self.df['X'].mean():.6f}")

            return True

        except Exception as e:
            print(f"❌ 数据加载失败: {e}")
            return False

    def calculate_x_indicators(self):
        """计算X指标和相关技术指标"""
        print("   计算X指标 (X = 1 - Full_Y)...")

        # 核心X指标：X = 1 - Full_Y (弱势比例)
        self.df['X'] = 1 - self.df['full_y']

        # X指标的技术分析
        self.df['X_change'] = self.df['X'].diff()
        self.df['X_change_pct'] = self.df['X'].pct_change()

        # X指标的移动平均
        self.df['X_ma5'] = self.df['X'].rolling(5).mean()
        self.df['X_ma20'] = self.df['X'].rolling(20).mean()

        # X相对于移动平均的位置
        self.df['X_vs_ma5'] = self.df['X'] - self.df['X_ma5']
        self.df['X_vs_ma20'] = self.df['X'] - self.df['X_ma20']

        # X趋势强度
        self.df['X_trend'] = self.df['X_ma5'] - self.df['X_ma20']

        # X的极值标记
        self.df['X_high'] = self.df['X'] > self.x_high_threshold
        self.df['X_low'] = self.df['X'] < self.x_low_threshold

        # 价格技术指标
        self.df['price_deviation'] = (self.df['close'] - self.df['new_midprice']) / self.df['new_midprice']
        self.df['price_vs_ma20'] = self.df['close'] / self.df['ma_20']
        self.df['volatility'] = self.df['close'].pct_change().rolling(window=20).std()

        # RSI
        delta = self.df['close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
        rs = gain / loss
        self.df['rsi'] = 100 - (100 / (1 + rs))

        print("✓ X指标计算完成")

    def calculate_kelly(self, win_rate, profit_ratio, loss_ratio):
        """计算凯利公式建议仓位"""
        if win_rate <= 0 or profit_ratio <= 0:
            return 0
        q = 1 - win_rate
        kelly = (win_rate/q * profit_ratio - 1) / profit_ratio
        kelly *= self.kelly_fraction
        return max(0, min(kelly, 0.3))  # 最大仓位限制在30%

    def get_position_size(self, price, capital, volatility):
        """计算仓位大小"""
        # 基于历史数据计算胜率和盈亏比
        if len(self.trades) < 5:
            win_rate = 0.6  # 初始假设胜率
        else:
            trades_df = pd.DataFrame(self.trades)
            exit_trades = trades_df[trades_df['type'].str.contains('exit')]
            if len(exit_trades) > 0:
                profit_trades = exit_trades[exit_trades.get('profit', 0) > 0]
                win_rate = len(profit_trades) / len(exit_trades)
            else:
                win_rate = 0.6

        # 根据持仓方向设置盈亏比
        if self.position == 1:  # 多头
            profit_ratio = self.take_profit_long
            loss_ratio = self.stop_loss_long
        else:  # 空头
            profit_ratio = self.take_profit_short
            loss_ratio = self.stop_loss_short

        # 使用凯利公式计算仓位比例
        kelly = self.calculate_kelly(win_rate, profit_ratio, loss_ratio)

        # 根据波动率调整仓位
        volatility_factor = 1 - min(volatility * 50, 0.5)

        return capital * kelly * volatility_factor

    def add_monthly_capital(self, date, capital):
        """每月增加资金"""
        last_month = getattr(self, 'last_month', None)
        current_month = date.replace(day=1)

        if last_month is None or current_month > last_month:
            self.last_month = current_month
            return capital + self.monthly_addition

        return capital

    def check_entry_conditions(self, row):
        """检查入场条件 (基于X指标)"""
        X = row['X']
        X_change = row['X_change']
        X_trend = row['X_trend']
        y_prob = row['y_probability']
        price_deviation = row['price_deviation']
        rsi = row['rsi']

        # 基本数据检查
        if pd.isna(X) or pd.isna(y_prob) or pd.isna(rsi):
            return 0

        # 多头条件：X处于高位 (弱势比例高，适合抄底)
        long_condition = (
            X > self.x_high_threshold and  # X高位 (市场弱势)
            X_change > -0.0001 and  # X不再大幅上升 (弱势缓解)
            (X_trend < 0.0001 or X_trend > -0.0001) and  # X趋势平缓
            y_prob > 0.48 and  # y_probability不太悲观
            price_deviation < 0.02 and  # 价格不太高
            rsi < 65  # RSI不超买
        )

        # 空头条件：X处于低位 (弱势比例低，市场强势过头)
        short_condition = (
            X < self.x_low_threshold and  # X低位 (市场强势)
            X_change < 0.0001 and  # X不再大幅下降 (强势见顶)
            (X_trend > -0.0001 or X_trend < 0.0001) and  # X趋势平缓
            y_prob < 0.52 and  # y_probability不太乐观
            price_deviation > -0.02 and  # 价格不太低
            rsi > 35  # RSI不超卖
        )

        if long_condition:
            return 1
        elif short_condition:
            return -1
        else:
            return 0

    def run_backtest(self):
        """运行回测"""
        print("\n2. 开始X指标策略回测...")

        capital = self.initial_capital
        last_trade_date = None
        min_trade_interval = timedelta(days=2)  # 缩短交易间隔

        for i in range(20, len(self.df)):  # 从第20天开始
            row = self.df.iloc[i]
            date = pd.to_datetime(row['date'])

            # 每月增加资金
            capital = self.add_monthly_capital(date, capital)

            # 记录权益
            current_equity = capital
            if self.position != 0:
                # 计算当前持仓的浮动盈亏
                if self.position == 1:  # 多头
                    unrealized_pnl = (row['close'] - self.current_price) / self.current_price * capital * 0.25
                else:  # 空头
                    unrealized_pnl = (self.current_price - row['close']) / self.current_price * capital * 0.25
                current_equity += unrealized_pnl

            self.equity_curve.append({
                'date': date,
                'equity': current_equity,
                'position': self.position,
                'X': row['X']
            })

            # 检查交易间隔
            if last_trade_date and (date - last_trade_date) < min_trade_interval:
                continue

            # 检查止盈止损
            if self.position != 0:
                if self.position == 1:  # 多头
                    profit_ratio = (row['close'] - self.current_price) / self.current_price

                    if profit_ratio >= self.take_profit_long:  # 止盈
                        exit_price = self.current_price * (1 + self.take_profit_long)
                        profit = profit_ratio * capital * 0.25  # 假设25%仓位
                        capital += profit
                        self.position = 0
                        self.trades.append({
                            'date': date,
                            'type': 'long_exit_tp',
                            'price': exit_price,
                            'profit': profit,
                            'capital': capital,
                            'X': row['X']
                        })
                    elif profit_ratio <= -self.stop_loss_long:  # 止损
                        exit_price = self.current_price * (1 - self.stop_loss_long)
                        loss = profit_ratio * capital * 0.25
                        capital += loss
                        self.position = 0
                        self.trades.append({
                            'date': date,
                            'type': 'long_exit_sl',
                            'price': exit_price,
                            'profit': loss,
                            'capital': capital,
                            'X': row['X']
                        })

                elif self.position == -1:  # 空头
                    profit_ratio = (self.current_price - row['close']) / self.current_price

                    if profit_ratio >= self.take_profit_short:  # 止盈
                        exit_price = self.current_price * (1 - self.take_profit_short)
                        profit = profit_ratio * capital * 0.25
                        capital += profit
                        self.position = 0
                        self.trades.append({
                            'date': date,
                            'type': 'short_exit_tp',
                            'price': exit_price,
                            'profit': profit,
                            'capital': capital,
                            'X': row['X']
                        })
                    elif profit_ratio <= -self.stop_loss_short:  # 止损
                        exit_price = self.current_price * (1 + self.stop_loss_short)
                        loss = profit_ratio * capital * 0.25
                        capital += loss
                        self.position = 0
                        self.trades.append({
                            'date': date,
                            'type': 'short_exit_sl',
                            'price': exit_price,
                            'profit': loss,
                            'capital': capital,
                            'X': row['X']
                        })

            # 检查开仓条件
            if self.position == 0:
                position_signal = self.check_entry_conditions(row)

                if position_signal != 0:
                    position_size = self.get_position_size(row['close'], capital, row['volatility'])

                    if position_size > 500:  # 最小交易金额限制
                        self.position = position_signal
                        self.current_price = row['close']
                        last_trade_date = date

                        self.trades.append({
                            'date': date,
                            'type': 'long_entry' if position_signal == 1 else 'short_entry',
                            'price': self.current_price,
                            'size': position_size,
                            'capital': capital,
                            'X': row['X'],
                            'y_probability': row['y_probability']
                        })

        self.final_capital = capital
        print(f"✓ 回测完成！最终资金：{self.final_capital:,.2f}")

    def analyze_results(self):
        """分析回测结果"""
        print("\n=== X指标策略回测分析 ===")

        trades_df = pd.DataFrame(self.trades)
        if len(trades_df) == 0:
            print("没有产生任何交易")
            return

        # 基本统计
        entry_trades = trades_df[trades_df['type'].str.contains('entry')]
        exit_trades = trades_df[trades_df['type'].str.contains('exit')]

        total_trades = len(entry_trades)
        winning_trades = len(exit_trades[exit_trades['profit'] > 0]) if 'profit' in exit_trades.columns else 0

        print(f"\n📊 交易统计：")
        print(f"总交易次数：{total_trades}")
        print(f"盈利交易：{winning_trades}")
        print(f"亏损交易：{total_trades - winning_trades}")
        if total_trades > 0:
            print(f"胜率：{winning_trades/total_trades*100:.2f}%")

        # 盈亏分析
        if 'profit' in exit_trades.columns and len(exit_trades) > 0:
            total_profit = exit_trades['profit'].sum()
            profit_trades = exit_trades[exit_trades['profit'] > 0]
            loss_trades = exit_trades[exit_trades['profit'] < 0]

            print(f"总交易盈亏：{total_profit:,.2f}")

            if len(profit_trades) > 0:
                print(f"平均盈利：{profit_trades['profit'].mean():.2f}")
                print(f"最大盈利：{profit_trades['profit'].max():.2f}")

            if len(loss_trades) > 0:
                print(f"平均亏损：{loss_trades['profit'].mean():.2f}")
                print(f"最大亏损：{loss_trades['profit'].min():.2f}")

        # 收益率分析
        initial_equity = self.initial_capital
        final_equity = self.final_capital
        total_days = (self.df['date'].max() - self.df['date'].min()).days
        total_years = total_days / 365

        # 计算总投入
        months = total_days / 30
        total_invested = initial_equity + months * self.monthly_addition

        net_profit = final_equity - total_invested
        total_return = net_profit / total_invested if total_invested > 0 else 0
        annual_return = (final_equity / total_invested) ** (1/total_years) - 1 if total_years > 0 else 0

        print(f"\n💰 收益统计：")
        print(f"初始资金：{initial_equity:,.2f}")
        print(f"总投入：{total_invested:,.2f}")
        print(f"最终资金：{final_equity:,.2f}")
        print(f"净收益：{net_profit:,.2f}")
        print(f"总收益率：{total_return*100:.2f}%")
        print(f"年化收益率：{annual_return*100:.2f}%")

        # X指标分析
        if 'X' in entry_trades.columns:
            print(f"\n🎯 X指标分析：")
            avg_entry_X = entry_trades['X'].mean()
            print(f"平均入场X值：{avg_entry_X:.6f}")

            long_entries = entry_trades[entry_trades['type'] == 'long_entry']
            short_entries = entry_trades[entry_trades['type'] == 'short_entry']

            if len(long_entries) > 0:
                print(f"多头入场平均X值：{long_entries['X'].mean():.6f} (弱势抄底)")
            if len(short_entries) > 0:
                print(f"空头入场平均X值：{short_entries['X'].mean():.6f} (强势做空)")

        # 与买入持有比较
        hsi_start = self.df['close'].iloc[20]
        hsi_end = self.df['close'].iloc[-1]
        buy_hold_return = (hsi_end - hsi_start) / hsi_start
        buy_hold_annual = (buy_hold_return + 1) ** (1/total_years) - 1

        print(f"\n📊 策略对比：")
        print(f"X指标策略年化收益：{annual_return*100:.2f}%")
        print(f"买入持有年化收益：{buy_hold_annual*100:.2f}%")
        print(f"超额收益：{(annual_return - buy_hold_annual)*100:+.2f}%")

        # 保存结果
        try:
            equity_df = pd.DataFrame(self.equity_curve)

            # 绘制权益曲线和X指标
            fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(15, 10))

            # 权益曲线
            ax1.plot(equity_df['date'], equity_df['equity'])
            ax1.set_title('X指标策略权益曲线')
            ax1.set_ylabel('资金')
            ax1.grid(True)

            # X指标曲线
            ax2.plot(equity_df['date'], equity_df['X'])
            ax2.axhline(y=self.x_high_threshold, color='g', linestyle='--', label='高位线(做多)')
            ax2.axhline(y=self.x_low_threshold, color='r', linestyle='--', label='低位线(做空)')
            ax2.set_title('X指标 (弱势比例)')
            ax2.set_ylabel('X值')
            ax2.set_xlabel('日期')
            ax2.legend()
            ax2.grid(True)

            plt.tight_layout()
            plt.savefig('x_strategy_analysis.png')
            plt.close()

            if len(trades_df) > 0:
                trades_df.to_excel('x_strategy_trades.xlsx', index=False)
                print("\n📄 交易记录已保存到 x_strategy_trades.xlsx")

            print("📈 分析图表已保存到 x_strategy_analysis.png")

        except Exception as e:
            print(f"保存文件时出错: {e}")

def main():
    """主函数"""
    print("🎯 HSI50 X指标策略")
    print("X = 1 - Full_Y (弱势比例指标)")
    print("="*50)

    try:
        strategy = HSI50XStrategy()

        if not strategy.load_data():
            return

        strategy.run_backtest()
        strategy.analyze_results()

        print("\n🎉 X指标策略测试完成！")
        print("💡 X高位做多(弱势抄底)，X低位做空(强势见顶)")

    except Exception as e:
        print(f"❌ 运行失败: {e}")

if __name__ == "__main__":
    main()
