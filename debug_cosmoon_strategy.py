#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试Cosmoon策略
==============
分析为什么没有产生交易信号
"""

import mysql.connector
import pandas as pd
import numpy as np
from datetime import datetime
from scipy import stats
import warnings
warnings.filterwarnings('ignore')

def debug_cosmoon_conditions():
    """调试Cosmoon策略条件"""
    
    # 数据库配置
    db_config = {
        'host': 'localhost',
        'database': 'finance',
        'user': 'root',
        'password': '12345678',
        'charset': 'utf8mb4'
    }
    
    try:
        print("🔍 调试Cosmoon策略条件")
        print("=" * 50)
        
        # 加载数据
        connection = mysql.connector.connect(**db_config)
        query = """
            SELECT Date, Open, High, Low, Close, Volume,
                   ma_20, ma_60, y_probability, 
                   new_midprice, new_controller, new_Full_Y
            FROM hkhsi50 
            WHERE Date >= '2024-01-01'
            AND y_probability IS NOT NULL
            ORDER BY Date ASC
        """
        
        df = pd.read_sql(query, connection)
        connection.close()
        
        # 数据预处理
        df['date'] = pd.to_datetime(df['Date'])
        df.rename(columns={
            'Open': 'open', 'High': 'high', 'Low': 'low', 
            'Close': 'close', 'Volume': 'volume'
        }, inplace=True)
        
        print(f"📊 加载了 {len(df)} 条数据")
        
        # 计算技术指标
        print("\n📈 计算技术指标...")
        
        # RSI
        delta = df['close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
        rs = gain / loss
        df['rsi'] = 100 - (100 / (1 + rs))
        
        # 成交量比率
        df['volume_ratio'] = df['volume'] / df['volume'].rolling(20).mean()
        
        # 资金流入比率
        df['inflow_ratio'] = df['y_probability']
        
        # 回归线
        window = 60
        df['regression_line'] = df['close'].rolling(window=window).apply(
            lambda x: stats.linregress(range(len(x)), x)[0] * (window-1) + stats.linregress(range(len(x)), x)[1]
        )
        
        # 价格相对回归线位置
        df['price_position'] = (df['close'] - df['regression_line']) / df['regression_line']
        
        # 趋势强度
        df['trend_strength'] = df['regression_line'].diff() / df['regression_line'].shift(1)
        
        # RSI变化率
        df['rsi_change'] = df['rsi'].diff()
        
        # 分析各个条件
        print(f"\n🔍 条件分析:")
        
        # 参数设置
        regression_threshold = 0.002
        
        # 1. 价格位置分析
        price_low = df['price_position'] < -regression_threshold
        price_high = df['price_position'] > regression_threshold
        
        print(f"   • 价格显著低于回归线(<-0.2%): {price_low.sum()} 天 ({price_low.mean()*100:.1f}%)")
        print(f"   • 价格显著高于回归线(>0.2%): {price_high.sum()} 天 ({price_high.mean()*100:.1f}%)")
        print(f"   • 价格位置范围: {df['price_position'].min()*100:.2f}% ~ {df['price_position'].max()*100:.2f}%")
        
        # 2. RSI分析
        rsi_oversold = df['rsi'] < 30
        rsi_overbought = df['rsi'] > 70
        rsi_up = df['rsi_change'] > 0
        rsi_down = df['rsi_change'] < 0
        
        print(f"   • RSI超卖(<30): {rsi_oversold.sum()} 天 ({rsi_oversold.mean()*100:.1f}%)")
        print(f"   • RSI超买(>70): {rsi_overbought.sum()} 天 ({rsi_overbought.mean()*100:.1f}%)")
        print(f"   • RSI上升: {rsi_up.sum()} 天 ({rsi_up.mean()*100:.1f}%)")
        print(f"   • RSI下降: {rsi_down.sum()} 天 ({rsi_down.mean()*100:.1f}%)")
        print(f"   • RSI范围: {df['rsi'].min():.1f} ~ {df['rsi'].max():.1f}")
        
        # 3. 趋势强度分析
        trend_strong = np.abs(df['trend_strength']) > 0.0005
        
        print(f"   • 趋势强度>0.05%: {trend_strong.sum()} 天 ({trend_strong.mean()*100:.1f}%)")
        print(f"   • 趋势强度范围: {df['trend_strength'].min()*100:.4f}% ~ {df['trend_strength'].max()*100:.4f}%")
        
        # 4. 成交量分析
        volume_active = df['volume_ratio'] > 1.2
        
        print(f"   • 成交量活跃(>1.2倍): {volume_active.sum()} 天 ({volume_active.mean()*100:.1f}%)")
        print(f"   • 成交量比率范围: {df['volume_ratio'].min():.2f} ~ {df['volume_ratio'].max():.2f}")
        
        # 5. 资金流入分析
        inflow_strong = df['inflow_ratio'] > 0.6
        outflow_strong = df['inflow_ratio'] < 0.4
        
        print(f"   • 资金流入强势(>0.6): {inflow_strong.sum()} 天 ({inflow_strong.mean()*100:.1f}%)")
        print(f"   • 资金流出强势(<0.4): {outflow_strong.sum()} 天 ({outflow_strong.mean()*100:.1f}%)")
        
        # 6. 组合条件分析
        print(f"\n🎯 组合条件分析:")
        
        # 多头条件组合
        long_condition1 = price_low & rsi_oversold & rsi_up
        long_condition2 = long_condition1 & trend_strong
        long_condition3 = long_condition2 & volume_active
        long_condition4 = long_condition3 & inflow_strong
        
        print(f"   • 价格低+RSI超卖+RSI上升: {long_condition1.sum()} 天")
        print(f"   • +趋势强: {long_condition2.sum()} 天")
        print(f"   • +成交量活跃: {long_condition3.sum()} 天")
        print(f"   • +资金流入强: {long_condition4.sum()} 天")
        
        # 空头条件组合
        short_condition1 = price_high & rsi_overbought & rsi_down
        short_condition2 = short_condition1 & trend_strong
        short_condition3 = short_condition2 & volume_active
        short_condition4 = short_condition3 & outflow_strong
        
        print(f"   • 价格高+RSI超买+RSI下降: {short_condition1.sum()} 天")
        print(f"   • +趋势强: {short_condition2.sum()} 天")
        print(f"   • +成交量活跃: {short_condition3.sum()} 天")
        print(f"   • +资金流出强: {short_condition4.sum()} 天")
        
        # 7. 建议的参数调整
        print(f"\n💡 建议的参数调整:")
        
        # 找到合理的阈值
        price_pos_95 = np.percentile(np.abs(df['price_position']), 95)
        trend_95 = np.percentile(np.abs(df['trend_strength']), 95)
        volume_75 = np.percentile(df['volume_ratio'], 75)
        
        print(f"   • 价格偏离阈值建议: {price_pos_95*100:.3f}% (95%分位数)")
        print(f"   • 趋势强度阈值建议: {trend_95*100:.4f}% (95%分位数)")
        print(f"   • 成交量比率阈值建议: {volume_75:.2f} (75%分位数)")
        print(f"   • RSI阈值建议: 35/65 (放宽超买超卖条件)")
        print(f"   • 资金流入阈值建议: 0.55/0.45 (放宽条件)")
        
        # 8. 测试放宽条件
        print(f"\n🧪 测试放宽条件:")
        
        # 放宽的多头条件
        relaxed_long = (
            (df['price_position'] < -price_pos_95/2) &  # 价格偏低
            (df['rsi'] < 35) &  # RSI偏低
            (df['rsi_change'] > -1) &  # RSI不大幅下降
            (np.abs(df['trend_strength']) > trend_95/2) &  # 趋势适中
            (df['volume_ratio'] > volume_75/2) &  # 成交量适中
            (df['inflow_ratio'] > 0.45)  # 资金流入不太差
        )
        
        # 放宽的空头条件
        relaxed_short = (
            (df['price_position'] > price_pos_95/2) &  # 价格偏高
            (df['rsi'] > 65) &  # RSI偏高
            (df['rsi_change'] < 1) &  # RSI不大幅上升
            (np.abs(df['trend_strength']) > trend_95/2) &  # 趋势适中
            (df['volume_ratio'] > volume_75/2) &  # 成交量适中
            (df['inflow_ratio'] < 0.55)  # 资金流出不太差
        )
        
        print(f"   • 放宽多头条件: {relaxed_long.sum()} 天 ({relaxed_long.mean()*100:.1f}%)")
        print(f"   • 放宽空头条件: {relaxed_short.sum()} 天 ({relaxed_short.mean()*100:.1f}%)")
        
        # 显示具体的机会
        if relaxed_long.sum() > 0:
            print(f"\n📈 多头机会示例:")
            long_opportunities = df[relaxed_long].tail(5)
            for _, row in long_opportunities.iterrows():
                print(f"   {row['date'].strftime('%Y-%m-%d')}: 价格偏离{row['price_position']*100:.2f}%, RSI{row['rsi']:.1f}, y_prob{row['y_probability']:.3f}")
        
        if relaxed_short.sum() > 0:
            print(f"\n📉 空头机会示例:")
            short_opportunities = df[relaxed_short].tail(5)
            for _, row in short_opportunities.iterrows():
                print(f"   {row['date'].strftime('%Y-%m-%d')}: 价格偏离{row['price_position']*100:.2f}%, RSI{row['rsi']:.1f}, y_prob{row['y_probability']:.3f}")
        
        return True
        
    except Exception as e:
        print(f"❌ 调试失败: {e}")
        return False

def main():
    """主函数"""
    debug_cosmoon_conditions()

if __name__ == "__main__":
    main()
