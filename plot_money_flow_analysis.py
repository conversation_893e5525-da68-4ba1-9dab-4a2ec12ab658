#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
散户资金流分析图表
================
绘制散户资金流入与总资金流的对比图表
分析X值（散户买升概率）与实际资金流的关系
"""

import mysql.connector
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class MoneyFlowAnalyzer:
    def __init__(self):
        """初始化资金流分析器"""
        self.host = "localhost"
        self.user = "root"
        self.password = "12345678"
        self.database = "finance"
        self.df = None

    def connect_mysql(self):
        """连接MySQL数据库"""
        print("🔗 连接MySQL数据库...")
        try:
            self.conn = mysql.connector.connect(
                host=self.host,
                user=self.user,
                password=self.password,
                database=self.database
            )
            print(f"✅ 成功连接到MySQL数据库: {self.database}")
            return True
        except mysql.connector.Error as e:
            print(f"❌ MySQL连接失败: {e}")
            return False

    def load_and_calculate_data(self):
        """加载数据并计算资金流指标"""
        print("📊 加载hkhsi50数据并计算资金流指标...")

        try:
            query = """
                SELECT * FROM hkhsi50
                ORDER BY Date ASC
            """

            self.df = pd.read_sql_query(query, self.conn)

            # 转换数据类型
            for col in self.df.columns:
                if self.df[col].dtype == 'object':
                    try:
                        self.df[col] = pd.to_numeric(self.df[col], errors='ignore')
                    except:
                        pass

            # 确保日期格式正确
            self.df['Date'] = pd.to_datetime(self.df['Date'])
            self.df = self.df.sort_values('Date').reset_index(drop=True)

            print(f"✅ 成功加载 {len(self.df):,} 条记录")
            print(f"📅 数据范围: {self.df['Date'].min()} 至 {self.df['Date'].max()}")

            # 计算资金流指标
            self.calculate_money_flow_indicators()

            return True

        except Exception as e:
            print(f"❌ 数据加载失败: {e}")
            return False

    def calculate_money_flow_indicators(self):
        """计算资金流指标"""
        print("💰 计算资金流指标...")

        # 1. 计算典型价格
        self.df['typical_price'] = (self.df['High'] + self.df['Low'] + self.df['Close']) / 3

        # 2. 计算总资金流量
        self.df['total_money_flow'] = self.df['typical_price'] * self.df['Volume']

        # 3. 计算价格变化方向
        self.df['price_change'] = self.df['typical_price'].diff()

        # 4. 分类资金流
        self.df['positive_money_flow'] = np.where(self.df['price_change'] > 0, self.df['total_money_flow'], 0)
        self.df['negative_money_flow'] = np.where(self.df['price_change'] < 0, self.df['total_money_flow'], 0)

        # 5. 计算净资金流
        self.df['net_money_flow'] = self.df['positive_money_flow'] - self.df['negative_money_flow']

        # 6. 计算14日MFI
        period = 14
        self.df['positive_mf_sum'] = self.df['positive_money_flow'].rolling(period).sum()
        self.df['negative_mf_sum'] = self.df['negative_money_flow'].rolling(period).sum()
        self.df['money_flow_ratio'] = self.df['positive_mf_sum'] / (self.df['negative_mf_sum'] + 1e-10)
        self.df['mfi'] = 100 - (100 / (1 + self.df['money_flow_ratio']))

        # 7. 计算VWAP
        self.df['vwap'] = (self.df['typical_price'] * self.df['Volume']).rolling(20).sum() / self.df['Volume'].rolling(20).sum()

        # 8. 计算相对成交量
        self.df['volume_ma'] = self.df['Volume'].rolling(20).mean()
        self.df['relative_volume'] = self.df['Volume'] / self.df['volume_ma']

        # 9. 计算价格位置
        self.df['price_vs_vwap'] = self.df['Close'] / self.df['vwap']
        self.df['price_percentile'] = self.df['Close'].rolling(20).apply(
            lambda x: (x.iloc[-1] - x.min()) / (x.max() - x.min()) if x.max() != x.min() else 0.5
        )

        # 10. 计算散户买升概率X
        self.df['X'] = (
            (self.df['mfi'] / 100) * 0.40 +
            np.clip((self.df['price_vs_vwap'] - 0.98) / 0.04, 0, 1) * 0.25 +
            np.clip((self.df['relative_volume'] - 0.5) / 1.5, 0, 1) * 0.20 +
            self.df['price_percentile'] * 0.15
        )
        self.df['X'] = np.clip(self.df['X'], 0, 1)

        # 11. 计算散户资金流入（基于X值）
        self.df['retail_inflow'] = self.df['total_money_flow'] * self.df['X']
        self.df['retail_outflow'] = self.df['total_money_flow'] * (1 - self.df['X'])

        # 12. 计算移动平均
        self.df['total_mf_ma20'] = self.df['total_money_flow'].rolling(20).mean()
        self.df['retail_inflow_ma20'] = self.df['retail_inflow'].rolling(20).mean()
        self.df['net_mf_ma20'] = self.df['net_money_flow'].rolling(20).mean()

        print("✅ 资金流指标计算完成")

    def create_money_flow_charts(self):
        """创建资金流分析图表"""
        print("📊 创建资金流分析图表...")

        # 选择最近5年的数据用于绘图（数据太多会影响可读性）
        recent_data = self.df.tail(1300)  # 约5年数据

        # 创建图表
        fig, axes = plt.subplots(4, 1, figsize=(15, 16))
        fig.suptitle('散户资金流分析图表\n基于hkhsi50数据的资金流向分析', fontsize=16, fontweight='bold')

        # 图表1: 总资金流 vs 散户资金流入
        ax1 = axes[0]
        ax1.plot(recent_data['Date'], recent_data['total_money_flow']/1e9,
                label='总资金流', color='blue', alpha=0.7, linewidth=1)
        ax1.plot(recent_data['Date'], recent_data['retail_inflow']/1e9,
                label='散户资金流入', color='red', alpha=0.8, linewidth=1.5)
        ax1.plot(recent_data['Date'], recent_data['total_mf_ma20']/1e9,
                label='总资金流20日均线', color='darkblue', linewidth=2)
        ax1.plot(recent_data['Date'], recent_data['retail_inflow_ma20']/1e9,
                label='散户流入20日均线', color='darkred', linewidth=2)

        ax1.set_title('总资金流 vs 散户资金流入对比', fontsize=14, fontweight='bold')
        ax1.set_ylabel('资金流量 (十亿港元)', fontsize=12)
        ax1.legend(loc='upper left')
        ax1.grid(True, alpha=0.3)
        ax1.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m'))
        ax1.xaxis.set_major_locator(mdates.MonthLocator(interval=6))

        # 图表2: 散户买升概率X值
        ax2 = axes[1]
        ax2.plot(recent_data['Date'], recent_data['X'],
                color='green', linewidth=1.5, label='散户买升概率(X)')
        ax2.axhline(y=0.5, color='gray', linestyle='--', alpha=0.7, label='中性线(0.5)')
        ax2.axhline(y=0.65, color='red', linestyle='--', alpha=0.7, label='过热线(0.65)')
        ax2.axhline(y=0.35, color='blue', linestyle='--', alpha=0.7, label='过冷线(0.35)')
        ax2.fill_between(recent_data['Date'], 0.35, 0.65, alpha=0.1, color='gray', label='中性区间')

        ax2.set_title('散户买升概率(X)时间序列', fontsize=14, fontweight='bold')
        ax2.set_ylabel('X值', fontsize=12)
        ax2.set_ylim(0, 1)
        ax2.legend(loc='upper left')
        ax2.grid(True, alpha=0.3)
        ax2.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m'))
        ax2.xaxis.set_major_locator(mdates.MonthLocator(interval=6))

        # 图表3: 净资金流与价格
        ax3 = axes[2]
        ax3_price = ax3.twinx()

        # 净资金流
        ax3.bar(recent_data['Date'], recent_data['net_money_flow']/1e9,
               alpha=0.6, color='purple', width=1, label='净资金流')
        ax3.plot(recent_data['Date'], recent_data['net_mf_ma20']/1e9,
                color='indigo', linewidth=2, label='净资金流20日均线')

        # 价格
        ax3_price.plot(recent_data['Date'], recent_data['Close'],
                      color='orange', linewidth=2, label='收盘价')

        ax3.set_title('净资金流 vs 价格走势', fontsize=14, fontweight='bold')
        ax3.set_ylabel('净资金流 (十亿港元)', fontsize=12, color='purple')
        ax3_price.set_ylabel('价格 (港元)', fontsize=12, color='orange')
        ax3.legend(loc='upper left')
        ax3_price.legend(loc='upper right')
        ax3.grid(True, alpha=0.3)
        ax3.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m'))
        ax3.xaxis.set_major_locator(mdates.MonthLocator(interval=6))

        # 图表4: 散户资金流入占比
        ax4 = axes[3]
        retail_ratio = recent_data['retail_inflow'] / recent_data['total_money_flow']
        ax4.plot(recent_data['Date'], retail_ratio,
                color='brown', linewidth=1.5, label='散户流入占比')
        ax4.plot(recent_data['Date'], retail_ratio.rolling(20).mean(),
                color='maroon', linewidth=2, label='散户流入占比20日均线')
        ax4.axhline(y=0.5, color='gray', linestyle='--', alpha=0.7, label='50%基准线')

        ax4.set_title('散户资金流入占总资金流比例', fontsize=14, fontweight='bold')
        ax4.set_ylabel('占比', fontsize=12)
        ax4.set_xlabel('时间', fontsize=12)
        ax4.legend(loc='upper left')
        ax4.grid(True, alpha=0.3)
        ax4.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m'))
        ax4.xaxis.set_major_locator(mdates.MonthLocator(interval=6))

        # 调整布局
        plt.tight_layout()
        plt.subplots_adjust(top=0.93)

        # 保存图表
        plt.savefig('money_flow_analysis.png', dpi=300, bbox_inches='tight')
        print("✅ 图表已保存为 money_flow_analysis.png")

        # 显示图表
        plt.show()

    def create_correlation_analysis(self):
        """创建相关性分析图表"""
        print("📊 创建相关性分析图表...")

        # 选择最近3年数据
        recent_data = self.df.tail(800)

        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        fig.suptitle('散户资金流相关性分析', fontsize=16, fontweight='bold')

        # 图表1: X值 vs 总资金流散点图
        ax1 = axes[0, 0]
        scatter1 = ax1.scatter(recent_data['X'], recent_data['total_money_flow']/1e9,
                              c=recent_data['Close'], cmap='viridis', alpha=0.6, s=20)
        ax1.set_xlabel('散户买升概率(X)')
        ax1.set_ylabel('总资金流 (十亿港元)')
        ax1.set_title('X值 vs 总资金流')
        ax1.grid(True, alpha=0.3)
        plt.colorbar(scatter1, ax=ax1, label='收盘价')

        # 图表2: X值 vs MFI散点图
        ax2 = axes[0, 1]
        scatter2 = ax2.scatter(recent_data['X'], recent_data['mfi'],
                              c=recent_data['relative_volume'], cmap='plasma', alpha=0.6, s=20)
        ax2.set_xlabel('散户买升概率(X)')
        ax2.set_ylabel('MFI')
        ax2.set_title('X值 vs MFI')
        ax2.grid(True, alpha=0.3)
        plt.colorbar(scatter2, ax=ax2, label='相对成交量')

        # 图表3: 散户流入占比分布
        ax3 = axes[1, 0]
        retail_ratio = recent_data['retail_inflow'] / recent_data['total_money_flow']
        ax3.hist(retail_ratio, bins=50, alpha=0.7, color='skyblue', edgecolor='black')
        ax3.axvline(retail_ratio.mean(), color='red', linestyle='--',
                   label=f'均值: {retail_ratio.mean():.3f}')
        ax3.axvline(retail_ratio.median(), color='green', linestyle='--',
                   label=f'中位数: {retail_ratio.median():.3f}')
        ax3.set_xlabel('散户流入占比')
        ax3.set_ylabel('频次')
        ax3.set_title('散户资金流入占比分布')
        ax3.legend()
        ax3.grid(True, alpha=0.3)

        # 图表4: X值分布
        ax4 = axes[1, 1]
        ax4.hist(recent_data['X'], bins=50, alpha=0.7, color='lightcoral', edgecolor='black')
        ax4.axvline(recent_data['X'].mean(), color='blue', linestyle='--',
                   label=f'均值: {recent_data["X"].mean():.3f}')
        ax4.axvline(0.5, color='gray', linestyle='--', label='中性线(0.5)')
        ax4.axvline(0.65, color='red', linestyle='--', label='过热线(0.65)')
        ax4.axvline(0.35, color='green', linestyle='--', label='过冷线(0.35)')
        ax4.set_xlabel('散户买升概率(X)')
        ax4.set_ylabel('频次')
        ax4.set_title('X值分布')
        ax4.legend()
        ax4.grid(True, alpha=0.3)

        plt.tight_layout()
        plt.subplots_adjust(top=0.93)
        plt.savefig('money_flow_correlation.png', dpi=300, bbox_inches='tight')
        print("✅ 相关性分析图表已保存为 money_flow_correlation.png")
        plt.show()

    def print_statistics(self):
        """打印统计信息"""
        print("\n📊 资金流统计分析:")
        print("=" * 60)

        # 基本统计
        total_mf_mean = self.df['total_money_flow'].mean() / 1e9
        retail_inflow_mean = self.df['retail_inflow'].mean() / 1e9
        retail_ratio_mean = (self.df['retail_inflow'] / self.df['total_money_flow']).mean()

        print(f"📈 总资金流平均值: {total_mf_mean:.2f} 十亿港元")
        print(f"💰 散户流入平均值: {retail_inflow_mean:.2f} 十亿港元")
        print(f"📊 散户流入占比平均: {retail_ratio_mean:.1%}")

        # X值统计
        x_stats = self.df['X'].describe()
        print(f"\n🎯 X值统计:")
        print(f"   最小值: {x_stats['min']:.3f}")
        print(f"   25%分位: {x_stats['25%']:.3f}")
        print(f"   中位数: {x_stats['50%']:.3f}")
        print(f"   75%分位: {x_stats['75%']:.3f}")
        print(f"   最大值: {x_stats['max']:.3f}")
        print(f"   均值: {x_stats['mean']:.3f}")

        # 极端情况统计
        high_x_days = (self.df['X'] > 0.65).sum()
        low_x_days = (self.df['X'] < 0.35).sum()
        neutral_x_days = len(self.df) - high_x_days - low_x_days

        print(f"\n📊 X值区间分布:")
        print(f"   散户过度买升 (X>0.65): {high_x_days}天 ({high_x_days/len(self.df)*100:.1f}%)")
        print(f"   散户过度买跌 (X<0.35): {low_x_days}天 ({low_x_days/len(self.df)*100:.1f}%)")
        print(f"   散户情绪中性: {neutral_x_days}天 ({neutral_x_days/len(self.df)*100:.1f}%)")

        # 相关性分析
        correlation_matrix = self.df[['X', 'total_money_flow', 'mfi', 'relative_volume', 'Close']].corr()
        print(f"\n🔗 相关性矩阵 (X值与其他指标):")
        print(f"   X vs 总资金流: {correlation_matrix.loc['X', 'total_money_flow']:.3f}")
        print(f"   X vs MFI: {correlation_matrix.loc['X', 'mfi']:.3f}")
        print(f"   X vs 相对成交量: {correlation_matrix.loc['X', 'relative_volume']:.3f}")
        print(f"   X vs 收盘价: {correlation_matrix.loc['X', 'Close']:.3f}")

    def close_connection(self):
        """关闭数据库连接"""
        if hasattr(self, 'conn') and self.conn.is_connected():
            self.conn.close()
            print("🔗 MySQL连接已关闭")

    def run_analysis(self):
        """运行完整分析"""
        print("🎯 散户资金流分析")
        print("=" * 60)

        try:
            # 连接数据库
            if not self.connect_mysql():
                return False

            # 加载和计算数据
            if not self.load_and_calculate_data():
                return False

            # 打印统计信息
            self.print_statistics()

            # 创建图表
            self.create_money_flow_charts()
            self.create_correlation_analysis()

            print("\n🎉 散户资金流分析完成！")
            print("📊 已生成以下图表文件:")
            print("   • money_flow_analysis.png - 资金流时间序列分析")
            print("   • money_flow_correlation.png - 相关性分析")

            return True

        finally:
            self.close_connection()

def main():
    """主函数"""
    analyzer = MoneyFlowAnalyzer()
    analyzer.run_analysis()

if __name__ == "__main__":
    main()
