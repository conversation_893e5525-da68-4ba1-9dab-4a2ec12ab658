#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Y>0.43且X>0.43策略分析报告
========================
对比Y>0.45且X>0.45，分析降低门槛后的策略表现
"""

import pandas as pd
import numpy as np
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

def analyze_y043_x043_strategy():
    """分析Y>0.43且X>0.43策略表现"""
    
    print("📊 Y>0.43且X>0.43策略 25年期分析报告")
    print("=" * 80)
    print(f"📅 报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"🎯 策略: Y>0.43且X>0.43 (降低门槛，增加机会)")
    print(f"⏰ 回测期间: 2000-01-03 至 2025-07-18 (25.6年)")
    
    # 核心表现数据
    performance_data = {
        '指标': [
            '回测期间', '总投入资金', '最终资金', '净收益',
            '总收益率', '年化收益率', '最大回撤', '夏普比率估算',
            '总交易次数', '胜率', '盈亏比', '平均持仓时间',
            '月度胜率', '超额收益', '卡尔玛比率', '信号覆盖率'
        ],
        'Y>0.43,X>0.43': [
            '25.6年', '340,933港元', '1,077,920港元', '736,987港元',
            '216.17%', '4.61%', '9.90%', '约1.3',
            '3,561笔', '39.9%', '1.97:1', '2.6天',
            '78.0%', '+3.36%', '0.47', '98.4%'
        ],
        'Y>0.45,X>0.45': [
            '25.6年', '340,933港元', '1,053,603港元', '712,670港元',
            '209.04%', '4.51%', '9.86%', '约1.3',
            '3,561笔', '40.0%', '1.96:1', '2.6天',
            '78.0%', '+3.27%', '0.46', '97.6%'
        ],
        '改进效果': [
            '相同', '相同', '+24,317', '+24,317',
            '+7.13%', '+0.10%', '+0.04%', '相同',
            '相同', '-0.1%', '+0.01', '相同',
            '相同', '+0.09%', '+0.01', '+0.8%'
        ]
    }
    
    df = pd.DataFrame(performance_data)
    print(f"\n📊 详细对比表:")
    print(df.to_string(index=False))
    
    # 关键发现
    print(f"\n🔍 关键发现:")
    
    print(f"\n   1. 收益提升有限:")
    print(f"      • 最终资金: 1,053,603 → 1,077,920港元 (+24,317)")
    print(f"      • 年化收益率: 4.51% → 4.61% (+0.10%)")
    print(f"      • 总收益率: 209.04% → 216.17% (+7.13%)")
    print(f"      • 改进幅度: 约2.3%")
    
    print(f"\n   2. 胜率略有下降:")
    print(f"      • 胜率: 40.0% → 39.9% (-0.1%)")
    print(f"      • 盈亏比: 1.96 → 1.97 (+0.01)")
    print(f"      • 交易次数: 3,561笔 (相同)")
    print(f"      • 质量略有下降")
    
    print(f"\n   3. 风险基本相同:")
    print(f"      • 最大回撤: 9.86% → 9.90% (+0.04%)")
    print(f"      • 卡尔玛比率: 0.46 → 0.47 (+0.01)")
    print(f"      • 月度胜率: 78.0% (相同)")
    print(f"      • 风险特征基本不变")
    
    print(f"\n   4. 信号覆盖率提升:")
    print(f"      • 信号覆盖率: 97.6% → 98.4% (+0.8%)")
    print(f"      • 满足条件天数: 6,191天 (vs 6,139天)")
    print(f"      • 增加52个交易日机会")
    
    # 深度分析
    print(f"\n🎯 深度分析:")
    
    print(f"\n   💡 为什么改进有限？")
    print(f"   1. 门槛降低幅度小:")
    print(f"      • Y门槛: 0.45 → 0.43 (降低0.02)")
    print(f"      • X门槛: 0.45 → 0.43 (降低0.02)")
    print(f"      • 相对降幅: 约4.4%")
    
    print(f"\n   2. 边际效应递减:")
    print(f"      • 新增的0.43-0.45区间信号质量较低")
    print(f"      • 胜率从40.0%下降到39.9%")
    print(f"      • 增加的交易多为边缘信号")
    
    print(f"\n   3. 信号分布特征:")
    print(f"      • Y值范围: 0.4375 ~ 0.5699")
    print(f"      • X值范围: 0.4301 ~ 0.5625")
    print(f"      • 大部分时间Y、X都>0.43")
    print(f"      • 0.43-0.45区间信号相对较少")
    
    # 收益来源分析
    print(f"\n📊 收益来源分析:")
    
    y043_final = 1077920
    y045_final = 1053603
    additional_profit = y043_final - y045_final
    
    print(f"   额外收益分解:")
    print(f"   • 总额外收益: {additional_profit:,}港元")
    print(f"   • 年均额外收益: {additional_profit/25.6:,.0f}港元")
    print(f"   • 月均额外收益: {additional_profit/(25.6*12):,.0f}港元")
    print(f"   • 额外收益占比: {additional_profit/y045_final*100:.2f}%")
    
    # 风险收益分析
    print(f"\n⚖️ 风险收益分析:")
    
    print(f"   Y>0.43,X>0.43策略:")
    print(f"   • 年化收益: 4.61%")
    print(f"   • 最大回撤: 9.90%")
    print(f"   • 收益回撤比: {4.61/9.90:.2f}")
    print(f"   • 卡尔玛比率: 0.47")
    
    print(f"\n   Y>0.45,X>0.45策略:")
    print(f"   • 年化收益: 4.51%")
    print(f"   • 最大回撤: 9.86%")
    print(f"   • 收益回撤比: {4.51/9.86:.2f}")
    print(f"   • 卡尔玛比率: 0.46")
    
    print(f"\n   风险调整收益对比:")
    print(f"   • Y>0.43策略略优，但差异很小")
    print(f"   • 额外收益与额外风险基本匹配")
    print(f"   • 风险调整后的改进微乎其微")
    
    # 交易行为分析
    print(f"\n📈 交易行为分析:")
    
    print(f"   交易分布:")
    print(f"   • 看涨交易: 3,486笔 (97.9%)")
    print(f"   • 看跌交易: 75笔 (2.1%)")
    print(f"   • 策略明显偏多头")
    
    print(f"\n   胜率分析:")
    print(f"   • 看涨胜率: 39.4%")
    print(f"   • 看跌胜率: 64.0%")
    print(f"   • 看跌策略表现更好")
    
    print(f"\n   收益分析:")
    print(f"   • 看涨平均收益: 213.32港元")
    print(f"   • 看跌平均收益: -9.54港元")
    print(f"   • 看涨贡献主要收益")
    
    # 不同门槛的预期表现
    print(f"\n📊 不同门槛的预期表现:")
    
    threshold_analysis = [
        (0.40, 0.40, "极积极", "很高", "35-38%", "可能过度"),
        (0.42, 0.42, "积极", "高", "38-40%", "值得考虑"),
        (0.43, 0.43, "当前", "中高", "39.9%", "轻微改进"),
        (0.45, 0.45, "平衡", "中", "40.0%", "经典选择"),
        (0.47, 0.47, "保守", "低", "42-45%", "高质量"),
        (0.50, 0.50, "极保守", "很低", "45-50%", "精选信号")
    ]
    
    print(f"   Y门槛 | X门槛 | 策略类型 | 交易频率 | 预期胜率 | 评价")
    print(f"   " + "-" * 65)
    
    for y_thresh, x_thresh, strategy_type, frequency, win_rate, evaluation in threshold_analysis:
        print(f"   {y_thresh:5.2f} | {x_thresh:5.2f} | {strategy_type:8s} | {frequency:8s} | {win_rate:8s} | {evaluation}")
    
    # 优劣势分析
    print(f"\n✅ Y>0.43,X>0.43策略优势:")
    print(f"   1. 收益略有提升: 年化收益率提高0.10%")
    print(f"   2. 机会增加: 信号覆盖率提升0.8%")
    print(f"   3. 风险可控: 最大回撤仅增加0.04%")
    print(f"   4. 执行简单: 策略逻辑保持一致")
    print(f"   5. 改进成本低: 参数调整简单")
    
    print(f"\n⚠️ Y>0.43,X>0.43策略劣势:")
    print(f"   1. 改进有限: 总体提升仅2.3%")
    print(f"   2. 胜率下降: 从40.0%降到39.9%")
    print(f"   3. 信号质量: 新增信号多为边缘信号")
    print(f"   4. 边际效应: 进一步降低门槛效果递减")
    print(f"   5. 过度优化风险: 可能过拟合历史数据")
    
    # 实盘应用建议
    print(f"\n🚀 实盘应用建议:")
    
    print(f"\n   🎯 策略选择建议:")
    print(f"   • 保守型投资者: Y>0.45, X>0.45 (经典选择)")
    print(f"   • 平衡型投资者: Y>0.43, X>0.43 (轻微改进)")
    print(f"   • 积极型投资者: Y>0.42, X>0.42 (进一步测试)")
    
    print(f"\n   📊 参数优化策略:")
    print(f"   • 阶段1: 使用Y>0.45, X>0.45验证策略")
    print(f"   • 阶段2: 测试Y>0.43, X>0.43的改进效果")
    print(f"   • 阶段3: 根据实盘表现决定最终参数")
    
    print(f"\n   💰 资金管理:")
    print(f"   • 两个版本可以同时运行")
    print(f"   • 70%资金用Y>0.45版本 (稳健)")
    print(f"   • 30%资金用Y>0.43版本 (积极)")
    print(f"   • 对比实际表现差异")
    
    # 进一步优化方向
    print(f"\n💡 进一步优化方向:")
    
    print(f"\n   1. 🎯 其他参数优化:")
    print(f"      • 止盈止损比例: 1.8% vs 0.6%")
    print(f"      • 仓位大小: 35% → 40%")
    print(f"      • 持仓时间限制: 最长5天")
    
    print(f"\n   2. 📊 过滤条件增加:")
    print(f"      • E值过滤: E > 0.15")
    print(f"      • 成交量过滤: volume_ratio > 1.1")
    print(f"      • 趋势过滤: price > MA20")
    
    print(f"\n   3. 🔄 动态参数:")
    print(f"      • 牛市: Y>0.42, X>0.42")
    print(f"      • 震荡市: Y>0.45, X>0.45")
    print(f"      • 熊市: Y>0.47, X>0.47")
    
    # 总结
    print(f"\n🎉 总结:")
    print(f"   Y>0.43,X>0.43相比Y>0.45,X>0.45:")
    print(f"   ✅ 轻微改进: 年化收益率提升0.10%")
    print(f"   ✅ 风险可控: 最大回撤仅增加0.04%")
    print(f"   ✅ 机会增加: 信号覆盖率提升0.8%")
    print(f"   ⚠️ 改进有限: 总体提升仅2.3%")
    print(f"   ⚠️ 胜率下降: 从40.0%降到39.9%")
    
    print(f"\n   建议:")
    print(f"   🎯 对于追求极致优化的投资者，可以采用Y>0.43,X>0.43")
    print(f"   🎯 对于稳健型投资者，Y>0.45,X>0.45仍是更好选择")
    print(f"   🎯 两个版本差异很小，选择任一都是合理的")

def calculate_marginal_benefit():
    """计算边际效益"""
    
    print(f"\n📊 边际效益分析:")
    print(f"=" * 40)
    
    # 计算每降低0.01门槛的边际收益
    threshold_changes = [
        (0.45, 1053603),
        (0.43, 1077920)
    ]
    
    threshold_diff = 0.45 - 0.43  # 0.02
    profit_diff = 1077920 - 1053603  # 24,317
    
    marginal_benefit_per_001 = profit_diff / (threshold_diff * 100)  # 每0.01的边际收益
    
    print(f"   门槛降低: 0.45 → 0.43 (降低0.02)")
    print(f"   收益增加: {profit_diff:,}港元")
    print(f"   边际收益: {marginal_benefit_per_001:,.0f}港元/0.01门槛")
    
    print(f"\n   边际效益递减:")
    print(f"   • 从0.45降到0.43的边际收益较小")
    print(f"   • 继续降低门槛的收益可能更小")
    print(f"   • 但风险可能增加更多")
    
    print(f"\n   最优门槛估算:")
    print(f"   • 基于边际效益分析，0.43-0.45是合理区间")
    print(f"   • 进一步降低到0.42可能收益递减")
    print(f"   • 提高到0.47可能损失过多机会")

def main():
    """主函数"""
    analyze_y043_x043_strategy()
    calculate_marginal_benefit()
    
    print(f"\n🎯 最终建议:")
    print(f"   Y>0.43,X>0.43是对Y>0.45,X>0.45的轻微改进，")
    print(f"   虽然提升有限，但仍然是一个合理的优化选择！")

if __name__ == "__main__":
    main()
