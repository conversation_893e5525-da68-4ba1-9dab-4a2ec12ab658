#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
交易记录追踪0023HK
================

东亚银行(0023.HK)专用交易记录追踪系统
严格按照Excel表格逻辑执行交易记录和分析

功能特点：
1. 实时获取0023HK市场数据
2. 基于XYE技术指标生成交易信号
3. 严格执行止盈止损策略
4. 完整记录所有交易细节
5. 自动生成Excel报告和分析图表
6. 支持每月定投计划

作者: Cosmoon NG
日期: 2025年7月28日
版本: v2.0
"""

import yfinance as yf
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from scipy import stats
import warnings
import os
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class TradingTracker0023HK:
    """东亚银行(0023.HK)交易记录追踪系统"""

    def __init__(self):
        """初始化交易追踪系统"""
        self.symbol = "0023.HK"
        self.company_name = "东亚银行"

        # 交易参数设置
        self.initial_capital = 10000  # 初始资金10,000港元
        self.monthly_addition = 3000  # 每月追加3,000港元

        # 止盈止损参数（严格执行）
        self.take_profit_long = 0.025   # 多头止盈 2.5%
        self.stop_loss_long = 0.015     # 多头止损 1.5%
        self.take_profit_short = 0.015  # 空头止盈 1.5%
        self.stop_loss_short = 0.025    # 空头止损 2.5%

        # 交易手续费
        self.commission_rate = 0.001    # 0.1%手续费

        # 当前交易状态
        self.position = 0               # 持仓状态：1=多头，-1=空头，0=空仓
        self.entry_price = 0           # 开仓价格
        self.current_capital = self.initial_capital
        self.total_invested = self.initial_capital

        # 交易记录表格结构（完全按照Excel逻辑）
        self.trading_records = pd.DataFrame(columns=[
            '交易序号',      # 交易编号
            '交易日期',      # 交易发生日期
            '交易类型',      # 开仓/平仓/资金注入
            '交易方向',      # 多头/空头/追加资金
            '交易价格',      # 成交价格
            '持仓数量',      # 持仓股数
            '交易金额',      # 交易总金额
            '手续费',        # 交易手续费
            '净交易额',      # 扣除手续费后金额
            '持仓成本',      # 平均持仓成本
            '当前市值',      # 当前持仓市值
            '浮动盈亏',      # 未实现盈亏
            '实现盈亏',      # 已实现盈亏
            '累计盈亏',      # 累计总盈亏
            '账户余额',      # 当前账户余额
            '总资产',        # 总资产价值
            '收益率',        # 当日收益率
            '累计收益率',    # 累计收益率
            'Y值',          # 技术指标Y值
            'X值',          # 技术指标X值
            'E值',          # 技术指标E值
            '信号强度',      # 交易信号强度
            '风险等级',      # 当前风险等级
            '平仓原因',      # 平仓原因
            '备注'          # 交易备注
        ])

        # 统计数据
        self.trade_count = 0
        self.win_count = 0
        self.loss_count = 0
        self.total_profit = 0
        self.total_loss = 0
        self.max_profit = 0
        self.max_loss = 0
        self.max_drawdown = 0
        self.peak_value = self.initial_capital

        print(f"🎯 {self.company_name}({self.symbol})交易记录追踪系统已启动")
        print(f"💰 初始资金: {self.initial_capital:,.2f} 港元")
        print(f"📈 每月追加: {self.monthly_addition:,.2f} 港元")
        print("📊 严格按照Excel表格逻辑执行交易记录")

    def load_market_data(self, period="2y"):
        """加载0023HK市场数据"""
        print(f"\n📊 加载{self.symbol}市场数据...")

        try:
            # 获取历史数据
            end_date = datetime.now()
            if period.endswith('y'):
                years = int(period[:-1])
                start_date = end_date - timedelta(days=years*365)
            elif period.endswith('m'):
                months = int(period[:-1])
                start_date = end_date - timedelta(days=months*30)
            else:
                start_date = end_date - timedelta(days=730)  # 默认2年

            # 获取Yahoo Finance数据
            ticker = yf.Ticker(self.symbol)
            hist_data = ticker.history(start=start_date, end=end_date)

            if hist_data.empty:
                raise ValueError(f"无法获取{self.symbol}的数据")

            # 转换为标准格式
            self.market_data = pd.DataFrame({
                'date': hist_data.index,
                'open': hist_data['Open'].values,
                'high': hist_data['High'].values,
                'low': hist_data['Low'].values,
                'close': hist_data['Close'].values,
                'volume': hist_data['Volume'].values
            })

            # 计算XYE技术指标
            print("   计算XYE技术指标...")
            self.calculate_xye_indicators()

            print(f"✅ 成功加载 {len(self.market_data)} 条数据")
            print(f"   数据范围: {self.market_data['date'].min().strftime('%Y-%m-%d')} 至 {self.market_data['date'].max().strftime('%Y-%m-%d')}")
            print(f"   当前价格: {self.market_data['close'].iloc[-1]:.2f} 港元")

            return True

        except Exception as e:
            print(f"❌ 数据加载失败: {e}")
            return False

    def calculate_xye_indicators(self):
        """计算XYE技术指标"""
        df = self.market_data.copy()

        # 计算移动平均线
        df['ma5'] = df['close'].rolling(window=5).mean()
        df['ma10'] = df['close'].rolling(window=10).mean()
        df['ma20'] = df['close'].rolling(window=20).mean()

        # 计算Y值（资金流向指标）
        df['typical_price'] = (df['high'] + df['low'] + df['close']) / 3
        df['money_flow'] = df['typical_price'] * df['volume']
        df['positive_flow'] = np.where(df['typical_price'] > df['typical_price'].shift(1),
                                      df['money_flow'], 0)
        df['negative_flow'] = np.where(df['typical_price'] < df['typical_price'].shift(1),
                                      df['money_flow'], 0)

        # 14日资金流向比率
        period = 14
        df['positive_flow_sum'] = df['positive_flow'].rolling(window=period).sum()
        df['negative_flow_sum'] = df['negative_flow'].rolling(window=period).sum()
        df['money_flow_ratio'] = df['positive_flow_sum'] / (df['negative_flow_sum'] + 1e-10)
        df['y_value'] = 100 - (100 / (1 + df['money_flow_ratio']))

        # 计算X值（市场情绪指标）
        df['price_change'] = df['close'].pct_change()
        df['volatility'] = df['price_change'].rolling(window=20).std()
        df['rsi'] = self.calculate_rsi(df['close'], 14)
        df['x_value'] = (df['rsi'] / 100) * (1 - df['volatility'].fillna(0))

        # 计算E值（控股商控制指标）
        df['volume_ma'] = df['volume'].rolling(window=20).mean()
        df['volume_ratio'] = df['volume'] / (df['volume_ma'] + 1e-10)
        df['price_volume_trend'] = ((df['close'] - df['close'].shift(1)) / df['close'].shift(1)) * df['volume_ratio']
        df['e_value'] = df['price_volume_trend'].rolling(window=10).mean()

        # 生成交易信号
        df['signal'] = self.generate_trading_signals(df)

        # 更新市场数据
        self.market_data = df.fillna(0)

        print("   XYE指标计算完成")

    def calculate_rsi(self, prices, period=14):
        """计算RSI指标"""
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / (loss + 1e-10)
        rsi = 100 - (100 / (1 + rs))
        return rsi

    def generate_trading_signals(self, df):
        """生成交易信号"""
        signals = []

        for i in range(len(df)):
            y_val = df['y_value'].iloc[i]
            x_val = df['x_value'].iloc[i]
            e_val = df['e_value'].iloc[i]

            # 信号判断逻辑
            if y_val > 0.6 and x_val > 0.5 and e_val > 0:
                signals.append('强烈买入')
            elif y_val < 0.4 and x_val < 0.5 and e_val < 0:
                signals.append('强烈卖出')
            elif y_val > 0.5 and x_val > 0.4:
                signals.append('买入')
            elif y_val < 0.5 and x_val < 0.6:
                signals.append('卖出')
            else:
                signals.append('观望')

        return signals

    def add_monthly_capital(self, date):
        """添加每月资金注入记录"""
        if date.day == 1:  # 每月1日注入资金
            self.current_capital += self.monthly_addition
            self.total_invested += self.monthly_addition

            # 记录资金注入
            self.add_trading_record(
                date=date,
                trade_type="资金注入",
                direction="追加资金",
                price=0.0,
                quantity=0,
                realized_pnl=0,
                note=f"每月定投追加{self.monthly_addition:,.0f}港元"
            )

    def add_trading_record(self, date, trade_type, direction, price, quantity=0,
                          realized_pnl=0, close_reason="", note=""):
        """添加交易记录到Excel表格结构"""

        # 获取当前技术指标
        current_data = self.market_data[self.market_data['date'].dt.date == date.date()]
        if len(current_data) == 0:
            current_data = self.market_data.iloc[-1]
            y_val, x_val, e_val = current_data['y_value'], current_data['x_value'], current_data['e_value']
            signal = current_data['signal']
        else:
            current_data = current_data.iloc[-1]
            y_val, x_val, e_val = current_data['y_value'], current_data['x_value'], current_data['e_value']
            signal = current_data['signal']

        # 计算交易金额和手续费
        trade_amount = price * quantity if quantity > 0 else 0
        commission = trade_amount * self.commission_rate if trade_amount > 0 else 0
        net_amount = trade_amount - commission

        # 计算当前市值和浮动盈亏
        current_price = self.market_data['close'].iloc[-1]
        if self.position != 0 and quantity > 0:
            current_market_value = current_price * quantity
            floating_pnl = (current_price - self.entry_price) * quantity * self.position
        else:
            current_market_value = 0
            floating_pnl = 0

        # 计算累计盈亏
        if hasattr(self, 'cumulative_pnl'):
            self.cumulative_pnl += realized_pnl
        else:
            self.cumulative_pnl = realized_pnl

        # 计算总资产
        total_assets = self.current_capital + current_market_value

        # 计算收益率
        daily_return = (realized_pnl / self.current_capital * 100) if self.current_capital > 0 else 0
        cumulative_return = ((total_assets - self.total_invested) / self.total_invested * 100) if self.total_invested > 0 else 0

        # 风险等级评估
        if abs(floating_pnl) / total_assets > 0.1:
            risk_level = "高风险"
        elif abs(floating_pnl) / total_assets > 0.05:
            risk_level = "中风险"
        else:
            risk_level = "低风险"

        # 创建新记录
        new_record = {
            '交易序号': len(self.trading_records) + 1,
            '交易日期': date.strftime('%Y-%m-%d'),
            '交易类型': trade_type,
            '交易方向': direction,
            '交易价格': round(price, 2),
            '持仓数量': quantity,
            '交易金额': round(trade_amount, 2),
            '手续费': round(commission, 2),
            '净交易额': round(net_amount, 2),
            '持仓成本': round(self.entry_price, 2) if self.position != 0 else 0,
            '当前市值': round(current_market_value, 2),
            '浮动盈亏': round(floating_pnl, 2),
            '实现盈亏': round(realized_pnl, 2),
            '累计盈亏': round(self.cumulative_pnl, 2),
            '账户余额': round(self.current_capital, 2),
            '总资产': round(total_assets, 2),
            '收益率': round(daily_return, 2),
            '累计收益率': round(cumulative_return, 2),
            'Y值': round(y_val, 4),
            'X值': round(x_val, 4),
            'E值': round(e_val, 4),
            '信号强度': signal,
            '风险等级': risk_level,
            '平仓原因': close_reason,
            '备注': note
        }

        # 添加到记录中
        self.trading_records = pd.concat([self.trading_records, pd.DataFrame([new_record])], ignore_index=True)

        print(f"📝 交易记录已添加: {date.strftime('%Y-%m-%d')} | {trade_type} | {direction} | {price:.2f}")

    def execute_trading_strategy(self):
        """执行交易策略并记录所有交易"""
        print(f"\n🎯 开始执行{self.symbol}交易策略...")

        for i, row in self.market_data.iterrows():
            current_date = row['date']
            current_price = row['close']
            signal = row['signal']

            # 检查是否需要添加月度资金
            self.add_monthly_capital(current_date)

            # 检查止盈止损
            if self.position != 0:
                self.check_stop_loss_take_profit(current_date, current_price)

            # 根据信号执行交易
            if self.position == 0:  # 空仓状态
                if signal in ['强烈买入', '买入']:
                    self.open_long_position(current_date, current_price)
                elif signal in ['强烈卖出', '卖出']:
                    self.open_short_position(current_date, current_price)

        print(f"✅ 交易策略执行完成，共生成 {len(self.trading_records)} 条交易记录")

    def open_long_position(self, date, price):
        """开多头仓位"""
        if self.current_capital > 1000:  # 最少1000港元才能开仓
            # 使用70%资金开仓
            investment_amount = self.current_capital * 0.7
            quantity = int(investment_amount / price)

            if quantity > 0:
                self.position = 1
                self.entry_price = price
                self.current_capital -= quantity * price * (1 + self.commission_rate)

                self.add_trading_record(
                    date=date,
                    trade_type="开仓",
                    direction="多头",
                    price=price,
                    quantity=quantity,
                    note=f"多头开仓，投入{investment_amount:.0f}港元"
                )

    def open_short_position(self, date, price):
        """开空头仓位"""
        if self.current_capital > 1000:  # 最少1000港元才能开仓
            # 使用70%资金开仓
            investment_amount = self.current_capital * 0.7
            quantity = int(investment_amount / price)

            if quantity > 0:
                self.position = -1
                self.entry_price = price
                self.current_capital -= quantity * price * (1 + self.commission_rate)

                self.add_trading_record(
                    date=date,
                    trade_type="开仓",
                    direction="空头",
                    price=price,
                    quantity=quantity,
                    note=f"空头开仓，投入{investment_amount:.0f}港元"
                )

    def check_stop_loss_take_profit(self, date, current_price):
        """检查止盈止损"""
        if self.position == 0:
            return

        price_change = (current_price - self.entry_price) / self.entry_price

        if self.position == 1:  # 多头仓位
            if price_change >= self.take_profit_long:
                self.close_position(date, current_price, "多头止盈")
            elif price_change <= -self.stop_loss_long:
                self.close_position(date, current_price, "多头止损")

        elif self.position == -1:  # 空头仓位
            if price_change <= -self.take_profit_short:
                self.close_position(date, current_price, "空头止盈")
            elif price_change >= self.stop_loss_short:
                self.close_position(date, current_price, "空头止损")

    def close_position(self, date, price, reason):
        """平仓"""
        if self.position == 0:
            return

        # 获取最后一条开仓记录的数量
        last_trade = self.trading_records[self.trading_records['交易类型'] == '开仓'].iloc[-1]
        quantity = last_trade['持仓数量']

        # 计算盈亏
        if self.position == 1:  # 多头平仓
            realized_pnl = (price - self.entry_price) * quantity
        else:  # 空头平仓
            realized_pnl = (self.entry_price - price) * quantity

        # 扣除手续费
        commission = price * quantity * self.commission_rate
        realized_pnl -= commission

        # 更新资金
        self.current_capital += price * quantity - commission

        # 更新统计
        self.trade_count += 1
        if realized_pnl > 0:
            self.win_count += 1
            self.total_profit += realized_pnl
            self.max_profit = max(self.max_profit, realized_pnl)
        else:
            self.loss_count += 1
            self.total_loss += abs(realized_pnl)
            self.max_loss = max(self.max_loss, abs(realized_pnl))

        # 记录平仓
        self.add_trading_record(
            date=date,
            trade_type="平仓",
            direction=reason,
            price=price,
            quantity=quantity,
            realized_pnl=realized_pnl,
            close_reason=reason,
            note=f"{reason}，盈亏{realized_pnl:.2f}港元"
        )

        # 重置仓位
        self.position = 0
        self.entry_price = 0

    def save_trading_records(self):
        """保存交易记录到Excel文件"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"交易记录追踪{self.symbol.replace('.', '')}_{timestamp}.xlsx"

        try:
            # 格式化数据
            df_save = self.trading_records.copy()

            # 格式化数值列
            numeric_cols = ['交易价格', '交易金额', '手续费', '净交易额', '持仓成本',
                           '当前市值', '浮动盈亏', '实现盈亏', '累计盈亏', '账户余额',
                           '总资产', '收益率', '累计收益率', 'Y值', 'X值', 'E值']

            for col in numeric_cols:
                if col in df_save.columns:
                    df_save[col] = pd.to_numeric(df_save[col], errors='coerce').round(4)

            # 保存到Excel
            df_save.to_excel(filename, index=False, sheet_name='交易记录')

            print(f"💾 交易记录已保存到: {filename}")
            return filename

        except Exception as e:
            print(f"❌ 保存交易记录失败: {e}")
            return None

    def analyze_trading_performance(self):
        """分析交易表现"""
        print(f"\n📊 {self.symbol}交易表现分析")
        print("=" * 60)

        if len(self.trading_records) == 0:
            print("❌ 没有交易记录可分析")
            return

        # 基本统计
        total_trades = len(self.trading_records[self.trading_records['交易类型'] == '开仓'])
        profit_trades = len(self.trading_records[self.trading_records['实现盈亏'] > 0])
        loss_trades = len(self.trading_records[self.trading_records['实现盈亏'] < 0])

        print(f"📈 交易统计:")
        print(f"   总交易次数: {total_trades}")
        print(f"   盈利交易: {profit_trades}")
        print(f"   亏损交易: {loss_trades}")
        if total_trades > 0:
            print(f"   胜率: {profit_trades/(profit_trades+loss_trades)*100:.2f}%")

        # 收益分析
        final_assets = self.trading_records['总资产'].iloc[-1] if len(self.trading_records) > 0 else self.initial_capital
        total_return = ((final_assets - self.total_invested) / self.total_invested * 100)
        max_return = self.trading_records['累计收益率'].max() if len(self.trading_records) > 0 else 0

        print(f"\n💰 收益分析:")
        print(f"   初始资金: {self.initial_capital:,.2f} 港元")
        print(f"   总投入: {self.total_invested:,.2f} 港元")
        print(f"   最终资产: {final_assets:,.2f} 港元")
        print(f"   累计收益率: {total_return:.2f}%")
        print(f"   最高收益率: {max_return:.2f}%")

        # 风险分析
        if self.trade_count > 0:
            avg_profit = self.total_profit / self.win_count if self.win_count > 0 else 0
            avg_loss = self.total_loss / self.loss_count if self.loss_count > 0 else 0
            profit_factor = self.total_profit / self.total_loss if self.total_loss > 0 else float('inf')

            print(f"\n⚠️ 风险分析:")
            print(f"   平均盈利: {avg_profit:.2f} 港元")
            print(f"   平均亏损: {-avg_loss:.2f} 港元")
            print(f"   最大盈利: {self.max_profit:.2f} 港元")
            print(f"   最大亏损: {-self.max_loss:.2f} 港元")
            print(f"   盈亏比: {profit_factor:.2f}")

        # XYE信号分析
        signal_counts = self.trading_records['信号强度'].value_counts()
        print(f"\n🎯 XYE信号分析:")
        for signal, count in signal_counts.items():
            print(f"   {signal}: {count}次")

    def create_performance_charts(self):
        """创建表现分析图表"""
        if len(self.trading_records) == 0:
            print("❌ 没有数据可绘制图表")
            return None

        try:
            # 创建图表
            fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))

            # 1. 资产增长曲线
            dates = pd.to_datetime(self.trading_records['交易日期'])
            assets = self.trading_records['总资产']

            ax1.plot(dates, assets, 'b-', linewidth=2, label='总资产')
            ax1.axhline(y=self.initial_capital, color='r', linestyle='--', alpha=0.7, label='初始资金')
            ax1.set_title(f'{self.symbol} 资产增长曲线', fontsize=14, fontweight='bold')
            ax1.set_ylabel('资产价值 (港元)')
            ax1.legend()
            ax1.grid(True, alpha=0.3)

            # 2. 累计收益率
            returns = self.trading_records['累计收益率']
            ax2.plot(dates, returns, 'g-', linewidth=2)
            ax2.axhline(y=0, color='r', linestyle='--', alpha=0.7)
            ax2.set_title('累计收益率变化', fontsize=14, fontweight='bold')
            ax2.set_ylabel('收益率 (%)')
            ax2.grid(True, alpha=0.3)

            # 3. 交易盈亏分布
            realized_pnl = self.trading_records[self.trading_records['实现盈亏'] != 0]['实现盈亏']
            if len(realized_pnl) > 0:
                ax3.hist(realized_pnl, bins=20, alpha=0.7, color='skyblue', edgecolor='black')
                ax3.axvline(x=0, color='r', linestyle='--', alpha=0.7)
                ax3.set_title('交易盈亏分布', fontsize=14, fontweight='bold')
                ax3.set_xlabel('盈亏金额 (港元)')
                ax3.set_ylabel('交易次数')
                ax3.grid(True, alpha=0.3)

            # 4. XYE信号分布
            signal_counts = self.trading_records['信号强度'].value_counts()
            if len(signal_counts) > 0:
                ax4.pie(signal_counts.values, labels=signal_counts.index, autopct='%1.1f%%')
                ax4.set_title('XYE信号分布', fontsize=14, fontweight='bold')

            # 调整布局并保存
            plt.tight_layout()
            chart_filename = f'{self.symbol.replace(".", "")}交易分析图表_{datetime.now().strftime("%Y%m%d_%H%M%S")}.png'
            plt.savefig(chart_filename, dpi=300, bbox_inches='tight')
            plt.close()

            print(f"📊 分析图表已保存到: {chart_filename}")
            return chart_filename

        except Exception as e:
            print(f"❌ 图表创建失败: {e}")
            return None

    def generate_complete_report(self):
        """生成完整的交易报告"""
        print(f"\n📋 生成{self.symbol}完整交易报告...")

        # 执行交易策略
        self.execute_trading_strategy()

        # 保存交易记录
        excel_file = self.save_trading_records()

        # 分析交易表现
        self.analyze_trading_performance()

        # 创建图表
        chart_file = self.create_performance_charts()

        # 生成总结报告
        if len(self.trading_records) > 0:
            print(f"\n📄 交易报告总结:")
            print(f"   📊 Excel记录文件: {excel_file}")
            print(f"   📈 分析图表文件: {chart_file}")
            print(f"   📝 交易记录条数: {len(self.trading_records)}")
            print(f"   💰 最终资产价值: {self.trading_records['总资产'].iloc[-1]:,.2f} 港元")
            print(f"   📈 累计收益率: {self.trading_records['累计收益率'].iloc[-1]:.2f}%")

        return excel_file, chart_file

def main():
    """主函数 - 运行0023HK交易记录追踪系统"""
    print("🎯 交易记录追踪0023HK系统")
    print("=" * 50)

    try:
        # 创建交易追踪器
        tracker = TradingTracker0023HK()

        # 加载市场数据
        if not tracker.load_market_data(period="2y"):
            return None

        # 生成完整报告
        excel_file, chart_file = tracker.generate_complete_report()

        print(f"\n✅ 交易记录追踪系统运行完成")
        print(f"💡 所有文件已生成，可查看详细交易记录")

        return tracker

    except Exception as e:
        print(f"\n❌ 系统运行失败: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    tracker = main()
