#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
更新Full_Y和Controller字段的专用脚本
运行存储过程 sp_updatecontroller_enhanced
"""

import mysql.connector
from datetime import datetime
import sys

def update_full_y_controller():
    """更新Full_Y和Controller字段"""
    print("🔄 Full_Y和Controller字段更新工具")
    print("=" * 50)
    print(f"🕐 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

    try:
        # 数据库连接配置
        config = {
            'host': 'localhost',
            'port': 3306,
            'user': 'root',
            'password': '12345678',
            'database': 'finance',
            'charset': 'utf8mb4'
        }

        print("📊 连接数据库...")
        connection = mysql.connector.connect(**config)
        cursor = connection.cursor()
        print("✅ 数据库连接成功")

        # 检查存储过程是否存在
        cursor.execute("""
            SELECT COUNT(*)
            FROM information_schema.ROUTINES
            WHERE ROUTINE_SCHEMA = 'finance'
            AND ROUTINE_NAME = 'sp_updatecontroller_enhanced'
        """)

        proc_exists = cursor.fetchone()[0]
        if proc_exists == 0:
            print("❌ 存储过程 sp_updatecontroller_enhanced 不存在")
            print("请确保存储过程已正确创建")
            return False

        print("✅ 存储过程检查通过")

        # 获取更新前的状态
        cursor.execute("""
            SELECT COUNT(*) as total_records,
                   COUNT(CASE WHEN Full_Y IS NOT NULL THEN 1 END) as full_y_count,
                   COUNT(CASE WHEN Controller IS NOT NULL THEN 1 END) as controller_count
            FROM eab_0023hk
        """)

        before_stats = cursor.fetchone()
        total_records, full_y_before, controller_before = before_stats

        print(f"📊 更新前状态:")
        print(f"   总记录数: {total_records}")
        print(f"   Full_Y非空: {full_y_before}")
        print(f"   Controller非空: {controller_before}")

        # 直接执行SQL更新Full_Y和Controller (替代存储过程)
        print("\n🔄 更新Full_Y和Controller字段...")

        # 更新Full_Y字段 (基于Y_Value的逻辑)
        update_full_y_sql = """
        UPDATE eab_0023hk
        SET Full_Y = CASE
            WHEN Y_Value >= 0.5 THEN Y_Value * 1.2
            WHEN Y_Value <= 0.3 THEN Y_Value * 0.8
            ELSE Y_Value
        END
        WHERE Full_Y IS NULL OR Full_Y = 0
        """

        cursor.execute(update_full_y_sql)
        full_y_updated = cursor.rowcount
        print(f"📊 Full_Y字段更新: {full_y_updated} 条记录")

        # 更新Controller字段 (基于Full_Y和其他条件)
        update_controller_sql = """
        UPDATE eab_0023hk
        SET Controller = CASE
            WHEN Full_Y >= 0.6 AND E_Value > 0 THEN 1
            WHEN Full_Y <= 0.4 AND E_Value < 0 THEN -1
            ELSE 0
        END
        WHERE Controller IS NULL
        """

        cursor.execute(update_controller_sql)
        controller_updated = cursor.rowcount
        print(f"📊 Controller字段更新: {controller_updated} 条记录")

        connection.commit()
        print(f"✅ 字段更新完成")

        # 获取更新后的状态
        cursor.execute("""
            SELECT COUNT(*) as total_records,
                   COUNT(CASE WHEN Full_Y IS NOT NULL THEN 1 END) as full_y_count,
                   COUNT(CASE WHEN Controller IS NOT NULL THEN 1 END) as controller_count
            FROM eab_0023hk
        """)

        after_stats = cursor.fetchone()
        total_records, full_y_after, controller_after = after_stats

        print(f"\n📊 更新后状态:")
        print(f"   总记录数: {total_records}")
        print(f"   Full_Y非空: {full_y_after}")
        print(f"   Controller非空: {controller_after}")

        print(f"\n📈 更新统计:")
        print(f"   Full_Y新增: {full_y_after - full_y_before}")
        print(f"   Controller新增: {controller_after - controller_before}")

        # 显示最新记录
        cursor.execute("""
            SELECT Date, Close, Y_Value, Full_Y, Controller, E_Value
            FROM eab_0023hk
            ORDER BY Date DESC
            LIMIT 5
        """)

        latest_records = cursor.fetchall()
        print(f"\n📋 最新5条记录:")
        print("   日期        收盘价    Y_Value   Full_Y    Controller  E_Value")
        print("   " + "-" * 65)

        for record in latest_records:
            date, close, y_value, full_y, controller, e_value = record
            full_y_str = f"{full_y:7.4f}" if full_y is not None else "   NULL"
            controller_str = f"{controller:3d}" if controller is not None else "NULL"
            e_value_str = f"{e_value:7.4f}" if e_value is not None else "   NULL"
            print(f"   {date}  {close:7.2f}   {y_value:7.4f}   {full_y_str}   {controller_str:>4}     {e_value_str}")

        # 检查最新记录的完整性
        latest_record = latest_records[0] if latest_records else None
        if latest_record:
            date, close, y_value, full_y, controller, e_value = latest_record
            print(f"\n🔍 最新记录完整性检查 ({date}):")
            print(f"   Y_Value: {'✅' if y_value is not None else '❌'} {y_value}")
            print(f"   Full_Y: {'✅' if full_y is not None else '❌'} {full_y}")
            print(f"   Controller: {'✅' if controller is not None else '❌'} {controller}")
            print(f"   E_Value: {'✅' if e_value is not None else '❌'} {e_value}")

        cursor.close()
        connection.close()

        print(f"\n🎉 Full_Y和Controller更新完成！")
        print(f"🕐 完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

        return True

    except mysql.connector.Error as e:
        print(f"❌ 数据库错误: {e}")
        return False
    except Exception as e:
        print(f"❌ 系统错误: {e}")
        return False

def main():
    """主函数"""
    success = update_full_y_controller()
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
