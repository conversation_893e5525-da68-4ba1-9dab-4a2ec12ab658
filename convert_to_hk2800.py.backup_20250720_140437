#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库转换：从HK00023转换到HK2800 (盈富基金)
"""

import pymysql
import yfinance as yf
import pandas as pd
import numpy as np
from datetime import datetime, <PERSON><PERSON><PERSON>

def convert_to_hk2800():
    """转换数据库到HK2800"""
    
    db_config = {
        'host': '************',
        'port': 3306,
        'database': 'finance',
        'user': 'root',
        'password': '',
        'charset': 'utf8mb4'
    }
    
    try:
        connection = pymysql.connect(**db_config)
        cursor = connection.cursor()
        
        print("🔄 数据库转换：HK00023 → HK2800")
        print("="*60)
        print("📊 目标：盈富基金 (2800.HK)")
        print("📅 获取25年历史数据 (2000-2025)")
        
        # 1. 检查是否已存在hk2800表
        cursor.execute("SHOW TABLES LIKE 'hk2800'")
        table_exists = cursor.fetchone()
        
        if table_exists:
            print("⚠️ hk2800表已存在，是否要重新创建？")
            response = input("输入 'y' 继续，其他键跳过: ")
            if response.lower() != 'y':
                print("❌ 跳过表创建")
                return False
            
            cursor.execute("DROP TABLE hk2800")
            print("🗑️ 已删除旧的hk2800表")
        
        # 2. 创建hk2800表结构（复制hk00023的结构）
        print("🔧 创建hk2800表结构...")
        
        cursor.execute("SHOW CREATE TABLE hk00023")
        create_table_sql = cursor.fetchone()[1]
        
        # 替换表名
        create_table_sql = create_table_sql.replace('hk00023', 'hk2800')
        cursor.execute(create_table_sql)
        
        print("✅ hk2800表结构创建完成")
        
        # 3. 从yfinance获取HK2800数据
        print("📡 从yfinance获取HK2800数据...")
        
        ticker = yf.Ticker("2800.HK")
        
        # 获取最大历史数据
        hist = ticker.history(period="max")
        
        if hist.empty:
            print("❌ 无法获取HK2800数据")
            return False
        
        print(f"📊 获取到 {len(hist)} 条HK2800数据")
        print(f"📅 数据范围: {hist.index[0].date()} 到 {hist.index[-1].date()}")
        
        # 4. 数据预处理
        print("🔧 数据预处理...")
        
        # 重置索引，将日期作为列
        hist_df = hist.reset_index()
        hist_df['Date'] = pd.to_datetime(hist_df['Date']).dt.date
        
        # 重命名列以匹配数据库结构
        hist_df = hist_df.rename(columns={
            'Date': 'date',
            'Open': 'open',
            'High': 'high', 
            'Low': 'low',
            'Close': 'close',
            'Volume': 'volume'
        })
        
        # 添加adj_close（对于ETF，close就是复权价）
        hist_df['adj_close'] = hist_df['close']
        
        # 5. 插入基础价格数据
        print("💾 插入基础价格数据...")
        
        insert_count = 0
        for _, row in hist_df.iterrows():
            try:
                cursor.execute("""
                    INSERT INTO hk2800 (date, open, high, low, close, adj_close, volume)
                    VALUES (%s, %s, %s, %s, %s, %s, %s)
                """, (
                    row['date'],
                    float(row['open']),
                    float(row['high']),
                    float(row['low']),
                    float(row['close']),
                    float(row['adj_close']),
                    int(row['volume'])
                ))
                insert_count += 1
            except Exception as e:
                print(f"⚠️ 插入数据失败: {row['date']} - {e}")
        
        connection.commit()
        print(f"✅ 已插入 {insert_count} 条基础数据")
        
        # 6. 计算技术指标
        print("📊 计算技术指标...")
        
        # 获取所有数据用于计算
        cursor.execute("""
            SELECT date, close, volume
            FROM hk2800 
            ORDER BY date ASC
        """)
        
        data = cursor.fetchall()
        df = pd.DataFrame(data, columns=['date', 'close', 'volume'])
        df['close'] = pd.to_numeric(df['close'])
        df['volume'] = pd.to_numeric(df['volume'])
        
        # 计算移动平均线
        df['ma5'] = df['close'].rolling(window=5).mean()
        df['ma10'] = df['close'].rolling(window=10).mean()
        df['ma20'] = df['close'].rolling(window=20).mean()
        df['ma60'] = df['close'].rolling(window=60).mean()
        
        # 计算RSI
        def calculate_rsi(prices, window=14):
            delta = prices.diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=window).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=window).mean()
            rs = gain / loss
            rsi = 100 - (100 / (1 + rs))
            return rsi
        
        df['rsi'] = calculate_rsi(df['close'])
        
        # 计算MACD
        exp1 = df['close'].ewm(span=12).mean()
        exp2 = df['close'].ewm(span=26).mean()
        df['macd'] = exp1 - exp2
        df['macd_signal'] = df['macd'].ewm(span=9).mean()
        df['macd_histogram'] = df['macd'] - df['macd_signal']
        
        # 计算布林带
        df['bb_middle'] = df['close'].rolling(window=20).mean()
        bb_std = df['close'].rolling(window=20).std()
        df['bb_upper'] = df['bb_middle'] + (bb_std * 2)
        df['bb_lower'] = df['bb_middle'] - (bb_std * 2)
        
        # 7. 更新技术指标到数据库
        print("💾 更新技术指标...")
        
        update_count = 0
        for _, row in df.iterrows():
            try:
                cursor.execute("""
                    UPDATE hk2800 
                    SET ma5 = %s, ma10 = %s, ma20 = %s, ma60 = %s,
                        rsi = %s, macd = %s, macd_signal = %s, macd_histogram = %s,
                        bb_upper = %s, bb_middle = %s, bb_lower = %s
                    WHERE date = %s
                """, (
                    float(row['ma5']) if pd.notna(row['ma5']) else None,
                    float(row['ma10']) if pd.notna(row['ma10']) else None,
                    float(row['ma20']) if pd.notna(row['ma20']) else None,
                    float(row['ma60']) if pd.notna(row['ma60']) else None,
                    float(row['rsi']) if pd.notna(row['rsi']) else None,
                    float(row['macd']) if pd.notna(row['macd']) else None,
                    float(row['macd_signal']) if pd.notna(row['macd_signal']) else None,
                    float(row['macd_histogram']) if pd.notna(row['macd_histogram']) else None,
                    float(row['bb_upper']) if pd.notna(row['bb_upper']) else None,
                    float(row['bb_middle']) if pd.notna(row['bb_middle']) else None,
                    float(row['bb_lower']) if pd.notna(row['bb_lower']) else None,
                    row['date']
                ))
                update_count += 1
            except Exception as e:
                print(f"⚠️ 更新技术指标失败: {row['date']} - {e}")
        
        connection.commit()
        print(f"✅ 已更新 {update_count} 条技术指标")
        
        # 8. 计算22天滚动中位数
        print("📊 计算22天滚动中位数...")
        
        # 重新获取数据计算中位数
        cursor.execute("""
            SELECT date, close
            FROM hk2800 
            ORDER BY date ASC
        """)
        
        price_data = cursor.fetchall()
        
        median_updates = []
        for i, (date, close) in enumerate(price_data):
            # 获取当前记录之前的22天数据（包括当前）
            start_idx = max(0, i - 21)
            window_data = price_data[start_idx:i+1]
            
            # 提取价格
            window_prices = [float(row[1]) for row in window_data]
            
            # 计算中位数
            median_price = np.median(window_prices)
            
            median_updates.append((median_price, date))
        
        # 更新中位数
        for median_price, date in median_updates:
            cursor.execute("""
                UPDATE hk2800 
                SET median_price = %s
                WHERE date = %s
            """, (median_price, date))
        
        connection.commit()
        print(f"✅ 已更新 {len(median_updates)} 条中位数数据")
        
        # 9. 计算Y值（博弈论核心指标）
        print("🎯 计算Y值（博弈论核心指标）...")
        
        # 重新获取完整数据
        cursor.execute("""
            SELECT date, close, median_price
            FROM hk2800 
            ORDER BY date ASC
        """)
        
        complete_data = cursor.fetchall()
        
        # 计算Y值
        cumulative_marks = 0
        y_updates = []
        
        for i, (date, close, median) in enumerate(complete_data):
            record_number = i + 1
            
            if median is not None and close is not None:
                close_val = float(close)
                median_val = float(median)
                
                # 控股商标记：价格高于中位数为1，否则为0
                mark = 1 if close_val > median_val else 0
                cumulative_marks += mark
                
                # Y值 = 累计标记数 / 当前记录序号
                y_value = cumulative_marks / record_number
                
                y_updates.append((y_value, mark, date))
            else:
                y_updates.append((None, None, date))
        
        # 更新Y值和控股商标记
        for y_value, controlling, date in y_updates:
            cursor.execute("""
                UPDATE hk2800 
                SET y_probability = %s, 
                    global_control_coeff = %s,
                    controlling_shareholder = %s
                WHERE date = %s
            """, (y_value, y_value, controlling, date))
        
        connection.commit()
        print(f"✅ 已更新 {len(y_updates)} 条Y值数据")
        
        # 10. 模拟资金流向数据（ETF特殊处理）
        print("💰 计算资金流向数据...")
        
        # 对于ETF，使用成交量和价格变化来模拟资金流向
        cursor.execute("""
            SELECT date, close, volume
            FROM hk2800 
            ORDER BY date ASC
        """)
        
        volume_data = cursor.fetchall()
        
        money_flow_updates = []
        prev_close = None
        
        for i, (date, close, volume) in enumerate(volume_data):
            if prev_close is not None and volume is not None:
                close_val = float(close)
                volume_val = float(volume)
                
                # 价格变化率
                price_change = (close_val - prev_close) / prev_close
                
                # 模拟资金流向（简化模型）
                if price_change > 0:
                    # 价格上涨，假设更多资金流入
                    inflow_ratio = 0.5 + min(price_change * 10, 0.4)  # 0.5-0.9
                else:
                    # 价格下跌，假设更多资金流出
                    inflow_ratio = 0.5 + max(price_change * 10, -0.4)  # 0.1-0.5
                
                # 计算具体金额（基于成交量）
                total_amount = volume_val * close_val
                money_inflow = total_amount * inflow_ratio
                money_outflow = total_amount * (1 - inflow_ratio)
                net_inflow = money_inflow - money_outflow
                
                money_flow_updates.append((
                    money_inflow, money_outflow, net_inflow, inflow_ratio, date
                ))
            else:
                # 第一天或无数据，设置默认值
                money_flow_updates.append((None, None, None, 0.5, date))
            
            prev_close = float(close) if close is not None else prev_close
        
        # 更新资金流向数据
        for money_inflow, money_outflow, net_inflow, inflow_ratio, date in money_flow_updates:
            cursor.execute("""
                UPDATE hk2800 
                SET money_inflow = %s,
                    money_outflow = %s,
                    net_inflow = %s,
                    inflow_ratio = %s
                WHERE date = %s
            """, (money_inflow, money_outflow, net_inflow, inflow_ratio, date))
        
        connection.commit()
        print(f"✅ 已更新 {len(money_flow_updates)} 条资金流向数据")
        
        # 11. 验证数据完整性
        print("🔍 验证数据完整性...")
        
        cursor.execute("""
            SELECT 
                COUNT(*) as total,
                COUNT(close) as with_price,
                COUNT(y_probability) as with_y,
                COUNT(inflow_ratio) as with_x,
                COUNT(median_price) as with_median,
                MIN(date) as start_date,
                MAX(date) as end_date
            FROM hk2800
        """)
        
        verification = cursor.fetchone()
        total, with_price, with_y, with_x, with_median, start_date, end_date = verification
        
        print(f"📊 数据完整性验证:")
        print(f"   总记录数: {total}")
        print(f"   有价格数据: {with_price} ({with_price/total*100:.1f}%)")
        print(f"   有Y值数据: {with_y} ({with_y/total*100:.1f}%)")
        print(f"   有X值数据: {with_x} ({with_x/total*100:.1f}%)")
        print(f"   有中位数据: {with_median} ({with_median/total*100:.1f}%)")
        print(f"   数据范围: {start_date} 到 {end_date}")
        
        # 12. 显示样本数据
        cursor.execute("""
            SELECT date, close, y_probability, inflow_ratio, median_price
            FROM hk2800 
            ORDER BY date DESC 
            LIMIT 10
        """)
        
        sample_data = cursor.fetchall()
        
        print(f"\n📅 最新10天样本数据:")
        print("日期         价格    Y值      X值     中位价")
        print("-" * 50)
        
        for row in sample_data:
            date, close, y_val, x_val, median = row
            y_str = f"{float(y_val):.3f}" if y_val else "None"
            x_str = f"{float(x_val):.3f}" if x_val else "None"
            median_str = f"{float(median):.2f}" if median else "None"
            
            print(f"{date}  {float(close):>6.2f}  {y_str:>6}  {x_str:>6}  {median_str:>7}")
        
        print(f"\n🎉 HK2800数据库转换完成！")
        print("="*50)
        print("✅ 基础价格数据: 完整")
        print("✅ 技术指标: 完整")
        print("✅ 博弈论Y值: 完整")
        print("✅ 资金流向X值: 完整")
        print("✅ 中位数基准: 完整")
        print("\n🚀 现在可以使用Y≥X≥0.4策略测试HK2800了！")
        
        return True
        
    except Exception as e:
        print(f"❌ 转换失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        if 'connection' in locals():
            connection.close()

if __name__ == "__main__":
    convert_to_hk2800()
