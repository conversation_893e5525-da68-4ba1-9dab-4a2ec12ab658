#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查test表状态
=============

检查test表是否已经成功添加真实资金流数据

作者: Cosmoon NG
日期: 2025年7月
"""

import pymysql
import pandas as pd

def check_test_table():
    """检查test表状态"""
    print("🔍 检查test表状态")
    print("="*50)
    
    # 数据库配置
    db_config = {
        'host': '************',
        'port': 3306,
        'database': 'finance',
        'user': 'root',
        'password': '',
        'charset': 'utf8mb4'
    }
    
    try:
        # 连接数据库
        connection = pymysql.connect(**db_config)
        print(f"✅ 成功连接MySQL数据库: {db_config['host']}")
        
        cursor = connection.cursor()
        
        # 检查表结构
        cursor.execute("DESCRIBE test")
        columns = cursor.fetchall()
        
        print("\n📊 test表结构:")
        flow_columns = []
        for col in columns:
            print(f"   • {col[0]} ({col[1]})")
            if '流' in col[0] or 'flow' in col[0].lower():
                flow_columns.append(col[0])
        
        # 检查是否有资金流列
        if flow_columns:
            print(f"\n💰 发现资金流相关列: {flow_columns}")
            
            # 检查数据
            cursor.execute("""
                SELECT COUNT(*) as total,
                       COUNT(CASE WHEN `真实流入` > 0 OR `真实流出` > 0 THEN 1 END) as with_flow
                FROM test
            """)
            
            counts = cursor.fetchone()
            total, with_flow = counts
            
            print(f"\n📈 数据统计:")
            print(f"   • 总记录数: {total}")
            print(f"   • 有资金流数据的记录: {with_flow}")
            print(f"   • 数据完整率: {with_flow/total*100:.1f}%")
            
            if with_flow > 0:
                # 显示前10条有资金流数据的记录
                cursor.execute("""
                    SELECT 交易序号, 开仓日期, 交易方向, 策略区域, 净利润,
                           `真实流入`, `真实流出`, `资金流比例`
                    FROM test 
                    WHERE `真实流入` > 0 OR `真实流出` > 0
                    ORDER BY 交易序号 
                    LIMIT 10
                """)
                
                sample_data = cursor.fetchall()
                
                print(f"\n📋 前10条有资金流数据的记录:")
                print("-" * 100)
                print(f"{'序号':<4} {'开仓日期':<12} {'方向':<4} {'策略区域':<15} {'净利润':<8} {'真实流入':<12} {'真实流出':<12} {'流比例':<8}")
                print("-" * 100)
                
                for row in sample_data:
                    trade_id, date, direction, zone, profit, flow_in, flow_out, ratio = row
                    print(f"{trade_id:<4} {date:<12} {direction:<4} {zone:<15} {int(profit):<8} "
                          f"{float(flow_in):<12,.0f} {float(flow_out):<12,.0f} {float(ratio):<8.3f}")
                
                print("\n✅ test表已成功添加真实资金流数据!")
            else:
                print("\n❌ test表虽然有资金流列，但没有数据")
        else:
            print("\n❌ test表没有资金流相关列")
        
        connection.close()
        
    except Exception as e:
        print(f"❌ 检查test表失败: {e}")

def main():
    """主函数"""
    check_test_table()

if __name__ == "__main__":
    main()
