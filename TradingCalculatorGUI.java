/**
 * 交易止盈止损价位计算器 - Windows GUI版本
 * 
 * 功能：根据入场价格和交易方向（多头/空头），计算止盈和止损价位
 * 风险回报比：1:2 (止损0.06%, 止盈0.12%)
 * 
 * <AUTHOR> NG
 * @version 2.0 GUI
 * @date 2025-07-28
 */

import javax.swing.*;
import javax.swing.border.TitledBorder;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.math.BigDecimal;
import java.math.RoundingMode;

public class TradingCalculatorGUI extends JFrame {
    
    // 风险回报比参数 (1:2)
    private static final double STOP_LOSS_RATE = 0.0006;   // 止损 0.06%
    private static final double TAKE_PROFIT_RATE = 0.0012; // 止盈 0.12%
    
    // GUI组件
    private JTextField entryPriceField;
    private JRadioButton longRadio;
    private JRadioButton shortRadio;
    private ButtonGroup directionGroup;
    private JLabel takeProfitLabel;
    private J<PERSON>abe<PERSON> stopLossLabel;
    private JLabel riskRewardLabel;
    private JTextArea resultArea;
    private JButton calculateButton;
    private JButton clearButton;
    
    public TradingCalculatorGUI() {
        initializeGUI();
    }
    
    private void initializeGUI() {
        // 设置窗口基本属性
        setTitle("🎯 交易止盈止损价位计算器 v2.0");
        setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
        setLayout(new BorderLayout());
        setResizable(false);
        
        // 设置Look and Feel为系统默认
        try {
            UIManager.setLookAndFeel(UIManager.getSystemLookAndFeel());
        } catch (Exception e) {
            e.printStackTrace();
        }
        
        // 创建主面板
        JPanel mainPanel = new JPanel(new BorderLayout(10, 10));
        mainPanel.setBorder(BorderFactory.createEmptyBorder(15, 15, 15, 15));
        
        // 创建输入面板
        JPanel inputPanel = createInputPanel();
        
        // 创建结果面板
        JPanel resultPanel = createResultPanel();
        
        // 创建按钮面板
        JPanel buttonPanel = createButtonPanel();
        
        // 创建信息面板
        JPanel infoPanel = createInfoPanel();
        
        // 添加到主面板
        mainPanel.add(infoPanel, BorderLayout.NORTH);
        mainPanel.add(inputPanel, BorderLayout.CENTER);
        mainPanel.add(resultPanel, BorderLayout.EAST);
        mainPanel.add(buttonPanel, BorderLayout.SOUTH);
        
        add(mainPanel);
        
        // 设置窗口大小和位置
        pack();
        setLocationRelativeTo(null);
        
        // 设置默认焦点
        entryPriceField.requestFocus();
    }
    
    private JPanel createInfoPanel() {
        JPanel panel = new JPanel(new FlowLayout(FlowLayout.CENTER));
        panel.setBorder(new TitledBorder("📊 策略信息"));
        
        JLabel infoLabel = new JLabel("<html><center>" +
            "<b>风险回报比: 1:2</b><br>" +
            "止损: 0.06% | 止盈: 0.12%<br>" +
            "<font color='blue'>超短线交易策略</font>" +
            "</center></html>");
        infoLabel.setFont(new Font("微软雅黑", Font.PLAIN, 12));
        
        panel.add(infoLabel);
        return panel;
    }
    
    private JPanel createInputPanel() {
        JPanel panel = new JPanel(new GridBagLayout());
        panel.setBorder(new TitledBorder("📝 交易参数"));
        GridBagConstraints gbc = new GridBagConstraints();
        
        // 入场价格
        gbc.gridx = 0; gbc.gridy = 0;
        gbc.anchor = GridBagConstraints.WEST;
        gbc.insets = new Insets(5, 5, 5, 5);
        panel.add(new JLabel("入场价格:"), gbc);
        
        gbc.gridx = 1; gbc.gridy = 0;
        gbc.fill = GridBagConstraints.HORIZONTAL;
        entryPriceField = new JTextField(10);
        entryPriceField.setFont(new Font("Consolas", Font.PLAIN, 14));
        panel.add(entryPriceField, gbc);
        
        gbc.gridx = 2; gbc.gridy = 0;
        gbc.fill = GridBagConstraints.NONE;
        panel.add(new JLabel("HK"), gbc);
        
        // 交易方向
        gbc.gridx = 0; gbc.gridy = 1;
        gbc.anchor = GridBagConstraints.WEST;
        panel.add(new JLabel("交易方向:"), gbc);
        
        JPanel radioPanel = new JPanel(new FlowLayout(FlowLayout.LEFT, 0, 0));
        longRadio = new JRadioButton("📈 多头 (做多)", true);
        shortRadio = new JRadioButton("📉 空头 (做空)");
        
        directionGroup = new ButtonGroup();
        directionGroup.add(longRadio);
        directionGroup.add(shortRadio);
        
        radioPanel.add(longRadio);
        radioPanel.add(Box.createHorizontalStrut(20));
        radioPanel.add(shortRadio);
        
        gbc.gridx = 1; gbc.gridy = 1;
        gbc.gridwidth = 2;
        panel.add(radioPanel, gbc);
        
        return panel;
    }
    
    private JPanel createResultPanel() {
        JPanel panel = new JPanel(new GridBagLayout());
        panel.setBorder(new TitledBorder("📊 计算结果"));
        GridBagConstraints gbc = new GridBagConstraints();
        
        // 止盈价格
        gbc.gridx = 0; gbc.gridy = 0;
        gbc.anchor = GridBagConstraints.WEST;
        gbc.insets = new Insets(5, 5, 5, 5);
        panel.add(new JLabel("🎯 止盈价格:"), gbc);
        
        gbc.gridx = 1; gbc.gridy = 0;
        takeProfitLabel = new JLabel("---.---");
        takeProfitLabel.setFont(new Font("Consolas", Font.BOLD, 14));
        takeProfitLabel.setForeground(Color.GREEN.darker());
        panel.add(takeProfitLabel, gbc);
        
        // 止损价格
        gbc.gridx = 0; gbc.gridy = 1;
        panel.add(new JLabel("🛑 止损价格:"), gbc);
        
        gbc.gridx = 1; gbc.gridy = 1;
        stopLossLabel = new JLabel("---.---");
        stopLossLabel.setFont(new Font("Consolas", Font.BOLD, 14));
        stopLossLabel.setForeground(Color.RED.darker());
        panel.add(stopLossLabel, gbc);
        
        // 风险回报比
        gbc.gridx = 0; gbc.gridy = 2;
        panel.add(new JLabel("⚖️ 风险回报比:"), gbc);
        
        gbc.gridx = 1; gbc.gridy = 2;
        riskRewardLabel = new JLabel("2.0:1");
        riskRewardLabel.setFont(new Font("Consolas", Font.BOLD, 14));
        riskRewardLabel.setForeground(Color.BLUE.darker());
        panel.add(riskRewardLabel, gbc);
        
        // 详细结果区域
        gbc.gridx = 0; gbc.gridy = 3;
        gbc.gridwidth = 2;
        gbc.fill = GridBagConstraints.BOTH;
        gbc.weightx = 1.0;
        gbc.weighty = 1.0;
        
        resultArea = new JTextArea(8, 25);
        resultArea.setFont(new Font("Consolas", Font.PLAIN, 11));
        resultArea.setEditable(false);
        resultArea.setBackground(new Color(248, 248, 248));
        resultArea.setBorder(BorderFactory.createLoweredBevelBorder());
        
        JScrollPane scrollPane = new JScrollPane(resultArea);
        panel.add(scrollPane, gbc);
        
        return panel;
    }
    
    private JPanel createButtonPanel() {
        JPanel panel = new JPanel(new FlowLayout(FlowLayout.CENTER, 10, 5));
        
        calculateButton = new JButton("🧮 计算");
        calculateButton.setFont(new Font("微软雅黑", Font.BOLD, 12));
        calculateButton.setPreferredSize(new Dimension(100, 35));
        calculateButton.addActionListener(new CalculateActionListener());
        
        clearButton = new JButton("🗑️ 清除");
        clearButton.setFont(new Font("微软雅黑", Font.PLAIN, 12));
        clearButton.setPreferredSize(new Dimension(100, 35));
        clearButton.addActionListener(e -> clearResults());
        
        JButton exitButton = new JButton("❌ 退出");
        exitButton.setFont(new Font("微软雅黑", Font.PLAIN, 12));
        exitButton.setPreferredSize(new Dimension(100, 35));
        exitButton.addActionListener(e -> System.exit(0));
        
        panel.add(calculateButton);
        panel.add(clearButton);
        panel.add(exitButton);
        
        // 设置回车键触发计算
        getRootPane().setDefaultButton(calculateButton);
        
        return panel;
    }
    
    private class CalculateActionListener implements ActionListener {
        @Override
        public void actionPerformed(ActionEvent e) {
            calculatePrices();
        }
    }
    
    private void calculatePrices() {
        try {
            // 获取入场价格
            String priceText = entryPriceField.getText().trim();
            if (priceText.isEmpty()) {
                showError("请输入入场价格！");
                return;
            }
            
            double entryPrice = Double.parseDouble(priceText);
            if (entryPrice <= 0) {
                showError("入场价格必须大于0！");
                return;
            }
            
            // 获取交易方向
            boolean isLong = longRadio.isSelected();
            
            // 计算止盈止损
            double takeProfitPrice, stopLossPrice;
            String direction;
            
            if (isLong) {
                // 多头计算
                takeProfitPrice = entryPrice * (1 + TAKE_PROFIT_RATE);
                stopLossPrice = entryPrice * (1 - STOP_LOSS_RATE);
                direction = "多头 (做多)";
            } else {
                // 空头计算
                takeProfitPrice = entryPrice * (1 - TAKE_PROFIT_RATE);
                stopLossPrice = entryPrice * (1 + STOP_LOSS_RATE);
                direction = "空头 (做空)";
            }
            
            // 保留3位小数
            takeProfitPrice = roundToThreeDecimals(takeProfitPrice);
            stopLossPrice = roundToThreeDecimals(stopLossPrice);
            
            // 更新显示
            takeProfitLabel.setText(String.format("%.3f", takeProfitPrice));
            stopLossLabel.setText(String.format("%.3f", stopLossPrice));
            
            // 更新详细结果
            updateResultArea(entryPrice, direction, takeProfitPrice, stopLossPrice, isLong);
            
        } catch (NumberFormatException ex) {
            showError("请输入有效的数字！");
        } catch (Exception ex) {
            showError("计算错误: " + ex.getMessage());
        }
    }
    
    private void updateResultArea(double entryPrice, String direction, 
                                 double takeProfitPrice, double stopLossPrice, boolean isLong) {
        StringBuilder sb = new StringBuilder();
        sb.append("=== 交易计算结果 ===\n");
        sb.append("交易方向: ").append(direction).append("\n");
        sb.append("入场价格: ").append(String.format("%.3f", entryPrice)).append(" HK\n");
        sb.append("止盈价格: ").append(String.format("%.3f", takeProfitPrice)).append(" HK\n");
        sb.append("止损价格: ").append(String.format("%.3f", stopLossPrice)).append(" HK\n");
        sb.append("风险回报比: 2.0:1\n");
        sb.append("止盈幅度: 0.12%\n");
        sb.append("止损幅度: 0.06%\n\n");
        
        sb.append("=== 计算公式 ===\n");
        if (isLong) {
            sb.append("多头止盈: ").append(entryPrice).append(" × (1 + 0.0012)\n");
            sb.append("多头止损: ").append(entryPrice).append(" × (1 - 0.0006)\n");
        } else {
            sb.append("空头止盈: ").append(entryPrice).append(" × (1 - 0.0012)\n");
            sb.append("空头止损: ").append(entryPrice).append(" × (1 + 0.0006)\n");
        }
        
        sb.append("\n=== 交易说明 ===\n");
        if (isLong) {
            sb.append("• 价格上涨至 ").append(String.format("%.3f", takeProfitPrice)).append(" 时止盈\n");
            sb.append("• 价格下跌至 ").append(String.format("%.3f", stopLossPrice)).append(" 时止损\n");
        } else {
            sb.append("• 价格下跌至 ").append(String.format("%.3f", takeProfitPrice)).append(" 时止盈\n");
            sb.append("• 价格上涨至 ").append(String.format("%.3f", stopLossPrice)).append(" 时止损\n");
        }
        
        resultArea.setText(sb.toString());
        resultArea.setCaretPosition(0);
    }
    
    private void clearResults() {
        entryPriceField.setText("");
        longRadio.setSelected(true);
        takeProfitLabel.setText("---.---");
        stopLossLabel.setText("---.---");
        resultArea.setText("");
        entryPriceField.requestFocus();
    }
    
    private void showError(String message) {
        JOptionPane.showMessageDialog(this, message, "输入错误", JOptionPane.ERROR_MESSAGE);
        entryPriceField.requestFocus();
        entryPriceField.selectAll();
    }
    
    private double roundToThreeDecimals(double value) {
        BigDecimal bd = new BigDecimal(value);
        bd = bd.setScale(3, RoundingMode.HALF_UP);
        return bd.doubleValue();
    }
    
    public static void main(String[] args) {
        // 设置系统Look and Feel
        SwingUtilities.invokeLater(() -> {
            try {
                UIManager.setLookAndFeel(UIManager.getSystemLookAndFeel());
            } catch (Exception e) {
                e.printStackTrace();
            }
            
            new TradingCalculatorGUI().setVisible(true);
        });
    }
}
