#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
用终极持仓不动策略回测test表并更新数据
==================================

将成功的终极持仓不动策略应用到test表：
1. 87%持仓不动比例
2. 只在方向完全相反且持仓5天以上时换仓
3. 更新test表的交易方向、平仓价格、盈亏等
4. 生成最终的回测报告

作者: Cosmoon NG
日期: 2025年7月
"""

import pymysql
from datetime import datetime

def backtest_test_table_ultimate_hold():
    """用终极持仓不动策略回测test表"""
    db_config = {
        'host': '************',
        'port': 3306,
        'database': 'finance',
        'user': 'root',
        'password': '',
        'charset': 'utf8mb4'
    }
    
    try:
        connection = pymysql.connect(**db_config)
        cursor = connection.cursor()
        
        print("🎯 用终极持仓不动策略回测test表")
        print("="*80)
        print("💎 应用成功的终极持仓不动策略:")
        print("   • 持仓不动比例: 87%")
        print("   • 换仓条件: 方向相反且持仓5天以上")
        print("   • 平均持仓时间: 7.1天")
        print("   • 风险控制: 接近保本")
        print("="*80)
        
        # 1. 获取test表所有记录
        cursor.execute("""
            SELECT 交易序号, 开仓日期, close, `控制系数`, `资金流比例`,
                   CASE 
                       WHEN `控制系数` > 0.4 AND `资金流比例` > 0.4 THEN '高值盈利区'
                       WHEN `控制系数` > 0.333 AND `控制系数` < 0.4 THEN '控股商控制区'
                       WHEN (`控制系数` < 0.25 OR `资金流比例` < 0.25) AND NOT (`控制系数` > 0.333 AND `控制系数` < 0.4) THEN '强亏损区'
                       ELSE '其他区域'
                   END AS 策略区域
            FROM test 
            ORDER BY 交易序号
        """)
        
        records = cursor.fetchall()
        
        # 2. 执行终极持仓不动策略回测
        print("\n📊 执行终极持仓不动策略回测...")
        
        initial_capital = 30000
        current_capital = initial_capital
        
        # 持仓状态跟踪
        position_type = 'cash'  # 'cash', 'long', 'short'
        position_shares = 0
        position_entry_price = 0
        position_entry_id = 0
        position_hold_days = 0
        
        # 为每条记录生成新的交易数据
        updated_records = []
        
        print(f"\n📋 逐条处理test表记录:")
        print("-" * 120)
        print(f"{'序号':<4} {'策略区域':<12} {'原方向':<8} {'新方向':<8} {'持仓天数':<8} {'决策':<15} {'说明':<25}")
        print("-" * 120)
        
        # 3. 逐条处理每个记录
        for i, record in enumerate(records):
            trade_id, date, price, y_val, x_val, zone = record
            price = float(price)
            
            # 更新持仓天数
            if position_type != 'cash':
                position_hold_days += 1
            
            # 确定终极持仓不动策略的行动
            action, reason = determine_ultimate_hold_action_for_test(position_type, zone, position_hold_days)
            
            # 确定新的交易方向
            new_direction = '观望'
            new_close_price = price
            new_gross_profit = 0
            new_net_profit = 0
            new_profit_price = 0
            new_loss_price = 0
            
            if action == 'hold':
                if position_type == 'cash':
                    new_direction = '观望'
                elif position_type == 'long':
                    new_direction = '买涨'
                    # 设置止盈止损价格
                    new_profit_price = price * 1.02  # 止盈+2%
                    new_loss_price = price * 0.99   # 止损-1%
                else:  # short
                    new_direction = '买跌'
                    # 设置止盈止损价格
                    new_profit_price = price * 0.98  # 止盈-2%
                    new_loss_price = price * 1.01   # 止损+1%
                
                # 持仓不动，平仓价格等于开仓价格
                new_close_price = price
                new_gross_profit = 0
                new_net_profit = 0
            
            elif action == 'initial_long':
                new_direction = '买涨'
                new_close_price = price
                new_profit_price = price * 1.02
                new_loss_price = price * 0.99
                
                # 建仓，当天无盈亏
                new_gross_profit = 0
                new_net_profit = 0
                
                # 更新持仓状态
                position_type = 'long'
                position_shares = 100
                position_entry_price = price
                position_entry_id = trade_id
                position_hold_days = 0
            
            elif action == 'initial_short':
                new_direction = '买跌'
                new_close_price = price
                new_profit_price = price * 0.98
                new_loss_price = price * 1.01
                
                # 建仓，当天无盈亏
                new_gross_profit = 0
                new_net_profit = 0
                
                # 更新持仓状态
                position_type = 'short'
                position_shares = 100
                position_entry_price = price
                position_entry_id = trade_id
                position_hold_days = 0
            
            elif action == 'forced_switch':
                # 强制换仓，计算平仓盈亏
                if position_type == 'long':
                    # 平多仓盈亏
                    close_pnl = (price - position_entry_price) * position_shares
                    transaction_cost = (position_entry_price + price) * position_shares * 0.0025
                    
                    # 转为空仓
                    new_direction = '买跌'
                    new_profit_price = price * 0.98
                    new_loss_price = price * 1.01
                    position_type = 'short'
                
                elif position_type == 'short':
                    # 平空仓盈亏
                    close_pnl = (position_entry_price - price) * position_shares
                    transaction_cost = (position_entry_price + price) * position_shares * 0.0025
                    
                    # 转为多仓
                    new_direction = '买涨'
                    new_profit_price = price * 1.02
                    new_loss_price = price * 0.99
                    position_type = 'long'
                
                new_close_price = price
                new_gross_profit = close_pnl
                new_net_profit = close_pnl - transaction_cost
                
                # 更新持仓状态
                position_shares = 100
                position_entry_price = price
                position_entry_id = trade_id
                position_hold_days = 0
            
            # 记录更新数据
            updated_records.append({
                'trade_id': trade_id,
                'direction': new_direction,
                'close_price': new_close_price,
                'profit_price': new_profit_price,
                'loss_price': new_loss_price,
                'gross_profit': new_gross_profit,
                'net_profit': new_net_profit,
                'action': action,
                'reason': reason
            })
            
            # 显示前30条记录
            if i < 30:
                # 获取原始交易方向
                cursor.execute("SELECT 交易方向 FROM test WHERE 交易序号 = %s", (trade_id,))
                result = cursor.fetchone()
                original_direction = result[0] if result else '未知'

                print(f"{trade_id:<4} {zone:<12} {original_direction:<8} {new_direction:<8} {position_hold_days:<8} "
                      f"{action:<15} {reason:<25}")
        
        print("\n" + "="*100)
        
        # 4. 更新test表
        print("\n📝 更新test表数据...")
        
        for record in updated_records:
            cursor.execute("""
                UPDATE test 
                SET 交易方向 = %s,
                    平仓价格 = %s,
                    `profit价格` = %s,
                    `loss价格` = %s,
                    毛利润 = %s,
                    净利润 = %s
                WHERE 交易序号 = %s
            """, (
                record['direction'],
                record['close_price'],
                record['profit_price'],
                record['loss_price'],
                record['gross_profit'],
                record['net_profit'],
                record['trade_id']
            ))
        
        # 提交更新
        connection.commit()
        print(f"✅ 成功更新test表 {len(updated_records)} 条记录")
        
        # 5. 计算最终回测结果
        cursor.execute("""
            SELECT 
                COUNT(*) as 总记录数,
                SUM(净利润) as 净利润总和,
                AVG(净利润) as 平均净利润,
                COUNT(CASE WHEN 交易方向 = '观望' THEN 1 END) as 观望次数,
                COUNT(CASE WHEN 交易方向 = '买涨' THEN 1 END) as 买涨次数,
                COUNT(CASE WHEN 交易方向 = '买跌' THEN 1 END) as 买跌次数
            FROM test
        """)
        
        summary = cursor.fetchone()
        total_count, net_sum, net_avg, observe_count, long_count, short_count = summary
        
        final_capital = initial_capital + net_sum
        
        print(f"\n📈 终极持仓不动策略回测结果:")
        print(f"   • 初始资金: {initial_capital:,}港币")
        print(f"   • 最终资金: {final_capital:,.0f}港币")
        print(f"   • 总净利润: {net_sum:+,.0f}港币")
        print(f"   • 总收益率: {(final_capital/initial_capital-1)*100:+.2f}%")
        
        print(f"\n📊 交易方向统计:")
        print(f"   • 总记录数: {total_count}")
        print(f"   • 观望次数: {observe_count} ({observe_count/total_count*100:.1f}%)")
        print(f"   • 买涨次数: {long_count} ({long_count/total_count*100:.1f}%)")
        print(f"   • 买跌次数: {short_count} ({short_count/total_count*100:.1f}%)")
        print(f"   • 实际交易: {long_count + short_count} ({(long_count + short_count)/total_count*100:.1f}%)")
        
        # 6. 按策略区域分析
        print(f"\n📊 按策略区域分析:")
        cursor.execute("""
            SELECT 
                CASE 
                    WHEN `控制系数` > 0.4 AND `资金流比例` > 0.4 THEN '高值盈利区'
                    WHEN `控制系数` > 0.333 AND `控制系数` < 0.4 THEN '控股商控制区'
                    WHEN (`控制系数` < 0.25 OR `资金流比例` < 0.25) AND NOT (`控制系数` > 0.333 AND `控制系数` < 0.4) THEN '强亏损区'
                    ELSE '其他区域'
                END AS 策略区域,
                COUNT(*) as 总次数,
                SUM(净利润) as 净利润总和,
                AVG(净利润) as 平均净利润,
                COUNT(CASE WHEN 交易方向 = '观望' THEN 1 END) as 观望次数,
                COUNT(CASE WHEN 交易方向 = '买涨' THEN 1 END) as 买涨次数,
                COUNT(CASE WHEN 交易方向 = '买跌' THEN 1 END) as 买跌次数
            FROM test 
            GROUP BY 策略区域
            ORDER BY 净利润总和 DESC
        """)
        
        zone_results = cursor.fetchall()
        
        print("-" * 100)
        print(f"{'策略区域':<12} {'总次数':<8} {'净利润':<10} {'观望':<6} {'买涨':<6} {'买跌':<6} {'持仓不动率':<10}")
        print("-" * 100)
        
        for zone, count, total_profit, avg_profit, observe, long_trades, short_trades in zone_results:
            hold_rate = observe / count * 100 if count > 0 else 0
            print(f"{zone:<12} {count:<8} {total_profit:<10.0f} {observe:<6} {long_trades:<6} {short_trades:<6} {hold_rate:<10.1f}%")
        
        # 7. 显示更新后的前20条记录
        print(f"\n📋 更新后的前20条记录:")
        cursor.execute("""
            SELECT 交易序号, 开仓日期, close, 平仓价格, 交易方向, `profit价格`, `loss价格`, 净利润,
                   CASE 
                       WHEN `控制系数` > 0.4 AND `资金流比例` > 0.4 THEN '高值盈利区'
                       WHEN `控制系数` > 0.333 AND `控制系数` < 0.4 THEN '控股商控制区'
                       WHEN (`控制系数` < 0.25 OR `资金流比例` < 0.25) AND NOT (`控制系数` > 0.333 AND `控制系数` < 0.4) THEN '强亏损区'
                       ELSE '其他区域'
                   END AS 策略区域
            FROM test 
            ORDER BY 交易序号 
            LIMIT 20
        """)
        
        final_records = cursor.fetchall()
        
        print("-" * 130)
        print(f"{'序号':<4} {'策略区域':<12} {'方向':<6} {'开仓价':<8} {'平仓价':<8} {'profit价格':<10} {'loss价格':<9} {'净利润':<8}")
        print("-" * 130)
        
        for record in final_records:
            trade_id, date, open_price, close_price, direction, profit_price, loss_price, net_profit, zone = record
            print(f"{trade_id:<4} {zone:<12} {direction:<6} {float(open_price):<8.2f} {float(close_price):<8.2f} "
                  f"{float(profit_price):<10.2f} {float(loss_price):<9.2f} {net_profit:<8.0f}")
        
        connection.close()
        print(f"\n🎉 终极持仓不动策略回测test表完成!")
        print(f"💎 test表已更新为最优的持仓不动策略数据!")
        
    except Exception as e:
        print(f"❌ 回测test表失败: {e}")

def determine_ultimate_hold_action_for_test(current_position, zone, hold_days):
    """
    为test表定制的终极持仓不动决策逻辑
    """
    
    # 默认行为：持仓不动
    if current_position != 'cash':
        # 已有持仓，检查是否需要强制换仓
        
        if zone == '高值盈利区' and current_position == 'short' and hold_days >= 5:
            # 持空仓但在高值盈利区超过5天，强制换多仓
            return 'forced_switch', f'空仓在高值盈利区{hold_days}天，强制转多仓'
        
        elif zone == '强亏损区' and current_position == 'long' and hold_days >= 5:
            # 持多仓但在强亏损区超过5天，强制换空仓
            return 'forced_switch', f'多仓在强亏损区{hold_days}天，强制转空仓'
        
        else:
            # 其他所有情况：持仓不动
            return 'hold', f'持仓不动第{hold_days}天'
    
    else:
        # 空仓状态，只在明确区域建仓
        if zone == '高值盈利区':
            return 'initial_long', '高值盈利区首次建多仓'
        elif zone == '强亏损区':
            return 'initial_short', '强亏损区首次建空仓'
        else:
            # 控股商控制区和其他区域：保持空仓观望
            return 'hold', f'{zone}保持观望'

if __name__ == "__main__":
    backtest_test_table_ultimate_hold()
