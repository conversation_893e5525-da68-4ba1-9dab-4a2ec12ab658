#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生成详细交易记录
==============

基于数据库中修正后的数据，生成详细的交易记录
包括：开仓、平仓、盈亏、持仓时间等详细信息

作者: Cosmoon NG
日期: 2025年7月16日
"""

import pymysql
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# 数据库配置
db_config = {
    'host': '************',
    'user': 'root',
    'password': '',
    'database': 'finance',
    'charset': 'utf8mb4'
}

class TradingRecordGenerator:
    def __init__(self):
        """初始化交易记录生成器"""
        self.initial_capital = 30000  # 初始资金30K
        self.monthly_addition = 2000  # 每月追加2K
        
        # 不对称止盈止损设计
        self.take_profit_long = 0.016   # 多头止盈 1.6%
        self.stop_loss_long = 0.008     # 多头止损 0.8%
        self.take_profit_short = 0.008  # 空头止盈 0.8%
        self.stop_loss_short = 0.016    # 空头止损 1.6%
        
        # 交易状态
        self.position = 0  # 0=空仓, 1=多头, -1=空头
        self.entry_price = 0
        self.entry_date = None
        self.shares = 0
        self.capital = self.initial_capital
        
        # 记录
        self.trades = []
        self.daily_records = []
        self.last_month = None
        self.trade_id = 1
    
    def fetch_data_from_db(self):
        """从数据库获取修正后的数据"""
        print("📊 从数据库获取修正后的数据...")
        
        try:
            conn = pymysql.connect(**db_config)
            
            # 获取所有数据，按日期排序
            query = """
            SELECT Date, Open, High, Low, Close, Volume,
                   MoneyFlowRatio, Full_Y, E, Controller
            FROM hkhsi50
            ORDER BY Date ASC
            """
            
            df = pd.read_sql(query, conn)
            conn.close()
            
            if df.empty:
                print("❌ 数据库中无数据")
                return None
            
            print(f"✅ 成功获取 {len(df):,} 条记录")
            print(f"   • 日期范围: {df['Date'].min()} 至 {df['Date'].max()}")
            
            return df
            
        except Exception as e:
            print(f"❌ 获取数据失败: {e}")
            return None
    
    def get_trading_signal(self, row):
        """获取交易信号"""
        # 买涨条件: E>0 且 MoneyFlowRatio>0.45 且 Full_Y>0.45 且价格低于回归线
        if (row['E'] > 0 and 
            row['MoneyFlowRatio'] > 0.45 and 
            row['Full_Y'] > 0.45):
            return 'LONG'
        
        # 买跌条件: (Full_Y<0.25 或 MoneyFlowRatio<0.25)
        elif (row['Full_Y'] < 0.25 or row['MoneyFlowRatio'] < 0.25):
            return 'SHORT'
        
        return 'HOLD'
    
    def check_exit_conditions(self, current_price, high_price, low_price):
        """检查止盈止损条件"""
        if self.position == 0:
            return False, 0, ''
        
        if self.position == 1:  # 多头
            # 止盈检查
            profit_ratio = (high_price - self.entry_price) / self.entry_price
            if profit_ratio >= self.take_profit_long:
                exit_price = self.entry_price * (1 + self.take_profit_long)
                return True, exit_price, 'long_tp'
            
            # 止损检查
            loss_ratio = (self.entry_price - low_price) / self.entry_price
            if loss_ratio >= self.stop_loss_long:
                exit_price = self.entry_price * (1 - self.stop_loss_long)
                return True, exit_price, 'long_sl'
        
        elif self.position == -1:  # 空头
            # 止盈检查
            profit_ratio = (self.entry_price - low_price) / self.entry_price
            if profit_ratio >= self.take_profit_short:
                exit_price = self.entry_price * (1 - self.take_profit_short)
                return True, exit_price, 'short_tp'
            
            # 止损检查
            loss_ratio = (high_price - self.entry_price) / self.entry_price
            if loss_ratio >= self.stop_loss_short:
                exit_price = self.entry_price * (1 + self.stop_loss_short)
                return True, exit_price, 'short_sl'
        
        return False, 0, ''
    
    def calculate_shares(self, capital, price):
        """计算可购买股数"""
        max_investment = capital * 0.95  # 保留5%现金
        shares = max_investment / price
        return shares
    
    def add_monthly_capital(self, date):
        """每月增加资金"""
        current_month = date.replace(day=1)
        
        if self.last_month is None or current_month > self.last_month:
            self.last_month = current_month
            self.capital += self.monthly_addition
            return True
        
        return False
    
    def generate_trading_records(self):
        """生成详细交易记录"""
        print("🚀 开始生成详细交易记录...")
        
        # 获取数据
        df = self.fetch_data_from_db()
        if df is None:
            return None, None
        
        total_trades = 0
        winning_trades = 0
        
        # 从第60天开始回测，确保指标稳定
        for i in range(60, len(df)):
            row = df.iloc[i]
            date = pd.to_datetime(row['Date']).date()
            price = float(row['Close'])
            high = float(row['High'])
            low = float(row['Low'])
            open_price = float(row['Open'])
            volume = int(row['Volume'])
            
            # 每月增加资金
            monthly_added = self.add_monthly_capital(date)
            
            # 检查现有持仓的止盈止损
            should_exit, exit_price, exit_type = self.check_exit_conditions(price, high, low)
            
            if should_exit:
                # 计算持仓天数
                holding_days = (date - self.entry_date).days
                
                # 计算盈亏
                if self.position == 1:  # 多头
                    profit_amount = (exit_price - self.entry_price) * self.shares
                    profit_pct = (exit_price - self.entry_price) / self.entry_price * 100
                    self.capital += self.shares * exit_price  # 卖出股票获得现金
                else:  # 空头
                    profit_amount = (self.entry_price - exit_price) * self.shares
                    profit_pct = (self.entry_price - exit_price) / self.entry_price * 100
                    self.capital -= self.shares * exit_price  # 买回股票支付现金
                
                # 记录平仓交易
                self.trades.append({
                    'trade_id': self.trade_id,
                    'action': '平仓',
                    'direction': '多头' if self.position == 1 else '空头',
                    'date': date,
                    'price': round(exit_price, 2),
                    'shares': self.shares,
                    'amount': round(self.shares * exit_price, 2),
                    'entry_date': self.entry_date,
                    'entry_price': self.entry_price,
                    'holding_days': holding_days,
                    'exit_type': exit_type,
                    'profit_amount': round(profit_amount, 2),
                    'profit_pct': round(profit_pct, 2),
                    'capital_after': round(self.capital, 2),
                    'monthly_added': 0,
                    'signal_strength': '',
                    'market_condition': self.get_market_condition(row)
                })
                
                if profit_amount > 0:
                    winning_trades += 1
                
                total_trades += 1
                self.trade_id += 1
                self.position = 0
                self.entry_price = 0
                self.entry_date = None
                self.shares = 0
            
            # 如果空仓，检查开仓信号
            if self.position == 0:
                signal = self.get_trading_signal(row)
                
                if signal in ['LONG', 'SHORT']:
                    # 计算可购买股数
                    self.shares = self.calculate_shares(self.capital, price)
                    
                    if self.shares > 0:
                        if signal == 'LONG':
                            investment = self.shares * price
                            self.capital -= investment
                            self.position = 1
                            direction = '多头'
                        else:  # SHORT
                            proceeds = self.shares * price
                            self.capital += proceeds
                            self.position = -1
                            direction = '空头'
                        
                        self.entry_price = price
                        self.entry_date = date
                        
                        # 记录开仓交易
                        self.trades.append({
                            'trade_id': self.trade_id,
                            'action': '开仓',
                            'direction': direction,
                            'date': date,
                            'price': round(price, 2),
                            'shares': self.shares,
                            'amount': round(self.shares * price, 2),
                            'entry_date': date,
                            'entry_price': price,
                            'holding_days': 0,
                            'exit_type': '',
                            'profit_amount': 0,
                            'profit_pct': 0,
                            'capital_after': round(self.capital, 2),
                            'monthly_added': self.monthly_addition if monthly_added else 0,
                            'signal_strength': self.get_signal_strength(row),
                            'market_condition': self.get_market_condition(row)
                        })
            
            # 计算当前总价值
            if self.position == 1:  # 持有股票
                stock_value = self.shares * price
                total_value = self.capital + stock_value
            elif self.position == -1:  # 做空状态
                unrealized_pnl = (self.entry_price - price) * self.shares
                total_value = self.capital + unrealized_pnl
            else:  # 空仓
                total_value = self.capital
            
            # 记录每日权益
            self.daily_records.append({
                'date': date,
                'open': open_price,
                'high': high,
                'low': low,
                'close': price,
                'volume': volume,
                'capital': round(self.capital, 2),
                'total_value': round(total_value, 2),
                'position': self.position,
                'shares': self.shares,
                'money_flow_ratio': round(float(row['MoneyFlowRatio']), 4),
                'full_y': round(float(row['Full_Y']), 4),
                'e_value': round(float(row['E']), 4),
                'controller': int(row['Controller']),
                'signal': self.get_trading_signal(row),
                'monthly_added': self.monthly_addition if monthly_added else 0
            })
        
        print(f"✅ 交易记录生成完成!")
        print(f"📊 统计信息:")
        print(f"   • 总交易次数: {total_trades}")
        print(f"   • 盈利次数: {winning_trades}")
        print(f"   • 胜率: {winning_trades/total_trades*100:.1f}%" if total_trades > 0 else "   • 胜率: 0%")
        print(f"   • 最终资金: {self.capital:,.0f} 港币")
        print(f"   • 最终总价值: {self.daily_records[-1]['total_value']:,.0f} 港币")
        
        return pd.DataFrame(self.trades), pd.DataFrame(self.daily_records)
    
    def get_signal_strength(self, row):
        """获取信号强度"""
        e_val = float(row['E'])
        money_flow = float(row['MoneyFlowRatio'])
        full_y = float(row['Full_Y'])
        
        if e_val > 0.3 and money_flow > 0.6 and full_y > 0.6:
            return '强信号'
        elif e_val > 0 and money_flow > 0.45 and full_y > 0.45:
            return '中等信号'
        elif money_flow < 0.25 or full_y < 0.25:
            return '弱信号'
        else:
            return '无信号'
    
    def get_market_condition(self, row):
        """获取市场状态"""
        money_flow = float(row['MoneyFlowRatio'])
        full_y = float(row['Full_Y'])
        
        if full_y > 0.5 and money_flow > 0.5:
            return '高值盈利区'
        elif full_y > 0.333 and full_y < 0.4:
            return '控股商控制区'
        elif full_y < 0.25 or money_flow < 0.25:
            return '强亏损区'
        else:
            return '其他区域'

def save_detailed_records(trades_df, daily_df):
    """保存详细记录到Excel"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"详细交易记录_{timestamp}.xlsx"
    
    with pd.ExcelWriter(filename, engine='openpyxl') as writer:
        # 1. 交易记录
        if not trades_df.empty:
            trades_df.to_excel(writer, sheet_name='交易记录', index=False)
        
        # 2. 每日记录
        if not daily_df.empty:
            daily_df.to_excel(writer, sheet_name='每日记录', index=False)
        
        # 3. 交易统计
        if not trades_df.empty:
            # 开仓记录
            entry_trades = trades_df[trades_df['action'] == '开仓']
            exit_trades = trades_df[trades_df['action'] == '平仓']
            
            # 统计分析
            stats_data = []
            
            if len(exit_trades) > 0:
                winning_trades = len(exit_trades[exit_trades['profit_amount'] > 0])
                losing_trades = len(exit_trades[exit_trades['profit_amount'] <= 0])
                
                stats_data.extend([
                    ['总交易次数', len(entry_trades)],
                    ['完成交易次数', len(exit_trades)],
                    ['盈利次数', winning_trades],
                    ['亏损次数', losing_trades],
                    ['胜率(%)', round(winning_trades/len(exit_trades)*100, 2)],
                    ['最大盈利', exit_trades['profit_amount'].max()],
                    ['最大亏损', exit_trades['profit_amount'].min()],
                    ['平均盈利', round(exit_trades['profit_amount'].mean(), 2)],
                    ['平均持仓天数', round(exit_trades['holding_days'].mean(), 1)],
                    ['最长持仓天数', exit_trades['holding_days'].max()],
                    ['最短持仓天数', exit_trades['holding_days'].min()]
                ])
                
                # 按方向统计
                long_trades = exit_trades[exit_trades['direction'] == '多头']
                short_trades = exit_trades[exit_trades['direction'] == '空头']
                
                if len(long_trades) > 0:
                    long_wins = len(long_trades[long_trades['profit_amount'] > 0])
                    stats_data.extend([
                        ['', ''],
                        ['多头交易次数', len(long_trades)],
                        ['多头盈利次数', long_wins],
                        ['多头胜率(%)', round(long_wins/len(long_trades)*100, 2)],
                        ['多头平均盈利', round(long_trades['profit_amount'].mean(), 2)]
                    ])
                
                if len(short_trades) > 0:
                    short_wins = len(short_trades[short_trades['profit_amount'] > 0])
                    stats_data.extend([
                        ['', ''],
                        ['空头交易次数', len(short_trades)],
                        ['空头盈利次数', short_wins],
                        ['空头胜率(%)', round(short_wins/len(short_trades)*100, 2)],
                        ['空头平均盈利', round(short_trades['profit_amount'].mean(), 2)]
                    ])
            
            stats_df = pd.DataFrame(stats_data, columns=['指标', '数值'])
            stats_df.to_excel(writer, sheet_name='交易统计', index=False)
    
    print(f"✅ 详细记录已保存至: {filename}")
    return filename

def main():
    """主函数"""
    print("📊 生成详细交易记录")
    print("=" * 60)
    
    # 创建交易记录生成器
    generator = TradingRecordGenerator()
    
    # 生成交易记录
    trades_df, daily_df = generator.generate_trading_records()
    
    if trades_df is not None and daily_df is not None:
        # 保存记录
        filename = save_detailed_records(trades_df, daily_df)
        
        # 显示部分记录
        if not trades_df.empty:
            print(f"\n📋 最近10笔交易记录:")
            recent_trades = trades_df.tail(10)
            for _, trade in recent_trades.iterrows():
                action = trade['action']
                direction = trade['direction']
                date = trade['date']
                price = trade['price']
                
                if action == '开仓':
                    print(f"   {trade['trade_id']:3d}. {date} {action}{direction} @{price:.2f} "
                          f"({trade['signal_strength']}, {trade['market_condition']})")
                else:
                    profit = trade['profit_amount']
                    days = trade['holding_days']
                    print(f"   {trade['trade_id']:3d}. {date} {action}{direction} @{price:.2f} "
                          f"盈亏:{profit:+.0f} 持仓:{days}天 ({trade['exit_type']})")
        
        print(f"\n🎉 详细交易记录生成完成！")
        print(f"📁 文件: {filename}")
    else:
        print("❌ 交易记录生成失败")

if __name__ == "__main__":
    main()
