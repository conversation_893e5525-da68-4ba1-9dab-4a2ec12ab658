#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查sp_averagelineV3存储过程的参数
================================
确认存储过程的正确参数数量
"""

import mysql.connector
import warnings
warnings.filterwarnings('ignore')

def check_sp_averagelineV3():
    """检查sp_averagelineV3存储过程"""
    
    # 数据库配置
    db_config = {
        'host': 'localhost',
        'database': 'finance',
        'user': 'root',
        'password': '12345678',
        'charset': 'utf8mb4'
    }
    
    try:
        print("🔍 检查sp_averagelineV3存储过程")
        print("=" * 50)
        
        # 连接数据库
        connection = mysql.connector.connect(**db_config)
        cursor = connection.cursor()
        print(f"✅ 成功连接到数据库: {db_config['host']}/{db_config['database']}")
        
        # 1. 检查存储过程是否存在
        print("\n📊 检查存储过程是否存在...")
        cursor.execute("""
            SELECT COUNT(*) FROM information_schema.ROUTINES 
            WHERE ROUTINE_SCHEMA = 'finance' 
            AND ROUTINE_NAME = 'sp_averagelineV3'
        """)
        
        exists = cursor.fetchone()[0]
        if exists == 0:
            print("❌ sp_averagelineV3存储过程不存在")
            return False
        
        print("✅ sp_averagelineV3存储过程存在")
        
        # 2. 获取存储过程定义
        print("\n📋 获取存储过程定义...")
        cursor.execute("SHOW CREATE PROCEDURE sp_averagelineV3")
        result = cursor.fetchone()
        
        if result:
            procedure_definition = result[2]
            print("✅ 成功获取存储过程定义")
            
            # 分析参数
            print("\n🔍 分析存储过程参数...")
            lines = procedure_definition.split('\n')
            for i, line in enumerate(lines):
                if 'CREATE' in line and 'PROCEDURE' in line:
                    print(f"📝 定义行 {i+1}: {line.strip()}")
                    
                    # 查找参数部分
                    if '(' in line and ')' in line:
                        param_part = line[line.find('(')+1:line.find(')')]
                        if param_part.strip():
                            params = [p.strip() for p in param_part.split(',')]
                            print(f"📊 参数数量: {len(params)}")
                            for j, param in enumerate(params, 1):
                                print(f"   参数{j}: {param}")
                        else:
                            print("📊 参数数量: 0 (无参数)")
                    break
            
            # 显示完整定义（前20行）
            print(f"\n📄 存储过程定义 (前20行):")
            for i, line in enumerate(lines[:20], 1):
                print(f"{i:2d}: {line}")
            
            if len(lines) > 20:
                print(f"... (还有 {len(lines) - 20} 行)")
        
        # 3. 测试调用存储过程
        print(f"\n🧪 测试调用sp_averagelineV3...")
        
        # 测试1: 只传递表名
        try:
            print("   测试1: 只传递表名 'stock_600887_ss'")
            cursor.callproc('sp_averagelineV3', ['stock_600887_ss'])
            
            # 消费结果集
            for result_set in cursor.stored_results():
                rows = result_set.fetchall()
                if rows:
                    print(f"   ✅ 调用成功，返回结果: {rows[0] if rows else 'None'}")
                else:
                    print("   ✅ 调用成功，无返回结果")
            
        except mysql.connector.Error as e:
            print(f"   ❌ 调用失败: {e}")
        
        # 测试2: 传递两个参数（这应该会失败）
        try:
            print("   测试2: 传递两个参数 ['stock_600887_ss', 0]")
            cursor.callproc('sp_averagelineV3', ['stock_600887_ss', 0])
            print("   ⚠️ 意外成功 - 存储过程接受了2个参数")
            
        except mysql.connector.Error as e:
            print(f"   ✅ 预期失败: {e}")
        
        # 4. 检查其他相关存储过程
        print(f"\n📊 检查其他相关存储过程...")
        cursor.execute("""
            SELECT ROUTINE_NAME, ROUTINE_TYPE 
            FROM information_schema.ROUTINES 
            WHERE ROUTINE_SCHEMA = 'finance' 
            AND ROUTINE_NAME LIKE '%average%'
            ORDER BY ROUTINE_NAME
        """)
        
        related_procedures = cursor.fetchall()
        if related_procedures:
            print("📋 相关存储过程:")
            for proc_name, proc_type in related_procedures:
                print(f"   • {proc_name} ({proc_type})")
        else:
            print("📋 没有找到相关的存储过程")
        
        # 5. 生成正确的调用示例
        print(f"\n📝 正确的调用方法:")
        print("=" * 50)
        print("SQL调用:")
        print("   CALL sp_averagelineV3('stock_600887_ss');")
        print("")
        print("Python调用:")
        print("   cursor.callproc('sp_averagelineV3', ['stock_600887_ss'])")
        print("")
        print("⚠️ 注意:")
        print("   • sp_averagelineV3只需要1个参数：表名")
        print("   • 不要传递额外的参数")
        print("   • 确保表名用引号包围")
        
        # 关闭连接
        cursor.close()
        connection.close()
        
        print(f"\n✅ 检查完成!")
        return True
        
    except mysql.connector.Error as e:
        print(f"❌ 数据库操作失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 执行失败: {e}")
        return False

def main():
    """主函数"""
    success = check_sp_averagelineV3()
    
    if success:
        print("\n💡 解决方案:")
        print("   修改代码中所有调用sp_averagelineV3的地方")
        print("   确保只传递1个参数（表名）")
    else:
        print("\n❌ 检查失败!")

if __name__ == "__main__":
    main()
