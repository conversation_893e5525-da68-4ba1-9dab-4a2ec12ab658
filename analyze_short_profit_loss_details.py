#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
详细分析买跌策略的止盈止损金额
============================

买跌策略详细计算：
- 买跌，止盈+1% → profit → close * (1-1%) → 赚多少钱
- 买跌，止损-2% → loss → close * (1+2%) → 赔多少钱

买涨策略详细计算：
- 买涨，止盈+2% → profit → close * (1+2%) → 赚多少钱  
- 买涨，止损-1% → loss → close * (1-1%) → 赔多少钱

作者: Cosmoon NG
日期: 2025年7月
"""

import pymysql
import pandas as pd

class ShortProfitLossAnalyzer:
    def __init__(self):
        """初始化买跌盈亏分析器"""
        self.db_config = {
            'host': 'localhost',
            'port': 3306,
            'database': 'finance',
            'user': 'root',
            'password': '12345678',
            'charset': 'utf8mb4'
        }
        self.connection = None
        self.position_size = 100  # 100股
        
    def connect_database(self):
        """连接数据库"""
        try:
            self.connection = pymysql.connect(**self.db_config)
            print(f"✅ 成功连接MySQL数据库: {self.db_config['host']}")
            return True
        except Exception as e:
            print(f"❌ 连接数据库失败: {e}")
            return False
    
    def analyze_short_strategy_details(self):
        """详细分析买跌策略的盈亏"""
        try:
            cursor = self.connection.cursor()
            
            # 获取所有记录
            cursor.execute("""
                SELECT 交易序号, 开仓日期, close, 平仓价格, `控制系数`, `资金流比例`, 
                       E值, 净利润, `收益率%`
                FROM test 
                ORDER BY 交易序号
            """)
            
            records = cursor.fetchall()
            
            print("📊 买跌策略详细盈亏分析")
            print("="*200)
            print(f"{'序号':<4} {'日期':<12} {'策略区域':<12} {'方向':<6} {'开仓价':<8} {'平仓价':<8} "
                  f"{'价格变动%':<10} {'止盈价格':<10} {'止盈金额':<10} {'止损价格':<10} {'止损金额':<10} "
                  f"{'实际结果':<12} {'实际金额':<10}")
            print("-" * 200)
            
            total_short_profit = 0
            total_long_profit = 0
            short_trades = 0
            long_trades = 0
            
            for record in records:
                (trade_id, open_date, open_price, close_price, y_val, x_val, 
                 e_val, actual_profit, actual_return) = record
                
                # 分类策略区域和方向
                if y_val > 0.43 and x_val > 0.43:
                    zone = '高值盈利区'
                    direction = '买涨'
                    take_profit_pct = 2.0  # +2%
                    stop_loss_pct = 1.0    # -1%
                elif 0.333 < y_val < 0.4:
                    zone = '控股商控制区'
                    direction = '观望'
                    take_profit_pct = 0
                    stop_loss_pct = 0
                elif y_val < 0.25 or x_val < 0.25:
                    zone = '强亏损区'
                    direction = '买跌'
                    take_profit_pct = 2.0  # +2%
                    stop_loss_pct = 1.0    # -1%
                else:
                    zone = '其他区域'
                    direction = '买跌'
                    take_profit_pct = 1.0  # +1%
                    stop_loss_pct = 2.0    # -2%
                
                if direction == '观望':
                    print(f"{trade_id:<4} {open_date:<12} {zone:<12} {direction:<6} {open_price:<8.2f} {close_price:<8.2f} "
                          f"{'观望':<10} {'观望':<10} {'观望':<10} {'观望':<10} {'观望':<10} "
                          f"{'观望':<12} {'0':<10}")
                    continue
                
                # 计算价格变动
                price_change_pct = (close_price - open_price) / open_price * 100
                
                if direction == '买跌':
                    # 买跌策略详细计算
                    short_trades += 1
                    
                    # 止盈价格：开仓价 * (1 - 止盈%)
                    take_profit_price = open_price * (1 - take_profit_pct / 100)
                    # 止盈金额：(开仓价 - 止盈价) * 股数
                    take_profit_amount = (open_price - take_profit_price) * self.position_size
                    
                    # 止损价格：开仓价 * (1 + 止损%)
                    stop_loss_price = open_price * (1 + stop_loss_pct / 100)
                    # 止损金额：(止损价 - 开仓价) * 股数 (负数，表示亏损)
                    stop_loss_amount = -(stop_loss_price - open_price) * self.position_size
                    
                    # 实际结果
                    actual_amount = (open_price - close_price) * self.position_size
                    
                    if close_price <= take_profit_price:
                        result = '止盈'
                        result_amount = take_profit_amount
                    elif close_price >= stop_loss_price:
                        result = '止损'
                        result_amount = stop_loss_amount
                    else:
                        result = '到期平仓'
                        result_amount = actual_amount
                    
                    total_short_profit += result_amount
                    
                    print(f"{trade_id:<4} {open_date:<12} {zone:<12} {direction:<6} {open_price:<8.2f} {close_price:<8.2f} "
                          f"{price_change_pct:<9.2f}% {take_profit_price:<9.2f} {take_profit_amount:<9.0f} "
                          f"{stop_loss_price:<9.2f} {stop_loss_amount:<9.0f} {result:<12} {result_amount:<9.0f}")
                
                elif direction == '买涨':
                    # 买涨策略详细计算
                    long_trades += 1
                    
                    # 止盈价格：开仓价 * (1 + 止盈%)
                    take_profit_price = open_price * (1 + take_profit_pct / 100)
                    # 止盈金额：(止盈价 - 开仓价) * 股数
                    take_profit_amount = (take_profit_price - open_price) * self.position_size
                    
                    # 止损价格：开仓价 * (1 - 止损%)
                    stop_loss_price = open_price * (1 - stop_loss_pct / 100)
                    # 止损金额：(开仓价 - 止损价) * 股数 (负数，表示亏损)
                    stop_loss_amount = -(open_price - stop_loss_price) * self.position_size
                    
                    # 实际结果
                    actual_amount = (close_price - open_price) * self.position_size
                    
                    if close_price >= take_profit_price:
                        result = '止盈'
                        result_amount = take_profit_amount
                    elif close_price <= stop_loss_price:
                        result = '止损'
                        result_amount = stop_loss_amount
                    else:
                        result = '到期平仓'
                        result_amount = actual_amount
                    
                    total_long_profit += result_amount
                    
                    print(f"{trade_id:<4} {open_date:<12} {zone:<12} {direction:<6} {open_price:<8.2f} {close_price:<8.2f} "
                          f"{price_change_pct:<9.2f}% {take_profit_price:<9.2f} {take_profit_amount:<9.0f} "
                          f"{stop_loss_price:<9.2f} {stop_loss_amount:<9.0f} {result:<12} {result_amount:<9.0f}")
            
            print("\n" + "="*100)
            print(f"📊 策略盈亏汇总:")
            print(f"   • 买跌交易: {short_trades}次, 总盈亏: {total_short_profit:+,.0f}港币")
            print(f"   • 买涨交易: {long_trades}次, 总盈亏: {total_long_profit:+,.0f}港币")
            print(f"   • 总盈亏: {total_short_profit + total_long_profit:+,.0f}港币")
            
            return True
            
        except Exception as e:
            print(f"❌ 分析买跌策略失败: {e}")
            return False
    
    def analyze_by_zone_detailed(self):
        """按区域详细分析盈亏"""
        try:
            cursor = self.connection.cursor()
            
            print(f"\n📊 按策略区域详细盈亏分析:")
            print("="*120)
            
            zones = [
                ('高值盈利区', 'Y值 > 0.43 AND X值 > 0.43', '买涨', 2.0, 1.0),
                ('强亏损区', '(Y值 < 0.25 OR X值 < 0.25) AND NOT (Y值 > 0.333 AND Y值 < 0.4)', '买跌', 2.0, 1.0),
                ('其他区域', 'NOT (Y值 > 0.43 AND X值 > 0.43) AND NOT (Y值 > 0.333 AND Y值 < 0.4) AND NOT (Y值 < 0.25 OR X值 < 0.25)', '买跌', 1.0, 2.0)
            ]
            
            for zone_name, condition, direction, take_profit, stop_loss in zones:
                print(f"\n🎯 {zone_name} - {direction}策略:")
                print(f"   止盈: +{take_profit}%, 止损: -{stop_loss}%")
                print("-" * 80)
                
                # 构建查询条件
                if zone_name == '高值盈利区':
                    where_clause = "`控制系数` > 0.43 AND `资金流比例` > 0.43"
                elif zone_name == '强亏损区':
                    where_clause = "(`控制系数` < 0.25 OR `资金流比例` < 0.25) AND NOT (`控制系数` > 0.333 AND `控制系数` < 0.4)"
                else:  # 其他区域
                    where_clause = """NOT (`控制系数` > 0.43 AND `资金流比例` > 0.43) 
                                     AND NOT (`控制系数` > 0.333 AND `控制系数` < 0.4) 
                                     AND NOT (`控制系数` < 0.25 OR `资金流比例` < 0.25)"""
                
                cursor.execute(f"""
                    SELECT 交易序号, 开仓日期, close, 平仓价格
                    FROM test 
                    WHERE {where_clause}
                    ORDER BY 交易序号
                """)
                
                trades = cursor.fetchall()
                
                if not trades:
                    print("   无交易记录")
                    continue
                
                print(f"{'序号':<4} {'日期':<12} {'开仓价':<8} {'平仓价':<8} {'止盈价':<8} {'止盈金额':<8} "
                      f"{'止损价':<8} {'止损金额':<8} {'实际结果':<10} {'实际金额':<8}")
                print("-" * 80)
                
                zone_total_profit = 0
                take_profit_count = 0
                stop_loss_count = 0
                normal_close_count = 0
                
                for trade_id, open_date, open_price, close_price in trades:
                    if direction == '买跌':
                        # 买跌计算
                        take_profit_price = open_price * (1 - take_profit / 100)
                        take_profit_amount = (open_price - take_profit_price) * self.position_size
                        
                        stop_loss_price = open_price * (1 + stop_loss / 100)
                        stop_loss_amount = -(stop_loss_price - open_price) * self.position_size
                        
                        actual_amount = (open_price - close_price) * self.position_size
                        
                        if close_price <= take_profit_price:
                            result = '止盈'
                            result_amount = take_profit_amount
                            take_profit_count += 1
                        elif close_price >= stop_loss_price:
                            result = '止损'
                            result_amount = stop_loss_amount
                            stop_loss_count += 1
                        else:
                            result = '到期平仓'
                            result_amount = actual_amount
                            normal_close_count += 1
                    
                    else:  # 买涨
                        # 买涨计算
                        take_profit_price = open_price * (1 + take_profit / 100)
                        take_profit_amount = (take_profit_price - open_price) * self.position_size
                        
                        stop_loss_price = open_price * (1 - stop_loss / 100)
                        stop_loss_amount = -(open_price - stop_loss_price) * self.position_size
                        
                        actual_amount = (close_price - open_price) * self.position_size
                        
                        if close_price >= take_profit_price:
                            result = '止盈'
                            result_amount = take_profit_amount
                            take_profit_count += 1
                        elif close_price <= stop_loss_price:
                            result = '止损'
                            result_amount = stop_loss_amount
                            stop_loss_count += 1
                        else:
                            result = '到期平仓'
                            result_amount = actual_amount
                            normal_close_count += 1
                    
                    zone_total_profit += result_amount
                    
                    print(f"{trade_id:<4} {open_date:<12} {open_price:<8.2f} {close_price:<8.2f} "
                          f"{take_profit_price:<8.2f} {take_profit_amount:<8.0f} {stop_loss_price:<8.2f} "
                          f"{stop_loss_amount:<8.0f} {result:<10} {result_amount:<8.0f}")
                
                total_trades = len(trades)
                print(f"\n   📈 {zone_name}汇总:")
                print(f"   • 总交易: {total_trades}次")
                print(f"   • 止盈: {take_profit_count}次 ({take_profit_count/total_trades*100:.1f}%)")
                print(f"   • 止损: {stop_loss_count}次 ({stop_loss_count/total_trades*100:.1f}%)")
                print(f"   • 到期平仓: {normal_close_count}次 ({normal_close_count/total_trades*100:.1f}%)")
                print(f"   • 总盈亏: {zone_total_profit:+,.0f}港币")
                print(f"   • 平均盈亏: {zone_total_profit/total_trades:+.0f}港币/次")
                
                # 分析赔率效果
                if take_profit_count > 0 and stop_loss_count > 0:
                    actual_win_rate = take_profit_count / (take_profit_count + stop_loss_count)
                    theoretical_odds = take_profit / stop_loss
                    print(f"   • 止盈止损胜率: {actual_win_rate:.3f}")
                    print(f"   • 理论赔率: {theoretical_odds:.1f}:1")
                    
                    # 凯利公式
                    kelly_f = (theoretical_odds * actual_win_rate - (1 - actual_win_rate)) / theoretical_odds
                    print(f"   • 凯利系数: {kelly_f:.6f}")
            
            return True
            
        except Exception as e:
            print(f"❌ 按区域分析失败: {e}")
            return False
    
    def close_connection(self):
        """关闭数据库连接"""
        if self.connection:
            self.connection.close()
            print("\n🔒 数据库连接已关闭")

def main():
    """主函数"""
    print("📊 买跌/买涨策略详细盈亏分析")
    print("="*80)
    print("🔍 详细计算公式:")
    print("   买跌止盈: 开仓价 * (1 - 止盈%) → 赚 (开仓价 - 止盈价) * 股数")
    print("   买跌止损: 开仓价 * (1 + 止损%) → 赔 (止损价 - 开仓价) * 股数")
    print("   买涨止盈: 开仓价 * (1 + 止盈%) → 赚 (止盈价 - 开仓价) * 股数")
    print("   买涨止损: 开仓价 * (1 - 止损%) → 赔 (开仓价 - 止损价) * 股数")
    
    # 创建分析器
    analyzer = ShortProfitLossAnalyzer()
    
    # 连接数据库
    if not analyzer.connect_database():
        return
    
    # 详细分析买跌策略
    analyzer.analyze_short_strategy_details()
    
    # 按区域详细分析
    analyzer.analyze_by_zone_detailed()
    
    # 关闭连接
    analyzer.close_connection()
    
    print(f"\n🎯 详细盈亏分析完成！")

if __name__ == "__main__":
    main()
