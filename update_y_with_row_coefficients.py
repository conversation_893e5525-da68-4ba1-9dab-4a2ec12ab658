#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
使用存储过程更新Y值 - Row Coefficients版本
=========================================

调用存储过程 sp_stock_analysis_with_row_coefficients 来计算和更新Y值
- 连接MariaDB数据库
- 调用存储过程
- 验证Y值更新结果
- 显示更新统计

作者: Cosmoon NG
日期: 2025年7月
"""

import pymysql
from datetime import datetime
import pandas as pd

# 数据库配置
db_config = {
    'host': 'localhost',
    'user': 'root',
    'password': '12345678',
    'database': 'finance',
    'charset': 'utf8mb4'
}

def check_stored_procedure():
    """检查存储过程是否存在"""
    print("🔍 检查存储过程 sp_stock_analysis_with_row_coefficients...")
    
    try:
        conn = pymysql.connect(**db_config)
        cursor = conn.cursor()
        
        # 检查存储过程是否存在
        cursor.execute("""
            SELECT ROUTINE_NAME, ROUTINE_TYPE, CREATED, LAST_ALTERED 
            FROM information_schema.ROUTINES 
            WHERE ROUTINE_SCHEMA = 'finance' 
            AND ROUTINE_NAME = 'sp_stock_analysis_with_row_coefficients'
        """)
        
        result = cursor.fetchone()
        
        if result:
            print(f"✅ 找到存储过程:")
            print(f"   • 名称: {result[0]}")
            print(f"   • 类型: {result[1]}")
            print(f"   • 创建时间: {result[2]}")
            print(f"   • 最后修改: {result[3]}")
            
            # 获取存储过程参数信息
            cursor.execute("""
                SELECT PARAMETER_NAME, DATA_TYPE, PARAMETER_MODE
                FROM information_schema.PARAMETERS 
                WHERE SPECIFIC_SCHEMA = 'finance' 
                AND SPECIFIC_NAME = 'sp_stock_analysis_with_row_coefficients'
                ORDER BY ORDINAL_POSITION
            """)
            
            params = cursor.fetchall()
            if params:
                print(f"\n📋 存储过程参数:")
                for param in params:
                    print(f"   • {param[2]} {param[0]} ({param[1]})")
            else:
                print(f"   • 无参数或参数信息不可用")
                
        else:
            print("❌ 未找到存储过程 sp_stock_analysis_with_row_coefficients")
            
            # 列出所有可用的存储过程
            cursor.execute("""
                SELECT ROUTINE_NAME 
                FROM information_schema.ROUTINES 
                WHERE ROUTINE_SCHEMA = 'finance' 
                AND ROUTINE_TYPE = 'PROCEDURE'
                ORDER BY ROUTINE_NAME
            """)
            
            all_procs = cursor.fetchall()
            if all_procs:
                print(f"\n📋 可用的存储过程:")
                for proc in all_procs:
                    print(f"   • {proc[0]}")
            
            return False
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 检查存储过程失败: {e}")
        return False

def check_table_structure():
    """检查hkhsi50表结构"""
    print("\n📊 检查hkhsi50表结构...")
    
    try:
        conn = pymysql.connect(**db_config)
        cursor = conn.cursor()
        
        # 检查表结构
        cursor.execute("DESCRIBE hkhsi50")
        columns = cursor.fetchall()
        
        print(f"📋 表结构:")
        for col in columns:
            print(f"   • {col[0]} ({col[1]}) - {col[2]} {col[3]} {col[4]} {col[5]}")
        
        # 检查数据统计
        cursor.execute("SELECT COUNT(*) FROM hkhsi50")
        total_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT MIN(date), MAX(date) FROM hkhsi50")
        date_range = cursor.fetchone()
        
        print(f"\n📈 数据统计:")
        print(f"   • 总记录数: {total_count}")
        print(f"   • 日期范围: {date_range[0]} 至 {date_range[1]}")
        
        # 检查Y值相关字段
        y_fields = ['y_value', 'base_y', 'x_value', 'e_value']
        for field in y_fields:
            try:
                cursor.execute(f"SELECT COUNT(*) FROM hkhsi50 WHERE {field} IS NOT NULL")
                count = cursor.fetchone()[0]
                percentage = count / total_count * 100 if total_count > 0 else 0
                print(f"   • {field}: {count} 条 ({percentage:.1f}%)")
            except:
                print(f"   • {field}: 字段不存在")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 检查表结构失败: {e}")
        return False

def call_stored_procedure():
    """调用存储过程更新Y值"""
    print("\n🚀 调用存储过程 sp_stock_analysis_with_row_coefficients...")
    
    try:
        conn = pymysql.connect(**db_config)
        cursor = conn.cursor()
        
        # 记录开始时间
        start_time = datetime.now()
        print(f"   • 开始时间: {start_time.strftime('%Y-%m-%d %H:%M:%S')}")
        
        # 调用存储过程 - 尝试不同的参数组合
        try:
            # 尝试1: 只传表名
            print("   • 尝试调用: sp_stock_analysis_with_row_coefficients('hkhsi50')")
            cursor.callproc('sp_stock_analysis_with_row_coefficients', ['hkhsi50'])
        except Exception as e1:
            try:
                # 尝试2: 无参数
                print("   • 尝试调用: sp_stock_analysis_with_row_coefficients()")
                cursor.callproc('sp_stock_analysis_with_row_coefficients', [])
            except Exception as e2:
                try:
                    # 尝试3: 直接执行CALL语句
                    print("   • 尝试直接执行: CALL sp_stock_analysis_with_row_coefficients('hkhsi50')")
                    cursor.execute("CALL sp_stock_analysis_with_row_coefficients('hkhsi50')")
                except Exception as e3:
                    print(f"   ❌ 所有调用方式都失败:")
                    print(f"      - 方式1错误: {e1}")
                    print(f"      - 方式2错误: {e2}")
                    print(f"      - 方式3错误: {e3}")
                    return False
        
        # 获取存储过程的结果（如果有）
        results = []
        try:
            for result in cursor.stored_results():
                rows = result.fetchall()
                results.extend(rows)
                for row in rows:
                    print(f"   • 存储过程输出: {row}")
        except:
            pass  # 有些存储过程可能不返回结果集
        
        # 提交事务
        conn.commit()
        
        # 记录结束时间
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        
        print(f"   • 结束时间: {end_time.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"   • 执行耗时: {duration:.2f} 秒")
        
        if results:
            print(f"   • 存储过程返回: {len(results)} 条结果")
        
        print("✅ 存储过程执行完成")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 调用存储过程失败: {e}")
        return False

def verify_results():
    """验证更新结果"""
    print("\n📊 验证Y值更新结果...")
    
    try:
        conn = pymysql.connect(**db_config)
        cursor = conn.cursor()
        
        # 检查Y值更新情况
        cursor.execute("""
            SELECT COUNT(*) FROM hkhsi50 
            WHERE y_value IS NOT NULL
        """)
        y_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM hkhsi50")
        total_count = cursor.fetchone()[0]
        
        # 检查Y值统计
        cursor.execute("""
            SELECT 
                MIN(y_value) as min_y,
                MAX(y_value) as max_y,
                AVG(y_value) as avg_y,
                STDDEV(y_value) as std_y
            FROM hkhsi50 
            WHERE y_value IS NOT NULL
        """)
        
        stats = cursor.fetchone()
        
        print(f"📈 更新结果:")
        print(f"   • 总记录数: {total_count}")
        print(f"   • 有Y值记录: {y_count}")
        print(f"   • Y值覆盖率: {y_count/total_count*100:.1f}%")
        
        if stats and stats[0] is not None:
            print(f"\n📊 Y值统计:")
            print(f"   • 最小值: {stats[0]:.4f}")
            print(f"   • 最大值: {stats[1]:.4f}")
            print(f"   • 平均值: {stats[2]:.4f}")
            print(f"   • 标准差: {stats[3]:.4f}")
        
        # 显示最新的Y值
        cursor.execute("""
            SELECT date, close, y_value, x_value, e_value
            FROM hkhsi50 
            WHERE y_value IS NOT NULL
            ORDER BY date DESC 
            LIMIT 10
        """)
        
        recent_data = cursor.fetchall()
        if recent_data:
            print(f"\n📋 最新10条记录:")
            print(f"{'日期':<12} {'收盘价':<10} {'Y值':<8} {'X值':<8} {'E值':<8}")
            print("-" * 55)
            for row in recent_data:
                y_val = f"{row[2]:.3f}" if row[2] is not None else "NULL"
                x_val = f"{row[3]:.3f}" if row[3] is not None else "NULL"
                e_val = f"{row[4]:.3f}" if row[4] is not None else "NULL"
                print(f"{row[0]:<12} {row[1]:<10.2f} {y_val:<8} {x_val:<8} {e_val:<8}")
        
        # 检查策略区域分布
        cursor.execute("""
            SELECT 
                CASE 
                    WHEN y_value > 0.4 AND x_value > 0.4 THEN '高值盈利区'
                    WHEN y_value > 0.333 AND y_value < 0.4 THEN '控股商控制区'
                    WHEN y_value < 0.25 OR x_value < 0.25 THEN '强亏损区'
                    ELSE '其他区域'
                END AS 策略区域,
                COUNT(*) AS 数量
            FROM hkhsi50 
            WHERE y_value IS NOT NULL AND x_value IS NOT NULL
            GROUP BY 策略区域
            ORDER BY 数量 DESC
        """)
        
        zone_data = cursor.fetchall()
        if zone_data:
            print(f"\n🎯 策略区域分布:")
            for zone, count in zone_data:
                percentage = count / y_count * 100 if y_count > 0 else 0
                print(f"   • {zone}: {count} 条 ({percentage:.1f}%)")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 验证结果失败: {e}")
        return False

def export_results():
    """导出结果"""
    print("\n💾 导出更新结果...")
    
    try:
        conn = pymysql.connect(**db_config)
        
        # 查询最新数据
        query = """
            SELECT date, open, high, low, close, volume,
                   ma20, ma60, rsi, money_flow,
                   base_x, x_value, base_y, y_value, e_value
            FROM hkhsi50 
            WHERE y_value IS NOT NULL
            ORDER BY date DESC
            LIMIT 500
        """
        
        df = pd.read_sql(query, conn)
        
        # 保存到Excel
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"hkhsi50_row_coefficients_result_{timestamp}.xlsx"
        
        df.to_excel(filename, index=False)
        
        print(f"✅ 结果已导出到: {filename}")
        print(f"   • 导出记录数: {len(df)}")
        
        conn.close()
        return filename
        
    except Exception as e:
        print(f"❌ 导出结果失败: {e}")
        return None

def main():
    """主函数"""
    print("🎯 使用存储过程更新hkhsi50的Y值")
    print("="*60)
    print("📋 将调用: sp_stock_analysis_with_row_coefficients")
    
    # 1. 检查存储过程
    if not check_stored_procedure():
        return
    
    # 2. 检查表结构
    if not check_table_structure():
        return
    
    # 3. 确认执行
    print(f"\n⚠️  即将调用存储过程更新Y值")
    confirm = input("是否继续？(y/N): ").lower().strip()
    if confirm != 'y':
        print("❌ 操作已取消")
        return
    
    # 4. 调用存储过程
    if not call_stored_procedure():
        return
    
    # 5. 验证结果
    if not verify_results():
        return
    
    # 6. 导出结果
    export_results()
    
    print(f"\n🎉 Y值更新完成！")
    print(f"💡 存储过程 sp_stock_analysis_with_row_coefficients 已成功执行")

if __name__ == "__main__":
    main()
