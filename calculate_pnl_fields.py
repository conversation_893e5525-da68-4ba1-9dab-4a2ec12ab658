#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
计算Excel表格中的盈亏字段
========================

正确计算以下字段:
- 浮动盈亏 (当前持仓的未实现盈亏)
- 实现盈亏 (已平仓的盈亏)
- 累计盈亏 (总盈亏)
- 收益率 (当日收益率)

作者: Cosmoon NG
"""

import pandas as pd
import mysql.connector
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

def calculate_pnl_fields():
    """计算盈亏相关字段"""
    
    excel_file = "交易记录追踪0023HK.xlsx"
    
    print("💰 计算盈亏字段...")
    
    # 用户实际参数
    total_capital = 2500.00      # 总资本
    max_shares = 200             # 最大持仓股数
    current_price = 12.14        # 当前价格 (从实时监控获取)
    commission_rate = 0.001      # 手续费率
    
    # 交易参数 (假设空头开仓)
    entry_price = current_price  # 开仓价格
    trade_direction = "空头"     # 交易方向
    
    print(f"📊 交易参数:")
    print(f"   总资本: {total_capital:,.2f} 港元")
    print(f"   持仓: {max_shares} 股")
    print(f"   开仓价格: {entry_price:.2f} 港元")
    print(f"   当前价格: {current_price:.2f} 港元")
    print(f"   交易方向: {trade_direction}")
    
    # 计算交易金额和费用
    trade_amount = current_price * max_shares  # 2428港元
    commission = trade_amount * commission_rate  # 2.43港元
    net_amount = trade_amount - commission
    
    # 计算账户状态
    account_balance = total_capital - trade_amount - commission  # 剩余现金
    current_market_value = current_price * max_shares  # 当前市值
    
    # 计算盈亏
    if trade_direction == "空头":
        # 空头: 开仓价格高于当前价格为盈利
        price_diff = entry_price - current_price  # 价格差
        unrealized_pnl = price_diff * max_shares  # 浮动盈亏
    else:
        # 多头: 当前价格高于开仓价格为盈利
        price_diff = current_price - entry_price
        unrealized_pnl = price_diff * max_shares
    
    # 由于是开仓，实现盈亏为0
    realized_pnl = 0.00
    
    # 累计盈亏 = 浮动盈亏 + 实现盈亏
    cumulative_pnl = unrealized_pnl + realized_pnl
    
    # 计算总资产
    total_assets = account_balance + current_market_value + unrealized_pnl
    
    # 计算收益率 (相对于初始资本)
    daily_return = (total_assets - total_capital) / total_capital * 100
    cumulative_return = daily_return  # 首日累计收益率等于当日收益率
    
    print(f"\n💰 盈亏计算结果:")
    print(f"   交易金额: {trade_amount:,.2f} 港元")
    print(f"   手续费: {commission:.2f} 港元")
    print(f"   账户余额: {account_balance:,.2f} 港元")
    print(f"   当前市值: {current_market_value:,.2f} 港元")
    print(f"   浮动盈亏: {unrealized_pnl:,.2f} 港元")
    print(f"   实现盈亏: {realized_pnl:,.2f} 港元")
    print(f"   累计盈亏: {cumulative_pnl:,.2f} 港元")
    print(f"   总资产: {total_assets:,.2f} 港元")
    print(f"   收益率: {daily_return:.2f}%")
    print(f"   累计收益率: {cumulative_return:.2f}%")
    
    # 计算止盈止损价格
    take_profit_rate = 0.012  # 1.2%
    stop_loss_rate = 0.006    # 0.6%
    
    if trade_direction == "空头":
        take_profit_price = entry_price * (1 - take_profit_rate)  # 12.03
        stop_loss_price = entry_price * (1 + stop_loss_rate)      # 12.25
    else:
        take_profit_price = entry_price * (1 + take_profit_rate)
        stop_loss_price = entry_price * (1 - stop_loss_rate)
    
    print(f"\n🎯 止盈止损:")
    print(f"   止盈价: {take_profit_price:.2f} 港元")
    print(f"   止损价: {stop_loss_price:.2f} 港元")
    
    # 获取技术指标
    try:
        config = {
            'host': 'localhost',
            'port': 3306,
            'user': 'root',
            'password': '12345678',
            'database': 'finance'
        }
        connection = mysql.connector.connect(**config)
        cursor = connection.cursor()
        query = "SELECT Y_Value, X_Value, E_Value, Full_Y, MoneyFlowRatio FROM eab_0023hk ORDER BY Date DESC LIMIT 1"
        cursor.execute(query)
        result = cursor.fetchone()
        cursor.close()
        connection.close()
        
        if result:
            y_val, x_val, e_val, full_y, mfr = result
            print(f"\n📈 技术指标 (从数据库):")
        else:
            y_val, x_val, e_val, full_y, mfr = 0.2069, 0.3211, -0.0525, 0.2069, 0.3211
            print(f"\n📈 技术指标 (默认值):")
    except:
        y_val, x_val, e_val, full_y, mfr = 0.2069, 0.3211, -0.0525, 0.2069, 0.3211
        print(f"\n📈 技术指标 (默认值):")
    
    # 计算MyE
    mye_value = 8 * mfr * full_y - 3 * mfr - 3 * full_y + 1
    
    print(f"   Y值: {y_val:.4f}")
    print(f"   Full_Y: {full_y:.4f}")
    print(f"   X值: {x_val:.4f}")
    print(f"   MoneyFlowRatio: {mfr:.4f}")
    print(f"   E值: {e_val:.4f}")
    print(f"   MyE: {mye_value:.4f}")
    
    # 创建完整记录
    complete_record = {
        '交易日期': datetime.now().strftime('%Y-%m-%d'),
        '交易类型': '开仓',
        '交易方向': trade_direction,
        '交易价格': current_price,
        '止盈价': take_profit_price,
        '止损价': stop_loss_price,
        '持仓数量': max_shares,
        '交易金额': trade_amount,
        '手续费': commission,
        '净交易额': net_amount,
        '持仓成本': entry_price,
        '当前市值': current_market_value,
        '浮动盈亏': unrealized_pnl,
        '实现盈亏': realized_pnl,
        '累计盈亏': cumulative_pnl,
        '账户余额': account_balance,
        '总资产': total_assets,
        '收益率': daily_return,
        '累计收益率': cumulative_return,
        'Y值': y_val,
        'Full_Y': full_y,
        'X值': x_val,
        'MoneyFlowRatio': mfr,
        'E值': e_val,
        'MyE': mye_value,
        '信号强度': '强烈卖出',
        '风险等级': '高风险',
        '备注': f'完整盈亏计算 {trade_direction}开仓 浮动盈亏{unrealized_pnl:.2f}港元'
    }
    
    # 更新Excel文件
    try:
        df = pd.read_excel(excel_file)
        print(f"\n📋 Excel文件状态:")
        print(f"   当前记录数: {len(df)}")
        
        # 替换或添加最新记录
        if len(df) > 0:
            df.iloc[-1] = complete_record
            print("🔄 已更新最新记录")
        else:
            df = pd.concat([df, pd.DataFrame([complete_record])], ignore_index=True)
            print("➕ 已添加新记录")
        
        # 保存
        df.to_excel(excel_file, index=False)
        print(f"✅ Excel文件已保存: {excel_file}")
        
        # 验证关键字段
        print(f"\n🔍 关键字段验证:")
        latest = df.iloc[-1]
        key_fields = ['浮动盈亏', '实现盈亏', '累计盈亏', '收益率', '累计收益率']
        for field in key_fields:
            if field in df.columns:
                value = latest[field]
                print(f"   ✅ {field}: {value}")
            else:
                print(f"   ❌ {field}: 字段不存在")
        
        return True
        
    except Exception as e:
        print(f"❌ Excel操作失败: {e}")
        return False

def main():
    """主函数"""
    print("💰 Excel盈亏字段计算器")
    print("=" * 50)
    
    try:
        success = calculate_pnl_fields()
        
        if success:
            print(f"\n🎯 盈亏字段计算完成！")
            print(f"✅ 浮动盈亏: 基于当前持仓的未实现盈亏")
            print(f"✅ 实现盈亏: 已平仓交易的盈亏")
            print(f"✅ 累计盈亏: 总盈亏金额")
            print(f"✅ 收益率: 当日收益率百分比")
        else:
            print(f"\n❌ 盈亏字段计算失败")
            
    except Exception as e:
        print(f"❌ 系统错误: {e}")

if __name__ == "__main__":
    main()
