#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查HSI数据
=========

检查恒生指数25年数据

作者: Cosmoon NG
日期: 2025年7月
"""

import sqlite3
import pandas as pd
import matplotlib.pyplot as plt

def check_hsi_data():
    """检查HSI数据"""
    try:
        # 连接数据库
        conn = sqlite3.connect('hsi_25years.db')
        
        # 读取数据
        df = pd.read_sql("""
            SELECT date, open, high, low, close, volume,
                   ma_20, ma_60, rsi, volume_ratio, inflow_ratio
            FROM hsi_data
            ORDER BY date
        """, conn)
        
        # 显示基本信息
        print("\n数据基本信息：")
        print("="*50)
        print(f"数据范围：{df['date'].min()} 至 {df['date'].max()}")
        print(f"总交易日数：{len(df):,}")
        
        # 检查数据完整性
        print("\n数据完整性检查：")
        print("="*50)
        for col in df.columns:
            null_count = df[col].isnull().sum()
            print(f"{col}: {null_count:,} 个空值")
        
        # 显示数据样本
        print("\n数据样本：")
        print("="*50)
        print(df.head())
        
        # 绘制价格图表
        plt.figure(figsize=(15, 7))
        plt.plot(pd.to_datetime(df['date']), df['close'])
        plt.title('恒生指数25年走势')
        plt.xlabel('日期')
        plt.ylabel('价格')
        plt.grid(True)
        plt.savefig('hsi_25years.png')
        plt.close()
        
        conn.close()
        print("\n✅ 数据检查完成")
        print("• 价格走势图已保存到 hsi_25years.png")
        
    except Exception as e:
        print(f"❌ 检查失败: {str(e)}")

def main():
    """主函数"""
    print("\nHSI数据检查工具")
    print("="*50)
    
    try:
        check_hsi_data()
    except Exception as e:
        print(f"\n❌ 程序运行失败: {str(e)}")
    else:
        print("\n程序运行完成")

if __name__ == "__main__":
    main()
