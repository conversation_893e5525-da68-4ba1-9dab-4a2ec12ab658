#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
总资产详细分析
============
分析20年期XY策略的总资产构成和变化
"""

import pandas as pd
import numpy as np
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

def analyze_total_assets():
    """分析总资产构成"""
    
    print("💰 20年期XY策略总资产详细分析")
    print("=" * 60)
    print(f"📅 分析时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 基础数据
    initial_capital = 30000      # 初始资金
    monthly_addition = 1000      # 每月追加
    years = 20.6                 # 投资年数
    months = years * 12          # 总月数
    final_capital = 592740       # 最终资金
    
    # 计算总投入
    total_invested = initial_capital + monthly_addition * months
    net_profit = final_capital - total_invested
    
    print(f"\n📊 总资产构成分析:")
    print(f"   💰 最终总资产: {final_capital:,.2f}港元")
    print(f"   📈 初始投入: {initial_capital:,}港元")
    print(f"   💵 定投累计: {monthly_addition * months:,.0f}港元 ({months:.0f}个月)")
    print(f"   🎯 总投入: {total_invested:,.2f}港元")
    print(f"   📊 策略收益: {net_profit:,.2f}港元")
    
    # 资产构成比例
    initial_ratio = initial_capital / final_capital * 100
    monthly_ratio = (monthly_addition * months) / final_capital * 100
    profit_ratio = net_profit / final_capital * 100
    
    print(f"\n📊 资产构成比例:")
    print(f"   • 初始投入占比: {initial_ratio:.1f}%")
    print(f"   • 定投累计占比: {monthly_ratio:.1f}%")
    print(f"   • 策略收益占比: {profit_ratio:.1f}%")
    
    # 35%仓位的实际金额
    position_ratio = 0.35
    trading_capital = final_capital * position_ratio
    cash_reserve = final_capital * (1 - position_ratio)
    
    print(f"\n💼 资产配置分析 (35%仓位策略):")
    print(f"   🎯 交易资金 (35%): {trading_capital:,.2f}港元")
    print(f"   💵 现金储备 (65%): {cash_reserve:,.2f}港元")
    print(f"   📊 单笔最大交易: {trading_capital:,.2f}港元")
    print(f"   📉 单笔最大风险: {trading_capital * 0.008:,.2f}港元 (0.8%止损)")
    print(f"   📈 单笔最大收益: {trading_capital * 0.016:,.2f}港元 (1.6%止盈)")
    
    # 不同时期的资产水平
    print(f"\n📈 资产增长轨迹:")
    
    milestones = [
        (0, initial_capital, "起始"),
        (1, initial_capital + 12 * monthly_addition, "第1年末"),
        (5, initial_capital + 60 * monthly_addition, "第5年末"),
        (10, initial_capital + 120 * monthly_addition, "第10年末"),
        (15, initial_capital + 180 * monthly_addition, "第15年末"),
        (20, initial_capital + 240 * monthly_addition, "第20年末(仅投入)"),
        (20.6, final_capital, "实际最终")
    ]
    
    print(f"   时间点 | 资产金额 | 35%交易资金 | 65%现金储备")
    print(f"   " + "-" * 55)
    
    for year, capital, description in milestones:
        trading = capital * 0.35
        cash = capital * 0.65
        print(f"   {description:12s} | {capital:9,.0f} | {trading:10,.0f} | {cash:10,.0f}")
    
    # 与其他资产规模对比
    print(f"\n📊 资产规模对比:")
    
    asset_levels = [
        ("小额投资者", "10-50万", "入门级"),
        ("中等投资者", "50-200万", "成长级"),
        ("较大投资者", "200-500万", "成熟级"),
        ("高净值投资者", "500-1000万", "富裕级"),
        ("超高净值投资者", "1000万+", "顶级")
    ]
    
    current_level = final_capital / 10000  # 转换为万元
    
    print(f"   投资者类型 | 资产范围 | 等级 | 当前状态")
    print(f"   " + "-" * 50)
    
    for investor_type, asset_range, level in asset_levels:
        if investor_type == "小额投资者":
            status = "✅ 已达到" if current_level >= 10 else "❌ 未达到"
        elif investor_type == "中等投资者":
            status = "✅ 已达到" if current_level >= 50 else "❌ 未达到"
        elif investor_type == "较大投资者":
            status = "✅ 已达到" if current_level >= 200 else "❌ 未达到"
        else:
            status = "❌ 未达到"
        
        print(f"   {investor_type:12s} | {asset_range:8s} | {level:6s} | {status}")
    
    print(f"\n   🎯 当前资产水平: {current_level:.1f}万港元")
    print(f"   📊 已达到: 中等投资者级别 (50-200万)")
    
    # 购买力分析
    print(f"\n🏠 购买力分析:")
    
    # 香港房价参考 (2025年估算)
    hk_property_prices = [
        ("新界小型单位", "400-600万", "可付首期"),
        ("九龙中型单位", "800-1200万", "需要更多资金"),
        ("港岛大型单位", "1500-3000万", "需要大幅增长"),
        ("豪华住宅", "3000万+", "遥不可及")
    ]
    
    print(f"   物业类型 | 价格范围 | 可负担性")
    print(f"   " + "-" * 40)
    
    for property_type, price_range, affordability in hk_property_prices:
        print(f"   {property_type:12s} | {price_range:10s} | {affordability}")
    
    # 生活水平分析
    print(f"\n🎯 生活水平分析:")
    
    # 基于4%提取率计算年收入
    annual_income = final_capital * 0.04
    monthly_income = annual_income / 12
    
    print(f"   💰 基于4%提取率:")
    print(f"   • 年收入: {annual_income:,.0f}港元")
    print(f"   • 月收入: {monthly_income:,.0f}港元")
    
    # 香港生活水平参考
    living_standards = [
        ("基本生活", "15,000-25,000", "勉强维持"),
        ("舒适生活", "25,000-40,000", "中产水平"),
        ("富裕生活", "40,000-80,000", "高收入"),
        ("奢华生活", "80,000+", "顶级享受")
    ]
    
    print(f"\n   生活水平 | 月支出范围 | 当前状态")
    print(f"   " + "-" * 40)
    
    for standard, expense_range, status in living_standards:
        if standard == "基本生活":
            current_status = "✅ 可支撑" if monthly_income >= 15000 else "❌ 不足"
        elif standard == "舒适生活":
            current_status = "✅ 可支撑" if monthly_income >= 25000 else "❌ 不足"
        elif standard == "富裕生活":
            current_status = "✅ 可支撑" if monthly_income >= 40000 else "❌ 不足"
        else:
            current_status = "❌ 不足"
        
        print(f"   {standard:8s} | {expense_range:12s} | {current_status}")
    
    # 退休规划分析
    print(f"\n🎯 退休规划分析:")
    
    retirement_scenarios = [
        ("基本退休", 300, "维持基本生活"),
        ("舒适退休", 500, "中产退休生活"),
        ("富裕退休", 800, "高质量退休"),
        ("奢华退休", 1200, "顶级退休享受")
    ]
    
    print(f"   退休类型 | 所需资产(万) | 当前进度")
    print(f"   " + "-" * 40)
    
    for retirement_type, required_assets, description in retirement_scenarios:
        progress = min(current_level / required_assets * 100, 100)
        status = "✅ 已达成" if progress >= 100 else f"📊 {progress:.0f}%"
        
        print(f"   {retirement_type:8s} | {required_assets:10.0f} | {status:8s}")
    
    # 继续投资的潜力
    print(f"\n🚀 继续投资潜力:")
    
    # 如果继续投资10年
    future_scenarios = [
        ("继续10年", 10, 30, 1000),
        ("继续15年", 15, 30, 1000),
        ("继续20年", 20, 30, 1000),
        ("提高投入", 10, 30, 2000)
    ]
    
    annual_return = 0.0372  # 3.72%年化收益率
    
    print(f"   情况 | 年数 | 起始(万) | 月投入 | 预期最终(万)")
    print(f"   " + "-" * 55)
    
    for scenario, years, start_capital, monthly_invest in future_scenarios:
        # 简化计算
        total_future_investment = start_capital * 10000 + monthly_invest * 12 * years
        future_value = total_future_investment * (1 + annual_return) ** years
        
        print(f"   {scenario:8s} | {years:4d} | {start_capital:7.0f} | {monthly_invest:6,d} | {future_value/10000:10.0f}")
    
    # 总结
    print(f"\n🎉 总资产分析总结:")
    print(f"   📊 当前总资产: {final_capital:,.0f}港元 ({current_level:.1f}万)")
    print(f"   🎯 资产等级: 中等投资者 (50-200万级别)")
    print(f"   💰 被动收入: 月收入{monthly_income:,.0f}港元 (4%提取率)")
    print(f"   🏠 购买力: 可付新界小型单位首期")
    print(f"   🎯 退休状况: 已达成舒适退休标准")
    print(f"   🚀 增长潜力: 继续投资可达更高水平")

def calculate_asset_growth_trajectory():
    """计算资产增长轨迹"""
    
    print(f"\n📈 详细资产增长轨迹:")
    print(f"=" * 40)
    
    # 年度资产增长模拟
    years = list(range(0, 21))
    assets = []
    
    initial = 30000
    monthly = 1000
    annual_return = 0.0372
    
    for year in years:
        if year == 0:
            asset = initial
        else:
            # 简化计算：年末一次性投资
            total_invested = initial + monthly * 12 * year
            asset = total_invested * (1 + annual_return) ** year
        
        assets.append(asset)
    
    print(f"   年份 | 累计投入 | 总资产 | 策略收益 | 资产等级")
    print(f"   " + "-" * 55)
    
    for i, year in enumerate(years[::5]):  # 每5年显示一次
        if i < len(assets[::5]):
            asset = assets[::5][i]
            invested = initial + monthly * 12 * year if year > 0 else initial
            profit = asset - invested
            
            if asset < 100000:
                level = "入门"
            elif asset < 500000:
                level = "小额"
            elif asset < 2000000:
                level = "中等"
            else:
                level = "较大"
            
            print(f"   {year:4d} | {invested:8,.0f} | {asset:7,.0f} | {profit:8,.0f} | {level:4s}")

def main():
    """主函数"""
    analyze_total_assets()
    calculate_asset_growth_trajectory()
    
    print(f"\n🎯 关键要点:")
    print(f"   • 20年积累约59万港元，达到中等投资者水平")
    print(f"   • 可提供月收入约2万港元的被动收入")
    print(f"   • 已具备在香港舒适退休的资产基础")
    print(f"   • 继续投资有望达到更高财富水平")

if __name__ == "__main__":
    main()
