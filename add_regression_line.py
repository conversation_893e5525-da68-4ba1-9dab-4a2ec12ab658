#!/usr/bin/env python3
# -*- coding: utf-8 -*-



计算并添加回归中线到数据库

作者: Cosmoon NG
日期: 2025年7月
"""

import sqlite3
import pandas as pd
import numpy as np
from scipy import stats

def add_regression_line():
    """为数据添加回归线"""
    print("添加回归线...")
    
    # 连接数据库
    conn = sqlite3.connect('finance.db')
    
    # 读取数据
    df = pd.read_sql("""
        SELECT date, close 
        FROM hkhsi50 
        ORDER BY date
    """, conn)
    
    # 添加序号
    df['i'] = range(1, len(df) + 1)
    
    # 计算回归参数
    slope, intercept, r_value, p_value, std_err = stats.linregress(
        df['i'], df['close']
    )
    
    # 计算回归线
    df['midprice'] = intercept + slope * df['i']
    
    # 更新数据库
    cursor = conn.cursor()
    
    # 检查midprice列是否存在
    cursor.execute("""
        SELECT COUNT(*) 
        FROM pragma_table_info('hkhsi50') 
        WHERE name='midprice'
    """)
    
    if cursor.fetchone()[0] == 0:
        cursor.execute("""
            ALTER TABLE hkhsi50 
            ADD COLUMN midprice REAL
        """)
    
    # 更新数据
    for _, row in df.iterrows():
        cursor.execute("""
            UPDATE hkhsi50 
            SET midprice = ? 
            WHERE date = ?
        """, (row['midprice'], row['date']))
    
    # 提交更改
    conn.commit()
    conn.close()
    
    print("✅ 回归线添加完成")
    print(f"数据点数：{len(df)}")
    print(f"斜率：{slope:.6f}")
    print(f"截距：{intercept:.6f}")
    print(f"R平方：{r_value**2:.6f}")

if __name__ == "__main__":
    print("HSI50回归线计算器")
    print("="*50)
    add_regression_line()
