/**
 * 交易止盈止损价位计算器
 * 
 * 功能：根据入场价格和交易方向（多头/空头），计算止盈和止损价位
 * 风险回报比：1:2 (止损0.06%, 止盈0.12%)
 * 
 * <AUTHOR> NG
 * @version 1.0
 * @date 2025-07-28
 */

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Scanner;

public class TradingCalculator {
    
    // 风险回报比参数 (1:2)
    private static final double STOP_LOSS_RATE = 0.0006;   // 止损 0.06%
    private static final double TAKE_PROFIT_RATE = 0.0012; // 止盈 0.12%
    
    // 交易方向枚举
    public enum TradeDirection {
        LONG("多头"),
        SHORT("空头");
        
        private final String description;
        
        TradeDirection(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * 交易结果类
     */
    public static class TradeResult {
        private final double entryPrice;
        private final TradeDirection direction;
        private final double takeProfitPrice;
        private final double stopLossPrice;
        private final double riskRewardRatio;
        
        public TradeResult(double entryPrice, TradeDirection direction, 
                          double takeProfitPrice, double stopLossPrice) {
            this.entryPrice = entryPrice;
            this.direction = direction;
            this.takeProfitPrice = takeProfitPrice;
            this.stopLossPrice = stopLossPrice;
            this.riskRewardRatio = TAKE_PROFIT_RATE / STOP_LOSS_RATE;
        }
        
        // Getters
        public double getEntryPrice() { return entryPrice; }
        public TradeDirection getDirection() { return direction; }
        public double getTakeProfitPrice() { return takeProfitPrice; }
        public double getStopLossPrice() { return stopLossPrice; }
        public double getRiskRewardRatio() { return riskRewardRatio; }
        
        @Override
        public String toString() {
            return String.format(
                "=== 交易计算结果 ===\n" +
                "交易方向: %s\n" +
                "入场价格: %.3f\n" +
                "止盈价格: %.3f\n" +
                "止损价格: %.3f\n" +
                "风险回报比: %.1f:1\n" +
                "止盈幅度: %.2f%%\n" +
                "止损幅度: %.2f%%",
                direction.getDescription(),
                entryPrice,
                takeProfitPrice,
                stopLossPrice,
                riskRewardRatio,
                TAKE_PROFIT_RATE * 100,
                STOP_LOSS_RATE * 100
            );
        }
    }
    
    /**
     * 计算多头止盈止损价位
     * 
     * @param entryPrice 入场价格
     * @return TradeResult 交易结果
     */
    public static TradeResult calculateLongPositions(double entryPrice) {
        // 多头计算公式:
        // 止盈 = entry_price * (1 + take_profit_rate)
        // 止损 = entry_price * (1 - stop_loss_rate)
        
        double takeProfitPrice = entryPrice * (1 + TAKE_PROFIT_RATE);
        double stopLossPrice = entryPrice * (1 - STOP_LOSS_RATE);
        
        // 保留3位小数
        takeProfitPrice = roundToThreeDecimals(takeProfitPrice);
        stopLossPrice = roundToThreeDecimals(stopLossPrice);
        
        return new TradeResult(entryPrice, TradeDirection.LONG, takeProfitPrice, stopLossPrice);
    }
    
    /**
     * 计算空头止盈止损价位
     * 
     * @param entryPrice 入场价格
     * @return TradeResult 交易结果
     */
    public static TradeResult calculateShortPositions(double entryPrice) {
        // 空头计算公式:
        // 止盈 = entry_price * (1 - take_profit_rate)
        // 止损 = entry_price * (1 + stop_loss_rate)
        
        double takeProfitPrice = entryPrice * (1 - TAKE_PROFIT_RATE);
        double stopLossPrice = entryPrice * (1 + STOP_LOSS_RATE);
        
        // 保留3位小数
        takeProfitPrice = roundToThreeDecimals(takeProfitPrice);
        stopLossPrice = roundToThreeDecimals(stopLossPrice);
        
        return new TradeResult(entryPrice, TradeDirection.SHORT, takeProfitPrice, stopLossPrice);
    }
    
    /**
     * 根据交易方向计算止盈止损
     * 
     * @param entryPrice 入场价格
     * @param direction 交易方向
     * @return TradeResult 交易结果
     */
    public static TradeResult calculate(double entryPrice, TradeDirection direction) {
        switch (direction) {
            case LONG:
                return calculateLongPositions(entryPrice);
            case SHORT:
                return calculateShortPositions(entryPrice);
            default:
                throw new IllegalArgumentException("不支持的交易方向: " + direction);
        }
    }
    
    /**
     * 保留3位小数
     */
    private static double roundToThreeDecimals(double value) {
        BigDecimal bd = new BigDecimal(value);
        bd = bd.setScale(3, RoundingMode.HALF_UP);
        return bd.doubleValue();
    }
    
    /**
     * 验证计算逻辑
     */
    public static void validateCalculations() {
        System.out.println("🔍 计算逻辑验证:");
        System.out.println("==================");
        
        // 测试多头
        double testPrice = 12.400;
        TradeResult longResult = calculateLongPositions(testPrice);
        System.out.println("多头测试 (入场价: " + testPrice + "):");
        System.out.println("  止盈: " + testPrice + " * (1 + 0.0012) = " + longResult.getTakeProfitPrice());
        System.out.println("  止损: " + testPrice + " * (1 - 0.0006) = " + longResult.getStopLossPrice());
        
        // 测试空头
        TradeResult shortResult = calculateShortPositions(testPrice);
        System.out.println("\n空头测试 (入场价: " + testPrice + "):");
        System.out.println("  止盈: " + testPrice + " * (1 - 0.0012) = " + shortResult.getTakeProfitPrice());
        System.out.println("  止损: " + testPrice + " * (1 + 0.0006) = " + shortResult.getStopLossPrice());
        
        System.out.println("\n✅ 验证完成！");
    }
    
    /**
     * 主程序
     */
    public static void main(String[] args) {
        Scanner scanner = new Scanner(System.in);
        
        System.out.println("🎯 交易止盈止损价位计算器");
        System.out.println("========================");
        System.out.println("风险回报比: 1:2 (止损0.06%, 止盈0.12%)");
        System.out.println();
        
        // 先运行验证
        validateCalculations();
        System.out.println();
        
        while (true) {
            try {
                // 输入入场价格
                System.out.print("请输入入场价格 (输入0退出): ");
                double entryPrice = scanner.nextDouble();
                
                if (entryPrice == 0) {
                    System.out.println("程序退出。");
                    break;
                }
                
                if (entryPrice <= 0) {
                    System.out.println("❌ 价格必须大于0，请重新输入。");
                    continue;
                }
                
                // 输入交易方向
                System.out.print("请选择交易方向 (1=多头, 2=空头): ");
                int directionChoice = scanner.nextInt();
                
                TradeDirection direction;
                switch (directionChoice) {
                    case 1:
                        direction = TradeDirection.LONG;
                        break;
                    case 2:
                        direction = TradeDirection.SHORT;
                        break;
                    default:
                        System.out.println("❌ 无效选择，请输入1或2。");
                        continue;
                }
                
                // 计算结果
                TradeResult result = calculate(entryPrice, direction);
                
                // 显示结果
                System.out.println();
                System.out.println(result);
                System.out.println();
                
            } catch (Exception e) {
                System.out.println("❌ 输入错误，请重新输入。");
                scanner.nextLine(); // 清除错误输入
            }
        }
        
        scanner.close();
    }
}
