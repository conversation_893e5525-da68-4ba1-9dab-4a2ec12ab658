import pandas as pd
from datetime import datetime

# 修正后的数据
data = [
    ['2025-07-22', '空仓', '无', 12.140, 0.000, 0, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 10000.000, 10000.000, 0.500, 0.297, -0.203, 0.459, 0.473, -0.060, 2, '中风险', '空仓观望，信号强度不足', 52.540, 31.250, '⚠️', '空仓', '无', 0, 0.000, 0.000, 0.000, 0.000, 0.000, '无', 0.000],
    ['2025-07-23', '开仓', '空头', 12.180, 12.180, 656, 7990.080, 7.990, 11.938, 12.363, 7998.070, 12.180, 7990.080, 0.000, 0.000, 0.000, 0.000, 0.000, 1994.930, 9984.010, 0.207, 0.321, -0.053, 0.459, 0.473, -0.060, 1, '低风险', '试探性开空仓', 52.540, 32.110, '📉', '持仓中', '空头', 1, 79.90, 0.000, 2.000, 1.500, 1.33, '重仓', 79.90],
    ['2025-07-24', '平仓', '平空', 12.220, 12.180, 0, 8016.320, 8.016, 0.000, 0.000, 8008.304, 0.000, 0.000, 0.000, -34.066, -34.066, -0.426, -0.341, 9968.238, 9968.238, 0.379, 0.313, -0.127, 0.456, 0.000, 0.000, 2, '高风险', '止损平仓', 45.100, 31.310, '🔴', '空仓', '无', 0, 0.000, -0.426, 0.000, 0.000, 0.000, '无', 0.000],
    ['2025-07-25', '空仓', '无', 12.220, 0.000, 0, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, 0.000, -34.066, -34.066, 0.000, -0.341, 9968.238, 9968.238, 0.379, 0.313, -0.127, 0.456, 0.000, 0.000, 2, '中风险', '空仓观望', 45.100, 31.310, '⚠️', '空仓', '无', 0, 0.000, 0.000, 0.000, 0.000, 0.000, '无', 0.000]
]

columns = ['交易日期', '交易类型', '交易方向', '交易价格', '入场价格', '持仓数量', '交易金额', '手续费', '止盈价', '止损价', '净交易额', '持仓成本', '当前市值', '浮动盈亏', '实现盈亏', '累计盈亏', '收益率', '累计收益率', '账户余额', '总资产', 'Y值', 'X值', 'E值', 'Full_Y', 'MoneyFlowRatio', 'MyE', '信号强度', '风险等级', '备注', 'RSI', 'MFI', '信号图标', '持仓状态', '持仓方向', '持仓天数', '持仓比例%', '盈亏比例%', '止盈距离%', '止损距离%', '风险收益比', '持仓强度', '资金利用率%']

df = pd.DataFrame(data, columns=columns)

# 备份原文件
import os
import shutil
original_file = "交易记录追踪0023HK.xlsx"
if os.path.exists(original_file):
    backup_file = f"交易记录追踪0023HK_备份_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
    shutil.copy2(original_file, backup_file)
    print(f"原文件已备份: {backup_file}")

# 写入新数据
df.to_excel(original_file, index=False)
print(f"修正数据已写入: {original_file}")
print(f"记录数: {len(df)}")
print(f"初始资金: {df['总资产'].iloc[0]:,.2f}")
print(f"最终资金: {df['总资产'].iloc[-1]:,.2f}")
print(f"总盈亏: {df['累计盈亏'].iloc[-1]:+,.2f}")
print("Excel修正完成！")
