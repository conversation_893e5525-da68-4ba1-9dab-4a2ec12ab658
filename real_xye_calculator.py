#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import yfinance as yf
import pandas as pd
import numpy as np
from datetime import datetime

def get_real_xye_values():
    """获取0023.HK真实的XYE指标值"""
    
    print("获取0023.HK真实XYE指标值")
    print("=" * 50)
    
    try:
        # 获取0023.HK足够的历史数据
        ticker = yf.Ticker("0023.HK")
        
        # 获取更长期数据确保计算准确
        hist = ticker.history(period="3mo")  # 3个月数据
        
        if hist.empty:
            print("无法获取0023.HK数据")
            return None
        
        print(f"获取到 {len(hist)} 天的历史数据")
        print(f"数据范围: {hist.index[0].date()} 至 {hist.index[-1].date()}")
        
        # 获取今日数据
        today_data = hist.iloc[-1]
        print(f"今日收盘价: {today_data['Close']:.2f} 港元")
        print(f"今日成交量: {today_data['Volume']:,.0f}")
        
        # 1. 计算真实Y值 (价格在20日区间的位置)
        window = 20
        if len(hist) >= window:
            # 计算20日最高最低
            high_20 = hist['High'].rolling(window).max().iloc[-1]
            low_20 = hist['Low'].rolling(window).min().iloc[-1]
            
            # Y值公式
            if high_20 != low_20:
                y_value = (today_data['Close'] - low_20) / (high_20 - low_20)
                y_value = max(0, min(1, y_value))  # 限制在0-1之间
            else:
                y_value = 0.5
            
            print(f"20日最高价: {high_20:.2f} 港元")
            print(f"20日最低价: {low_20:.2f} 港元")
            print(f"Y值计算: ({today_data['Close']:.2f} - {low_20:.2f}) / ({high_20:.2f} - {low_20:.2f}) = {y_value:.4f}")
        else:
            y_value = 0.5
            print("数据不足20天，Y值使用默认值0.5")
        
        # 2. 计算真实X值 (基于MFI的资金流强度)
        period = 14
        if len(hist) >= period:
            # 计算典型价格
            hist['typical_price'] = (hist['High'] + hist['Low'] + hist['Close']) / 3
            
            # 计算资金流
            hist['money_flow'] = hist['typical_price'] * hist['Volume']
            
            # 计算价格变化
            hist['price_change'] = hist['typical_price'].diff()
            
            # 正负资金流
            hist['positive_mf'] = np.where(hist['price_change'] > 0, hist['money_flow'], 0)
            hist['negative_mf'] = np.where(hist['price_change'] < 0, hist['money_flow'], 0)
            
            # 14日资金流总和
            positive_mf_14 = hist['positive_mf'].rolling(period).sum().iloc[-1]
            negative_mf_14 = hist['negative_mf'].rolling(period).sum().iloc[-1]
            
            # 计算MFI
            if negative_mf_14 > 0:
                money_flow_ratio = positive_mf_14 / negative_mf_14
                mfi = 100 - (100 / (1 + money_flow_ratio))
            else:
                mfi = 100  # 如果没有负资金流，MFI为100
            
            # X值 (MFI归一化)
            x_value = mfi / 100
            
            print(f"14日正资金流: {positive_mf_14:,.0f}")
            print(f"14日负资金流: {negative_mf_14:,.0f}")
            print(f"MFI: {mfi:.2f}")
            print(f"X值: {mfi:.2f} / 100 = {x_value:.4f}")
        else:
            x_value = 0.5
            mfi = 50
            print("数据不足14天，X值使用默认值0.5")
        
        # 3. 计算真实E值 (Cosmoon公式)
        e_value = (8 * x_value - 3) * y_value - 3 * x_value + 1
        
        print(f"E值计算: (8 × {x_value:.4f} - 3) × {y_value:.4f} - 3 × {x_value:.4f} + 1")
        print(f"E值计算: ({8*x_value:.4f} - 3) × {y_value:.4f} - {3*x_value:.4f} + 1")
        print(f"E值计算: {8*x_value-3:.4f} × {y_value:.4f} - {3*x_value:.4f} + 1")
        print(f"E值计算: {(8*x_value-3)*y_value:.4f} - {3*x_value:.4f} + 1")
        print(f"E值: {e_value:.4f}")
        
        return {
            'close_price': today_data['Close'],
            'volume': today_data['Volume'],
            'high_20': high_20 if len(hist) >= 20 else today_data['High'],
            'low_20': low_20 if len(hist) >= 20 else today_data['Low'],
            'mfi': mfi,
            'y_value': y_value,
            'x_value': x_value,
            'e_value': e_value,
            'date': hist.index[-1].date()
        }
        
    except Exception as e:
        print(f"计算失败: {e}")
        return None

def update_excel_with_real_values():
    """用真实XYE值更新Excel文件"""
    
    # 获取真实XYE值
    real_data = get_real_xye_values()
    if real_data is None:
        print("无法获取真实数据")
        return
    
    try:
        # 读取现有Excel文件
        df = pd.read_excel('交易记录追踪0023HK.xlsx')
        
        if len(df) > 0:
            # 更新最新记录的真实XYE值
            df.loc[df.index[-1], 'Y值'] = real_data['y_value']
            df.loc[df.index[-1], 'X值'] = real_data['x_value']
            df.loc[df.index[-1], 'E值'] = real_data['e_value']
            df.loc[df.index[-1], '交易价格'] = real_data['close_price']
            df.loc[df.index[-1], '备注'] = f"真实市场数据收盘价{real_data['close_price']:.2f}港元MFI{real_data['mfi']:.1f}"
            
            # 保存更新后的文件
            df.to_excel('交易记录追踪0023HK.xlsx', index=False)
            
            print()
            print("✅ Excel文件已更新为真实XYE值")
            print("=" * 50)
            print("更新后的真实记录:")
            latest = df.iloc[-1]
            print(f"交易日期: {latest['交易日期']}")
            print(f"收盘价格: {latest['交易价格']:.2f} 港元")
            print(f"真实Y值: {latest['Y值']:.4f}")
            print(f"真实X值: {latest['X值']:.4f}")
            print(f"真实E值: {latest['E值']:.4f}")
            print(f"备注: {latest['备注']}")
            
            # 生成交易信号
            y = latest['Y值']
            x = latest['X值']
            e = latest['E值']
            
            print()
            print("基于真实XYE值的交易信号:")
            if e > 0 and x > 0.45 and y > 0.45:
                print("🟢 多头信号: 强烈买入")
            elif (y < 0.3 or x < 0.3 or 
                  (x > 0.45 and y < 0.35) or
                  (x < 0.45 and y > 0.35)):
                print("🔴 空头信号: 强烈卖出")
            else:
                print("⚪ 观望信号: 等待机会")
        
        else:
            print("Excel文件为空")
            
    except Exception as e:
        print(f"更新Excel失败: {e}")

if __name__ == "__main__":
    update_excel_with_real_values()
