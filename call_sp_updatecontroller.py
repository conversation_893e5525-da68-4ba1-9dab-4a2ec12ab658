#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调用存储过程 sp_updatecontroller
===============================

分析hkhsi50表的控股商控制情况：
1. 添加控股商字段
2. 根据收市价vs中值分类控股商控制
3. 计算控制系数K值
4. 分析控制模式

作者: Cosmoon NG
日期: 2025年7月
"""

import pymysql
import pandas as pd
from datetime import datetime

# 数据库配置
db_config = {
    'host': 'localhost',
    'user': 'root',
    'password': '12345678',
    'database': 'finance',
    'charset': 'utf8mb4'
}

def check_required_columns():
    """检查必需的列是否存在"""
    print("🔍 检查hkhsi50表结构...")
    
    try:
        conn = pymysql.connect(**db_config)
        cursor = conn.cursor()
        
        # 检查表结构
        cursor.execute("DESCRIBE hkhsi50")
        columns = cursor.fetchall()
        
        column_names = [col[0] for col in columns]
        
        print(f"📋 当前表列:")
        for col in column_names:
            print(f"   • {col}")
        
        # 检查必需的列
        required_columns = ['收市', '中值']
        missing_columns = []
        
        # 检查收市列（可能是close）
        if '收市' not in column_names:
            if 'close' in column_names:
                print("ℹ️  找到'close'列，将作为收市价使用")
            else:
                missing_columns.append('收市/close')
        
        # 检查中值列
        if '中值' not in column_names:
            missing_columns.append('中值')
        
        if missing_columns:
            print(f"❌ 缺少必需列: {missing_columns}")
            return False, missing_columns
        else:
            print("✅ 所有必需列都存在")
            return True, []
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 检查表结构失败: {e}")
        return False, []

def add_missing_columns():
    """添加缺失的列"""
    print("\n🔧 添加缺失的列...")
    
    try:
        conn = pymysql.connect(**db_config)
        cursor = conn.cursor()
        
        # 检查是否需要添加收市列
        cursor.execute("SELECT COUNT(*) FROM information_schema.COLUMNS WHERE TABLE_SCHEMA='finance' AND TABLE_NAME='hkhsi50' AND COLUMN_NAME='收市'")
        has_close_cn = cursor.fetchone()[0] > 0
        
        if not has_close_cn:
            # 检查是否有close列
            cursor.execute("SELECT COUNT(*) FROM information_schema.COLUMNS WHERE TABLE_SCHEMA='finance' AND TABLE_NAME='hkhsi50' AND COLUMN_NAME='close'")
            has_close_en = cursor.fetchone()[0] > 0
            
            if has_close_en:
                # 添加收市列并复制close的值
                cursor.execute("ALTER TABLE hkhsi50 ADD COLUMN `收市` DECIMAL(15,4)")
                cursor.execute("UPDATE hkhsi50 SET `收市` = close")
                print("✅ 添加'收市'列并复制close数据")
            else:
                print("❌ 没有找到close列，无法创建收市列")
                return False
        
        # 检查是否需要添加中值列
        cursor.execute("SELECT COUNT(*) FROM information_schema.COLUMNS WHERE TABLE_SCHEMA='finance' AND TABLE_NAME='hkhsi50' AND COLUMN_NAME='中值'")
        has_median = cursor.fetchone()[0] > 0
        
        if not has_median:
            # 添加中值列
            cursor.execute("ALTER TABLE hkhsi50 ADD COLUMN `中值` DECIMAL(15,4)")
            
            # 计算中值（使用收市价的中位数）
            cursor.execute("SELECT COUNT(*) FROM hkhsi50")
            total_count = cursor.fetchone()[0]
            
            if total_count > 0:
                # 计算中位数
                cursor.execute("""
                    SELECT `收市` FROM hkhsi50 
                    ORDER BY `收市` 
                    LIMIT 1 OFFSET %s
                """, (total_count // 2,))
                
                median_value = cursor.fetchone()[0]
                
                # 更新所有记录的中值
                cursor.execute("UPDATE hkhsi50 SET `中值` = %s", (median_value,))
                
                print(f"✅ 添加'中值'列，中值设为: {median_value:.2f}")
            else:
                print("❌ 表中没有数据，无法计算中值")
                return False
        
        conn.commit()
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 添加列失败: {e}")
        return False

def call_sp_updatecontroller():
    """调用存储过程 sp_updatecontroller"""
    print("\n🚀 调用存储过程 sp_updatecontroller...")
    
    try:
        conn = pymysql.connect(**db_config)
        cursor = conn.cursor()
        
        # 调用存储过程（需要2个参数：IN tablename, OUT result_k）
        cursor.execute("CALL sp_updatecontroller('hkhsi50', @k_result)")

        # 获取OUT参数结果
        cursor.execute("SELECT @k_result AS k_value")
        result = cursor.fetchone()
        
        if result:
            k_value = float(result[0]) if result[0] is not None else 0
            print(f"✅ 存储过程执行成功")
            print(f"📊 控制系数K值: {k_value:.6f}")
            
            # 解释K值含义
            if k_value > 0.6:
                print(f"📈 市场解读: 控股商强势控制 (K={k_value:.3f})")
            elif k_value > 0.4:
                print(f"⚖️  市场解读: 控股商适度控制 (K={k_value:.3f})")
            else:
                print(f"📉 市场解读: 散户主导市场 (K={k_value:.3f})")
            
            return k_value
        else:
            print("❌ 未能获取K值结果")
            return None
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 调用存储过程失败: {e}")
        return None

def analyze_controller_distribution():
    """分析控股商分布"""
    print("\n📊 分析控股商控制分布...")
    
    try:
        conn = pymysql.connect(**db_config)
        
        # 获取控股商分布
        df = pd.read_sql("""
            SELECT 
                控股商,
                COUNT(*) as 天数,
                COUNT(*) * 100.0 / (SELECT COUNT(*) FROM hkhsi50) as 百分比,
                AVG(`收市`) as 平均收市价,
                AVG(`中值`) as 平均中值
            FROM hkhsi50 
            WHERE 控股商 IS NOT NULL
            GROUP BY 控股商
            ORDER BY 控股商
        """, conn)
        
        print(f"📋 控股商控制分布:")
        print("-" * 70)
        print(f"{'控股商':<8} {'含义':<15} {'天数':<8} {'百分比':<10} {'平均收市价':<12}")
        print("-" * 70)
        
        control_meanings = {
            0: '散户控制',
            1: '控股商控制', 
            3: '平衡状态'
        }
        
        for _, row in df.iterrows():
            control_type = int(row['控股商'])
            meaning = control_meanings.get(control_type, '未知')
            print(f"{control_type:<8} {meaning:<15} {row['天数']:<8.0f} {row['百分比']:<10.1f}% {row['平均收市价']:<12.2f}")
        
        # 获取最近的控股商状态
        recent_df = pd.read_sql("""
            SELECT date, `收市`, `中值`, 控股商
            FROM hkhsi50 
            WHERE 控股商 IS NOT NULL
            ORDER BY date DESC 
            LIMIT 10
        """, conn)
        
        print(f"\n📅 最近10天控股商状态:")
        print("-" * 60)
        print(f"{'日期':<12} {'收市价':<10} {'中值':<10} {'控股商':<8} {'状态':<10}")
        print("-" * 60)
        
        for _, row in recent_df.iterrows():
            control_type = int(row['控股商']) if row['控股商'] is not None else -1
            meaning = control_meanings.get(control_type, '未知')
            print(f"{row['date']:<12} {row['收市']:<10.2f} {row['中值']:<10.2f} {control_type:<8} {meaning:<10}")
        
        conn.close()
        return df
        
    except Exception as e:
        print(f"❌ 分析控股商分布失败: {e}")
        return None

def export_controller_analysis():
    """导出控股商分析结果"""
    print("\n💾 导出控股商分析结果...")
    
    try:
        conn = pymysql.connect(**db_config)
        
        # 获取完整数据
        df = pd.read_sql("""
            SELECT date, open, high, low, close, volume,
                   `收市`, `中值`, 控股商,
                   y_value, x_value, e_value
            FROM hkhsi50 
            WHERE 控股商 IS NOT NULL
            ORDER BY date DESC
            LIMIT 1000
        """, conn)
        
        # 保存到Excel
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"控股商分析结果_{timestamp}.xlsx"
        
        df.to_excel(filename, index=False)
        
        print(f"✅ 分析结果已导出到: {filename}")
        print(f"   • 导出记录数: {len(df)}")
        
        conn.close()
        return filename
        
    except Exception as e:
        print(f"❌ 导出失败: {e}")
        return None

def main():
    """主函数"""
    print("🎯 控股商控制分析工具")
    print("="*50)
    print("📋 功能:")
    print("   • 调用存储过程 sp_updatecontroller")
    print("   • 分析控股商vs散户控制")
    print("   • 计算控制系数K值")
    print("   • 生成分析报告")
    print("="*50)
    
    # 1. 检查必需的列
    has_columns, missing = check_required_columns()

    # 2. 添加缺失的列（总是尝试添加，因为可能需要更新）
    if not add_missing_columns():
        print("❌ 无法添加必需的列，退出")
        return
    
    # 3. 调用存储过程
    k_value = call_sp_updatecontroller()
    
    if k_value is None:
        print("❌ 存储过程调用失败，退出")
        return
    
    # 4. 分析控股商分布
    distribution = analyze_controller_distribution()
    
    # 5. 导出分析结果
    export_controller_analysis()
    
    print(f"\n🎉 控股商控制分析完成！")
    print(f"📊 关键发现:")
    print(f"   • 控制系数K值: {k_value:.6f}")
    
    if k_value > 0.5:
        print(f"   • 市场特征: 控股商主导")
        print(f"   • 策略建议: 适合趋势跟踪")
    else:
        print(f"   • 市场特征: 散户主导")
        print(f"   • 策略建议: 适合反向操作")

if __name__ == "__main__":
    main()
