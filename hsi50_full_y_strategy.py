#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
HSI50 Full_Y策略
===============
基于reference.py，使用Full_Y作为核心指标

特点：
1. 使用Full_Y作为主要趋势判断
2. 结合y_probability增强信号
3. 采用凯利公式优化仓位
4. 每月复利加入3000

基于Cosmoon NG的reference.py改进
"""

import mysql.connector
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import matplotlib.pyplot as plt
from scipy import stats
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

class HSI50FullYStrategy:
    def __init__(self):
        """初始化回测系统"""
        # 数据库配置
        self.db_config = {
            'host': 'localhost',
            'database': 'finance',
            'user': 'root',
            'password': '12345678',
            'charset': 'utf8mb4'
        }

        # 策略参数 (基于reference.py，调整为更实用)
        self.initial_capital = 30000      # 初始资金
        self.monthly_addition = 3000      # 每月追加资金
        self.take_profit_long = 0.08      # 多头止盈 8%
        self.stop_loss_long = 0.04        # 多头止损 4%
        self.take_profit_short = 0.06     # 空头止盈 6%
        self.stop_loss_short = 0.08       # 空头止损 8%
        self.kelly_fraction = 0.3         # 凯利公式分数 (保守)

        # Full_Y策略参数 (基于实际数据调整)
        self.full_y_oversold = 0.521      # Full_Y超卖阈值 (接近最小值)
        self.full_y_overbought = 0.526    # Full_Y超买阈值 (接近最大值)
        self.full_y_trend_threshold = 0.0005 # Full_Y趋势变化阈值

        # 状态变量
        self.position = 0                 # 当前持仓
        self.current_price = 0            # 当前持仓价格
        self.trades = []
        self.equity_curve = []

    def load_data(self):
        """从新数据库加载数据"""
        print("\n1. 加载HSI50数据 (使用Full_Y)...")
        try:
            connection = mysql.connector.connect(**self.db_config)

            # 加载数据，重点关注Full_Y
            query = """
                SELECT Date, Open, High, Low, Close, Volume,
                       ma_20, ma_60, y_probability,
                       new_midprice, new_controller, new_Full_Y
                FROM hkhsi50
                WHERE Date >= '2020-01-01'
                AND new_Full_Y IS NOT NULL
                ORDER BY Date ASC
            """

            self.df = pd.read_sql(query, connection)
            connection.close()

            # 数据预处理
            self.df['date'] = pd.to_datetime(self.df['Date'])
            self.df.rename(columns={
                'Open': 'open', 'High': 'high', 'Low': 'low',
                'Close': 'close', 'Volume': 'volume',
                'new_Full_Y': 'full_y'
            }, inplace=True)

            # 计算技术指标
            self.calculate_indicators()

            print(f"✓ 成功加载 {len(self.df)} 条数据")
            print(f"数据范围：{self.df['date'].min().date()} 至 {self.df['date'].max().date()}")
            print(f"Full_Y范围：{self.df['full_y'].min():.4f} ~ {self.df['full_y'].max():.4f}")

            return True

        except Exception as e:
            print(f"❌ 数据加载失败: {e}")
            return False

    def calculate_indicators(self):
        """计算技术指标"""
        print("   计算Full_Y相关指标...")

        # Full_Y的变化率
        self.df['full_y_change'] = self.df['full_y'].diff()
        self.df['full_y_change_pct'] = self.df['full_y'].pct_change()

        # Full_Y的移动平均
        self.df['full_y_ma5'] = self.df['full_y'].rolling(5).mean()
        self.df['full_y_ma20'] = self.df['full_y'].rolling(20).mean()

        # Full_Y相对于移动平均的位置
        self.df['full_y_vs_ma5'] = self.df['full_y'] - self.df['full_y_ma5']
        self.df['full_y_vs_ma20'] = self.df['full_y'] - self.df['full_y_ma20']

        # Full_Y趋势强度
        self.df['full_y_trend'] = self.df['full_y_ma5'] - self.df['full_y_ma20']

        # 价格偏离 (相对于new_midprice)
        self.df['price_deviation'] = (self.df['close'] - self.df['new_midprice']) / self.df['new_midprice']

        # 价格相对于移动平均线
        self.df['price_vs_ma20'] = self.df['close'] / self.df['ma_20']
        self.df['price_vs_ma60'] = self.df['close'] / self.df['ma_60']

        # 波动率
        self.df['volatility'] = self.df['close'].pct_change().rolling(window=20).std()

        # RSI
        delta = self.df['close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
        rs = gain / loss
        self.df['rsi'] = 100 - (100 / (1 + rs))

        print("✓ 技术指标计算完成")

    def calculate_kelly(self, win_rate, profit_ratio, loss_ratio):
        """计算凯利公式建议仓位"""
        if win_rate <= 0 or profit_ratio <= 0:
            return 0
        q = 1 - win_rate
        kelly = (win_rate/q * profit_ratio - 1) / profit_ratio
        kelly *= self.kelly_fraction
        return max(0, min(kelly, 0.4))  # 最大仓位限制在40%

    def get_position_size(self, price, capital, volatility):
        """计算仓位大小"""
        # 基于历史数据计算胜率和盈亏比
        if len(self.trades) < 5:
            win_rate = 0.6  # 初始假设胜率
        else:
            trades_df = pd.DataFrame(self.trades)
            exit_trades = trades_df[trades_df['type'].str.contains('exit')]
            if len(exit_trades) > 0:
                profit_trades = exit_trades[exit_trades.get('profit', 0) > 0]
                win_rate = len(profit_trades) / len(exit_trades)
            else:
                win_rate = 0.6

        # 根据持仓方向设置盈亏比
        if self.position == 1:  # 多头
            profit_ratio = self.take_profit_long
            loss_ratio = self.stop_loss_long
        else:  # 空头
            profit_ratio = self.take_profit_short
            loss_ratio = self.stop_loss_short

        # 使用凯利公式计算仓位比例
        kelly = self.calculate_kelly(win_rate, profit_ratio, loss_ratio)

        # 根据波动率调整仓位
        volatility_factor = 1 - min(volatility * 50, 0.5)

        return capital * kelly * volatility_factor

    def add_monthly_capital(self, date, capital):
        """每月增加资金"""
        last_month = getattr(self, 'last_month', None)
        current_month = date.replace(day=1)

        if last_month is None or current_month > last_month:
            self.last_month = current_month
            return capital + self.monthly_addition

        return capital

    def check_entry_conditions(self, row):
        """检查入场条件 (基于Full_Y)"""
        full_y = row['full_y']
        full_y_change = row['full_y_change']
        full_y_trend = row['full_y_trend']
        y_prob = row['y_probability']
        price_deviation = row['price_deviation']
        rsi = row['rsi']

        # 基本数据检查
        if pd.isna(full_y) or pd.isna(y_prob) or pd.isna(rsi):
            return 0

        # 多头条件：Full_Y处于低位且开始上升
        long_condition = (
            full_y < self.full_y_oversold and  # Full_Y超卖
            full_y_change > 0.001 and  # Full_Y开始上升
            (full_y_trend > 0 or full_y_trend > -0.01) and  # 趋势不太差
            y_prob > 0.48 and  # y_probability不太悲观
            price_deviation < 0.03 and  # 价格不太高
            rsi < 60  # RSI不超买
        )

        # 空头条件：Full_Y处于高位且开始下降
        short_condition = (
            full_y > self.full_y_overbought and  # Full_Y超买
            full_y_change < -0.001 and  # Full_Y开始下降
            (full_y_trend < 0 or full_y_trend < 0.01) and  # 趋势不太好
            y_prob < 0.52 and  # y_probability不太乐观
            price_deviation > -0.03 and  # 价格不太低
            rsi > 40  # RSI不超卖
        )

        if long_condition:
            return 1
        elif short_condition:
            return -1
        else:
            return 0

    def run_backtest(self):
        """运行回测"""
        print("\n2. 开始Full_Y策略回测...")

        capital = self.initial_capital
        last_trade_date = None
        min_trade_interval = timedelta(days=3)  # 缩短交易间隔

        for i in range(20, len(self.df)):  # 从第20天开始
            row = self.df.iloc[i]
            date = pd.to_datetime(row['date'])

            # 每月增加资金
            capital = self.add_monthly_capital(date, capital)

            # 记录权益
            current_equity = capital
            if self.position != 0:
                # 计算当前持仓的浮动盈亏
                if self.position == 1:  # 多头
                    unrealized_pnl = (row['close'] - self.current_price) / self.current_price * capital * 0.3
                else:  # 空头
                    unrealized_pnl = (self.current_price - row['close']) / self.current_price * capital * 0.3
                current_equity += unrealized_pnl

            self.equity_curve.append({
                'date': date,
                'equity': current_equity,
                'position': self.position,
                'full_y': row['full_y']
            })

            # 检查交易间隔
            if last_trade_date and (date - last_trade_date) < min_trade_interval:
                continue

            # 检查止盈止损
            if self.position != 0:
                if self.position == 1:  # 多头
                    profit_ratio = (row['close'] - self.current_price) / self.current_price

                    if profit_ratio >= self.take_profit_long:  # 止盈
                        exit_price = self.current_price * (1 + self.take_profit_long)
                        profit = profit_ratio * capital * 0.3  # 假设30%仓位
                        capital += profit
                        self.position = 0
                        self.trades.append({
                            'date': date,
                            'type': 'long_exit_tp',
                            'price': exit_price,
                            'profit': profit,
                            'capital': capital,
                            'full_y': row['full_y']
                        })
                    elif profit_ratio <= -self.stop_loss_long:  # 止损
                        exit_price = self.current_price * (1 - self.stop_loss_long)
                        loss = profit_ratio * capital * 0.3
                        capital += loss
                        self.position = 0
                        self.trades.append({
                            'date': date,
                            'type': 'long_exit_sl',
                            'price': exit_price,
                            'profit': loss,
                            'capital': capital,
                            'full_y': row['full_y']
                        })

                elif self.position == -1:  # 空头
                    profit_ratio = (self.current_price - row['close']) / self.current_price

                    if profit_ratio >= self.take_profit_short:  # 止盈
                        exit_price = self.current_price * (1 - self.take_profit_short)
                        profit = profit_ratio * capital * 0.3
                        capital += profit
                        self.position = 0
                        self.trades.append({
                            'date': date,
                            'type': 'short_exit_tp',
                            'price': exit_price,
                            'profit': profit,
                            'capital': capital,
                            'full_y': row['full_y']
                        })
                    elif profit_ratio <= -self.stop_loss_short:  # 止损
                        exit_price = self.current_price * (1 + self.stop_loss_short)
                        loss = profit_ratio * capital * 0.3
                        capital += loss
                        self.position = 0
                        self.trades.append({
                            'date': date,
                            'type': 'short_exit_sl',
                            'price': exit_price,
                            'profit': loss,
                            'capital': capital,
                            'full_y': row['full_y']
                        })

            # 检查开仓条件
            if self.position == 0:
                position_signal = self.check_entry_conditions(row)

                if position_signal != 0:
                    position_size = self.get_position_size(row['close'], capital, row['volatility'])

                    if position_size > 500:  # 最小交易金额限制
                        self.position = position_signal
                        self.current_price = row['close']
                        last_trade_date = date

                        self.trades.append({
                            'date': date,
                            'type': 'long_entry' if position_signal == 1 else 'short_entry',
                            'price': self.current_price,
                            'size': position_size,
                            'capital': capital,
                            'full_y': row['full_y'],
                            'y_probability': row['y_probability']
                        })

        self.final_capital = capital
        print(f"✓ 回测完成！最终资金：{self.final_capital:,.2f}")

    def analyze_results(self):
        """分析回测结果"""
        print("\n=== Full_Y策略回测分析 ===")

        trades_df = pd.DataFrame(self.trades)
        if len(trades_df) == 0:
            print("没有产生任何交易")
            return

        # 基本统计
        entry_trades = trades_df[trades_df['type'].str.contains('entry')]
        exit_trades = trades_df[trades_df['type'].str.contains('exit')]

        total_trades = len(entry_trades)
        winning_trades = len(exit_trades[exit_trades['profit'] > 0]) if 'profit' in exit_trades.columns else 0

        print(f"\n📊 交易统计：")
        print(f"总交易次数：{total_trades}")
        print(f"盈利交易：{winning_trades}")
        print(f"亏损交易：{total_trades - winning_trades}")
        if total_trades > 0:
            print(f"胜率：{winning_trades/total_trades*100:.2f}%")

        # 盈亏分析
        if 'profit' in exit_trades.columns and len(exit_trades) > 0:
            total_profit = exit_trades['profit'].sum()
            profit_trades = exit_trades[exit_trades['profit'] > 0]
            loss_trades = exit_trades[exit_trades['profit'] < 0]

            print(f"总交易盈亏：{total_profit:,.2f}")

            if len(profit_trades) > 0:
                print(f"平均盈利：{profit_trades['profit'].mean():.2f}")
                print(f"最大盈利：{profit_trades['profit'].max():.2f}")

            if len(loss_trades) > 0:
                print(f"平均亏损：{loss_trades['profit'].mean():.2f}")
                print(f"最大亏损：{loss_trades['profit'].min():.2f}")

        # 收益率分析
        initial_equity = self.initial_capital
        final_equity = self.final_capital
        total_days = (self.df['date'].max() - self.df['date'].min()).days
        total_years = total_days / 365

        # 计算总投入
        months = total_days / 30
        total_invested = initial_equity + months * self.monthly_addition

        net_profit = final_equity - total_invested
        total_return = net_profit / total_invested if total_invested > 0 else 0
        annual_return = (final_equity / total_invested) ** (1/total_years) - 1 if total_years > 0 else 0

        print(f"\n💰 收益统计：")
        print(f"初始资金：{initial_equity:,.2f}")
        print(f"总投入：{total_invested:,.2f}")
        print(f"最终资金：{final_equity:,.2f}")
        print(f"净收益：{net_profit:,.2f}")
        print(f"总收益率：{total_return*100:.2f}%")
        print(f"年化收益率：{annual_return*100:.2f}%")

        # Full_Y分析
        if 'full_y' in entry_trades.columns:
            print(f"\n🎯 Full_Y分析：")
            avg_entry_full_y = entry_trades['full_y'].mean()
            print(f"平均入场Full_Y：{avg_entry_full_y:.4f}")

            long_entries = entry_trades[entry_trades['type'] == 'long_entry']
            short_entries = entry_trades[entry_trades['type'] == 'short_entry']

            if len(long_entries) > 0:
                print(f"多头入场平均Full_Y：{long_entries['full_y'].mean():.4f}")
            if len(short_entries) > 0:
                print(f"空头入场平均Full_Y：{short_entries['full_y'].mean():.4f}")

        # 与买入持有比较
        hsi_start = self.df['close'].iloc[20]
        hsi_end = self.df['close'].iloc[-1]
        buy_hold_return = (hsi_end - hsi_start) / hsi_start
        buy_hold_annual = (buy_hold_return + 1) ** (1/total_years) - 1

        print(f"\n📊 策略对比：")
        print(f"Full_Y策略年化收益：{annual_return*100:.2f}%")
        print(f"买入持有年化收益：{buy_hold_annual*100:.2f}%")
        print(f"超额收益：{(annual_return - buy_hold_annual)*100:+.2f}%")

        # 保存结果
        try:
            equity_df = pd.DataFrame(self.equity_curve)

            # 绘制权益曲线和Full_Y
            fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(15, 10))

            # 权益曲线
            ax1.plot(equity_df['date'], equity_df['equity'])
            ax1.set_title('Full_Y策略权益曲线')
            ax1.set_ylabel('资金')
            ax1.grid(True)

            # Full_Y曲线
            ax2.plot(equity_df['date'], equity_df['full_y'])
            ax2.axhline(y=self.full_y_oversold, color='g', linestyle='--', label='超卖线')
            ax2.axhline(y=self.full_y_overbought, color='r', linestyle='--', label='超买线')
            ax2.set_title('Full_Y指标')
            ax2.set_ylabel('Full_Y')
            ax2.set_xlabel('日期')
            ax2.legend()
            ax2.grid(True)

            plt.tight_layout()
            plt.savefig('full_y_strategy_analysis.png')
            plt.close()

            if len(trades_df) > 0:
                trades_df.to_excel('full_y_strategy_trades.xlsx', index=False)
                print("\n📄 交易记录已保存到 full_y_strategy_trades.xlsx")

            print("📈 分析图表已保存到 full_y_strategy_analysis.png")

        except Exception as e:
            print(f"保存文件时出错: {e}")

def main():
    """主函数"""
    print("🎯 HSI50 Full_Y策略")
    print("基于reference.py + Full_Y指标")
    print("="*50)

    try:
        strategy = HSI50FullYStrategy()

        if not strategy.load_data():
            return

        strategy.run_backtest()
        strategy.analyze_results()

        print("\n🎉 Full_Y策略测试完成！")

    except Exception as e:
        print(f"❌ 运行失败: {e}")

if __name__ == "__main__":
    main()
