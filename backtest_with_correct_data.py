#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
使用正确的profit和loss价格重新回测test表
====================================

使用test表中正确的数据进行回测：
- profit价格 = 止盈价格
- loss价格 = 止损价格
- 交易方向已正确设置
- 毛利润已正确计算

回测逻辑：
- 如果平仓价格达到profit价格 → 止盈
- 如果平仓价格达到loss价格 → 止损
- 否则 → 到期平仓

作者: Cosmoon NG
日期: 2025年7月
"""

import pymysql
import pandas as pd

class CorrectBacktester:
    def __init__(self):
        """初始化正确的回测器"""
        self.db_config = {
            'host': 'localhost',
            'port': 3306,
            'database': 'finance',
            'user': 'root',
            'password': '12345678',
            'charset': 'utf8mb4'
        }
        self.connection = None
        self.initial_capital = 30000
        
    def connect_database(self):
        """连接数据库"""
        try:
            self.connection = pymysql.connect(**self.db_config)
            print(f"✅ 成功连接MySQL数据库: {self.db_config['host']}")
            return True
        except Exception as e:
            print(f"❌ 连接数据库失败: {e}")
            return False
    
    def run_correct_backtest(self):
        """使用正确数据运行回测"""
        try:
            cursor = self.connection.cursor()
            
            print("📊 使用正确的profit和loss价格重新回测")
            print("="*80)
            print("🔍 回测逻辑:")
            print("   • profit价格 = 止盈价格 (盈利目标)")
            print("   • loss价格 = 止损价格 (亏损限制)")
            print("   • 观望 = 不交易")
            print("="*80)
            
            # 获取所有记录
            cursor.execute("""
                SELECT 交易序号, 开仓日期, close, 平仓价格, 交易方向, 交易股数,
                       `profit价格`, `loss价格`, `毛利润`, 净利润,
                       CASE 
                           WHEN `控制系数` > 0.43 AND `资金流比例` > 0.43 THEN '高值盈利区'
                           WHEN `控制系数` > 0.333 AND `控制系数` < 0.4 THEN '控股商控制区'
                           WHEN (`控制系数` < 0.25 OR `资金流比例` < 0.25) AND NOT (`控制系数` > 0.333 AND `控制系数` < 0.4) THEN '强亏损区'
                           ELSE '其他区域'
                       END AS 策略区域
                FROM test 
                ORDER BY 交易序号
            """)
            
            records = cursor.fetchall()
            
            print(f"\n📊 回测详细结果 (前30条记录):")
            print("-" * 140)
            print(f"{'序号':<4} {'策略区域':<12} {'方向':<6} {'开仓价':<8} {'平仓价':<8} "
                  f"{'profit价格':<10} {'loss价格':<9} {'回测结果':<10} {'回测盈亏':<10}")
            print("-" * 140)
            
            current_capital = self.initial_capital
            total_trades = 0
            observe_count = 0
            take_profit_count = 0
            stop_loss_count = 0
            normal_close_count = 0
            
            total_backtest_profit = 0
            total_gross_profit = 0
            total_net_profit = 0
            
            for i, record in enumerate(records):
                (trade_id, open_date, open_price, close_price, direction, shares,
                 profit_price, loss_price, gross_profit, net_profit, zone) = record
                
                # 回测逻辑
                if direction == '观望':
                    backtest_result = '观望'
                    backtest_profit = 0
                    observe_count += 1
                else:
                    total_trades += 1
                    
                    if direction == '买涨':
                        # 买涨策略
                        if float(close_price) >= float(profit_price):
                            backtest_result = '止盈'
                            backtest_profit = (float(profit_price) - float(open_price)) * shares
                            take_profit_count += 1
                        elif float(close_price) <= float(loss_price):
                            backtest_result = '止损'
                            backtest_profit = (float(loss_price) - float(open_price)) * shares
                            stop_loss_count += 1
                        else:
                            backtest_result = '到期平仓'
                            backtest_profit = (float(close_price) - float(open_price)) * shares
                            normal_close_count += 1

                    else:  # 买跌策略
                        if float(close_price) <= float(profit_price):
                            backtest_result = '止盈'
                            backtest_profit = (float(open_price) - float(profit_price)) * shares
                            take_profit_count += 1
                        elif float(close_price) >= float(loss_price):
                            backtest_result = '止损'
                            backtest_profit = (float(open_price) - float(loss_price)) * shares
                            stop_loss_count += 1
                        else:
                            backtest_result = '到期平仓'
                            backtest_profit = (float(open_price) - float(close_price)) * shares
                            normal_close_count += 1
                
                current_capital += backtest_profit
                total_backtest_profit += backtest_profit
                total_gross_profit += gross_profit
                total_net_profit += net_profit
                
                # 显示前30条记录
                if i < 30:
                    print(f"{trade_id:<4} {zone:<12} {direction:<6} {float(open_price):<8.2f} {float(close_price):<8.2f} "
                          f"{float(profit_price):<10.2f} {float(loss_price):<9.2f} {backtest_result:<10} {backtest_profit:<10.0f}")
            
            print("\n" + "="*100)
            
            # 回测结果统计
            print(f"📈 回测结果统计:")
            print(f"   • 初始资金: {self.initial_capital:,}港币")
            print(f"   • 最终资金: {current_capital:,.0f}港币")
            print(f"   • 回测总盈亏: {total_backtest_profit:+,.0f}港币")
            print(f"   • 回测收益率: {(current_capital/self.initial_capital-1)*100:+.2f}%")
            
            print(f"\n📊 交易统计:")
            print(f"   • 总记录数: {len(records)}")
            print(f"   • 实际交易: {total_trades}")
            print(f"   • 观望次数: {observe_count}")
            print(f"   • 止盈次数: {take_profit_count} ({take_profit_count/total_trades*100:.1f}%)")
            print(f"   • 止损次数: {stop_loss_count} ({stop_loss_count/total_trades*100:.1f}%)")
            print(f"   • 到期平仓: {normal_close_count} ({normal_close_count/total_trades*100:.1f}%)")
            
            if total_trades > 0:
                win_rate = take_profit_count / total_trades
                print(f"   • 胜率: {win_rate*100:.1f}%")
            
            # 对比原始数据
            print(f"\n🔍 与原始数据对比:")
            print(f"   • 回测毛利润: {total_backtest_profit:+,.0f}港币")
            print(f"   • 原始毛利润: {total_gross_profit:+,.0f}港币")
            print(f"   • 原始净利润: {total_net_profit:+,.0f}港币")
            print(f"   • 差异(回测-毛利润): {total_backtest_profit-total_gross_profit:+,.0f}港币")
            
            return True
            
        except Exception as e:
            print(f"❌ 回测失败: {e}")
            return False
    
    def analyze_by_strategy_zones(self):
        """按策略区域分析回测结果"""
        try:
            cursor = self.connection.cursor()
            
            print(f"\n📊 按策略区域分析回测结果:")
            print("="*120)
            
            zones = [
                ('高值盈利区', '买涨'),
                ('强亏损区', '买跌'),
                ('其他区域', '买跌'),
                ('控股商控制区', '观望')
            ]
            
            for zone_name, expected_direction in zones:
                # 构建查询条件
                if zone_name == '高值盈利区':
                    where_clause = "`控制系数` > 0.43 AND `资金流比例` > 0.43"
                elif zone_name == '强亏损区':
                    where_clause = "(`控制系数` < 0.25 OR `资金流比例` < 0.25) AND NOT (`控制系数` > 0.333 AND `控制系数` < 0.4)"
                elif zone_name == '其他区域':
                    where_clause = """NOT (`控制系数` > 0.43 AND `资金流比例` > 0.43) 
                                     AND NOT (`控制系数` > 0.333 AND `控制系数` < 0.4) 
                                     AND NOT (`控制系数` < 0.25 OR `资金流比例` < 0.25)"""
                else:  # 控股商控制区
                    where_clause = "`控制系数` > 0.333 AND `控制系数` < 0.4"
                
                cursor.execute(f"""
                    SELECT 交易序号, close, 平仓价格, 交易方向, 交易股数,
                           `profit价格`, `loss价格`, `毛利润`
                    FROM test 
                    WHERE {where_clause}
                    ORDER BY 交易序号
                """)
                
                zone_records = cursor.fetchall()
                
                if not zone_records:
                    print(f"\n🎯 {zone_name}: 无记录")
                    continue
                
                print(f"\n🎯 {zone_name} ({len(zone_records)}条记录):")
                
                zone_backtest_profit = 0
                zone_gross_profit = 0
                zone_take_profit = 0
                zone_stop_loss = 0
                zone_normal_close = 0
                zone_observe = 0
                
                for record in zone_records:
                    (trade_id, open_price, close_price, direction, shares,
                     profit_price, loss_price, gross_profit) = record
                    
                    if direction == '观望':
                        backtest_profit = 0
                        zone_observe += 1
                    else:
                        if direction == '买涨':
                            if float(close_price) >= float(profit_price):
                                backtest_profit = (float(profit_price) - float(open_price)) * shares
                                zone_take_profit += 1
                            elif float(close_price) <= float(loss_price):
                                backtest_profit = (float(loss_price) - float(open_price)) * shares
                                zone_stop_loss += 1
                            else:
                                backtest_profit = (float(close_price) - float(open_price)) * shares
                                zone_normal_close += 1
                        else:  # 买跌
                            if float(close_price) <= float(profit_price):
                                backtest_profit = (float(open_price) - float(profit_price)) * shares
                                zone_take_profit += 1
                            elif float(close_price) >= float(loss_price):
                                backtest_profit = (float(open_price) - float(loss_price)) * shares
                                zone_stop_loss += 1
                            else:
                                backtest_profit = (float(open_price) - float(close_price)) * shares
                                zone_normal_close += 1
                    
                    zone_backtest_profit += backtest_profit
                    zone_gross_profit += gross_profit
                
                total_zone_trades = zone_take_profit + zone_stop_loss + zone_normal_close
                
                print(f"   • 策略方向: {expected_direction}")
                print(f"   • 回测盈亏: {zone_backtest_profit:+,.0f}港币")
                print(f"   • 原始毛利润: {zone_gross_profit:+,.0f}港币")
                print(f"   • 止盈: {zone_take_profit}次, 止损: {zone_stop_loss}次, 到期: {zone_normal_close}次, 观望: {zone_observe}次")
                
                if total_zone_trades > 0:
                    zone_win_rate = zone_take_profit / total_zone_trades
                    print(f"   • 胜率: {zone_win_rate*100:.1f}%")
                    print(f"   • 平均盈亏: {zone_backtest_profit/total_zone_trades:+.0f}港币/次")
            
            return True
            
        except Exception as e:
            print(f"❌ 按策略区域分析失败: {e}")
            return False
    
    def close_connection(self):
        """关闭数据库连接"""
        if self.connection:
            self.connection.close()
            print("\n🔒 数据库连接已关闭")

def main():
    """主函数"""
    print("🏦 使用正确数据重新回测test表")
    print("="*60)
    print("📊 使用test表中的正确数据:")
    print("   • profit价格 = 止盈价格")
    print("   • loss价格 = 止损价格")
    print("   • 交易方向已正确设置")
    print("   • 毛利润已正确计算")
    
    # 创建回测器
    backtester = CorrectBacktester()
    
    # 连接数据库
    if not backtester.connect_database():
        return
    
    # 运行正确回测
    backtester.run_correct_backtest()
    
    # 按策略区域分析
    backtester.analyze_by_strategy_zones()
    
    # 关闭连接
    backtester.close_connection()
    
    print(f"\n🎉 使用正确数据的回测完成!")

if __name__ == "__main__":
    main()
