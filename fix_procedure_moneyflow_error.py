#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复存储过程MoneyFlowRatio错误
===========================
修复1054错误：Unknown column 'MoneyFlowRatio' in 'where clause'
"""

import mysql.connector
import re

class ProcedureMoneyFlowFixer:
    """存储过程MoneyFlowRatio错误修复器"""
    
    def __init__(self):
        """初始化连接参数"""
        self.host = "localhost"
        self.user = "root"
        self.password = "12345678"
        self.database = "finance"
        
    def connect_database(self):
        """连接数据库"""
        try:
            self.conn = mysql.connector.connect(
                host=self.host,
                user=self.user,
                password=self.password,
                database=self.database
            )
            self.cursor = self.conn.cursor()
            print("✅ MySQL数据库连接成功")
            return True
        except mysql.connector.Error as e:
            print(f"❌ MySQL连接失败: {e}")
            return False
    
    def find_problematic_procedures(self):
        """查找包含MoneyFlowRatio的存储过程"""
        print("\n🔍 查找包含MoneyFlowRatio的存储过程...")
        
        try:
            # 查找所有存储过程
            self.cursor.execute(f"""
                SELECT ROUTINE_NAME, ROUTINE_DEFINITION 
                FROM information_schema.ROUTINES 
                WHERE ROUTINE_SCHEMA = '{self.database}' 
                AND ROUTINE_TYPE = 'PROCEDURE'
            """)
            
            procedures = self.cursor.fetchall()
            problematic_procedures = []
            
            for proc_name, proc_definition in procedures:
                if proc_definition and 'MoneyFlowRatio' in proc_definition:
                    problematic_procedures.append((proc_name, proc_definition))
                    print(f"   ⚠️ 发现问题存储过程: {proc_name}")
            
            if not problematic_procedures:
                print("   ✅ 未发现包含MoneyFlowRatio的存储过程")
                
                # 检查是否有其他可能的问题
                print("\n🔍 检查可能引用不存在列的存储过程...")
                
                # 获取所有表的列信息
                tables_with_moneyflow = []
                self.cursor.execute(f"SHOW TABLES FROM {self.database}")
                tables = self.cursor.fetchall()
                
                for (table_name,) in tables:
                    self.cursor.execute(f"SHOW COLUMNS FROM {table_name}")
                    columns = [col[0] for col in self.cursor.fetchall()]
                    if 'MoneyFlowRatio' in columns:
                        tables_with_moneyflow.append(table_name)
                
                print(f"   包含MoneyFlowRatio列的表: {tables_with_moneyflow}")
                
                # 检查存储过程是否引用了错误的表
                for proc_name, proc_definition in procedures:
                    if proc_definition:
                        # 检查是否引用了不包含MoneyFlowRatio的表但使用了该列
                        for (table_name,) in tables:
                            if table_name in proc_definition and table_name not in tables_with_moneyflow:
                                if 'MoneyFlowRatio' in proc_definition:
                                    print(f"   ⚠️ 存储过程 {proc_name} 可能错误引用了表 {table_name}")
            
            return problematic_procedures
            
        except mysql.connector.Error as e:
            print(f"❌ 查找存储过程失败: {e}")
            return []
    
    def check_table_structures(self):
        """检查表结构"""
        print("\n📊 检查相关表结构...")
        
        try:
            # 检查主要的表
            tables_to_check = ['eab_0023hk', 'eab_0023hk_moneyflow', 'hkhsi50']
            
            for table_name in tables_to_check:
                try:
                    self.cursor.execute(f"SHOW COLUMNS FROM {table_name}")
                    columns = self.cursor.fetchall()
                    
                    print(f"\n   📋 表 {table_name} 的列:")
                    has_moneyflow = False
                    for col_name, col_type, null, key, default, extra in columns:
                        if 'money' in col_name.lower() or 'flow' in col_name.lower():
                            print(f"     💰 {col_name} ({col_type})")
                            has_moneyflow = True
                        else:
                            print(f"       {col_name} ({col_type})")
                    
                    if not has_moneyflow:
                        print(f"     ⚠️ 表 {table_name} 不包含MoneyFlow相关列")
                
                except mysql.connector.Error as e:
                    print(f"     ❌ 表 {table_name} 不存在或无法访问: {e}")
            
        except mysql.connector.Error as e:
            print(f"❌ 检查表结构失败: {e}")
    
    def fix_procedure_references(self, problematic_procedures):
        """修复存储过程中的引用"""
        print("\n🔧 修复存储过程中的MoneyFlowRatio引用...")
        
        for proc_name, proc_definition in problematic_procedures:
            print(f"\n   修复存储过程: {proc_name}")
            
            try:
                # 备份原存储过程
                backup_name = f"{proc_name}_backup_moneyflow_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
                
                # 获取完整的存储过程定义
                self.cursor.execute(f"SHOW CREATE PROCEDURE {proc_name}")
                create_proc = self.cursor.fetchone()[2]
                
                print(f"     📄 创建备份: {backup_name}")
                
                # 创建备份（重命名原存储过程）
                backup_proc = create_proc.replace(f"PROCEDURE `{proc_name}`", f"PROCEDURE `{backup_name}`")
                self.cursor.execute(backup_proc)
                
                # 分析并修复原存储过程
                fixed_proc = self.analyze_and_fix_procedure(proc_name, create_proc)
                
                if fixed_proc:
                    # 删除原存储过程
                    self.cursor.execute(f"DROP PROCEDURE {proc_name}")
                    
                    # 创建修复后的存储过程
                    self.cursor.execute(fixed_proc)
                    
                    print(f"     ✅ 存储过程 {proc_name} 修复完成")
                else:
                    print(f"     ⚠️ 存储过程 {proc_name} 需要手动修复")
                
            except mysql.connector.Error as e:
                print(f"     ❌ 修复存储过程 {proc_name} 失败: {e}")
                self.conn.rollback()
    
    def analyze_and_fix_procedure(self, proc_name, create_proc):
        """分析并修复存储过程"""
        print(f"     🔍 分析存储过程 {proc_name}...")
        
        # 常见的修复模式
        fixes = [
            # 如果引用的是eab_0023hk表，改为eab_0023hk_moneyflow表
            (r'\beab_0023hk\b(?!\w)', 'eab_0023hk_moneyflow'),
            
            # 如果引用的是hkhsi50表但使用MoneyFlowRatio，需要JOIN
            (r'FROM\s+hkhsi50\s+WHERE.*MoneyFlowRatio', 
             'FROM hkhsi50 h JOIN eab_0023hk_moneyflow m ON h.Date = m.Date WHERE'),
            
            # 修复SELECT语句中的MoneyFlowRatio引用
            (r'SELECT\s+([^FROM]*?)MoneyFlowRatio([^FROM]*?)\s+FROM\s+(?!eab_0023hk_moneyflow)', 
             r'SELECT \1m.MoneyFlowRatio\2 FROM eab_0023hk_moneyflow m JOIN '),
        ]
        
        fixed_proc = create_proc
        changes_made = False
        
        for pattern, replacement in fixes:
            if re.search(pattern, fixed_proc, re.IGNORECASE):
                fixed_proc = re.sub(pattern, replacement, fixed_proc, flags=re.IGNORECASE)
                changes_made = True
                print(f"     🔧 应用修复模式: {pattern[:50]}...")
        
        if changes_made:
            return fixed_proc
        else:
            # 如果没有自动修复，提供手动修复建议
            print(f"     💡 建议手动修复:")
            print(f"       1. 确认要查询的表是否包含MoneyFlowRatio列")
            print(f"       2. 如果需要MoneyFlowRatio，使用eab_0023hk_moneyflow表")
            print(f"       3. 或者JOIN相关表获取MoneyFlowRatio数据")
            return None
    
    def create_safe_procedures(self):
        """创建安全的示例存储过程"""
        print("\n📝 创建安全的MoneyFlow查询存储过程...")
        
        try:
            # 创建一个安全的MoneyFlow查询存储过程
            safe_procedure = """
            CREATE PROCEDURE sp_get_eab_moneyflow_data(
                IN start_date DATE,
                IN end_date DATE
            )
            BEGIN
                SELECT 
                    Date,
                    Close,
                    Volume,
                    MoneyFlowRatio,
                    MFI,
                    Y_Value,
                    X_Value,
                    E_Value,
                    TradingSignal
                FROM eab_0023hk_moneyflow
                WHERE Date BETWEEN start_date AND end_date
                ORDER BY Date DESC;
            END
            """
            
            # 检查是否已存在
            self.cursor.execute("SHOW PROCEDURE STATUS WHERE Name = 'sp_get_eab_moneyflow_data'")
            if self.cursor.fetchone():
                self.cursor.execute("DROP PROCEDURE sp_get_eab_moneyflow_data")
            
            self.cursor.execute(safe_procedure)
            print("   ✅ 创建安全存储过程: sp_get_eab_moneyflow_data")
            
            # 创建MoneyFlow分析存储过程
            analysis_procedure = """
            CREATE PROCEDURE sp_analyze_moneyflow_signals(
                IN days_back INT
            )
            BEGIN
                SELECT 
                    TradingSignal,
                    COUNT(*) as signal_count,
                    AVG(MoneyFlowRatio) as avg_money_flow_ratio,
                    AVG(MFI) as avg_mfi,
                    AVG(Close) as avg_price
                FROM eab_0023hk_moneyflow
                WHERE Date >= DATE_SUB(CURDATE(), INTERVAL days_back DAY)
                GROUP BY TradingSignal
                ORDER BY TradingSignal;
            END
            """
            
            # 检查是否已存在
            self.cursor.execute("SHOW PROCEDURE STATUS WHERE Name = 'sp_analyze_moneyflow_signals'")
            if self.cursor.fetchone():
                self.cursor.execute("DROP PROCEDURE sp_analyze_moneyflow_signals")
            
            self.cursor.execute(analysis_procedure)
            print("   ✅ 创建分析存储过程: sp_analyze_moneyflow_signals")
            
            self.conn.commit()
            
        except mysql.connector.Error as e:
            print(f"   ❌ 创建安全存储过程失败: {e}")
            self.conn.rollback()
    
    def test_procedures(self):
        """测试存储过程"""
        print("\n🧪 测试存储过程...")
        
        try:
            # 测试MoneyFlow数据查询
            print("   测试 sp_get_eab_moneyflow_data...")
            self.cursor.callproc('sp_get_eab_moneyflow_data', ['2025-07-01', '2025-07-18'])
            
            for result in self.cursor.stored_results():
                rows = result.fetchall()
                print(f"     ✅ 返回 {len(rows)} 条记录")
                if rows:
                    print(f"     最新记录: {rows[0][0]} | 价格: {rows[0][1]} | MFI: {rows[0][4]:.1f}")
            
            # 测试信号分析
            print("   测试 sp_analyze_moneyflow_signals...")
            self.cursor.callproc('sp_analyze_moneyflow_signals', [30])
            
            for result in self.cursor.stored_results():
                rows = result.fetchall()
                print(f"     ✅ 信号分析结果:")
                for row in rows:
                    signal_name = "做多" if row[0] == 1 else "做空" if row[0] == -1 else "观望"
                    print(f"       {signal_name}: {row[1]}次, 平均MFR: {row[2]:.4f}, 平均MFI: {row[3]:.1f}")
            
        except mysql.connector.Error as e:
            print(f"   ❌ 测试存储过程失败: {e}")
    
    def close_connection(self):
        """关闭数据库连接"""
        if hasattr(self, 'cursor') and self.cursor:
            self.cursor.close()
        if hasattr(self, 'conn') and self.conn.is_connected():
            self.conn.close()
            print("🔗 数据库连接已关闭")
    
    def run_complete_fix(self):
        """运行完整的修复流程"""
        print("🔧 存储过程MoneyFlowRatio错误修复工具")
        print("=" * 80)
        print("修复错误: 1054 - Unknown column 'MoneyFlowRatio' in 'where clause'")
        
        try:
            # 连接数据库
            if not self.connect_database():
                return False
            
            # 检查表结构
            self.check_table_structures()
            
            # 查找问题存储过程
            problematic_procedures = self.find_problematic_procedures()
            
            # 修复存储过程
            if problematic_procedures:
                self.fix_procedure_references(problematic_procedures)
            
            # 创建安全的存储过程
            self.create_safe_procedures()
            
            # 测试存储过程
            self.test_procedures()
            
            print(f"\n🎉 存储过程MoneyFlowRatio错误修复完成！")
            print(f"💡 现在可以安全使用MoneyFlowRatio相关功能")
            
            return True
            
        except Exception as e:
            print(f"❌ 修复过程失败: {e}")
            return False
        
        finally:
            self.close_connection()

def manual_fix_guide():
    """手动修复指南"""
    print("\n📋 手动修复指南:")
    print("=" * 60)
    
    print("1. 检查错误的存储过程:")
    print("   SHOW PROCEDURE STATUS WHERE Db = 'finance';")
    
    print("\n2. 查看存储过程定义:")
    print("   SHOW CREATE PROCEDURE procedure_name;")
    
    print("\n3. 常见修复方法:")
    print("   a) 如果引用eab_0023hk表，改为eab_0023hk_moneyflow:")
    print("      FROM eab_0023hk -> FROM eab_0023hk_moneyflow")
    
    print("\n   b) 如果需要JOIN多个表:")
    print("      FROM hkhsi50 h")
    print("      JOIN eab_0023hk_moneyflow m ON h.Date = m.Date")
    print("      WHERE m.MoneyFlowRatio > 1.0")
    
    print("\n4. 重新创建存储过程:")
    print("   DROP PROCEDURE procedure_name;")
    print("   CREATE PROCEDURE procedure_name (...) BEGIN ... END;")
    
    print("\n5. 测试修复结果:")
    print("   CALL procedure_name(parameters);")

def main():
    """主函数"""
    from datetime import datetime
    
    fixer = ProcedureMoneyFlowFixer()
    
    print("选择操作:")
    print("1. 自动修复存储过程错误")
    print("2. 显示手动修复指南")
    print("3. 运行完整修复")
    
    choice = input("\n请输入选择 (1-3): ").strip()
    
    if choice == "1":
        success = fixer.run_complete_fix()
        if not success:
            manual_fix_guide()
    elif choice == "2":
        manual_fix_guide()
    elif choice == "3":
        fixer.run_complete_fix()
        manual_fix_guide()
    else:
        print("❌ 无效选择")

if __name__ == "__main__":
    main()
