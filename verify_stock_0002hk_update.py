#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
验证stock_0002_hk表更新结果
==========================

检查MySQL数据库中stock_0002_hk表的最新数据
验证更新是否成功

作者: Cosmoon NG
日期: 2025年7月24日
"""

import mysql.connector
from datetime import datetime
import pandas as pd

def verify_update():
    """验证数据库更新结果"""
    print("🔍 验证stock_0002_hk表更新结果")
    print("=" * 40)

    # 数据库配置
    db_config = {
        'host': 'localhost',
        'user': 'root',
        'password': '12345678',
        'database': 'finance',
        'charset': 'utf8mb4'
    }

    try:
        # 连接数据库
        connection = mysql.connector.connect(**db_config)
        cursor = connection.cursor()
        print("✅ 数据库连接成功")

        # 1. 检查总记录数
        cursor.execute("SELECT COUNT(*) FROM stock_0002_hk")
        total_count = cursor.fetchone()[0]
        print(f"\n📊 总记录数: {total_count}")

        # 2. 检查日期范围
        cursor.execute("SELECT MIN(date), MAX(date) FROM stock_0002_hk")
        date_range = cursor.fetchone()
        print(f"📅 日期范围: {date_range[0]} 至 {date_range[1]}")

        # 3. 检查最新10条记录
        cursor.execute("""
            SELECT date, close, y_probability, inflow_ratio, Full_Y, E, Controller
            FROM stock_0002_hk
            ORDER BY date DESC
            LIMIT 10
        """)

        latest_records = cursor.fetchall()
        print(f"\n📈 最新10条记录:")
        print("日期        | 收盘价  | Y概率  | 资金流 | Full_Y | E值    | 控制")
        print("-" * 65)
        for record in latest_records:
            e_val = record[5] if record[5] is not None else 0.0
            controller = record[6] if record[6] is not None else 0
            print(f"{record[0]} | {record[1]:6.2f} | {record[2]:5.3f} | {record[3]:5.3f} | {record[4]:5.3f} | {e_val:6.3f} | {controller}")

        # 4. 检查今日数据
        today = datetime.now().strftime('%Y-%m-%d')
        cursor.execute("""
            SELECT date, close, y_probability, inflow_ratio, Full_Y, E
            FROM stock_0002_hk
            WHERE date = %s
        """, (today,))

        today_data = cursor.fetchone()
        if today_data:
            print(f"\n🎯 今日数据 ({today}):")
            print(f"   💰 收盘价: {today_data[1]:.2f} 港元")
            print(f"   📊 Y概率: {today_data[2]:.3f}")
            print(f"   📊 资金流比率: {today_data[3]:.3f}")
            print(f"   📊 Full_Y: {today_data[4]:.3f}")
            print(f"   📊 E值: {today_data[5]:.3f}")
        else:
            print(f"\n⚠️ 未找到今日数据 ({today})")

        # 5. 检查最近一周的数据
        cursor.execute("""
            SELECT date, close, y_probability, inflow_ratio, Full_Y, E
            FROM stock_0002_hk
            WHERE date >= DATE_SUB(CURDATE(), INTERVAL 7 DAY)
            ORDER BY date DESC
        """)

        week_data = cursor.fetchall()
        print(f"\n📅 最近一周数据 ({len(week_data)} 条记录):")
        for record in week_data:
            print(f"   {record[0]}: 收盘={record[1]:.2f}, Y={record[2]:.3f}, 资金流={record[3]:.3f}, E={record[5]:.3f}")

        # 6. 统计分析
        cursor.execute("""
            SELECT
                AVG(close) as avg_close,
                MIN(close) as min_close,
                MAX(close) as max_close,
                AVG(y_probability) as avg_y,
                AVG(inflow_ratio) as avg_inflow,
                AVG(Full_Y) as avg_full_y,
                AVG(E) as avg_e
            FROM stock_0002_hk
            WHERE date >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
        """)

        stats = cursor.fetchone()
        if stats:
            print(f"\n📊 最近30天统计:")
            print(f"   💰 平均收盘价: {stats[0]:.2f} 港元")
            print(f"   📈 价格范围: {stats[1]:.2f} - {stats[2]:.2f} 港元")
            print(f"   📊 平均Y概率: {stats[3]:.3f}")
            print(f"   📊 平均资金流: {stats[4]:.3f}")
            print(f"   📊 平均Full_Y: {stats[5]:.3f}")
            print(f"   📊 平均E值: {stats[6]:.3f}")

        # 7. 检查数据完整性
        cursor.execute("""
            SELECT
                COUNT(*) as total,
                COUNT(y_probability) as y_count,
                COUNT(inflow_ratio) as inflow_count,
                COUNT(Full_Y) as full_y_count,
                COUNT(E) as e_count
            FROM stock_0002_hk
        """)

        integrity = cursor.fetchone()
        print(f"\n🔍 数据完整性检查:")
        print(f"   总记录数: {integrity[0]}")
        print(f"   Y概率完整: {integrity[1]} ({integrity[1]/integrity[0]*100:.1f}%)")
        print(f"   资金流完整: {integrity[2]} ({integrity[2]/integrity[0]*100:.1f}%)")
        print(f"   Full_Y完整: {integrity[3]} ({integrity[3]/integrity[0]*100:.1f}%)")
        print(f"   E值完整: {integrity[4]} ({integrity[4]/integrity[0]*100:.1f}%)")

        cursor.close()
        connection.close()

        print(f"\n✅ 验证完成！stock_0002_hk表数据已成功更新到今日")

    except mysql.connector.Error as e:
        print(f"❌ 数据库操作失败: {e}")
    except Exception as e:
        print(f"❌ 验证失败: {e}")

if __name__ == "__main__":
    verify_update()
