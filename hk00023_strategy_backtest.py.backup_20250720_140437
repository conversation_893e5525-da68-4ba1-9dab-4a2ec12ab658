#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
东亚银行(HK00023)策略回测
========================

专门针对东亚银行的Cosmoon博弈论策略回测
- 总资金：30,000港币
- 标的：东亚银行(HK00023)
- 期间：20年历史数据
- 策略：完整的买涨买跌策略

作者: Cosmoon NG
日期: 2025年7月
"""

import yfinance as yf
import pymysql
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

class HK00023Strategy:
    def __init__(self):
        """初始化东亚银行策略"""
        self.symbol = "0023.HK"
        self.data = None
        
        # 策略参数
        self.strategy_params = {
            'initial_capital': 30000,  # 30000港币
            'kelly_win_rate': 0.6,
            'kelly_win_ratio': 2,
            'kelly_loss_ratio': 1,
            'max_position_ratio': 0.15,  # 单次最大仓位15%
            'max_total_positions': 4,    # 最多同时持有4个仓位
            
            # 止盈止损参数
            'high_profit_take_profit': 0.04,  # 高值区止盈4%
            'high_profit_stop_loss': 0.02,    # 高值区止损2%
            'strong_loss_take_profit': 0.04,  # 强亏损区止盈4%
            'strong_loss_stop_loss': 0.02,    # 强亏损区止损2%
            'other_take_profit': 0.04,        # 其他区域止盈4%
            'other_stop_loss': 0.02,          # 其他区域止损2%
            
            'transaction_cost': 0.0025,       # 交易成本0.25%
        }
        
        self.trades = []
        self.daily_portfolio = []
        self.current_positions = []
        
        # 数据库配置
        self.db_config = {
            'host': '************',
            'port': 3306,
            'database': 'finance',
            'user': 'root',
            'password': '',
            'charset': 'utf8mb4'
        }
    
    def try_database_first(self):
        """先尝试从数据库获取数据"""
        try:
            connection = pymysql.connect(**self.db_config)
            print("✅ 数据库连接成功，查找HK00023数据...")
            
            # 查找可能的表名
            possible_tables = [
                'hk00023', 'stock_0023_hk', 'stock_23_hk', 
                'hk0023', 'stock_0023', 'eastasia_0023'
            ]
            
            for table in possible_tables:
                try:
                    query = f"SELECT COUNT(*) as count FROM {table} WHERE date >= DATE_SUB(CURDATE(), INTERVAL 20 YEAR)"
                    result = pd.read_sql(query, connection)
                    if result['count'].iloc[0] > 1000:
                        print(f"✅ 找到数据表: {table}")
                        return self.load_from_database(connection, table)
                except:
                    continue
            
            print("❌ 数据库中未找到HK00023数据")
            connection.close()
            return False
            
        except Exception as e:
            print(f"❌ 数据库连接失败: {e}")
            return False
    
    def load_from_database(self, connection, table_name):
        """从数据库加载数据"""
        try:
            query = f"""
                SELECT 
                    date,
                    close,
                    high,
                    low,
                    volume,
                    y_probability,
                    inflow_ratio
                FROM {table_name}
                WHERE date >= DATE_SUB(CURDATE(), INTERVAL 20 YEAR)
                AND close > 0
                ORDER BY date ASC
            """
            
            self.data = pd.read_sql(query, connection)
            connection.close()
            
            if self.data.empty:
                return False
            
            self.data['date'] = pd.to_datetime(self.data['date'])
            print(f"✅ 从数据库加载了 {len(self.data)} 条记录")
            return True
            
        except Exception as e:
            print(f"❌ 数据库加载失败: {e}")
            return False
    
    def fetch_from_yfinance(self):
        """从yfinance获取数据"""
        print(f"📊 从yfinance获取东亚银行({self.symbol})数据...")
        
        try:
            ticker = yf.Ticker(self.symbol)
            self.data = ticker.history(period="20y", interval="1d")
            
            if self.data.empty:
                print("❌ yfinance未获取到数据")
                return False
            
            # 数据预处理
            self.data.reset_index(inplace=True)
            self.data.columns = [col.lower() for col in self.data.columns]
            
            print(f"✅ 从yfinance获取了 {len(self.data)} 条记录")
            print(f"   • 数据期间: {self.data['date'].min().strftime('%Y-%m-%d')} 至 {self.data['date'].max().strftime('%Y-%m-%d')}")
            print(f"   • 价格范围: {self.data['close'].min():.2f} - {self.data['close'].max():.2f} 港币")
            
            return True
            
        except Exception as e:
            print(f"❌ yfinance获取失败: {e}")
            return False
    
    def calculate_technical_indicators(self):
        """计算技术指标"""
        print("🔢 计算技术指标...")
        
        # 移动平均线
        self.data['ma_20'] = self.data['close'].rolling(window=20).mean()
        self.data['ma_60'] = self.data['close'].rolling(window=60).mean()
        self.data['ma_120'] = self.data['close'].rolling(window=120).mean()
        
        # RSI
        delta = self.data['close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
        rs = gain / loss
        self.data['rsi'] = 100 - (100 / (1 + rs))
        
        # 成交量指标
        self.data['volume_ma'] = self.data['volume'].rolling(window=20).mean()
        self.data['volume_ratio'] = self.data['volume'] / self.data['volume_ma']
        
        print("✅ 技术指标计算完成")
    
    def calculate_y_x_values(self):
        """计算Y和X值"""
        print("🎯 计算东亚银行专用Y和X值...")
        
        # Y值计算 - 基于均值回归理论
        price_vs_ma20 = self.data['close'] / self.data['ma_20']
        price_vs_ma60 = self.data['close'] / self.data['ma_60']
        
        # 基础Y值：价格相对于均线的位置
        base_y = np.where(price_vs_ma20 >= 1, 
                         0.5 + 0.4 * np.tanh((price_vs_ma20 - 1) * 3),
                         0.5 - 0.4 * np.tanh((1 - price_vs_ma20) * 3))
        
        # 趋势调整
        ma_trend = (self.data['ma_20'] / self.data['ma_60']).fillna(1)
        trend_adjustment = 0.1 * np.tanh((ma_trend - 1) * 2)
        
        # 成交量调整
        volume_adjustment = 0.05 * np.tanh((self.data['volume_ratio'] - 1))
        
        self.data['y_probability'] = base_y + trend_adjustment + volume_adjustment
        self.data['y_probability'] = np.clip(self.data['y_probability'], 0.1, 0.9)
        self.data['y_probability'].fillna(0.5, inplace=True)
        
        # X值计算 - 资金流入比例
        price_change = (self.data['close'] - self.data['open']) / self.data['open']
        money_flow = self.data['volume'] * price_change
        
        def calc_inflow_ratio(flows):
            if len(flows) == 0 or flows.isna().all():
                return 0.5
            flows = flows.dropna()
            if len(flows) == 0:
                return 0.5
            
            inflows = flows[flows > 0].sum()
            outflows = abs(flows[flows < 0].sum())
            total_flow = inflows + outflows
            
            return inflows / total_flow if total_flow > 0 else 0.5
        
        base_x = money_flow.rolling(window=20).apply(calc_inflow_ratio, raw=False)
        
        # RSI调整
        rsi_normalized = self.data['rsi'] / 100
        rsi_adjustment = 0.3 * (rsi_normalized - 0.5)
        
        self.data['inflow_ratio'] = base_x + rsi_adjustment
        self.data['inflow_ratio'] = np.clip(self.data['inflow_ratio'], 0.1, 0.9)
        self.data['inflow_ratio'].fillna(0.5, inplace=True)
        
        print(f"✅ Y和X值计算完成")
        print(f"   • Y值范围: {self.data['y_probability'].min():.3f} - {self.data['y_probability'].max():.3f}")
        print(f"   • X值范围: {self.data['inflow_ratio'].min():.3f} - {self.data['inflow_ratio'].max():.3f}")
    
    def analyze_strategy_zones(self):
        """分析策略区域"""
        print("\n🎯 东亚银行策略区域分析:")
        print("-" * 50)
        
        # 计算E值
        self.data['e_value'] = (8 * self.data['inflow_ratio'] * self.data['y_probability'] - 
                               3 * self.data['inflow_ratio'] - 3 * self.data['y_probability'] + 1)
        
        # 标记策略区域
        conditions = [
            (self.data['y_probability'] > 0.5) & (self.data['inflow_ratio'] > 0.5),  # 高值盈利区
            (self.data['y_probability'] > 0.333) & (self.data['y_probability'] < 0.4),  # 控股商控制区
            (self.data['y_probability'] < 0.25) | (self.data['inflow_ratio'] < 0.25),  # 强亏损区
        ]
        
        choices = ['HIGH_PROFIT', 'CONTROL_ZONE', 'STRONG_LOSS']
        self.data['strategy_zone'] = np.select(conditions, choices, default='OTHER')
        
        # 统计分布
        zone_counts = self.data['strategy_zone'].value_counts()
        total = len(self.data)
        
        print(f"📊 策略区域分布:")
        for zone, count in zone_counts.items():
            print(f"   • {zone}: {count} 天 ({count/total*100:.1f}%)")
        
        positive_e = (self.data['e_value'] > 0).sum()
        print(f"   • E>0 (理论盈利): {positive_e} 天 ({positive_e/total*100:.1f}%)")
        
        # 显示最近数据
        print(f"\n📈 最近5天数据:")
        recent = self.data.tail(5)[['date', 'close', 'y_probability', 'inflow_ratio', 'e_value', 'strategy_zone']]
        for _, row in recent.iterrows():
            date_str = row['date'].strftime('%Y-%m-%d')
            print(f"   {date_str}: 价格={row['close']:.2f}, Y={row['y_probability']:.3f}, X={row['inflow_ratio']:.3f}, E={row['e_value']:.3f}, 区域={row['strategy_zone']}")
    
    def calculate_kelly_position(self, win_rate_modifier=1.0):
        """计算凯利公式仓位"""
        win_rate = self.strategy_params['kelly_win_rate'] * win_rate_modifier
        win_ratio = self.strategy_params['kelly_win_ratio']
        loss_ratio = self.strategy_params['kelly_loss_ratio']
        
        b = win_ratio / loss_ratio
        p = win_rate
        q = 1 - p
        
        kelly_fraction = (b * p - q) / b
        return max(0, min(kelly_fraction, self.strategy_params['max_position_ratio']))
    
    def check_stop_conditions(self, position, current_price):
        """检查止盈止损"""
        entry_price = position['entry_price']
        direction = position['direction']
        zone = position['zone']
        
        if direction == 'LONG':
            profit_pct = (current_price - entry_price) / entry_price
            if zone in ['HIGH_PROFIT', 'STRONG_LOSS']:
                if profit_pct >= self.strategy_params['high_profit_take_profit']:
                    return 'TAKE_PROFIT'
                elif profit_pct <= -self.strategy_params['high_profit_stop_loss']:
                    return 'STOP_LOSS'
            else:
                if profit_pct >= self.strategy_params['other_take_profit']:
                    return 'TAKE_PROFIT'
                elif profit_pct <= -self.strategy_params['other_stop_loss']:
                    return 'STOP_LOSS'
        else:  # SHORT
            profit_pct = (entry_price - current_price) / entry_price
            if profit_pct >= self.strategy_params['other_take_profit']:
                return 'TAKE_PROFIT'
            elif profit_pct <= -self.strategy_params['other_stop_loss']:
                return 'STOP_LOSS'
        
        return None
    
    def run_backtest(self):
        """运行回测"""
        print(f"\n🚀 开始东亚银行(HK00023)策略回测...")
        print("="*70)
        print(f"💰 初始资金: {self.strategy_params['initial_capital']:,} 港币")
        print(f"🏦 标的: 东亚银行 (HK00023)")
        print(f"📅 期间: 20年历史数据")
        print(f"🎯 策略: Cosmoon博弈论 + 凯利公式 + 买涨买跌")
        print("="*70)
        
        current_cash = self.strategy_params['initial_capital']
        winning_trades = 0
        losing_trades = 0
        
        for i, row in self.data.iterrows():
            date = row['date']
            price = row['close']
            zone = row['strategy_zone']
            y_val = row['y_probability']
            x_val = row['inflow_ratio']
            
            # 检查止盈止损
            positions_to_close = []
            for j, position in enumerate(self.current_positions):
                stop_reason = self.check_stop_conditions(position, price)
                if stop_reason:
                    positions_to_close.append((j, stop_reason))
            
            # 平仓处理
            for j, reason in reversed(positions_to_close):
                position = self.current_positions[j]
                
                if position['direction'] == 'LONG':
                    profit = (price - position['entry_price']) * position['shares'] - position['cost']
                    exit_value = position['shares'] * price - position['cost']
                else:  # SHORT
                    profit = (position['entry_price'] - price) * position['shares'] - position['cost']
                    exit_value = position['shares'] * price - position['cost']
                
                current_cash += exit_value
                
                trade_record = {
                    'date': date,
                    'action': f"CLOSE_{position['direction']}",
                    'direction': position['direction'],
                    'price': price,
                    'shares': position['shares'],
                    'profit': profit,
                    'profit_pct': profit / (position['shares'] * position['entry_price']) * 100,
                    'reason': reason,
                    'zone': position['zone'],
                    'cash_after': current_cash
                }
                self.trades.append(trade_record)
                
                if profit > 0:
                    winning_trades += 1
                else:
                    losing_trades += 1
                
                del self.current_positions[j]
            
            # 开仓逻辑
            current_position_count = len(self.current_positions)
            
            if zone == 'HIGH_PROFIT' and current_position_count < self.strategy_params['max_total_positions']:
                # 高值盈利区：买涨
                kelly_fraction = self.calculate_kelly_position(1.0)
                position_value = current_cash * kelly_fraction
                transaction_cost = position_value * self.strategy_params['transaction_cost']
                net_value = position_value - transaction_cost
                shares = net_value / price
                
                if shares > 0 and position_value > 1000:  # 最小1000港币
                    current_cash -= position_value
                    
                    new_position = {
                        'entry_date': date,
                        'entry_price': price,
                        'shares': shares,
                        'direction': 'LONG',
                        'zone': zone,
                        'cost': transaction_cost
                    }
                    self.current_positions.append(new_position)
                    
            elif zone == 'STRONG_LOSS' and current_position_count < self.strategy_params['max_total_positions']:
                # 强亏损区：买涨（低位反弹）
                kelly_fraction = self.calculate_kelly_position(0.8)
                position_value = current_cash * kelly_fraction
                transaction_cost = position_value * self.strategy_params['transaction_cost']
                net_value = position_value - transaction_cost
                shares = net_value / price
                
                if shares > 0 and position_value > 1000:
                    current_cash -= position_value
                    
                    new_position = {
                        'entry_date': date,
                        'entry_price': price,
                        'shares': shares,
                        'direction': 'LONG',
                        'zone': zone,
                        'cost': transaction_cost
                    }
                    self.current_positions.append(new_position)
                    
            elif zone == 'OTHER' and current_position_count < self.strategy_params['max_total_positions']:
                # 其他区域：买跌
                kelly_fraction = self.calculate_kelly_position(0.7)
                position_value = current_cash * kelly_fraction
                transaction_cost = position_value * self.strategy_params['transaction_cost']
                net_value = position_value - transaction_cost
                shares = net_value / price
                
                if shares > 0 and position_value > 1000:
                    current_cash -= position_value
                    
                    new_position = {
                        'entry_date': date,
                        'entry_price': price,
                        'shares': shares,
                        'direction': 'SHORT',
                        'zone': zone,
                        'cost': transaction_cost
                    }
                    self.current_positions.append(new_position)
            
            # 记录每日组合价值
            position_value = 0
            for position in self.current_positions:
                if position['direction'] == 'LONG':
                    position_value += position['shares'] * price
                else:  # SHORT
                    position_value += position['shares'] * (2 * position['entry_price'] - price)
            
            total_value = current_cash + position_value
            
            daily_record = {
                'date': date,
                'price': price,
                'cash': current_cash,
                'position_value': position_value,
                'total_value': total_value,
                'positions_count': len(self.current_positions),
                'zone': zone
            }
            self.daily_portfolio.append(daily_record)
        
        # 最终清仓
        final_price = self.data['close'].iloc[-1]
        for position in self.current_positions:
            if position['direction'] == 'LONG':
                profit = (final_price - position['entry_price']) * position['shares']
            else:
                profit = (position['entry_price'] - final_price) * position['shares']
            
            current_cash += position['shares'] * final_price
            
            if profit > 0:
                winning_trades += 1
            else:
                losing_trades += 1
        
        # 计算结果
        final_value = current_cash
        total_return = (final_value / self.strategy_params['initial_capital'] - 1) * 100
        years = len(self.data) / 252
        annual_return = (final_value / self.strategy_params['initial_capital']) ** (1/years) - 1
        
        total_trades = len(self.trades)
        win_rate = winning_trades / (winning_trades + losing_trades) * 100 if (winning_trades + losing_trades) > 0 else 0
        
        # 统计各类型交易
        long_trades = len([t for t in self.trades if t['direction'] == 'LONG'])
        short_trades = len([t for t in self.trades if t['direction'] == 'SHORT'])
        
        print(f"\n✅ 东亚银行策略回测完成!")
        print(f"📊 交易统计:")
        print(f"   • 总交易次数: {total_trades}")
        print(f"   • 做多交易: {long_trades} 次")
        print(f"   • 做空交易: {short_trades} 次")
        print(f"   • 盈利交易: {winning_trades}")
        print(f"   • 亏损交易: {losing_trades}")
        print(f"   • 实际胜率: {win_rate:.1f}%")
        
        print(f"\n💰 收益统计:")
        print(f"   • 初始资金: {self.strategy_params['initial_capital']:,} 港币")
        print(f"   • 最终资金: {final_value:,.0f} 港币")
        print(f"   • 总收益: {final_value - self.strategy_params['initial_capital']:+,.0f} 港币")
        print(f"   • 总收益率: {total_return:+.2f}%")
        print(f"   • 年化收益率: {annual_return*100:.2f}%")
        
        # 分析买跌效果
        if short_trades > 0:
            short_profits = [t['profit'] for t in self.trades if t['direction'] == 'SHORT']
            short_total_profit = sum(short_profits)
            short_winning = len([p for p in short_profits if p > 0])
            short_win_rate = short_winning / short_trades * 100
            
            print(f"\n📉 买跌策略分析:")
            print(f"   • 做空交易次数: {short_trades}")
            print(f"   • 做空胜率: {short_win_rate:.1f}%")
            print(f"   • 做空总盈亏: {short_total_profit:+,.0f} 港币")
            print(f"   • 做空平均盈亏: {short_total_profit/short_trades:+.0f} 港币/次")

def main():
    """主函数"""
    print("🏦 东亚银行(HK00023)专用策略回测")
    print("="*60)
    print("📅 启动时间:", datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
    print("💰 初始资金: 30,000港币")
    print("📊 期间: 20年历史数据")
    
    # 创建策略实例
    strategy = HK00023Strategy()
    
    # 先尝试数据库，再用yfinance
    if not strategy.try_database_first():
        if not strategy.fetch_from_yfinance():
            print("❌ 无法获取数据")
            return
    
    # 计算技术指标和Y、X值
    if 'y_probability' not in strategy.data.columns:
        strategy.calculate_technical_indicators()
        strategy.calculate_y_x_values()
    
    # 分析策略区域
    strategy.analyze_strategy_zones()
    
    # 运行回测
    strategy.run_backtest()
    
    print(f"\n🎉 东亚银行策略回测完成!")
    print(f"💡 30,000港币 × 20年 × 博弈论策略")

if __name__ == "__main__":
    main()
