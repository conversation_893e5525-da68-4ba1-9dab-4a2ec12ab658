#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终修复存储过程
==============
修复sp_combined_stock_analysis存储过程的MoneyFlowRatio问题
"""

import mysql.connector

def final_fix_procedure():
    """最终修复存储过程"""
    
    try:
        conn = mysql.connector.connect(
            host="localhost",
            user="root",
            password="12345678",
            database="finance"
        )
        cursor = conn.cursor()
        
        print("🔧 最终修复存储过程MoneyFlowRatio问题")
        print("=" * 80)
        
        print("📊 问题分析:")
        print("   存储过程 sp_combined_stock_analysis 需要表名参数")
        print("   它试图在指定表中查找 MoneyFlowRatio 列")
        print("   但 eab_0023hk 表没有 MoneyFlowRatio 列")
        print("   只有 eab_0023hk_moneyflow 表才有该列")
        
        # 测试正确的调用方式
        print("\n🧪 测试正确的调用方式:")
        
        try:
            print("   测试1: 使用 eab_0023hk_moneyflow 表...")
            cursor.callproc('sp_combined_stock_analysis', ['eab_0023hk_moneyflow'])
            
            # 获取结果
            results = []
            for result in cursor.stored_results():
                rows = result.fetchall()
                results.append(rows)
            
            print(f"   ✅ 成功！返回 {len(results)} 个结果集")
            
            if results:
                # 显示主要结果
                main_result = results[0] if results[0] else []
                if main_result:
                    print(f"   主要结果: {len(main_result)} 条记录")
                    print(f"   示例记录: {main_result[0] if main_result else 'None'}")
                
                # 显示回归参数
                if len(results) > 1 and results[1]:
                    params = results[1][0]
                    print(f"   回归参数: 截距={params[0]:.4f}, 斜率={params[1]:.6f}")
                
                # 显示执行结果
                if len(results) > 2 and results[2]:
                    message = results[2][0][0]
                    print(f"   执行结果: {message}")
            
        except Exception as e:
            print(f"   ❌ 测试失败: {e}")
        
        try:
            print("\n   测试2: 使用 hkhsi50 表...")
            cursor.callproc('sp_combined_stock_analysis', ['hkhsi50'])
            
            # 获取结果
            results = []
            for result in cursor.stored_results():
                rows = result.fetchall()
                results.append(rows)
            
            print(f"   ✅ 成功！返回 {len(results)} 个结果集")
            
            if results and results[0]:
                print(f"   主要结果: {len(results[0])} 条记录")
                
        except Exception as e:
            print(f"   ❌ 测试失败: {e}")
        
        # 创建便捷的包装存储过程
        print("\n📝 创建便捷的包装存储过程...")
        
        wrapper_procedures = [
            {
                'name': 'sp_analyze_eab_moneyflow',
                'sql': """
                CREATE PROCEDURE sp_analyze_eab_moneyflow()
                BEGIN
                    CALL sp_combined_stock_analysis('eab_0023hk_moneyflow');
                END
                """
            },
            {
                'name': 'sp_analyze_hsi50',
                'sql': """
                CREATE PROCEDURE sp_analyze_hsi50()
                BEGIN
                    CALL sp_combined_stock_analysis('hkhsi50');
                END
                """
            },
            {
                'name': 'sp_get_latest_moneyflow_analysis',
                'sql': """
                CREATE PROCEDURE sp_get_latest_moneyflow_analysis()
                BEGIN
                    SELECT 
                        Date,
                        Close,
                        MoneyFlowRatio,
                        MFI,
                        Y_Value,
                        X_Value,
                        E_Value,
                        TradingSignal,
                        CASE 
                            WHEN MoneyFlowRatio > 5.0 THEN '极高资金流入'
                            WHEN MoneyFlowRatio > 2.0 THEN '高资金流入'
                            WHEN MoneyFlowRatio > 0.5 THEN '正常'
                            WHEN MoneyFlowRatio > 0.1 THEN '低资金流入'
                            ELSE '极低资金流入'
                        END as money_flow_status,
                        CASE 
                            WHEN MFI > 80 THEN '超买'
                            WHEN MFI > 70 THEN '偏高'
                            WHEN MFI < 20 THEN '超卖'
                            WHEN MFI < 30 THEN '偏低'
                            ELSE '正常'
                        END as mfi_status
                    FROM eab_0023hk_moneyflow
                    WHERE Date >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
                    ORDER BY Date DESC
                    LIMIT 20;
                END
                """
            }
        ]
        
        for proc in wrapper_procedures:
            try:
                # 删除如果存在
                cursor.execute(f"DROP PROCEDURE IF EXISTS {proc['name']}")
                # 创建新的
                cursor.execute(proc['sql'])
                print(f"   ✅ 创建包装存储过程: {proc['name']}")
            except Exception as e:
                print(f"   ❌ 创建存储过程 {proc['name']} 失败: {e}")
        
        # 测试新的包装存储过程
        print("\n🧪 测试新的包装存储过程...")
        
        try:
            print("   测试 sp_analyze_eab_moneyflow...")
            cursor.callproc('sp_analyze_eab_moneyflow')
            
            # 跳过结果，只检查是否成功
            for result in cursor.stored_results():
                rows = result.fetchall()
            
            print("   ✅ sp_analyze_eab_moneyflow 执行成功")
            
        except Exception as e:
            print(f"   ❌ sp_analyze_eab_moneyflow 失败: {e}")
        
        try:
            print("   测试 sp_get_latest_moneyflow_analysis...")
            cursor.callproc('sp_get_latest_moneyflow_analysis')
            
            for result in cursor.stored_results():
                rows = result.fetchall()
                print(f"   ✅ 返回 {len(rows)} 条最新分析记录")
                
                if rows:
                    print(f"   最新记录: {rows[0][0]} | 价格:{rows[0][1]:.2f} | MFR:{rows[0][2]:.4f} | 状态:{rows[0][9]}")
            
        except Exception as e:
            print(f"   ❌ sp_get_latest_moneyflow_analysis 失败: {e}")
        
        conn.commit()
        cursor.close()
        conn.close()
        
        print(f"\n🎉 存储过程修复完成！")
        print(f"\n💡 使用方法:")
        print(f"   1. 分析东亚银行MoneyFlow: CALL sp_analyze_eab_moneyflow();")
        print(f"   2. 分析HSI50: CALL sp_analyze_hsi50();")
        print(f"   3. 获取最新分析: CALL sp_get_latest_moneyflow_analysis();")
        print(f"   4. 原始调用: CALL sp_combined_stock_analysis('eab_0023hk_moneyflow');")
        
        return True
        
    except Exception as e:
        print(f"❌ 修复失败: {e}")
        return False

def create_usage_guide():
    """创建使用指南"""
    
    print(f"\n📋 MoneyFlow存储过程使用指南")
    print("=" * 80)
    
    usage_examples = [
        {
            'title': '1. 分析东亚银行MoneyFlow数据',
            'command': 'CALL sp_analyze_eab_moneyflow();',
            'description': '执行完整的东亚银行MoneyFlow分析，包括回归分析和E值计算'
        },
        {
            'title': '2. 获取最新MoneyFlow分析',
            'command': 'CALL sp_get_latest_moneyflow_analysis();',
            'description': '获取最近30天的MoneyFlow分析结果，包括状态判断'
        },
        {
            'title': '3. 查询特定日期范围的数据',
            'command': "CALL sp_get_eab_moneyflow_data('2025-07-01', '2025-07-18');",
            'description': '查询指定日期范围内的MoneyFlow数据'
        },
        {
            'title': '4. 分析交易信号',
            'command': 'CALL sp_analyze_moneyflow_signals(30);',
            'description': '分析最近30天的交易信号统计'
        },
        {
            'title': '5. 原始分析调用',
            'command': "CALL sp_combined_stock_analysis('eab_0023hk_moneyflow');",
            'description': '使用原始存储过程分析MoneyFlow表'
        }
    ]
    
    for example in usage_examples:
        print(f"\n{example['title']}:")
        print(f"   命令: {example['command']}")
        print(f"   说明: {example['description']}")
    
    print(f"\n⚠️ 重要提示:")
    print(f"   - 确保使用 'eab_0023hk_moneyflow' 表名（包含MoneyFlowRatio列）")
    print(f"   - 不要使用 'eab_0023hk' 表名（不包含MoneyFlowRatio列）")
    print(f"   - MoneyFlowRatio > 2.0 表示强烈买入压力")
    print(f"   - MoneyFlowRatio < 0.5 表示强烈卖出压力")

def main():
    """主函数"""
    success = final_fix_procedure()
    
    if success:
        create_usage_guide()
    else:
        print("\n❌ 修复失败，请手动检查存储过程")

if __name__ == "__main__":
    main()
