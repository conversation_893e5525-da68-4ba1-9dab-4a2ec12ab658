#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
腾讯控股(0700.HK)回归线策略回测
=============================

使用Cosmoon NG的回归线策略回测腾讯控股
- 数据源: 数据库 stock_0700_hk 表
- 初始资金: 30,000港币
- 历史数据: 20年
- 策略: 回归线 + XY确认 + 不对称止盈止损

作者: Cosmoon NG
日期: 2025年7月16日
"""

import pymysql
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from scipy import stats
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

# 数据库配置
db_config = {
    'host': '************',
    'user': 'root',
    'password': '',
    'database': 'finance',
    'charset': 'utf8mb4'
}

class Tencent0700Strategy:
    def __init__(self):
        """初始化腾讯0700回归线策略"""
        self.symbol = "0700.HK"
        self.name = "腾讯控股"
        self.initial_capital = 30000  # 初始资金30K
        self.monthly_addition = 2000  # 每月追加2K
        
        # 不对称止盈止损设计 (按您的方法)
        self.take_profit_long = 0.016   # 多头止盈 1.6%
        self.stop_loss_long = 0.008     # 多头止损 0.8%
        self.take_profit_short = 0.008  # 空头止盈 0.8%
        self.stop_loss_short = 0.016    # 空头止损 1.6%
        
        # 交易状态
        self.position = 0  # 0=空仓, 1=多头, -1=空头
        self.entry_price = 0
        self.entry_date = None
        self.shares = 0
        self.capital = self.initial_capital
        
        # 记录
        self.trades = []
        self.daily_records = []
        self.last_month = None
        self.trade_id = 1
        self.r_squared = 0
    
    def fetch_data_from_db(self):
        """从数据库获取腾讯0700的20年历史数据"""
        print("📊 从数据库获取腾讯0700的20年历史数据...")
        
        try:
            conn = pymysql.connect(**db_config)
            
            # 检查表是否存在
            cursor = conn.cursor()
            cursor.execute("""
                SELECT COUNT(*) FROM information_schema.tables 
                WHERE table_schema = 'finance' AND table_name = 'stock_0700_hk'
            """)
            
            if cursor.fetchone()[0] == 0:
                print("❌ 表 stock_0700_hk 不存在")
                conn.close()
                return None
            
            # 获取20年数据
            end_date = datetime.now()
            start_date = end_date - timedelta(days=20*365)
            
            # 先查询基础字段和现有的计算字段
            query = """
            SELECT date, open, high, low, close, volume,
                   y_probability, inflow_ratio
            FROM stock_0700_hk
            WHERE date >= %s
            ORDER BY date ASC
            """

            df = pd.read_sql(query, conn, params=[start_date.strftime('%Y-%m-%d')])

            # 重命名列以匹配我们的代码
            df.columns = ['Date', 'Open', 'High', 'Low', 'Close', 'Volume', 'Y_Probability', 'Inflow_Ratio']
            conn.close()
            
            if df.empty:
                print("❌ 数据库中无腾讯0700数据")
                return None
            
            print(f"✅ 成功获取腾讯0700数据:")
            print(f"   • 数据期间: {df['Date'].min()} 至 {df['Date'].max()}")
            print(f"   • 总记录数: {len(df):,} 天")
            print(f"   • 价格区间: {df['Close'].min():.2f} - {df['Close'].max():.2f} 港币")
            
            return df
            
        except Exception as e:
            print(f"❌ 获取数据失败: {e}")
            return None
    
    def calculate_regression_line(self, df):
        """计算回归线 - 核心策略"""
        print("📈 计算回归线...")
        
        # 添加索引列
        df['i'] = range(1, len(df) + 1)
        
        # 线性回归计算
        slope, intercept, r_value, p_value, std_err = stats.linregress(
            df['i'], df['Close']
        )
        
        # 回归线
        df['regression_line'] = intercept + slope * df['i']
        
        # 价格相对回归线的位置 (关键指标)
        df['price_position'] = (df['Close'] - df['regression_line']) / df['regression_line']
        
        self.r_squared = r_value ** 2
        
        print(f"✅ 回归线计算完成:")
        print(f"   • R² = {self.r_squared:.4f}")
        print(f"   • 斜率 = {slope:.2f}")
        print(f"   • 截距 = {intercept:.2f}")
        
        return df

    def calculate_technical_indicators(self, df):
        """计算技术指标"""
        print("📊 计算技术指标...")

        # 1. 计算RSI (14日)
        delta = df['Close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
        rs = gain / loss
        df['RSI'] = 100 - (100 / (1 + rs))

        # 2. 计算价格动量 (10日)
        df['Price_Momentum'] = df['Close'].pct_change(10)

        # 3. 计算Full_Y (控制系数)
        # 使用现有的y_probability作为基础，结合RSI和价格动量
        df['Full_Y'] = np.where(
            df['Y_Probability'].notna(),
            df['Y_Probability'],  # 如果有现成的y_probability就用
            # 否则用RSI和价格动量计算
            np.clip((df['RSI'] / 100 + np.tanh(df['Price_Momentum'] * 5) + 1) / 2, 0.1, 0.9)
        )

        # 4. 计算MoneyFlowRatio (资金流比例)
        # 使用现有的inflow_ratio作为基础
        df['MoneyFlowRatio'] = np.where(
            df['Inflow_Ratio'].notna(),
            df['Inflow_Ratio'],  # 如果有现成的inflow_ratio就用
            # 否则基于成交量和价格变化计算
            self.calculate_money_flow_ratio(df)
        )

        # 5. 计算E值 (博弈论期望值)
        df['E'] = (8 * df['MoneyFlowRatio'] * df['Full_Y'] -
                   3 * df['MoneyFlowRatio'] - 3 * df['Full_Y'] + 1)

        # 6. 计算Controller (控股商标识)
        # 基于价格相对中位数的位置
        df['Midprice'] = (df['High'] + df['Low']) / 2
        rolling_median = df['Midprice'].rolling(window=50, center=True).median()
        df['Controller'] = np.where(df['Close'] > rolling_median, 1, 0)

        print("✅ 技术指标计算完成")
        return df

    def calculate_money_flow_ratio(self, df):
        """计算资金流比例"""
        # 基于成交量和价格变化计算资金流
        price_change = (df['Close'] - df['Open']) / df['Open']
        money_flow = df['Volume'] * price_change

        def calc_inflow_ratio(flows):
            if len(flows) == 0 or flows.isna().all():
                return 0.5
            flows = flows.dropna()
            if len(flows) == 0:
                return 0.5

            inflows = flows[flows > 0].sum()
            outflows = abs(flows[flows < 0].sum())
            total_flow = inflows + outflows

            return inflows / total_flow if total_flow > 0 else 0.5

        money_flow_ratio = money_flow.rolling(window=20).apply(calc_inflow_ratio, raw=False)
        return np.clip(money_flow_ratio, 0.1, 0.9).fillna(0.5)

    def get_trading_signal(self, row):
        """获取交易信号 - 按您的方法"""
        # 检查数据完整性
        if pd.isna(row['E']) or pd.isna(row['MoneyFlowRatio']) or pd.isna(row['Full_Y']):
            return 'HOLD'

        e_value = float(row['E'])
        money_flow = float(row['MoneyFlowRatio'])
        full_y = float(row['Full_Y'])
        price_position = float(row['price_position'])

        # 买涨条件: E>0 且 MoneyFlowRatio>0.45 且 Full_Y>0.45 且价格低于回归线
        if (e_value > 0 and
            money_flow > 0.45 and
            full_y > 0.45 and
            price_position < 0):  # 价格低于回归线
            return 'LONG'

        # 买跌条件: (Full_Y<0.25 或 MoneyFlowRatio<0.25) 且价格高于回归线
        elif ((full_y < 0.25 or money_flow < 0.25) and
              price_position > 0):  # 价格高于回归线
            return 'SHORT'

        return 'HOLD'
    
    def check_exit_conditions(self, current_price, high_price, low_price):
        """检查止盈止损条件"""
        if self.position == 0:
            return False, 0, ''
        
        if self.position == 1:  # 多头
            # 止盈检查
            profit_ratio = (high_price - self.entry_price) / self.entry_price
            if profit_ratio >= self.take_profit_long:
                exit_price = self.entry_price * (1 + self.take_profit_long)
                return True, exit_price, 'long_tp'
            
            # 止损检查
            loss_ratio = (self.entry_price - low_price) / self.entry_price
            if loss_ratio >= self.stop_loss_long:
                exit_price = self.entry_price * (1 - self.stop_loss_long)
                return True, exit_price, 'long_sl'
        
        elif self.position == -1:  # 空头
            # 止盈检查
            profit_ratio = (self.entry_price - low_price) / self.entry_price
            if profit_ratio >= self.take_profit_short:
                exit_price = self.entry_price * (1 - self.take_profit_short)
                return True, exit_price, 'short_tp'
            
            # 止损检查
            loss_ratio = (high_price - self.entry_price) / self.entry_price
            if loss_ratio >= self.stop_loss_short:
                exit_price = self.entry_price * (1 + self.stop_loss_short)
                return True, exit_price, 'short_sl'
        
        return False, 0, ''
    
    def calculate_shares(self, capital, price):
        """计算可购买股数 (港股100股为一手)"""
        max_investment = capital * 0.95  # 保留5%现金
        max_shares = int(max_investment / price)
        # 港股以100股为一手
        lots = max_shares // 100
        return lots * 100
    
    def add_monthly_capital(self, date):
        """每月增加资金"""
        current_month = date.replace(day=1)
        
        if self.last_month is None or current_month > self.last_month:
            self.last_month = current_month
            self.capital += self.monthly_addition
            return True
        
        return False
    
    def get_market_condition(self, row):
        """获取市场状态"""
        if pd.isna(row['MoneyFlowRatio']) or pd.isna(row['Full_Y']):
            return '数据缺失'
        
        money_flow = float(row['MoneyFlowRatio'])
        full_y = float(row['Full_Y'])
        
        if full_y > 0.5 and money_flow > 0.5:
            return '高值盈利区'
        elif full_y > 0.333 and full_y < 0.4:
            return '控股商控制区'
        elif full_y < 0.25 or money_flow < 0.25:
            return '强亏损区'
        else:
            return '其他区域'
    
    def get_signal_strength(self, row):
        """获取信号强度"""
        if pd.isna(row['E']) or pd.isna(row['MoneyFlowRatio']) or pd.isna(row['Full_Y']):
            return '数据缺失'
        
        e_val = float(row['E'])
        money_flow = float(row['MoneyFlowRatio'])
        full_y = float(row['Full_Y'])
        
        if e_val > 0.3 and money_flow > 0.6 and full_y > 0.6:
            return '强信号'
        elif e_val > 0 and money_flow > 0.45 and full_y > 0.45:
            return '中等信号'
        elif money_flow < 0.25 or full_y < 0.25:
            return '弱信号'
        else:
            return '无信号'
    
    def backtest_tencent_0700(self):
        """执行腾讯0700回测"""
        print(f"\n🚀 开始腾讯0700回归线策略回测...")
        print("="*80)
        print(f"💰 初始资金: {self.initial_capital:,} 港币")
        print(f"📅 每月定投: {self.monthly_addition:,} 港币")
        print(f"📊 策略: 回归线 + XY确认 + 不对称止盈止损")
        print(f"📈 多头: 止盈{self.take_profit_long*100}%, 止损{self.stop_loss_long*100}%")
        print(f"📉 空头: 止盈{self.take_profit_short*100}%, 止损{self.stop_loss_short*100}%")
        print(f"🏢 标的: {self.symbol} ({self.name})")
        print("="*80)
        
        # 获取数据
        df = self.fetch_data_from_db()
        if df is None:
            return None, None
        
        # 计算回归线
        df = self.calculate_regression_line(df)

        # 计算技术指标
        df = self.calculate_technical_indicators(df)
        
        total_trades = 0
        winning_trades = 0
        
        # 从第60天开始回测，确保指标稳定
        for i in range(60, len(df)):
            row = df.iloc[i]
            date = pd.to_datetime(row['Date']).date()
            price = float(row['Close'])
            high = float(row['High'])
            low = float(row['Low'])
            open_price = float(row['Open'])
            volume = int(row['Volume']) if not pd.isna(row['Volume']) else 0
            
            # 每月增加资金
            monthly_added = self.add_monthly_capital(date)
            
            # 检查现有持仓的止盈止损
            should_exit, exit_price, exit_type = self.check_exit_conditions(price, high, low)
            
            if should_exit:
                # 计算持仓天数
                holding_days = (date - self.entry_date).days
                
                # 计算盈亏
                if self.position == 1:  # 多头
                    profit_amount = (exit_price - self.entry_price) * self.shares
                    self.capital += self.shares * exit_price  # 卖出股票获得现金
                else:  # 空头
                    profit_amount = (self.entry_price - exit_price) * self.shares
                    self.capital -= self.shares * exit_price  # 买回股票支付现金
                
                profit_pct = profit_amount / (self.entry_price * self.shares) * 100
                
                # 记录平仓交易
                self.trades.append({
                    'trade_id': self.trade_id,
                    'action': '平仓',
                    'direction': '多头' if self.position == 1 else '空头',
                    'date': date,
                    'price': round(exit_price, 2),
                    'shares': self.shares,
                    'amount': round(self.shares * exit_price, 2),
                    'entry_date': self.entry_date,
                    'entry_price': self.entry_price,
                    'holding_days': holding_days,
                    'exit_type': exit_type,
                    'profit_amount': round(profit_amount, 2),
                    'profit_pct': round(profit_pct, 2),
                    'capital_after': round(self.capital, 2),
                    'market_condition': self.get_market_condition(row)
                })
                
                if profit_amount > 0:
                    winning_trades += 1
                
                total_trades += 1
                self.trade_id += 1
                self.position = 0
                self.entry_price = 0
                self.entry_date = None
                self.shares = 0
            
            # 如果空仓，检查开仓信号
            if self.position == 0:
                signal = self.get_trading_signal(row)
                
                if signal in ['LONG', 'SHORT']:
                    # 计算可购买股数
                    self.shares = self.calculate_shares(self.capital, price)
                    
                    if self.shares > 0:
                        if signal == 'LONG':
                            investment = self.shares * price
                            self.capital -= investment
                            self.position = 1
                            direction = '多头'
                        else:  # SHORT
                            proceeds = self.shares * price
                            self.capital += proceeds
                            self.position = -1
                            direction = '空头'
                        
                        self.entry_price = price
                        self.entry_date = date
                        
                        # 记录开仓交易
                        self.trades.append({
                            'trade_id': self.trade_id,
                            'action': '开仓',
                            'direction': direction,
                            'date': date,
                            'price': round(price, 2),
                            'shares': self.shares,
                            'amount': round(self.shares * price, 2),
                            'entry_date': date,
                            'entry_price': price,
                            'holding_days': 0,
                            'exit_type': '',
                            'profit_amount': 0,
                            'profit_pct': 0,
                            'capital_after': round(self.capital, 2),
                            'monthly_added': self.monthly_addition if monthly_added else 0,
                            'signal_strength': self.get_signal_strength(row),
                            'market_condition': self.get_market_condition(row),
                            'price_position': round(row['price_position'], 4),
                            'money_flow_ratio': round(float(row['MoneyFlowRatio']), 4) if not pd.isna(row['MoneyFlowRatio']) else 0,
                            'full_y': round(float(row['Full_Y']), 4) if not pd.isna(row['Full_Y']) else 0,
                            'e_value': round(float(row['E']), 4) if not pd.isna(row['E']) else 0
                        })
            
            # 计算当前总价值
            if self.position == 1:  # 持有股票
                stock_value = self.shares * price
                total_value = self.capital + stock_value
            elif self.position == -1:  # 做空状态
                unrealized_pnl = (self.entry_price - price) * self.shares
                total_value = self.capital + unrealized_pnl
            else:  # 空仓
                total_value = self.capital
            
            # 记录每日权益
            self.daily_records.append({
                'date': date,
                'open': open_price,
                'high': high,
                'low': low,
                'close': price,
                'volume': volume,
                'capital': round(self.capital, 2),
                'total_value': round(total_value, 2),
                'position': self.position,
                'shares': self.shares,
                'regression_line': round(row['regression_line'], 2),
                'price_position': round(row['price_position'], 4),
                'money_flow_ratio': round(float(row['MoneyFlowRatio']), 4) if not pd.isna(row['MoneyFlowRatio']) else 0,
                'full_y': round(float(row['Full_Y']), 4) if not pd.isna(row['Full_Y']) else 0,
                'e_value': round(float(row['E']), 4) if not pd.isna(row['E']) else 0,
                'signal': self.get_trading_signal(row),
                'market_condition': self.get_market_condition(row),
                'monthly_added': self.monthly_addition if monthly_added else 0
            })
        
        self.final_capital = self.capital
        self.final_total_value = self.daily_records[-1]['total_value'] if self.daily_records else self.capital
        
        print(f"\n✅ 腾讯0700回测完成!")
        print(f"📊 总交易次数: {total_trades}")
        print(f"📈 盈利次数: {winning_trades}")
        print(f"📉 亏损次数: {total_trades - winning_trades}")
        if total_trades > 0:
            print(f"🎯 胜率: {winning_trades/total_trades*100:.1f}%")
        print(f"💰 最终总价值: {self.final_total_value:,.0f} 港币")
        
        return pd.DataFrame(self.trades), pd.DataFrame(self.daily_records)

    def analyze_results(self, trades_df, daily_df):
        """分析腾讯0700回测结果"""
        print("\n📊 腾讯0700回归线策略回测结果分析")
        print("=" * 80)

        # 基本统计
        entry_trades = trades_df[trades_df['action'] == '开仓'] if len(trades_df) > 0 else pd.DataFrame()
        exit_trades = trades_df[trades_df['action'] == '平仓'] if len(trades_df) > 0 else pd.DataFrame()

        total_trades = len(entry_trades)
        final_total_value = self.final_total_value

        # 计算总投入资金 (初始资金 + 定投)
        if len(daily_df) > 0:
            total_months = len(daily_df) // 30  # 估算月数
            total_invested = self.initial_capital + total_months * self.monthly_addition
        else:
            total_invested = self.initial_capital

        net_return = final_total_value - total_invested
        net_return_rate = (net_return / total_invested) * 100

        # 年化收益率
        if len(daily_df) > 0:
            start_date = pd.to_datetime(daily_df['date'].iloc[0])
            end_date = pd.to_datetime(daily_df['date'].iloc[-1])
            years = (end_date - start_date).days / 365
            annual_return_rate = ((final_total_value / self.initial_capital) ** (1/years) - 1) * 100
        else:
            annual_return_rate = 0

        # 交易统计
        if len(exit_trades) > 0:
            winning_trades = len(exit_trades[exit_trades['profit_amount'] > 0])
            win_rate = winning_trades / len(exit_trades) * 100
            max_profit = exit_trades['profit_amount'].max()
            max_loss = exit_trades['profit_amount'].min()
            avg_profit = exit_trades['profit_amount'].mean()
            avg_holding_days = exit_trades['holding_days'].mean()

            # 按交易类型分析
            long_trades = exit_trades[exit_trades['direction'] == '多头']
            short_trades = exit_trades[exit_trades['direction'] == '空头']

            tp_trades = exit_trades[exit_trades['exit_type'].str.contains('tp')]
            sl_trades = exit_trades[exit_trades['exit_type'].str.contains('sl')]
        else:
            winning_trades = 0
            win_rate = 0
            max_profit = 0
            max_loss = 0
            avg_profit = 0
            avg_holding_days = 0
            long_trades = pd.DataFrame()
            short_trades = pd.DataFrame()
            tp_trades = pd.DataFrame()
            sl_trades = pd.DataFrame()

        # 输出结果
        print(f"💰 资金统计:")
        print(f"• 初始资金: {self.initial_capital:,} 港元")
        print(f"• 总投入资金: {total_invested:,} 港元")
        print(f"• 最终总价值: {final_total_value:,.0f} 港元")
        print(f"• 净收益: {net_return:,.0f} 港元")
        print(f"• 净收益率: {net_return_rate:.2f}%")
        print(f"• 年化收益率: {annual_return_rate:.2f}%")
        print(f"• 回归线R²: {self.r_squared:.4f}")

        if total_trades > 0:
            print(f"\n📈 交易统计:")
            print(f"• 总交易次数: {total_trades}")
            print(f"• 盈利次数: {winning_trades}")
            print(f"• 胜率: {win_rate:.1f}%")
            print(f"• 最大单笔盈利: {max_profit:,.0f} 港元")
            print(f"• 最大单笔亏损: {max_loss:,.0f} 港元")
            print(f"• 平均每笔盈亏: {avg_profit:,.0f} 港元")
            print(f"• 平均持仓天数: {avg_holding_days:.1f} 天")

            if len(long_trades) > 0:
                long_win_rate = len(long_trades[long_trades['profit_amount'] > 0]) / len(long_trades) * 100
                print(f"• 多头胜率: {long_win_rate:.1f}% ({len(long_trades)}笔)")

            if len(short_trades) > 0:
                short_win_rate = len(short_trades[short_trades['profit_amount'] > 0]) / len(short_trades) * 100
                print(f"• 空头胜率: {short_win_rate:.1f}% ({len(short_trades)}笔)")

            if len(tp_trades) > 0 and len(sl_trades) > 0:
                tp_rate = len(tp_trades) / (len(tp_trades) + len(sl_trades)) * 100
                print(f"• 止盈率: {tp_rate:.1f}% (止盈{len(tp_trades)}笔, 止损{len(sl_trades)}笔)")

        # 与其他标的对比
        print(f"\n📊 与其他标的对比:")
        print(f"• 腾讯0700年化收益率: {annual_return_rate:.2f}%")
        print(f"• HSI50年化收益率: 16.94% (参考)")
        print(f"• HK00023年化收益率: 19.39% (参考)")
        print(f"• HK0001年化收益率: 18.39% (参考)")

        if annual_return_rate > 19.39:
            print(f"• 🏆 腾讯0700表现最优，超出最佳标的 {annual_return_rate-19.39:.2f}个百分点")
        elif annual_return_rate > 16.94:
            print(f"• 📈 腾讯0700表现良好，超出HSI50 {annual_return_rate-16.94:.2f}个百分点")
        else:
            print(f"• 📉 腾讯0700表现相对较弱")

        # 创建图表
        self.create_charts(trades_df, daily_df)

        # 保存结果
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        trades_filename = f"腾讯0700回归线策略交易记录_{timestamp}.xlsx"
        daily_filename = f"腾讯0700回归线策略每日记录_{timestamp}.xlsx"

        if len(trades_df) > 0:
            trades_df.to_excel(trades_filename, index=False)
            print(f"\n✅ 交易记录已保存至: {trades_filename}")

        if len(daily_df) > 0:
            daily_df.to_excel(daily_filename, index=False)
            print(f"✅ 每日记录已保存至: {daily_filename}")

        return trades_filename, daily_filename

    def create_charts(self, trades_df, daily_df):
        """创建腾讯0700分析图表"""
        if len(daily_df) == 0:
            print("⚠️ 无数据，跳过图表生成")
            return

        print("📊 生成腾讯0700分析图表...")

        # 创建图表
        fig = plt.figure(figsize=(20, 16))

        # 1. 权益曲线
        ax1 = plt.subplot(2, 2, 1)
        daily_df['date_dt'] = pd.to_datetime(daily_df['date'])
        daily_df['total_value_万'] = daily_df['total_value'] / 10000

        plt.plot(daily_df['date_dt'], daily_df['total_value_万'], linewidth=2, color='blue', label='总价值')
        plt.axhline(y=self.initial_capital/10000, color='red', linestyle='--', alpha=0.7, label='初始资金')

        # 标记持仓期间
        long_periods = daily_df[daily_df['position'] == 1]
        short_periods = daily_df[daily_df['position'] == -1]

        if len(long_periods) > 0:
            plt.scatter(long_periods['date_dt'], long_periods['total_value_万'],
                       c='green', s=1, alpha=0.6, label='多头持仓')
        if len(short_periods) > 0:
            plt.scatter(short_periods['date_dt'], short_periods['total_value_万'],
                       c='red', s=1, alpha=0.6, label='空头持仓')

        plt.title('腾讯0700权益曲线与持仓分析', fontsize=14, fontweight='bold')
        plt.xlabel('日期')
        plt.ylabel('总价值 (万港币)')
        plt.legend()
        plt.grid(True, alpha=0.3)

        # 2. 股价与回归线
        ax2 = plt.subplot(2, 2, 2)
        plt.plot(daily_df['date_dt'], daily_df['close'], linewidth=1, color='black', alpha=0.7, label='腾讯0700股价')
        plt.plot(daily_df['date_dt'], daily_df['regression_line'], linewidth=2, color='red', label='回归线')

        plt.title('腾讯0700股价 vs 回归线', fontsize=14, fontweight='bold')
        plt.xlabel('日期')
        plt.ylabel('股价 (港币)')
        plt.legend()
        plt.grid(True, alpha=0.3)

        # 3. 价格偏离度分布
        ax3 = plt.subplot(2, 2, 3)
        plt.hist(daily_df['price_position'], bins=50, alpha=0.7, color='skyblue', edgecolor='black')
        plt.axvline(0, color='red', linestyle='-', alpha=0.7, label='回归线')
        plt.axvline(daily_df['price_position'].mean(), color='green', linestyle='--',
                   label=f'平均偏离: {daily_df["price_position"].mean():.3f}')

        plt.title('腾讯0700价格相对回归线偏离度分布', fontsize=14, fontweight='bold')
        plt.xlabel('偏离度')
        plt.ylabel('天数')
        plt.legend()
        plt.grid(True, alpha=0.3)

        # 4. 交易盈亏分布
        ax4 = plt.subplot(2, 2, 4)
        if len(trades_df) > 0 and 'profit_amount' in trades_df.columns:
            profit_data = trades_df[trades_df['action'] == '平仓']['profit_amount'].dropna()
            if len(profit_data) > 0:
                plt.hist(profit_data, bins=30, alpha=0.7, color='lightgreen', edgecolor='black')
                plt.axvline(0, color='red', linestyle='-', alpha=0.7)
                plt.axvline(profit_data.mean(), color='blue', linestyle='--',
                           label=f'平均盈亏: {profit_data.mean():.0f}港币')

                plt.title('腾讯0700交易盈亏分布', fontsize=14, fontweight='bold')
                plt.xlabel('盈亏金额 (港币)')
                plt.ylabel('交易次数')
                plt.legend()
                plt.grid(True, alpha=0.3)

        plt.tight_layout()

        # 保存图表
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        chart_filename = f"腾讯0700回归线策略分析图表_{timestamp}.png"
        plt.savefig(chart_filename, dpi=300, bbox_inches='tight')
        print(f"✅ 图表已保存至: {chart_filename}")

        plt.show()

        return chart_filename

def main():
    """主函数"""
    print("🏢 腾讯0700回归线策略回测系统")
    print("=" * 80)
    print("💰 初始资金: 30,000港币")
    print("📅 每月定投: 2,000港币")
    print("📊 分析周期: 20年历史数据")
    print("🏢 标的股票: 0700.HK (腾讯控股)")
    print("🎯 核心策略: 线性回归线 + XY值确认")
    print("📈 多头条件: E>0 且 MoneyFlowRatio>0.45 且 Full_Y>0.45 且价格低于回归线")
    print("📉 空头条件: (Full_Y<0.25 或 MoneyFlowRatio<0.25) 且价格高于回归线")
    print("🎯 止盈止损: 多头(1.6%/0.8%), 空头(0.8%/1.6%)")
    print("🔄 复利计算: 启用")
    print("📊 交易单位: 100股/手")
    print("💾 数据源: 数据库 stock_0700_hk 表")

    # 创建回测器
    backtester = Tencent0700Strategy()

    # 执行回测
    trades_df, daily_df = backtester.backtest_tencent_0700()

    if trades_df is not None and daily_df is not None:
        # 分析结果
        backtester.analyze_results(trades_df, daily_df)
    else:
        print("❌ 回测失败，请检查数据库连接和数据")

if __name__ == "__main__":
    main()
