-- 数据库空间解决方案
-- =====================
-- 解决 "database full" 问题的完整SQL脚本
-- 执行时间: 2025-07-20

USE finance;

-- 1. 检查当前数据库大小
SELECT 
    'Database Size Check' as operation,
    ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) as 'Current_Size_MB'
FROM information_schema.tables 
WHERE table_schema = 'finance';

-- 2. 查看最大的表
SELECT 
    'Top 10 Largest Tables' as operation,
    table_name,
    ROUND(((data_length + index_length) / 1024 / 1024), 2) as 'Size_MB',
    table_rows as 'Row_Count'
FROM information_schema.TABLES 
WHERE table_schema = 'finance'
ORDER BY (data_length + index_length) DESC
LIMIT 10;

-- 3. 清理二进制日志 (释放日志空间)
-- 清理3天前的二进制日志
PURGE BINARY LOGS BEFORE DATE_SUB(NOW(), INTERVAL 3 DAY);

-- 4. 优化所有表 (回收碎片空间)
-- 注意: 这会逐个优化表，可能需要一些时间

-- 优化最大的几个表
OPTIMIZE TABLE hkhsi50;
OPTIMIZE TABLE stock_0388_hk;
OPTIMIZE TABLE stock_0016_hk;
OPTIMIZE TABLE stock_bac;
OPTIMIZE TABLE stock_0941_hk;
OPTIMIZE TABLE stock_0002_hk;
OPTIMIZE TABLE stock_amzn;
OPTIMIZE TABLE stock_600519_ss;
OPTIMIZE TABLE stock_aapl;
OPTIMIZE TABLE stock_jnj;

-- 5. 清理旧数据 (如果需要释放更多空间)
-- 注意: 请根据实际需求调整日期范围

-- 示例: 删除2年前的数据 (请谨慎使用)
-- DELETE FROM stock_600887_ss WHERE date < DATE_SUB(NOW(), INTERVAL 2 YEAR);
-- DELETE FROM stock_600036_ss WHERE date < DATE_SUB(NOW(), INTERVAL 2 YEAR);

-- 6. 重建表以回收空间 (对大表谨慎使用)
-- 注意: 这会锁定表，请在维护窗口执行

-- 示例: 重建最大的表
-- ALTER TABLE hkhsi50 ENGINE=InnoDB;

-- 7. 检查并删除重复数据
-- 查找可能的重复记录
SELECT 
    'Duplicate Check' as operation,
    table_name,
    'Check for duplicates manually' as suggestion
FROM information_schema.tables 
WHERE table_schema = 'finance'
AND table_name LIKE 'stock_%';

-- 8. 压缩表 (如果使用InnoDB)
-- 启用行格式压缩可以节省空间
-- ALTER TABLE large_table_name ROW_FORMAT=COMPRESSED;

-- 9. 检查索引使用情况
-- 删除未使用的索引可以节省空间
SELECT 
    'Index Usage Check' as operation,
    table_name,
    index_name,
    ROUND(((index_length) / 1024 / 1024), 2) as 'Index_Size_MB'
FROM information_schema.tables t
JOIN information_schema.statistics s ON t.table_name = s.table_name
WHERE t.table_schema = 'finance'
AND s.table_schema = 'finance'
ORDER BY index_length DESC
LIMIT 20;

-- 10. 设置自动清理 (创建事件调度器)
-- 每月自动优化表
DELIMITER $$
CREATE EVENT IF NOT EXISTS auto_optimize_tables
ON SCHEDULE EVERY 1 MONTH
STARTS CURRENT_TIMESTAMP
DO
BEGIN
    -- 优化主要的股票表
    DECLARE done INT DEFAULT FALSE;
    DECLARE table_name VARCHAR(255);
    DECLARE table_cursor CURSOR FOR 
        SELECT t.table_name 
        FROM information_schema.tables t
        WHERE t.table_schema = 'finance'
        AND t.table_name LIKE 'stock_%'
        ORDER BY (t.data_length + t.index_length) DESC
        LIMIT 10;
    
    DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;
    
    OPEN table_cursor;
    
    optimize_loop: LOOP
        FETCH table_cursor INTO table_name;
        IF done THEN
            LEAVE optimize_loop;
        END IF;
        
        SET @sql = CONCAT('OPTIMIZE TABLE `', table_name, '`');
        PREPARE stmt FROM @sql;
        EXECUTE stmt;
        DEALLOCATE PREPARE stmt;
        
    END LOOP;
    
    CLOSE table_cursor;
END$$
DELIMITER ;

-- 启用事件调度器
SET GLOBAL event_scheduler = ON;

-- 11. 检查修复后的大小
SELECT 
    'After Cleanup Size' as operation,
    ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) as 'New_Size_MB'
FROM information_schema.tables 
WHERE table_schema = 'finance';

-- 12. 显示空间使用建议
SELECT 
    'Space Management Tips' as operation,
    'Consider the following:' as suggestion
UNION ALL
SELECT '', '1. Regular OPTIMIZE TABLE maintenance'
UNION ALL
SELECT '', '2. Archive old data to separate database'
UNION ALL
SELECT '', '3. Monitor disk space regularly'
UNION ALL
SELECT '', '4. Use data compression for large tables'
UNION ALL
SELECT '', '5. Remove unused indexes'
UNION ALL
SELECT '', '6. Set up automated cleanup jobs';

-- 13. 紧急空间释放 (如果空间严重不足)
-- 只在紧急情况下使用

-- 删除最老的数据 (示例: 删除3年前的数据)
-- 请根据实际情况调整
/*
DELETE FROM stock_600887_ss WHERE date < '2022-01-01' LIMIT 1000;
DELETE FROM stock_600036_ss WHERE date < '2022-01-01' LIMIT 1000;
DELETE FROM stock_600000_ss WHERE date < '2022-01-01' LIMIT 1000;
DELETE FROM stock_000001_sz WHERE date < '2022-01-01' LIMIT 1000;
DELETE FROM stock_000858_sz WHERE date < '2022-01-01' LIMIT 1000;
*/

-- 14. 检查磁盘空间 (系统级别)
-- 这需要在操作系统级别检查
-- df -h (Linux/Mac)
-- dir (Windows)

-- 15. MySQL配置优化建议
SELECT 
    'MySQL Config Suggestions' as operation,
    'Check these variables:' as suggestion
UNION ALL
SELECT '', 'innodb_buffer_pool_size'
UNION ALL
SELECT '', 'tmp_table_size'
UNION ALL
SELECT '', 'max_heap_table_size'
UNION ALL
SELECT '', 'query_cache_size'
UNION ALL
SELECT '', 'innodb_log_file_size';

-- 显示当前重要配置
SHOW VARIABLES LIKE 'innodb_buffer_pool_size';
SHOW VARIABLES LIKE 'tmp_table_size';
SHOW VARIABLES LIKE 'max_heap_table_size';

-- 完成
SELECT 
    'Database Cleanup Complete' as status,
    NOW() as completion_time,
    'Check the results above' as next_step;
