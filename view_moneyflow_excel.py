#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
查看MoneyFlow Excel文件
=====================
"""

import pandas as pd
import os

def view_moneyflow_excel():
    """查看MoneyFlow Excel文件"""
    
    # 查找最新的MoneyFlow Excel文件
    excel_files = [f for f in os.listdir('.') if 'MoneyFlow数据' in f and f.endswith('.xlsx')]
    
    if not excel_files:
        print("❌ 未找到MoneyFlow Excel文件")
        return
    
    # 按修改时间排序，取最新的
    latest_file = max(excel_files, key=lambda x: os.path.getmtime(x))
    
    print(f"📄 查看文件: {latest_file}")
    print("=" * 80)
    
    try:
        # 读取Excel文件
        df = pd.read_excel(latest_file)
        
        print(f"📊 文件信息:")
        print(f"   记录数量: {len(df):,} 条")
        print(f"   字段数量: {len(df.columns)} 个")
        print(f"   文件大小: {os.path.getsize(latest_file):,} 字节")
        
        print(f"\n📋 字段列表:")
        for i, col in enumerate(df.columns, 1):
            print(f"   {i:2d}. {col}")
        
        print(f"\n📊 关键指标统计:")
        print(f"   价格范围: {df['Close'].min():.2f} - {df['Close'].max():.2f} 港元")
        print(f"   MFI范围: {df['MFI'].min():.1f} - {df['MFI'].max():.1f}")
        print(f"   MoneyFlowRatio范围: {df['MoneyFlowRatio'].min():.4f} - {df['MoneyFlowRatio'].max():.4f}")
        print(f"   Y值范围: {df['Y_Value'].min():.4f} - {df['Y_Value'].max():.4f}")
        print(f"   X值范围: {df['X_Value'].min():.4f} - {df['X_Value'].max():.4f}")
        
        print(f"\n🎯 交易信号统计:")
        signal_counts = df['TradingSignal'].value_counts().sort_index()
        for signal, count in signal_counts.items():
            signal_name = "🟢 做多" if signal == 1 else "🔴 做空" if signal == -1 else "⚪ 观望"
            percentage = count / len(df) * 100
            print(f"   {signal_name}: {count}次 ({percentage:.1f}%)")
        
        print(f"\n💰 MFI分布:")
        mfi_oversold = len(df[df['MFI'] < 20])
        mfi_normal = len(df[(df['MFI'] >= 20) & (df['MFI'] <= 80)])
        mfi_overbought = len(df[df['MFI'] > 80])
        
        print(f"   超卖 (MFI<20): {mfi_oversold}次 ({mfi_oversold/len(df)*100:.1f}%)")
        print(f"   正常 (20≤MFI≤80): {mfi_normal}次 ({mfi_normal/len(df)*100:.1f}%)")
        print(f"   超买 (MFI>80): {mfi_overbought}次 ({mfi_overbought/len(df)*100:.1f}%)")
        
        print(f"\n📅 最新10条记录:")
        print("-" * 120)
        
        # 显示最新10条记录的关键字段
        recent_df = df.tail(10)[['Date', 'Close', 'MFI', 'MoneyFlowRatio', 'Y_Value', 'X_Value', 'E_Value', 'TradingSignal']]
        
        # 格式化显示
        for _, row in recent_df.iterrows():
            signal_text = "做多" if row['TradingSignal'] == 1 else "做空" if row['TradingSignal'] == -1 else "观望"
            print(f"   {row['Date']} | 价格:{row['Close']:6.2f} | MFI:{row['MFI']:5.1f} | "
                  f"资金流比率:{row['MoneyFlowRatio']:7.4f} | Y:{row['Y_Value']:.4f} | "
                  f"X:{row['X_Value']:.4f} | E:{row['E_Value']:6.2f} | {signal_text}")
        
        print(f"\n📊 MoneyFlowRatio详细分析:")
        print(f"   平均值: {df['MoneyFlowRatio'].mean():.4f}")
        print(f"   中位数: {df['MoneyFlowRatio'].median():.4f}")
        print(f"   标准差: {df['MoneyFlowRatio'].std():.4f}")
        
        # MoneyFlowRatio分布
        mfr_high = len(df[df['MoneyFlowRatio'] > 2.0])
        mfr_normal = len(df[(df['MoneyFlowRatio'] >= 0.5) & (df['MoneyFlowRatio'] <= 2.0)])
        mfr_low = len(df[df['MoneyFlowRatio'] < 0.5])
        
        print(f"\n💰 MoneyFlowRatio分布:")
        print(f"   高比率 (>2.0): {mfr_high}次 ({mfr_high/len(df)*100:.1f}%)")
        print(f"   正常比率 (0.5-2.0): {mfr_normal}次 ({mfr_normal/len(df)*100:.1f}%)")
        print(f"   低比率 (<0.5): {mfr_low}次 ({mfr_low/len(df)*100:.1f}%)")
        
        print(f"\n🎯 关键发现:")
        
        # 找出MFI极值
        max_mfi_row = df.loc[df['MFI'].idxmax()]
        min_mfi_row = df.loc[df['MFI'].idxmin()]
        
        print(f"   最高MFI: {max_mfi_row['MFI']:.1f} (日期: {max_mfi_row['Date']}, 价格: {max_mfi_row['Close']:.2f})")
        print(f"   最低MFI: {min_mfi_row['MFI']:.1f} (日期: {min_mfi_row['Date']}, 价格: {min_mfi_row['Close']:.2f})")
        
        # 找出MoneyFlowRatio极值
        max_mfr_row = df.loc[df['MoneyFlowRatio'].idxmax()]
        min_mfr_row = df.loc[df['MoneyFlowRatio'].idxmin()]
        
        print(f"   最高资金流比率: {max_mfr_row['MoneyFlowRatio']:.4f} (日期: {max_mfr_row['Date']}, MFI: {max_mfr_row['MFI']:.1f})")
        print(f"   最低资金流比率: {min_mfr_row['MoneyFlowRatio']:.4f} (日期: {min_mfr_row['Date']}, MFI: {min_mfr_row['MFI']:.1f})")
        
        # 信号与MFI的关系
        long_signals = df[df['TradingSignal'] == 1]
        short_signals = df[df['TradingSignal'] == -1]
        
        if len(long_signals) > 0:
            print(f"   做多信号平均MFI: {long_signals['MFI'].mean():.1f}")
            print(f"   做多信号平均资金流比率: {long_signals['MoneyFlowRatio'].mean():.4f}")
        
        if len(short_signals) > 0:
            print(f"   做空信号平均MFI: {short_signals['MFI'].mean():.1f}")
            print(f"   做空信号平均资金流比率: {short_signals['MoneyFlowRatio'].mean():.4f}")
        
    except Exception as e:
        print(f"❌ 读取文件失败: {e}")

def main():
    """主函数"""
    view_moneyflow_excel()

if __name__ == "__main__":
    main()
