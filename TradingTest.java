/**
 * 交易止盈止损计算器测试版
 */

public class TradingTest {
    
    // 风险回报比参数 (1:2)
    private static final double STOP_LOSS_RATE = 0.0006;   // 止损 0.06%
    private static final double TAKE_PROFIT_RATE = 0.0012; // 止盈 0.12%
    
    /**
     * 计算多头止盈止损价位
     */
    public static void calculateLong(double entryPrice) {
        double takeProfitPrice = entryPrice * (1 + TAKE_PROFIT_RATE);
        double stopLossPrice = entryPrice * (1 - STOP_LOSS_RATE);
        
        System.out.println("=== 多头交易 ===");
        System.out.println("入场价格: " + entryPrice);
        System.out.println("止盈价格: " + String.format("%.3f", takeProfitPrice));
        System.out.println("止损价格: " + String.format("%.3f", stopLossPrice));
        System.out.println("计算公式:");
        System.out.println("  止盈 = " + entryPrice + " * (1 + 0.0012) = " + String.format("%.3f", takeProfitPrice));
        System.out.println("  止损 = " + entryPrice + " * (1 - 0.0006) = " + String.format("%.3f", stopLossPrice));
    }
    
    /**
     * 计算空头止盈止损价位
     */
    public static void calculateShort(double entryPrice) {
        double takeProfitPrice = entryPrice * (1 - TAKE_PROFIT_RATE);
        double stopLossPrice = entryPrice * (1 + STOP_LOSS_RATE);
        
        System.out.println("=== 空头交易 ===");
        System.out.println("入场价格: " + entryPrice);
        System.out.println("止盈价格: " + String.format("%.3f", takeProfitPrice));
        System.out.println("止损价格: " + String.format("%.3f", stopLossPrice));
        System.out.println("计算公式:");
        System.out.println("  止盈 = " + entryPrice + " * (1 - 0.0012) = " + String.format("%.3f", takeProfitPrice));
        System.out.println("  止损 = " + entryPrice + " * (1 + 0.0006) = " + String.format("%.3f", stopLossPrice));
    }
    
    public static void main(String[] args) {
        System.out.println("🎯 交易止盈止损价位计算器");
        System.out.println("========================");
        System.out.println("风险回报比: 1:2 (止损0.06%, 止盈0.12%)");
        System.out.println();
        
        // 测试您提到的价格
        double testPrice1 = 12.420;
        calculateLong(testPrice1);
        System.out.println();
        calculateShort(testPrice1);
        System.out.println();
        
        // 测试其他价格
        double testPrice2 = 10.000;
        System.out.println("--- 其他测试价格 ---");
        calculateLong(testPrice2);
        System.out.println();
        calculateShort(testPrice2);
        System.out.println();
        
        // 验证风险回报比
        System.out.println("🔍 风险回报比验证:");
        System.out.println("止盈比例: " + (TAKE_PROFIT_RATE * 100) + "%");
        System.out.println("止损比例: " + (STOP_LOSS_RATE * 100) + "%");
        System.out.println("风险回报比: " + (TAKE_PROFIT_RATE / STOP_LOSS_RATE) + ":1");
        System.out.println("✅ 完美的1:2风险回报比！");
    }
}
