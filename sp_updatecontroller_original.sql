CREATE DEFINER=`root`@`%` PROCEDURE `sp_updatecontroller`(

    IN tablename VARCHAR(64),

     OUT result_k DECIMAL(20,6) -- 新增OUT参数

)
BEGIN

    DECLARE col_exists INT DEFAULT 0;



    -- 检查controller列是否存在（原逻辑不变）

    SET @sql = CONCAT(

        'SELECT COUNT(*) INTO @col_exists FROM information_schema.COLUMNS ',

        'WHERE TABLE_SCHEMA=DATABASE() AND TABLE_NAME=\'', tablename, '\' AND COLUMN_NAME=\'controller\''

    );

    PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;

    SET col_exists = @col_exists;



    IF col_exists = 0 THEN

        SET @sql = CONCAT('ALTER TABLE `', tablename, '` ADD COLUMN `controller` INT');

        PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;

    END IF;



    -- 更新controller字段（原逻辑不变）

    SET @sql = CONCAT(

        'UPDATE `', 

        tablename,

        '` SET controller = CASE WHEN close < midprice THEN 0 WHEN close > midprice THEN 1 ELSE 3 END'

    );

    PREPARE stmt FROM @sql;

    EXECUTE stmt;

    DEALLOCATE PREPARE stmt;



    -- 计算k值并存入OUT参数

    SET @sql = CONCAT(

        'SELECT SUM(CASE WHEN controller = 1 THEN 1 ELSE 0 END) / NULLIF(COUNT(*), 0) INTO @k_value ',

        'FROM `', tablename, '`'

    );

    PREPARE stmt FROM @sql;

    EXECUTE stmt;

    DEALLOCATE PREPARE stmt;

    

    SET result_k = @k_value;  -- 将结果赋值给OUT参数

END