-- E字段计算：E = (8*MFI/100 - 3)*Full_Y - 3*MFI/100 + 1

-- 方案1：假设MFI是常数值（请根据实际情况修改MFI值）
-- 示例：假设MFI = 50

DELIMITER $$

CREATE PROCEDURE `sp_calculate_e_field`(
    IN tablename VARCHAR(64),
    IN mfi_value DECIMAL(10,3) DEFAULT 50.0
)
BEGIN
    DECLARE coefficient1 DECIMAL(10,6);
    DECLARE coefficient2 DECIMAL(10,6);

    -- 计算系数
    -- coefficient1 = (8*MFI/100 - 3)
    SET coefficient1 = (8 * mfi_value / 100 - 3);

    -- coefficient2 = -3*MFI/100 + 1
    SET coefficient2 = (-3 * mfi_value / 100 + 1);

    SELECT CONCAT('MFI值: ', mfi_value) AS mfi_info;
    SELECT CONCAT('系数1 (8*MFI/100-3): ', coefficient1) AS coeff1_info;
    SELECT CONCAT('系数2 (-3*MFI/100+1): ', coefficient2) AS coeff2_info;

    -- 更新E字段
    -- E = coefficient1 * Full_Y + coefficient2
    SET @sql = CONCAT(
        'UPDATE `', tablename, '` ',
        'SET E = ROUND(', coefficient1, ' * Full_Y + ', coefficient2, ', 6) ',
        'WHERE Full_Y IS NOT NULL'
    );

    PREPARE stmt FROM @sql;
    EXECUTE stmt;
    DEALLOCATE PREPARE stmt;

    SELECT 'E字段计算完成' AS e_calculation_status;

    -- 显示计算结果统计
    SET @sql = CONCAT(
        'SELECT ',
        '    COUNT(*) as total_records, ',
        '    COUNT(CASE WHEN E IS NOT NULL THEN 1 END) as e_calculated, ',
        '    MIN(E) as min_e, ',
        '    MAX(E) as max_e, ',
        '    AVG(E) as avg_e ',
        'FROM `', tablename, '`'
    );
    PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;

END$$

DELIMITER ;

-- 方案2：如果MFI是表中的字段
-- UPDATE your_table_name
-- SET E = ROUND((8*MFI/100 - 3)*Full_Y - 3*MFI/100 + 1, 6)
-- WHERE Full_Y IS NOT NULL AND MFI IS NOT NULL;

-- 方案3：集成到您的主存储过程中
-- 在sp_updatecontroller_enhanced中添加以下代码：

    -- 7. 更新E字段
    -- 假设MFI是常数，您可以根据需要修改这个值
    SET @mfi_value = 50.0;  -- 请根据实际情况设置MFI值

    -- 计算系数
    SET @coefficient1 = (8 * @mfi_value / 100 - 3);  -- (8*MFI/100 - 3)
    SET @coefficient2 = (-3 * @mfi_value / 100 + 1); -- -3*MFI/100 + 1

    SELECT CONCAT('计算E字段，MFI=', @mfi_value,
                  ', 系数1=', @coefficient1,
                  ', 系数2=', @coefficient2) AS e_calculation_info;

    SET @sql = CONCAT(
        'UPDATE `', tablename, '` ',
        'SET E = ROUND(', @coefficient1, ' * Full_Y + ', @coefficient2, ', 6) ',
        'WHERE Full_Y IS NOT NULL'
    );
    PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;
    SELECT 'E字段计算完成' AS e_update_status;

-- 方案4：如果MFI需要作为存储过程参数传入
DELIMITER $$

CREATE PROCEDURE `sp_updatecontroller_with_mfi`(
    IN tablename VARCHAR(64),
    IN mfi_value DECIMAL(10,3),
    OUT result_k DECIMAL(20,6)
)
BEGIN
    DECLARE col_exists INT DEFAULT 0;
    DECLARE v_start_time DATETIME DEFAULT NOW();
    DECLARE coefficient1 DECIMAL(10,6);
    DECLARE coefficient2 DECIMAL(10,6);

    -- [前面的代码保持不变：midprice更新、controller更新、Full_Y更新]

    -- 计算E字段的系数
    SET coefficient1 = (8 * mfi_value / 100 - 3);
    SET coefficient2 = (-3 * mfi_value / 100 + 1);

    -- 更新E字段
    SET @sql = CONCAT(
        'UPDATE `', tablename, '` ',
        'SET E = ROUND(', coefficient1, ' * Full_Y + ', coefficient2, ', 6) ',
        'WHERE Full_Y IS NOT NULL'
    );
    PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;

    -- [后续代码保持不变]

END$$

DELIMITER ;

-- 验证E字段计算的查询
SELECT
    ROW_NUMBER() OVER (ORDER BY date, id) as row_num,
    controller,
    Full_Y,
    E,
    -- 手动验证计算（假设MFI=50）
    ROUND((8*50/100 - 3)*Full_Y - 3*50/100 + 1, 6) as 验证E值
FROM your_table_name
ORDER BY date, id
LIMIT 10;
