#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于1-X的策略回测
================
使用1-X（散户买跌概率）作为新的X值
保持原来的策略规则: Y>0.43且X>0.43看涨
"""

import mysql.connector
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

class OneMinusXStrategy:
    def __init__(self):
        """初始化基于1-X的策略"""
        self.host = "localhost"
        self.user = "root"
        self.password = "12345678"
        self.database = "finance"
        
        self.initial_capital = 30000
        self.monthly_addition = 1000
        self.max_position_ratio = 0.35  # 35%仓位
        
        # 策略参数
        self.take_profit_long = 0.016   # 多头止盈1.6%
        self.stop_loss_long = 0.008     # 多头止损0.8%
        self.take_profit_short = 0.008  # 空头止盈0.8%
        self.stop_loss_short = 0.016    # 空头止损1.6%
        
        # 原始策略参数 (保持不变)
        self.y_long_threshold = 0.43    # 看涨Y门槛
        self.x_long_threshold = 0.43    # 看涨X门槛 (现在X=1-原X)
        
        self.trades = []
        self.df = None
        
    def connect_mysql(self):
        """连接MySQL数据库"""
        print("🔗 连接MySQL数据库...")
        try:
            self.conn = mysql.connector.connect(
                host=self.host,
                user=self.user,
                password=self.password,
                database=self.database
            )
            print(f"✅ 成功连接到MySQL数据库: {self.database}")
            return True
        except mysql.connector.Error as e:
            print(f"❌ MySQL连接失败: {e}")
            return False
    
    def load_hkhsi50_data(self):
        """加载hkhsi50数据"""
        print(f"📊 加载hkhsi50数据...")
        
        try:
            query = """
                SELECT * FROM hkhsi50 
                ORDER BY Date ASC
            """
            
            self.df = pd.read_sql_query(query, self.conn)
            
            # 转换Decimal类型为float
            for col in self.df.columns:
                if self.df[col].dtype == 'object':
                    try:
                        self.df[col] = pd.to_numeric(self.df[col], errors='ignore')
                    except:
                        pass
            
            print(f"✅ 成功加载 {len(self.df):,} 条记录")
            print(f"📅 数据范围: {self.df['Date'].min()} 至 {self.df['Date'].max()}")
            
            # 数据预处理
            self.preprocess_data()
            
            return True
            
        except Exception as e:
            print(f"❌ 数据加载失败: {e}")
            return False
    
    def preprocess_data(self):
        """数据预处理"""
        print("   🔧 数据预处理...")
        
        # 确保日期格式正确
        self.df['Date'] = pd.to_datetime(self.df['Date'])
        self.df = self.df.sort_values('Date').reset_index(drop=True)
        
        # 计算技术指标
        self.calculate_indicators()
        
        print(f"   ✅ 预处理完成")
    
    def calculate_indicators(self):
        """计算技术指标"""
        print("   📊 计算基于1-X的指标...")
        
        # 移动平均线
        self.df['ma_20'] = self.df['Close'].rolling(window=20).mean()
        self.df['ma_60'] = self.df['Close'].rolling(window=60).mean()
        
        # 计算基础Y指标 (保持原来的定义)
        self.df['midprice'] = (self.df['High'] + self.df['Low']) / 2
        self.df['controller'] = (self.df['Close'] > self.df['midprice']).astype(int)
        self.df['cumulative_controller'] = self.df['controller'].cumsum()
        self.df['row_number'] = range(1, len(self.df) + 1)
        self.df['Y'] = self.df['cumulative_controller'] / self.df['row_number']  # 控股商控制比例
        
        # 计算基于资金流的散户情绪指标
        self.calculate_money_flow_sentiment()
        
        # 重新定义X为1-原X (散户买跌概率)
        self.df['X_original'] = self.df['X']  # 保存原始X值
        self.df['X'] = 1 - self.df['X_original']  # 新X = 1 - 原X (散户买跌概率)
        
        print("   ✅ 基于1-X的指标计算完成")
        
        # 输出统计信息
        print(f"   📊 新X值统计 (1-原X):")
        print(f"      • 新X值范围: {self.df['X'].min():.3f} ~ {self.df['X'].max():.3f}")
        print(f"      • 新X值均值: {self.df['X'].mean():.3f}")
        print(f"      • Y>0.43且X>0.43天数: {((self.df['Y'] > 0.43) & (self.df['X'] > 0.43)).sum()}天")
        print(f"      • 占总天数比例: {((self.df['Y'] > 0.43) & (self.df['X'] > 0.43)).sum()/len(self.df)*100:.1f}%")
    
    def calculate_money_flow_sentiment(self):
        """计算基于资金流的散户情绪指标"""
        print("   💰 计算资金流散户情绪指标...")
        
        # 1. 计算典型价格 (Typical Price)
        self.df['typical_price'] = (self.df['High'] + self.df['Low'] + self.df['Close']) / 3
        
        # 2. 计算资金流量 (Money Flow)
        self.df['money_flow'] = self.df['typical_price'] * self.df['Volume']
        
        # 3. 计算价格变化方向
        self.df['price_change'] = self.df['typical_price'].diff()
        
        # 4. 分类资金流 (正向和负向)
        self.df['positive_money_flow'] = np.where(self.df['price_change'] > 0, self.df['money_flow'], 0)
        self.df['negative_money_flow'] = np.where(self.df['price_change'] < 0, self.df['money_flow'], 0)
        
        # 5. 计算14日资金流指标 (Money Flow Index - MFI)
        period = 14
        self.df['positive_mf_sum'] = self.df['positive_money_flow'].rolling(period).sum()
        self.df['negative_mf_sum'] = self.df['negative_money_flow'].rolling(period).sum()
        
        # 避免除零错误
        self.df['money_flow_ratio'] = self.df['positive_mf_sum'] / (self.df['negative_mf_sum'] + 1e-10)
        self.df['mfi'] = 100 - (100 / (1 + self.df['money_flow_ratio']))
        
        # 6. 计算成交量加权平均价格 (VWAP)
        self.df['vwap'] = (self.df['typical_price'] * self.df['Volume']).rolling(20).sum() / self.df['Volume'].rolling(20).sum()
        
        # 7. 计算相对成交量
        self.df['volume_ma'] = self.df['Volume'].rolling(20).mean()
        self.df['relative_volume'] = self.df['Volume'] / self.df['volume_ma']
        
        # 8. 计算价格相对于VWAP的位置
        self.df['price_vs_vwap'] = self.df['Close'] / self.df['vwap']
        
        # 9. 计算价格在近期区间的位置
        self.df['price_percentile'] = self.df['Close'].rolling(20).apply(
            lambda x: (x.iloc[-1] - x.min()) / (x.max() - x.min()) if x.max() != x.min() else 0.5
        )
        
        # 10. 综合计算散户买升资金流概率 (原X)
        self.df['X'] = (
            # MFI权重40% - 资金流入越多，散户买升概率越高
            (self.df['mfi'] / 100) * 0.40 +
            
            # 价格相对VWAP权重25% - 价格越高于VWAP，散户越倾向追涨
            np.clip((self.df['price_vs_vwap'] - 0.98) / 0.04, 0, 1) * 0.25 +
            
            # 相对成交量权重20% - 成交量放大时散户更活跃
            np.clip((self.df['relative_volume'] - 0.5) / 1.5, 0, 1) * 0.20 +
            
            # 价格位置权重15% - 价格越接近高位，散户越倾向买升
            self.df['price_percentile'] * 0.15
        )
        
        # 确保X在0-1之间
        self.df['X'] = np.clip(self.df['X'], 0, 1)
        
        print("   ✅ 资金流散户情绪指标计算完成")
    
    def check_entry_conditions(self, row):
        """检查入场条件 - 使用原始策略规则但X=1-原X"""
        y_value = row['Y']
        x_value = row['X']  # 现在X = 1 - 原X (散户买跌概率)
        
        if pd.isna(y_value) or pd.isna(x_value):
            return 0
        
        # 原始策略规则: Y>0.43且X>0.43看涨
        # 现在X=散户买跌概率，所以Y>0.43且X>0.43意味着:
        # 控股商控制强且散户大量买跌时，做多
        if y_value > self.y_long_threshold and x_value > self.x_long_threshold:
            return 1  # 多头
        
        # 可以添加看跌条件 (可选)
        # 当Y<0.25或X<0.25时做空
        elif y_value < 0.25 or x_value < 0.25:
            return -1  # 空头
        
        return 0  # 观望
    
    def add_monthly_capital(self, date, capital):
        """每月增加资金"""
        last_month = getattr(self, 'last_month', None)
        current_month = date.replace(day=1)
        
        if last_month is None or current_month > last_month:
            self.last_month = current_month
            return capital + self.monthly_addition
        
        return capital
    
    def run_one_minus_x_backtest(self):
        """运行基于1-X的回测"""
        print(f"\n🎯 开始基于1-X的策略回测...")
        print(f"   📊 新策略逻辑: Y>0.43且X>0.43看涨 (X=散户买跌概率)")
        print(f"   💡 含义: 控股商控制强且散户大量买跌时做多")
        
        capital = self.initial_capital
        position = 0
        entry_price = 0
        entry_date = None
        entry_y = 0
        entry_x = 0
        take_profit_price = 0
        stop_loss_price = 0
        
        max_capital = capital
        max_drawdown = 0
        
        start_index = max(60, 0)
        
        for i in range(start_index, len(self.df)):
            row = self.df.iloc[i]
            date = row['Date']
            
            # 每月增加资金
            capital = self.add_monthly_capital(date, capital)
            
            # 更新最大资金和回撤
            if capital > max_capital:
                max_capital = capital
            
            current_drawdown = (max_capital - capital) / max_capital
            if current_drawdown > max_drawdown:
                max_drawdown = current_drawdown
            
            # 检查平仓条件
            if position != 0:
                should_exit = False
                exit_reason = ""
                exit_price = row['Close']
                
                if position == 1:  # 多头
                    if row['High'] >= take_profit_price:
                        should_exit = True
                        exit_reason = "止盈"
                        exit_price = take_profit_price
                    elif row['Low'] <= stop_loss_price:
                        should_exit = True
                        exit_reason = "止损"
                        exit_price = stop_loss_price
                
                elif position == -1:  # 空头
                    if row['Low'] <= take_profit_price:
                        should_exit = True
                        exit_reason = "止盈"
                        exit_price = take_profit_price
                    elif row['High'] >= stop_loss_price:
                        should_exit = True
                        exit_reason = "止损"
                        exit_price = stop_loss_price
                
                if should_exit:
                    position_size = capital * self.max_position_ratio
                    
                    if position == 1:
                        profit_ratio = (exit_price - entry_price) / entry_price
                    else:
                        profit_ratio = (entry_price - exit_price) / entry_price
                    
                    profit = position_size * profit_ratio
                    capital += profit
                    
                    holding_days = (date - entry_date).days
                    
                    self.trades.append({
                        'entry_date': entry_date,
                        'exit_date': date,
                        'direction': '看涨' if position == 1 else '看跌',
                        'entry_price': entry_price,
                        'exit_price': exit_price,
                        'profit': profit,
                        'profit_ratio': profit_ratio,
                        'holding_days': holding_days,
                        'exit_reason': exit_reason,
                        'entry_y': entry_y,
                        'entry_x': entry_x,
                        'capital_after': capital
                    })
                    
                    position = 0
            
            # 检查开仓条件
            if position == 0:
                signal = self.check_entry_conditions(row)
                
                if signal != 0:
                    position = signal
                    entry_price = row['Close']
                    entry_date = date
                    entry_y = row['Y']
                    entry_x = row['X']
                    
                    if position == 1:
                        take_profit_price = entry_price * (1 + self.take_profit_long)
                        stop_loss_price = entry_price * (1 - self.stop_loss_long)
                    else:
                        take_profit_price = entry_price * (1 - self.take_profit_short)
                        stop_loss_price = entry_price * (1 + self.stop_loss_short)
        
        self.final_capital = capital
        self.max_drawdown = max_drawdown
        
        print(f"✅ 基于1-X的回测完成！")
        print(f"💰 最终资金: {self.final_capital:,.2f}港元")
        print(f"📊 总交易次数: {len(self.trades)}笔")
        print(f"📉 最大回撤: {self.max_drawdown*100:.2f}%")
    
    def analyze_one_minus_x_results(self):
        """分析基于1-X的回测结果"""
        print("\n" + "="*80)
        print("📊 基于1-X的策略回测报告")
        print("="*80)
        print(f"📅 报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"🎯 策略逻辑: Y>0.43且X>0.43看涨 (X=散户买跌概率)")
        print(f"💡 含义: 控股商控制强且散户大量买跌时做多")
        print(f"📊 数据记录数: {len(self.df):,}条")
        
        if not self.trades:
            print("❌ 没有产生任何交易")
            return
        
        # 基本统计
        trades_df = pd.DataFrame(self.trades)
        
        total_trades = len(trades_df)
        winning_trades = len(trades_df[trades_df['profit'] > 0])
        losing_trades = total_trades - winning_trades
        win_rate = winning_trades / total_trades if total_trades > 0 else 0
        
        total_profit = trades_df['profit'].sum()
        avg_profit = trades_df['profit'].mean()
        max_profit = trades_df['profit'].max()
        min_profit = trades_df['profit'].min()
        
        # 时间统计
        start_date = self.df['Date'].min()
        end_date = self.df['Date'].max()
        total_days = (end_date - start_date).days
        total_years = total_days / 365
        
        # 投入资金计算
        months = total_days / 30
        total_invested = self.initial_capital + months * self.monthly_addition
        net_profit = self.final_capital - total_invested
        total_return = net_profit / total_invested if total_invested > 0 else 0
        annual_return = (self.final_capital / total_invested) ** (1/total_years) - 1 if total_years > 0 else 0
        
        print(f"\n📊 基本统计:")
        print(f"   回测期间: {start_date.date()} 至 {end_date.date()} ({total_years:.1f}年)")
        print(f"   数据记录: {len(self.df):,}条")
        print(f"   总投入: {total_invested:,.2f}港元")
        print(f"   最终资金: {self.final_capital:,.2f}港元")
        print(f"   净收益: {net_profit:,.2f}港元")
        print(f"   总收益率: {total_return*100:.2f}%")
        print(f"   年化收益率: {annual_return*100:.2f}%")
        
        print(f"\n📈 交易统计:")
        print(f"   总交易次数: {total_trades}笔")
        print(f"   盈利交易: {winning_trades}笔 ({win_rate*100:.1f}%)")
        print(f"   亏损交易: {losing_trades}笔 ({(1-win_rate)*100:.1f}%)")
        print(f"   平均每笔收益: {avg_profit:.2f}港元")
        print(f"   最大盈利: {max_profit:.2f}港元")
        print(f"   最大亏损: {min_profit:.2f}港元")
        
        # 方向统计
        long_trades = trades_df[trades_df['direction'] == '看涨']
        short_trades = trades_df[trades_df['direction'] == '看跌']
        
        print(f"\n📊 方向统计:")
        print(f"   看涨交易: {len(long_trades)}笔 ({len(long_trades)/total_trades*100:.1f}%)")
        print(f"   看跌交易: {len(short_trades)}笔 ({len(short_trades)/total_trades*100:.1f}%)")
        
        if len(long_trades) > 0:
            long_win_rate = len(long_trades[long_trades['profit'] > 0]) / len(long_trades)
            print(f"   看涨胜率: {long_win_rate*100:.1f}%")
            print(f"   看涨平均收益: {long_trades['profit'].mean():.2f}港元")
        
        if len(short_trades) > 0:
            short_win_rate = len(short_trades[short_trades['profit'] > 0]) / len(short_trades)
            print(f"   看跌胜率: {short_win_rate*100:.1f}%")
            print(f"   看跌平均收益: {short_trades['profit'].mean():.2f}港元")
        
        # X值分析 (现在X=散户买跌概率)
        print(f"\n🎯 X值分析 (散户买跌概率):")
        signal_data = self.df[(self.df['Y'] > 0.43) & (self.df['X'] > 0.43)]
        
        print(f"   Y>0.43且X>0.43天数: {len(signal_data)}天 ({len(signal_data)/len(self.df)*100:.1f}%)")
        print(f"   含义: 控股商控制强且散户大量买跌的天数")
        
        if len(signal_data) > 0:
            print(f"   信号期间Y值范围: {signal_data['Y'].min():.4f} ~ {signal_data['Y'].max():.4f}")
            print(f"   信号期间X值范围: {signal_data['X'].min():.4f} ~ {signal_data['X'].max():.4f}")
        
        # 风险指标
        print(f"\n📉 风险指标:")
        print(f"   最大回撤: {self.max_drawdown*100:.2f}%")
        
        if annual_return > 0 and self.max_drawdown > 0:
            calmar_ratio = annual_return / self.max_drawdown
            print(f"   卡尔玛比率: {calmar_ratio:.2f}")
        
        return {
            'total_records': len(self.df),
            'total_trades': total_trades,
            'long_trades': len(long_trades),
            'short_trades': len(short_trades),
            'win_rate': win_rate,
            'annual_return': annual_return,
            'max_drawdown': self.max_drawdown,
            'final_capital': self.final_capital
        }
    
    def close_connection(self):
        """关闭数据库连接"""
        if hasattr(self, 'conn') and self.conn.is_connected():
            self.conn.close()
            print("🔗 MySQL连接已关闭")
    
    def run(self):
        """运行完整分析"""
        print("🎯 基于1-X的策略回测分析")
        print("=" * 60)
        
        try:
            # 连接数据库
            if not self.connect_mysql():
                return None
            
            # 加载数据
            if not self.load_hkhsi50_data():
                return None
            
            # 运行回测
            self.run_one_minus_x_backtest()
            
            # 生成报告
            results = self.analyze_one_minus_x_results()
            
            return results
            
        finally:
            # 确保关闭连接
            self.close_connection()

def main():
    """主函数"""
    analyzer = OneMinusXStrategy()
    results = analyzer.run()
    
    if results:
        print(f"\n🎉 基于1-X的策略分析完成！")
        print(f"📊 基于 {results['total_records']:,} 条记录的分析")
        print(f"🎯 总交易: {results['total_trades']}笔")
        print(f"📈 看涨交易: {results['long_trades']}笔")
        print(f"📉 看跌交易: {results['short_trades']}笔")
        print(f"✅ 胜率: {results['win_rate']*100:.1f}%")
        print(f"📈 年化收益: {results['annual_return']*100:.2f}%")

if __name__ == "__main__":
    main()
