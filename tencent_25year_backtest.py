#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
腾讯700HK 25年历史回测系统
==========================

使用yfinance获取25年历史数据
应用Cosmoon XYE交易逻辑进行完整回测

作者: Cosmoon NG
"""

import yfinance as yf
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import matplotlib.pyplot as plt
import warnings
warnings.filterwarnings('ignore')

class TencentBacktest:
    """腾讯700HK回测系统"""
    
    def __init__(self):
        self.symbol = "0700.HK"
        self.initial_capital = 100000  # 初始资金10万港元
        self.commission_rate = 0.001   # 手续费率0.1%
        
        # 交易参数
        self.position_size = 0.8       # 仓位比例80%
        self.take_profit = 0.012       # 止盈1.2%
        self.stop_loss = 0.006         # 止损0.6%
        
        print(f"🎯 腾讯700HK 25年回测系统")
        print(f"💰 初始资金: {self.initial_capital:,} 港元")
        print(f"📊 仓位比例: {self.position_size*100}%")
        print(f"🎯 止盈: {self.take_profit*100}%")
        print(f"🛑 止损: {self.stop_loss*100}%")

    def get_historical_data(self):
        """获取25年历史数据"""
        try:
            # 计算25年前的日期
            end_date = datetime.now()
            start_date = end_date - timedelta(days=25*365)
            
            print(f"\n📅 获取历史数据...")
            print(f"   开始日期: {start_date.strftime('%Y-%m-%d')}")
            print(f"   结束日期: {end_date.strftime('%Y-%m-%d')}")
            
            ticker = yf.Ticker(self.symbol)
            data = ticker.history(start=start_date, end=end_date)
            
            if data.empty:
                print("❌ 无法获取历史数据")
                return None
            
            print(f"✅ 成功获取 {len(data)} 天历史数据")
            print(f"📊 数据范围: {data.index[0].strftime('%Y-%m-%d')} 至 {data.index[-1].strftime('%Y-%m-%d')}")
            
            return data
            
        except Exception as e:
            print(f"❌ 获取数据失败: {e}")
            return None

    def calculate_technical_indicators(self, data):
        """计算技术指标"""
        df = data.copy()
        
        print("📊 计算技术指标...")
        
        # 基础计算
        df['TypicalPrice'] = (df['High'] + df['Low'] + df['Close']) / 3
        
        # 计算MFI相关指标
        df['MoneyFlow'] = df['TypicalPrice'] * df['Volume']
        df['Price_Change'] = df['TypicalPrice'].diff()
        
        # 正负资金流
        df['PositiveMoneyFlow'] = np.where(df['Price_Change'] > 0, df['MoneyFlow'], 0)
        df['NegativeMoneyFlow'] = np.where(df['Price_Change'] < 0, df['MoneyFlow'], 0)
        
        # 14日资金流总和
        df['PositiveMoneyFlow_14'] = df['PositiveMoneyFlow'].rolling(window=14).sum()
        df['NegativeMoneyFlow_14'] = df['NegativeMoneyFlow'].rolling(window=14).sum()
        
        # MoneyFlowRatio
        df['MoneyFlowRatio'] = df['PositiveMoneyFlow_14'] / (df['NegativeMoneyFlow_14'] + 1e-10)
        
        # MFI
        df['MFI'] = 100 - (100 / (1 + df['MoneyFlowRatio']))
        
        # 计算RSI
        delta = df['Close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
        rs = gain / loss
        df['RSI'] = 100 - (100 / (1 + rs))
        
        return df

    def calculate_cosmoon_xye(self, df):
        """计算Cosmoon XYE指标"""
        
        print("🧮 计算Cosmoon XYE指标...")
        
        # X值 (资金流强度) - 基于MoneyFlowRatio标准化
        mfr_rolling_mean = df['MoneyFlowRatio'].rolling(window=50, min_periods=10).mean()
        mfr_rolling_std = df['MoneyFlowRatio'].rolling(window=50, min_periods=10).std()
        df['X_Value'] = (df['MoneyFlowRatio'] - mfr_rolling_mean) / (mfr_rolling_std + 1e-10)
        df['X_Value'] = (df['X_Value'] + 3) / 6  # 标准化到0-1
        df['X_Value'] = df['X_Value'].clip(0, 1)
        
        # Y值 (价格强度) - 基于价格相对位置
        price_min = df['Close'].rolling(window=50, min_periods=10).min()
        price_max = df['Close'].rolling(window=50, min_periods=10).max()
        df['Y_Value'] = (df['Close'] - price_min) / (price_max - price_min + 1e-10)
        
        # Full_Y (行控制系数) - 基于价格趋势强度
        df['Price_Trend'] = df['Close'].rolling(window=10, min_periods=5).apply(
            lambda x: np.polyfit(range(len(x)), x, 1)[0] if len(x) >= 5 else 0
        )
        trend_rolling_mean = df['Price_Trend'].rolling(window=50, min_periods=10).mean()
        trend_rolling_std = df['Price_Trend'].rolling(window=50, min_periods=10).std()
        df['Full_Y'] = (df['Price_Trend'] - trend_rolling_mean) / (trend_rolling_std + 1e-10)
        df['Full_Y'] = (df['Full_Y'] + 3) / 6  # 标准化到0-1
        df['Full_Y'] = df['Full_Y'].clip(0, 1)
        
        # E值 (综合能量)
        volume_norm = df['Volume'] / df['Volume'].rolling(window=50, min_periods=10).mean()
        price_change_norm = abs(df['Close'].pct_change()) * 100
        df['E_Value'] = (volume_norm * price_change_norm).rolling(window=5, min_periods=3).mean()
        e_rolling_mean = df['E_Value'].rolling(window=50, min_periods=10).mean()
        e_rolling_std = df['E_Value'].rolling(window=50, min_periods=10).std()
        df['E_Value'] = (df['E_Value'] - e_rolling_mean) / (e_rolling_std + 1e-10)
        df['E_Value'] = df['E_Value'] / 10  # 调整范围
        
        # MyE (Cosmoon公式)
        df['MyE'] = 8 * df['MoneyFlowRatio'] * df['Full_Y'] - 3 * df['MoneyFlowRatio'] - 3 * df['Full_Y'] + 1
        
        return df

    def generate_trading_signals(self, df):
        """生成交易信号"""
        
        print("🎯 生成交易信号...")
        
        signals = []
        
        for i in range(len(df)):
            row = df.iloc[i]
            
            # 获取指标值
            mye = row['MyE']
            y_val = row['Y_Value']
            x_val = row['X_Value']
            rsi = row['RSI']
            mfi = row['MFI']
            
            # 交易信号逻辑
            if pd.isna(mye) or pd.isna(y_val) or pd.isna(x_val):
                signal = 0  # 无信号
            elif mye > 0.5 and y_val > 0.7 and x_val > 0.6:
                signal = 2  # 强烈买入
            elif mye > 0.1 and y_val > 0.6 and x_val > 0.5:
                signal = 1  # 买入
            elif mye < -0.5 and (y_val < 0.3 or x_val < 0.3):
                signal = -2  # 强烈卖出
            elif mye < -0.1 and (y_val < 0.4 or x_val < 0.4):
                signal = -1  # 卖出
            elif rsi < 25 and mfi < 25:
                signal = 1  # 超卖买入
            elif rsi > 75 and mfi > 75:
                signal = -1  # 超买卖出
            else:
                signal = 0  # 观望
            
            signals.append(signal)
        
        df['TradingSignal'] = signals
        return df

    def run_backtest(self, df):
        """运行回测"""
        
        print("🔄 运行回测...")
        
        # 初始化
        capital = self.initial_capital
        position = 0  # 0=空仓, 1=多头, -1=空头
        entry_price = 0
        entry_date = None
        shares = 0
        
        # 记录交易
        trades = []
        portfolio_values = []
        
        for i in range(len(df)):
            row = df.iloc[i]
            current_date = row.name
            current_price = row['Close']
            signal = row['TradingSignal']
            
            # 计算当前组合价值
            if position == 0:
                portfolio_value = capital
            else:
                unrealized_pnl = (current_price - entry_price) * shares * position
                portfolio_value = capital + unrealized_pnl
            
            portfolio_values.append(portfolio_value)
            
            # 检查止盈止损
            if position != 0:
                price_change = (current_price - entry_price) / entry_price
                
                if position == 1:  # 多头
                    if price_change >= self.take_profit or price_change <= -self.stop_loss:
                        # 平仓
                        pnl = (current_price - entry_price) * shares - current_price * shares * self.commission_rate
                        capital += pnl
                        
                        trades.append({
                            'entry_date': entry_date,
                            'exit_date': current_date,
                            'direction': 'Long',
                            'entry_price': entry_price,
                            'exit_price': current_price,
                            'shares': shares,
                            'pnl': pnl,
                            'return': price_change,
                            'reason': 'Take Profit' if price_change >= self.take_profit else 'Stop Loss'
                        })
                        
                        position = 0
                        shares = 0
                        entry_price = 0
                        entry_date = None
                
                elif position == -1:  # 空头
                    if price_change <= -self.take_profit or price_change >= self.stop_loss:
                        # 平仓
                        pnl = (entry_price - current_price) * shares - current_price * shares * self.commission_rate
                        capital += pnl
                        
                        trades.append({
                            'entry_date': entry_date,
                            'exit_date': current_date,
                            'direction': 'Short',
                            'entry_price': entry_price,
                            'exit_price': current_price,
                            'shares': shares,
                            'pnl': pnl,
                            'return': -price_change,
                            'reason': 'Take Profit' if price_change <= -self.take_profit else 'Stop Loss'
                        })
                        
                        position = 0
                        shares = 0
                        entry_price = 0
                        entry_date = None
            
            # 开仓信号
            if position == 0 and signal != 0:
                if signal > 0:  # 买入信号
                    position = 1
                    shares = int(capital * self.position_size / current_price)
                    entry_price = current_price
                    entry_date = current_date
                    capital -= current_price * shares * (1 + self.commission_rate)
                
                elif signal < 0:  # 卖出信号
                    position = -1
                    shares = int(capital * self.position_size / current_price)
                    entry_price = current_price
                    entry_date = current_date
                    capital -= current_price * shares * self.commission_rate
        
        # 添加组合价值到DataFrame
        df['PortfolioValue'] = portfolio_values
        
        return df, trades, capital

    def analyze_results(self, df, trades, final_capital):
        """分析回测结果"""
        
        print("\n📊 回测结果分析")
        print("=" * 60)
        
        # 基本统计
        total_return = (final_capital - self.initial_capital) / self.initial_capital * 100
        total_trades = len(trades)
        
        if total_trades > 0:
            winning_trades = len([t for t in trades if t['pnl'] > 0])
            losing_trades = len([t for t in trades if t['pnl'] <= 0])
            win_rate = winning_trades / total_trades * 100
            
            avg_win = np.mean([t['pnl'] for t in trades if t['pnl'] > 0]) if winning_trades > 0 else 0
            avg_loss = np.mean([t['pnl'] for t in trades if t['pnl'] <= 0]) if losing_trades > 0 else 0
            
            profit_factor = abs(avg_win * winning_trades / (avg_loss * losing_trades)) if avg_loss != 0 else float('inf')
        else:
            win_rate = 0
            avg_win = 0
            avg_loss = 0
            profit_factor = 0
        
        # 计算年化收益率
        years = len(df) / 252  # 假设252个交易日/年
        annual_return = (final_capital / self.initial_capital) ** (1/years) - 1 if years > 0 else 0
        
        # 计算最大回撤
        portfolio_values = df['PortfolioValue'].values
        peak = np.maximum.accumulate(portfolio_values)
        drawdown = (portfolio_values - peak) / peak
        max_drawdown = np.min(drawdown) * 100
        
        # 买入持有策略对比
        buy_hold_return = (df['Close'].iloc[-1] - df['Close'].iloc[0]) / df['Close'].iloc[0] * 100
        
        print(f"💰 总收益率: {total_return:.2f}%")
        print(f"📈 年化收益率: {annual_return*100:.2f}%")
        print(f"📊 买入持有收益率: {buy_hold_return:.2f}%")
        print(f"🎯 超额收益: {total_return - buy_hold_return:.2f}%")
        print(f"📉 最大回撤: {max_drawdown:.2f}%")
        print(f"🔢 总交易次数: {total_trades}")
        print(f"✅ 胜率: {win_rate:.1f}%")
        print(f"💎 盈亏比: {profit_factor:.2f}")
        print(f"💰 平均盈利: {avg_win:,.0f} 港元")
        print(f"💸 平均亏损: {avg_loss:,.0f} 港元")
        
        # 保存结果
        trades_df = pd.DataFrame(trades)
        if not trades_df.empty:
            trades_df.to_csv(f'腾讯700HK_25年回测交易记录_{datetime.now().strftime("%Y%m%d")}.csv', index=False)
            print(f"\n💾 交易记录已保存")
        
        # 保存技术指标数据
        df.to_csv(f'腾讯700HK_25年回测数据_{datetime.now().strftime("%Y%m%d")}.csv')
        print(f"💾 回测数据已保存")
        
        return {
            'total_return': total_return,
            'annual_return': annual_return,
            'max_drawdown': max_drawdown,
            'win_rate': win_rate,
            'total_trades': total_trades,
            'profit_factor': profit_factor,
            'buy_hold_return': buy_hold_return
        }

def main():
    """主函数"""
    backtest = TencentBacktest()
    
    # 获取历史数据
    data = backtest.get_historical_data()
    if data is None:
        return
    
    # 计算技术指标
    df = backtest.calculate_technical_indicators(data)
    df = backtest.calculate_cosmoon_xye(df)
    df = backtest.generate_trading_signals(df)
    
    # 运行回测
    df, trades, final_capital = backtest.run_backtest(df)
    
    # 分析结果
    results = backtest.analyze_results(df, trades, final_capital)
    
    print(f"\n🎯 腾讯700HK 25年回测完成！")

if __name__ == "__main__":
    main()
