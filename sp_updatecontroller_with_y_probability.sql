
CREATE PROCEDURE sp_updatecontroller(
    IN tablename VARCHAR(64),
    OUT result_k DECIMAL(20,6)
)
BEGIN
    DECLARE col_exists INT DEFAULT 0;
    DECLARE v_start_time DATETIME DEFAULT NOW();

    -- 1. 首先更新midprice (调用sp_averagelineV3)
    SELECT CONCAT('开始更新midprice - 表: ', tablename, ' 时间: ', v_start_time) AS start_message;
    CALL sp_averagelineV3(tablename);
    SELECT 'midprice更新完成' AS midprice_status;

    -- 2. 检查并添加必要的列
    -- 检查controller列
    SET @sql = CONCAT(
        'SELECT COUNT(*) INTO @col_exists FROM information_schema.COLUMNS ',
        'WHERE TABLE_SCHEMA=DATABASE() AND TABLE_NAME=''', tablename, ''' AND COLUMN_NAME=''controller'''
    );
    PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;
    SET col_exists = @col_exists;

    IF col_exists = 0 THEN
        SET @sql = CONCAT('ALTER TABLE `', tablename, '` ADD COLUMN `controller` INT DEFAULT NULL');
        PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;
        SELECT 'controller列已添加' AS controller_status;
    END IF;

    -- 检查Full_Y列
    SET @sql = CONCAT(
        'SELECT COUNT(*) INTO @col_exists FROM information_schema.COLUMNS ',
        'WHERE TABLE_SCHEMA=DATABASE() AND TABLE_NAME=''', tablename, ''' AND COLUMN_NAME=''Full_Y'''
    );
    PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;
    SET col_exists = @col_exists;

    IF col_exists = 0 THEN
        SET @sql = CONCAT('ALTER TABLE `', tablename, '` ADD COLUMN `Full_Y` DECIMAL(20,10) DEFAULT NULL');
        PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;
        SELECT 'Full_Y列已添加' AS full_y_status;
    END IF;

    -- 检查y_probability列
    SET @sql = CONCAT(
        'SELECT COUNT(*) INTO @col_exists FROM information_schema.COLUMNS ',
        'WHERE TABLE_SCHEMA=DATABASE() AND TABLE_NAME=''', tablename, ''' AND COLUMN_NAME=''y_probability'''
    );
    PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;
    SET col_exists = @col_exists;

    IF col_exists = 0 THEN
        SET @sql = CONCAT('ALTER TABLE `', tablename, '` ADD COLUMN `y_probability` DECIMAL(10,6) DEFAULT NULL');
        PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;
        SELECT 'y_probability列已添加' AS y_prob_status;
    END IF;

    -- 检查ma_20列
    SET @sql = CONCAT(
        'SELECT COUNT(*) INTO @col_exists FROM information_schema.COLUMNS ',
        'WHERE TABLE_SCHEMA=DATABASE() AND TABLE_NAME=''', tablename, ''' AND COLUMN_NAME=''ma_20'''
    );
    PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;
    SET col_exists = @col_exists;

    IF col_exists = 0 THEN
        SET @sql = CONCAT('ALTER TABLE `', tablename, '` ADD COLUMN `ma_20` DECIMAL(20,6) DEFAULT NULL');
        PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;
        SELECT 'ma_20列已添加' AS ma20_status;
    END IF;

    -- 检查ma_60列
    SET @sql = CONCAT(
        'SELECT COUNT(*) INTO @col_exists FROM information_schema.COLUMNS ',
        'WHERE TABLE_SCHEMA=DATABASE() AND TABLE_NAME=''', tablename, ''' AND COLUMN_NAME=''ma_60'''
    );
    PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;
    SET col_exists = @col_exists;

    IF col_exists = 0 THEN
        SET @sql = CONCAT('ALTER TABLE `', tablename, '` ADD COLUMN `ma_60` DECIMAL(20,6) DEFAULT NULL');
        PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;
        SELECT 'ma_60列已添加' AS ma60_status;
    END IF;

    -- 3. 计算移动平均线
    SELECT '开始计算移动平均线...' AS ma_calc_status;

    -- 计算MA20
    SET @sql = CONCAT(
        'UPDATE `', tablename, '` t1 ',
        'JOIN (',
        '  SELECT date, ',
        '    AVG(close) OVER (ORDER BY date ASC ROWS 19 PRECEDING) as ma20_value ',
        '  FROM `', tablename, '` ',
        '  ORDER BY date ASC',
        ') t2 ON t1.date = t2.date ',
        'SET t1.ma_20 = t2.ma20_value'
    );
    PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;

    -- 计算MA60
    SET @sql = CONCAT(
        'UPDATE `', tablename, '` t1 ',
        'JOIN (',
        '  SELECT date, ',
        '    AVG(close) OVER (ORDER BY date ASC ROWS 59 PRECEDING) as ma60_value ',
        '  FROM `', tablename, '` ',
        '  ORDER BY date ASC',
        ') t2 ON t1.date = t2.date ',
        'SET t1.ma_60 = t2.ma60_value'
    );
    PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;

    SELECT '移动平均线计算完成' AS ma_complete_status;

    -- 4. 计算y_probability (简化版本)
    SELECT '开始计算y_probability...' AS y_prob_calc_status;

    -- 先计算基础y_probability (简化版本，避免复杂的子查询)
    SET @sql = CONCAT(
        'UPDATE `', tablename, '` ',
        'SET y_probability = CASE ',
        '  WHEN ma_20 > 0 THEN ',
        '    GREATEST(0.1, LEAST(0.9, ',
        '      CASE WHEN (close / ma_20) >= 1 THEN ',
        '        0.5 + 0.4 * ((close / ma_20 - 1) / (1 + ABS(close / ma_20 - 1))) ',
        '      ELSE ',
        '        0.5 - 0.4 * ((1 - close / ma_20) / (1 + ABS(1 - close / ma_20))) ',
        '      END ',
        '    )) ',
        '  ELSE 0.5 ',
        'END ',
        'WHERE ma_20 IS NOT NULL AND close IS NOT NULL'
    );
    PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;

    -- 添加趋势调整
    SET @sql = CONCAT(
        'UPDATE `', tablename, '` ',
        'SET y_probability = GREATEST(0.1, LEAST(0.9, ',
        '  y_probability + CASE ',
        '    WHEN ma_20 > 0 AND ma_60 > 0 THEN ',
        '      0.1 * ((ma_20 / ma_60 - 1) / (1 + ABS(ma_20 / ma_60 - 1))) ',
        '    ELSE 0 ',
        '  END ',
        ')) ',
        'WHERE ma_20 IS NOT NULL AND ma_60 IS NOT NULL AND y_probability IS NOT NULL'
    );
    PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;

    SELECT 'y_probability计算完成' AS y_prob_complete_status;

    -- 5. 更新controller字段 (二元逻辑)
    SELECT '开始计算controller (二元逻辑)...' AS controller_calc_status;

    SET @sql = CONCAT(
        'UPDATE `', tablename, '` ',
        'SET controller = CASE WHEN (close - midprice) > 0 THEN 1 ELSE 0 END ',
        'WHERE midprice IS NOT NULL'
    );
    PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;

    SELECT 'controller二元逻辑计算完成' AS controller_complete_status;

    -- 6. 计算Full_Y字段 (累积controller计数 / 行号)
    SELECT '开始计算Full_Y (累积比例)...' AS full_y_calc_status;

    SET @sql = CONCAT(
        'UPDATE `', tablename, '` t1 ',
        'JOIN (',
        '  SELECT date, ',
        '    SUM(controller) OVER (ORDER BY date ASC ROWS UNBOUNDED PRECEDING) as cumulative_count, ',
        '    ROW_NUMBER() OVER (ORDER BY date ASC) as row_num ',
        '  FROM `', tablename, '` ',
        '  WHERE controller IS NOT NULL ',
        '  ORDER BY date ASC',
        ') t2 ON t1.date = t2.date ',
        'SET t1.Full_Y = t2.cumulative_count / t2.row_num'
    );
    PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;

    SELECT 'Full_Y累积比例计算完成' AS full_y_complete_status;

    -- 7. 计算k值 (controller=1的比例)
    SET @sql = CONCAT(
        'SELECT SUM(controller) / COUNT(*) INTO @k_value FROM `', tablename, '` WHERE controller IS NOT NULL'
    );
    PREPARE stmt FROM @sql; EXECUTE stmt; DEALLOCATE PREPARE stmt;

    SET result_k = @k_value;

    -- 8. 返回统计信息
    SELECT
        CONCAT('更新完成 - 表: ', tablename) AS completion_message,
        CONCAT('k值: ', IFNULL(result_k, 0)) AS k_value_message,
        CONCAT('耗时: ', TIMESTAMPDIFF(SECOND, v_start_time, NOW()), ' 秒') AS duration;

END
            