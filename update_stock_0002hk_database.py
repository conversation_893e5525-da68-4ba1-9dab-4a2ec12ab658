#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
更新MySQL数据库中的stock_0002_hk表
================================

使用yfinance获取0002.HK (中电控股) 的最新数据
并更新到MySQL数据库的stock_0002_hk表中

数据库配置:
- Host: localhost
- User: root
- Password: 12345678
- Database: finance
- Table: stock_0002_hk

作者: Cosmoon NG
日期: 2025年7月24日
"""

import yfinance as yf
import pandas as pd
import numpy as np
import mysql.connector
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

class Stock0002HKDatabaseUpdater:
    def __init__(self):
        """初始化数据库更新器"""
        self.symbol = "0002.HK"  # 中电控股
        self.db_config = {
            'host': 'localhost',
            'user': 'root',
            'password': '12345678',
            'database': 'finance',
            'charset': 'utf8mb4',
            'autocommit': True
        }
        self.connection = None
        self.cursor = None
        self.data = None
        self.today = datetime.now().strftime('%Y-%m-%d')
        
    def connect_database(self):
        """连接MySQL数据库"""
        try:
            print(f"🔗 连接MySQL数据库...")
            self.connection = mysql.connector.connect(**self.db_config)
            self.cursor = self.connection.cursor()
            print(f"✅ 成功连接到数据库: {self.db_config['host']}/{self.db_config['database']}")
            return True
        except mysql.connector.Error as e:
            print(f"❌ 数据库连接失败: {e}")
            return False
    
    def check_table_exists(self):
        """检查stock_0002_hk表是否存在"""
        try:
            self.cursor.execute("SHOW TABLES LIKE 'stock_0002_hk'")
            result = self.cursor.fetchone()
            
            if result:
                print("✅ 找到表: stock_0002_hk")
                
                # 查看表结构
                self.cursor.execute("DESCRIBE stock_0002_hk")
                columns = self.cursor.fetchall()
                print("📋 表结构:")
                for col in columns:
                    print(f"   {col[0]} - {col[1]}")
                
                # 查看记录数
                self.cursor.execute("SELECT COUNT(*) FROM stock_0002_hk")
                count = self.cursor.fetchone()[0]
                print(f"📊 当前记录数: {count}")
                
                return True
            else:
                print("❌ 未找到表: stock_0002_hk")
                return self.create_table()
                
        except mysql.connector.Error as e:
            print(f"❌ 检查表失败: {e}")
            return False
    
    def create_table(self):
        """创建stock_0002_hk表"""
        try:
            print("🔨 创建stock_0002_hk表...")
            
            create_table_sql = """
            CREATE TABLE IF NOT EXISTS stock_0002_hk (
                id INT AUTO_INCREMENT PRIMARY KEY,
                date DATE NOT NULL UNIQUE,
                open DECIMAL(10,4) NOT NULL,
                high DECIMAL(10,4) NOT NULL,
                low DECIMAL(10,4) NOT NULL,
                close DECIMAL(10,4) NOT NULL,
                volume BIGINT NOT NULL,
                x_value DECIMAL(8,6) DEFAULT NULL,
                y_value DECIMAL(8,6) DEFAULT NULL,
                e_value DECIMAL(8,6) DEFAULT NULL,
                mfi DECIMAL(8,4) DEFAULT NULL,
                rsi DECIMAL(8,4) DEFAULT NULL,
                ma20 DECIMAL(10,4) DEFAULT NULL,
                ma60 DECIMAL(10,4) DEFAULT NULL,
                median_price DECIMAL(10,4) DEFAULT NULL,
                controlling_mark TINYINT DEFAULT 0,
                typical_price DECIMAL(10,4) DEFAULT NULL,
                money_flow DECIMAL(20,4) DEFAULT NULL,
                price_momentum DECIMAL(8,6) DEFAULT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                INDEX idx_date (date),
                INDEX idx_close (close),
                INDEX idx_x_value (x_value),
                INDEX idx_y_value (y_value)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            """
            
            self.cursor.execute(create_table_sql)
            print("✅ 成功创建stock_0002_hk表")
            return True
            
        except mysql.connector.Error as e:
            print(f"❌ 创建表失败: {e}")
            return False
    
    def fetch_latest_data(self, period="25y"):
        """获取最新的股票数据"""
        print(f"📊 获取{self.symbol} (中电控股) 最新数据...")
        print(f"📅 更新日期: {self.today}")
        
        try:
            # 获取股票数据
            ticker = yf.Ticker(self.symbol)
            print(f"   🔄 正在从Yahoo Finance获取{self.symbol}数据...")
            
            # 获取历史数据
            end_date = datetime.now()
            start_date = end_date - timedelta(days=25*365)
            
            hist_data = ticker.history(start=start_date, end=end_date, auto_adjust=True, prepost=True)
            
            if hist_data.empty:
                print(f"   ❌ 无法获取{self.symbol}数据，尝试备用方法...")
                hist_data = yf.download(self.symbol, start=start_date, end=end_date, progress=False)
            
            if hist_data.empty:
                print(f"   ❌ 仍无法获取数据")
                return False
            
            # 数据预处理
            self.data = pd.DataFrame({
                'date': hist_data.index.date,  # 只保留日期部分
                'open': hist_data['Open'],
                'high': hist_data['High'],
                'low': hist_data['Low'],
                'close': hist_data['Close'],
                'volume': hist_data['Volume']
            })
            
            # 清理数据
            self.data = self.data.dropna().reset_index(drop=True)
            
            print(f"   ✅ 成功获取 {len(self.data)} 条数据")
            print(f"   📅 数据范围: {self.data['date'].min()} 至 {self.data['date'].max()}")
            print(f"   📈 最新价格: {self.data['close'].iloc[-1]:.2f} 港元")
            
            return True
            
        except Exception as e:
            print(f"   ❌ 数据获取失败: {e}")
            return False
    
    def calculate_technical_indicators(self):
        """计算技术指标"""
        print(f"\n🧮 计算技术指标...")
        
        # === X值计算 (MFI归一化) ===
        print("   计算X值 (MFI归一化)...")
        
        # 1. 计算典型价格和资金流
        self.data['typical_price'] = (self.data['high'] + self.data['low'] + self.data['close']) / 3
        self.data['money_flow'] = self.data['typical_price'] * self.data['volume']
        self.data['price_change'] = self.data['typical_price'].diff()
        
        # 2. 分离正负资金流
        self.data['positive_mf'] = np.where(self.data['price_change'] > 0, self.data['money_flow'], 0)
        self.data['negative_mf'] = np.where(self.data['price_change'] < 0, self.data['money_flow'], 0)
        
        # 3. 计算14日正负资金流总和
        period = 14
        self.data['positive_mf_14'] = self.data['positive_mf'].rolling(period).sum()
        self.data['negative_mf_14'] = self.data['negative_mf'].rolling(period).sum()
        
        # 4. 计算资金流比率
        self.data['money_flow_ratio'] = self.data['positive_mf_14'] / (self.data['negative_mf_14'] + 1e-10)
        
        # 5. 计算MFI (Money Flow Index)
        self.data['mfi'] = 100 - (100 / (1 + self.data['money_flow_ratio']))
        
        # 6. X值 = MFI归一化到0-1
        self.data['x_value'] = self.data['mfi'] / 100
        self.data['x_value'] = self.data['x_value'].fillna(0.5).clip(0.1, 0.9)
        
        # === Y值计算 (Full_Y) ===
        print("   计算Y值 (Full_Y)...")
        
        # 1. 计算RSI
        delta = self.data['close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
        rs = gain / loss
        self.data['rsi'] = 100 - (100 / (1 + rs))
        self.data['rsi'] = self.data['rsi'].fillna(50)
        
        # 2. 计算价格动量
        self.data['price_momentum'] = self.data['close'].pct_change(10)
        self.data['price_momentum'] = self.data['price_momentum'].fillna(0)
        
        # 3. Full_Y计算 (控制系数)
        self.data['y_value'] = (self.data['rsi'] / 100 + np.tanh(self.data['price_momentum'] * 5) + 1) / 2
        self.data['y_value'] = np.clip(self.data['y_value'], 0.1, 0.9)
        
        # === 其他技术指标 ===
        print("   计算其他技术指标...")
        
        # 移动平均线
        self.data['ma20'] = self.data['close'].rolling(20).mean()
        self.data['ma60'] = self.data['close'].rolling(60).mean()
        
        # E值 (策略指标)
        self.data['e_value'] = (8 * self.data['x_value'] - 3) * self.data['y_value'] - 3 * self.data['x_value'] + 1
        
        # 中位价格 (22日滚动中位数)
        self.data['median_price'] = self.data['close'].rolling(22).median()
        
        # 控股商标记 (价格高于中位数为1，否则为0)
        self.data['controlling_mark'] = np.where(self.data['close'] > self.data['median_price'], 1, 0)
        
        print(f"   ✅ 技术指标计算完成")
        print(f"   📊 X值范围: {self.data['x_value'].min():.3f} - {self.data['x_value'].max():.3f}")
        print(f"   📊 Y值范围: {self.data['y_value'].min():.3f} - {self.data['y_value'].max():.3f}")
        print(f"   📊 E值范围: {self.data['e_value'].min():.3f} - {self.data['e_value'].max():.3f}")
    
    def get_existing_dates(self):
        """获取数据库中已存在的日期"""
        try:
            self.cursor.execute("SELECT date FROM stock_0002_hk ORDER BY date")
            existing_dates = [row[0] for row in self.cursor.fetchall()]
            return set(existing_dates)
        except mysql.connector.Error as e:
            print(f"❌ 获取已存在日期失败: {e}")
            return set()
    
    def update_database(self):
        """更新数据库"""
        print(f"\n💾 更新数据库...")
        
        try:
            # 获取已存在的日期
            existing_dates = self.get_existing_dates()
            print(f"   📊 数据库中已有 {len(existing_dates)} 条记录")
            
            # 筛选新数据
            new_data = self.data[~self.data['date'].isin(existing_dates)]
            print(f"   📊 需要插入 {len(new_data)} 条新记录")
            
            if len(new_data) == 0:
                print("   ✅ 数据已是最新，无需更新")
                return True
            
            # 准备插入SQL
            insert_sql = """
            INSERT INTO stock_0002_hk (
                date, open, high, low, close, volume,
                x_value, y_value, e_value, mfi, rsi,
                ma20, ma60, median_price, controlling_mark,
                typical_price, money_flow, price_momentum
            ) VALUES (
                %s, %s, %s, %s, %s, %s,
                %s, %s, %s, %s, %s,
                %s, %s, %s, %s,
                %s, %s, %s
            )
            """
            
            # 批量插入数据
            insert_count = 0
            for _, row in new_data.iterrows():
                try:
                    values = (
                        row['date'], row['open'], row['high'], row['low'], row['close'], int(row['volume']),
                        float(row['x_value']), float(row['y_value']), float(row['e_value']), 
                        float(row['mfi']), float(row['rsi']),
                        float(row['ma20']) if pd.notna(row['ma20']) else None,
                        float(row['ma60']) if pd.notna(row['ma60']) else None,
                        float(row['median_price']) if pd.notna(row['median_price']) else None,
                        int(row['controlling_mark']),
                        float(row['typical_price']), float(row['money_flow']), float(row['price_momentum'])
                    )
                    
                    self.cursor.execute(insert_sql, values)
                    insert_count += 1
                    
                except mysql.connector.Error as e:
                    print(f"   ⚠️ 插入记录失败 {row['date']}: {e}")
                    continue
            
            print(f"   ✅ 成功插入 {insert_count} 条记录")
            
            # 显示最新记录
            self.cursor.execute("""
                SELECT date, close, x_value, y_value, e_value 
                FROM stock_0002_hk 
                ORDER BY date DESC 
                LIMIT 5
            """)
            
            latest_records = self.cursor.fetchall()
            print(f"\n📈 最新5条记录:")
            for record in latest_records:
                print(f"   {record[0]}: 收盘={record[1]:.2f}, X={record[2]:.3f}, Y={record[3]:.3f}, E={record[4]:.3f}")
            
            return True
            
        except mysql.connector.Error as e:
            print(f"❌ 更新数据库失败: {e}")
            return False
    
    def get_latest_info(self):
        """获取最新股票信息"""
        try:
            self.cursor.execute("""
                SELECT date, close, x_value, y_value, e_value, mfi, rsi
                FROM stock_0002_hk 
                ORDER BY date DESC 
                LIMIT 1
            """)
            
            result = self.cursor.fetchone()
            if result:
                return {
                    'date': result[0].strftime('%Y-%m-%d'),
                    'close': result[1],
                    'x_value': result[2],
                    'y_value': result[3],
                    'e_value': result[4],
                    'mfi': result[5],
                    'rsi': result[6]
                }
            return None
            
        except mysql.connector.Error as e:
            print(f"❌ 获取最新信息失败: {e}")
            return None
    
    def close_connection(self):
        """关闭数据库连接"""
        if self.cursor:
            self.cursor.close()
        if self.connection:
            self.connection.close()
        print("🔌 数据库连接已关闭")

def main():
    """主函数"""
    print("🎯 中电控股0002.HK数据库更新工具")
    print("=" * 50)
    print("📋 功能:")
    print("   📊 从Yahoo Finance获取最新数据")
    print("   🧮 计算完整技术指标 (X值、Y值、E值等)")
    print("   💾 更新MySQL数据库")
    print("   📈 显示最新市场信息")
    
    updater = Stock0002HKDatabaseUpdater()
    
    try:
        # 连接数据库
        if not updater.connect_database():
            return
        
        # 检查表是否存在
        if not updater.check_table_exists():
            return
        
        # 获取最新数据
        if not updater.fetch_latest_data():
            return
        
        # 计算技术指标
        updater.calculate_technical_indicators()
        
        # 更新数据库
        if updater.update_database():
            # 显示最新信息
            latest_info = updater.get_latest_info()
            if latest_info:
                print(f"\n🎯 最新市场信息 ({latest_info['date']}):")
                print(f"   💰 收盘价: {latest_info['close']:.2f} 港元")
                print(f"   📊 X值(MFI): {latest_info['x_value']:.3f}")
                print(f"   📊 Y值(Full_Y): {latest_info['y_value']:.3f}")
                print(f"   📊 E值: {latest_info['e_value']:.3f}")
                print(f"   📊 MFI: {latest_info['mfi']:.1f}")
                print(f"   📊 RSI: {latest_info['rsi']:.1f}")
            
            print(f"\n🎉 0002.HK数据库更新完成!")
            print(f"💡 数据已保存到MySQL数据库的stock_0002_hk表中")
        
    finally:
        updater.close_connection()

if __name__ == "__main__":
    main()
