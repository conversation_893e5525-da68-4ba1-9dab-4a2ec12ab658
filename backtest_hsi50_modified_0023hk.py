#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
HSI50指数回测系统（修正版 - 使用0023.HK数据）
==========================================

基于backtest_hsi50_final.py修改
数据源：yfinance 0023.HK (东亚银行)

特点：
1. 使用回归中线作为趋势判断
2. 使用凯利公式优化仓位
3. 采用Cosmoon XYE方法
4. 每月复利加入3000
5. 数据源改为yfinance 0023.HK

作者: Cosmoon NG
日期: 2025年7月
"""

import yfinance as yf
import sqlite3
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import matplotlib.pyplot as plt
from scipy import stats
import matplotlib.pyplot as plt
plt.rcParams['font.sans-serif'] = ['SimHei']  # Use SimHei for Chinese characters
plt.rcParams['axes.unicode_minus'] = False  # This is to display minus signs correctly

class HSI50Backtest:
    def __init__(self):
        """初始化回测系统"""
        self.symbol = "0023.HK"  # 东亚银行
        self.initial_capital = 10000  # 初始资金
        self.monthly_addition = 3000  # 每月追加资金
        self.take_profit_long = 0.012  # 多头止盈 1.2%
        self.stop_loss_long = 0.006    # 多头止损 0.6%
        self.take_profit_short = 0.012  # 空头止盈 1.2%
        self.stop_loss_short = 0.006   # 空头止损 0.6%
        self.position = 0  # 当前持仓，1为多头，-1为空头，0为空仓
        self.current_price = 0  # 当前持仓价格

    def load_data(self):
        """从yfinance加载0023.HK数据"""
        print(f"\n1. 加载{self.symbol}数据...")
        try:
            # 获取尽可能多的历史数据
            end_date = datetime.now()
            start_date = end_date - timedelta(days=25*365)  # 25年数据

            print(f"   尝试获取数据期间: {start_date.date()} 至 {end_date.date()}")

            # 从Yahoo Finance获取数据
            ticker = yf.Ticker(self.symbol)
            hist_data = ticker.history(start=start_date, end=end_date)

            if hist_data.empty:
                raise ValueError(f"无法获取{self.symbol}的数据")

            # 转换为标准格式
            self.df = pd.DataFrame({
                'date': hist_data.index,
                'open': hist_data['Open'],
                'high': hist_data['High'],
                'low': hist_data['Low'],
                'close': hist_data['Close'],
                'volume': hist_data['Volume']
            })

            # 数据清理
            self.df = self.df.dropna()
            self.df = self.df.sort_values('date').reset_index(drop=True)

            # 计算XYE指标
            self.calculate_xye_indicators()

            print(f"✓ 加载了 {len(self.df)} 条数据")
            print(f"数据范围：{self.df['date'].min().date()} 至 {self.df['date'].max().date()}")
            print(f"起始价格：{self.df['close'].iloc[0]:.2f} 港元")
            print(f"最新价格：{self.df['close'].iloc[-1]:.2f} 港元")

        except Exception as e:
            print(f"❌ 数据加载失败: {str(e)}")
            raise

    def calculate_xye_indicators(self):
        """计算Cosmoon XYE指标"""
        print("   计算XYE指标...")

        # 计算Y指标 (价格在20日区间的位置)
        window = 20
        self.df['high_20'] = self.df['high'].rolling(window).max()
        self.df['low_20'] = self.df['low'].rolling(window).min()
        self.df['y_value'] = (self.df['close'] - self.df['low_20']) / (self.df['high_20'] - self.df['low_20'])
        self.df['y_value'] = self.df['y_value'].fillna(0.5).clip(0, 1)

        # 计算X指标 (资金流强度)
        self.df['typical_price'] = (self.df['high'] + self.df['low'] + self.df['close']) / 3
        self.df['money_flow'] = self.df['typical_price'] * self.df['volume']
        self.df['price_change'] = self.df['typical_price'].diff()

        # 正负资金流
        self.df['positive_mf'] = np.where(self.df['price_change'] > 0, self.df['money_flow'], 0)
        self.df['negative_mf'] = np.where(self.df['price_change'] < 0, self.df['money_flow'], 0)

        # 14日资金流比率
        period = 14
        self.df['positive_mf_14'] = self.df['positive_mf'].rolling(period).sum()
        self.df['negative_mf_14'] = self.df['negative_mf'].rolling(period).sum()
        self.df['money_flow_ratio'] = self.df['positive_mf_14'] / (self.df['negative_mf_14'] + 1e-10)

        # MFI和X值
        self.df['mfi'] = 100 - (100 / (1 + self.df['money_flow_ratio']))
        self.df['x_value'] = self.df['mfi'] / 100  # 归一化到0-1

        # 计算E指标 (Cosmoon公式)
        self.df['e_value'] = (8 * self.df['x_value'] - 3) * self.df['y_value'] - 3 * self.df['x_value'] + 1

        print(f"   XYE指标范围: Y({self.df['y_value'].min():.3f}-{self.df['y_value'].max():.3f}) "
              f"X({self.df['x_value'].min():.3f}-{self.df['x_value'].max():.3f}) "
              f"E({self.df['e_value'].min():.3f}-{self.df['e_value'].max():.3f})")

    def calculate_regression_line(self):
        """计算回归线"""
        print("\n2. 计算回归线...")
        try:
            # 添加序号
            self.df['i'] = range(1, len(self.df) + 1)

            # 计算回归参数
            slope, intercept, r_value, p_value, std_err = stats.linregress(
                self.df['i'], self.df['close']
            )

            # 计算回归线
            self.df['regression_line'] = intercept + slope * self.df['i']

            # 计算价格相对回归线的位置
            self.df['price_position'] = (self.df['close'] - self.df['regression_line']) / self.df['regression_line']

            print(f"✓ 回归线计算完成 (R² = {r_value**2:.4f})")
            print(f"回归斜率: {slope:.6f} (每日变化)")
            print(f"年化趋势: {slope*365:.2f} 港元/年")

        except Exception as e:
            print(f"❌ 回归线计算失败: {str(e)}")
            raise

    def calculate_kelly(self, win_rate, profit_ratio):
        """计算凯利公式建议仓位"""
        if win_rate <= 0 or profit_ratio <= 0:
            return 0
        q = 1 - win_rate
        kelly = (win_rate/q * profit_ratio - 1) / profit_ratio
        return max(0, min(kelly, 1))  # 限制在0-1之间

    def add_monthly_capital(self, date, capital):
        """每月增加资金"""
        last_month = getattr(self, 'last_month', None)
        current_month = date.replace(day=1)

        if last_month is None or current_month > last_month:
            self.last_month = current_month
            return capital + self.monthly_addition

        return capital

    def run_backtest(self):
        """运行回测"""
        print("\n3. 开始回测...")
        try:
            # 准备结果记录
            self.trades = []
            capital = self.initial_capital
            self.equity_curve = []

            for i in range(60, len(self.df)):  # 从第60天开始，确保有足够的历史数据
                row = self.df.iloc[i]
                date = row['date']

                # 每月增加资金
                capital = self.add_monthly_capital(date, capital)

                # 记录权益
                self.equity_curve.append({
                    'date': date,
                    'equity': capital,
                    'position': self.position
                })

                # 如果有持仓，检查止盈止损
                if self.position != 0:
                    if self.position == 1:  # 多头
                        profit_ratio = (row['high'] - self.current_price) / self.current_price
                        loss_ratio = (self.current_price - row['low']) / self.current_price

                        if profit_ratio >= self.take_profit_long:  # 止盈
                            exit_price = self.current_price * (1 + self.take_profit_long)
                            profit = (exit_price - self.current_price) / self.current_price * capital
                            capital += profit
                            self.position = 0
                            self.trades.append({
                                'date': date,
                                'type': 'long_exit_tp',
                                'price': exit_price,
                                'profit': profit,
                                'capital': capital
                            })
                        elif loss_ratio >= self.stop_loss_long:  # 止损
                            exit_price = self.current_price * (1 - self.stop_loss_long)
                            loss = (exit_price - self.current_price) / self.current_price * capital
                            capital += loss
                            self.position = 0
                            self.trades.append({
                                'date': date,
                                'type': 'long_exit_sl',
                                'price': exit_price,
                                'profit': loss,
                                'capital': capital
                            })

                    elif self.position == -1:  # 空头
                        profit_ratio = (self.current_price - row['low']) / self.current_price
                        loss_ratio = (row['high'] - self.current_price) / self.current_price

                        if profit_ratio >= self.take_profit_short:  # 止盈
                            exit_price = self.current_price * (1 - self.take_profit_short)
                            profit = (self.current_price - exit_price) / self.current_price * capital
                            capital += profit
                            self.position = 0
                            self.trades.append({
                                'date': date,
                                'type': 'short_exit_tp',
                                'price': exit_price,
                                'profit': profit,
                                'capital': capital
                            })
                        elif loss_ratio >= self.stop_loss_short:  # 止损
                            exit_price = self.current_price * (1 + self.stop_loss_short)
                            loss = (exit_price - self.current_price) / self.current_price * capital * -1
                            capital += loss
                            self.position = 0
                            self.trades.append({
                                'date': date,
                                'type': 'short_exit_sl',
                                'price': exit_price,
                                'profit': loss,
                                'capital': capital
                            })

                # 如果空仓，判断是否开仓 (使用Cosmoon XYE策略)
                if self.position == 0:
                    # 使用Cosmoon策略判断
                    if row['e_value'] > 0 and row['x_value'] > 0.45 and row['y_value'] > 0.45:
                        # 多头信号
                        if row['price_position'] < 0:  # 价格低于回归线
                            self.position = 1
                            self.current_price = row['close']
                            self.trades.append({
                                'date': date,
                                'type': 'long_entry',
                                'price': self.current_price,
                                'capital': capital
                            })

                    elif (row['y_value'] < 0.3 or row['x_value'] < 0.3 or
                          (row['x_value'] > 0.45 and row['y_value'] < 0.35) or
                          (row['x_value'] < 0.45 and row['y_value'] > 0.35)):
                        # 空头信号
                        if row['price_position'] > 0:  # 价格高于回归线
                            self.position = -1
                            self.current_price = row['close']
                            self.trades.append({
                                'date': date,
                                'type': 'short_entry',
                                'price': self.current_price,
                                'capital': capital
                            })

            self.final_capital = capital
            print(f"✓ 回测完成！最终资金：{self.final_capital:,.2f}")

        except Exception as e:
            print(f"❌ 回测执行失败: {str(e)}")
            raise

    def analyze_results(self):
        """分析回测结果"""
        print("\n4. 回测分析...")
        try:
            # 转换交易记录为DataFrame
            trades_df = pd.DataFrame(self.trades)
            if len(trades_df) == 0:
                print("没有产生任何交易")
                return

            # 计算基本统计数据
            total_trades = len(trades_df[trades_df['type'].str.contains('entry')])
            winning_trades = len(trades_df[trades_df['profit'] > 0]) if 'profit' in trades_df.columns else 0
            profit_trades = trades_df[trades_df['profit'] > 0] if 'profit' in trades_df.columns else pd.DataFrame()
            loss_trades = trades_df[trades_df['profit'] < 0] if 'profit' in trades_df.columns else pd.DataFrame()

            print(f"\n📊 {self.symbol}交易统计：")
            print(f"总交易次数：{total_trades}")
            print(f"盈利交易：{winning_trades}")
            print(f"亏损交易：{total_trades - winning_trades}")
            if total_trades > 0:
                print(f"胜率：{winning_trades/len(trades_df[trades_df['type'].str.contains('exit')])*100:.2f}%")

            if len(profit_trades) > 0:
                print(f"平均盈利：{profit_trades['profit'].mean():,.2f}")
                print(f"最大盈利：{profit_trades['profit'].max():,.2f}")

            if len(loss_trades) > 0:
                print(f"平均亏损：{loss_trades['profit'].mean():,.2f}")
                print(f"最大亏损：{loss_trades['profit'].min():,.2f}")

            # 计算收益率
            initial_equity = self.initial_capital
            final_equity = self.final_capital
            total_days = (self.df['date'].max() - self.df['date'].min()).days
            total_years = total_days / 365

            total_return = (final_equity - initial_equity) / initial_equity
            annual_return = (1 + total_return) ** (1/total_years) - 1

            print(f"\n💰 收益统计：")
            print(f"回测期间：{total_years:.1f}年")
            print(f"初始资金：{initial_equity:,.2f}")
            print(f"最终资金：{final_equity:,.2f}")
            print(f"总收益率：{total_return*100:.2f}%")
            print(f"年化收益率：{annual_return*100:.2f}%")

            # 计算复利考虑每月追加资金
            total_additions = (total_days // 30) * self.monthly_addition
            total_invested = initial_equity + total_additions
            actual_return = (final_equity - total_invested) / total_invested
            print(f"总投入资金：{total_invested:,.2f}")
            print(f"考虑每月追加后的实际收益率：{actual_return*100:.2f}%")

            # 买入持有策略对比
            start_price = self.df['close'].iloc[0]
            end_price = self.df['close'].iloc[-1]
            buy_hold_return = (end_price - start_price) / start_price
            buy_hold_final = total_invested * (1 + buy_hold_return)

            print(f"\n🆚 买入持有对比：")
            print(f"买入持有收益率：{buy_hold_return*100:.2f}%")
            print(f"买入持有最终资金：{buy_hold_final:,.2f}")
            print(f"策略超额收益：{final_equity - buy_hold_final:,.2f}")

            # 创建子图
            fig = plt.figure(figsize=(20, 15))

            # 1. 权益曲线（带持仓标记）
            ax1 = plt.subplot(221)
            equity_df = pd.DataFrame(self.equity_curve)
            equity_df['date'] = pd.to_datetime(equity_df['date'])

            # 绘制基础权益曲线
            ax1.plot(equity_df['date'], equity_df['equity'], 'b-', label='权益曲线', linewidth=1.5)

            # 标记多空持仓
            long_periods = equity_df[equity_df['position'] == 1]
            short_periods = equity_df[equity_df['position'] == -1]

            if len(long_periods) > 0:
                ax1.plot(long_periods['date'], long_periods['equity'], 'g.', label='多头', markersize=3)
            if len(short_periods) > 0:
                ax1.plot(short_periods['date'], short_periods['equity'], 'r.', label='空头', markersize=3)

            ax1.set_title(f'{self.symbol}权益曲线与持仓分析')
            ax1.set_xlabel('日期')
            ax1.set_ylabel('资金')
            ax1.grid(True)
            ax1.legend()

            # 2. 月度收益分布
            ax2 = plt.subplot(222)
            monthly_returns = []

            for year in equity_df['date'].dt.year.unique():
                for month in range(1, 13):
                    month_data = equity_df[
                        (equity_df['date'].dt.year == year) &
                        (equity_df['date'].dt.month == month)
                    ]
                    if len(month_data) > 0:
                        start_equity = month_data['equity'].iloc[0]
                        end_equity = month_data['equity'].iloc[-1]
                        monthly_return = (end_equity - start_equity) / start_equity * 100
                        monthly_returns.append(monthly_return)

            ax2.hist(monthly_returns, bins=50, color='blue', alpha=0.7)
            ax2.axvline(x=0, color='r', linestyle='--')
            ax2.set_title('月度收益分布')
            ax2.set_xlabel('收益率 (%)')
            ax2.set_ylabel('频次')

            # 3. 累计收益与回撤分析
            ax3 = plt.subplot(223)
            equity_df['return'] = equity_df['equity'].pct_change()
            equity_df['cum_return'] = (1 + equity_df['return']).cumprod()
            equity_df['cum_roll_max'] = equity_df['cum_return'].cummax()
            equity_df['drawdown'] = equity_df['cum_roll_max'] - equity_df['cum_return']

            ax3.plot(equity_df['date'], equity_df['cum_return'], 'b-', label='累计收益')
            ax3.plot(equity_df['date'], equity_df['cum_roll_max'], 'g--', label='历史新高')
            ax3.fill_between(equity_df['date'],
                           equity_df['cum_return'],
                           equity_df['cum_roll_max'],
                           alpha=0.3,
                           color='red',
                           label='回撤')
            ax3.set_title('累计收益与回撤分析')
            ax3.set_xlabel('日期')
            ax3.set_ylabel('累计收益倍数')
            ax3.grid(True)
            ax3.legend()

            # 4. 交易盈亏分布
            ax4 = plt.subplot(224)
            if 'profit' in trades_df.columns:
                trades_df['profit'].hist(bins=50, ax=ax4, color='blue', alpha=0.7)
                ax4.axvline(x=0, color='r', linestyle='--')
                ax4.set_title('交易盈亏分布')
                ax4.set_xlabel('盈亏金额')
                ax4.set_ylabel('频次')

            # 调整布局并保存
            plt.tight_layout()
            chart_filename = f'{self.symbol.replace(".", "_")}_分析图表_{datetime.now().strftime("%Y%m%d_%H%M%S")}.png'
            plt.savefig(chart_filename, dpi=300, bbox_inches='tight')
            plt.close()

            # 保存交易记录
            trades_df_save = trades_df.copy()
            if 'date' in trades_df_save.columns:
                trades_df_save['date'] = pd.to_datetime(trades_df_save['date']).dt.tz_localize(None)
            excel_filename = f'{self.symbol.replace(".", "_")}_交易记录_{datetime.now().strftime("%Y%m%d_%H%M%S")}.xlsx'
            trades_df_save.to_excel(excel_filename, index=False)

            print("\n✓ 分析完成")
            print(f"• 交易记录已保存到 {excel_filename}")
            print(f"• 详细分析图表已保存到 {chart_filename}")

        except Exception as e:
            print(f"❌ 分析失败: {str(e)}")
            raise

def main():
    """主函数"""
    print(f"\n{HSI50Backtest().symbol}回测系统（基于Cosmoon XYE策略）")
    print("="*60)

    try:
        # 创建回测实例
        backtest = HSI50Backtest()

        # 加载数据
        backtest.load_data()

        # 计算回归线
        backtest.calculate_regression_line()

        # 运行回测
        backtest.run_backtest()

        # 分析结果
        backtest.analyze_results()

    except Exception as e:
        print(f"\n❌ 程序运行失败: {str(e)}")
    else:
        print(f"\n✅ {backtest.symbol}回测程序运行完成")

if __name__ == "__main__":
    main()
