-- Full_Y 字段的多种计算方法

-- 方法1：典型价格 (推荐)
UPDATE your_table_name 
SET Full_Y = ROUND((high + low + close) / 3, 3)
WHERE high IS NOT NULL AND low IS NOT NULL AND close IS NOT NULL;

-- 方法2：加权典型价格 (重视收盘价)
UPDATE your_table_name 
SET Full_Y = ROUND((high + low + 2*close) / 4, 3)
WHERE high IS NOT NULL AND low IS NOT NULL AND close IS NOT NULL;

-- 方法3：中位价格
UPDATE your_table_name 
SET Full_Y = ROUND((high + low) / 2, 3)
WHERE high IS NOT NULL AND low IS NOT NULL;

-- 方法4：OHLC平均价格
UPDATE your_table_name 
SET Full_Y = ROUND((open + high + low + close) / 4, 3)
WHERE open IS NOT NULL AND high IS NOT NULL AND low IS NOT NULL AND close IS NOT NULL;

-- 方法5：动态选择计算方法的存储过程
DELIMITER $$

CREATE PROCEDURE `sp_update_full_y`(
    IN tablename VARCHAR(64),
    IN calculation_method INT DEFAULT 1
)
BEGIN
    DECLARE v_sql TEXT;
    
    CASE calculation_method
        WHEN 1 THEN
            -- 典型价格
            SET v_sql = CONCAT(
                'UPDATE `', tablename, '` ',
                'SET Full_Y = ROUND((high + low + close) / 3, 3) ',
                'WHERE high IS NOT NULL AND low IS NOT NULL AND close IS NOT NULL'
            );
            
        WHEN 2 THEN
            -- 加权典型价格
            SET v_sql = CONCAT(
                'UPDATE `', tablename, '` ',
                'SET Full_Y = ROUND((high + low + 2*close) / 4, 3) ',
                'WHERE high IS NOT NULL AND low IS NOT NULL AND close IS NOT NULL'
            );
            
        WHEN 3 THEN
            -- 中位价格
            SET v_sql = CONCAT(
                'UPDATE `', tablename, '` ',
                'SET Full_Y = ROUND((high + low) / 2, 3) ',
                'WHERE high IS NOT NULL AND low IS NOT NULL'
            );
            
        WHEN 4 THEN
            -- OHLC平均价格
            SET v_sql = CONCAT(
                'UPDATE `', tablename, '` ',
                'SET Full_Y = ROUND((open + high + low + close) / 4, 3) ',
                'WHERE open IS NOT NULL AND high IS NOT NULL AND low IS NOT NULL AND close IS NOT NULL'
            );
            
        ELSE
            -- 默认使用典型价格
            SET v_sql = CONCAT(
                'UPDATE `', tablename, '` ',
                'SET Full_Y = ROUND((high + low + close) / 3, 3) ',
                'WHERE high IS NOT NULL AND low IS NOT NULL AND close IS NOT NULL'
            );
    END CASE;
    
    SET @sql = v_sql;
    PREPARE stmt FROM @sql;
    EXECUTE stmt;
    DEALLOCATE PREPARE stmt;
    
    SELECT CONCAT('Full_Y更新完成，使用方法: ', calculation_method) AS result;
    
END$$

DELIMITER ;

-- 使用示例：
-- CALL sp_update_full_y('your_table_name', 1);  -- 使用典型价格
-- CALL sp_update_full_y('your_table_name', 2);  -- 使用加权典型价格
