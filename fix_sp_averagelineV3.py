#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复存储过程 sp_averagelineV3
============================

修复存储过程以适配hkhsi50表结构：
1. 使用正确的字段名
2. 修复语法错误
3. 优化计算逻辑

作者: Cosmoon NG
日期: 2025年7月
"""

import pymysql

# 数据库配置
db_config = {
    'host': 'localhost',
    'user': 'root',
    'password': '12345678',
    'database': 'finance',
    'charset': 'utf8mb4'
}

def create_fixed_sp_averagelineV3():
    """创建修复的存储过程"""
    print("🔧 创建修复的存储过程 sp_averagelineV3...")
    
    # 分别执行SQL语句
    sqls = [
        "DROP PROCEDURE IF EXISTS sp_averagelineV3",
        """
        CREATE PROCEDURE sp_averagelineV3(IN tablename VARCHAR(64))
        BEGIN
            DECLARE Xavg DECIMAL(20, 6) DEFAULT 0;
            DECLARE Yavg DECIMAL(20, 6) DEFAULT 0;
            DECLARE sum_xy DECIMAL(20, 6) DEFAULT 0;
            DECLARE sum_xx DECIMAL(20, 6) DEFAULT 0;
            DECLARE b DECIMAL(20, 6) DEFAULT 0;
            DECLARE m DECIMAL(20, 6) DEFAULT 0;
            DECLARE total_rows INT DEFAULT 0;
            DECLARE col_exists INT DEFAULT 0;
            DECLARE stmt_sql TEXT;

            -- 检查 i 列是否存在（hkhsi50表已有此列）
            SET @col_exists = 0;
            SET stmt_sql = CONCAT(
                'SELECT COUNT(*) INTO @col_exists FROM information_schema.COLUMNS ',
                'WHERE TABLE_SCHEMA=DATABASE() AND TABLE_NAME=''', tablename, ''' AND COLUMN_NAME=''i'''
            );
            PREPARE stmt FROM stmt_sql; 
            EXECUTE stmt; 
            DEALLOCATE PREPARE stmt;
            SET col_exists = @col_exists;
            
            -- 如果i列不存在则添加（通常hkhsi50已有）
            IF col_exists = 0 THEN
                SET stmt_sql = CONCAT('ALTER TABLE `', tablename, '` ADD COLUMN `i` INT AUTO_INCREMENT UNIQUE');
                PREPARE stmt FROM stmt_sql; 
                EXECUTE stmt; 
                DEALLOCATE PREPARE stmt;
            END IF;

            -- 获取总行数
            SET @total_rows = 0;
            SET stmt_sql = CONCAT('SELECT COUNT(*) INTO @total_rows FROM `', tablename, '`');
            PREPARE stmt FROM stmt_sql; 
            EXECUTE stmt; 
            DEALLOCATE PREPARE stmt;
            SET total_rows = @total_rows;

            -- 计算 Xavg (行号的平均值)
            SET Xavg = (total_rows + 1) / 2;

            -- 计算 Yavg (收盘价的平均值)
            SET @Yavg = 0;
            SET stmt_sql = CONCAT('SELECT AVG(Close) INTO @Yavg FROM `', tablename, '`');
            PREPARE stmt FROM stmt_sql; 
            EXECUTE stmt; 
            DEALLOCATE PREPARE stmt;
            SET Yavg = @Yavg;

            -- 计算 sum_xy (使用ROW_NUMBER确保正确的行号)
            SET @sum_xy = 0;
            SET stmt_sql = CONCAT(
                'SELECT SUM((row_num - ', Xavg, ') * (Close - ', Yavg, ')) INTO @sum_xy ',
                'FROM (SELECT ROW_NUMBER() OVER (ORDER BY Date) as row_num, Close FROM `', tablename, '`) t'
            );
            PREPARE stmt FROM stmt_sql; 
            EXECUTE stmt; 
            DEALLOCATE PREPARE stmt;
            SET sum_xy = @sum_xy;

            -- 计算 sum_xx (使用ROW_NUMBER确保正确的行号)
            SET @sum_xx = 0;
            SET stmt_sql = CONCAT(
                'SELECT SUM(POWER(row_num - ', Xavg, ', 2)) INTO @sum_xx ',
                'FROM (SELECT ROW_NUMBER() OVER (ORDER BY Date) as row_num FROM `', tablename, '`) t'
            );
            PREPARE stmt FROM stmt_sql; 
            EXECUTE stmt; 
            DEALLOCATE PREPARE stmt;
            SET sum_xx = @sum_xx;

            -- 计算回归系数
            IF sum_xx > 0 THEN
                SET b = sum_xy / sum_xx;
                SET m = Yavg - b * Xavg;
            ELSE
                SET b = 0;
                SET m = Yavg;
            END IF;

            -- 检查Midprice列是否存在（hkhsi50表已有此列）
            SET @col_exists = 0;
            SET stmt_sql = CONCAT(
                'SELECT COUNT(*) INTO @col_exists FROM information_schema.COLUMNS ',
                'WHERE TABLE_SCHEMA=DATABASE() AND TABLE_NAME=''', tablename, ''' AND COLUMN_NAME=''Midprice'''
            );
            PREPARE stmt FROM stmt_sql; 
            EXECUTE stmt; 
            DEALLOCATE PREPARE stmt;
            SET col_exists = @col_exists;
            
            -- 如果Midprice列不存在则添加
            IF col_exists = 0 THEN
                SET stmt_sql = CONCAT('ALTER TABLE `', tablename, '` ADD COLUMN `Midprice` DECIMAL(20, 6)');
                PREPARE stmt FROM stmt_sql; 
                EXECUTE stmt; 
                DEALLOCATE PREPARE stmt;
            END IF;

            -- 更新Midprice字段为回归线值
            SET stmt_sql = CONCAT(
                'UPDATE `', tablename, '` t1 ',
                'JOIN (SELECT Date, ROW_NUMBER() OVER (ORDER BY Date) as row_num FROM `', tablename, '`) t2 ',
                'ON t1.Date = t2.Date ',
                'SET t1.Midprice = ', m, ' + ', b, ' * t2.row_num'
            );
            PREPARE stmt FROM stmt_sql; 
            EXECUTE stmt; 
            DEALLOCATE PREPARE stmt;

            -- 返回回归参数和统计信息
            SELECT 
                m AS Intercept,
                b AS Slope,
                sum_xx AS SUM_xx,
                sum_xy AS SUM_xy,
                Xavg AS Xavg,
                Yavg AS Yavg,
                total_rows AS TOTAL_rows,
                CONCAT('y = ', ROUND(m, 4), ' + ', ROUND(b, 4), ' * x') AS 回归方程;
                
            -- 显示最新几条记录的回归线值
            SET stmt_sql = CONCAT(
                'SELECT Date, Close, Midprice as 回归线值, ',
                'ROUND(Close - Midprice, 2) as 偏差 ',
                'FROM `', tablename, '` ',
                'ORDER BY Date DESC LIMIT 10'
            );
            PREPARE stmt FROM stmt_sql; 
            EXECUTE stmt; 
            DEALLOCATE PREPARE stmt;

        END
        """
    ]
    
    try:
        conn = pymysql.connect(**db_config)
        cursor = conn.cursor()
        
        # 执行创建存储过程的SQL
        for sql in sqls:
            sql = sql.strip()
            if sql:
                cursor.execute(sql)
        
        conn.commit()
        print("✅ 修复的存储过程创建成功")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 创建存储过程失败: {e}")
        return False

def test_fixed_sp_averagelineV3():
    """测试修复的存储过程"""
    print("\n🧪 测试修复的存储过程...")
    
    try:
        conn = pymysql.connect(**db_config)
        cursor = conn.cursor()
        
        # 调用存储过程
        cursor.execute("CALL sp_averagelineV3('hkhsi50')")
        
        # 获取第一个结果集（回归参数）
        regression_params = cursor.fetchall()
        
        if regression_params:
            print(f"📊 回归分析结果:")
            params = regression_params[0]
            print(f"   • 截距 (Intercept): {params[0]:.4f}")
            print(f"   • 斜率 (Slope): {params[1]:.6f}")
            print(f"   • SUM_xx: {params[2]:.2f}")
            print(f"   • SUM_xy: {params[3]:.2f}")
            print(f"   • X平均值: {params[4]:.2f}")
            print(f"   • Y平均值: {params[5]:.2f}")
            print(f"   • 总行数: {params[6]}")
            print(f"   • 回归方程: {params[7]}")
        
        # 获取第二个结果集（最新记录）
        if cursor.nextset():
            recent_data = cursor.fetchall()
            
            if recent_data:
                print(f"\n📋 最新10条记录的回归线分析:")
                print(f"{'日期':<12} {'收盘价':<10} {'回归线值':<10} {'偏差':<8}")
                print("-" * 50)
                
                for row in recent_data:
                    date = str(row[0])
                    close = row[1]
                    regression_val = row[2]
                    deviation = row[3]
                    
                    print(f"{date:<12} {close:<10.2f} {regression_val:<10.2f} {deviation:<8.2f}")
        
        # 验证Midprice更新
        cursor.execute("""
            SELECT COUNT(*) as 总数,
                   COUNT(CASE WHEN Midprice IS NOT NULL THEN 1 END) as Midprice非空,
                   AVG(Midprice) as Midprice平均值,
                   MIN(Midprice) as Midprice最小值,
                   MAX(Midprice) as Midprice最大值
            FROM hkhsi50
        """)
        
        stats = cursor.fetchone()
        print(f"\n📈 Midprice更新统计:")
        print(f"   • 总记录数: {stats[0]}")
        print(f"   • Midprice非空: {stats[1]}")
        print(f"   • Midprice平均值: {stats[2]:.2f}")
        print(f"   • Midprice范围: {stats[3]:.2f} - {stats[4]:.2f}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def show_improvements():
    """显示修复的改进点"""
    print("\n📊 修复的改进点:")
    print("="*60)
    
    print("🔧 原问题:")
    print("   • 字段名不匹配 (close vs Close)")
    print("   • 中值字段名错误")
    print("   • i列逻辑不正确")
    print("   • 缺少错误处理")
    
    print("\n✅ 修复内容:")
    print("   • 使用正确的字段名 (Close, Midprice)")
    print("   • 使用ROW_NUMBER()确保正确的行号")
    print("   • 添加除零检查")
    print("   • 优化SQL语法")
    print("   • 增加结果展示")
    
    print("\n🎯 功能说明:")
    print("   • 计算收盘价的线性回归线")
    print("   • 更新Midprice为回归线值")
    print("   • 显示回归方程和统计信息")
    print("   • 分析价格偏差")

def main():
    """主函数"""
    print("🎯 修复存储过程 sp_averagelineV3")
    print("="*50)
    
    # 显示改进说明
    show_improvements()
    
    # 1. 创建修复的存储过程
    if not create_fixed_sp_averagelineV3():
        return
    
    # 2. 测试存储过程
    if test_fixed_sp_averagelineV3():
        print("\n🎉 存储过程修复和测试完成！")
        print("💡 现在可以使用:")
        print("   CALL sp_averagelineV3('hkhsi50');")
        print("📊 功能:")
        print("   • 计算收盘价的线性回归线")
        print("   • 更新Midprice为回归线值")
        print("   • 提供回归分析统计")
        print("   • 显示价格偏差分析")
    else:
        print("\n❌ 存储过程测试失败")

if __name__ == "__main__":
    main()
