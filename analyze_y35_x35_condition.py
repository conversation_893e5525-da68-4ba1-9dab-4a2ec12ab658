#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析Y<0.35且X<0.35条件
====================
分析为什么修改后的看跌条件仍然没有产生交易
"""

import mysql.connector
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

def analyze_y35_x35_condition():
    """分析Y<0.35且X<0.35条件"""
    
    print("📊 Y<0.35且X<0.35条件分析")
    print("=" * 60)
    print(f"📅 分析时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 连接MySQL数据库
    print("\n🔗 连接MySQL数据库...")
    try:
        conn = mysql.connector.connect(
            host="localhost",
            user="root",
            password="12345678",
            database="finance"
        )
        print("✅ 连接成功")
    except Exception as e:
        print(f"❌ 连接失败: {e}")
        return
    
    # 加载数据
    print("📊 加载数据...")
    try:
        query = "SELECT * FROM hkhsi50 ORDER BY Date ASC"
        df = pd.read_sql_query(query, conn)
        
        # 转换数据类型
        for col in df.columns:
            if df[col].dtype == 'object':
                try:
                    df[col] = pd.to_numeric(df[col], errors='ignore')
                except:
                    pass
        
        print(f"✅ 加载 {len(df):,} 条记录")
        
    except Exception as e:
        print(f"❌ 数据加载失败: {e}")
        return
    finally:
        conn.close()
    
    # 计算XY指标
    print("📊 计算XY指标...")
    df['Date'] = pd.to_datetime(df['Date'])
    df = df.sort_values('Date').reset_index(drop=True)
    
    # 计算XY指标
    df['midprice'] = (df['High'] + df['Low']) / 2
    df['controller'] = (df['Close'] > df['midprice']).astype(int)
    df['cumulative_controller'] = df['controller'].cumsum()
    df['row_number'] = range(1, len(df) + 1)
    df['Y'] = df['cumulative_controller'] / df['row_number']
    df['X'] = 1 - df['Y']
    
    print("✅ XY指标计算完成")
    
    # 分析Y和X的分布
    print(f"\n📊 Y和X值分布分析:")
    print(f"   Y值范围: {df['Y'].min():.4f} ~ {df['Y'].max():.4f}")
    print(f"   X值范围: {df['X'].min():.4f} ~ {df['X'].max():.4f}")
    print(f"   Y值均值: {df['Y'].mean():.4f}")
    print(f"   X值均值: {df['X'].mean():.4f}")
    
    # 分析各种条件的满足情况
    print(f"\n🎯 各种条件满足情况:")
    
    conditions = [
        ("Y>0.43且X>0.43", (df['Y'] > 0.43) & (df['X'] > 0.43)),
        ("Y<0.35且X<0.35", (df['Y'] < 0.35) & (df['X'] < 0.35)),
        ("Y<0.25或X<0.25", (df['Y'] < 0.25) | (df['X'] < 0.25)),
        ("Y<0.30且X<0.30", (df['Y'] < 0.30) & (df['X'] < 0.30)),
        ("Y<0.40且X<0.40", (df['Y'] < 0.40) & (df['X'] < 0.40)),
        ("Y<0.35", df['Y'] < 0.35),
        ("X<0.35", df['X'] < 0.35),
        ("Y>0.65", df['Y'] > 0.65),
        ("X>0.65", df['X'] > 0.65)
    ]
    
    print(f"   条件 | 满足天数 | 占比 | 首次出现 | 最后出现")
    print(f"   " + "-" * 70)
    
    for condition_name, condition in conditions:
        satisfied_days = condition.sum()
        percentage = satisfied_days / len(df) * 100
        
        if satisfied_days > 0:
            first_occurrence = df[condition]['Date'].min().date()
            last_occurrence = df[condition]['Date'].max().date()
        else:
            first_occurrence = "从未"
            last_occurrence = "从未"
        
        print(f"   {condition_name:15s} | {satisfied_days:7d} | {percentage:5.1f}% | {first_occurrence} | {last_occurrence}")
    
    # 分析为什么Y<0.35且X<0.35从未出现
    print(f"\n🔍 为什么Y<0.35且X<0.35从未出现？")
    
    print(f"\n   数学分析:")
    print(f"   • Y + X = 1 (恒等式)")
    print(f"   • 如果Y<0.35且X<0.35，则Y+X<0.70")
    print(f"   • 但Y+X必须等于1，所以这个条件数学上不可能！")
    
    print(f"\n   💡 关键发现:")
    print(f"   Y<0.35且X<0.35是一个数学上不可能的条件！")
    print(f"   因为Y+X=1，如果Y<0.35，则X=1-Y>0.65")
    print(f"   如果X<0.35，则Y=1-X>0.65")
    print(f"   所以Y和X不可能同时小于0.35")
    
    # 分析正确的看跌条件
    print(f"\n🎯 正确的看跌条件设计:")
    
    alternative_conditions = [
        ("Y<0.35或X<0.35", (df['Y'] < 0.35) | (df['X'] < 0.35)),
        ("Y<0.30或X<0.30", (df['Y'] < 0.30) | (df['X'] < 0.30)),
        ("Y<0.25或X<0.25", (df['Y'] < 0.25) | (df['X'] < 0.25)),
        ("Y<0.20或X<0.20", (df['Y'] < 0.20) | (df['X'] < 0.20)),
        ("Y>0.70或X>0.70", (df['Y'] > 0.70) | (df['X'] > 0.70)),
        ("Y>0.65或X>0.65", (df['Y'] > 0.65) | (df['X'] > 0.65)),
        ("Y>0.60或X>0.60", (df['Y'] > 0.60) | (df['X'] > 0.60))
    ]
    
    print(f"\n   建议的看跌条件:")
    print(f"   条件 | 满足天数 | 占比 | 可行性")
    print(f"   " + "-" * 50)
    
    for condition_name, condition in alternative_conditions:
        satisfied_days = condition.sum()
        percentage = satisfied_days / len(df) * 100
        
        if percentage > 5:
            feasibility = "很好"
        elif percentage > 1:
            feasibility = "可行"
        elif percentage > 0.1:
            feasibility = "较少"
        else:
            feasibility = "极少"
        
        print(f"   {condition_name:15s} | {satisfied_days:7d} | {percentage:5.1f}% | {feasibility}")
    
    # 分析Y值的历史分布
    print(f"\n📊 Y值历史分布分析:")
    
    y_ranges = [
        (0.0, 0.1), (0.1, 0.2), (0.2, 0.3), (0.3, 0.4), (0.4, 0.5),
        (0.5, 0.6), (0.6, 0.7), (0.7, 0.8), (0.8, 0.9), (0.9, 1.0)
    ]
    
    print(f"   Y值区间 | 天数 | 占比 | 对应X值区间")
    print(f"   " + "-" * 45)
    
    for y_min, y_max in y_ranges:
        condition = (df['Y'] >= y_min) & (df['Y'] < y_max)
        count = condition.sum()
        percentage = count / len(df) * 100
        x_min, x_max = 1-y_max, 1-y_min
        
        print(f"   {y_min:.1f}-{y_max:.1f} | {count:6d} | {percentage:5.1f}% | {x_min:.1f}-{x_max:.1f}")
    
    # 推荐的策略修改方案
    print(f"\n🚀 推荐的策略修改方案:")
    
    print(f"\n   方案1: 使用'或'条件")
    print(f"   • 看跌条件: Y<0.35 或 X<0.35")
    print(f"   • 数学上可行，会产生看跌信号")
    print(f"   • 预期看跌交易占比: 约20-30%")
    
    print(f"\n   方案2: 使用极端条件")
    print(f"   • 看跌条件: Y<0.25 或 X<0.25")
    print(f"   • 捕捉极端市场情况")
    print(f"   • 预期看跌交易占比: 约5-10%")
    
    print(f"\n   方案3: 使用高值条件")
    print(f"   • 看跌条件: Y>0.65 或 X>0.65")
    print(f"   • 在极端牛市时做空")
    print(f"   • 预期看跌交易占比: 约10-15%")
    
    print(f"\n   方案4: 动态平衡条件")
    print(f"   • 看涨: 0.43<Y<0.57 (保持不变)")
    print(f"   • 看跌: Y<0.30 或 Y>0.70")
    print(f"   • 平衡多空机会")
    
    # 创建Y值分布图
    create_y_distribution_chart(df)
    
    print(f"\n🎉 分析完成！")
    print(f"   关键结论: Y<0.35且X<0.35是数学上不可能的条件")
    print(f"   建议使用: Y<0.35 或 X<0.35 作为看跌条件")

def create_y_distribution_chart(df):
    """创建Y值分布图"""
    
    print(f"\n📊 创建Y值分布图...")
    
    plt.figure(figsize=(15, 10))
    
    # 子图1: Y值时间序列
    plt.subplot(2, 2, 1)
    plt.plot(df['Date'], df['Y'], alpha=0.7, linewidth=0.5)
    plt.axhline(y=0.35, color='red', linestyle='--', label='Y=0.35')
    plt.axhline(y=0.43, color='green', linestyle='--', label='Y=0.43')
    plt.axhline(y=0.65, color='orange', linestyle='--', label='Y=0.65')
    plt.title('Y值时间序列 (35年)')
    plt.xlabel('时间')
    plt.ylabel('Y值')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # 子图2: Y值分布直方图
    plt.subplot(2, 2, 2)
    plt.hist(df['Y'], bins=50, alpha=0.7, edgecolor='black')
    plt.axvline(x=0.35, color='red', linestyle='--', label='Y=0.35')
    plt.axvline(x=0.43, color='green', linestyle='--', label='Y=0.43')
    plt.axvline(x=0.65, color='orange', linestyle='--', label='Y=0.65')
    plt.title('Y值分布直方图')
    plt.xlabel('Y值')
    plt.ylabel('频次')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # 子图3: X值时间序列
    plt.subplot(2, 2, 3)
    plt.plot(df['Date'], df['X'], alpha=0.7, linewidth=0.5, color='orange')
    plt.axhline(y=0.35, color='red', linestyle='--', label='X=0.35')
    plt.axhline(y=0.43, color='green', linestyle='--', label='X=0.43')
    plt.axhline(y=0.65, color='blue', linestyle='--', label='X=0.65')
    plt.title('X值时间序列 (35年)')
    plt.xlabel('时间')
    plt.ylabel('X值')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # 子图4: Y vs X散点图
    plt.subplot(2, 2, 4)
    plt.scatter(df['Y'], df['X'], alpha=0.1, s=1)
    plt.plot([0, 1], [1, 0], 'r-', linewidth=2, label='Y+X=1')
    plt.axvline(x=0.35, color='red', linestyle='--', alpha=0.7)
    plt.axhline(y=0.35, color='red', linestyle='--', alpha=0.7)
    plt.axvline(x=0.43, color='green', linestyle='--', alpha=0.7)
    plt.axhline(y=0.43, color='green', linestyle='--', alpha=0.7)
    plt.title('Y vs X 散点图')
    plt.xlabel('Y值')
    plt.ylabel('X值')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # 标注不可能区域
    plt.fill_between([0, 0.35], [0, 0], [0.35, 0.35], alpha=0.3, color='red', 
                     label='不可能区域 (Y<0.35且X<0.35)')
    
    plt.tight_layout()
    plt.savefig('y_x_distribution_analysis.png', dpi=300, bbox_inches='tight')
    print("   ✅ 图表已保存为 y_x_distribution_analysis.png")

def main():
    """主函数"""
    analyze_y35_x35_condition()

if __name__ == "__main__":
    main()
