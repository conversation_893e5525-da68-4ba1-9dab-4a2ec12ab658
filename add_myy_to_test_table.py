#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
为test表添加MyY列并计算数值
=========================

功能：
1. 为test表添加MyY列
2. 计算MyY = SUM($S$2:$S3)/$A3 (累积Y值/交易序号)
3. 更新所有记录的MyY值

作者: Cosmoon NG
日期: 2025年7月
"""

import pymysql
import pandas as pd

class TestTableMyYCalculator:
    def __init__(self):
        """初始化MyY计算器"""
        self.db_config = {
            'host': 'localhost',
            'port': 3306,
            'database': 'finance',
            'user': 'root',
            'password': '12345678',
            'charset': 'utf8mb4'
        }
        self.connection = None
        
    def connect_database(self):
        """连接数据库"""
        try:
            self.connection = pymysql.connect(**self.db_config)
            print(f"✅ 成功连接MySQL数据库: {self.db_config['host']}")
            return True
        except Exception as e:
            print(f"❌ 连接数据库失败: {e}")
            return False
    
    def check_and_add_myy_column(self):
        """检查并添加MyY列"""
        try:
            cursor = self.connection.cursor()
            
            # 检查是否已有MyY列
            cursor.execute("SHOW COLUMNS FROM test LIKE 'MyY'")
            if cursor.fetchone():
                print("⚠️ MyY列已存在，将重新计算数值")
                # 删除现有MyY列
                cursor.execute("ALTER TABLE test DROP COLUMN MyY")
            
            # 添加MyY列
            cursor.execute("ALTER TABLE test ADD COLUMN MyY DECIMAL(10,6) DEFAULT 0 COMMENT '累积Y值平均数'")
            self.connection.commit()
            
            print("✅ 成功添加MyY列到test表")
            return True
            
        except Exception as e:
            print(f"❌ 添加MyY列失败: {e}")
            return False
    
    def calculate_myy_values(self):
        """计算MyY值"""
        try:
            print("🧮 开始计算MyY值...")
            
            cursor = self.connection.cursor()
            
            # 获取所有记录，按交易序号排序
            cursor.execute("""
                SELECT 交易序号, Y值
                FROM test 
                ORDER BY 交易序号
            """)
            
            records = cursor.fetchall()
            
            if not records:
                print("❌ test表中没有数据")
                return False
            
            print(f"📊 处理 {len(records)} 条记录...")
            
            # 计算累积Y值和MyY
            cumulative_y = 0
            updates = []
            
            for trade_id, y_value in records:
                # 累积Y值
                cumulative_y += float(y_value)
                
                # 计算MyY = 累积Y值 / 交易序号
                myy = cumulative_y / trade_id
                
                updates.append((myy, trade_id))
                
                # 显示前10条计算过程
                if trade_id <= 10:
                    print(f"   交易{trade_id}: Y值={y_value:.3f}, 累积Y={cumulative_y:.3f}, MyY={myy:.6f}")
            
            # 批量更新MyY值
            update_sql = "UPDATE test SET MyY = %s WHERE 交易序号 = %s"
            cursor.executemany(update_sql, updates)
            self.connection.commit()
            
            print(f"✅ 成功计算并更新 {len(updates)} 条记录的MyY值")
            return True
            
        except Exception as e:
            print(f"❌ 计算MyY值失败: {e}")
            return False
    
    def verify_myy_calculation(self):
        """验证MyY计算结果"""
        try:
            cursor = self.connection.cursor()
            
            # 获取前20条记录验证
            cursor.execute("""
                SELECT 交易序号, Y值, MyY
                FROM test 
                ORDER BY 交易序号 
                LIMIT 20
            """)
            
            verification_data = cursor.fetchall()
            
            print("\n📊 MyY计算验证 (前20条记录):")
            print("="*60)
            print(f"{'序号':<4} {'Y值':<8} {'MyY':<10} {'手工验证':<10} {'差异':<8}")
            print("-" * 60)
            
            cumulative_y = 0
            for trade_id, y_value, myy in verification_data:
                cumulative_y += float(y_value)
                manual_myy = cumulative_y / trade_id
                difference = abs(float(myy) - manual_myy)
                
                print(f"{trade_id:<4} {float(y_value):<8.3f} {float(myy):<10.6f} {manual_myy:<10.6f} {difference:<8.6f}")
            
            # 统计信息
            cursor.execute("""
                SELECT 
                    COUNT(*) as total_records,
                    MIN(MyY) as min_myy,
                    MAX(MyY) as max_myy,
                    AVG(MyY) as avg_myy,
                    AVG(Y值) as avg_y
                FROM test
            """)
            
            stats = cursor.fetchone()
            total, min_myy, max_myy, avg_myy, avg_y = stats
            
            print(f"\n📈 MyY统计信息:")
            print(f"   • 总记录数: {total}")
            print(f"   • MyY最小值: {float(min_myy):.6f}")
            print(f"   • MyY最大值: {float(max_myy):.6f}")
            print(f"   • MyY平均值: {float(avg_myy):.6f}")
            print(f"   • Y值平均值: {float(avg_y):.6f}")
            
            # 显示最后几条记录
            cursor.execute("""
                SELECT 交易序号, Y值, MyY
                FROM test 
                ORDER BY 交易序号 DESC
                LIMIT 5
            """)
            
            last_records = cursor.fetchall()
            
            print(f"\n📋 最后5条记录的MyY值:")
            print("-" * 40)
            for trade_id, y_value, myy in last_records:
                print(f"交易{trade_id}: Y值={float(y_value):.3f}, MyY={float(myy):.6f}")
            
            return True
            
        except Exception as e:
            print(f"❌ 验证MyY计算失败: {e}")
            return False
    
    def analyze_myy_trends(self):
        """分析MyY趋势"""
        try:
            cursor = self.connection.cursor()
            
            # 按策略区域分析MyY
            cursor.execute("""
                SELECT 策略区域,
                       COUNT(*) as count,
                       AVG(Y值) as avg_y,
                       AVG(MyY) as avg_myy,
                       MIN(MyY) as min_myy,
                       MAX(MyY) as max_myy
                FROM test 
                GROUP BY 策略区域
                ORDER BY avg_myy DESC
            """)
            
            zone_stats = cursor.fetchall()
            
            print(f"\n📊 按策略区域的MyY分析:")
            print("-" * 80)
            print(f"{'策略区域':<15} {'次数':<6} {'平均Y值':<10} {'平均MyY':<10} {'最小MyY':<10} {'最大MyY':<10}")
            print("-" * 80)
            
            for zone, count, avg_y, avg_myy, min_myy, max_myy in zone_stats:
                print(f"{zone:<15} {count:<6} {float(avg_y):<10.3f} {float(avg_myy):<10.6f} "
                      f"{float(min_myy):<10.6f} {float(max_myy):<10.6f}")
            
            # 分析MyY与盈亏的关系
            cursor.execute("""
                SELECT 
                    CASE 
                        WHEN MyY >= 0.6 THEN 'MyY高(>=0.6)'
                        WHEN MyY >= 0.5 THEN 'MyY中(0.5-0.6)'
                        ELSE 'MyY低(<0.5)'
                    END as myy_category,
                    COUNT(*) as count,
                    AVG(净利润) as avg_profit,
                    SUM(净利润) as total_profit,
                    AVG(MyY) as avg_myy
                FROM test 
                GROUP BY myy_category
                ORDER BY avg_myy DESC
            """)
            
            myy_profit_stats = cursor.fetchall()
            
            print(f"\n📈 MyY与盈亏关系分析:")
            print("-" * 70)
            print(f"{'MyY区间':<15} {'次数':<6} {'平均盈亏':<10} {'总盈亏':<10} {'平均MyY':<10}")
            print("-" * 70)
            
            for category, count, avg_profit, total_profit, avg_myy in myy_profit_stats:
                print(f"{category:<15} {count:<6} {int(avg_profit):<10} {int(total_profit):<10} {float(avg_myy):<10.6f}")
            
            return True
            
        except Exception as e:
            print(f"❌ 分析MyY趋势失败: {e}")
            return False
    
    def close_connection(self):
        """关闭数据库连接"""
        if self.connection:
            self.connection.close()
            print("\n🔒 数据库连接已关闭")

def main():
    """主函数"""
    print("🏦 为test表添加MyY列并计算数值")
    print("="*50)
    print("📊 公式: MyY = SUM($S$2:$S3)/$A3 (累积Y值/交易序号)")
    
    # 创建计算器
    calculator = TestTableMyYCalculator()
    
    # 连接数据库
    if not calculator.connect_database():
        return
    
    # 检查并添加MyY列
    if not calculator.check_and_add_myy_column():
        calculator.close_connection()
        return
    
    # 计算MyY值
    if not calculator.calculate_myy_values():
        calculator.close_connection()
        return
    
    # 验证计算结果
    calculator.verify_myy_calculation()
    
    # 分析MyY趋势
    calculator.analyze_myy_trends()
    
    # 关闭连接
    calculator.close_connection()
    
    print("\n🎉 test表MyY列添加和计算完成!")
    print("📊 MyY = 累积Y值 / 交易序号")
    print("💡 MyY反映了从开始到当前交易的Y值平均水平")

if __name__ == "__main__":
    main()
