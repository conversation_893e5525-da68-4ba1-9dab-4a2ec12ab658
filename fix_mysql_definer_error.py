#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复MySQL Definer错误
===================
修复1449错误：The user specified as a definer ('root'@'%') does not exist
"""

import mysql.connector
import sys

class MySQLDefinerFixer:
    """MySQL Definer错误修复器"""
    
    def __init__(self):
        """初始化连接参数"""
        self.host = "localhost"
        self.user = "root"
        self.password = "12345678"
        self.database = "finance"
        
    def connect_database(self):
        """连接数据库"""
        try:
            self.conn = mysql.connector.connect(
                host=self.host,
                user=self.user,
                password=self.password,
                database=self.database
            )
            self.cursor = self.conn.cursor()
            print("✅ MySQL数据库连接成功")
            return True
        except mysql.connector.Error as e:
            print(f"❌ MySQL连接失败: {e}")
            return False
    
    def check_current_users(self):
        """检查当前数据库用户"""
        print("\n📊 检查当前数据库用户...")
        
        try:
            # 查看当前用户
            self.cursor.execute("SELECT USER(), CURRENT_USER();")
            current_user = self.cursor.fetchone()
            print(f"   当前用户: {current_user[0]}")
            print(f"   当前权限用户: {current_user[1]}")
            
            # 查看所有用户
            self.cursor.execute("SELECT User, Host FROM mysql.user;")
            users = self.cursor.fetchall()
            
            print(f"\n📋 数据库中的所有用户:")
            for user, host in users:
                print(f"   '{user}'@'{host}'")
            
            return users
            
        except mysql.connector.Error as e:
            print(f"❌ 检查用户失败: {e}")
            return []
    
    def create_missing_user(self):
        """创建缺失的用户"""
        print("\n🔧 创建缺失的用户...")
        
        try:
            # 检查是否存在 'root'@'%' 用户
            self.cursor.execute("SELECT COUNT(*) FROM mysql.user WHERE User='root' AND Host='%';")
            count = self.cursor.fetchone()[0]
            
            if count == 0:
                print("   创建 'root'@'%' 用户...")
                
                # 创建用户
                create_user_sql = "CREATE USER 'root'@'%' IDENTIFIED BY '12345678';"
                self.cursor.execute(create_user_sql)
                
                # 授予所有权限
                grant_sql = "GRANT ALL PRIVILEGES ON *.* TO 'root'@'%' WITH GRANT OPTION;"
                self.cursor.execute(grant_sql)
                
                # 刷新权限
                self.cursor.execute("FLUSH PRIVILEGES;")
                
                self.conn.commit()
                print("✅ 用户 'root'@'%' 创建成功")
            else:
                print("   用户 'root'@'%' 已存在")
                
        except mysql.connector.Error as e:
            print(f"❌ 创建用户失败: {e}")
            self.conn.rollback()
    
    def fix_existing_objects(self):
        """修复现有数据库对象的definer"""
        print("\n🔧 修复现有数据库对象的definer...")
        
        try:
            # 查找所有视图
            self.cursor.execute(f"""
                SELECT TABLE_NAME, DEFINER 
                FROM information_schema.VIEWS 
                WHERE TABLE_SCHEMA = '{self.database}'
            """)
            views = self.cursor.fetchall()
            
            if views:
                print(f"   发现 {len(views)} 个视图需要修复:")
                for view_name, definer in views:
                    print(f"     视图: {view_name}, 当前definer: {definer}")
                    
                    # 获取视图定义
                    self.cursor.execute(f"SHOW CREATE VIEW {view_name};")
                    view_def = self.cursor.fetchone()[1]
                    
                    # 删除旧视图
                    self.cursor.execute(f"DROP VIEW {view_name};")
                    
                    # 重新创建视图（会使用当前用户作为definer）
                    # 移除原有的definer定义
                    import re
                    view_def = re.sub(r'DEFINER=`[^`]+`@`[^`]+`\s+', '', view_def)
                    
                    self.cursor.execute(view_def)
                    print(f"     ✅ 视图 {view_name} 修复完成")
            else:
                print("   未发现需要修复的视图")
            
            # 查找所有存储过程
            self.cursor.execute(f"""
                SELECT ROUTINE_NAME, DEFINER 
                FROM information_schema.ROUTINES 
                WHERE ROUTINE_SCHEMA = '{self.database}' AND ROUTINE_TYPE = 'PROCEDURE'
            """)
            procedures = self.cursor.fetchall()
            
            if procedures:
                print(f"   发现 {len(procedures)} 个存储过程需要修复:")
                for proc_name, definer in procedures:
                    print(f"     存储过程: {proc_name}, 当前definer: {definer}")
                    # 注意：存储过程的修复比较复杂，这里只是提示
                    print(f"     ⚠️ 存储过程需要手动重新创建")
            else:
                print("   未发现需要修复的存储过程")
            
            # 查找所有函数
            self.cursor.execute(f"""
                SELECT ROUTINE_NAME, DEFINER 
                FROM information_schema.ROUTINES 
                WHERE ROUTINE_SCHEMA = '{self.database}' AND ROUTINE_TYPE = 'FUNCTION'
            """)
            functions = self.cursor.fetchall()
            
            if functions:
                print(f"   发现 {len(functions)} 个函数需要修复:")
                for func_name, definer in functions:
                    print(f"     函数: {func_name}, 当前definer: {definer}")
                    print(f"     ⚠️ 函数需要手动重新创建")
            else:
                print("   未发现需要修复的函数")
            
            # 查找所有触发器
            self.cursor.execute(f"""
                SELECT TRIGGER_NAME, DEFINER 
                FROM information_schema.TRIGGERS 
                WHERE TRIGGER_SCHEMA = '{self.database}'
            """)
            triggers = self.cursor.fetchall()
            
            if triggers:
                print(f"   发现 {len(triggers)} 个触发器需要修复:")
                for trigger_name, definer in triggers:
                    print(f"     触发器: {trigger_name}, 当前definer: {definer}")
                    print(f"     ⚠️ 触发器需要手动重新创建")
            else:
                print("   未发现需要修复的触发器")
            
            self.conn.commit()
            
        except mysql.connector.Error as e:
            print(f"❌ 修复数据库对象失败: {e}")
            self.conn.rollback()
    
    def test_database_access(self):
        """测试数据库访问"""
        print("\n🧪 测试数据库访问...")
        
        try:
            # 测试查询现有表
            self.cursor.execute(f"SHOW TABLES FROM {self.database};")
            tables = self.cursor.fetchall()
            
            print(f"   数据库 {self.database} 中的表:")
            for table in tables:
                print(f"     - {table[0]}")
            
            # 测试查询东亚银行数据
            if ('eab_0023hk_moneyflow',) in tables:
                self.cursor.execute("SELECT COUNT(*) FROM eab_0023hk_moneyflow;")
                count = self.cursor.fetchone()[0]
                print(f"   ✅ eab_0023hk_moneyflow 表包含 {count:,} 条记录")
                
                # 测试查询最新数据
                self.cursor.execute("""
                    SELECT Date, Close, MFI, MoneyFlowRatio 
                    FROM eab_0023hk_moneyflow 
                    ORDER BY Date DESC 
                    LIMIT 3
                """)
                latest_data = self.cursor.fetchall()
                
                print(f"   最新3条记录:")
                for row in latest_data:
                    print(f"     {row[0]} | 价格:{row[1]:.2f} | MFI:{row[2]:.1f} | 资金流比率:{row[3]:.4f}")
            
            print("✅ 数据库访问测试成功")
            
        except mysql.connector.Error as e:
            print(f"❌ 数据库访问测试失败: {e}")
    
    def fix_sql_mode(self):
        """修复SQL模式设置"""
        print("\n⚙️ 检查和修复SQL模式...")
        
        try:
            # 检查当前SQL模式
            self.cursor.execute("SELECT @@sql_mode;")
            current_mode = self.cursor.fetchone()[0]
            print(f"   当前SQL模式: {current_mode}")
            
            # 设置更宽松的SQL模式
            new_mode = "ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,ERROR_FOR_DIVISION_BY_ZERO,NO_AUTO_CREATE_USER,NO_ENGINE_SUBSTITUTION"
            self.cursor.execute(f"SET sql_mode = '{new_mode}';")
            
            print(f"   ✅ SQL模式已更新")
            
        except mysql.connector.Error as e:
            print(f"❌ 修复SQL模式失败: {e}")
    
    def close_connection(self):
        """关闭数据库连接"""
        if hasattr(self, 'cursor') and self.cursor:
            self.cursor.close()
        if hasattr(self, 'conn') and self.conn.is_connected():
            self.conn.close()
            print("🔗 数据库连接已关闭")
    
    def run_complete_fix(self):
        """运行完整的修复流程"""
        print("🔧 MySQL Definer错误修复工具")
        print("=" * 80)
        print("修复错误: 1449 - The user specified as a definer ('root'@'%') does not exist")
        
        try:
            # 连接数据库
            if not self.connect_database():
                return False
            
            # 检查当前用户
            users = self.check_current_users()
            
            # 创建缺失的用户
            self.create_missing_user()
            
            # 修复SQL模式
            self.fix_sql_mode()
            
            # 修复现有数据库对象
            self.fix_existing_objects()
            
            # 测试数据库访问
            self.test_database_access()
            
            print(f"\n🎉 MySQL Definer错误修复完成！")
            print(f"💡 如果仍有问题，请重启MySQL服务")
            
            return True
            
        except Exception as e:
            print(f"❌ 修复过程失败: {e}")
            return False
        
        finally:
            self.close_connection()

def manual_fix_commands():
    """显示手动修复命令"""
    print("\n📋 手动修复命令 (如果自动修复失败):")
    print("=" * 60)
    
    print("1. 连接到MySQL:")
    print("   mysql -u root -p")
    
    print("\n2. 创建缺失的用户:")
    print("   CREATE USER 'root'@'%' IDENTIFIED BY '12345678';")
    print("   GRANT ALL PRIVILEGES ON *.* TO 'root'@'%' WITH GRANT OPTION;")
    print("   FLUSH PRIVILEGES;")
    
    print("\n3. 检查用户是否创建成功:")
    print("   SELECT User, Host FROM mysql.user WHERE User='root';")
    
    print("\n4. 如果有视图或存储过程错误，重新创建它们:")
    print("   SHOW CREATE VIEW view_name;")
    print("   DROP VIEW view_name;")
    print("   CREATE VIEW view_name AS ...")
    
    print("\n5. 重启MySQL服务 (如果需要):")
    print("   Windows: net stop mysql && net start mysql")
    print("   Linux: sudo systemctl restart mysql")

def main():
    """主函数"""
    fixer = MySQLDefinerFixer()
    
    print("选择操作:")
    print("1. 自动修复definer错误")
    print("2. 显示手动修复命令")
    print("3. 运行完整修复")
    
    choice = input("\n请输入选择 (1-3): ").strip()
    
    if choice == "1":
        success = fixer.run_complete_fix()
        if not success:
            manual_fix_commands()
    elif choice == "2":
        manual_fix_commands()
    elif choice == "3":
        fixer.run_complete_fix()
        manual_fix_commands()
    else:
        print("❌ 无效选择")

if __name__ == "__main__":
    main()
