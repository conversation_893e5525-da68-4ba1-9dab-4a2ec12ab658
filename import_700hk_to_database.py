#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
导入腾讯700HK数据到finance数据库
==============================

1. 创建eab_0700hk表
2. 导入Excel数据到数据库
3. 运行存储过程计算技术指标
4. 导出完整的29字段Excel

作者: Cosmoon NG
"""

import pandas as pd
import mysql.connector
import yfinance as yf
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

def create_0700hk_table():
    """创建腾讯0700HK数据表"""

    print("📊 创建腾讯0700HK数据表...")

    config = {
        'host': 'localhost',
        'port': 3306,
        'user': 'root',
        'password': '12345678',
        'database': 'finance'
    }

    try:
        connection = mysql.connector.connect(**config)
        cursor = connection.cursor()

        # 删除已存在的表
        cursor.execute("DROP TABLE IF EXISTS eab_0700hk")
        print("   已删除旧表 eab_0700hk")

        # 创建新表 (参考eab_0023hk结构)
        create_table_sql = """
        CREATE TABLE eab_0700hk (
            Date DATE PRIMARY KEY,
            Open DECIMAL(10,4),
            High DECIMAL(10,4),
            Low DECIMAL(10,4),
            Close DECIMAL(10,4),
            Volume BIGINT,
            Y_Value DECIMAL(10,6),
            X_Value DECIMAL(10,6),
            E_Value DECIMAL(10,6),
            Full_Y DECIMAL(10,6),
            MoneyFlowRatio DECIMAL(10,6),
            MyE DECIMAL(10,6),
            MidPrice DECIMAL(10,4),
            MinPrice DECIMAL(10,4),
            MaxPrice DECIMAL(10,4),
            Y_Probability DECIMAL(10,6),
            K_Value DECIMAL(10,6),
            RegressionLine DECIMAL(10,4),
            PricePosition DECIMAL(10,6),
            TradingSignal VARCHAR(50),
            RiskLevel VARCHAR(20),
            INDEX idx_date (Date),
            INDEX idx_close (Close),
            INDEX idx_y_value (Y_Value),
            INDEX idx_x_value (X_Value)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
        """

        cursor.execute(create_table_sql)
        print("   ✅ 成功创建表 eab_0700hk")

        cursor.close()
        connection.close()

        return True

    except Exception as e:
        print(f"   ❌ 创建表失败: {e}")
        return False

def fetch_700hk_data():
    """获取腾讯700HK历史数据"""

    print("📈 获取腾讯700HK历史数据...")

    try:
        # 获取25年历史数据
        end_date = datetime.now()
        start_date = end_date - timedelta(days=25*365)

        print(f"   时间范围: {start_date.strftime('%Y-%m-%d')} 至 {end_date.strftime('%Y-%m-%d')}")

        ticker = yf.Ticker("0700.HK")
        data = ticker.history(start=start_date, end=end_date)

        if data.empty:
            print("   ❌ 无法获取数据")
            return None

        print(f"   ✅ 成功获取 {len(data)} 天数据")

        # 重置索引，将日期作为列
        data.reset_index(inplace=True)
        data['Date'] = data['Date'].dt.date

        return data

    except Exception as e:
        print(f"   ❌ 获取数据失败: {e}")
        return None

def import_data_to_database(data):
    """导入数据到数据库"""

    print("💾 导入数据到数据库...")

    config = {
        'host': 'localhost',
        'port': 3306,
        'user': 'root',
        'password': '12345678',
        'database': 'finance'
    }

    try:
        connection = mysql.connector.connect(**config)
        cursor = connection.cursor()

        # 准备插入SQL
        insert_sql = """
        INSERT INTO eab_0700hk (Date, Open, High, Low, Close, Volume)
        VALUES (%s, %s, %s, %s, %s, %s)
        """

        # 批量插入数据
        insert_data = []
        for _, row in data.iterrows():
            insert_data.append((
                row['Date'],
                float(row['Open']),
                float(row['High']),
                float(row['Low']),
                float(row['Close']),
                int(row['Volume'])
            ))

        cursor.executemany(insert_sql, insert_data)
        connection.commit()

        print(f"   ✅ 成功导入 {len(insert_data)} 条记录")

        cursor.close()
        connection.close()

        return True

    except Exception as e:
        print(f"   ❌ 导入数据失败: {e}")
        return False

def run_stored_procedures():
    """运行存储过程计算技术指标"""

    print("🧮 运行存储过程计算技术指标...")

    config = {
        'host': 'localhost',
        'port': 3306,
        'user': 'root',
        'password': '12345678',
        'database': 'finance'
    }

    try:
        connection = mysql.connector.connect(**config)
        cursor = connection.cursor()

        # 1. 运行综合分析存储过程
        print("   🔄 执行 sp_combined_stock_analysis...")
        cursor.callproc('sp_combined_stock_analysis', ['eab_0700hk'])

        # 获取结果
        for result in cursor.stored_results():
            rows = result.fetchall()
            if rows:
                print(f"   📈 处理了 {len(rows)} 行数据")

        connection.commit()
        print("   ✅ sp_combined_stock_analysis 执行完成")

        # 2. 运行增强控制器存储过程 (需要2个参数)
        print("   🔄 执行 sp_updatecontroller_enhanced...")
        cursor.callproc('sp_updatecontroller_enhanced', ['eab_0700hk', 'eab_0700hk'])

        # 获取结果
        for result in cursor.stored_results():
            rows = result.fetchall()
            if rows:
                print(f"   📊 更新了 {len(rows)} 行控制数据")

        connection.commit()
        print("   ✅ sp_updatecontroller_enhanced 执行完成")

        # 验证数据
        cursor.execute("""
            SELECT COUNT(*) as total_rows,
                   COUNT(Y_Value) as y_count,
                   COUNT(X_Value) as x_count,
                   COUNT(E_Value) as e_count,
                   COUNT(Full_Y) as full_y_count,
                   COUNT(MoneyFlowRatio) as mfr_count
            FROM eab_0700hk
        """)

        result = cursor.fetchone()
        total, y_count, x_count, e_count, full_y_count, mfr_count = result

        print(f"   📊 数据验证:")
        print(f"      总行数: {total}")
        print(f"      Y值: {y_count}")
        print(f"      X值: {x_count}")
        print(f"      E值: {e_count}")
        print(f"      Full_Y: {full_y_count}")
        print(f"      MoneyFlowRatio: {mfr_count}")

        cursor.close()
        connection.close()

        return True

    except Exception as e:
        print(f"   ❌ 存储过程执行失败: {e}")
        return False

def export_complete_excel():
    """导出完整的29字段Excel"""

    print("📋 导出完整的29字段Excel...")

    config = {
        'host': 'localhost',
        'port': 3306,
        'user': 'root',
        'password': '12345678',
        'database': 'finance'
    }

    try:
        connection = mysql.connector.connect(**config)

        # 查询所有数据
        query = """
        SELECT Date, Open, High, Low, Close, Volume,
               Y_Value, X_Value, E_Value, Full_Y, MoneyFlowRatio, MyE,
               MidPrice, MinPrice, MaxPrice, Y_Probability, K_Value,
               RegressionLine, PricePosition, TradingSignal, RiskLevel
        FROM eab_0700hk
        ORDER BY Date
        """

        df = pd.read_sql(query, connection)
        connection.close()

        print(f"   📊 查询到 {len(df)} 条记录")

        # 创建完整的交易记录格式
        trading_records = []

        # 交易参数
        initial_capital = 10000.00
        monthly_addition = 3000.00
        commission_rate = 0.001

        current_capital = initial_capital
        position = 0  # 0=空仓, 1=多头, -1=空头
        entry_price = 0
        shares = 0

        for i, row in df.iterrows():
            current_date = row['Date']
            current_price = row['Close']
            y_value = row['Y_Value'] if pd.notna(row['Y_Value']) else 0
            x_value = row['X_Value'] if pd.notna(row['X_Value']) else 0
            e_value = row['E_Value'] if pd.notna(row['E_Value']) else 0
            full_y = row['Full_Y'] if pd.notna(row['Full_Y']) else 0
            mfr = row['MoneyFlowRatio'] if pd.notna(row['MoneyFlowRatio']) else 0
            mye = row['MyE'] if pd.notna(row['MyE']) else 0

            # 每月追加资金
            if i > 0 and i % 22 == 0:  # 大约每月一次
                current_capital += monthly_addition

            # 简化的交易逻辑
            signal = "观望"
            trade_type = "持仓"
            trade_direction = "空仓"

            if position == 0:  # 空仓
                if e_value > 0 and x_value > 0.45 and y_value > 0.45:
                    signal = "强烈买入"
                    trade_type = "开仓"
                    trade_direction = "多头"
                    position = 1
                    entry_price = current_price
                    shares = int(current_capital * 0.8 / current_price) if current_price > 0 else 0
                    if shares > 0:
                        cost = shares * current_price * (1 + commission_rate)
                        current_capital -= cost
                elif (y_value < 0.3 or x_value < 0.3):
                    signal = "强烈卖出"
                    trade_type = "开仓"
                    trade_direction = "空头"
                    position = -1
                    entry_price = current_price
                    shares = int(current_capital * 0.8 / current_price) if current_price > 0 else 0
            else:  # 有持仓
                if position == 1:
                    trade_direction = "多头"
                    price_change = (current_price - entry_price) / entry_price if entry_price > 0 else 0
                    if price_change >= 0.012:  # 1.2%止盈
                        signal = "止盈平仓"
                        trade_type = "平仓"
                        if shares > 0:
                            current_capital += shares * current_price * (1 - commission_rate)
                        position = 0
                        shares = 0
                    elif price_change <= -0.006:  # 0.6%止损
                        signal = "止损平仓"
                        trade_type = "平仓"
                        if shares > 0:
                            current_capital += shares * current_price * (1 - commission_rate)
                        position = 0
                        shares = 0
                    else:
                        signal = "持有多头"
                elif position == -1:
                    trade_direction = "空头"
                    price_change = (current_price - entry_price) / entry_price if entry_price > 0 else 0
                    if price_change <= -0.012:  # 1.2%止盈
                        signal = "止盈平仓"
                        trade_type = "平仓"
                        position = 0
                        shares = 0
                    elif price_change >= 0.006:  # 0.6%止损
                        signal = "止损平仓"
                        trade_type = "平仓"
                        position = 0
                        shares = 0
                    else:
                        signal = "持有空头"

            # 计算当前状态
            if position != 0 and shares > 0:
                if position == 1:
                    unrealized_pnl = shares * (current_price - entry_price)
                    current_market_value = shares * current_price
                else:
                    unrealized_pnl = shares * (entry_price - current_price)
                    current_market_value = shares * current_price
                total_assets = current_capital + current_market_value + unrealized_pnl
            else:
                unrealized_pnl = 0
                current_market_value = 0
                total_assets = current_capital

            # 计算收益率
            months_passed = i // 22
            total_invested = initial_capital + months_passed * monthly_addition
            daily_return = (total_assets - total_invested) / total_invested * 100 if total_invested > 0 else 0

            # 止盈止损价格
            if position == 1:
                take_profit_price = entry_price * 1.012 if entry_price > 0 else current_price * 1.012
                stop_loss_price = entry_price * 0.994 if entry_price > 0 else current_price * 0.994
            elif position == -1:
                take_profit_price = entry_price * 0.988 if entry_price > 0 else current_price * 0.988
                stop_loss_price = entry_price * 1.006 if entry_price > 0 else current_price * 1.006
            else:
                take_profit_price = current_price * 1.012
                stop_loss_price = current_price * 0.994

            # 风险等级
            if abs(e_value) > 0.5:
                risk_level = "高风险"
            elif abs(e_value) > 0.2:
                risk_level = "中风险"
            else:
                risk_level = "低风险"

            # 创建完整记录
            record = {
                '交易日期': current_date.strftime('%Y-%m-%d') if hasattr(current_date, 'strftime') else str(current_date),
                '交易类型': trade_type,
                '交易方向': trade_direction,
                '交易价格': round(current_price, 2),
                '入场价格': round(entry_price, 2) if entry_price > 0 else round(current_price, 2),
                '止盈价': round(take_profit_price, 2),
                '止损价': round(stop_loss_price, 2),
                '持仓数量': shares,
                '交易金额': round(shares * current_price, 2) if shares > 0 else 0,
                '手续费': round(shares * current_price * commission_rate, 2) if shares > 0 else 0,
                '净交易额': round(shares * current_price * (1 - commission_rate), 2) if shares > 0 else 0,
                '持仓成本': round(entry_price, 2) if entry_price > 0 else 0,
                '当前市值': round(current_market_value, 2),
                '浮动盈亏': round(unrealized_pnl, 2),
                '实现盈亏': 0.00,
                '累计盈亏': round(unrealized_pnl, 2),
                '账户余额': round(current_capital, 2),
                '总资产': round(total_assets, 2),
                '收益率': round(daily_return, 2),
                '累计收益率': round(daily_return, 2),
                'Y值': round(y_value, 4),
                'Full_Y': round(full_y, 4),
                'X值': round(x_value, 4),
                'MoneyFlowRatio': round(mfr, 4),
                'E值': round(e_value, 4),
                'MyE': round(mye, 4),
                '信号强度': signal,
                '风险等级': risk_level,
                '备注': f'数据库计算 价格{current_price:.2f}港元 {signal} 资产{total_assets:,.0f}港元'
            }

            trading_records.append(record)

        # 创建最终DataFrame
        final_df = pd.DataFrame(trading_records)

        # 保存Excel文件
        filename = f'交易记录追踪0700HK_数据库完整版_{datetime.now().strftime("%Y%m%d_%H%M%S")}.xlsx'
        final_df.to_excel(filename, index=False)

        print(f"   ✅ 完整Excel已导出: {filename}")
        print(f"   📊 包含 {len(final_df)} 条记录，29个完整字段")
        print(f"   🧮 所有技术指标均来自数据库计算")

        return filename, final_df

    except Exception as e:
        print(f"   ❌ 导出Excel失败: {e}")
        return None, None

def main():
    """主函数 - 完整流程"""

    print("🎯 腾讯700HK数据库导入和处理系统")
    print("=" * 60)
    print("📋 流程:")
    print("   1. 创建eab_0700hk表")
    print("   2. 获取yfinance历史数据")
    print("   3. 导入数据到数据库")
    print("   4. 运行存储过程计算技术指标")
    print("   5. 导出完整29字段Excel")

    # 步骤1: 创建表
    if not create_0700hk_table():
        print("❌ 创建表失败，停止流程")
        return

    # 步骤2: 获取数据
    data = fetch_700hk_data()
    if data is None:
        print("❌ 获取数据失败，停止流程")
        return

    # 步骤3: 导入数据
    if not import_data_to_database(data):
        print("❌ 导入数据失败，停止流程")
        return

    # 步骤4: 运行存储过程
    if not run_stored_procedures():
        print("❌ 存储过程执行失败，停止流程")
        return

    # 步骤5: 导出Excel
    filename, df = export_complete_excel()
    if filename is None:
        print("❌ 导出Excel失败")
        return

    print(f"\n🎉 完整流程执行成功!")
    print(f"✅ 数据库表: eab_0700hk (已创建并填充)")
    print(f"✅ 存储过程: 已执行完成")
    print(f"✅ Excel文件: {filename}")
    print(f"📊 现在您有了基于数据库计算的完整29字段腾讯700HK交易记录!")

if __name__ == "__main__":
    main()
