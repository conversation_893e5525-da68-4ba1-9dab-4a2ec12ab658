#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
16:00收市后每日更新系统
======================

正确的更新流程:
1. 先更新数据库 (执行存储过程)
2. 再更新Excel表格 (从数据库读取数据)

作者: Cosmoon NG
"""

import mysql.connector
import pandas as pd
import yfinance as yf
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

def update_database_first():
    """第一步：更新数据库"""
    
    print("📊 第一步：更新数据库")
    print("=" * 50)
    
    config = {
        'host': 'localhost',
        'port': 3306,
        'user': 'root',
        'password': '12345678',
        'database': 'finance'
    }
    
    try:
        connection = mysql.connector.connect(**config)
        cursor = connection.cursor()
        
        print("✅ 连接到finance数据库成功")
        
        # 执行存储过程
        print("🔄 执行存储过程: sp_combined_stock_analysis('eab_0023hk')")
        cursor.callproc('sp_combined_stock_analysis', ['eab_0023hk'])
        
        # 获取存储过程的结果
        for result in cursor.stored_results():
            rows = result.fetchall()
            if rows:
                print(f"📈 存储过程返回 {len(rows)} 行数据")
        
        connection.commit()
        print("✅ 存储过程执行成功")
        
        # 验证数据更新
        cursor.execute("SELECT Date, Y_Value, X_Value, E_Value, Full_Y, MoneyFlowRatio FROM eab_0023hk ORDER BY Date DESC LIMIT 1")
        result = cursor.fetchone()
        
        if result:
            date, y_val, x_val, e_val, full_y, mfr = result
            print(f"\n📈 最新数据验证:")
            print(f"   日期: {date}")
            print(f"   Y值: {y_val:.4f}")
            print(f"   X值: {x_val:.4f}")
            print(f"   E值: {e_val:.4f}")
            print(f"   Full_Y: {full_y:.4f}")
            print(f"   MoneyFlowRatio: {mfr:.4f}")
        
        cursor.close()
        connection.close()
        
        return True, result
        
    except Exception as e:
        print(f"❌ 数据库更新失败: {e}")
        return False, None

def update_excel_second(db_data):
    """第二步：更新Excel表格"""
    
    print("\n📊 第二步：更新Excel表格")
    print("=" * 50)
    
    excel_file = "交易记录追踪0023HK.xlsx"
    
    if not db_data:
        print("❌ 无数据库数据，无法更新Excel")
        return False
    
    try:
        # 解析数据库数据
        date, y_val, x_val, e_val, full_y, mfr = db_data
        
        # 获取当前市场价格
        try:
            ticker = yf.Ticker("0023.HK")
            hist = ticker.history(period="1d")
            current_price = hist['Close'].iloc[-1]
            print(f"📈 获取当前价格: {current_price:.2f} 港元")
        except:
            current_price = 12.14
            print(f"⚠️ 使用默认价格: {current_price:.2f} 港元")
        
        # 计算MyE值
        mye_value = 8 * mfr * full_y - 3 * mfr - 3 * full_y + 1
        
        # 用户实际参数
        total_capital = 2500.00
        max_shares = 200
        trade_direction = "空头"
        
        # 计算交易数据
        entry_price = current_price
        trade_amount = current_price * max_shares
        commission = trade_amount * 0.001
        account_balance = total_capital - trade_amount - commission
        current_market_value = current_price * max_shares
        
        # 计算浮动盈亏
        if trade_direction == "空头":
            unrealized_pnl = (entry_price - current_price) * max_shares
        else:
            unrealized_pnl = (current_price - entry_price) * max_shares
        
        # 计算止盈止损价格
        take_profit_rate = 0.012
        stop_loss_rate = 0.006
        
        if trade_direction == "空头":
            take_profit_price = entry_price * (1 - take_profit_rate)
            stop_loss_price = entry_price * (1 + stop_loss_rate)
        else:
            take_profit_price = entry_price * (1 + take_profit_rate)
            stop_loss_price = entry_price * (1 - stop_loss_rate)
        
        # 计算总资产和收益率
        total_assets = account_balance + current_market_value + unrealized_pnl
        daily_return = (total_assets - total_capital) / total_capital * 100
        
        # 判断交易信号
        if mye_value > 0:
            signal_strength = "强烈买入"
            risk_level = "中风险"
        elif mye_value < -0.1:
            signal_strength = "强烈卖出"
            risk_level = "高风险"
        else:
            signal_strength = "观望"
            risk_level = "低风险"
        
        # 创建完整记录
        complete_record = {
            '交易日期': datetime.now().strftime('%Y-%m-%d'),
            '交易类型': '开仓',
            '交易方向': trade_direction,
            '交易价格': current_price,
            '入场价格': entry_price,
            '止盈价': take_profit_price,
            '止损价': stop_loss_price,
            '持仓数量': max_shares,
            '交易金额': trade_amount,
            '手续费': commission,
            '净交易额': trade_amount - commission,
            '持仓成本': entry_price,
            '当前市值': current_market_value,
            '浮动盈亏': unrealized_pnl,
            '实现盈亏': 0.00,
            '累计盈亏': unrealized_pnl,
            '账户余额': account_balance,
            '总资产': total_assets,
            '收益率': daily_return,
            '累计收益率': daily_return,
            'Y值': y_val,
            'Full_Y': full_y,
            'X值': x_val,
            'MoneyFlowRatio': mfr,
            'E值': e_val,
            'MyE': mye_value,
            '信号强度': signal_strength,
            '风险等级': risk_level,
            '备注': f'16:00收市更新 数据库已更新 {signal_strength} MyE={mye_value:.4f}'
        }
        
        # 读取并更新Excel
        df = pd.read_excel(excel_file)
        print(f"📋 Excel当前记录数: {len(df)}")
        
        # 替换最新记录
        df.iloc[-1] = complete_record
        
        # 保存Excel
        df.to_excel(excel_file, index=False)
        
        print(f"✅ Excel文件已更新: {excel_file}")
        
        # 显示更新结果
        print(f"\n📈 更新结果:")
        print(f"   交易价格: {current_price:.2f} 港元")
        print(f"   止盈价: {take_profit_price:.2f} 港元")
        print(f"   止损价: {stop_loss_price:.2f} 港元")
        print(f"   浮动盈亏: {unrealized_pnl:.2f} 港元")
        print(f"   总资产: {total_assets:.2f} 港元")
        print(f"   收益率: {daily_return:.2f}%")
        
        print(f"\n📊 技术指标 (从数据库):")
        print(f"   Y值: {y_val:.4f}")
        print(f"   Full_Y: {full_y:.4f}")
        print(f"   X值: {x_val:.4f}")
        print(f"   MoneyFlowRatio: {mfr:.4f}")
        print(f"   E值: {e_val:.4f}")
        print(f"   MyE: {mye_value:.4f}")
        print(f"   信号: {signal_strength}")
        
        return True
        
    except Exception as e:
        print(f"❌ Excel更新失败: {e}")
        return False

def main():
    """主函数 - 16:00收市后更新流程"""
    
    print("⏰ 16:00收市后每日更新系统")
    print("🔄 正确流程: 数据库 → Excel")
    print("=" * 60)
    
    # 第一步：更新数据库
    db_success, db_data = update_database_first()
    
    if not db_success:
        print("\n❌ 数据库更新失败，停止流程")
        return
    
    # 第二步：更新Excel
    excel_success = update_excel_second(db_data)
    
    if excel_success:
        print(f"\n🎯 16:00收市后更新完成！")
        print(f"✅ 数据库已更新 (存储过程执行)")
        print(f"✅ Excel表格已更新 (从数据库读取)")
        print(f"📊 数据流向: 数据库 → Excel")
        print(f"⏰ 更新时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    else:
        print(f"\n❌ Excel更新失败")

if __name__ == "__main__":
    main()
