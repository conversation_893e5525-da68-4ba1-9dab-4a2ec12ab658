@echo off
chcp 65001 >nul
title 每日更新系统 - Daily Update System

echo.
echo ========================================
echo 🎯 每日更新系统启动
echo ========================================
echo 📅 日期: %date%
echo 🕐 时间: %time%
echo.

REM 切换到脚本所在目录
cd /d "%~dp0"

echo 📂 当前目录: %cd%
echo.

REM 检查Python是否可用
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python 未安装或不在PATH中
    echo 请确保Python已正确安装并添加到系统PATH
    pause
    exit /b 1
)

echo ✅ Python 环境检查通过
echo.

REM 检查必要文件是否存在
if not exist "daily_update_complete.py" (
    echo ❌ 缺少文件: daily_update_complete.py
    pause
    exit /b 1
)

if not exist "daily_update_eab_table.py" (
    echo ❌ 缺少文件: daily_update_eab_table.py
    pause
    exit /b 1
)

if not exist "position_status_viewer.py" (
    echo ❌ 缺少文件: position_status_viewer.py
    pause
    exit /b 1
)

echo ✅ 必要文件检查通过
echo.

echo ========================================
echo 🚀 开始执行每日更新任务
echo ========================================
echo.

REM 执行Python更新脚本
python daily_update_complete.py

REM 检查执行结果
if errorlevel 1 (
    echo.
    echo ========================================
    echo ❌ 更新任务执行失败
    echo ========================================
    echo 请检查上方的错误信息
    echo.
    pause
    exit /b 1
) else (
    echo.
    echo ========================================
    echo ✅ 更新任务执行成功
    echo ========================================
    echo 🎉 所有任务已完成！
    echo.
)

echo 📊 任务完成时间: %date% %time%
echo.

REM 询问是否查看日志
set /p choice="是否要查看详细日志? (y/n): "
if /i "%choice%"=="y" (
    echo.
    echo 📋 如需查看详细日志，请检查上方输出信息
    echo.
)

echo 按任意键退出...
pause >nul
