#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查存储过程参数
==============
"""

import mysql.connector

def check_procedure_params():
    """检查存储过程参数"""
    
    try:
        conn = mysql.connector.connect(
            host="localhost",
            user="root",
            password="12345678",
            database="finance"
        )
        cursor = conn.cursor()
        
        print("🔍 检查存储过程参数")
        print("=" * 60)
        
        # 查看存储过程定义
        cursor.execute("SHOW CREATE PROCEDURE sp_combined_stock_analysis")
        result = cursor.fetchone()
        
        if result:
            proc_name = result[0]
            proc_definition = result[2]
            
            print(f"📋 存储过程: {proc_name}")
            print("=" * 40)
            print(proc_definition)
            print("=" * 40)
            
            # 查看参数信息
            cursor.execute("""
                SELECT 
                    PARAMETER_NAME,
                    PARAMETER_MODE,
                    DATA_TYPE
                FROM information_schema.PARAMETERS
                WHERE SPECIFIC_SCHEMA = 'finance'
                AND SPECIFIC_NAME = 'sp_combined_stock_analysis'
                ORDER BY ORDINAL_POSITION
            """)
            
            params = cursor.fetchall()
            
            if params:
                print(f"\n📊 存储过程参数:")
                for param_name, param_mode, data_type in params:
                    print(f"   {param_mode} {param_name} {data_type}")
            else:
                print(f"\n📊 存储过程无参数")
            
            # 尝试调用存储过程
            print(f"\n🧪 测试调用存储过程...")
            
            if params:
                # 如果有参数，尝试用默认值调用
                if len(params) == 1:
                    try:
                        cursor.callproc('sp_combined_stock_analysis', [30])  # 假设是天数参数
                        print("   ✅ 使用参数30调用成功")
                        
                        for result in cursor.stored_results():
                            rows = result.fetchall()
                            print(f"   返回 {len(rows)} 条记录")
                            if rows and len(rows) > 0:
                                print(f"   示例记录: {rows[0]}")
                    except Exception as e:
                        print(f"   ❌ 调用失败: {e}")
                        
                        # 尝试其他可能的参数
                        try:
                            cursor.callproc('sp_combined_stock_analysis', ['2025-07-01'])  # 假设是日期参数
                            print("   ✅ 使用日期参数调用成功")
                        except Exception as e2:
                            print(f"   ❌ 使用日期参数也失败: {e2}")
            else:
                try:
                    cursor.callproc('sp_combined_stock_analysis')
                    print("   ✅ 无参数调用成功")
                    
                    for result in cursor.stored_results():
                        rows = result.fetchall()
                        print(f"   返回 {len(rows)} 条记录")
                        if rows and len(rows) > 0:
                            print(f"   示例记录: {rows[0]}")
                except Exception as e:
                    print(f"   ❌ 无参数调用失败: {e}")
        
        cursor.close()
        conn.close()
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")

def fix_procedure_call():
    """修复存储过程调用"""
    
    try:
        conn = mysql.connector.connect(
            host="localhost",
            user="root",
            password="12345678",
            database="finance"
        )
        cursor = conn.cursor()
        
        print("\n🔧 修复存储过程调用")
        print("=" * 60)
        
        # 重新创建一个无参数版本的存储过程
        new_procedure = """
        DROP PROCEDURE IF EXISTS sp_combined_stock_analysis_no_params;
        
        CREATE PROCEDURE sp_combined_stock_analysis_no_params()
        BEGIN
            SELECT 
                h.Date,
                h.Close as HSI_Close,
                h.MoneyFlowRatio as HSI_MoneyFlowRatio,
                e.Close as EAB_Close,
                e.MoneyFlowRatio as EAB_MoneyFlowRatio,
                e.MFI as EAB_MFI,
                e.Y_Value,
                e.X_Value,
                e.TradingSignal
            FROM hkhsi50 h
            LEFT JOIN eab_0023hk_moneyflow e ON h.Date = e.Date
            WHERE h.Date >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
            ORDER BY h.Date DESC
            LIMIT 50;
        END
        """
        
        # 执行创建
        for statement in new_procedure.split(';'):
            if statement.strip():
                cursor.execute(statement)
        
        print("✅ 创建无参数版本: sp_combined_stock_analysis_no_params")
        
        # 测试新存储过程
        cursor.callproc('sp_combined_stock_analysis_no_params')
        
        for result in cursor.stored_results():
            rows = result.fetchall()
            print(f"✅ 新存储过程返回 {len(rows)} 条记录")
            
            if rows:
                print(f"\n📊 最新5条记录:")
                for i, row in enumerate(rows[:5]):
                    print(f"   {row[0]} | HSI:{row[1]:.2f}({row[2]:.4f}) | EAB:{row[3]:.2f}({row[4]:.4f}) | MFI:{row[5]:.1f}")
        
        conn.commit()
        cursor.close()
        conn.close()
        
        print(f"\n💡 使用建议:")
        print(f"   - 无参数调用: CALL sp_combined_stock_analysis_no_params();")
        print(f"   - 有参数调用: CALL sp_combined_stock_analysis(30);")
        
    except Exception as e:
        print(f"❌ 修复失败: {e}")

def main():
    """主函数"""
    check_procedure_params()
    fix_procedure_call()

if __name__ == "__main__":
    main()
