#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
每日更新系统 - 0023.HK
1. 从yfinance获取数据并更新finance.eab_0023hk表
2. 调用存储过程sp_combined_stock_analysis
3. 更新Excel交易记录
"""

import mysql.connector
import pandas as pd
import yfinance as yf
import numpy as np
from scipy import stats

def get_eab_table_structure():
    """获取eab_0023hk表结构"""
    config = {
        'host': 'localhost',
        'port': 3306,
        'user': 'root',
        'password': '12345678',
        'database': 'finance'
    }

    try:
        connection = mysql.connector.connect(**config)
        cursor = connection.cursor()

        # 获取表结构
        cursor.execute("DESCRIBE eab_0023hk")
        columns = cursor.fetchall()

        print("📋 eab_0023hk表结构:")
        for col in columns:
            print(f"   {col[0]} - {col[1]} - {col[2]} - {col[3]}")

        cursor.close()
        connection.close()

        return [col[0] for col in columns]

    except Exception as e:
        print(f"❌ 获取表结构失败: {e}")
        return None

def fetch_and_calculate_data():
    """从yfinance获取数据并计算所有指标"""
    print("📊 从yfinance获取0023.HK数据...")

    try:
        ticker = yf.Ticker("0023.HK")

        # 获取足够的历史数据用于计算技术指标
        hist = ticker.history(period="3mo")  # 3个月数据

        if hist.empty:
            print("❌ 无法获取历史数据")
            return None

        print(f"✅ 获取到 {len(hist)} 天的历史数据")

        # 重置索引，将日期作为列
        hist.reset_index(inplace=True)
        hist['Date'] = hist['Date'].dt.date

        # 计算技术指标
        print("🧮 计算技术指标...")

        # 1. 基础数据
        hist['open'] = hist['Open']
        hist['high'] = hist['High']
        hist['low'] = hist['Low']
        hist['close'] = hist['Close']
        hist['volume'] = hist['Volume']

        # 2. 移动平均线
        hist['ma5'] = hist['close'].rolling(5).mean()
        hist['ma10'] = hist['close'].rolling(10).mean()
        hist['ma20'] = hist['close'].rolling(20).mean()
        hist['ma50'] = hist['close'].rolling(50).mean()

        # 3. Y值 (价格在20日区间的位置)
        window = 20
        hist['high_20'] = hist['high'].rolling(window).max()
        hist['low_20'] = hist['low'].rolling(window).min()
        hist['y_value'] = (hist['close'] - hist['low_20']) / (hist['high_20'] - hist['low_20'])
        hist['y_value'] = hist['y_value'].fillna(0.5).clip(0, 1)

        # 4. X值 (资金流强度MFI)
        hist['typical_price'] = (hist['high'] + hist['low'] + hist['close']) / 3
        hist['money_flow'] = hist['typical_price'] * hist['volume']
        hist['price_change'] = hist['typical_price'].diff()

        hist['positive_mf'] = np.where(hist['price_change'] > 0, hist['money_flow'], 0)
        hist['negative_mf'] = np.where(hist['price_change'] < 0, hist['money_flow'], 0)

        period = 14
        hist['positive_mf_14'] = hist['positive_mf'].rolling(period).sum()
        hist['negative_mf_14'] = hist['negative_mf'].rolling(period).sum()
        hist['money_flow_ratio'] = hist['positive_mf_14'] / (hist['negative_mf_14'] + 1e-10)

        hist['mfi'] = 100 - (100 / (1 + hist['money_flow_ratio']))
        hist['x_value'] = hist['mfi'] / 100

        # 5. E值 (Cosmoon公式)
        hist['e_value'] = (8 * hist['x_value'] - 3) * hist['y_value'] - 3 * hist['x_value'] + 1

        # 6. RSI
        delta = hist['close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
        rs = gain / (loss + 1e-10)
        hist['rsi'] = 100 - (100 / (1 + rs))

        # 7. MACD
        exp1 = hist['close'].ewm(span=12).mean()
        exp2 = hist['close'].ewm(span=26).mean()
        hist['macd'] = exp1 - exp2
        hist['macd_signal'] = hist['macd'].ewm(span=9).mean()
        hist['macd_histogram'] = hist['macd'] - hist['macd_signal']

        # 8. 布林带
        hist['bb_middle'] = hist['close'].rolling(20).mean()
        bb_std = hist['close'].rolling(20).std()
        hist['bb_upper'] = hist['bb_middle'] + (bb_std * 2)
        hist['bb_lower'] = hist['bb_middle'] - (bb_std * 2)

        # 9. 成交量指标
        hist['volume_ma'] = hist['volume'].rolling(20).mean()
        hist['volume_ratio'] = hist['volume'] / hist['volume_ma']

        # 10. 价格变化
        hist['price_change_pct'] = hist['close'].pct_change() * 100

        # 11. 回归线
        hist['i'] = range(1, len(hist) + 1)
        if len(hist) > 50:
            slope, intercept, _, _, _ = stats.linregress(hist['i'], hist['close'])
            hist['regression_line'] = intercept + slope * hist['i']
            hist['price_position'] = (hist['close'] - hist['regression_line']) / hist['regression_line']

        print("✅ 技术指标计算完成")

        # 获取最新一天的数据
        latest = hist.iloc[-1]

        # 根据实际表结构返回数据
        return {
            'Date': latest['Date'],
            'Open': latest['open'],
            'High': latest['high'],
            'Low': latest['low'],
            'Close': latest['close'],
            'Volume': int(latest['volume']),
            'TypicalPrice': latest['typical_price'],
            'MoneyFlow': latest['money_flow'],
            'PositiveMoneyFlow': latest['positive_mf_14'],
            'NegativeMoneyFlow': latest['negative_mf_14'],
            'MoneyFlowRatio': latest['money_flow_ratio'],
            'MFI': latest['mfi'],
            'Y_Value': latest['y_value'],
            'X_Value': latest['x_value'],
            'E_Value': latest['e_value'],
            'RSI': latest['rsi'],
            'TradingSignal': 0,  # 默认值，后续可以根据信号设置
            'i': 0,  # 将在update_eab_table函数中正确计算
            'midprice': (latest['high'] + latest['low']) / 2,
            'Controller': 0,  # 默认值
            'Full_Y': latest['y_value'],
            'E': latest['e_value']
        }

    except Exception as e:
        print(f"❌ 数据获取和计算失败: {e}")
        return None

def update_eab_table(data):
    """更新eab_0023hk表"""
    print("📝 更新finance.eab_0023hk表...")

    config = {
        'host': 'localhost',
        'port': 3306,
        'user': 'root',
        'password': '12345678',
        'database': 'finance'
    }

    try:
        connection = mysql.connector.connect(**config)
        cursor = connection.cursor()

        # 获取当前表中的最大i值，用于计算新的i值
        cursor.execute("SELECT MAX(i) FROM eab_0023hk")
        max_i_result = cursor.fetchone()[0]
        next_i = (max_i_result + 1) if max_i_result is not None else 1

        print(f"📊 当前最大i值: {max_i_result}, 新记录i值: {next_i}")

        # 更新data中的i值
        data['i'] = next_i

        # 检查今日数据是否已存在
        check_sql = "SELECT COUNT(*) FROM eab_0023hk WHERE Date = %s"
        cursor.execute(check_sql, (data['Date'],))
        exists = cursor.fetchone()[0]

        if exists > 0:
            # 更新现有记录
            update_sql = """
            UPDATE eab_0023hk SET
                Open = %s, High = %s, Low = %s, Close = %s, Volume = %s,
                TypicalPrice = %s, MoneyFlow = %s, PositiveMoneyFlow = %s, NegativeMoneyFlow = %s,
                MoneyFlowRatio = %s, MFI = %s, Y_Value = %s, X_Value = %s, E_Value = %s,
                RSI = %s, TradingSignal = %s, i = %s, midprice = %s,
                Controller = %s, Full_Y = %s, E = %s, updated_at = NOW()
            WHERE Date = %s
            """

            cursor.execute(update_sql, (
                data['Open'], data['High'], data['Low'], data['Close'], data['Volume'],
                data['TypicalPrice'], data['MoneyFlow'], data['PositiveMoneyFlow'], data['NegativeMoneyFlow'],
                data['MoneyFlowRatio'], data['MFI'], data['Y_Value'], data['X_Value'], data['E_Value'],
                data['RSI'], data['TradingSignal'], data['i'], data['midprice'],
                data['Controller'], data['Full_Y'], data['E'],
                data['Date']
            ))

            print(f"✅ 更新了 {data['Date']} 的记录")

        else:
            # 插入新记录
            insert_sql = """
            INSERT INTO eab_0023hk (
                Date, Open, High, Low, Close, Volume,
                TypicalPrice, MoneyFlow, PositiveMoneyFlow, NegativeMoneyFlow,
                MoneyFlowRatio, MFI, Y_Value, X_Value, E_Value,
                RSI, TradingSignal, i, midprice,
                Controller, Full_Y, E, created_at, updated_at
            ) VALUES (
                %s, %s, %s, %s, %s, %s,
                %s, %s, %s, %s,
                %s, %s, %s, %s, %s,
                %s, %s, %s, %s,
                %s, %s, %s, NOW(), NOW()
            )
            """

            cursor.execute(insert_sql, (
                data['Date'], data['Open'], data['High'], data['Low'], data['Close'], data['Volume'],
                data['TypicalPrice'], data['MoneyFlow'], data['PositiveMoneyFlow'], data['NegativeMoneyFlow'],
                data['MoneyFlowRatio'], data['MFI'], data['Y_Value'], data['X_Value'], data['E_Value'],
                data['RSI'], data['TradingSignal'], data['i'], data['midprice'],
                data['Controller'], data['Full_Y'], data['E']
            ))

            print(f"✅ 插入了 {data['Date']} 的新记录")

        connection.commit()
        cursor.close()
        connection.close()

        return True

    except Exception as e:
        print(f"❌ 更新eab_0023hk表失败: {e}")
        return False

def call_stored_procedure():
    """调用存储过程"""
    print("🔄 调用存储过程...")

    config = {
        'host': 'localhost',
        'port': 3306,
        'user': 'root',
        'password': '12345678',
        'database': 'finance'
    }

    try:
        connection = mysql.connector.connect(**config)
        cursor = connection.cursor()

        # 调用修复后的无参数存储过程
        cursor.execute("CALL sp_combined_stock_analysis()")

        # 获取结果并清理
        results = cursor.fetchall()
        print(f"✅ 存储过程执行成功，返回 {len(results)} 条记录")

        # 清理结果集
        while cursor.nextset():
            pass

        connection.commit()
        cursor.close()
        connection.close()

        return True

    except Exception as e:
        print(f"❌ 存储过程执行失败: {e}")
        print("💡 尝试使用备用方法...")

        # 备用方法：直接查询最新数据验证更新
        try:
            connection = mysql.connector.connect(**config)
            cursor = connection.cursor()

            cursor.execute("""
                SELECT Date, Close, Y_Value, X_Value, E_Value
                FROM eab_0023hk
                ORDER BY Date DESC
                LIMIT 1
            """)

            latest = cursor.fetchone()
            if latest:
                print(f"✅ 数据验证成功: {latest[0]} 收盘价 {latest[1]:.2f}")
                cursor.close()
                connection.close()
                return True
            else:
                cursor.close()
                connection.close()
                return False

        except Exception as e2:
            print(f"❌ 备用验证也失败: {e2}")
            return False

def calculate_trading_signal(data):
    """计算交易信号"""
    y = data['Y_Value']
    x = data['X_Value']
    e = data['E_Value']
    rsi = data['RSI']

    # 改进的交易信号逻辑
    if e > 0.1 and x > 0.6 and y > 0.7 and rsi < 70:
        return "强烈买入", "🟢", 1
    elif e > 0 and x > 0.5 and y > 0.5:
        return "买入", "🟡", 1
    elif e < -0.1 and (x < 0.3 or y < 0.3) and rsi > 30:
        return "强烈卖出", "🔴", -1
    elif e < 0 and (x < 0.4 or y < 0.4):
        return "卖出", "🟠", -1
    else:
        return "观望", "⚪", 0

def update_excel_record(data, db_success, sp_success):
    """更新Excel记录"""
    print("📝 更新Excel交易记录...")

    excel_file = "交易记录追踪0023HK.xlsx"
    backup_file = f"交易记录追踪0023HK_backup_{data['Date'].strftime('%Y%m%d')}.xlsx"

    try:
        import os
        import time
        from datetime import datetime

        # 生成交易信号
        signal, signal_icon, signal_value = calculate_trading_signal(data)

        # 检查文件是否被占用
        max_attempts = 3
        for attempt in range(max_attempts):
            try:
                # 加载现有记录
                if os.path.exists(excel_file):
                    df = pd.read_excel(excel_file)

                    # 创建备份
                    df.to_excel(backup_file, index=False)
                    print(f"📦 创建备份文件: {backup_file}")
                else:
                    # 创建新的DataFrame结构
                    df = pd.DataFrame(columns=[
                        '交易日期', '交易类型', '交易方向', '交易价格', '持仓数量',
                        '交易金额', '手续费', '净交易额', '持仓成本', '当前市值',
                        '浮动盈亏', '实现盈亏', '累计盈亏', '账户余额', '总资产',
                        '收益率', '累计收益率', 'Y值', 'X值', 'E值', 'RSI', 'MFI',
                        '信号强度', '信号图标', '风险等级', '备注'
                    ])

                # 检查今日记录是否已存在
                today_str = data['Date'].strftime('%Y-%m-%d')
                existing_mask = df['交易日期'] == today_str

                # 创建今日记录
                record = {
                    '交易日期': today_str,
                    '交易类型': '观察',
                    '交易方向': '无' if signal_value == 0 else ('买入' if signal_value > 0 else '卖出'),
                    '交易价格': data['Close'],
                    '持仓数量': 0,
                    '交易金额': 0.00,
                    '手续费': 0.00,
                    '净交易额': 0.00,
                    '持仓成本': 0.00,
                    '当前市值': 0.00,
                    '浮动盈亏': 0.00,
                    '实现盈亏': 0.00,
                    '累计盈亏': 0.00,
                    '账户余额': 10000.00,
                    '总资产': 10000.00,
                    '收益率': 0.00,
                    '累计收益率': 0.00,
                    'Y值': round(data['Y_Value'], 4),
                    'X值': round(data['X_Value'], 4),
                    'E值': round(data['E_Value'], 4),
                    'RSI': round(data['RSI'], 2),
                    'MFI': round(data['MFI'], 2),
                    '信号强度': signal,
                    '信号图标': signal_icon,
                    '风险等级': '高风险' if abs(signal_value) > 0 else '中风险',
                    '备注': f"DB:{'✅' if db_success else '❌'} SP:{'✅' if sp_success else '❌'} 价格:{data['Close']:.2f}港元 更新时间:{datetime.now().strftime('%H:%M:%S')}"
                }

                if existing_mask.any():
                    # 更新现有记录
                    for col, value in record.items():
                        df.loc[existing_mask, col] = value
                    print(f"✅ 更新了今日记录: {today_str}")
                else:
                    # 添加新记录
                    new_df = pd.concat([df, pd.DataFrame([record])], ignore_index=True)
                    df = new_df
                    print(f"✅ 添加了新记录: {today_str}")

                # 保存文件
                df.to_excel(excel_file, index=False)
                print(f"✅ Excel记录已更新: {excel_file}")
                print(f"📊 当前信号: {signal_icon} {signal}")

                return True

            except PermissionError:
                print(f"⚠️ 文件被占用，等待 {attempt + 1}/{max_attempts}...")
                if attempt < max_attempts - 1:
                    time.sleep(2)
                    continue
                else:
                    # 最后一次尝试：保存到临时文件
                    temp_file = f"交易记录追踪0023HK_temp_{int(time.time())}.xlsx"
                    df.to_excel(temp_file, index=False)
                    print(f"⚠️ 原文件被占用，已保存到临时文件: {temp_file}")
                    print(f"💡 请手动将 {temp_file} 重命名为 {excel_file}")
                    return True

            except Exception as e:
                print(f"❌ 尝试 {attempt + 1} 失败: {e}")
                if attempt == max_attempts - 1:
                    raise

        return False

    except Exception as e:
        print(f"❌ Excel更新失败: {e}")
        print(f"💡 建议手动检查文件权限或关闭Excel程序")

        # 尝试保存基本信息到文本文件
        try:
            txt_file = f"交易记录_{data['Date'].strftime('%Y%m%d')}.txt"
            with open(txt_file, 'w', encoding='utf-8') as f:
                f.write(f"交易日期: {data['Date']}\n")
                f.write(f"收盘价: {data['Close']:.2f} 港元\n")
                f.write(f"Y值: {data['Y_Value']:.4f}\n")
                f.write(f"X值: {data['X_Value']:.4f}\n")
                f.write(f"E值: {data['E_Value']:.4f}\n")
                f.write(f"交易信号: {signal}\n")
                f.write(f"数据库更新: {'成功' if db_success else '失败'}\n")
                f.write(f"存储过程: {'成功' if sp_success else '失败'}\n")
            print(f"📝 已保存基本信息到: {txt_file}")
        except:
            pass

        return False

def main():
    """主函数"""
    from datetime import datetime

    print("🕔 香港交易所交易时间结束 下午5:00")
    print("📊 开始每日更新流程...")
    print(f"🕐 更新时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)

    try:
        # 1. 获取表结构（用于验证）
        print("1️⃣ 验证数据库连接和表结构...")
        get_eab_table_structure()

        # 2. 从yfinance获取数据并计算指标
        print("\n2️⃣ 获取市场数据和计算技术指标...")
        data = fetch_and_calculate_data()

        if data:
            # 显示今日数据
            print(f"\n📈 今日数据 ({data['Date']}):")
            print(f"   💰 收盘价: {data['Close']:.2f} 港元")
            print(f"   📊 成交量: {data['Volume']:,}")
            print(f"   🎯 Y值: {data['Y_Value']:.4f}")
            print(f"   🎯 X值: {data['X_Value']:.4f}")
            print(f"   ⚡ E值: {data['E_Value']:.4f}")
            print(f"   📈 RSI: {data['RSI']:.2f}")
            print(f"   💹 MFI: {data['MFI']:.2f}")
            print(f"   💰 MoneyFlowRatio: {data['MoneyFlowRatio']:.4f}")

            # 显示交易信号
            signal, signal_icon, _ = calculate_trading_signal(data)
            print(f"   🚦 交易信号: {signal_icon} {signal}")

            # 3. 更新eab_0023hk表
            print("\n3️⃣ 更新数据库...")
            db_success = update_eab_table(data)

            # 4. 调用存储过程
            print("\n4️⃣ 执行存储过程...")
            sp_success = call_stored_procedure()

            # 5. 更新Excel记录
            print("\n5️⃣ 更新Excel记录...")
            excel_success = update_excel_record(data, db_success, sp_success)

            # 显示最终结果
            print("\n" + "=" * 60)
            print("📊 更新结果汇总:")
            print(f"   📋 数据库更新: {'✅ 成功' if db_success else '❌ 失败'}")
            print(f"   🔧 存储过程: {'✅ 成功' if sp_success else '❌ 失败'}")
            print(f"   📄 Excel记录: {'✅ 成功' if excel_success else '❌ 失败'}")

            # 计算成功率
            success_count = sum([db_success, sp_success, excel_success])
            success_rate = (success_count / 3) * 100
            print(f"   📈 成功率: {success_rate:.1f}% ({success_count}/3)")

            if success_rate == 100:
                print("🎉 所有更新任务完成！")
            elif success_rate >= 66:
                print("⚠️ 主要任务完成，部分功能需要检查")
            else:
                print("❌ 多个任务失败，需要手动检查")

        else:
            print("❌ 无法获取市场数据，请检查网络连接或股票代码")
            print("💡 建议:")
            print("   - 检查网络连接")
            print("   - 确认股票代码 0023.HK 正确")
            print("   - 检查yfinance库是否正常工作")

    except KeyboardInterrupt:
        print("\n⚠️ 用户中断操作")
    except Exception as e:
        print(f"\n❌ 程序执行出错: {e}")
        import traceback
        print("🔍 详细错误信息:")
        traceback.print_exc()
        print("\n💡 建议联系技术支持或查看日志文件")

    finally:
        print(f"\n⏰ 更新完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("⏰ 下次更新: 明日下午5:00")
        print("=" * 60)

if __name__ == "__main__":
    main()
