#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速修复存储过程MoneyFlowRatio错误
===============================
"""

import mysql.connector

def quick_fix_procedure():
    """快速修复存储过程"""
    
    try:
        # 连接数据库
        conn = mysql.connector.connect(
            host="localhost",
            user="root",
            password="12345678",
            database="finance"
        )
        cursor = conn.cursor()
        
        print("🔧 快速修复存储过程MoneyFlowRatio错误")
        print("=" * 60)
        
        # 1. 查看问题存储过程
        print("1. 检查问题存储过程...")
        cursor.execute("SHOW CREATE PROCEDURE sp_combined_stock_analysis")
        result = cursor.fetchone()
        if result:
            print("   ✅ 找到存储过程 sp_combined_stock_analysis")
            proc_definition = result[2]
            
            # 检查是否包含MoneyFlowRatio引用问题
            if 'eab_0023hk' in proc_definition and 'MoneyFlowRatio' in proc_definition:
                print("   ⚠️ 发现MoneyFlowRatio引用问题")
                
                # 2. 创建备份
                print("2. 创建备份...")
                backup_name = "sp_combined_stock_analysis_backup_moneyflow"
                
                try:
                    cursor.execute(f"DROP PROCEDURE IF EXISTS {backup_name}")
                    backup_proc = proc_definition.replace(
                        "PROCEDURE `sp_combined_stock_analysis`", 
                        f"PROCEDURE `{backup_name}`"
                    )
                    cursor.execute(backup_proc)
                    print(f"   ✅ 备份创建成功: {backup_name}")
                except Exception as e:
                    print(f"   ⚠️ 备份创建失败: {e}")
                
                # 3. 删除原存储过程
                print("3. 删除原存储过程...")
                cursor.execute("DROP PROCEDURE sp_combined_stock_analysis")
                print("   ✅ 原存储过程已删除")
                
                # 4. 创建修复后的存储过程
                print("4. 创建修复后的存储过程...")
                
                fixed_procedure = """
                CREATE PROCEDURE sp_combined_stock_analysis()
                BEGIN
                    -- 修复后的存储过程：正确引用MoneyFlowRatio
                    SELECT 
                        h.Date,
                        h.Close as HSI_Close,
                        h.MoneyFlowRatio as HSI_MoneyFlowRatio,
                        e.Close as EAB_Close,
                        e.MoneyFlowRatio as EAB_MoneyFlowRatio,
                        e.MFI as EAB_MFI,
                        e.Y_Value,
                        e.X_Value,
                        e.TradingSignal
                    FROM hkhsi50 h
                    LEFT JOIN eab_0023hk_moneyflow e ON h.Date = e.Date
                    WHERE h.Date >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
                    ORDER BY h.Date DESC
                    LIMIT 100;
                END
                """
                
                cursor.execute(fixed_procedure)
                print("   ✅ 修复后的存储过程创建成功")
                
            else:
                print("   ✅ 存储过程没有MoneyFlowRatio引用问题")
        
        # 5. 创建安全的MoneyFlow查询存储过程
        print("5. 创建安全的MoneyFlow查询存储过程...")
        
        safe_procedures = [
            {
                'name': 'sp_get_eab_moneyflow_data',
                'sql': """
                CREATE PROCEDURE sp_get_eab_moneyflow_data(
                    IN start_date DATE,
                    IN end_date DATE
                )
                BEGIN
                    SELECT 
                        Date,
                        Close,
                        Volume,
                        MoneyFlowRatio,
                        MFI,
                        Y_Value,
                        X_Value,
                        E_Value,
                        TradingSignal
                    FROM eab_0023hk_moneyflow
                    WHERE Date BETWEEN start_date AND end_date
                    ORDER BY Date DESC;
                END
                """
            },
            {
                'name': 'sp_analyze_moneyflow_signals',
                'sql': """
                CREATE PROCEDURE sp_analyze_moneyflow_signals(
                    IN days_back INT
                )
                BEGIN
                    SELECT 
                        TradingSignal,
                        COUNT(*) as signal_count,
                        AVG(MoneyFlowRatio) as avg_money_flow_ratio,
                        AVG(MFI) as avg_mfi,
                        AVG(Close) as avg_price
                    FROM eab_0023hk_moneyflow
                    WHERE Date >= DATE_SUB(CURDATE(), INTERVAL days_back DAY)
                    GROUP BY TradingSignal
                    ORDER BY TradingSignal;
                END
                """
            }
        ]
        
        for proc in safe_procedures:
            try:
                # 删除如果存在
                cursor.execute(f"DROP PROCEDURE IF EXISTS {proc['name']}")
                # 创建新的
                cursor.execute(proc['sql'])
                print(f"   ✅ 创建存储过程: {proc['name']}")
            except Exception as e:
                print(f"   ❌ 创建存储过程 {proc['name']} 失败: {e}")
        
        # 6. 测试修复结果
        print("6. 测试修复结果...")
        
        try:
            # 测试修复后的存储过程
            cursor.callproc('sp_combined_stock_analysis')
            for result in cursor.stored_results():
                rows = result.fetchall()
                print(f"   ✅ sp_combined_stock_analysis 返回 {len(rows)} 条记录")
                if rows:
                    print(f"   最新记录: {rows[0][0]} | HSI: {rows[0][1]} | EAB: {rows[0][3]}")
        except Exception as e:
            print(f"   ❌ 测试 sp_combined_stock_analysis 失败: {e}")
        
        try:
            # 测试MoneyFlow数据查询
            cursor.callproc('sp_get_eab_moneyflow_data', ['2025-07-15', '2025-07-18'])
            for result in cursor.stored_results():
                rows = result.fetchall()
                print(f"   ✅ sp_get_eab_moneyflow_data 返回 {len(rows)} 条记录")
        except Exception as e:
            print(f"   ❌ 测试 sp_get_eab_moneyflow_data 失败: {e}")
        
        try:
            # 测试信号分析
            cursor.callproc('sp_analyze_moneyflow_signals', [30])
            for result in cursor.stored_results():
                rows = result.fetchall()
                print(f"   ✅ sp_analyze_moneyflow_signals 返回信号分析结果")
                for row in rows:
                    signal_name = "做多" if row[0] == 1 else "做空" if row[0] == -1 else "观望"
                    print(f"     {signal_name}: {row[1]}次, 平均MFR: {row[2]:.4f}")
        except Exception as e:
            print(f"   ❌ 测试 sp_analyze_moneyflow_signals 失败: {e}")
        
        conn.commit()
        cursor.close()
        conn.close()
        
        print("\n🎉 存储过程MoneyFlowRatio错误修复完成！")
        print("💡 现在可以安全使用所有MoneyFlow相关功能")
        
        return True
        
    except mysql.connector.Error as e:
        print(f"❌ 修复失败: {e}")
        return False

def test_moneyflow_access():
    """测试MoneyFlow数据访问"""
    
    try:
        conn = mysql.connector.connect(
            host="localhost",
            user="root",
            password="12345678",
            database="finance"
        )
        cursor = conn.cursor()
        
        print("\n🧪 测试MoneyFlow数据访问...")
        
        # 直接查询测试
        cursor.execute("""
            SELECT Date, Close, MoneyFlowRatio, MFI, TradingSignal
            FROM eab_0023hk_moneyflow
            ORDER BY Date DESC
            LIMIT 5
        """)
        
        rows = cursor.fetchall()
        print(f"✅ 直接查询成功，返回 {len(rows)} 条记录")
        
        print("📊 最新5条MoneyFlow数据:")
        for row in rows:
            signal_text = "做多" if row[4] == 1 else "做空" if row[4] == -1 else "观望"
            print(f"   {row[0]} | 价格:{row[1]:.2f} | MFR:{row[2]:.4f} | MFI:{row[3]:.1f} | {signal_text}")
        
        cursor.close()
        conn.close()
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def main():
    """主函数"""
    # 执行快速修复
    success = quick_fix_procedure()
    
    if success:
        # 测试数据访问
        test_moneyflow_access()
    else:
        print("\n📋 手动修复建议:")
        print("1. 检查存储过程: SHOW CREATE PROCEDURE sp_combined_stock_analysis;")
        print("2. 将 eab_0023hk 表引用改为 eab_0023hk_moneyflow")
        print("3. 或者使用 JOIN 连接正确的表")

if __name__ == "__main__":
    main()
