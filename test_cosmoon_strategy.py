#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
使用Cosmoon策略测试test表数据
===========================

策略参数：
- 初始资金: 30,000港币
- 高值盈利区 (Y>0.43, X>0.43): 买涨，止盈+2%，止损-1%
- 控股商控制区 (0.333<Y<0.4): 观望
- 强亏损区 (Y<0.25或X<0.25): 买跌，止盈+2%，止损-1%
- 其他区域: 买跌，止盈+1%，止损-2%

作者: Cosmoon NG
日期: 2025年7月
"""

import pymysql
import pandas as pd
import numpy as np
from datetime import datetime

class CosmoonStrategyTester:
    def __init__(self):
        """初始化Cosmoon策略测试器"""
        self.db_config = {
            'host': 'localhost',
            'port': 3306,
            'database': 'finance',
            'user': 'root',
            'password': '12345678',
            'charset': 'utf8mb4'
        }
        self.connection = None
        self.initial_capital = 30000
        
        # Cosmoon策略参数
        self.strategy_params = {
            'high_profit_y': 0.43,
            'high_profit_x': 0.43,
            'control_zone_min': 0.333,
            'control_zone_max': 0.4,
            'strong_loss_y': 0.25,
            'strong_loss_x': 0.25,
            
            # 高值盈利区参数
            'high_profit_take_profit': 0.02,   # 2%止盈
            'high_profit_stop_loss': 0.01,     # 1%止损
            
            # 强亏损区参数
            'strong_loss_take_profit': 0.02,   # 2%止盈
            'strong_loss_stop_loss': 0.01,     # 1%止损
            
            # 其他区域参数
            'other_take_profit': 0.01,         # 1%止盈
            'other_stop_loss': 0.02,           # 2%止损
            
            'transaction_cost': 0.0025,
            'position_ratio': 0.08,
        }
        
    def connect_database(self):
        """连接数据库"""
        try:
            self.connection = pymysql.connect(**self.db_config)
            print(f"✅ 成功连接MySQL数据库: {self.db_config['host']}")
            return True
        except Exception as e:
            print(f"❌ 连接数据库失败: {e}")
            return False
    
    def load_test_data(self):
        """从test表加载数据"""
        try:
            print("📊 从test表加载100行数据...")
            
            cursor = self.connection.cursor()
            cursor.execute("""
                SELECT 交易序号, 开仓日期, 平仓日期, 持仓天数, 交易方向,
                       close, 平仓价格, 交易股数, 交易金额, 净利润, `收益率%`,
                       `控制系数`, `资金流比例`, E值, 策略区域, 交易理由, 平仓原因,
                       `真实流入`, `真实流出`
                FROM test
                ORDER BY 交易序号
                LIMIT 100
            """)
            
            data = cursor.fetchall()
            
            # 转换为DataFrame
            columns = ['交易序号', '开仓日期', '平仓日期', '持仓天数', '交易方向',
                      '开仓价格', '平仓价格', '交易股数', '交易金额', '净利润', '收益率',
                      'Y值', 'X值', 'E值', '策略区域', '交易理由', '平仓原因',
                      '真实流入', '真实流出']
            
            df = pd.DataFrame(data, columns=columns)
            
            print(f"✅ 成功加载test表数据: {len(df)} 条记录")
            print(f"📅 数据期间: {df['开仓日期'].min()} 至 {df['开仓日期'].max()}")
            
            return df
            
        except Exception as e:
            print(f"❌ 加载test表数据失败: {e}")
            return None
    
    def classify_strategy_zones(self, df):
        """根据Cosmoon策略重新分类策略区域"""
        print("🎯 根据Cosmoon策略重新分类策略区域...")
        
        # 重新分类策略区域
        conditions = [
            (df['Y值'] > self.strategy_params['high_profit_y']) & 
            (df['X值'] > self.strategy_params['high_profit_x']),  # 高值盈利区
            
            (df['Y值'] > self.strategy_params['control_zone_min']) & 
            (df['Y值'] < self.strategy_params['control_zone_max']),  # 控股商控制区
            
            (df['Y值'] < self.strategy_params['strong_loss_y']) | 
            (df['X值'] < self.strategy_params['strong_loss_x']),  # 强亏损区
        ]
        
        choices = ['高值盈利区', '控股商控制区', '强亏损区']
        df['Cosmoon策略区域'] = np.select(conditions, choices, default='其他区域')
        
        # 统计策略区域分布
        zone_counts = df['Cosmoon策略区域'].value_counts()
        total = len(df)
        print(f"📊 Cosmoon策略区域分布:")
        for zone, count in zone_counts.items():
            print(f"   • {zone}: {count} 次 ({count/total*100:.1f}%)")
        
        return df
    
    def simulate_cosmoon_strategy(self, df):
        """模拟Cosmoon策略交易"""
        print("🎯 开始模拟Cosmoon策略交易...")
        print("💰 初始资金: 30,000港币")
        
        results = []
        current_cash = self.initial_capital
        
        for _, row in df.iterrows():
            trade_id = row['交易序号']
            open_date = row['开仓日期']
            open_price = row['开仓价格']  # 使用close列作为开仓价格
            close_price = row['平仓价格']
            y_val = row['Y值']  # Y = 控制系数
            x_val = row['X值']  # X = 资金流比例
            e_val = row['E值']
            zone = row['Cosmoon策略区域']
            real_flow_in = row['真实流入']
            real_flow_out = row['真实流出']
            
            # 确定Cosmoon策略
            action = None
            direction = None
            take_profit = None
            stop_loss = None
            reason = None
            
            if zone == '控股商控制区':
                action = '观望'
                direction = '观望'
                reason = f'控股商控制区：控制系数Y={y_val:.3f}在0.333-0.4之间，观望'
            elif zone == '高值盈利区':
                action = '买入'
                direction = '做多'
                take_profit = self.strategy_params['high_profit_take_profit']
                stop_loss = self.strategy_params['high_profit_stop_loss']
                reason = f'高值盈利区：控制系数Y={y_val:.3f}>0.43且资金流比例X={x_val:.3f}>0.43，买涨策略'
            elif zone == '强亏损区':
                action = '卖出'
                direction = '做空'
                take_profit = self.strategy_params['strong_loss_take_profit']
                stop_loss = self.strategy_params['strong_loss_stop_loss']
                reason = f'强亏损区：控制系数Y={y_val:.3f}<0.25或资金流比例X={x_val:.3f}<0.25，买跌策略'
            elif zone == '其他区域':
                action = '卖出'
                direction = '做空'
                take_profit = self.strategy_params['other_take_profit']
                stop_loss = self.strategy_params['other_stop_loss']
                reason = f'其他区域：控制系数Y={y_val:.3f}，资金流比例X={x_val:.3f}，买跌策略'
            
            # 计算交易结果
            if action == '观望':
                # 观望不交易
                net_profit = 0
                profit_pct = 0
                exit_reason = '观望'
                actual_value = 0
                shares = 0
            else:
                # 计算仓位
                position_value = current_cash * self.strategy_params['position_ratio']
                shares = int(position_value / open_price / 100) * 100
                actual_value = shares * open_price
                
                if shares >= 100 and current_cash > actual_value:
                    # 计算盈亏
                    if direction == '做多':
                        profit_pct = (close_price - open_price) / open_price
                        if profit_pct >= take_profit:
                            exit_reason = '止盈'
                            profit_pct = take_profit
                        elif profit_pct <= -stop_loss:
                            exit_reason = '止损'
                            profit_pct = -stop_loss
                        else:
                            exit_reason = '到期平仓'
                    else:  # 做空
                        profit_pct = (open_price - close_price) / open_price
                        if profit_pct >= take_profit:
                            exit_reason = '止盈'
                            profit_pct = take_profit
                        elif profit_pct <= -stop_loss:
                            exit_reason = '止损'
                            profit_pct = -stop_loss
                        else:
                            exit_reason = '到期平仓'
                    
                    # 计算净利润
                    gross_profit = profit_pct * actual_value
                    transaction_cost = actual_value * self.strategy_params['transaction_cost'] * 2
                    net_profit = gross_profit - transaction_cost
                    
                    # 更新现金
                    current_cash += net_profit
                else:
                    # 资金不足
                    net_profit = 0
                    profit_pct = 0
                    exit_reason = '资金不足'
                    actual_value = 0
                    shares = 0
            
            # 记录结果
            result = {
                '交易序号': trade_id,
                '开仓日期': open_date,
                '开仓价格': round(open_price, 2),
                '平仓价格': round(close_price, 2),
                'Y值': round(y_val, 3),
                'X值': round(x_val, 3),
                'E值': round(e_val, 3),

                'Cosmoon策略区域': zone,
                'Cosmoon交易方向': direction,
                'Cosmoon交易理由': reason,
                '交易股数': shares,
                '交易金额': round(actual_value, 0),
                '收益率%': round(profit_pct * 100, 2),
                '净利润': round(net_profit, 0),
                '平仓原因': exit_reason,
                '账户余额': round(current_cash, 0),
                '真实流入': round(real_flow_in, 0),
                '真实流出': round(real_flow_out, 0),
                '资金流比例': round(x_val, 3),
                '止盈参数': f"{take_profit*100:.0f}%" if take_profit else "N/A",
                '止损参数': f"{stop_loss*100:.0f}%" if stop_loss else "N/A"
            }
            
            results.append(result)
        
        print(f"✅ Cosmoon策略模拟完成!")
        print(f"💰 最终资金: {current_cash:,.0f}港币")
        print(f"📈 总收益: {current_cash - self.initial_capital:+,.0f}港币")
        print(f"📊 总收益率: {(current_cash / self.initial_capital - 1) * 100:+.2f}%")
        
        return pd.DataFrame(results)
    
    def analyze_cosmoon_results(self, results_df):
        """分析Cosmoon策略结果"""
        print("\n📊 Cosmoon策略结果分析:")
        print("="*80)
        
        # 基础统计
        total_trades = len(results_df)
        actual_trades = len(results_df[results_df['Cosmoon交易方向'] != '观望'])
        observe_trades = len(results_df[results_df['Cosmoon交易方向'] == '观望'])
        winning_trades = len(results_df[results_df['净利润'] > 0])
        losing_trades = len(results_df[results_df['净利润'] < 0])
        
        total_profit = results_df['净利润'].sum()
        final_capital = results_df['账户余额'].iloc[-1]
        total_return = (final_capital / self.initial_capital - 1) * 100
        
        print(f"📈 基础统计:")
        print(f"   • 总记录数: {total_trades}")
        print(f"   • 实际交易: {actual_trades}")
        print(f"   • 观望次数: {observe_trades}")
        print(f"   • 盈利次数: {winning_trades}")
        print(f"   • 亏损次数: {losing_trades}")
        print(f"   • 胜率: {winning_trades/actual_trades*100:.1f}%" if actual_trades > 0 else "   • 胜率: N/A")
        print(f"   • 总盈亏: {total_profit:+,.0f}港币")
        print(f"   • 总收益率: {total_return:+.2f}%")
        
        # 按策略区域分析
        print(f"\n📊 按Cosmoon策略区域分析:")
        for zone in results_df['Cosmoon策略区域'].unique():
            zone_data = results_df[results_df['Cosmoon策略区域'] == zone]
            count = len(zone_data)
            zone_profit = zone_data['净利润'].sum()
            zone_trades = len(zone_data[zone_data['Cosmoon交易方向'] != '观望'])
            zone_wins = len(zone_data[zone_data['净利润'] > 0])
            
            if zone_trades > 0:
                zone_win_rate = zone_wins / zone_trades * 100
                avg_profit = zone_profit / zone_trades
                print(f"• {zone}: {count}次({zone_trades}交易), 盈亏{zone_profit:+,.0f}港币, "
                      f"胜率{zone_win_rate:.1f}%, 平均{avg_profit:+.0f}港币")
            else:
                print(f"• {zone}: {count}次(全观望), 盈亏{zone_profit:+,.0f}港币")
        
        # 按交易方向分析
        print(f"\n📊 按交易方向分析:")
        for direction in results_df['Cosmoon交易方向'].unique():
            dir_data = results_df[results_df['Cosmoon交易方向'] == direction]
            count = len(dir_data)
            dir_profit = dir_data['净利润'].sum()
            
            if direction != '观望':
                dir_wins = len(dir_data[dir_data['净利润'] > 0])
                dir_win_rate = dir_wins / count * 100
                avg_profit = dir_profit / count
                print(f"• {direction}: {count}次, 盈亏{dir_profit:+,.0f}港币, "
                      f"胜率{dir_win_rate:.1f}%, 平均{avg_profit:+.0f}港币")
            else:
                print(f"• {direction}: {count}次, 盈亏{dir_profit:+,.0f}港币")
        
        # 平仓原因分析
        print(f"\n📊 平仓原因分析:")
        exit_reasons = results_df[results_df['Cosmoon交易方向'] != '观望']['平仓原因'].value_counts()
        for reason, count in exit_reasons.items():
            reason_data = results_df[results_df['平仓原因'] == reason]
            reason_profit = reason_data['净利润'].sum()
            avg_profit = reason_profit / count
            print(f"• {reason}: {count}次, 盈亏{reason_profit:+,.0f}港币, 平均{avg_profit:+.0f}港币")
        
        return results_df
    
    def create_cosmoon_excel(self, results_df):
        """创建Cosmoon策略Excel文件"""
        print("\n📄 创建Cosmoon策略Excel文件...")
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"Cosmoon策略测试结果_{timestamp}.xlsx"
        
        with pd.ExcelWriter(filename, engine='openpyxl') as writer:
            # 主要结果
            results_df.to_excel(writer, sheet_name='Cosmoon策略结果', index=False)
            
            # 汇总统计
            summary_data = {
                '项目': [
                    '策略名称', '初始资金(港币)', '最终资金(港币)', '总盈亏(港币)', '总收益率(%)',
                    '总记录数', '实际交易数', '观望次数', '盈利次数', '亏损次数', '胜率(%)',
                    '最大盈利(港币)', '最大亏损(港币)', '平均盈亏(港币)'
                ],
                '数值': [
                    'Cosmoon策略', self.initial_capital, results_df['账户余额'].iloc[-1], 
                    results_df['净利润'].sum(), (results_df['账户余额'].iloc[-1] / self.initial_capital - 1) * 100,
                    len(results_df), len(results_df[results_df['Cosmoon交易方向'] != '观望']),
                    len(results_df[results_df['Cosmoon交易方向'] == '观望']),
                    len(results_df[results_df['净利润'] > 0]), len(results_df[results_df['净利润'] < 0]),
                    len(results_df[results_df['净利润'] > 0]) / len(results_df[results_df['Cosmoon交易方向'] != '观望']) * 100 if len(results_df[results_df['Cosmoon交易方向'] != '观望']) > 0 else 0,
                    results_df['净利润'].max(), results_df['净利润'].min(),
                    results_df[results_df['Cosmoon交易方向'] != '观望']['净利润'].mean() if len(results_df[results_df['Cosmoon交易方向'] != '观望']) > 0 else 0
                ]
            }
            summary_df = pd.DataFrame(summary_data)
            summary_df.to_excel(writer, sheet_name='汇总统计', index=False)
            
            # 策略说明
            strategy_explanation = {
                '策略区域': [
                    '高值盈利区',
                    '控股商控制区',
                    '强亏损区',
                    '其他区域'
                ],
                '条件': [
                    'Y>0.43 且 X>0.43',
                    '0.333<Y<0.4',
                    'Y<0.25 或 X<0.25',
                    '其他情况'
                ],
                '操作': [
                    '买涨',
                    '观望',
                    '买跌',
                    '买跌'
                ],
                '止盈': [
                    '+2%',
                    'N/A',
                    '+2%',
                    '+1%'
                ],
                '止损': [
                    '-1%',
                    'N/A',
                    '-1%',
                    '-2%'
                ]
            }
            strategy_df = pd.DataFrame(strategy_explanation)
            strategy_df.to_excel(writer, sheet_name='Cosmoon策略说明', index=False)
        
        print(f"✅ Cosmoon策略Excel文件已创建: {filename}")
        return filename
    
    def close_connection(self):
        """关闭数据库连接"""
        if self.connection:
            self.connection.close()
            print("\n🔒 数据库连接已关闭")

def main():
    """主函数"""
    print("🏦 Cosmoon策略测试 - test表100行数据")
    print("="*60)
    print("💰 初始资金: 30,000港币")
    print("📊 Cosmoon策略参数:")
    print("   • 高值盈利区 (Y>0.43, X>0.43): 买涨，止盈+2%，止损-1%")
    print("   • 控股商控制区 (0.333<Y<0.4): 观望")
    print("   • 强亏损区 (Y<0.25或X<0.25): 买跌，止盈+2%，止损-1%")
    print("   • 其他区域: 买跌，止盈+1%，止损-2%")
    
    # 创建测试器
    tester = CosmoonStrategyTester()
    
    # 连接数据库
    if not tester.connect_database():
        return
    
    # 加载test表数据
    df = tester.load_test_data()
    if df is None:
        tester.close_connection()
        return
    
    # 重新分类策略区域
    df = tester.classify_strategy_zones(df)
    
    # 模拟Cosmoon策略
    results_df = tester.simulate_cosmoon_strategy(df)
    
    # 分析结果
    tester.analyze_cosmoon_results(results_df)
    
    # 创建Excel文件
    filename = tester.create_cosmoon_excel(results_df)
    
    # 关闭连接
    tester.close_connection()
    
    print(f"\n🎉 Cosmoon策略测试完成!")
    print(f"📄 结果文件: {filename}")

if __name__ == "__main__":
    main()
