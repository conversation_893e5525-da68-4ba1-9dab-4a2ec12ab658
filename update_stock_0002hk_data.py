#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
更新中电控股0002.HK股票数据到今日
================================

使用yfinance获取0002.HK (中电控股/CLP Holdings) 的最新数据
并保存为CSV文件，包含完整的技术指标计算

作者: Cosmoon NG
日期: 2025年7月24日
"""

import yfinance as yf
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

class Stock0002HKUpdater:
    def __init__(self):
        """初始化0002.HK数据更新器"""
        self.symbol = "0002.HK"  # 中电控股
        self.data = None
        self.today = datetime.now().strftime('%Y-%m-%d')
        
    def fetch_latest_data(self, period="25y"):
        """获取最新的股票数据"""
        print(f"📊 更新{self.symbol} (中电控股) 股票数据到今日...")
        print(f"📅 更新日期: {self.today}")
        
        try:
            # 获取股票数据
            ticker = yf.Ticker(self.symbol)
            print(f"   🔄 正在从Yahoo Finance获取{self.symbol}数据...")
            
            # 获取历史数据 - 使用25年数据确保有足够的历史数据
            end_date = datetime.now()
            start_date = end_date - timedelta(days=25*365)
            
            hist_data = ticker.history(start=start_date, end=end_date, auto_adjust=True, prepost=True)
            
            if hist_data.empty:
                print(f"   ❌ 无法获取{self.symbol}数据，尝试备用方法...")
                # 备用方法：使用yf.download
                hist_data = yf.download(self.symbol, start=start_date, end=end_date, progress=False)
            
            if hist_data.empty:
                print(f"   ❌ 仍无法获取数据")
                return False
            
            # 数据预处理
            self.data = pd.DataFrame({
                'date': hist_data.index,
                'open': hist_data['Open'],
                'high': hist_data['High'],
                'low': hist_data['Low'],
                'close': hist_data['Close'],
                'volume': hist_data['Volume']
            })
            
            # 清理数据
            self.data = self.data.dropna().sort_values('date').reset_index(drop=True)
            
            print(f"   ✅ 成功获取 {len(self.data)} 条数据")
            print(f"   📅 数据范围: {self.data['date'].min().strftime('%Y-%m-%d')} 至 {self.data['date'].max().strftime('%Y-%m-%d')}")
            print(f"   📈 最新价格: {self.data['close'].iloc[-1]:.2f} 港元")
            print(f"   📊 价格范围: {self.data['close'].min():.2f} - {self.data['close'].max():.2f} 港元")
            
            return True
            
        except Exception as e:
            print(f"   ❌ 数据获取失败: {e}")
            return False
    
    def calculate_technical_indicators(self):
        """计算技术指标"""
        print(f"\n🧮 计算技术指标...")
        
        # === X值计算 (MFI归一化) ===
        print("   计算X值 (MFI归一化)...")
        
        # 1. 计算典型价格和资金流
        self.data['typical_price'] = (self.data['high'] + self.data['low'] + self.data['close']) / 3
        self.data['money_flow'] = self.data['typical_price'] * self.data['volume']
        self.data['price_change'] = self.data['typical_price'].diff()
        
        # 2. 分离正负资金流
        self.data['positive_mf'] = np.where(self.data['price_change'] > 0, self.data['money_flow'], 0)
        self.data['negative_mf'] = np.where(self.data['price_change'] < 0, self.data['money_flow'], 0)
        
        # 3. 计算14日正负资金流总和
        period = 14
        self.data['positive_mf_14'] = self.data['positive_mf'].rolling(period).sum()
        self.data['negative_mf_14'] = self.data['negative_mf'].rolling(period).sum()
        
        # 4. 计算资金流比率
        self.data['money_flow_ratio'] = self.data['positive_mf_14'] / (self.data['negative_mf_14'] + 1e-10)
        
        # 5. 计算MFI (Money Flow Index)
        self.data['mfi'] = 100 - (100 / (1 + self.data['money_flow_ratio']))
        
        # 6. X值 = MFI归一化到0-1
        self.data['x_value'] = self.data['mfi'] / 100
        self.data['x_value'] = self.data['x_value'].fillna(0.5).clip(0.1, 0.9)
        
        # === Y值计算 (Full_Y) ===
        print("   计算Y值 (Full_Y)...")
        
        # 1. 计算RSI
        delta = self.data['close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
        rs = gain / loss
        self.data['rsi'] = 100 - (100 / (1 + rs))
        self.data['rsi'] = self.data['rsi'].fillna(50)
        
        # 2. 计算价格动量
        self.data['price_momentum'] = self.data['close'].pct_change(10)
        self.data['price_momentum'] = self.data['price_momentum'].fillna(0)
        
        # 3. Full_Y计算 (控制系数)
        self.data['y_value'] = (self.data['rsi'] / 100 + np.tanh(self.data['price_momentum'] * 5) + 1) / 2
        self.data['y_value'] = np.clip(self.data['y_value'], 0.1, 0.9)
        
        # === 其他技术指标 ===
        print("   计算其他技术指标...")
        
        # 移动平均线
        self.data['ma20'] = self.data['close'].rolling(20).mean()
        self.data['ma60'] = self.data['close'].rolling(60).mean()
        
        # E值 (策略指标)
        self.data['e_value'] = (8 * self.data['x_value'] - 3) * self.data['y_value'] - 3 * self.data['x_value'] + 1
        
        # 中位价格 (22日滚动中位数)
        self.data['median_price'] = self.data['close'].rolling(22).median()
        
        # 控股商标记 (价格高于中位数为1，否则为0)
        self.data['controlling_mark'] = np.where(self.data['close'] > self.data['median_price'], 1, 0)
        
        print(f"   ✅ 技术指标计算完成")
        print(f"   📊 X值(MFI)范围: {self.data['x_value'].min():.3f} - {self.data['x_value'].max():.3f}")
        print(f"   📊 Y值(Full_Y)范围: {self.data['y_value'].min():.3f} - {self.data['y_value'].max():.3f}")
        print(f"   📊 E值范围: {self.data['e_value'].min():.3f} - {self.data['e_value'].max():.3f}")
        print(f"   📊 MFI范围: {self.data['mfi'].min():.1f} - {self.data['mfi'].max():.1f}")
        print(f"   📊 RSI范围: {self.data['rsi'].min():.1f} - {self.data['rsi'].max():.1f}")
    
    def save_data(self):
        """保存数据到CSV文件"""
        print(f"\n💾 保存数据到文件...")
        
        try:
            # 生成文件名
            filename = f"stock_0002hk_data_{self.today.replace('-', '')}.csv"
            
            # 选择要保存的列
            columns_to_save = [
                'date', 'open', 'high', 'low', 'close', 'volume',
                'x_value', 'y_value', 'e_value', 'mfi', 'rsi',
                'ma20', 'ma60', 'median_price', 'controlling_mark',
                'typical_price', 'money_flow', 'price_momentum'
            ]
            
            # 保存数据
            save_data = self.data[columns_to_save].copy()
            save_data.to_csv(filename, index=False, encoding='utf-8-sig')
            
            print(f"   ✅ 数据已保存到: {filename}")
            print(f"   📊 保存了 {len(save_data)} 条记录")
            print(f"   📋 包含 {len(columns_to_save)} 个字段")
            
            # 显示最新几条数据
            print(f"\n📈 最新5条数据预览:")
            latest_data = save_data.tail(5)[['date', 'close', 'x_value', 'y_value', 'e_value']]
            for _, row in latest_data.iterrows():
                print(f"   {row['date'].strftime('%Y-%m-%d')}: 收盘={row['close']:.2f}, X={row['x_value']:.3f}, Y={row['y_value']:.3f}, E={row['e_value']:.3f}")
            
            return filename
            
        except Exception as e:
            print(f"   ❌ 保存数据失败: {e}")
            return None
    
    def get_latest_info(self):
        """获取最新股票信息"""
        if self.data is None or len(self.data) == 0:
            return None
        
        latest = self.data.iloc[-1]
        return {
            'date': latest['date'].strftime('%Y-%m-%d'),
            'close': latest['close'],
            'x_value': latest['x_value'],
            'y_value': latest['y_value'],
            'e_value': latest['e_value'],
            'mfi': latest['mfi'],
            'rsi': latest['rsi']
        }

def main():
    """主函数"""
    print("🎯 中电控股0002.HK股票数据更新工具")
    print("=" * 50)
    print("📋 功能:")
    print("   📊 从Yahoo Finance获取最新数据")
    print("   🧮 计算完整技术指标 (X值、Y值、E值等)")
    print("   💾 保存为CSV文件")
    print("   📈 显示最新市场信息")
    
    # 创建更新器
    updater = Stock0002HKUpdater()
    
    # 获取最新数据
    if not updater.fetch_latest_data():
        print("❌ 数据获取失败，程序退出")
        return
    
    # 计算技术指标
    updater.calculate_technical_indicators()
    
    # 保存数据
    filename = updater.save_data()
    
    if filename:
        # 显示最新信息
        latest_info = updater.get_latest_info()
        if latest_info:
            print(f"\n🎯 最新市场信息 ({latest_info['date']}):")
            print(f"   💰 收盘价: {latest_info['close']:.2f} 港元")
            print(f"   📊 X值(MFI): {latest_info['x_value']:.3f}")
            print(f"   📊 Y值(Full_Y): {latest_info['y_value']:.3f}")
            print(f"   📊 E值: {latest_info['e_value']:.3f}")
            print(f"   📊 MFI: {latest_info['mfi']:.1f}")
            print(f"   📊 RSI: {latest_info['rsi']:.1f}")
        
        print(f"\n🎉 0002.HK数据更新完成!")
        print(f"📁 数据文件: {filename}")
        print(f"💡 可以使用此数据进行策略分析和回测")
    else:
        print("❌ 数据保存失败")

if __name__ == "__main__":
    main()
