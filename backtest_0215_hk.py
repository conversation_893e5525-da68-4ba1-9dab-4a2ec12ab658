#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
和记电讯香港 (0215.HK) Cosmoon回归线策略回测
===========================================
使用Cosmoon NG回归线策略 + 凯利公式 + 风险管理
回测期间：10年历史数据
初始资金：2500港币
"""

import yfinance as yf
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from scipy import stats
import warnings
warnings.filterwarnings('ignore')

class CosmoonRegressionStrategy0215:
    def __init__(self):
        """初始化和记电讯香港回测系统"""
        self.symbol = '0215.HK'
        self.name = '和记电讯香港'

        # 策略参数 (参考reference.py)
        self.initial_capital = 2500         # 初始资金2500港币
        self.monthly_addition = 0           # 每月追加资金(设为0)
        self.take_profit_long = 0.012       # 多头止盈 1.2%
        self.stop_loss_long = 0.006         # 多头止损 0.6%
        self.take_profit_short = 0.008      # 空头止盈 0.8%
        self.stop_loss_short = 0.012        # 空头止损 1.2%
        self.regression_threshold = 0.002   # 回归线阈值 0.2%
        self.position = 0                   # 当前持仓，1为多头，-1为空头，0为空仓
        self.current_price = 0              # 当前持仓价格
        self.kelly_fraction = 0.5           # 凯利公式分数，用于控制风险

        # 交易记录
        self.trades = []
        self.equity_curve = []
        self.data = None

    def load_data(self):
        """加载10年历史数据"""
        print(f"📊 加载{self.symbol} {self.name} 10年历史数据...")

        try:
            # 获取10年数据
            end_date = datetime.now()
            start_date = end_date - timedelta(days=10*365)

            ticker = yf.Ticker(self.symbol)
            hist = ticker.history(start=start_date, end=end_date)

            if hist.empty:
                print(f"❌ 无法获取{self.symbol}数据")
                return False

            # 准备数据
            self.data = hist.copy()
            self.data.reset_index(inplace=True)
            self.data['date'] = pd.to_datetime(self.data['Date'])
            self.data.rename(columns={
                'Open': 'open',
                'High': 'high',
                'Low': 'low',
                'Close': 'close',
                'Volume': 'volume'
            }, inplace=True)

            # 计算技术指标
            self.calculate_regression_line()

            print(f"✅ 成功加载{len(self.data)}天数据 ({start_date.strftime('%Y-%m-%d')} 至 {end_date.strftime('%Y-%m-%d')})")
            return True

        except Exception as e:
            print(f"❌ 数据加载失败: {e}")
            return False

    def calculate_regression_line(self):
        """计算回归线和技术指标 (参考reference.py)"""
        print("🧮 计算回归线和技术指标...")

        # 计算移动平均线
        self.data['ma_20'] = self.data['close'].rolling(20).mean()
        self.data['ma_60'] = self.data['close'].rolling(60).mean()

        # 计算RSI
        delta = self.data['close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
        rs = gain / loss
        self.data['rsi'] = 100 - (100 / (1 + rs))

        # 计算成交量比率
        volume_ma = self.data['volume'].rolling(20).mean()
        self.data['volume_ratio'] = self.data['volume'] / volume_ma

        # 计算资金流入比率 (简化版)
        price_change = self.data['close'].pct_change()
        volume_weighted = self.data['volume'] * price_change
        volume_weighted_ma = volume_weighted.rolling(20).mean()
        self.data['inflow_ratio'] = np.clip((volume_weighted_ma + 1) / 2, 0, 1)

        # 计算60日回归线
        window = 60
        self.data['regression_line'] = self.data['close'].rolling(window=window).apply(
            lambda x: stats.linregress(range(len(x)), x)[0] * (window-1) + stats.linregress(range(len(x)), x)[1]
        )

        # 计算价格相对回归线的位置（百分比）
        self.data['price_position'] = (self.data['close'] - self.data['regression_line']) / self.data['regression_line']

        # 计算趋势强度（回归线斜率）
        self.data['trend_strength'] = self.data['regression_line'].diff() / self.data['regression_line'].shift(1)

        # 计算波动率（20日）
        self.data['volatility'] = self.data['close'].pct_change().rolling(window=20).std()

        # 计算RSI的变化率
        self.data['rsi_change'] = self.data['rsi'].diff()

        # 填充缺失值
        self.data.fillna(method='ffill', inplace=True)
        self.data.fillna(0, inplace=True)

        # 调试信息：检查关键指标
        print("✅ 技术指标计算完成")
        print(f"📊 数据样本 (最近5天):")
        recent_data = self.data.tail(5)[['date', 'close', 'regression_line', 'price_position', 'rsi', 'volume_ratio']]
        for _, row in recent_data.iterrows():
            print(f"   {row['date'].strftime('%Y-%m-%d')}: 价格={row['close']:.3f}, 回归线={row['regression_line']:.3f}, "
                  f"位置={row['price_position']:.3f}, RSI={row['rsi']:.1f}, 成交量比={row['volume_ratio']:.2f}")

        # 检查是否有有效的回归线数据
        valid_regression = self.data['regression_line'].notna().sum()
        print(f"📈 有效回归线数据: {valid_regression}/{len(self.data)} 天")

    def calculate_kelly(self, win_rate, profit_ratio, loss_ratio):
        """计算凯利公式建议仓位"""
        if win_rate <= 0 or profit_ratio <= 0:
            return 0.1  # 返回最小仓位而不是0
        q = 1 - win_rate
        kelly = (win_rate * profit_ratio - q * loss_ratio) / profit_ratio
        kelly *= self.kelly_fraction  # 使用保守的凯利分数
        return max(0.1, min(kelly, 0.5))  # 最大仓位限制在50%，最小10%

    def get_position_size(self, price, capital, volatility):
        """计算仓位大小"""
        # 基于历史数据计算胜率和盈亏比
        if len(self.trades) < 10:
            win_rate = 0.5
        else:
            trades_df = pd.DataFrame(self.trades)
            win_rate = len(trades_df[trades_df['profit'] > 0]) / len(trades_df)

        # 根据波动率调整盈亏比
        if self.position == 1:  # 多头
            profit_ratio = self.take_profit_long
            loss_ratio = self.stop_loss_long
        else:  # 空头
            profit_ratio = self.take_profit_short
            loss_ratio = self.stop_loss_short

        # 使用凯利公式计算仓位比例
        kelly = self.calculate_kelly(win_rate, profit_ratio, loss_ratio)

        # 根据波动率调整仓位
        volatility_factor = 1 - min(volatility * 100, 0.5)  # 波动率越大，仓位越小

        return capital * kelly * volatility_factor

    def add_monthly_capital(self, date, capital):
        """每月增加资金"""
        # 获取上一次操作的月份
        last_month = getattr(self, 'last_month', None)
        current_month = date.replace(day=1)

        # 如果是新的月份，增加资金
        if last_month is None or current_month > last_month:
            self.last_month = current_month
            return capital + self.monthly_addition

        return capital

    def check_entry_conditions(self, row):
        """检查入场条件 (大幅放宽以测试策略)"""
        # 多头条件：价格低于回归线
        if (row['price_position'] < 0 and  # 价格低于回归线
            row['rsi'] < 50):  # RSI低于50
            return 1

        # 空头条件：价格高于回归线
        elif (row['price_position'] > 0 and  # 价格高于回归线
              row['rsi'] > 50):  # RSI高于50
            return -1

        return 0

    def run_backtest(self):
        """运行回归线策略回测"""
        print(f"\n🚀 开始{self.name}({self.symbol}) 回归线策略回测...")
        print("="*80)
        print(f"💰 初始资金: {self.initial_capital:,} 港币")
        print(f"📊 策略核心: 回归线 + RSI + 凯利公式 + 风险管理")
        print(f"📈 多头条件: 价格低于回归线{self.regression_threshold*100:.1f}% + RSI<30上升 + 成交量活跃")
        print(f"📉 空头条件: 价格高于回归线{self.regression_threshold*100:.1f}% + RSI>70下降 + 成交量活跃")
        print(f"🎯 止盈止损: 多头{self.take_profit_long*100:.1f}%/{self.stop_loss_long*100:.1f}%, 空头{self.take_profit_short*100:.1f}%/{self.stop_loss_short*100:.1f}%")
        print("="*80)

        # 初始化变量
        capital = self.initial_capital
        last_trade_date = None

        # 设置最小交易间隔（避免过度交易）
        min_trade_interval = timedelta(days=5)

        for i in range(60, len(self.data)):  # 从第60天开始，确保有足够的历史数据
            row = self.data.iloc[i]
            date = pd.to_datetime(row['date'])

            # 每月增加资金
            capital = self.add_monthly_capital(date, capital)

            # 记录权益
            self.equity_curve.append({
                'date': date,
                'equity': capital,
                'position': self.position
            })

            # 检查是否可以交易（避免过度交易）
            if last_trade_date and (date - last_trade_date) < min_trade_interval:
                continue

            # 如果有持仓，检查止盈止损
            if self.position != 0:
                if self.position == 1:  # 多头
                    profit_ratio = (row['high'] - self.current_price) / self.current_price
                    loss_ratio = (self.current_price - row['low']) / self.current_price

                    if profit_ratio >= self.take_profit_long:  # 止盈
                        exit_price = self.current_price * (1 + self.take_profit_long)
                        profit = (exit_price - self.current_price) / self.current_price * capital
                        capital += profit
                        self.position = 0
                        self.trades.append({
                            'date': date,
                            'type': 'long_exit_tp',
                            'price': exit_price,
                            'profit': profit,
                            'capital': capital
                        })
                        print(f"📈 {date.strftime('%Y-%m-%d')} 多头止盈: @ {exit_price:.3f}, 盈利: {profit:+.0f}")

                    elif loss_ratio >= self.stop_loss_long:  # 止损
                        exit_price = self.current_price * (1 - self.stop_loss_long)
                        loss = (exit_price - self.current_price) / self.current_price * capital
                        capital += loss
                        self.position = 0
                        self.trades.append({
                            'date': date,
                            'type': 'long_exit_sl',
                            'price': exit_price,
                            'profit': loss,
                            'capital': capital
                        })
                        print(f"📉 {date.strftime('%Y-%m-%d')} 多头止损: @ {exit_price:.3f}, 亏损: {loss:+.0f}")

                elif self.position == -1:  # 空头
                    profit_ratio = (self.current_price - row['low']) / self.current_price
                    loss_ratio = (row['high'] - self.current_price) / self.current_price

                    if profit_ratio >= self.take_profit_short:  # 止盈
                        exit_price = self.current_price * (1 - self.take_profit_short)
                        profit = (self.current_price - exit_price) / self.current_price * capital
                        capital += profit
                        self.position = 0
                        self.trades.append({
                            'date': date,
                            'type': 'short_exit_tp',
                            'price': exit_price,
                            'profit': profit,
                            'capital': capital
                        })
                        print(f"📈 {date.strftime('%Y-%m-%d')} 空头止盈: @ {exit_price:.3f}, 盈利: {profit:+.0f}")

                    elif loss_ratio >= self.stop_loss_short:  # 止损
                        exit_price = self.current_price * (1 + self.stop_loss_short)
                        loss = (exit_price - self.current_price) / self.current_price * capital * -1
                        capital += loss
                        self.position = 0
                        self.trades.append({
                            'date': date,
                            'type': 'short_exit_sl',
                            'price': exit_price,
                            'profit': loss,
                            'capital': capital
                        })
                        print(f"📉 {date.strftime('%Y-%m-%d')} 空头止损: @ {exit_price:.3f}, 亏损: {loss:+.0f}")

            # 如果空仓，判断是否开仓
            if self.position == 0:
                position_signal = self.check_entry_conditions(row)

                # 调试信息：显示前几次检查
                if i < 65:  # 只显示前几天的检查
                    print(f"🔍 {date.strftime('%Y-%m-%d')}: 价格位置={row['price_position']:.3f}, RSI={row['rsi']:.1f}, 信号={position_signal}")

                if position_signal != 0:
                    # 计算仓位大小
                    position_size = self.get_position_size(row['close'], capital, row['volatility'])

                    # 调试信息
                    if i < 65:
                        print(f"   💰 仓位大小: {position_size:.2f}, 资金: {capital:.2f}, 波动率: {row['volatility']:.4f}")

                    if position_size > 100:  # 最小交易金额限制
                        self.position = position_signal
                        self.current_price = row['close']
                        last_trade_date = date

                        self.trades.append({
                            'date': date,
                            'type': 'long_entry' if position_signal == 1 else 'short_entry',
                            'price': self.current_price,
                            'size': position_size,
                            'capital': capital
                        })

                        direction = '多头' if position_signal == 1 else '空头'
                        print(f"📊 {date.strftime('%Y-%m-%d')} 开{direction}: @ {self.current_price:.3f}, 仓位: {position_size:.0f}")

        self.final_capital = capital
        print(f"✅ 回测完成！最终资金：{self.final_capital:,.2f}")

        return True

    def analyze_results(self):
        """分析回测结果"""
        print(f"\n✅ {self.name} 回归线策略回测完成!")
        print("="*80)

        # 转换交易记录为DataFrame
        trades_df = pd.DataFrame(self.trades)
        if len(trades_df) == 0:
            print("没有产生任何交易")
            return

        # 计算基本统计数据
        total_trades = len(trades_df[trades_df['type'].str.contains('entry')])
        winning_trades = len(trades_df[trades_df['profit'] > 0]) if 'profit' in trades_df.columns else 0
        profit_trades = trades_df[trades_df['profit'] > 0] if 'profit' in trades_df.columns else pd.DataFrame()
        loss_trades = trades_df[trades_df['profit'] < 0] if 'profit' in trades_df.columns else pd.DataFrame()

        print(f"\n📊 交易统计:")
        print(f"   • 总交易次数: {total_trades}")
        print(f"   • 盈利交易: {winning_trades}")
        print(f"   • 亏损交易: {total_trades - winning_trades}")
        print(f"   • 胜率: {winning_trades/total_trades*100:.1f}%" if total_trades > 0 else "   • 胜率: N/A")

        if len(profit_trades) > 0:
            print(f"   • 平均盈利: {profit_trades['profit'].mean():.2f}")
            print(f"   • 最大盈利: {profit_trades['profit'].max():.2f}")

        if len(loss_trades) > 0:
            print(f"   • 平均亏损: {loss_trades['profit'].mean():.2f}")
            print(f"   • 最大亏损: {loss_trades['profit'].min():.2f}")

        # 计算收益率
        initial_equity = self.initial_capital
        final_equity = self.final_capital
        total_days = (self.data['date'].max() - self.data['date'].min()).days
        total_years = total_days / 365

        total_return = (final_equity - initial_equity) / initial_equity
        annual_return = (1 + total_return) ** (1/total_years) - 1

        print(f"\n💰 收益统计:")
        print(f"   • 初始资金: {initial_equity:,.0f} 港币")
        print(f"   • 最终资金: {final_equity:,.0f} 港币")
        print(f"   • 总收益: {final_equity - initial_equity:+,.0f} 港币")
        print(f"   • 总收益率: {total_return*100:+.2f}%")
        print(f"   • 年化收益率: {annual_return*100:.2f}%")

        return True

def main():
    """主函数"""
    print("🎯 和记电讯香港 (0215.HK) Cosmoon回归线策略回测")
    print("="*60)
    print("📅 启动时间:", datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
    print("👨‍💼 策略作者: Cosmoon NG")
    print("📊 核心: 回归线 + RSI + 凯利公式 + 风险管理")

    # 创建回测实例
    backtest = CosmoonRegressionStrategy0215()

    # 加载数据
    if not backtest.load_data():
        return

    # 运行回测
    if not backtest.run_backtest():
        return

    # 分析结果
    backtest.analyze_results()

    print("\n🎉 和记电讯香港回测完成!")
    print("💡 万物皆数，博弈制胜！")

if __name__ == "__main__":
    main()
