#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
HSI50优化策略回测系统 - 30K资金20年历史 (优化版)
===============================================

基于第一版结果的优化改进：
- 降低交易频率，提高信号质量
- 调整止盈止损比例为严格的1:2
- 优化凯利公式参数
- 增加持仓时间限制
- 改进区域判断逻辑

作者: Cosmoon NG
日期: 2025年7月
"""

import pandas as pd
import yfinance as yf
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

class OptimizedHSI50Backtest:
    def __init__(self):
        """初始化优化HSI50回测系统"""
        self.symbol = "^HSI"  # 恒生指数
        self.initial_capital = 30000  # 30K港币
        self.data = None
        
        # 优化后的策略参数
        self.strategy_params = {
            # 区域定义 - 更严格的条件
            'high_profit_y': 0.6,            # 提高高值盈利区阈值
            'high_profit_x': 0.6,            # 提高高值盈利区阈值
            'control_zone_min': 0.35,        # 调整控股商控制区
            'control_zone_max': 0.45,        # 调整控股商控制区
            'strong_loss_y': 0.2,            # 降低强亏损区阈值
            'strong_loss_x': 0.2,            # 降低强亏损区阈值
            
            # 凯利公式参数 - 基于实际表现调整
            'kelly_win_rate': 0.45,          # 更现实的胜率预期
            'kelly_win_ratio': 2,            # 保持1:2赔率
            'kelly_loss_ratio': 1,           # 亏损倍数
            'max_position_ratio': 0.15,      # 降低最大单次仓位到15%
            'max_total_positions': 2,        # 减少同时持仓数量
            
            # 严格的1:2止盈止损
            'take_profit': 0.02,             # 统一止盈2%
            'stop_loss': 0.01,               # 统一止损1%
            
            'transaction_cost': 30,          # 每笔交易成本30港币
            'point_value': 50,               # 每个恒指点子价值50港元
            'min_holding_days': 3,           # 最少持有3天
            'max_holding_days': 30,          # 最多持有30天
            
            # 信号过滤
            'e_threshold': 0.1,              # E值阈值过滤
            'volume_filter': True,           # 成交量过滤
            'trend_filter': True,            # 趋势过滤
        }
        
        self.trades = []
        self.daily_portfolio = []
        self.current_positions = []
    
    def fetch_hsi_data(self):
        """获取恒生指数20年历史数据"""
        print("📈 获取恒生指数20年历史数据...")
        
        try:
            # 获取20年数据
            end_date = datetime.now()
            start_date = end_date - timedelta(days=20*365)
            
            ticker = yf.Ticker(self.symbol)
            self.data = ticker.history(start=start_date, end=end_date)
            
            if self.data.empty:
                print("❌ 数据获取失败：返回空数据")
                return False
            
            self.data.reset_index(inplace=True)
            self.data.columns = [col.lower() for col in self.data.columns]
            
            print(f"✅ 成功获取恒生指数数据:")
            print(f"   • 数据期间: {self.data['date'].min().strftime('%Y-%m-%d')} 至 {self.data['date'].max().strftime('%Y-%m-%d')}")
            print(f"   • 总记录数: {len(self.data):,} 天")
            
            return True
            
        except Exception as e:
            print(f"❌ 获取数据失败: {e}")
            return False
    
    def calculate_indicators(self):
        """计算技术指标和X、Y值"""
        print("📊 计算技术指标和优化策略参数...")
        
        # 移动平均线
        self.data['ma_20'] = self.data['close'].rolling(window=20).mean()
        self.data['ma_60'] = self.data['close'].rolling(window=60).mean()
        self.data['ma_120'] = self.data['close'].rolling(window=120).mean()
        
        # RSI
        delta = self.data['close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
        rs = gain / loss
        self.data['rsi'] = 100 - (100 / (1 + rs))
        
        # 成交量指标
        self.data['volume_ma'] = self.data['volume'].rolling(window=20).mean()
        self.data['volume_ratio'] = self.data['volume'] / self.data['volume_ma']
        
        # 波动率
        self.data['volatility'] = self.data['close'].pct_change().rolling(window=20).std()
        
        # Y值计算 (控制系数) - 优化版
        price_vs_ma20 = self.data['close'] / self.data['ma_20']
        price_vs_ma60 = self.data['close'] / self.data['ma_60']
        
        # 基础Y值
        base_y = np.where(price_vs_ma20 >= 1, 
                         0.5 + 0.3 * np.tanh((price_vs_ma20 - 1) * 2),
                         0.5 - 0.3 * np.tanh((1 - price_vs_ma20) * 2))
        
        # 趋势调整 - 更保守
        ma_trend = (self.data['ma_20'] / self.data['ma_60']).fillna(1)
        trend_adjustment = 0.05 * np.tanh((ma_trend - 1) * 1.5)
        
        # 成交量调整 - 更保守
        volume_adjustment = 0.03 * np.tanh((self.data['volume_ratio'] - 1))
        
        # 波动率调整
        volatility_norm = (self.data['volatility'] - self.data['volatility'].rolling(60).mean()) / self.data['volatility'].rolling(60).std()
        volatility_adjustment = -0.02 * np.tanh(volatility_norm)  # 高波动率降低Y值
        
        self.data['y_probability'] = base_y + trend_adjustment + volume_adjustment + volatility_adjustment
        self.data['y_probability'] = np.clip(self.data['y_probability'], 0.1, 0.9)
        self.data['y_probability'].fillna(0.5, inplace=True)
        
        # X值计算 (资金流比例) - 优化版
        price_change = (self.data['close'] - self.data['open']) / self.data['open']
        money_flow = self.data['volume'] * price_change
        
        def calc_inflow_ratio(flows):
            if len(flows) == 0 or flows.isna().all():
                return 0.5
            flows = flows.dropna()
            if len(flows) == 0:
                return 0.5
            
            inflows = flows[flows > 0].sum()
            outflows = abs(flows[flows < 0].sum())
            total_flow = inflows + outflows
            
            return inflows / total_flow if total_flow > 0 else 0.5
        
        base_x = money_flow.rolling(window=30).apply(calc_inflow_ratio, raw=False)  # 延长窗口
        rsi_adjustment = 0.2 * (self.data['rsi'] / 100 - 0.5)  # 减少RSI影响
        
        self.data['inflow_ratio'] = base_x + rsi_adjustment
        self.data['inflow_ratio'] = np.clip(self.data['inflow_ratio'], 0.1, 0.9)
        self.data['inflow_ratio'].fillna(0.5, inplace=True)
        
        # E值计算
        self.data['e_value'] = (8 * self.data['inflow_ratio'] * self.data['y_probability'] - 
                               3 * self.data['inflow_ratio'] - 3 * self.data['y_probability'] + 1)
        
        # 趋势过滤
        self.data['trend_up'] = (self.data['ma_20'] > self.data['ma_60']) & (self.data['ma_60'] > self.data['ma_120'])
        self.data['trend_down'] = (self.data['ma_20'] < self.data['ma_60']) & (self.data['ma_60'] < self.data['ma_120'])
        
        print("✅ 优化指标计算完成")
    
    def calculate_kelly_position(self, win_rate_modifier=1.0):
        """计算凯利公式仓位"""
        win_rate = self.strategy_params['kelly_win_rate'] * win_rate_modifier
        win_ratio = self.strategy_params['kelly_win_ratio']
        loss_ratio = self.strategy_params['kelly_loss_ratio']
        
        b = win_ratio / loss_ratio
        p = win_rate
        q = 1 - p
        
        kelly_fraction = (b * p - q) / b
        return max(0, min(kelly_fraction, self.strategy_params['max_position_ratio']))
    
    def classify_strategy_zone(self, y_val, x_val, e_val, trend_up, trend_down, volume_ratio):
        """分类策略区域 - 优化版"""
        # E值过滤
        if abs(e_val) < self.strategy_params['e_threshold']:
            return 'NEUTRAL'
        
        # 成交量过滤
        if self.strategy_params['volume_filter'] and volume_ratio < 0.8:
            return 'LOW_VOLUME'
        
        if (y_val > self.strategy_params['high_profit_y'] and 
            x_val > self.strategy_params['high_profit_x'] and
            e_val > 0):
            # 趋势过滤：高值盈利区需要上升趋势
            if self.strategy_params['trend_filter'] and not trend_up:
                return 'TREND_FILTERED'
            return 'HIGH_PROFIT'
        elif (self.strategy_params['control_zone_min'] < y_val < self.strategy_params['control_zone_max']):
            return 'CONTROL'
        elif (y_val < self.strategy_params['strong_loss_y'] or 
              x_val < self.strategy_params['strong_loss_x']) and e_val < -0.2:
            # 趋势过滤：强亏损区需要下降趋势
            if self.strategy_params['trend_filter'] and not trend_down:
                return 'TREND_FILTERED'
            return 'STRONG_LOSS'
        else:
            return 'OTHER'

    def check_stop_conditions(self, position, current_price, current_date):
        """检查止盈止损和时间限制"""
        entry_price = position['entry_price']
        direction = position['direction']
        entry_date = position['entry_date']

        # 计算持仓天数
        holding_days = (current_date - entry_date).days

        if direction == 'LONG':
            profit_pct = (current_price - entry_price) / entry_price
        else:  # SHORT
            profit_pct = (entry_price - current_price) / entry_price

        # 时间限制
        if holding_days >= self.strategy_params['max_holding_days']:
            return True, '时间止损', profit_pct

        # 止盈止损
        if profit_pct >= self.strategy_params['take_profit']:
            return True, '止盈', profit_pct
        elif profit_pct <= -self.strategy_params['stop_loss']:
            return True, '止损', profit_pct
        elif holding_days >= self.strategy_params['min_holding_days'] and profit_pct < -0.005:
            # 最少持有期后的小幅亏损也可以止损
            return True, '小幅止损', profit_pct

        return False, '', profit_pct

    def execute_trade(self, date, price, direction, zone, kelly_modifier=1.0):
        """执行交易 - 优化版"""
        # 计算凯利公式仓位
        kelly_position = self.calculate_kelly_position(kelly_modifier)

        # 计算可用资金
        current_cash = self.initial_capital
        for trade in self.trades:
            current_cash += trade['net_profit']

        # 资金不足检查
        if current_cash < 1000:  # 最少保留1000港币
            return

        # 计算投资金额
        investment_amount = current_cash * kelly_position

        if investment_amount < self.strategy_params['transaction_cost'] * 2:
            return  # 资金不足

        # 计算股数
        shares = (investment_amount - self.strategy_params['transaction_cost']) / price

        # 创建持仓记录
        position = {
            'entry_date': date,
            'entry_price': price,
            'shares': shares,
            'direction': direction,
            'zone': zone,
            'investment': investment_amount
        }

        self.current_positions.append(position)

    def backtest_strategy(self):
        """执行优化策略回测"""
        print("\n🚀 开始优化Cosmoon凯利策略回测...")
        print("="*60)
        print(f"💰 初始资金: {self.initial_capital:,} 港币")
        print(f"📊 优化策略: 更严格的信号过滤")
        print(f"📈 高值盈利区: Y > {self.strategy_params['high_profit_y']}, X > {self.strategy_params['high_profit_x']} + 趋势过滤")
        print(f"📉 强亏损区: Y < {self.strategy_params['strong_loss_y']} 或 X < {self.strategy_params['strong_loss_x']} + 趋势过滤")
        print(f"⏸️ 控股商控制区: {self.strategy_params['control_zone_min']} < Y < {self.strategy_params['control_zone_max']}")
        print(f"🎲 凯利公式: 1:{self.strategy_params['kelly_win_ratio']} 赔率, 最大仓位{self.strategy_params['max_position_ratio']*100}%")
        print(f"⏰ 持仓时间: {self.strategy_params['min_holding_days']}-{self.strategy_params['max_holding_days']}天")
        print("="*60)

        # 统计变量
        total_trades = 0
        winning_trades = 0
        losing_trades = 0

        # 跳过前120天用于指标计算
        for i in range(120, len(self.data)):
            row = self.data.iloc[i]
            date = row['date']
            price = row['close']
            y_val = row['y_probability']
            x_val = row['inflow_ratio']
            e_val = row['e_value']
            trend_up = row['trend_up']
            trend_down = row['trend_down']
            volume_ratio = row['volume_ratio']

            # 分类策略区域
            zone = self.classify_strategy_zone(y_val, x_val, e_val, trend_up, trend_down, volume_ratio)

            # 检查现有持仓的止盈止损
            positions_to_close = []
            for j, position in enumerate(self.current_positions):
                should_exit, exit_reason, profit_pct = self.check_stop_conditions(position, price, date)

                if should_exit:
                    # 计算实际盈亏
                    if position['direction'] == 'LONG':
                        price_diff = price - position['entry_price']
                    else:  # SHORT
                        price_diff = position['entry_price'] - price

                    gross_profit = price_diff * position['shares']
                    net_profit = gross_profit - self.strategy_params['transaction_cost']

                    # 记录交易
                    trade_record = {
                        'entry_date': position['entry_date'].strftime('%Y-%m-%d'),
                        'exit_date': date.strftime('%Y-%m-%d'),
                        'direction': position['direction'],
                        'zone': position['zone'],
                        'entry_price': round(position['entry_price'], 2),
                        'exit_price': round(price, 2),
                        'shares': round(position['shares'], 2),
                        'investment': round(position['investment'], 2),
                        'gross_profit': round(gross_profit, 2),
                        'transaction_cost': self.strategy_params['transaction_cost'],
                        'net_profit': round(net_profit, 2),
                        'profit_pct': round(profit_pct * 100, 2),
                        'holding_days': (date - position['entry_date']).days,
                        'y_value': round(y_val, 3),
                        'x_value': round(x_val, 3),
                        'e_value': round(e_val, 3),
                        'exit_reason': exit_reason
                    }

                    self.trades.append(trade_record)

                    if net_profit > 0:
                        winning_trades += 1
                    else:
                        losing_trades += 1

                    total_trades += 1
                    positions_to_close.append(j)

            # 移除已平仓的持仓
            for j in sorted(positions_to_close, reverse=True):
                del self.current_positions[j]

            # 根据策略区域决定开仓 - 更严格的条件
            current_position_count = len(self.current_positions)

            if (zone == 'HIGH_PROFIT' and
                current_position_count < self.strategy_params['max_total_positions'] and
                e_val > 0.2):  # 更高的E值要求
                # 高值盈利区：买涨
                self.execute_trade(date, price, 'LONG', zone, kelly_modifier=1.0)

            elif (zone == 'STRONG_LOSS' and
                  current_position_count < self.strategy_params['max_total_positions'] and
                  e_val < -0.3):  # 更低的E值要求
                # 强亏损区：买跌
                self.execute_trade(date, price, 'SHORT', zone, kelly_modifier=0.8)

            # 其他区域暂时不交易，减少噪音

            # 记录每日组合价值
            current_cash = self.initial_capital
            for trade in self.trades:
                current_cash += trade['net_profit']

            # 计算当前持仓价值
            position_value = 0
            for position in self.current_positions:
                if position['direction'] == 'LONG':
                    unrealized_profit = (price - position['entry_price']) * position['shares']
                else:  # SHORT
                    unrealized_profit = (position['entry_price'] - price) * position['shares']
                position_value += position['investment'] + unrealized_profit

            total_value = current_cash + position_value

            self.daily_portfolio.append({
                'date': date.strftime('%Y-%m-%d'),
                'price': price,
                'cash': current_cash,
                'position_value': position_value,
                'total_value': total_value,
                'y_value': y_val,
                'x_value': x_val,
                'e_value': e_val,
                'zone': zone,
                'positions_count': len(self.current_positions)
            })

        print(f"\n✅ 优化回测完成!")
        print(f"📊 总交易次数: {total_trades}")
        print(f"📈 盈利次数: {winning_trades}")
        print(f"📉 亏损次数: {losing_trades}")
        if total_trades > 0:
            print(f"🎯 胜率: {winning_trades/total_trades*100:.1f}%")

        return pd.DataFrame(self.trades), pd.DataFrame(self.daily_portfolio)

    def analyze_results(self, trades_df, daily_df):
        """分析优化回测结果"""
        print("\n📊 优化Cosmoon凯利策略回测结果分析")
        print("=" * 60)

        # 基本统计
        total_trades = len(trades_df)
        final_cash = daily_df['cash'].iloc[-1] if len(daily_df) > 0 else self.initial_capital
        final_total_value = daily_df['total_value'].iloc[-1] if len(daily_df) > 0 else self.initial_capital
        total_return = final_total_value - self.initial_capital
        total_return_rate = (total_return / self.initial_capital) * 100

        # 年化收益率
        years = 20
        annual_return_rate = ((final_total_value / self.initial_capital) ** (1/years) - 1) * 100

        if total_trades > 0:
            winning_trades = len(trades_df[trades_df['net_profit'] > 0])
            win_rate = winning_trades / total_trades * 100
            max_profit = trades_df['net_profit'].max()
            max_loss = trades_df['net_profit'].min()
            avg_profit = trades_df['net_profit'].mean()
            total_trading_profit = trades_df['net_profit'].sum()
            avg_holding_days = trades_df['holding_days'].mean()

            # 按区域分析
            zone_stats = trades_df.groupby('zone').agg({
                'net_profit': ['count', 'sum', 'mean'],
                'profit_pct': 'mean',
                'holding_days': 'mean'
            }).round(2)

            # 按方向分析
            direction_stats = trades_df.groupby('direction').agg({
                'net_profit': ['count', 'sum', 'mean'],
                'profit_pct': 'mean',
                'holding_days': 'mean'
            }).round(2)
        else:
            winning_trades = 0
            win_rate = 0
            max_profit = 0
            max_loss = 0
            avg_profit = 0
            total_trading_profit = 0
            avg_holding_days = 0
            zone_stats = pd.DataFrame()
            direction_stats = pd.DataFrame()

        # 输出结果
        print(f"💰 资金统计:")
        print(f"• 初始资金: {self.initial_capital:,} 港元")
        print(f"• 最终现金: {final_cash:,.0f} 港元")
        print(f"• 最终总价值: {final_total_value:,.0f} 港元")
        print(f"• 总收益: {total_return:,.0f} 港元")
        print(f"• 总收益率: {total_return_rate:.2f}%")
        print(f"• 年化收益率: {annual_return_rate:.2f}%")

        if total_trades > 0:
            print(f"\n📈 交易统计:")
            print(f"• 总交易次数: {total_trades}")
            print(f"• 盈利次数: {winning_trades}")
            print(f"• 胜率: {win_rate:.1f}%")
            print(f"• 最大单笔盈利: {max_profit:,.0f} 港元")
            print(f"• 最大单笔亏损: {max_loss:,.0f} 港元")
            print(f"• 平均每笔盈亏: {avg_profit:,.0f} 港元")
            print(f"• 交易总盈亏: {total_trading_profit:,.0f} 港元")
            print(f"• 平均持仓天数: {avg_holding_days:.1f} 天")

            print(f"\n📊 区域分析:")
            print(zone_stats)

            print(f"\n📊 方向分析:")
            print(direction_stats)

            # 凯利公式验证
            actual_win_rate = win_rate / 100
            actual_win_ratio = abs(max_profit / max_loss) if max_loss != 0 else 2

            print(f"\n🎲 凯利公式验证:")
            print(f"• 实际胜率: {actual_win_rate:.3f}")
            print(f"• 实际盈亏比: {actual_win_ratio:.2f}")
            print(f"• 预设胜率: {self.strategy_params['kelly_win_rate']:.3f}")
            print(f"• 预设盈亏比: {self.strategy_params['kelly_win_ratio']:.2f}")

        # 保存到Excel
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"HSI50优化策略回测结果_{timestamp}.xlsx"

        with pd.ExcelWriter(filename, engine='openpyxl') as writer:
            if len(trades_df) > 0:
                trades_df.to_excel(writer, sheet_name='交易记录', index=False)
            daily_df.to_excel(writer, sheet_name='每日数据', index=False)

            # 汇总统计
            summary_data = {
                '项目': ['初始资金(港元)', '最终现金(港元)', '最终总价值(港元)',
                        '总收益(港元)', '总收益率(%)', '年化收益率(%)',
                        '总交易次数', '盈利次数', '胜率(%)',
                        '最大单笔盈利(港元)', '最大单笔亏损(港元)', '平均每笔盈亏(港元)',
                        '平均持仓天数'],
                '数值': [self.initial_capital, round(final_cash, 0), round(final_total_value, 0),
                        round(total_return, 0), round(total_return_rate, 2), round(annual_return_rate, 2),
                        total_trades, winning_trades, round(win_rate, 1),
                        round(max_profit, 0) if total_trades > 0 else 0,
                        round(max_loss, 0) if total_trades > 0 else 0,
                        round(avg_profit, 0) if total_trades > 0 else 0,
                        round(avg_holding_days, 1) if total_trades > 0 else 0]
            }
            pd.DataFrame(summary_data).to_excel(writer, sheet_name='汇总统计', index=False)

            if len(zone_stats) > 0:
                zone_stats.to_excel(writer, sheet_name='区域分析')
            if len(direction_stats) > 0:
                direction_stats.to_excel(writer, sheet_name='方向分析')

        print(f"\n✅ 详细结果已保存至: {filename}")
        return filename

def main():
    """主函数"""
    print("🏢 HSI50优化策略回测系统")
    print("=" * 60)
    print("💰 总资金: 30,000港元")
    print("📊 分析周期: 20年历史数据")
    print("🎯 策略: 优化Cosmoon博弈论 + 凯利公式")
    print("📈 高值盈利区: Y>0.6, X>0.6 + 趋势过滤 (止盈+2%, 止损-1%)")
    print("📉 强亏损区: Y<0.2或X<0.2 + 趋势过滤 (止盈+2%, 止损-1%)")
    print("⏸️ 控股商控制区: 0.35<Y<0.45 观望")
    print("🎲 凯利公式: 1:2赔率, 最大仓位15%")
    print("⏰ 持仓时间: 3-30天")
    print("🔍 信号过滤: E值阈值 + 成交量 + 趋势")

    # 创建回测器
    backtester = OptimizedHSI50Backtest()

    # 获取数据
    if not backtester.fetch_hsi_data():
        return

    # 计算指标
    backtester.calculate_indicators()

    # 执行回测
    trades_df, daily_df = backtester.backtest_strategy()

    # 分析结果
    backtester.analyze_results(trades_df, daily_df)

if __name__ == "__main__":
    main()
