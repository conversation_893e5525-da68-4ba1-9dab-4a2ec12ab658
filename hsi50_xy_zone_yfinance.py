#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
HSI50 XY分区策略 - YFinance版本
=============================
使用yfinance从网上获取HSI50数据

策略规则：
1. 高值盈利区 (Y>0.4, X>0.4): 买涨，止盈+1.6%，止损-0.8%
2. 控股商控制区 (0.333<Y<0.4): 观望
3. 强亏损区 (Y<0.25或X<0.25): 买跌，止盈+0.8%，止损-1.6%
4. 其他区域: 买跌，止盈+1%，止损-2%

其中：
- Y = Full_Y (累积强势比例)
- X = 1 - Full_Y (弱势比例)
"""

import yfinance as yf
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import matplotlib.pyplot as plt
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

class HSI50XYZoneYFinance:
    def __init__(self):
        """初始化XY分区策略 - YFinance版本"""

        # 基础参数
        self.initial_capital = 30000      # 初始资金
        self.monthly_addition = 1000      # 每月追加资金
        self.max_position_ratio = 0.35    # 最大仓位35% (提升收益，风险可控)

        # HSI50相关代码
        self.symbol = "^HSI"              # 恒生指数代码
        self.start_date = "2020-01-01"    # 开始日期
        self.end_date = None              # 结束日期(None表示到今天)

        # XY分区策略参数
        self.zones = {
            'high_profit': {
                'condition': lambda y, x: (y > 0.4) and (x > 0.4),
                'action': 'LONG',
                'take_profit': 0.016,  # +1.6%
                'stop_loss': 0.008,    # -0.8%
                'description': '高值盈利区 (Y>0.4, X>0.4): 买涨'
            },
            'control': {
                'condition': lambda y, x: (0.333 < y < 0.4),
                'action': 'HOLD',
                'take_profit': 0,
                'stop_loss': 0,
                'description': '控股商控制区 (0.333<Y<0.4): 观望'
            },
            'strong_loss': {
                'condition': lambda y, x: (y < 0.25) or (x < 0.25),
                'action': 'SHORT',
                'take_profit': 0.008,  # +0.8%
                'stop_loss': 0.016,    # -1.6%
                'description': '强亏损区 (Y<0.25或X<0.25): 买跌'
            },
            'other': {
                'condition': lambda y, x: True,  # 默认区域
                'action': 'SHORT',
                'take_profit': 0.01,   # +1%
                'stop_loss': 0.02,     # -2%
                'description': '其他区域: 买跌'
            }
        }

        # 状态变量
        self.position = 0                 # 当前持仓 (1=多头, -1=空头, 0=空仓)
        self.current_price = 0            # 当前持仓价格
        self.current_zone = None          # 当前所在区域
        self.current_take_profit = 0      # 当前止盈
        self.current_stop_loss = 0        # 当前止损
        self.trades = []
        self.equity_curve = []

    def load_data_from_yfinance(self):
        """从YFinance加载HSI数据"""
        print("\n1. 从YFinance加载HSI数据...")
        try:
            # 下载HSI数据
            print(f"   下载 {self.symbol} 数据...")
            ticker = yf.Ticker(self.symbol)

            # 获取历史数据
            hist = ticker.history(
                start=self.start_date,
                end=self.end_date,
                interval="1d"
            )

            if hist.empty:
                print(f"❌ 无法获取 {self.symbol} 数据")
                return False

            # 数据预处理
            self.df = hist.reset_index()
            self.df.columns = [col.lower() for col in self.df.columns]

            # 重命名列以匹配原始格式
            column_mapping = {
                'date': 'date',
                'open': 'open',
                'high': 'high',
                'low': 'low',
                'close': 'close',
                'volume': 'volume'
            }

            # 确保所有需要的列都存在
            for old_col, new_col in column_mapping.items():
                if old_col in self.df.columns:
                    self.df[new_col] = self.df[old_col]

            # 确保date列是datetime类型
            if 'date' not in self.df.columns:
                self.df['date'] = self.df.index
            self.df['date'] = pd.to_datetime(self.df['date'])

            # 按日期排序
            self.df = self.df.sort_values('date').reset_index(drop=True)

            print(f"✅ 成功加载 {len(self.df)} 条数据")
            print(f"📅 数据范围：{self.df['date'].min().date()} 至 {self.df['date'].max().date()}")
            print(f"📊 价格范围：{self.df['close'].min():.2f} ~ {self.df['close'].max():.2f}")

            # 计算技术指标
            self.calculate_technical_indicators()

            return True

        except Exception as e:
            print(f"❌ 数据加载失败: {e}")
            print("💡 请确保已安装yfinance: pip install yfinance")
            return False

    def calculate_technical_indicators(self):
        """计算技术指标"""
        print("   计算技术指标...")

        # 移动平均线
        self.df['ma_20'] = self.df['close'].rolling(window=20).mean()
        self.df['ma_60'] = self.df['close'].rolling(window=60).mean()

        # 计算midprice (简化版本: (high + low) / 2)
        self.df['midprice'] = (self.df['high'] + self.df['low']) / 2

        # 计算controller (close > midprice则为1，否则为0)
        self.df['controller'] = (self.df['close'] > self.df['midprice']).astype(int)

        # 计算Full_Y (累积强势比例)
        self.df['cumulative_controller'] = self.df['controller'].cumsum()
        self.df['row_number'] = range(1, len(self.df) + 1)
        self.df['full_y'] = self.df['cumulative_controller'] / self.df['row_number']

        # 计算XY指标
        self.df['Y'] = self.df['full_y']      # Y = Full_Y
        self.df['X'] = 1 - self.df['full_y']  # X = 1 - Full_Y

        # 计算y_probability (简化版本，基于价格相对MA20的位置)
        self.df['price_vs_ma20'] = self.df['close'] / self.df['ma_20']
        self.df['y_probability'] = np.where(
            self.df['price_vs_ma20'] >= 1,
            0.5 + 0.4 * np.tanh((self.df['price_vs_ma20'] - 1) * 3),
            0.5 - 0.4 * np.tanh((1 - self.df['price_vs_ma20']) * 3)
        )
        self.df['y_probability'] = np.clip(self.df['y_probability'], 0.1, 0.9)

        # RSI
        delta = self.df['close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
        rs = gain / loss
        self.df['rsi'] = 100 - (100 / (1 + rs))

        # 价格偏离
        self.df['price_deviation'] = (self.df['close'] - self.df['midprice']) / self.df['midprice']

        # 波动率
        self.df['volatility'] = self.df['close'].pct_change().rolling(window=20).std()

        # 计算分区
        self.df['zone'] = self.df.apply(self.determine_zone, axis=1)
        self.df['action'] = self.df['zone'].map(lambda z: self.zones[z]['action'])
        self.df['take_profit'] = self.df['zone'].map(lambda z: self.zones[z]['take_profit'])
        self.df['stop_loss'] = self.df['zone'].map(lambda z: self.zones[z]['stop_loss'])

        # 分析分区分布
        self.analyze_zone_distribution()

        print("✅ 技术指标计算完成")

    def determine_zone(self, row):
        """确定当前行所属的区域"""
        y = row['Y']
        x = row['X']

        # 按优先级检查区域
        if self.zones['high_profit']['condition'](y, x):
            return 'high_profit'
        elif self.zones['control']['condition'](y, x):
            return 'control'
        elif self.zones['strong_loss']['condition'](y, x):
            return 'strong_loss'
        else:
            return 'other'

    def analyze_zone_distribution(self):
        """分析分区分布"""
        print("\n📊 XY分区分布分析:")
        zone_counts = self.df['zone'].value_counts()
        total_count = len(self.df)

        for zone, count in zone_counts.items():
            percentage = count / total_count * 100
            description = self.zones[zone]['description']
            action = self.zones[zone]['action']
            print(f"   • {description}")
            print(f"     {action}: {count} 天 ({percentage:.1f}%)")

        # 显示XY分布统计
        print(f"\n📈 XY指标统计:")
        print(f"   • Y均值: {self.df['Y'].mean():.6f}")
        print(f"   • X均值: {self.df['X'].mean():.6f}")
        print(f"   • Y范围: {self.df['Y'].min():.6f} ~ {self.df['Y'].max():.6f}")
        print(f"   • X范围: {self.df['X'].min():.6f} ~ {self.df['X'].max():.6f}")
        print(f"   • Y>0.4的天数: {(self.df['Y'] > 0.4).sum()} 天")
        print(f"   • X>0.4的天数: {(self.df['X'] > 0.4).sum()} 天")
        print(f"   • Y<0.25的天数: {(self.df['Y'] < 0.25).sum()} 天")
        print(f"   • X<0.25的天数: {(self.df['X'] < 0.25).sum()} 天")

    def add_monthly_capital(self, date, capital):
        """每月增加资金"""
        last_month = getattr(self, 'last_month', None)
        current_month = date.replace(day=1)

        if last_month is None or current_month > last_month:
            self.last_month = current_month
            return capital + self.monthly_addition

        return capital

    def check_entry_conditions(self, row):
        """检查入场条件"""
        zone = row['zone']
        action = row['action']

        # 基本数据检查
        if pd.isna(row['Y']) or pd.isna(row['X']) or pd.isna(row['rsi']):
            return 0, None, 0, 0

        # 观望区域不交易
        if action == 'HOLD':
            return 0, zone, 0, 0

        # 获取区域参数
        take_profit = self.zones[zone]['take_profit']
        stop_loss = self.zones[zone]['stop_loss']

        # 添加一些过滤条件
        rsi = row['rsi']
        price_deviation = row['price_deviation']

        # 多头条件 (高值盈利区)
        if action == 'LONG':
            if (rsi < 80 and  # RSI不过度超买
                price_deviation < 0.05):  # 价格不过度偏高
                return 1, zone, take_profit, stop_loss

        # 空头条件 (强亏损区和其他区域)
        elif action == 'SHORT':
            if (rsi > 20 and  # RSI不过度超卖
                price_deviation > -0.05):  # 价格不过度偏低
                return -1, zone, take_profit, stop_loss

        return 0, zone, 0, 0

    def run_backtest(self):
        """运行回测"""
        print("\n2. 开始XY分区策略回测...")

        capital = self.initial_capital
        last_trade_date = None
        min_trade_interval = timedelta(days=1)  # 允许每日交易

        for i in range(60, len(self.df)):  # 从第60天开始(确保MA60计算完成)
            row = self.df.iloc[i]
            date = pd.to_datetime(row['date'])

            # 每月增加资金
            capital = self.add_monthly_capital(date, capital)

            # 记录权益
            current_equity = capital
            if self.position != 0:
                # 计算当前持仓的浮动盈亏
                if self.position == 1:  # 多头
                    unrealized_pnl = (row['close'] - self.current_price) / self.current_price * capital * self.max_position_ratio
                else:  # 空头
                    unrealized_pnl = (self.current_price - row['close']) / self.current_price * capital * self.max_position_ratio
                current_equity += unrealized_pnl

            self.equity_curve.append({
                'date': date,
                'equity': current_equity,
                'position': self.position,
                'zone': row['zone'],
                'Y': row['Y'],
                'X': row['X']
            })

            # 检查交易间隔
            if last_trade_date and (date - last_trade_date) < min_trade_interval:
                continue

            # 检查止盈止损
            if self.position != 0:
                profit_ratio = 0

                if self.position == 1:  # 多头
                    profit_ratio = (row['close'] - self.current_price) / self.current_price
                else:  # 空头
                    profit_ratio = (self.current_price - row['close']) / self.current_price

                should_exit = False
                exit_reason = ""

                if profit_ratio >= self.current_take_profit:  # 止盈
                    should_exit = True
                    exit_reason = "止盈"
                elif profit_ratio <= -self.current_stop_loss:  # 止损
                    should_exit = True
                    exit_reason = "止损"

                if should_exit:
                    profit = profit_ratio * capital * self.max_position_ratio
                    capital += profit

                    self.trades.append({
                        'date': date,
                        'type': f'{["short", "long"][self.position == 1]}_exit',
                        'price': row['close'],
                        'profit': profit,
                        'profit_ratio': profit_ratio,
                        'capital': capital,
                        'zone': self.current_zone,
                        'exit_reason': exit_reason,
                        'Y': row['Y'],
                        'X': row['X']
                    })

                    self.position = 0
                    self.current_zone = None

            # 检查开仓条件
            if self.position == 0:
                position_signal, zone, take_profit, stop_loss = self.check_entry_conditions(row)

                if position_signal != 0:
                    position_size = capital * self.max_position_ratio

                    if position_size > 1000:  # 最小交易金额限制
                        self.position = position_signal
                        self.current_price = row['close']
                        self.current_zone = zone
                        self.current_take_profit = take_profit
                        self.current_stop_loss = stop_loss
                        last_trade_date = date

                        self.trades.append({
                            'date': date,
                            'type': 'long_entry' if position_signal == 1 else 'short_entry',
                            'price': self.current_price,
                            'size': position_size,
                            'capital': capital,
                            'zone': zone,
                            'take_profit': take_profit,
                            'stop_loss': stop_loss,
                            'Y': row['Y'],
                            'X': row['X']
                        })

        self.final_capital = capital
        print(f"✅ 回测完成！最终资金：{self.final_capital:,.2f}")

    def analyze_results(self):
        """分析回测结果"""
        print("\n=== XY分区策略回测分析 (YFinance数据) ===")

        trades_df = pd.DataFrame(self.trades)
        if len(trades_df) == 0:
            print("没有产生任何交易")
            return

        # 基本统计
        entry_trades = trades_df[trades_df['type'].str.contains('entry')]
        exit_trades = trades_df[trades_df['type'].str.contains('exit')]

        total_trades = len(entry_trades)
        winning_trades = len(exit_trades[exit_trades['profit'] > 0]) if 'profit' in exit_trades.columns else 0

        print(f"\n📊 交易统计：")
        print(f"总交易次数：{total_trades}")
        print(f"盈利交易：{winning_trades}")
        print(f"亏损交易：{total_trades - winning_trades}")
        if total_trades > 0:
            print(f"胜率：{winning_trades/total_trades*100:.2f}%")

        # 按区域分析交易
        if 'zone' in entry_trades.columns:
            print(f"\n🎯 分区交易分析：")
            zone_trades = entry_trades['zone'].value_counts()
            for zone, count in zone_trades.items():
                zone_desc = self.zones[zone]['description']
                zone_action = self.zones[zone]['action']
                print(f"   • {zone_desc}")
                print(f"     {zone_action}: {count} 笔交易")

                # 该区域的盈亏统计
                zone_exits = exit_trades[exit_trades['zone'] == zone]
                if len(zone_exits) > 0:
                    zone_wins = len(zone_exits[zone_exits['profit'] > 0])
                    zone_win_rate = zone_wins / len(zone_exits) * 100
                    zone_total_profit = zone_exits['profit'].sum()
                    print(f"     胜率: {zone_win_rate:.1f}%, 总盈亏: {zone_total_profit:,.2f}")

        # 盈亏分析
        if 'profit' in exit_trades.columns and len(exit_trades) > 0:
            total_profit = exit_trades['profit'].sum()
            profit_trades = exit_trades[exit_trades['profit'] > 0]
            loss_trades = exit_trades[exit_trades['profit'] < 0]

            print(f"\n💰 盈亏分析：")
            print(f"总交易盈亏：{total_profit:,.2f}")

            if len(profit_trades) > 0:
                print(f"平均盈利：{profit_trades['profit'].mean():.2f}")
                print(f"最大盈利：{profit_trades['profit'].max():.2f}")

            if len(loss_trades) > 0:
                print(f"平均亏损：{loss_trades['profit'].mean():.2f}")
                print(f"最大亏损：{loss_trades['profit'].min():.2f}")

            # 止盈止损分析
            tp_trades = exit_trades[exit_trades['exit_reason'] == '止盈']
            sl_trades = exit_trades[exit_trades['exit_reason'] == '止损']

            print(f"止盈次数：{len(tp_trades)} ({len(tp_trades)/len(exit_trades)*100:.1f}%)")
            print(f"止损次数：{len(sl_trades)} ({len(sl_trades)/len(exit_trades)*100:.1f}%)")

        # 收益率分析
        initial_equity = self.initial_capital
        final_equity = self.final_capital
        total_days = (self.df['date'].max() - self.df['date'].min()).days
        total_years = total_days / 365

        # 计算总投入
        months = total_days / 30
        total_invested = initial_equity + months * self.monthly_addition

        net_profit = final_equity - total_invested
        total_return = net_profit / total_invested if total_invested > 0 else 0
        annual_return = (final_equity / total_invested) ** (1/total_years) - 1 if total_years > 0 else 0

        print(f"\n📈 收益统计：")
        print(f"初始资金：{initial_equity:,.2f}")
        print(f"总投入：{total_invested:,.2f}")
        print(f"最终资金：{final_equity:,.2f}")
        print(f"净收益：{net_profit:,.2f}")
        print(f"总收益率：{total_return*100:.2f}%")
        print(f"年化收益率：{annual_return*100:.2f}%")

        # 与买入持有比较
        hsi_start = self.df['close'].iloc[60]
        hsi_end = self.df['close'].iloc[-1]
        buy_hold_return = (hsi_end - hsi_start) / hsi_start
        buy_hold_annual = (buy_hold_return + 1) ** (1/total_years) - 1

        print(f"\n📊 策略对比：")
        print(f"XY分区策略年化收益：{annual_return*100:.2f}%")
        print(f"买入持有年化收益：{buy_hold_annual*100:.2f}%")
        print(f"超额收益：{(annual_return - buy_hold_annual)*100:+.2f}%")

        # 保存结果
        try:
            if len(trades_df) > 0:
                # 处理时区问题
                if 'date' in trades_df.columns:
                    trades_df['date'] = pd.to_datetime(trades_df['date']).dt.tz_localize(None)
                trades_df.to_excel('xy_zone_yfinance_trades.xlsx', index=False)
                print("\n📄 交易记录已保存到 xy_zone_yfinance_trades.xlsx")

            # 保存权益曲线数据
            equity_df = pd.DataFrame(self.equity_curve)
            if 'date' in equity_df.columns:
                equity_df['date'] = pd.to_datetime(equity_df['date']).dt.tz_localize(None)
            equity_df.to_excel('xy_zone_yfinance_equity.xlsx', index=False)
            print("📄 权益曲线已保存到 xy_zone_yfinance_equity.xlsx")

        except Exception as e:
            print(f"保存文件时出错: {e}")

def main():
    """主函数"""
    print("🎯 HSI50 XY分区策略 - YFinance版本")
    print("从网上获取实时数据进行回测")
    print("="*60)
    print("策略规则：")
    print("1. 高值盈利区 (Y>0.4, X>0.4): 买涨，止盈+1.6%，止损-0.8%")
    print("2. 控股商控制区 (0.333<Y<0.4): 观望")
    print("3. 强亏损区 (Y<0.25或X<0.25): 买跌，止盈+0.8%，止损-1.6%")
    print("4. 其他区域: 买跌，止盈+1%，止损-2%")
    print("="*60)

    try:
        strategy = HSI50XYZoneYFinance()

        if not strategy.load_data_from_yfinance():
            return

        strategy.run_backtest()
        strategy.analyze_results()

        print("\n🎉 XY分区策略测试完成！")
        print("💡 使用YFinance实时数据，策略表现已分析")

    except Exception as e:
        print(f"❌ 运行失败: {e}")
        print("💡 请确保已安装yfinance: pip install yfinance")

if __name__ == "__main__":
    main()
