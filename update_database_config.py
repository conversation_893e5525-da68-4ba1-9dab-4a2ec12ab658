#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量更新数据库配置
================
将所有Python文件中的数据库配置更新为新的连接参数
"""

import os
import re
from datetime import datetime

class DatabaseConfigUpdater:
    def __init__(self):
        """初始化配置更新器"""
        self.old_config = {
            'host': '************',
            'user': 'root',
            'password': '',
        }
        
        self.new_config = {
            'host': 'localhost',
            'user': 'root',
            'password': '12345678',
        }
        
        self.updated_files = []
        self.skipped_files = []
        
    def find_python_files(self):
        """查找当前目录下的所有Python文件"""
        python_files = []
        for file in os.listdir('.'):
            if file.endswith('.py') and file != 'update_database_config.py':
                python_files.append(file)
        return python_files
    
    def backup_file(self, filename):
        """备份文件"""
        backup_name = f"{filename}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        try:
            with open(filename, 'r', encoding='utf-8') as original:
                content = original.read()
            with open(backup_name, 'w', encoding='utf-8') as backup:
                backup.write(content)
            return backup_name
        except Exception as e:
            print(f"⚠️ 备份文件 {filename} 失败: {e}")
            return None
    
    def update_file_config(self, filename):
        """更新单个文件的数据库配置"""
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                content = f.read()
            
            original_content = content
            updated = False
            
            # 模式1: 字典形式的配置
            patterns = [
                # 匹配 'host': '************'
                (r"'host'\s*:\s*'192\.168\.1\.10'", "'host': 'localhost'"),
                (r'"host"\s*:\s*"192\.168\.1\.10"', '"host": "localhost"'),
                
                # 匹配 'password': ''
                (r"'password'\s*:\s*''", "'password': '12345678'"),
                (r'"password"\s*:\s*""', '"password": "12345678"'),
                
                # 匹配完整的数据库配置块
                (r"'host'\s*:\s*'192\.168\.1\.10',\s*'database'\s*:\s*'finance',\s*'user'\s*:\s*'root',\s*'password'\s*:\s*''",
                 "'host': 'localhost',\n        'database': 'finance',\n        'user': 'root',\n        'password': '12345678'"),
            ]
            
            for pattern, replacement in patterns:
                if re.search(pattern, content):
                    content = re.sub(pattern, replacement, content)
                    updated = True
            
            # 如果内容有变化，写回文件
            if updated and content != original_content:
                # 先备份
                backup_name = self.backup_file(filename)
                if backup_name:
                    print(f"📄 已备份: {filename} -> {backup_name}")
                
                # 写入更新后的内容
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(content)
                
                self.updated_files.append(filename)
                print(f"✅ 已更新: {filename}")
                return True
            else:
                self.skipped_files.append(filename)
                print(f"⚠️ 跳过: {filename} (未找到需要更新的配置)")
                return False
                
        except Exception as e:
            print(f"❌ 更新文件 {filename} 失败: {e}")
            return False
    
    def create_new_config_template(self):
        """创建新的数据库配置模板"""
        template = '''
# 新的数据库配置模板
# ==================

# 标准配置
db_config = {
    'host': 'localhost',
    'database': 'finance',
    'user': 'root',
    'password': '12345678',
    'charset': 'utf8mb4',
    'autocommit': True
}

# 连接示例
import mysql.connector

try:
    connection = mysql.connector.connect(**db_config)
    cursor = connection.cursor()
    print("✅ 数据库连接成功")
    
    # 测试查询
    cursor.execute("SELECT DATABASE()")
    current_db = cursor.fetchone()[0]
    print(f"📊 当前数据库: {current_db}")
    
    cursor.close()
    connection.close()
    
except mysql.connector.Error as e:
    print(f"❌ 数据库连接失败: {e}")
'''
        
        with open('new_database_config_template.py', 'w', encoding='utf-8') as f:
            f.write(template)
        
        print("📄 已创建新配置模板: new_database_config_template.py")
    
    def test_new_connection(self):
        """测试新的数据库连接"""
        try:
            import mysql.connector
            
            test_config = {
                'host': 'localhost',
                'database': 'finance',
                'user': 'root',
                'password': '12345678',
                'charset': 'utf8mb4'
            }
            
            print("\n🧪 测试新的数据库连接...")
            connection = mysql.connector.connect(**test_config)
            cursor = connection.cursor()
            
            # 测试基本查询
            cursor.execute("SELECT DATABASE(), VERSION(), NOW()")
            result = cursor.fetchone()
            
            print(f"✅ 连接成功!")
            print(f"   📊 数据库: {result[0]}")
            print(f"   🔧 版本: {result[1]}")
            print(f"   ⏰ 时间: {result[2]}")
            
            # 测试表访问
            cursor.execute("SHOW TABLES LIKE 'stock_%' LIMIT 5")
            tables = cursor.fetchall()
            if tables:
                print(f"   📋 找到股票表: {len(tables)} 个")
                for table in tables:
                    print(f"      • {table[0]}")
            
            cursor.close()
            connection.close()
            
            return True
            
        except Exception as e:
            print(f"❌ 连接测试失败: {e}")
            print("💡 请检查:")
            print("   1. MySQL服务是否启动")
            print("   2. 用户名密码是否正确")
            print("   3. finance数据库是否存在")
            print("   4. 网络连接是否正常")
            return False
    
    def run(self):
        """执行配置更新"""
        print("🎯 批量更新数据库配置")
        print("=" * 60)
        print(f"📅 执行时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"🔄 配置变更:")
        print(f"   旧配置: host='{self.old_config['host']}', user='{self.old_config['user']}', password='{self.old_config['password']}'")
        print(f"   新配置: host='{self.new_config['host']}', user='{self.new_config['user']}', password='{self.new_config['password']}'")
        
        # 1. 查找Python文件
        python_files = self.find_python_files()
        print(f"\n📁 找到 {len(python_files)} 个Python文件")
        
        # 2. 更新每个文件
        print(f"\n🔄 开始更新文件...")
        for filename in python_files:
            print(f"\n📝 处理文件: {filename}")
            self.update_file_config(filename)
        
        # 3. 显示更新结果
        print(f"\n📊 更新结果:")
        print(f"   ✅ 成功更新: {len(self.updated_files)} 个文件")
        if self.updated_files:
            for file in self.updated_files:
                print(f"      • {file}")
        
        print(f"   ⚠️ 跳过文件: {len(self.skipped_files)} 个文件")
        if self.skipped_files:
            for file in self.skipped_files:
                print(f"      • {file}")
        
        # 4. 创建配置模板
        self.create_new_config_template()
        
        # 5. 测试新连接
        self.test_new_connection()
        
        print(f"\n🎉 配置更新完成!")
        print(f"💡 建议:")
        print(f"   1. 检查更新后的文件是否正确")
        print(f"   2. 测试相关功能是否正常")
        print(f"   3. 如有问题可使用备份文件恢复")

def main():
    """主函数"""
    updater = DatabaseConfigUpdater()
    updater.run()

if __name__ == "__main__":
    main()
