import pandas as pd

# 读取Excel
df = pd.read_excel('交易记录追踪0023HK.xlsx')

# 添加止盈价和止损价列
if '止盈价' not in df.columns:
    df.insert(5, '止盈价', 0.00)

if '止损价' not in df.columns:
    df.insert(6, '止损价', 0.00)

# 设置最新记录的止盈止损价格
trade_price = 12.18
df.iloc[-1, df.columns.get_loc('止盈价')] = 12.03  # 空头止盈 -1.2%
df.iloc[-1, df.columns.get_loc('止损价')] = 12.25  # 空头止损 +0.6%

# 修正资金数据
df.iloc[-1, df.columns.get_loc('持仓数量')] = 200
df.iloc[-1, df.columns.get_loc('交易金额')] = 2436.00
df.iloc[-1, df.columns.get_loc('手续费')] = 2.44
df.iloc[-1, df.columns.get_loc('账户余额')] = 61.56
df.iloc[-1, df.columns.get_loc('当前市值')] = 2436.00
df.iloc[-1, df.columns.get_loc('总资产')] = 2497.56
df.iloc[-1, df.columns.get_loc('累计收益率')] = -0.10

# 保存
df.to_excel('交易记录追踪0023HK.xlsx', index=False)

print("✅ Excel已更新，添加了止盈价和止损价")
print("🎯 止盈价: 12.03 港元 (-1.2%)")
print("🛑 止损价: 12.25 港元 (+0.6%)")
print("💰 资金已修正为实际情况")
