#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
赔率对比分析
比较1:1.5, 1:2, 1:2.9的效果和持仓时间
"""

import numpy as np
import pandas as pd

class OddsComparison:
    def __init__(self):
        """初始化赔率对比分析"""
        self.win_rate = 0.46  # 历史胜率46%
        
    def calculate_kelly_value(self, odds, win_rate):
        """计算凯利值"""
        # 凯利公式: f = (bp - q) / b
        kelly_f = (odds * win_rate - (1 - win_rate)) / odds
        return max(0, kelly_f)
    
    def estimate_holding_time(self, take_profit_pct, stop_loss_pct):
        """估算持仓时间 (基于价格波动)"""
        # 基于东亚银行日均波动约1.5%估算
        daily_volatility = 0.015
        
        # 到达止盈的预期天数
        tp_days = (take_profit_pct / daily_volatility) * 0.7  # 考虑趋势因素
        
        # 到达止损的预期天数
        sl_days = (stop_loss_pct / daily_volatility) * 0.5  # 止损通常更快
        
        # 加权平均持仓时间
        avg_holding_days = self.win_rate * tp_days + (1 - self.win_rate) * sl_days
        
        return avg_holding_days
    
    def analyze_odds_scenarios(self):
        """分析不同赔率场景"""
        print("📊 赔率对比分析")
        print("=" * 60)
        
        scenarios = [
            {
                'name': '原始设置',
                'odds': 1.5,
                'take_profit': 0.012,  # 1.2%
                'stop_loss': 0.008     # 0.8%
            },
            {
                'name': '平衡设置 (推荐)',
                'odds': 2.0,
                'take_profit': 0.016,  # 1.6%
                'stop_loss': 0.008     # 0.8%
            },
            {
                'name': '凯利最优',
                'odds': 2.9,
                'take_profit': 0.023,  # 2.3%
                'stop_loss': 0.008     # 0.8%
            }
        ]
        
        results = []
        
        for scenario in scenarios:
            kelly_value = self.calculate_kelly_value(scenario['odds'], self.win_rate)
            holding_days = self.estimate_holding_time(scenario['take_profit'], scenario['stop_loss'])
            
            # 计算期望收益
            expected_return = (self.win_rate * scenario['take_profit'] - 
                             (1 - self.win_rate) * scenario['stop_loss'])
            
            # 年化收益 (假设每月2次交易)
            trades_per_year = 365 / holding_days * 0.7  # 考虑市场开放时间
            annual_return = expected_return * trades_per_year
            
            results.append({
                'scenario': scenario['name'],
                'odds': scenario['odds'],
                'kelly': kelly_value,
                'holding_days': holding_days,
                'expected_return': expected_return,
                'annual_return': annual_return,
                'take_profit': scenario['take_profit'],
                'stop_loss': scenario['stop_loss']
            })
        
        # 显示结果
        for i, result in enumerate(results):
            print(f"\n{i+1}. {result['scenario']}")
            print("-" * 40)
            print(f"📈 赔率: 1:{result['odds']:.1f}")
            print(f"🎯 止盈: {result['take_profit']*100:.1f}% | 🛑 止损: {result['stop_loss']*100:.1f}%")
            print(f"🧮 凯利值: {result['kelly']:.3f} (建议仓位: {result['kelly']*100:.1f}%)")
            print(f"⏰ 预期持仓: {result['holding_days']:.1f} 天")
            print(f"💰 单次期望收益: {result['expected_return']*100:+.2f}%")
            print(f"📊 年化收益率: {result['annual_return']*100:+.1f}%")
            
            # 风险评估
            if result['holding_days'] <= 3:
                risk_level = "低 (快进快出)"
            elif result['holding_days'] <= 7:
                risk_level = "中 (适中持仓)"
            else:
                risk_level = "高 (长期持仓)"
            
            print(f"⚠️ 持仓风险: {risk_level}")
        
        return results
    
    def recommend_best_setup(self, results):
        """推荐最佳设置"""
        print(f"\n🎯 推荐分析")
        print("=" * 60)
        
        # 找到1:2赔率的结果
        balanced_result = next(r for r in results if r['odds'] == 2.0)
        
        print(f"💡 推荐选择: {balanced_result['scenario']}")
        print(f"理由:")
        print(f"  ✅ 合理的凯利值: {balanced_result['kelly']:.3f} (不过度激进)")
        print(f"  ✅ 适中持仓时间: {balanced_result['holding_days']:.1f}天 (降低心理压力)")
        print(f"  ✅ 良好的年化收益: {balanced_result['annual_return']*100:+.1f}%")
        print(f"  ✅ 平衡风险收益: 1:2赔率经典设置")
        
        print(f"\n📋 具体参数:")
        print(f"  🟢 看涨: 止盈1.6% / 止损0.8%")
        print(f"  🔴 看跌: 止盈2.0% / 止损1.0%")
        print(f"  💰 建议仓位: {balanced_result['kelly']*100:.1f}%")
        
        print(f"\n🆚 与其他方案对比:")
        original = next(r for r in results if r['odds'] == 1.5)
        optimal = next(r for r in results if r['odds'] == 2.9)
        
        print(f"  vs 原始设置: 凯利值提升{((balanced_result['kelly']/original['kelly']-1)*100):+.0f}%")
        print(f"  vs 凯利最优: 持仓时间减少{((optimal['holding_days']/balanced_result['holding_days']-1)*100):+.0f}%")
    
    def show_practical_tips(self):
        """显示实用建议"""
        print(f"\n💡 实用建议")
        print("=" * 60)
        
        print(f"🎯 1:2赔率的优势:")
        print(f"  • 心理压力小: 持仓时间适中，不用长期担心")
        print(f"  • 执行容易: 止盈止损点位合理，容易坚持")
        print(f"  • 风险可控: 即使连续亏损也不会大幅回撤")
        print(f"  • 收益稳定: 平衡了收益和风险")
        
        print(f"\n📈 操作建议:")
        print(f"  • 看涨信号: 入场后设置1.6%止盈，0.8%止损")
        print(f"  • 看跌信号: 入场后设置2.0%止盈，1.0%止损")
        print(f"  • 仓位控制: 单次投入总资金的15-20%")
        print(f"  • 心态管理: 严格执行，不要贪婪或恐惧")
        
        print(f"\n⚠️ 注意事项:")
        print(f"  • 市场波动大时可适当降低仓位")
        print(f"  • 连续亏损时暂停交易，检查策略")
        print(f"  • 定期回顾交易记录，优化参数")

def main():
    """主函数"""
    analyzer = OddsComparison()
    
    # 分析不同赔率场景
    results = analyzer.analyze_odds_scenarios()
    
    # 推荐最佳设置
    analyzer.recommend_best_setup(results)
    
    # 显示实用建议
    analyzer.show_practical_tips()

if __name__ == "__main__":
    main()
