#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
调试信号生成 - 分析为什么没有交易
"""

import pandas as pd
import numpy as np
import mysql.connector
from datetime import datetime, timedelta

def debug_signal_generation():
    """调试信号生成逻辑"""
    print("🔍 调试信号生成 - 分析为什么没有交易")
    print("=" * 60)
    
    try:
        # 连接数据库
        config = {
            'host': 'localhost',
            'port': 3306,
            'user': 'root',
            'password': '12345678',
            'database': 'finance',
            'charset': 'utf8mb4'
        }
        
        conn = mysql.connector.connect(**config)
        cursor = conn.cursor()
        
        # 获取最近1年数据
        one_year_ago = datetime.now() - timedelta(days=365)
        
        query = """
        SELECT 
            Date, Close, 
            Y_Value, X_Value, E_Value, 
            Full_Y, E as E2, Controller,
            RSI, MFI, midprice
        FROM eab_0023hk 
        WHERE Date >= %s
        AND Close IS NOT NULL
        ORDER BY Date ASC
        """
        
        cursor.execute(query, (one_year_ago.strftime('%Y-%m-%d'),))
        results = cursor.fetchall()
        
        columns = ['date', 'close', 'y_value', 'x_value', 'e_value', 
                  'full_y', 'e2', 'controller', 'rsi', 'mfi', 'midprice']
        
        df = pd.DataFrame(results, columns=columns)
        df['date'] = pd.to_datetime(df['date'])
        
        # 转换数值列
        numeric_cols = ['close', 'y_value', 'x_value', 'e_value', 
                       'full_y', 'e2', 'controller', 'rsi', 'mfi', 'midprice']
        
        for col in numeric_cols:
            if col in df.columns:
                df[col] = pd.to_numeric(df[col], errors='coerce')
        
        cursor.close()
        conn.close()
        
        print(f"✅ 数据加载完成: {len(df)} 条记录")
        print(f"📅 时间范围: {df['date'].min().date()} 到 {df['date'].max().date()}")
        print()
        
        # 分析数据质量
        print("📊 数据质量分析:")
        print("-" * 30)
        for col in ['y_value', 'x_value', 'e_value', 'full_y', 'e2', 'controller']:
            null_count = df[col].isnull().sum()
            valid_count = len(df) - null_count
            print(f"   {col}: {valid_count}/{len(df)} 有效 ({null_count} 空值)")
        print()
        
        # 分析信号生成
        signal_analysis = []
        buy_signals_system1 = 0
        sell_signals_system1 = 0
        buy_signals_system2 = 0
        sell_signals_system2 = 0
        strong_signals = 0
        
        for i, row in df.iterrows():
            # 系统1信号分析
            y1, x1, e1 = row['y_value'], row['x_value'], row['e_value']
            signal1 = "观望"
            
            if pd.notna(e1) and pd.notna(y1):
                if e1 > 0 and y1 > 0.6:
                    signal1 = "买入"
                    buy_signals_system1 += 1
                elif e1 < -0.05 and y1 < 0.4:
                    signal1 = "卖出"
                    sell_signals_system1 += 1
            
            # 系统2信号分析
            y2, e2, controller = row['full_y'], row['e2'], row['controller']
            signal2 = "观望"
            price_deviation = 0
            
            if pd.notna(row['midprice']) and row['midprice'] > 0:
                price_deviation = (row['close'] - row['midprice']) / row['midprice']
            
            if pd.notna(controller):
                if controller == 1 and price_deviation < -0.05:
                    signal2 = "买入"
                    buy_signals_system2 += 1
                elif controller == 0 and price_deviation > 0.05:
                    signal2 = "卖出"
                    sell_signals_system2 += 1
            
            # 综合信号强度
            strength = 0
            final_signal = "观望"
            
            if signal1 == "买入" and signal2 == "买入":
                final_signal = "买入"
                strength = 4
                strong_signals += 1
            elif signal1 == "买入" or signal2 == "买入":
                final_signal = "买入"
                strength = 2
            elif signal1 == "卖出" and signal2 == "卖出":
                final_signal = "卖出"
                strength = 4
                strong_signals += 1
            elif signal1 == "卖出" or signal2 == "卖出":
                final_signal = "卖出"
                strength = 2
            
            signal_analysis.append({
                'date': row['date'],
                'close': row['close'],
                'y1': y1,
                'x1': x1,
                'e1': e1,
                'y2': y2,
                'e2': e2,
                'controller': controller,
                'deviation': price_deviation,
                'signal1': signal1,
                'signal2': signal2,
                'final_signal': final_signal,
                'strength': strength
            })
        
        # 统计结果
        print("🚦 信号统计分析:")
        print("-" * 30)
        print(f"   系统1买入信号: {buy_signals_system1}")
        print(f"   系统1卖出信号: {sell_signals_system1}")
        print(f"   系统2买入信号: {buy_signals_system2}")
        print(f"   系统2卖出信号: {sell_signals_system2}")
        print(f"   强信号(强度4): {strong_signals}")
        print()
        
        # 分析为什么没有强信号
        print("🔍 强信号缺失原因分析:")
        print("-" * 40)
        
        # 检查系统1条件
        system1_buy_candidates = df[(df['e_value'] > 0) & (df['y_value'] > 0.6)]
        system1_sell_candidates = df[(df['e_value'] < -0.05) & (df['y_value'] < 0.4)]
        
        print(f"📈 系统1分析:")
        print(f"   E值>0 且 Y值>0.6: {len(system1_buy_candidates)} 次")
        print(f"   E值<-0.05 且 Y值<0.4: {len(system1_sell_candidates)} 次")
        
        # 检查系统2条件
        controller_1_count = len(df[df['controller'] == 1])
        controller_0_count = len(df[df['controller'] == 0])
        
        print(f"\n📊 系统2分析:")
        print(f"   Controller=1: {controller_1_count} 次")
        print(f"   Controller=0: {controller_0_count} 次")
        
        # 检查价格偏离
        df['price_deviation'] = (df['close'] - df['midprice']) / df['midprice']
        low_deviation = len(df[df['price_deviation'] < -0.05])
        high_deviation = len(df[df['price_deviation'] > 0.05])
        
        print(f"   价格偏离<-5%: {low_deviation} 次")
        print(f"   价格偏离>+5%: {high_deviation} 次")
        
        # 检查双系统组合
        print(f"\n🔗 双系统组合分析:")
        
        # 买入组合
        system1_buy = (df['e_value'] > 0) & (df['y_value'] > 0.6)
        system2_buy = (df['controller'] == 1) & (df['price_deviation'] < -0.05)
        double_buy = system1_buy & system2_buy
        
        print(f"   双系统买入: {double_buy.sum()} 次")
        
        # 卖出组合
        system1_sell = (df['e_value'] < -0.05) & (df['y_value'] < 0.4)
        system2_sell = (df['controller'] == 0) & (df['price_deviation'] > 0.05)
        double_sell = system1_sell & system2_sell
        
        print(f"   双系统卖出: {double_sell.sum()} 次")
        
        # 显示最接近的信号
        print(f"\n🎯 最接近强信号的情况:")
        print("-" * 40)
        
        signals_df = pd.DataFrame(signal_analysis)
        strong_signals_df = signals_df[signals_df['strength'] >= 3]
        
        if len(strong_signals_df) > 0:
            print("强度≥3的信号:")
            for _, signal in strong_signals_df.head(5).iterrows():
                print(f"   {signal['date'].date()}: {signal['final_signal']} (强度{signal['strength']})")
        else:
            print("没有强度≥3的信号")
            
            # 显示最强的信号
            max_strength = signals_df['strength'].max()
            max_signals = signals_df[signals_df['strength'] == max_strength].head(5)
            
            print(f"最强信号 (强度{max_strength}):")
            for _, signal in max_signals.iterrows():
                print(f"   {signal['date'].date()}: {signal['final_signal']} (强度{signal['strength']})")
        
        # 建议调整
        print(f"\n💡 建议调整:")
        print("-" * 20)
        print("1. 降低信号强度要求: 从4降到2或3")
        print("2. 放宽系统1条件: E值>-0.02, Y值>0.5")
        print("3. 放宽系统2条件: 价格偏离>3%")
        print("4. 允许单系统信号: 不要求双系统同时")
        
        # 保存详细分析
        signals_df.to_excel("信号生成调试分析.xlsx", index=False)
        print(f"\n✅ 详细分析已保存: 信号生成调试分析.xlsx")
        
        return signals_df
        
    except Exception as e:
        print(f"❌ 调试失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def main():
    """主函数"""
    print("🔍 信号生成调试工具")
    print("=" * 50)
    print("📋 分析目标:")
    print("   • 检查数据质量")
    print("   • 分析信号生成逻辑")
    print("   • 找出没有交易的原因")
    print("   • 提供优化建议")
    
    try:
        signals_df = debug_signal_generation()
        
        if signals_df is not None:
            print("\n🎉 调试分析完成！")
            print("📊 现在您知道为什么没有交易了")
        else:
            print("\n❌ 调试分析失败")
            
    except Exception as e:
        print(f"\n❌ 程序执行失败: {e}")

if __name__ == "__main__":
    main()
