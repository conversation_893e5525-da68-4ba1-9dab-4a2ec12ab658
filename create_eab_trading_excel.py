#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建东亚银行交易记录Excel文件
===========================
生成包含100条交易记录的Excel文件，包含所有指定字段
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import yfinance as yf
import warnings
warnings.filterwarnings('ignore')

class EABTradingExcelGenerator:
    """东亚银行交易Excel生成器"""
    
    def __init__(self):
        """初始化参数"""
        self.symbol = "0023.HK"
        self.initial_capital = 100000  # 初始资金10万港元
        self.position_size = 0.3       # 30%仓位
        
        # 止盈止损参数
        self.take_profit_long = 0.016   # 多头止盈1.6%
        self.stop_loss_long = 0.008     # 多头止损0.8%
        self.take_profit_short = 0.008  # 空头止盈0.8%
        self.stop_loss_short = 0.016    # 空头止损1.6%
    
    def get_eab_data(self):
        """获取东亚银行历史数据"""
        print("📊 获取东亚银行(00023.HK)历史数据...")
        
        try:
            # 获取最近2年的数据以确保有足够的交易信号
            end_date = datetime.now()
            start_date = end_date - timedelta(days=2*365)
            
            eab = yf.Ticker(self.symbol)
            hist_data = eab.history(start=start_date, end=end_date)
            
            if hist_data.empty:
                print("❌ 未能获取到历史数据")
                return None
            
            # 数据预处理
            hist_data.reset_index(inplace=True)
            df = pd.DataFrame({
                'Date': hist_data['Date'],
                'Open': hist_data['Open'],
                'High': hist_data['High'],
                'Low': hist_data['Low'],
                'Close': hist_data['Close'],
                'Volume': hist_data['Volume']
            })
            
            print(f"✅ 成功获取 {len(df)} 条历史数据")
            return df
            
        except Exception as e:
            print(f"❌ 获取数据失败: {e}")
            return None
    
    def calculate_indicators(self, df):
        """计算XYE指标"""
        print("📊 计算XYE指标...")
        
        # 计算Y指标 (价格在20日区间的位置)
        window = 20
        df['high_20'] = df['High'].rolling(window).max()
        df['low_20'] = df['Low'].rolling(window).min()
        df['Y'] = (df['Close'] - df['low_20']) / (df['high_20'] - df['low_20'])
        df['Y'] = df['Y'].fillna(0.5).clip(0, 1)
        
        # 计算X指标 (综合技术指标)
        # 成交量比率
        df['volume_sma'] = df['Volume'].rolling(20).mean()
        df['volume_ratio'] = df['Volume'] / df['volume_sma']
        df['volume_ratio'] = df['volume_ratio'].fillna(1.0)
        
        # 价格动量
        df['price_momentum'] = df['Close'].pct_change(5)
        df['price_momentum'] = df['price_momentum'].fillna(0)
        
        # RSI
        delta = df['Close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
        rs = gain / (loss + 1e-10)
        df['rsi'] = 100 - (100 / (1 + rs))
        df['rsi'] = df['rsi'].fillna(50)
        
        # 综合X值
        volume_component = np.clip((df['volume_ratio'] - 0.5) * 0.5, -0.5, 0.5)
        momentum_component = np.clip(df['price_momentum'] * 25, -0.5, 0.5)
        rsi_component = (df['rsi'] - 50) / 100
        
        df['X'] = 0.5 + volume_component + momentum_component + rsi_component * 0.3
        df['X'] = df['X'].clip(0, 1)
        
        # 计算E指标 (趋势指标)
        df['ema_12'] = df['Close'].ewm(span=12).mean()
        df['ema_26'] = df['Close'].ewm(span=26).mean()
        df['E'] = df['ema_12'] - df['ema_26']
        
        # 计算回归线
        window = 60
        df['regression_line'] = df['Close'].rolling(window).apply(
            lambda x: np.polyval(np.polyfit(range(len(x)), x, 1), len(x)-1) if len(x) == window else np.nan
        )
        df['price_position'] = (df['Close'] - df['regression_line']) / df['regression_line']
        df['price_position'] = df['price_position'].fillna(0)
        
        return df
    
    def generate_trading_signals(self, df):
        """生成交易信号"""
        print("🎯 生成交易信号...")
        
        signals = []
        for i, row in df.iterrows():
            if (pd.isna(row['Y']) or pd.isna(row['X']) or pd.isna(row['E']) or 
                pd.isna(row['price_position'])):
                signals.append(0)
                continue
            
            y_val = row['Y']
            x_val = row['X']
            e_val = row['E']
            price_pos = row['price_position']
            
            # 多头信号: Y>0.45 且 X>0.45 且 E>0 且价格低于回归线
            if y_val > 0.45 and x_val > 0.45 and e_val > 0 and price_pos < 0:
                signals.append(1)
            
            # 空头信号: 满足任一条件且价格高于回归线
            elif price_pos > 0:
                if (y_val < 0.3 or x_val < 0.3 or
                    (x_val > 0.45 and y_val < 0.35) or
                    (x_val < 0.45 and y_val > 0.35)):
                    signals.append(-1)
                else:
                    signals.append(0)
            else:
                signals.append(0)
        
        df['signal'] = signals
        return df
    
    def simulate_trading(self, df):
        """模拟交易过程"""
        print("💰 模拟交易过程...")
        
        trades = []
        capital = self.initial_capital
        position = 0
        entry_price = 0
        entry_date = None
        entry_time = None
        entry_y = 0
        entry_x = 0
        entry_e = 0
        take_profit_price = 0
        stop_loss_price = 0
        trade_id = 1
        
        for i in range(len(df)):
            row = df.iloc[i]
            current_date = row['Date'].date()
            current_time = row['Date'].strftime('%H:%M:%S')
            current_price = row['Close']
            
            # 检查平仓条件
            if position != 0:
                should_exit = False
                exit_reason = ""
                exit_price = current_price
                
                if position == 1:  # 多头
                    if row['High'] >= take_profit_price:
                        should_exit = True
                        exit_reason = "止盈"
                        exit_price = take_profit_price
                    elif row['Low'] <= stop_loss_price:
                        should_exit = True
                        exit_reason = "止损"
                        exit_price = stop_loss_price
                
                elif position == -1:  # 空头
                    if row['Low'] <= take_profit_price:
                        should_exit = True
                        exit_reason = "止盈"
                        exit_price = take_profit_price
                    elif row['High'] >= stop_loss_price:
                        should_exit = True
                        exit_reason = "止损"
                        exit_price = stop_loss_price
                
                if should_exit:
                    # 计算收益
                    position_value = capital * self.position_size
                    shares = int(position_value / entry_price)
                    
                    if position == 1:  # 多头
                        profit = shares * (exit_price - entry_price)
                    else:  # 空头
                        profit = shares * (entry_price - exit_price)
                    
                    capital += profit
                    holding_days = (current_date - entry_date).days
                    
                    # 记录交易
                    trades.append({
                        'i': trade_id,
                        '日期': current_date,
                        '时间': current_time,
                        '交易品种': '东亚银行(00023.HK)',
                        '价格': exit_price,
                        '数量': shares,
                        'Y值': row['Y'],
                        'X值': row['X'],
                        'E值': row['E'],
                        '方向': '平多' if position == 1 else '平空',
                        '止盈价': take_profit_price,
                        '止损价': stop_loss_price,
                        '收益': profit,
                        '总资本': capital,
                        '持仓时间': f"{holding_days}天",
                        '备注': f"{exit_reason}，入场价{entry_price:.2f}，Y{entry_y:.3f}，X{entry_x:.3f}，E{entry_e:.2f}"
                    })
                    
                    trade_id += 1
                    position = 0
                    
                    # 如果已经有100条记录，停止
                    if len(trades) >= 100:
                        break
            
            # 检查开仓条件
            if position == 0 and row['signal'] != 0:
                position = row['signal']
                entry_price = current_price
                entry_date = current_date
                entry_time = current_time
                entry_y = row['Y']
                entry_x = row['X']
                entry_e = row['E']
                
                # 计算止盈止损位
                if position == 1:  # 多头
                    take_profit_price = entry_price * (1 + self.take_profit_long)
                    stop_loss_price = entry_price * (1 - self.stop_loss_long)
                else:  # 空头
                    take_profit_price = entry_price * (1 - self.take_profit_short)
                    stop_loss_price = entry_price * (1 + self.stop_loss_short)
                
                # 计算仓位
                position_value = capital * self.position_size
                shares = int(position_value / entry_price)
                
                # 记录开仓交易
                trades.append({
                    'i': trade_id,
                    '日期': entry_date,
                    '时间': entry_time,
                    '交易品种': '东亚银行(00023.HK)',
                    '价格': entry_price,
                    '数量': shares,
                    'Y值': entry_y,
                    'X值': entry_x,
                    'E值': entry_e,
                    '方向': '开多' if position == 1 else '开空',
                    '止盈价': take_profit_price,
                    '止损价': stop_loss_price,
                    '收益': 0,
                    '总资本': capital,
                    '持仓时间': '0天',
                    '备注': f"开仓，目标止盈{take_profit_price:.2f}，止损{stop_loss_price:.2f}"
                })
                
                trade_id += 1
                
                # 如果已经有100条记录，停止
                if len(trades) >= 100:
                    break
        
        return trades
    
    def create_excel_file(self, trades):
        """创建Excel文件"""
        print("📄 创建Excel文件...")
        
        # 创建DataFrame
        df_trades = pd.DataFrame(trades)
        
        # 确保有100条记录
        if len(df_trades) < 100:
            print(f"⚠️ 只生成了 {len(df_trades)} 条交易记录")
        else:
            df_trades = df_trades.head(100)
        
        # 格式化数据
        df_trades['Y值'] = df_trades['Y值'].round(4)
        df_trades['X值'] = df_trades['X值'].round(4)
        df_trades['E值'] = df_trades['E值'].round(2)
        df_trades['价格'] = df_trades['价格'].round(2)
        df_trades['止盈价'] = df_trades['止盈价'].round(2)
        df_trades['止损价'] = df_trades['止损价'].round(2)
        df_trades['收益'] = df_trades['收益'].round(2)
        df_trades['总资本'] = df_trades['总资本'].round(2)
        
        # 生成文件名
        filename = f"东亚银行交易记录_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
        
        # 创建Excel文件
        with pd.ExcelWriter(filename, engine='openpyxl') as writer:
            df_trades.to_excel(writer, sheet_name='交易记录', index=False)
            
            # 获取工作表
            worksheet = writer.sheets['交易记录']
            
            # 设置列宽
            column_widths = {
                'A': 5,   # i
                'B': 12,  # 日期
                'C': 10,  # 时间
                'D': 20,  # 交易品种
                'E': 8,   # 价格
                'F': 8,   # 数量
                'G': 8,   # Y值
                'H': 8,   # X值
                'I': 8,   # E值
                'J': 8,   # 方向
                'K': 10,  # 止盈价
                'L': 10,  # 止损价
                'M': 10,  # 收益
                'N': 12,  # 总资本
                'O': 10,  # 持仓时间
                'P': 40   # 备注
            }
            
            for col, width in column_widths.items():
                worksheet.column_dimensions[col].width = width
            
            # 设置标题行格式
            for cell in worksheet[1]:
                cell.font = cell.font.copy(bold=True)
        
        print(f"✅ Excel文件已创建: {filename}")
        
        # 显示统计信息
        self.show_trading_statistics(df_trades)
        
        return filename
    
    def show_trading_statistics(self, df_trades):
        """显示交易统计信息"""
        print(f"\n📊 交易统计信息:")
        print(f"=" * 50)
        
        total_trades = len(df_trades)
        open_trades = len(df_trades[df_trades['方向'].str.contains('开')])
        close_trades = len(df_trades[df_trades['方向'].str.contains('平')])
        
        print(f"总交易记录: {total_trades} 条")
        print(f"开仓记录: {open_trades} 条")
        print(f"平仓记录: {close_trades} 条")
        
        if close_trades > 0:
            close_df = df_trades[df_trades['方向'].str.contains('平')]
            total_profit = close_df['收益'].sum()
            winning_trades = len(close_df[close_df['收益'] > 0])
            win_rate = winning_trades / close_trades * 100
            
            print(f"总收益: {total_profit:.2f} 港元")
            print(f"盈利交易: {winning_trades} 笔")
            print(f"胜率: {win_rate:.1f}%")
            
            if total_profit != 0:
                initial_capital = df_trades['总资本'].iloc[0] - df_trades['收益'].iloc[0]
                final_capital = df_trades['总资本'].iloc[-1]
                total_return = (final_capital - initial_capital) / initial_capital * 100
                print(f"总收益率: {total_return:.2f}%")
        
        # 显示最近几条记录
        print(f"\n📅 最近5条交易记录:")
        recent_trades = df_trades.tail(5)[['日期', '方向', '价格', '收益', '总资本', '备注']]
        for _, trade in recent_trades.iterrows():
            print(f"   {trade['日期']} {trade['方向']} {trade['价格']:.2f} 收益{trade['收益']:.2f} 资本{trade['总资本']:.0f}")
    
    def run(self):
        """运行完整流程"""
        print("🏦 东亚银行交易记录Excel生成器")
        print("=" * 80)
        
        try:
            # 获取数据
            df = self.get_eab_data()
            if df is None:
                return None
            
            # 计算指标
            df = self.calculate_indicators(df)
            
            # 生成交易信号
            df = self.generate_trading_signals(df)
            
            # 模拟交易
            trades = self.simulate_trading(df)
            
            if not trades:
                print("❌ 没有生成任何交易记录")
                return None
            
            # 创建Excel文件
            filename = self.create_excel_file(trades)
            
            return filename
            
        except Exception as e:
            print(f"❌ 生成Excel文件失败: {e}")
            return None

def main():
    """主函数"""
    generator = EABTradingExcelGenerator()
    filename = generator.run()
    
    if filename:
        print(f"\n🎉 东亚银行交易记录Excel文件生成完成！")
        print(f"📄 文件名: {filename}")
        print(f"💡 包含100条交易记录，可直接用于分析")
    else:
        print(f"❌ Excel文件生成失败")

if __name__ == "__main__":
    main()
