#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
查询东亚银行MoneyFlow数据
=======================
"""

import mysql.connector
import pandas as pd

def query_eab_moneyflow():
    """查询东亚银行MoneyFlow数据"""
    
    try:
        # 连接数据库
        conn = mysql.connector.connect(
            host="localhost",
            user="root",
            password="12345678",
            database="finance"
        )
        
        print("✅ 数据库连接成功")
        print("📊 查询东亚银行MoneyFlow数据")
        print("=" * 80)
        
        # 查询基本统计
        stats_query = """
        SELECT 
            COUNT(*) as total_records,
            MIN(Date) as start_date,
            MAX(Date) as end_date,
            MIN(Close) as min_price,
            MAX(Close) as max_price,
            AVG(Close) as avg_price,
            MIN(MFI) as min_mfi,
            MAX(MFI) as max_mfi,
            AVG(MFI) as avg_mfi,
            MIN(MoneyFlowRatio) as min_mfr,
            MAX(MoneyFlowRatio) as max_mfr,
            AVG(MoneyFlowRatio) as avg_mfr
        FROM eab_0023hk_moneyflow
        """
        
        df_stats = pd.read_sql_query(stats_query, conn)
        stats = df_stats.iloc[0]
        
        print(f"📊 数据统计:")
        print(f"   总记录数: {stats['total_records']:,} 条")
        print(f"   数据期间: {stats['start_date']} 至 {stats['end_date']}")
        print(f"   价格范围: {stats['min_price']:.2f} - {stats['max_price']:.2f} 港元 (平均: {stats['avg_price']:.2f})")
        print(f"   MFI范围: {stats['min_mfi']:.1f} - {stats['max_mfi']:.1f} (平均: {stats['avg_mfi']:.1f})")
        print(f"   MoneyFlowRatio范围: {stats['min_mfr']:.4f} - {stats['max_mfr']:.4f} (平均: {stats['avg_mfr']:.4f})")
        
        # 查询信号统计
        signal_query = """
        SELECT TradingSignal, COUNT(*) as count
        FROM eab_0023hk_moneyflow
        GROUP BY TradingSignal
        ORDER BY TradingSignal
        """
        
        df_signals = pd.read_sql_query(signal_query, conn)
        
        print(f"\n🎯 交易信号统计:")
        total_signals = df_signals['count'].sum()
        for _, row in df_signals.iterrows():
            signal = row['TradingSignal']
            count = row['count']
            signal_name = "🟢 做多" if signal == 1 else "🔴 做空" if signal == -1 else "⚪ 观望"
            percentage = count / total_signals * 100
            print(f"   {signal_name}: {count}次 ({percentage:.1f}%)")
        
        # 查询MFI分布
        mfi_query = """
        SELECT 
            SUM(CASE WHEN MFI < 20 THEN 1 ELSE 0 END) as oversold,
            SUM(CASE WHEN MFI BETWEEN 20 AND 80 THEN 1 ELSE 0 END) as normal,
            SUM(CASE WHEN MFI > 80 THEN 1 ELSE 0 END) as overbought
        FROM eab_0023hk_moneyflow
        """
        
        df_mfi = pd.read_sql_query(mfi_query, conn)
        mfi_dist = df_mfi.iloc[0]
        
        print(f"\n💰 MFI分布:")
        total_mfi = mfi_dist['oversold'] + mfi_dist['normal'] + mfi_dist['overbought']
        print(f"   超卖 (MFI<20): {mfi_dist['oversold']}次 ({mfi_dist['oversold']/total_mfi*100:.1f}%)")
        print(f"   正常 (20≤MFI≤80): {mfi_dist['normal']}次 ({mfi_dist['normal']/total_mfi*100:.1f}%)")
        print(f"   超买 (MFI>80): {mfi_dist['overbought']}次 ({mfi_dist['overbought']/total_mfi*100:.1f}%)")
        
        # 查询MoneyFlowRatio分布
        mfr_query = """
        SELECT 
            SUM(CASE WHEN MoneyFlowRatio < 0.5 THEN 1 ELSE 0 END) as low_mfr,
            SUM(CASE WHEN MoneyFlowRatio BETWEEN 0.5 AND 2.0 THEN 1 ELSE 0 END) as normal_mfr,
            SUM(CASE WHEN MoneyFlowRatio > 2.0 THEN 1 ELSE 0 END) as high_mfr
        FROM eab_0023hk_moneyflow
        """
        
        df_mfr = pd.read_sql_query(mfr_query, conn)
        mfr_dist = df_mfr.iloc[0]
        
        print(f"\n📊 MoneyFlowRatio分布:")
        total_mfr = mfr_dist['low_mfr'] + mfr_dist['normal_mfr'] + mfr_dist['high_mfr']
        print(f"   低比率 (<0.5): {mfr_dist['low_mfr']}次 ({mfr_dist['low_mfr']/total_mfr*100:.1f}%)")
        print(f"   正常比率 (0.5-2.0): {mfr_dist['normal_mfr']}次 ({mfr_dist['normal_mfr']/total_mfr*100:.1f}%)")
        print(f"   高比率 (>2.0): {mfr_dist['high_mfr']}次 ({mfr_dist['high_mfr']/total_mfr*100:.1f}%)")
        
        # 查询最新10条记录
        latest_query = """
        SELECT Date, Close, MFI, MoneyFlowRatio, Y_Value, X_Value, E_Value, TradingSignal
        FROM eab_0023hk_moneyflow
        ORDER BY Date DESC
        LIMIT 10
        """
        
        df_latest = pd.read_sql_query(latest_query, conn)
        
        print(f"\n📅 最新10条记录:")
        print("-" * 120)
        print(f"   {'日期':<12} {'价格':<8} {'MFI':<6} {'资金流比率':<10} {'Y值':<8} {'X值':<8} {'E值':<8} {'信号':<6}")
        print("-" * 120)
        
        for _, row in df_latest.iterrows():
            signal_text = "做多" if row['TradingSignal'] == 1 else "做空" if row['TradingSignal'] == -1 else "观望"
            print(f"   {row['Date']:<12} {row['Close']:<8.2f} {row['MFI']:<6.1f} {row['MoneyFlowRatio']:<10.4f} "
                  f"{row['Y_Value']:<8.4f} {row['X_Value']:<8.4f} {row['E_Value']:<8.2f} {signal_text:<6}")
        
        # 查询极值记录
        extremes_query = """
        SELECT 
            (SELECT CONCAT(Date, ' | MFI:', MFI, ' | 价格:', Close) FROM eab_0023hk_moneyflow ORDER BY MFI DESC LIMIT 1) as max_mfi,
            (SELECT CONCAT(Date, ' | MFI:', MFI, ' | 价格:', Close) FROM eab_0023hk_moneyflow ORDER BY MFI ASC LIMIT 1) as min_mfi,
            (SELECT CONCAT(Date, ' | 比率:', MoneyFlowRatio, ' | MFI:', MFI) FROM eab_0023hk_moneyflow ORDER BY MoneyFlowRatio DESC LIMIT 1) as max_mfr,
            (SELECT CONCAT(Date, ' | 比率:', MoneyFlowRatio, ' | MFI:', MFI) FROM eab_0023hk_moneyflow ORDER BY MoneyFlowRatio ASC LIMIT 1) as min_mfr
        """
        
        df_extremes = pd.read_sql_query(extremes_query, conn)
        extremes = df_extremes.iloc[0]
        
        print(f"\n🎯 极值记录:")
        print(f"   最高MFI: {extremes['max_mfi']}")
        print(f"   最低MFI: {extremes['min_mfi']}")
        print(f"   最高资金流比率: {extremes['max_mfr']}")
        print(f"   最低资金流比率: {extremes['min_mfr']}")
        
        # 查询信号与指标的关系
        signal_analysis_query = """
        SELECT 
            TradingSignal,
            AVG(MFI) as avg_mfi,
            AVG(MoneyFlowRatio) as avg_mfr,
            AVG(Y_Value) as avg_y,
            AVG(X_Value) as avg_x,
            COUNT(*) as count
        FROM eab_0023hk_moneyflow
        WHERE TradingSignal != 0
        GROUP BY TradingSignal
        """
        
        df_signal_analysis = pd.read_sql_query(signal_analysis_query, conn)
        
        print(f"\n📊 信号与指标关系:")
        for _, row in df_signal_analysis.iterrows():
            signal = row['TradingSignal']
            signal_name = "🟢 做多" if signal == 1 else "🔴 做空"
            print(f"   {signal_name}信号 ({row['count']}次):")
            print(f"     平均MFI: {row['avg_mfi']:.1f}")
            print(f"     平均资金流比率: {row['avg_mfr']:.4f}")
            print(f"     平均Y值: {row['avg_y']:.4f}")
            print(f"     平均X值: {row['avg_x']:.4f}")
        
        conn.close()
        print(f"\n✅ 查询完成")
        
    except Exception as e:
        print(f"❌ 查询失败: {e}")

def main():
    """主函数"""
    query_eab_moneyflow()

if __name__ == "__main__":
    main()
