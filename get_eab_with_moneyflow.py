#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
获取东亚银行数据并计算MoneyFlowRatio
=================================
从Yahoo Finance获取数据，计算完整的资金流指标
"""

import yfinance as yf
import mysql.connector
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

class EABMoneyFlowAnalyzer:
    """东亚银行资金流分析器"""

    def __init__(self):
        """初始化"""
        self.symbol = "0023.HK"
        self.host = "localhost"
        self.user = "root"
        self.password = "12345678"
        self.database = "finance"
        self.table_name = "eab_0023hk_moneyflow"

    def connect_database(self):
        """连接数据库"""
        try:
            self.conn = mysql.connector.connect(
                host=self.host,
                user=self.user,
                password=self.password,
                database=self.database
            )
            self.cursor = self.conn.cursor()
            print("✅ MySQL数据库连接成功")
            return True
        except mysql.connector.Error as e:
            print(f"❌ MySQL连接失败: {e}")
            return False

    def create_table(self):
        """创建包含资金流指标的数据表"""
        try:
            create_table_sql = f"""
            CREATE TABLE IF NOT EXISTS {self.table_name} (
                id INT AUTO_INCREMENT PRIMARY KEY,
                Date DATE NOT NULL UNIQUE,
                Open DECIMAL(10,4) NOT NULL,
                High DECIMAL(10,4) NOT NULL,
                Low DECIMAL(10,4) NOT NULL,
                Close DECIMAL(10,4) NOT NULL,
                Volume BIGINT NOT NULL,
                TypicalPrice DECIMAL(10,4),
                MoneyFlow DECIMAL(20,4),
                PositiveMoneyFlow DECIMAL(20,4),
                NegativeMoneyFlow DECIMAL(20,4),
                MoneyFlowRatio DECIMAL(10,6),
                MFI DECIMAL(8,4),
                Y_Value DECIMAL(8,6),
                X_Value DECIMAL(8,6),
                E_Value DECIMAL(10,4),
                RSI DECIMAL(8,4),
                TradingSignal INT DEFAULT 0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                INDEX idx_date (Date),
                INDEX idx_close (Close),
                INDEX idx_mfi (MFI),
                INDEX idx_signal (TradingSignal)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            """

            self.cursor.execute(create_table_sql)
            self.conn.commit()
            print(f"✅ 数据表 {self.table_name} 创建成功")
            return True

        except mysql.connector.Error as e:
            print(f"❌ 创建数据表失败: {e}")
            return False

    def get_eab_data(self):
        """获取东亚银行数据"""
        print("📊 获取东亚银行(00023.HK)历史数据...")

        try:
            # 获取最近3年数据
            end_date = datetime.now()
            start_date = end_date - timedelta(days=3*365)

            eab = yf.Ticker(self.symbol)

            # 获取股票信息
            info = eab.info
            print(f"🏦 股票名称: {info.get('longName', '东亚银行有限公司')}")
            print(f"📊 股票代码: {self.symbol}")

            # 获取历史数据
            hist_data = eab.history(start=start_date, end=end_date)

            if hist_data.empty:
                print("❌ 未能获取到历史数据")
                return None

            # 数据预处理
            hist_data.reset_index(inplace=True)
            df = pd.DataFrame({
                'Date': hist_data['Date'].dt.date,
                'Open': hist_data['Open'].round(4),
                'High': hist_data['High'].round(4),
                'Low': hist_data['Low'].round(4),
                'Close': hist_data['Close'].round(4),
                'Volume': hist_data['Volume'].astype(int)
            })

            # 数据清理
            df = df.dropna()
            df = df.sort_values('Date').reset_index(drop=True)

            print(f"✅ 成功获取 {len(df):,} 条历史记录")
            print(f"📊 数据期间: {df['Date'].min()} 至 {df['Date'].max()}")
            print(f"💰 最新价格: {df['Close'].iloc[-1]:.2f} 港元")

            return df

        except Exception as e:
            print(f"❌ 获取数据失败: {e}")
            return None

    def calculate_money_flow_indicators(self, df):
        """计算资金流指标"""
        print("💰 计算资金流指标...")

        df = df.copy()

        # 1. 计算典型价格 (Typical Price)
        df['TypicalPrice'] = (df['High'] + df['Low'] + df['Close']) / 3

        # 2. 计算资金流 (Money Flow)
        df['MoneyFlow'] = df['TypicalPrice'] * df['Volume']

        # 3. 计算价格变化方向
        df['PriceChange'] = df['TypicalPrice'].diff()

        # 4. 分类正负资金流
        df['PositiveMoneyFlow'] = np.where(df['PriceChange'] > 0, df['MoneyFlow'], 0)
        df['NegativeMoneyFlow'] = np.where(df['PriceChange'] < 0, df['MoneyFlow'], 0)

        # 5. 计算14日资金流比率 (Money Flow Ratio)
        period = 14
        df['PositiveMF_14'] = df['PositiveMoneyFlow'].rolling(period).sum()
        df['NegativeMF_14'] = df['NegativeMoneyFlow'].rolling(period).sum()

        # 避免除零错误
        df['MoneyFlowRatio'] = df['PositiveMF_14'] / (df['NegativeMF_14'] + 1e-10)

        # 6. 计算资金流指标 (MFI)
        df['MFI'] = 100 - (100 / (1 + df['MoneyFlowRatio']))

        # 7. 计算Y指标 (价格位置)
        window = 20
        df['High_20'] = df['High'].rolling(window).max()
        df['Low_20'] = df['Low'].rolling(window).min()
        df['Y_Value'] = (df['Close'] - df['Low_20']) / (df['High_20'] - df['Low_20'])
        df['Y_Value'] = df['Y_Value'].fillna(0.5).clip(0, 1)

        # 8. 计算X指标 (综合技术指标)
        # 成交量比率
        df['Volume_SMA'] = df['Volume'].rolling(20).mean()
        df['VolumeRatio'] = df['Volume'] / df['Volume_SMA']
        df['VolumeRatio'] = df['VolumeRatio'].fillna(1.0)

        # 价格动量
        df['PriceMomentum'] = df['Close'].pct_change(5)
        df['PriceMomentum'] = df['PriceMomentum'].fillna(0)

        # RSI
        delta = df['Close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
        rs = gain / (loss + 1e-10)
        df['RSI'] = 100 - (100 / (1 + rs))
        df['RSI'] = df['RSI'].fillna(50)

        # 综合X值
        volume_component = np.clip((df['VolumeRatio'] - 0.5) * 0.5, -0.5, 0.5)
        momentum_component = np.clip(df['PriceMomentum'] * 25, -0.5, 0.5)
        rsi_component = (df['RSI'] - 50) / 100

        df['X_Value'] = 0.5 + volume_component + momentum_component + rsi_component * 0.3
        df['X_Value'] = df['X_Value'].clip(0, 1)

        # 9. 计算E指标 (趋势指标)
        df['EMA_12'] = df['Close'].ewm(span=12).mean()
        df['EMA_26'] = df['Close'].ewm(span=26).mean()
        df['E_Value'] = df['EMA_12'] - df['EMA_26']

        # 10. 生成交易信号
        df = self.generate_signals(df)

        print("✅ 资金流指标计算完成")
        return df

    def generate_signals(self, df):
        """生成交易信号"""
        print("🎯 生成交易信号...")

        # 计算回归线
        window = 60
        df['RegressionLine'] = df['Close'].rolling(window).apply(
            lambda x: np.polyval(np.polyfit(range(len(x)), x, 1), len(x)-1) if len(x) == window else np.nan
        )
        df['PricePosition'] = (df['Close'] - df['RegressionLine']) / df['RegressionLine']
        df['PricePosition'] = df['PricePosition'].fillna(0)

        signals = []
        for i, row in df.iterrows():
            if (pd.isna(row['Y_Value']) or pd.isna(row['X_Value']) or
                pd.isna(row['E_Value']) or pd.isna(row['PricePosition'])):
                signals.append(0)
                continue

            y_val = row['Y_Value']
            x_val = row['X_Value']
            e_val = row['E_Value']
            price_pos = row['PricePosition']
            mfi = row['MFI']

            # 多头信号: Y>0.45 且 X>0.45 且 E>0 且价格低于回归线 且 MFI<70
            if (y_val > 0.45 and x_val > 0.45 and e_val > 0 and
                price_pos < 0 and mfi < 70):
                signals.append(1)

            # 空头信号: 满足任一条件且价格高于回归线 且 MFI>30
            elif price_pos > 0 and mfi > 30:
                if (y_val < 0.3 or x_val < 0.3 or
                    (x_val > 0.45 and y_val < 0.35) or
                    (x_val < 0.45 and y_val > 0.35) or
                    mfi > 80):  # 加入MFI超买信号
                    signals.append(-1)
                else:
                    signals.append(0)
            else:
                signals.append(0)

        df['TradingSignal'] = signals
        return df

    def save_to_database(self, df):
        """保存数据到数据库"""
        print("💾 保存数据到数据库...")

        try:
            # 准备插入数据
            insert_sql = f"""
            INSERT IGNORE INTO {self.table_name}
            (Date, Open, High, Low, Close, Volume, TypicalPrice, MoneyFlow,
             PositiveMoneyFlow, NegativeMoneyFlow, MoneyFlowRatio, MFI,
             Y_Value, X_Value, E_Value, RSI, TradingSignal)
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            """

            data_tuples = []
            for _, row in df.iterrows():
                # 处理NaN值
                def safe_float(val, default=0.0):
                    return float(val) if pd.notna(val) else default

                def safe_int(val, default=0):
                    return int(val) if pd.notna(val) else default

                data_tuples.append((
                    row['Date'],
                    safe_float(row['Open']),
                    safe_float(row['High']),
                    safe_float(row['Low']),
                    safe_float(row['Close']),
                    safe_int(row['Volume']),
                    safe_float(row['TypicalPrice']),
                    safe_float(row['MoneyFlow']),
                    safe_float(row['PositiveMoneyFlow']),
                    safe_float(row['NegativeMoneyFlow']),
                    safe_float(row['MoneyFlowRatio']),
                    safe_float(row['MFI'], 50.0),
                    safe_float(row['Y_Value'], 0.5),
                    safe_float(row['X_Value'], 0.5),
                    safe_float(row['E_Value']),
                    safe_float(row['RSI'], 50.0),
                    safe_int(row['TradingSignal'])
                ))

            # 批量插入
            self.cursor.executemany(insert_sql, data_tuples)
            self.conn.commit()

            inserted_rows = self.cursor.rowcount
            print(f"✅ 成功保存 {inserted_rows:,} 条记录到数据库")

            return True

        except mysql.connector.Error as e:
            print(f"❌ 保存数据失败: {e}")
            self.conn.rollback()
            return False

    def analyze_money_flow_data(self):
        """分析资金流数据"""
        print("\n📊 资金流数据分析")
        print("=" * 80)

        try:
            # 获取最新数据统计
            stats_sql = f"""
            SELECT
                COUNT(*) as total_records,
                MIN(Date) as earliest_date,
                MAX(Date) as latest_date,
                AVG(Close) as avg_price,
                AVG(MFI) as avg_mfi,
                AVG(MoneyFlowRatio) as avg_money_flow_ratio,
                AVG(Y_Value) as avg_y,
                AVG(X_Value) as avg_x,
                AVG(E_Value) as avg_e
            FROM {self.table_name}
            """

            self.cursor.execute(stats_sql)
            stats = self.cursor.fetchone()

            print(f"📊 数据统计:")
            print(f"   总记录数: {stats[0]:,} 条")
            print(f"   数据期间: {stats[1]} 至 {stats[2]}")
            print(f"   平均价格: {stats[3]:.2f} 港元")
            print(f"   平均MFI: {stats[4]:.2f}")
            print(f"   平均资金流比率: {stats[5]:.4f}")
            print(f"   平均Y值: {stats[6]:.4f}")
            print(f"   平均X值: {stats[7]:.4f}")
            print(f"   平均E值: {stats[8]:.2f}")

            # 信号统计
            signal_sql = f"""
            SELECT TradingSignal, COUNT(*) as count
            FROM {self.table_name}
            GROUP BY TradingSignal
            ORDER BY TradingSignal
            """

            self.cursor.execute(signal_sql)
            signals = self.cursor.fetchall()

            print(f"\n🎯 交易信号统计:")
            total_signals = sum([s[1] for s in signals])
            for signal, count in signals:
                signal_name = "🟢 做多" if signal == 1 else "🔴 做空" if signal == -1 else "⚪ 观望"
                percentage = count / total_signals * 100
                print(f"   {signal_name}: {count}次 ({percentage:.1f}%)")

            # MFI分布统计
            mfi_sql = f"""
            SELECT
                SUM(CASE WHEN MFI < 20 THEN 1 ELSE 0 END) as oversold,
                SUM(CASE WHEN MFI BETWEEN 20 AND 80 THEN 1 ELSE 0 END) as normal,
                SUM(CASE WHEN MFI > 80 THEN 1 ELSE 0 END) as overbought
            FROM {self.table_name}
            WHERE MFI IS NOT NULL
            """

            self.cursor.execute(mfi_sql)
            mfi_dist = self.cursor.fetchone()

            print(f"\n💰 MFI分布统计:")
            print(f"   超卖 (MFI<20): {mfi_dist[0]}次")
            print(f"   正常 (20≤MFI≤80): {mfi_dist[1]}次")
            print(f"   超买 (MFI>80): {mfi_dist[2]}次")

            # 最新数据
            latest_sql = f"""
            SELECT Date, Close, MFI, MoneyFlowRatio, Y_Value, X_Value, E_Value, TradingSignal
            FROM {self.table_name}
            ORDER BY Date DESC
            LIMIT 5
            """

            self.cursor.execute(latest_sql)
            latest_data = self.cursor.fetchall()

            print(f"\n📅 最新5天数据:")
            print(f"   {'日期':<12} {'收盘价':<8} {'MFI':<8} {'资金流比率':<10} {'Y值':<8} {'X值':<8} {'E值':<8} {'信号':<6}")
            print(f"   " + "-" * 80)

            for row in latest_data:
                signal_text = "做多" if row[7] == 1 else "做空" if row[7] == -1 else "观望"
                print(f"   {row[0]:<12} {row[1]:<8.2f} {row[2]:<8.1f} {row[3]:<10.4f} {row[4]:<8.4f} {row[5]:<8.4f} {row[6]:<8.2f} {signal_text:<6}")

        except mysql.connector.Error as e:
            print(f"❌ 数据分析失败: {e}")

    def export_to_excel(self):
        """导出数据到Excel"""
        print("\n📄 导出数据到Excel...")

        try:
            # 查询所有数据
            query_sql = f"""
            SELECT Date, Open, High, Low, Close, Volume, TypicalPrice, MoneyFlow,
                   PositiveMoneyFlow, NegativeMoneyFlow, MoneyFlowRatio, MFI,
                   Y_Value, X_Value, E_Value, RSI, TradingSignal
            FROM {self.table_name}
            ORDER BY Date
            """

            df = pd.read_sql_query(query_sql, self.conn)

            # 生成文件名
            filename = f"东亚银行_MoneyFlow数据_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"

            # 导出到Excel
            with pd.ExcelWriter(filename, engine='openpyxl') as writer:
                df.to_excel(writer, sheet_name='资金流数据', index=False)

                # 设置列宽
                worksheet = writer.sheets['资金流数据']
                for column in worksheet.columns:
                    max_length = 0
                    column_letter = column[0].column_letter
                    for cell in column:
                        try:
                            if len(str(cell.value)) > max_length:
                                max_length = len(str(cell.value))
                        except:
                            pass
                    adjusted_width = min(max_length + 2, 20)
                    worksheet.column_dimensions[column_letter].width = adjusted_width

            print(f"✅ 数据已导出到: {filename}")
            return filename

        except Exception as e:
            print(f"❌ 导出Excel失败: {e}")
            return None

    def close_connection(self):
        """关闭数据库连接"""
        if hasattr(self, 'cursor') and self.cursor:
            self.cursor.close()
        if hasattr(self, 'conn') and self.conn.is_connected():
            self.conn.close()
            print("🔗 数据库连接已关闭")

    def run_complete_analysis(self):
        """运行完整分析流程"""
        print("🏦 东亚银行资金流分析系统")
        print("=" * 80)

        try:
            # 连接数据库
            if not self.connect_database():
                return False

            # 创建数据表
            if not self.create_table():
                return False

            # 获取数据
            df = self.get_eab_data()
            if df is None:
                return False

            # 计算指标
            df = self.calculate_money_flow_indicators(df)

            # 保存到数据库
            if not self.save_to_database(df):
                return False

            # 分析数据
            self.analyze_money_flow_data()

            # 导出Excel
            excel_file = self.export_to_excel()

            print(f"\n🎉 东亚银行资金流分析完成！")
            print(f"💾 数据已保存到数据库: {self.database}.{self.table_name}")
            if excel_file:
                print(f"📄 Excel文件: {excel_file}")

            return True

        except Exception as e:
            print(f"❌ 分析过程失败: {e}")
            return False

        finally:
            self.close_connection()

def main():
    """主函数"""
    analyzer = EABMoneyFlowAnalyzer()
    analyzer.run_complete_analysis()

if __name__ == "__main__":
    main()
