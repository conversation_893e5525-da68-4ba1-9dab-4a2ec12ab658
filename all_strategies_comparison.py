#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
所有策略版本对比分析
==================
对比原始XY策略、散户情绪策略、资金流策略和1-X策略的表现
"""

import pandas as pd
import numpy as np
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

def analyze_all_strategies_comparison():
    """分析所有策略对比"""
    
    print("📊 所有XY策略版本对比分析")
    print("=" * 80)
    print(f"📅 分析时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"🎯 数据源: hkhsi50 (恒生指数50成分股)")
    print(f"📊 数据期间: 1990-01-02 至 2025-07-18 (35.6年)")
    print(f"📈 数据记录: 8,772条")
    
    # 所有策略对比数据
    strategy_comparison = {
        '策略版本': [
            '原始XY策略',
            '散户情绪策略',
            '资金流策略',
            '1-X策略'
        ],
        'X的定义': [
            '1-Y (传统定义)',
            '散户买升概率',
            '散户买升概率',
            '散户买跌概率 (1-散户买升概率)'
        ],
        '策略逻辑': [
            'Y>0.43且X>0.43看涨',
            '散户情绪逆向+控股商跟随',
            '资金流逆向+控股商跟随',
            'Y>0.43且X>0.43看涨 (X=散户买跌概率)'
        ],
        '最终资金': [
            '3,009,108港元',
            '1,468,258港元',
            '477,847港元',
            '856,017港元'
        ],
        '年化收益率': [
            '5.41%',
            '3.30%',
            '0.25%',
            '1.74%'
        ],
        '最大回撤': [
            '9.32%',
            '8.19%',
            '3.76%',
            '6.64%'
        ],
        '总交易次数': [
            '5,052笔',
            '3,315笔',
            '846笔',
            '2,598笔'
        ],
        '看涨交易': [
            '5,047笔 (99.9%)',
            '3,141笔 (94.8%)',
            '436笔 (51.5%)',
            '2,178笔 (83.8%)'
        ],
        '看跌交易': [
            '5笔 (0.1%)',
            '174笔 (5.2%)',
            '410笔 (48.5%)',
            '420笔 (16.2%)'
        ],
        '胜率': [
            '40.5%',
            '40.7%',
            '51.5%',
            '42.6%'
        ],
        '看跌胜率': [
            '60.0%',
            '61.5%',
            '62.7%',
            '61.7%'
        ],
        '卡尔玛比率': [
            '0.58',
            '0.40',
            '0.07',
            '0.26'
        ]
    }
    
    df_comparison = pd.DataFrame(strategy_comparison)
    print(f"\n📊 全策略对比总览:")
    print(df_comparison.to_string(index=False))
    
    # 深度分析
    print(f"\n🔍 深度分析:")
    
    print(f"\n   1. 📈 收益表现排名:")
    print(f"      🥇 第1名: 原始XY策略 (5.41%年化，301万)")
    print(f"      🥈 第2名: 散户情绪策略 (3.30%年化，147万)")
    print(f"      🥉 第3名: 1-X策略 (1.74%年化，86万)")
    print(f"      🏅 第4名: 资金流策略 (0.25%年化，48万)")
    
    print(f"\n   2. 🛡️ 风险控制排名:")
    print(f"      🥇 第1名: 资金流策略 (3.76%回撤)")
    print(f"      🥈 第2名: 1-X策略 (6.64%回撤)")
    print(f"      🥉 第3名: 散户情绪策略 (8.19%回撤)")
    print(f"      🏅 第4名: 原始XY策略 (9.32%回撤)")
    
    print(f"\n   3. 📊 多空平衡排名:")
    print(f"      🥇 第1名: 资金流策略 (51.5%看涨，48.5%看跌)")
    print(f"      🥈 第2名: 1-X策略 (83.8%看涨，16.2%看跌)")
    print(f"      🥉 第3名: 散户情绪策略 (94.8%看涨，5.2%看跌)")
    print(f"      🏅 第4名: 原始XY策略 (99.9%看涨，0.1%看跌)")
    
    print(f"\n   4. ✅ 胜率表现排名:")
    print(f"      🥇 第1名: 资金流策略 (51.5%总胜率)")
    print(f"      🥈 第2名: 1-X策略 (42.6%总胜率)")
    print(f"      🥉 第3名: 散户情绪策略 (40.7%总胜率)")
    print(f"      🏅 第4名: 原始XY策略 (40.5%总胜率)")
    
    # 策略特征分析
    print(f"\n🎯 各策略特征分析:")
    
    print(f"\n   🎯 原始XY策略:")
    print(f"   • 特点: 高收益，传统定义")
    print(f"   • 优势: 收益率最高，逻辑简单")
    print(f"   • 劣势: 缺乏多空平衡，回撤较高")
    print(f"   • 适用: 追求高收益的积极投资者")
    
    print(f"\n   🎯 散户情绪策略:")
    print(f"   • 特点: 中等收益，基于散户心理")
    print(f"   • 优势: 收益风险平衡，有一定看跌交易")
    print(f"   • 劣势: 策略复杂，看跌交易仍较少")
    print(f"   • 适用: 平衡型投资者")
    
    print(f"\n   🎯 资金流策略:")
    print(f"   • 特点: 低收益低风险，真正多空平衡")
    print(f"   • 优势: 风险最低，多空完全平衡")
    print(f"   • 劣势: 收益率过低，不适合财富增长")
    print(f"   • 适用: 极度保守的投资者，风险对冲")
    
    print(f"\n   🎯 1-X策略:")
    print(f"   • 特点: 中低收益，逆向逻辑")
    print(f"   • 优势: 有较多看跌交易，风险适中")
    print(f"   • 劣势: 收益率偏低，逻辑需要适应")
    print(f"   • 适用: 稳健型投资者")
    
    # X定义的影响分析
    print(f"\n💡 X定义对策略的影响分析:")
    
    print(f"\n   📊 X定义演变:")
    print(f"   1. 原始定义: X = 1-Y (数学关系)")
    print(f"   2. 散户买升: X = 散户买升概率 (心理学)")
    print(f"   3. 散户买跌: X = 散户买跌概率 (逆向心理学)")
    
    print(f"\n   🎯 不同X定义的信号覆盖:")
    print(f"   • 原始XY: Y>0.43且X>0.43 → 覆盖99.7%时间")
    print(f"   • 散户情绪: 复杂逻辑 → 覆盖37.8%时间")
    print(f"   • 资金流: 复杂逻辑 → 覆盖9.6%时间")
    print(f"   • 1-X策略: Y>0.43且X>0.43 → 覆盖39.5%时间")
    
    print(f"\n   💡 关键发现:")
    print(f"   • 原始定义信号最多，收益最高")
    print(f"   • 散户定义实现了一定平衡")
    print(f"   • 资金流定义最保守，平衡最好")
    print(f"   • 1-X定义提供了新的视角")
    
    # 1-X策略深度分析
    print(f"\n🔍 1-X策略深度分析:")
    
    print(f"\n   💡 策略逻辑解读:")
    print(f"   • Y>0.43: 控股商控制强势")
    print(f"   • X>0.43: 散户大量买跌 (X=1-散户买升概率)")
    print(f"   • 组合含义: 控股商强势且散户悲观时做多")
    print(f"   • 逆向逻辑: 散户悲观往往是底部信号")
    
    print(f"\n   📊 信号分布特征:")
    print(f"   • 信号天数: 3,462天 (39.5%)")
    print(f"   • Y值范围: 0.5207 ~ 0.5371 (控股商稳定控制)")
    print(f"   • X值范围: 0.4300 ~ 0.9415 (散户买跌程度不同)")
    print(f"   • 信号质量: 中等频率，适度分散")
    
    print(f"\n   🎯 交易特征:")
    print(f"   • 看涨主导: 83.8%看涨交易")
    print(f"   • 看跌补充: 16.2%看跌交易")
    print(f"   • 看跌胜率: 61.7% (优秀)")
    print(f"   • 平均亏损: 看跌交易平均亏损230港元")
    
    # 策略组合建议
    print(f"\n🚀 策略组合建议:")
    
    print(f"\n   💰 不同风险偏好的配置:")
    
    print(f"\n   🔥 激进型 (追求高收益):")
    print(f"   • 80%原始XY + 20%散户情绪")
    print(f"   • 预期年化: 4.99%")
    print(f"   • 预期回撤: 9.09%")
    print(f"   • 特点: 高收益，适度平衡")
    
    print(f"\n   ⚖️ 平衡型 (收益风险平衡):")
    print(f"   • 50%原始XY + 30%散户情绪 + 20%1-X")
    print(f"   • 预期年化: 3.99%")
    print(f"   • 预期回撤: 8.30%")
    print(f"   • 特点: 平衡收益风险，多元化")
    
    print(f"\n   🛡️ 稳健型 (控制风险):")
    print(f"   • 40%原始XY + 30%1-X + 30%资金流")
    print(f"   • 预期年化: 2.86%")
    print(f"   • 预期回撤: 6.90%")
    print(f"   • 特点: 低风险，多空平衡")
    
    print(f"\n   🏦 保守型 (资本保值):")
    print(f"   • 50%资金流 + 50%1-X")
    print(f"   • 预期年化: 1.00%")
    print(f"   • 预期回撤: 5.20%")
    print(f"   • 特点: 极低风险，资本保值")
    
    # 实盘应用指南
    print(f"\n📋 实盘应用指南:")
    
    print(f"\n   🎯 策略选择指南:")
    print(f"   • 如果追求高收益: 选择原始XY策略")
    print(f"   • 如果要多空平衡: 选择资金流策略")
    print(f"   • 如果要适中风险: 选择1-X策略")
    print(f"   • 如果要心理优势: 选择散户情绪策略")
    
    print(f"\n   📊 执行建议:")
    print(f"   • 分阶段测试: 先小资金测试3-6个月")
    print(f"   • 组合使用: 不同策略分配不同比例资金")
    print(f"   • 动态调整: 根据市场环境调整权重")
    print(f"   • 严格纪律: 按照回测参数严格执行")
    
    print(f"\n   🔄 动态切换:")
    print(f"   • 牛市: 增加原始XY策略权重")
    print(f"   • 熊市: 增加资金流和1-X策略权重")
    print(f"   • 震荡市: 平衡配置所有策略")
    print(f"   • 不确定期: 偏向保守策略")
    
    # 风险提示
    print(f"\n⚠️ 重要风险提示:")
    
    print(f"\n   📊 策略风险:")
    print(f"   • 历史表现不代表未来收益")
    print(f"   • 市场环境变化可能影响策略有效性")
    print(f"   • 需要持续监控和调整")
    print(f"   • 不同策略适合不同市场环境")
    
    print(f"\n   💰 资金管理:")
    print(f"   • 不要投入超过承受能力的资金")
    print(f"   • 保持充足的现金储备")
    print(f"   • 分散投资，避免过度集中")
    print(f"   • 定期评估和再平衡")
    
    # 总结
    print(f"\n🎉 总结:")
    
    print(f"\n   🎯 核心发现:")
    print(f"   • 原始XY策略收益最高，但缺乏平衡")
    print(f"   • 资金流策略风险最低，实现真正多空平衡")
    print(f"   • 1-X策略提供了新的逆向思维角度")
    print(f"   • 散户情绪策略在收益风险间取得平衡")
    
    print(f"\n   📊 最佳实践:")
    print(f"   • 单一策略: 原始XY策略表现最优")
    print(f"   • 组合策略: 多策略组合可以优化风险收益")
    print(f"   • 风险对冲: 资金流策略是优秀的对冲工具")
    print(f"   • 创新思维: 1-X策略展示了重新定义的价值")
    
    print(f"\n   💡 投资哲学:")
    print(f"   基于35年8,772条记录的验证，不同的X定义")
    print(f"   带来了不同的投资视角和风险收益特征。")
    print(f"   投资者应根据自身风险偏好和投资目标，")
    print(f"   选择合适的策略或策略组合。")

def calculate_portfolio_performance():
    """计算投资组合表现"""
    
    print(f"\n📊 推荐投资组合表现预估:")
    print(f"=" * 50)
    
    # 各策略数据
    strategies = {
        '原始XY': {'return': 0.0541, 'drawdown': 0.0932, 'final': 3009108},
        '散户情绪': {'return': 0.0330, 'drawdown': 0.0819, 'final': 1468258},
        '资金流': {'return': 0.0025, 'drawdown': 0.0376, 'final': 477847},
        '1-X': {'return': 0.0174, 'drawdown': 0.0664, 'final': 856017}
    }
    
    # 推荐组合
    portfolios = [
        ('激进型', {'原始XY': 0.8, '散户情绪': 0.2}),
        ('平衡型', {'原始XY': 0.5, '散户情绪': 0.3, '1-X': 0.2}),
        ('稳健型', {'原始XY': 0.4, '1-X': 0.3, '资金流': 0.3}),
        ('保守型', {'资金流': 0.5, '1-X': 0.5})
    ]
    
    print(f"   组合类型 | 年化收益 | 最大回撤 | 最终资金 | 风险调整收益")
    print(f"   " + "-" * 65)
    
    for portfolio_name, weights in portfolios:
        portfolio_return = sum(strategies[strategy]['return'] * weight 
                             for strategy, weight in weights.items())
        portfolio_drawdown = sum(strategies[strategy]['drawdown'] * weight 
                               for strategy, weight in weights.items())
        portfolio_final = sum(strategies[strategy]['final'] * weight 
                            for strategy, weight in weights.items())
        risk_adj_return = portfolio_return / portfolio_drawdown
        
        print(f"   {portfolio_name:8s} | {portfolio_return*100:7.2f}% | {portfolio_drawdown*100:7.2f}% | {portfolio_final:8,.0f} | {risk_adj_return:12.2f}")

def main():
    """主函数"""
    analyze_all_strategies_comparison()
    calculate_portfolio_performance()
    
    print(f"\n🎉 所有策略对比分析完成！")
    print(f"   关键结论: 不同的X定义创造了不同的投资机会，")
    print(f"   投资者可以根据自身需求选择合适的策略组合。")

if __name__ == "__main__":
    main()
