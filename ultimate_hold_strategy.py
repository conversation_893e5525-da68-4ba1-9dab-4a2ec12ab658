#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
终极持仓不动策略
==============

核心理念：持仓不动是王道
- 最大化持仓不动时间
- 最小化交易频率
- 只在绝对必要时才交易
- 严格的持仓不动纪律

策略原则：
1. 一旦建仓，尽可能长时间持有
2. 只有在策略方向完全错误时才换仓
3. 控股商控制区和其他区域完全不交易
4. 用最简单的逻辑，最少的交易

作者: Cosmoon NG
日期: 2025年7月
"""

import pymysql
from datetime import datetime

def ultimate_hold_strategy():
    """终极持仓不动策略"""
    db_config = {
        'host': 'localhost',
        'port': 3306,
        'database': 'finance',
        'user': 'root',
        'password': '12345678',
        'charset': 'utf8mb4'
    }
    
    try:
        connection = pymysql.connect(**db_config)
        cursor = connection.cursor()
        
        print("🎯 终极持仓不动策略")
        print("="*80)
        print("💎 核心理念：持仓不动是王道")
        print("🔑 策略原则:")
        print("   • 最大化持仓不动时间 (目标: >80%)")
        print("   • 最小化交易频率 (目标: <20次)")
        print("   • 只在绝对必要时才交易")
        print("   • 严格的持仓不动纪律")
        print("📊 交易规则:")
        print("   • 高值盈利区: 持多仓或买涨，然后持仓不动")
        print("   • 强亏损区: 持空仓或买跌，然后持仓不动")
        print("   • 控股商控制区: 完全持仓不动")
        print("   • 其他区域: 完全持仓不动")
        print("   • 换仓条件: 只有持仓方向与区域完全相反时")
        print("="*80)
        
        # 1. 获取所有记录
        cursor.execute("""
            SELECT 交易序号, 开仓日期, close, `控制系数`, `资金流比例`,
                   CASE 
                       WHEN `控制系数` > 0.4 AND `资金流比例` > 0.4 THEN '高值盈利区'
                       WHEN `控制系数` > 0.333 AND `控制系数` < 0.4 THEN '控股商控制区'
                       WHEN (`控制系数` < 0.25 OR `资金流比例` < 0.25) AND NOT (`控制系数` > 0.333 AND `控制系数` < 0.4) THEN '强亏损区'
                       ELSE '其他区域'
                   END AS 策略区域
            FROM test 
            ORDER BY 交易序号
        """)
        
        records = cursor.fetchall()
        
        # 2. 执行终极持仓不动策略
        print("\n📊 终极持仓不动策略执行过程:")
        
        initial_capital = 30000
        current_capital = initial_capital
        
        # 持仓状态跟踪
        position_type = 'cash'  # 'cash', 'long', 'short'
        position_shares = 0
        position_entry_price = 0
        position_entry_date = None
        position_entry_zone = None
        position_hold_days = 0
        
        # 交易记录
        trades = []
        daily_positions = []
        
        # 统计变量
        total_trades = 0
        hold_periods = 0
        position_changes = 0
        
        print("-" * 150)
        print(f"{'序号':<4} {'策略区域':<12} {'价格':<8} {'当前仓位':<15} {'持仓天数':<8} "
              f"{'决策':<15} {'新仓位':<15} {'原因':<25}")
        print("-" * 150)
        
        # 3. 逐日执行终极持仓不动策略
        for i, record in enumerate(records):
            trade_id, date, price, y_val, x_val, zone = record
            price = float(price)
            
            # 更新持仓天数
            if position_type != 'cash':
                position_hold_days += 1
            
            # 确定是否需要交易 (极其严格的条件)
            action, reason = determine_ultimate_hold_action(position_type, zone, position_hold_days)
            
            # 执行决策
            capital_change = 0
            new_position_type = position_type
            new_position_shares = position_shares
            trade_shares = 0
            
            if action == 'hold':
                hold_periods += 1
                # 持仓不动是默认行为
            
            elif action == 'initial_long' and position_type == 'cash':
                # 首次建立多仓 (只在高值盈利区且空仓时)
                trade_shares = 100
                cost = trade_shares * price
                transaction_cost = cost * 0.0025
                
                if current_capital >= cost + transaction_cost:
                    current_capital -= (cost + transaction_cost)
                    capital_change = -(cost + transaction_cost)
                    
                    new_position_type = 'long'
                    new_position_shares = trade_shares
                    position_entry_price = price
                    position_entry_date = date
                    position_entry_zone = zone
                    position_hold_days = 0
                    
                    total_trades += 1
                    position_changes += 1
                    
                    trades.append({
                        'date': date,
                        'trade_id': trade_id,
                        'zone': zone,
                        'action': 'initial_long',
                        'price': price,
                        'shares': trade_shares,
                        'capital_change': capital_change,
                        'total_capital': current_capital,
                        'reason': reason
                    })
            
            elif action == 'initial_short' and position_type == 'cash':
                # 首次建立空仓 (只在强亏损区且空仓时)
                trade_shares = 100
                transaction_cost = trade_shares * price * 0.0025
                
                current_capital -= transaction_cost
                capital_change = -transaction_cost
                
                new_position_type = 'short'
                new_position_shares = trade_shares
                position_entry_price = price
                position_entry_date = date
                position_entry_zone = zone
                position_hold_days = 0
                
                total_trades += 1
                position_changes += 1
                
                trades.append({
                    'date': date,
                    'trade_id': trade_id,
                    'zone': zone,
                    'action': 'initial_short',
                    'price': price,
                    'shares': trade_shares,
                    'capital_change': capital_change,
                    'total_capital': current_capital,
                    'reason': reason
                })
            
            elif action == 'forced_switch' and position_type != 'cash':
                # 强制换仓 (只在方向完全错误且持仓时间足够长时)
                if position_type == 'long':
                    # 平多仓
                    close_value = position_shares * price
                    close_pnl = (price - position_entry_price) * position_shares
                    close_cost = position_shares * position_entry_price * 0.0025
                    
                    # 建空仓
                    trade_shares = 100
                    open_cost = trade_shares * price * 0.0025
                    
                    total_cost = close_cost + open_cost
                    current_capital += close_value + close_pnl - total_cost
                    capital_change = close_pnl - total_cost
                    
                    new_position_type = 'short'
                    new_position_shares = trade_shares
                    
                elif position_type == 'short':
                    # 平空仓
                    close_pnl = (position_entry_price - price) * position_shares
                    close_cost = position_shares * position_entry_price * 0.0025
                    
                    # 建多仓
                    trade_shares = 100
                    open_cost = trade_shares * price
                    open_transaction_cost = open_cost * 0.0025
                    
                    total_cost = close_cost + open_cost + open_transaction_cost
                    current_capital += close_pnl - total_cost
                    capital_change = close_pnl - total_cost
                    
                    new_position_type = 'long'
                    new_position_shares = trade_shares
                
                position_entry_price = price
                position_entry_date = date
                position_entry_zone = zone
                position_hold_days = 0
                
                total_trades += 2  # 平仓 + 开仓
                position_changes += 1
                
                trades.append({
                    'date': date,
                    'trade_id': trade_id,
                    'zone': zone,
                    'action': 'forced_switch',
                    'price': price,
                    'shares': trade_shares,
                    'capital_change': capital_change,
                    'total_capital': current_capital,
                    'reason': reason
                })
            
            # 更新持仓状态
            position_type = new_position_type
            position_shares = new_position_shares
            
            # 记录每日状态
            daily_positions.append({
                'date': date,
                'zone': zone,
                'price': price,
                'position_type': position_type,
                'position_shares': position_shares,
                'hold_days': position_hold_days,
                'action': action
            })
            
            # 显示前50条记录
            if i < 50:
                position_display = f"{position_type}({position_shares})" if position_type != 'cash' else 'cash'
                new_position_display = f"{new_position_type}({new_position_shares})" if new_position_type != 'cash' else 'cash'
                
                print(f"{trade_id:<4} {zone:<12} {price:<8.2f} {position_display:<15} {position_hold_days:<8} "
                      f"{action:<15} {new_position_display:<15} {reason:<25}")
        
        print("\n" + "="*100)
        
        # 4. 最终平仓
        if position_type != 'cash':
            final_price = float(records[-1][2])
            if position_type == 'long':
                final_value = position_shares * final_price
                final_pnl = (final_price - position_entry_price) * position_shares
                current_capital += final_value + final_pnl
            else:  # short
                final_pnl = (position_entry_price - final_price) * position_shares
                current_capital += final_pnl
            
            final_cost = position_shares * position_entry_price * 0.0025
            current_capital -= final_cost
            
            print(f"📊 最终平仓: {position_type}仓位，持仓{position_hold_days}天，盈亏{final_pnl-final_cost:+.0f}港币")
        
        # 5. 终极持仓不动策略结果
        print(f"\n🏆 终极持仓不动策略结果:")
        print(f"   • 初始资金: {initial_capital:,}港币")
        print(f"   • 最终资金: {current_capital:,.0f}港币")
        print(f"   • 总收益: {current_capital - initial_capital:+,.0f}港币")
        print(f"   • 总收益率: {(current_capital/initial_capital-1)*100:+.2f}%")
        
        print(f"\n💎 持仓不动统计:")
        print(f"   • 总记录数: {len(records)}")
        print(f"   • 持仓不动次数: {hold_periods}")
        print(f"   • 实际交易次数: {total_trades}")
        print(f"   • 仓位变化次数: {position_changes}")
        print(f"   • 持仓不动比例: {hold_periods/len(records)*100:.1f}%")
        print(f"   • 交易频率: {total_trades/len(records)*100:.1f}%")
        
        # 6. 持仓时间分析
        if trades:
            print(f"\n📊 持仓时间分析:")
            print(f"   • 总交易次数: {len(trades)}")
            
            # 计算平均持仓时间
            hold_periods_list = []
            current_hold = 0
            for pos in daily_positions:
                if pos['action'] == 'hold':
                    current_hold += 1
                else:
                    if current_hold > 0:
                        hold_periods_list.append(current_hold)
                    current_hold = 0
            
            if hold_periods_list:
                avg_hold_period = sum(hold_periods_list) / len(hold_periods_list)
                max_hold_period = max(hold_periods_list)
                print(f"   • 平均持仓时间: {avg_hold_period:.1f}天")
                print(f"   • 最长持仓时间: {max_hold_period}天")
        
        # 7. 按策略区域分析持仓行为
        print(f"\n📊 按策略区域分析持仓行为:")
        print("-" * 80)
        
        zones = ['高值盈利区', '强亏损区', '其他区域', '控股商控制区']
        
        for zone_name in zones:
            zone_records = [r for r in daily_positions if r['zone'] == zone_name]
            if zone_records:
                zone_count = len(zone_records)
                zone_holds = len([r for r in zone_records if r['action'] == 'hold'])
                zone_trades = zone_count - zone_holds
                
                print(f"• {zone_name}:")
                print(f"  - 出现次数: {zone_count}")
                print(f"  - 持仓不动: {zone_holds}次 ({zone_holds/zone_count*100:.1f}%)")
                print(f"  - 交易操作: {zone_trades}次 ({zone_trades/zone_count*100:.1f}%)")
        
        # 8. 交易记录详情
        if trades:
            print(f"\n📋 实际交易记录:")
            print("-" * 120)
            print(f"{'日期':<12} {'策略区域':<12} {'操作':<15} {'价格':<8} {'盈亏':<10} {'原因':<25}")
            print("-" * 120)
            
            for trade in trades:
                print(f"{str(trade['date']):<12} {trade['zone']:<12} {trade['action']:<15} "
                      f"{trade['price']:<8.2f} {trade['capital_change']:<10.0f} {trade['reason']:<25}")
        
        connection.close()
        print(f"\n🎉 终极持仓不动策略完成!")
        print(f"💎 持仓不动是投资的最高境界！")
        
    except Exception as e:
        print(f"❌ 终极持仓不动策略失败: {e}")

def determine_ultimate_hold_action(current_position, zone, hold_days):
    """
    终极持仓不动决策逻辑
    
    原则：能不交易就不交易，能持仓就持仓
    """
    
    # 默认行为：持仓不动
    if current_position != 'cash':
        # 已有持仓，检查是否需要强制换仓
        
        if zone == '高值盈利区' and current_position == 'short' and hold_days >= 5:
            # 持空仓但在高值盈利区超过5天，强制换多仓
            return 'forced_switch', f'空仓在高值盈利区{hold_days}天，强制转多仓'
        
        elif zone == '强亏损区' and current_position == 'long' and hold_days >= 5:
            # 持多仓但在强亏损区超过5天，强制换空仓
            return 'forced_switch', f'多仓在强亏损区{hold_days}天，强制转空仓'
        
        else:
            # 其他所有情况：持仓不动
            return 'hold', f'持仓不动第{hold_days}天'
    
    else:
        # 空仓状态，只在明确区域建仓
        if zone == '高值盈利区':
            return 'initial_long', '高值盈利区首次建多仓'
        elif zone == '强亏损区':
            return 'initial_short', '强亏损区首次建空仓'
        else:
            # 控股商控制区和其他区域：保持空仓
            return 'hold', f'{zone}保持空仓'

if __name__ == "__main__":
    ultimate_hold_strategy()
