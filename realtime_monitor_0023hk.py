#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
0023.HK实时价格监控系统
========================

实时监控东亚银行(0023.HK)的市场价格变化
支持on-tick级别的价格更新和技术指标计算

特点:
1. 实时价格监控
2. 价格变化提醒
3. 技术指标实时计算
4. 交易信号提醒
5. 数据记录到数据库

作者: Cosmoon NG
"""

import yfinance as yf
import pandas as pd
import numpy as np
import mysql.connector
import time
import threading
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

class RealtimeMonitor0023HK:
    """0023.HK实时监控器"""
    
    def __init__(self):
        self.symbol = "0023.HK"
        self.company_name = "东亚银行"
        
        # MySQL配置
        self.mysql_config = {
            'host': 'localhost',
            'port': 3306,
            'user': 'root',
            'password': '12345678',
            'database': 'finance'
        }
        
        # 监控参数
        self.update_interval = 5  # 更新间隔(秒)
        self.price_change_threshold = 0.01  # 价格变化提醒阈值(1%)
        
        # 当前状态
        self.current_price = 0
        self.previous_price = 0
        self.is_monitoring = False
        self.start_time = None
        
        # 历史数据缓存
        self.price_history = []
        self.max_history = 100  # 保留最近100个价格点
        
        print(f"🎯 {self.symbol} ({self.company_name}) 实时监控系统")
        print(f"⏱️ 更新间隔: {self.update_interval}秒")
        print(f"📊 价格变化提醒阈值: {self.price_change_threshold*100:.1f}%")

    def get_realtime_price(self):
        """获取实时价格"""
        try:
            ticker = yf.Ticker(self.symbol)
            
            # 获取实时数据
            info = ticker.info
            current_price = info.get('currentPrice') or info.get('regularMarketPrice')
            
            if current_price is None:
                # 如果无法获取实时价格，使用最新历史数据
                hist = ticker.history(period="1d", interval="1m")
                if not hist.empty:
                    current_price = hist['Close'].iloc[-1]
            
            if current_price:
                # 获取更多市场信息
                market_data = {
                    'price': float(current_price),
                    'timestamp': datetime.now(),
                    'open': info.get('regularMarketOpen', 0),
                    'high': info.get('dayHigh', 0),
                    'low': info.get('dayLow', 0),
                    'volume': info.get('regularMarketVolume', 0),
                    'previous_close': info.get('regularMarketPreviousClose', 0),
                    'market_cap': info.get('marketCap', 0),
                    'pe_ratio': info.get('trailingPE', 0)
                }
                
                return market_data
            
        except Exception as e:
            print(f"❌ 获取价格失败: {e}")
            
        return None

    def calculate_technical_indicators(self):
        """基于价格历史计算技术指标"""
        if len(self.price_history) < 20:
            return None
        
        try:
            # 转换为DataFrame
            df = pd.DataFrame(self.price_history)
            df['price'] = df['price'].astype(float)
            
            # 计算移动平均
            df['ma5'] = df['price'].rolling(5).mean()
            df['ma10'] = df['price'].rolling(10).mean()
            df['ma20'] = df['price'].rolling(20).mean()
            
            # 计算RSI
            delta = df['price'].diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
            rs = gain / (loss + 1e-10)
            df['rsi'] = 100 - (100 / (1 + rs))
            
            # 计算布林带
            df['bb_middle'] = df['price'].rolling(20).mean()
            bb_std = df['price'].rolling(20).std()
            df['bb_upper'] = df['bb_middle'] + (bb_std * 2)
            df['bb_lower'] = df['bb_middle'] - (bb_std * 2)
            
            # 获取最新指标
            latest = df.iloc[-1]
            
            return {
                'ma5': latest.get('ma5', 0),
                'ma10': latest.get('ma10', 0),
                'ma20': latest.get('ma20', 0),
                'rsi': latest.get('rsi', 50),
                'bb_upper': latest.get('bb_upper', 0),
                'bb_middle': latest.get('bb_middle', 0),
                'bb_lower': latest.get('bb_lower', 0)
            }
            
        except Exception as e:
            print(f"❌ 技术指标计算失败: {e}")
            return None

    def generate_trading_signal(self, price, indicators):
        """生成交易信号"""
        if not indicators:
            return "数据不足", "⚪"
        
        rsi = indicators['rsi']
        ma5 = indicators['ma5']
        ma20 = indicators['ma20']
        bb_upper = indicators['bb_upper']
        bb_lower = indicators['bb_lower']
        
        signals = []
        
        # RSI信号
        if rsi > 70:
            signals.append("RSI超买")
        elif rsi < 30:
            signals.append("RSI超卖")
        
        # 均线信号
        if ma5 > ma20:
            signals.append("短期上涨")
        elif ma5 < ma20:
            signals.append("短期下跌")
        
        # 布林带信号
        if price > bb_upper:
            signals.append("突破上轨")
        elif price < bb_lower:
            signals.append("跌破下轨")
        
        if not signals:
            return "观望", "⚪"
        elif any("超卖" in s or "跌破" in s for s in signals):
            return f"买入信号: {', '.join(signals)}", "🟢"
        elif any("超买" in s or "突破" in s for s in signals):
            return f"卖出信号: {', '.join(signals)}", "🔴"
        else:
            return f"关注: {', '.join(signals)}", "🟡"

    def check_price_alert(self, current_price):
        """检查价格提醒"""
        if self.previous_price == 0:
            return None
        
        change_pct = (current_price - self.previous_price) / self.previous_price
        
        if abs(change_pct) >= self.price_change_threshold:
            direction = "上涨" if change_pct > 0 else "下跌"
            return {
                'type': 'price_alert',
                'direction': direction,
                'change_pct': change_pct * 100,
                'from_price': self.previous_price,
                'to_price': current_price
            }
        
        return None

    def save_to_database(self, market_data, indicators, signal):
        """保存数据到数据库"""
        try:
            connection = mysql.connector.connect(**self.mysql_config)
            cursor = connection.cursor()
            
            # 插入实时监控记录
            insert_sql = """
            INSERT INTO realtime_monitor_0023hk (
                timestamp, price, open_price, high_price, low_price, volume,
                previous_close, ma5, ma10, ma20, rsi, bb_upper, bb_middle, bb_lower,
                trading_signal, created_at
            ) VALUES (
                %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, NOW()
            )
            """
            
            cursor.execute(insert_sql, (
                market_data['timestamp'], market_data['price'], market_data['open'],
                market_data['high'], market_data['low'], market_data['volume'],
                market_data['previous_close'],
                indicators.get('ma5', 0) if indicators else 0,
                indicators.get('ma10', 0) if indicators else 0,
                indicators.get('ma20', 0) if indicators else 0,
                indicators.get('rsi', 50) if indicators else 50,
                indicators.get('bb_upper', 0) if indicators else 0,
                indicators.get('bb_middle', 0) if indicators else 0,
                indicators.get('bb_lower', 0) if indicators else 0,
                signal[0]
            ))
            
            connection.commit()
            cursor.close()
            connection.close()
            
        except Exception as e:
            print(f"⚠️ 数据库保存失败: {e}")

    def display_market_info(self, market_data, indicators, signal, alert):
        """显示市场信息"""
        timestamp = market_data['timestamp'].strftime('%H:%M:%S')
        price = market_data['price']
        
        # 计算涨跌
        if market_data['previous_close'] > 0:
            change = price - market_data['previous_close']
            change_pct = (change / market_data['previous_close']) * 100
            change_str = f"{change:+.4f} ({change_pct:+.2f}%)"
            change_color = "🟢" if change > 0 else "🔴" if change < 0 else "⚪"
        else:
            change_str = "N/A"
            change_color = "⚪"
        
        print(f"\n📊 {timestamp} | {self.symbol} {self.company_name}")
        print(f"💰 当前价格: {price:.4f} 港元 {change_color} {change_str}")
        
        if market_data['open'] > 0:
            print(f"📈 今日: 开盘 {market_data['open']:.4f} | 最高 {market_data['high']:.4f} | 最低 {market_data['low']:.4f}")
        
        if market_data['volume'] > 0:
            print(f"📊 成交量: {market_data['volume']:,}")
        
        # 显示技术指标
        if indicators:
            print(f"🧮 技术指标:")
            print(f"   MA5: {indicators['ma5']:.4f} | MA10: {indicators['ma10']:.4f} | MA20: {indicators['ma20']:.4f}")
            print(f"   RSI: {indicators['rsi']:.2f}")
            print(f"   布林带: {indicators['bb_lower']:.4f} - {indicators['bb_middle']:.4f} - {indicators['bb_upper']:.4f}")
        
        # 显示交易信号
        print(f"🎯 交易信号: {signal[1]} {signal[0]}")
        
        # 显示价格提醒
        if alert:
            print(f"⚠️ 价格提醒: {alert['direction']} {abs(alert['change_pct']):.2f}% ({alert['from_price']:.4f} → {alert['to_price']:.4f})")

    def start_monitoring(self):
        """开始监控"""
        print(f"\n🚀 开始监控 {self.symbol} 实时价格...")
        print("按 Ctrl+C 停止监控")
        print("=" * 60)
        
        self.is_monitoring = True
        self.start_time = datetime.now()
        
        try:
            while self.is_monitoring:
                # 获取实时价格
                market_data = self.get_realtime_price()
                
                if market_data:
                    current_price = market_data['price']
                    
                    # 更新价格历史
                    self.price_history.append({
                        'price': current_price,
                        'timestamp': market_data['timestamp']
                    })
                    
                    # 保持历史数据大小
                    if len(self.price_history) > self.max_history:
                        self.price_history.pop(0)
                    
                    # 计算技术指标
                    indicators = self.calculate_technical_indicators()
                    
                    # 生成交易信号
                    signal = self.generate_trading_signal(current_price, indicators)
                    
                    # 检查价格提醒
                    alert = self.check_price_alert(current_price)
                    
                    # 显示信息
                    self.display_market_info(market_data, indicators, signal, alert)
                    
                    # 保存到数据库
                    self.save_to_database(market_data, indicators, signal)
                    
                    # 更新价格
                    self.previous_price = self.current_price
                    self.current_price = current_price
                
                else:
                    print(f"⚠️ {datetime.now().strftime('%H:%M:%S')} 无法获取价格数据")
                
                # 等待下次更新
                time.sleep(self.update_interval)
                
        except KeyboardInterrupt:
            print(f"\n⏹️ 监控已停止")
            self.stop_monitoring()
        except Exception as e:
            print(f"\n❌ 监控出错: {e}")
            self.stop_monitoring()

    def stop_monitoring(self):
        """停止监控"""
        self.is_monitoring = False
        if self.start_time:
            duration = datetime.now() - self.start_time
            print(f"📊 监控时长: {duration}")
            print(f"📈 价格数据点: {len(self.price_history)}")

def main():
    """主函数"""
    print("🎯 0023.HK 实时价格监控系统")
    print("=" * 50)
    
    monitor = RealtimeMonitor0023HK()
    
    try:
        monitor.start_monitoring()
    except Exception as e:
        print(f"❌ 程序启动失败: {e}")

if __name__ == "__main__":
    main()
