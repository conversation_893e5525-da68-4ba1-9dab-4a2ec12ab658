#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
HSI50回归线策略回测系统 - 按照成功思路重新实现
===============================================

核心思路：
1. 使用线性回归线判断长期趋势
2. 价格相对回归线位置决定交易方向
3. XY值确认信号强度
4. 不对称止盈止损设计
5. 每月定投资金管理

作者: Cosmoon NG (重新实现版)
日期: 2025年7月
"""

import pandas as pd
import yfinance as yf
import numpy as np
import matplotlib.pyplot as plt
from scipy import stats
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

class HSI50RegressionStrategy:
    def __init__(self):
        """初始化回归线策略回测系统"""
        self.symbol = "^HSI"  # 恒生指数
        self.initial_capital = 30000  # 初始资金30K
        self.monthly_addition = 2000  # 每月追加2K (按您的设置)
        
        # 不对称止盈止损设计 (按您的成功参数)
        self.take_profit_long = 0.016   # 多头止盈 1.6%
        self.stop_loss_long = 0.008     # 多头止损 0.8%
        self.take_profit_short = 0.008  # 空头止盈 0.8%
        self.stop_loss_short = 0.016    # 空头止损 1.6%
        
        # 交易状态
        self.position = 0  # 0=空仓, 1=多头, -1=空头
        self.entry_price = 0
        self.data = None
        
        # 记录
        self.trades = []
        self.equity_curve = []
        self.last_month = None
    
    def fetch_hsi_data(self):
        """获取恒生指数25年历史数据"""
        print("📈 获取恒生指数25年历史数据...")
        
        try:
            # 获取25年数据 (按您的数据范围)
            end_date = datetime.now()
            start_date = end_date - timedelta(days=25*365)
            
            ticker = yf.Ticker(self.symbol)
            self.data = ticker.history(start=start_date, end=end_date)
            
            if self.data.empty:
                print("❌ 数据获取失败：返回空数据")
                return False
            
            self.data.reset_index(inplace=True)
            self.data.columns = [col.lower() for col in self.data.columns]
            
            print(f"✅ 成功获取恒生指数数据:")
            print(f"   • 数据期间: {self.data['date'].min().strftime('%Y-%m-%d')} 至 {self.data['date'].max().strftime('%Y-%m-%d')}")
            print(f"   • 总记录数: {len(self.data):,} 天")
            
            return True
            
        except Exception as e:
            print(f"❌ 获取数据失败: {e}")
            return False
    
    def calculate_indicators(self):
        """计算技术指标 - 按您的成功思路"""
        print("📊 计算回归线和技术指标...")
        
        # 1. 计算线性回归线 (核心策略)
        self.data['i'] = range(1, len(self.data) + 1)
        
        # 线性回归计算
        slope, intercept, r_value, p_value, std_err = stats.linregress(
            self.data['i'], self.data['close']
        )
        
        # 回归线
        self.data['regression_line'] = intercept + slope * self.data['i']
        
        # 价格相对回归线的位置 (关键指标)
        self.data['price_position'] = (self.data['close'] - self.data['regression_line']) / self.data['regression_line']
        
        print(f"✅ 回归线计算完成 (R² = {r_value**2:.4f})")
        
        # 2. 计算XY值 (信号确认)
        # RSI作为Y值基础
        delta = self.data['close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
        rs = gain / loss
        self.data['rsi'] = 100 - (100 / (1 + rs))
        
        # Y值：基于RSI和价格动量
        self.data['price_momentum'] = self.data['close'].pct_change(10)
        self.data['y_value'] = (self.data['rsi'] / 100 + 
                               np.tanh(self.data['price_momentum'] * 5) + 1) / 2
        self.data['y_value'] = np.clip(self.data['y_value'], 0.1, 0.9)
        
        # X值：基于成交量和价格关系
        self.data['volume_ma'] = self.data['volume'].rolling(window=20).mean()
        self.data['volume_ratio'] = self.data['volume'] / self.data['volume_ma']
        
        # 资金流计算
        price_change = (self.data['close'] - self.data['open']) / self.data['open']
        money_flow = self.data['volume'] * price_change
        
        def calc_inflow_ratio(flows):
            if len(flows) == 0 or flows.isna().all():
                return 0.5
            flows = flows.dropna()
            if len(flows) == 0:
                return 0.5
            
            inflows = flows[flows > 0].sum()
            outflows = abs(flows[flows < 0].sum())
            total_flow = inflows + outflows
            
            return inflows / total_flow if total_flow > 0 else 0.5
        
        self.data['x_value'] = money_flow.rolling(window=20).apply(calc_inflow_ratio, raw=False)
        self.data['x_value'] = np.clip(self.data['x_value'], 0.1, 0.9)
        self.data['x_value'].fillna(0.5, inplace=True)
        
        # E值计算
        self.data['e_value'] = (8 * self.data['x_value'] * self.data['y_value'] - 
                               3 * self.data['x_value'] - 3 * self.data['y_value'] + 1)
        
        print("✅ 技术指标计算完成")
    
    def add_monthly_capital(self, date, capital):
        """每月增加资金 - 按您的逻辑"""
        current_month = date.replace(day=1)
        
        if self.last_month is None or current_month > self.last_month:
            self.last_month = current_month
            return capital + self.monthly_addition
        
        return capital
    
    def check_exit_conditions(self, current_price, high_price, low_price):
        """检查止盈止损条件"""
        if self.position == 0:
            return False, 0, ''
        
        if self.position == 1:  # 多头
            # 止盈检查
            profit_ratio = (high_price - self.entry_price) / self.entry_price
            if profit_ratio >= self.take_profit_long:
                exit_price = self.entry_price * (1 + self.take_profit_long)
                return True, exit_price, 'long_tp'
            
            # 止损检查
            loss_ratio = (self.entry_price - low_price) / self.entry_price
            if loss_ratio >= self.stop_loss_long:
                exit_price = self.entry_price * (1 - self.stop_loss_long)
                return True, exit_price, 'long_sl'
        
        elif self.position == -1:  # 空头
            # 止盈检查
            profit_ratio = (self.entry_price - low_price) / self.entry_price
            if profit_ratio >= self.take_profit_short:
                exit_price = self.entry_price * (1 - self.take_profit_short)
                return True, exit_price, 'short_tp'
            
            # 止损检查
            loss_ratio = (high_price - self.entry_price) / self.entry_price
            if loss_ratio >= self.stop_loss_short:
                exit_price = self.entry_price * (1 + self.stop_loss_short)
                return True, exit_price, 'short_sl'
        
        return False, 0, ''
    
    def get_trading_signal(self, row):
        """获取交易信号 - 按您的成功逻辑"""
        # 核心条件：E值 > 0 且 XY值都 > 0.45 且价格低于回归线 → 买涨
        if (row['e_value'] > 0 and 
            row['x_value'] > 0.45 and 
            row['y_value'] > 0.45 and 
            row['price_position'] < 0):  # 价格低于回归线
            return 'LONG'
        
        # 核心条件：Y < 0.25 或 X < 0.25 且价格高于回归线 → 买跌
        elif ((row['y_value'] < 0.25 or row['x_value'] < 0.25) and 
              row['price_position'] > 0):  # 价格高于回归线
            return 'SHORT'
        
        return 'HOLD'
    
    def backtest_regression_strategy(self):
        """执行回归线策略回测"""
        print("\n🚀 开始回归线策略回测...")
        print("="*60)
        print(f"💰 初始资金: {self.initial_capital:,} 港币")
        print(f"📅 每月定投: {self.monthly_addition:,} 港币")
        print(f"📊 策略: 回归线 + XY确认 + 不对称止盈止损")
        print(f"📈 多头: 止盈{self.take_profit_long*100}%, 止损{self.stop_loss_long*100}%")
        print(f"📉 空头: 止盈{self.take_profit_short*100}%, 止损{self.stop_loss_short*100}%")
        print("="*60)
        
        capital = self.initial_capital
        total_trades = 0
        winning_trades = 0
        
        # 从第60天开始回测，确保指标稳定
        for i in range(60, len(self.data)):
            row = self.data.iloc[i]
            date = row['date']
            price = row['close']
            high = row['high']
            low = row['low']
            
            # 每月增加资金
            capital = self.add_monthly_capital(date, capital)
            
            # 检查现有持仓的止盈止损
            should_exit, exit_price, exit_type = self.check_exit_conditions(price, high, low)
            
            if should_exit:
                # 计算盈亏
                if self.position == 1:  # 多头
                    profit_pct = (exit_price - self.entry_price) / self.entry_price
                else:  # 空头
                    profit_pct = (self.entry_price - exit_price) / self.entry_price
                
                profit_amount = profit_pct * capital
                capital += profit_amount
                
                # 记录交易
                self.trades.append({
                    'exit_date': date.strftime('%Y-%m-%d'),
                    'exit_type': exit_type,
                    'exit_price': round(exit_price, 2),
                    'profit_pct': round(profit_pct * 100, 2),
                    'profit_amount': round(profit_amount, 2),
                    'capital_after': round(capital, 2)
                })
                
                if profit_amount > 0:
                    winning_trades += 1
                
                total_trades += 1
                self.position = 0
                self.entry_price = 0
            
            # 如果空仓，检查开仓信号
            if self.position == 0:
                signal = self.get_trading_signal(row)
                
                if signal == 'LONG':
                    self.position = 1
                    self.entry_price = price
                    self.trades.append({
                        'entry_date': date.strftime('%Y-%m-%d'),
                        'entry_type': 'long_entry',
                        'entry_price': round(price, 2),
                        'capital': round(capital, 2),
                        'price_position': round(row['price_position'], 4),
                        'x_value': round(row['x_value'], 3),
                        'y_value': round(row['y_value'], 3),
                        'e_value': round(row['e_value'], 3)
                    })
                
                elif signal == 'SHORT':
                    self.position = -1
                    self.entry_price = price
                    self.trades.append({
                        'entry_date': date.strftime('%Y-%m-%d'),
                        'entry_type': 'short_entry',
                        'entry_price': round(price, 2),
                        'capital': round(capital, 2),
                        'price_position': round(row['price_position'], 4),
                        'x_value': round(row['x_value'], 3),
                        'y_value': round(row['y_value'], 3),
                        'e_value': round(row['e_value'], 3)
                    })
            
            # 记录每日权益
            self.equity_curve.append({
                'date': date.strftime('%Y-%m-%d'),
                'capital': capital,
                'position': self.position,
                'price': price,
                'regression_line': row['regression_line'],
                'price_position': row['price_position']
            })
        
        self.final_capital = capital
        
        print(f"\n✅ 回归线策略回测完成!")
        print(f"📊 总交易次数: {total_trades}")
        print(f"📈 盈利次数: {winning_trades}")
        print(f"📉 亏损次数: {total_trades - winning_trades}")
        if total_trades > 0:
            print(f"🎯 胜率: {winning_trades/total_trades*100:.1f}%")
        print(f"💰 最终资金: {self.final_capital:,.0f} 港币")
        
        return pd.DataFrame(self.trades), pd.DataFrame(self.equity_curve)

    def analyze_results(self, trades_df, equity_df):
        """分析回测结果 - 按您的分析思路"""
        print("\n📊 回归线策略回测结果分析")
        print("=" * 60)

        # 基本统计
        entry_trades = trades_df[trades_df['entry_type'].notna()] if 'entry_type' in trades_df.columns else pd.DataFrame()
        exit_trades = trades_df[trades_df['exit_type'].notna()] if 'exit_type' in trades_df.columns else pd.DataFrame()

        total_trades = len(entry_trades)
        final_capital = self.final_capital
        total_invested = self.initial_capital + (len(equity_df) // 30) * self.monthly_addition
        net_return = final_capital - total_invested
        net_return_rate = (net_return / total_invested) * 100

        # 年化收益率
        if len(equity_df) > 0:
            start_date = pd.to_datetime(equity_df['date'].iloc[0])
            end_date = pd.to_datetime(equity_df['date'].iloc[-1])
            years = (end_date - start_date).days / 365
            annual_return_rate = ((final_capital / self.initial_capital) ** (1/years) - 1) * 100
        else:
            annual_return_rate = 0

        if len(exit_trades) > 0:
            winning_trades = len(exit_trades[exit_trades['profit_amount'] > 0])
            win_rate = winning_trades / len(exit_trades) * 100
            max_profit = exit_trades['profit_amount'].max()
            max_loss = exit_trades['profit_amount'].min()
            avg_profit = exit_trades['profit_amount'].mean()

            # 按交易类型分析
            long_trades = exit_trades[exit_trades['exit_type'].str.contains('long')]
            short_trades = exit_trades[exit_trades['exit_type'].str.contains('short')]

            tp_trades = exit_trades[exit_trades['exit_type'].str.contains('tp')]
            sl_trades = exit_trades[exit_trades['exit_type'].str.contains('sl')]
        else:
            winning_trades = 0
            win_rate = 0
            max_profit = 0
            max_loss = 0
            avg_profit = 0
            long_trades = pd.DataFrame()
            short_trades = pd.DataFrame()
            tp_trades = pd.DataFrame()
            sl_trades = pd.DataFrame()

        # 输出结果
        print(f"💰 资金统计:")
        print(f"• 初始资金: {self.initial_capital:,} 港元")
        print(f"• 总投入资金: {total_invested:,} 港元")
        print(f"• 最终资金: {final_capital:,.0f} 港元")
        print(f"• 净收益: {net_return:,.0f} 港元")
        print(f"• 净收益率: {net_return_rate:.2f}%")
        print(f"• 年化收益率: {annual_return_rate:.2f}%")

        if total_trades > 0:
            print(f"\n📈 交易统计:")
            print(f"• 总交易次数: {total_trades}")
            print(f"• 盈利次数: {winning_trades}")
            print(f"• 胜率: {win_rate:.1f}%")
            print(f"• 最大单笔盈利: {max_profit:,.0f} 港元")
            print(f"• 最大单笔亏损: {max_loss:,.0f} 港元")
            print(f"• 平均每笔盈亏: {avg_profit:,.0f} 港元")

            if len(long_trades) > 0:
                long_win_rate = len(long_trades[long_trades['profit_amount'] > 0]) / len(long_trades) * 100
                print(f"• 多头胜率: {long_win_rate:.1f}% ({len(long_trades)}笔)")

            if len(short_trades) > 0:
                short_win_rate = len(short_trades[short_trades['profit_amount'] > 0]) / len(short_trades) * 100
                print(f"• 空头胜率: {short_win_rate:.1f}% ({len(short_trades)}笔)")

            if len(tp_trades) > 0 and len(sl_trades) > 0:
                tp_rate = len(tp_trades) / (len(tp_trades) + len(sl_trades)) * 100
                print(f"• 止盈率: {tp_rate:.1f}% (止盈{len(tp_trades)}笔, 止损{len(sl_trades)}笔)")

        # 创建图表
        self.create_charts(trades_df, equity_df)

        # 保存结果
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        trades_filename = f"回归线策略交易记录_{timestamp}.xlsx"
        equity_filename = f"回归线策略权益曲线_{timestamp}.xlsx"

        if len(trades_df) > 0:
            trades_df.to_excel(trades_filename, index=False)
            print(f"\n✅ 交易记录已保存至: {trades_filename}")

        equity_df.to_excel(equity_filename, index=False)
        print(f"✅ 权益曲线已保存至: {equity_filename}")

        return trades_filename, equity_filename

    def create_charts(self, trades_df, equity_df):
        """创建分析图表"""
        print("📊 生成分析图表...")

        # 创建图表
        fig = plt.figure(figsize=(20, 16))

        # 1. 权益曲线与回归线对比
        ax1 = plt.subplot(2, 2, 1)
        equity_df['date_dt'] = pd.to_datetime(equity_df['date'])
        equity_df['capital_万'] = equity_df['capital'] / 10000

        plt.plot(equity_df['date_dt'], equity_df['capital_万'], linewidth=2, color='blue', label='权益曲线')
        plt.axhline(y=self.initial_capital/10000, color='red', linestyle='--', alpha=0.7, label='初始资金')

        # 标记持仓期间
        long_periods = equity_df[equity_df['position'] == 1]
        short_periods = equity_df[equity_df['position'] == -1]

        if len(long_periods) > 0:
            plt.scatter(long_periods['date_dt'], long_periods['capital_万'],
                       c='green', s=1, alpha=0.6, label='多头持仓')
        if len(short_periods) > 0:
            plt.scatter(short_periods['date_dt'], short_periods['capital_万'],
                       c='red', s=1, alpha=0.6, label='空头持仓')

        plt.title('权益曲线与持仓分析', fontsize=14, fontweight='bold')
        plt.xlabel('日期')
        plt.ylabel('资金 (万港币)')
        plt.legend()
        plt.grid(True, alpha=0.3)

        # 2. 恒指价格与回归线
        ax2 = plt.subplot(2, 2, 2)
        plt.plot(equity_df['date_dt'], equity_df['price'], linewidth=1, color='black', alpha=0.7, label='恒指价格')
        plt.plot(equity_df['date_dt'], equity_df['regression_line'], linewidth=2, color='red', label='回归线')

        plt.title('恒生指数价格 vs 回归线', fontsize=14, fontweight='bold')
        plt.xlabel('日期')
        plt.ylabel('恒指点数')
        plt.legend()
        plt.grid(True, alpha=0.3)

        # 3. 价格偏离度分布
        ax3 = plt.subplot(2, 2, 3)
        plt.hist(equity_df['price_position'], bins=50, alpha=0.7, color='skyblue', edgecolor='black')
        plt.axvline(0, color='red', linestyle='-', alpha=0.7, label='回归线')
        plt.axvline(equity_df['price_position'].mean(), color='green', linestyle='--',
                   label=f'平均偏离: {equity_df["price_position"].mean():.3f}')

        plt.title('价格相对回归线偏离度分布', fontsize=14, fontweight='bold')
        plt.xlabel('偏离度')
        plt.ylabel('天数')
        plt.legend()
        plt.grid(True, alpha=0.3)

        # 4. 交易盈亏分布
        ax4 = plt.subplot(2, 2, 4)
        if 'profit_amount' in trades_df.columns:
            profit_data = trades_df['profit_amount'].dropna()
            if len(profit_data) > 0:
                plt.hist(profit_data, bins=30, alpha=0.7, color='lightgreen', edgecolor='black')
                plt.axvline(0, color='red', linestyle='-', alpha=0.7)
                plt.axvline(profit_data.mean(), color='blue', linestyle='--',
                           label=f'平均盈亏: {profit_data.mean():.0f}港币')

                plt.title('交易盈亏分布', fontsize=14, fontweight='bold')
                plt.xlabel('盈亏金额 (港币)')
                plt.ylabel('交易次数')
                plt.legend()
                plt.grid(True, alpha=0.3)

        plt.tight_layout()

        # 保存图表
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        chart_filename = f"回归线策略分析图表_{timestamp}.png"
        plt.savefig(chart_filename, dpi=300, bbox_inches='tight')
        print(f"✅ 图表已保存至: {chart_filename}")

        plt.show()

        return chart_filename

def main():
    """主函数"""
    print("🏢 HSI50回归线策略回测系统")
    print("=" * 60)
    print("💰 初始资金: 30,000港币")
    print("📅 每月定投: 2,000港币")
    print("📊 分析周期: 25年历史数据")
    print("🎯 核心策略: 线性回归线 + XY值确认")
    print("📈 多头条件: E>0 且 X>0.45 且 Y>0.45 且价格低于回归线")
    print("📉 空头条件: (Y<0.25 或 X<0.25) 且价格高于回归线")
    print("🎯 止盈止损: 多头(1.6%/0.8%), 空头(0.8%/1.6%)")
    print("🔄 复利计算: 启用")

    # 创建回测器
    backtester = HSI50RegressionStrategy()

    # 获取数据
    if not backtester.fetch_hsi_data():
        return

    # 计算指标
    backtester.calculate_indicators()

    # 执行回测
    trades_df, equity_df = backtester.backtest_regression_strategy()

    # 分析结果
    backtester.analyze_results(trades_df, equity_df)

if __name__ == "__main__":
    main()
