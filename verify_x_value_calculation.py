#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
验证X值计算
===========

检查X值计算是否正确
"""

import pandas as pd
import numpy as np

def test_x_value_calculation():
    """测试X值计算"""
    
    print("🔍 验证X值计算...")
    
    # 测试给定的money_flow_ratio
    money_flow_ratio = 1.699
    
    # 按照700HK.py的公式计算
    mfi = 100 - (100 / (1 + money_flow_ratio))
    x_value = mfi / 100
    
    print(f"📊 理论计算:")
    print(f"   MoneyFlowRatio: {money_flow_ratio}")
    print(f"   MFI: {mfi:.4f}")
    print(f"   X值: {x_value:.4f}")
    
    # 检查最近生成的Excel文件
    try:
        # 读取最新的Excel文件
        import glob
        import os
        
        excel_files = glob.glob("交易记录追踪0700HK_*.xlsx")
        if excel_files:
            latest_file = max(excel_files, key=os.path.getctime)
            print(f"\n📋 检查文件: {latest_file}")
            
            df = pd.read_excel(latest_file)
            
            # 显示前几行的X值和MoneyFlowRatio
            print(f"\n📊 Excel文件中的数据 (前10行):")
            print("日期\t\tX值\t\tMoneyFlowRatio")
            print("-" * 50)
            
            for i in range(min(10, len(df))):
                date = df.iloc[i]['交易日期']
                x_val = df.iloc[i]['X值']
                mfr = df.iloc[i]['MoneyFlowRatio']
                print(f"{date}\t{x_val:.4f}\t\t{mfr:.4f}")
            
            # 检查是否有异常值
            x_values = df['X值']
            mfr_values = df['MoneyFlowRatio']
            
            print(f"\n📈 X值统计:")
            print(f"   最小值: {x_values.min():.4f}")
            print(f"   最大值: {x_values.max():.4f}")
            print(f"   平均值: {x_values.mean():.4f}")
            print(f"   中位数: {x_values.median():.4f}")
            
            print(f"\n📈 MoneyFlowRatio统计:")
            print(f"   最小值: {mfr_values.min():.4f}")
            print(f"   最大值: {mfr_values.max():.4f}")
            print(f"   平均值: {mfr_values.mean():.4f}")
            print(f"   中位数: {mfr_values.median():.4f}")
            
            # 验证计算关系
            print(f"\n🔍 验证计算关系 (随机抽取5行):")
            sample_indices = np.random.choice(len(df), min(5, len(df)), replace=False)
            
            for idx in sample_indices:
                mfr = df.iloc[idx]['MoneyFlowRatio']
                x_val = df.iloc[idx]['X值']
                
                # 重新计算
                calculated_mfi = 100 - (100 / (1 + mfr)) if mfr > 0 else 0
                calculated_x = calculated_mfi / 100
                
                print(f"   行{idx}: MFR={mfr:.4f} → 计算X={calculated_x:.4f}, 实际X={x_val:.4f}, 差异={abs(calculated_x-x_val):.6f}")
        
        else:
            print("❌ 未找到Excel文件")
            
    except Exception as e:
        print(f"❌ 读取Excel失败: {e}")

def manual_calculation_test():
    """手动计算测试"""
    
    print(f"\n🧮 手动计算测试:")
    
    test_cases = [
        0.1, 0.5, 1.0, 1.699, 2.0, 5.0, 10.0
    ]
    
    print("MoneyFlowRatio\tMFI\t\tX值")
    print("-" * 40)
    
    for mfr in test_cases:
        mfi = 100 - (100 / (1 + mfr))
        x_val = mfi / 100
        print(f"{mfr:.3f}\t\t{mfi:.2f}\t\t{x_val:.4f}")

def main():
    """主函数"""
    test_x_value_calculation()
    manual_calculation_test()

if __name__ == "__main__":
    main()
