#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
简化版Full_Y更新脚本 - 避免MySQL连接器警告
"""

import mysql.connector
from datetime import datetime
import sys

def update_full_y_simple():
    """简化版Full_Y更新"""
    print("简化版Full_Y更新工具")
    print("=" * 50)
    print(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

    try:
        # 数据库连接配置
        config = {
            'host': 'localhost',
            'port': 3306,
            'user': 'root',
            'password': '12345678',
            'database': 'finance',
            'charset': 'utf8mb4'
        }

        print("连接数据库...")
        connection = mysql.connector.connect(**config)
        cursor = connection.cursor()
        print("数据库连接成功")

        # 1. 调用sp_averagelineV3更新回归线
        print("调用sp_averagelineV3更新回归线...")
        try:
            cursor.callproc('sp_averagelineV3', ['eab_0023hk'])
            connection.commit()
            print("回归线更新完成")
        except Exception as e:
            print(f"回归线更新失败: {e}")

        # 2. 计算累积Full_Y
        print("计算累积Full_Y...")

        # 获取所有记录，按日期排序
        cursor.execute("""
            SELECT id, Date, Controller
            FROM eab_0023hk
            WHERE Controller IS NOT NULL
            ORDER BY Date ASC, id ASC
        """)

        records = cursor.fetchall()
        print(f"处理 {len(records)} 条记录...")

        # 逐行计算累积Full_Y
        cumulative_controller_1 = 0
        cumulative_total = 0

        for i, (record_id, date, controller) in enumerate(records):
            # 计算到当前行之前的累积统计
            if i == 0:
                full_y = 0.5  # 第一行默认值
            else:
                full_y = cumulative_controller_1 / cumulative_total if cumulative_total > 0 else 0.5

            # 更新Full_Y
            cursor.execute("UPDATE eab_0023hk SET Full_Y = %s WHERE id = %s", (full_y, record_id))

            # 更新累积计数 (在计算Full_Y之后)
            cumulative_total += 1
            if controller == 1:
                cumulative_controller_1 += 1

            # 验证最后一条记录
            if i == len(records) - 1:
                final_ratio = cumulative_controller_1 / cumulative_total
                print(f"   最后记录验证: Full_Y={full_y:.6f}, 全局比例={final_ratio:.6f}")

            # 显示进度
            if i % 100 == 0 or i < 10:
                print(f"   处理记录 {i+1}/{len(records)}: {date}, Full_Y={full_y:.6f}")

        connection.commit()
        print(f"Full_Y字段更新完成: {len(records)} 条记录")

        # 3. 更新E字段
        print("更新E字段...")
        cursor.execute("""
            UPDATE eab_0023hk
            SET E = CASE
                WHEN Full_Y IS NOT NULL AND MFI IS NOT NULL THEN
                    (8 * (MFI/100) - 3) * Full_Y - 3 * (MFI/100) + 1
                ELSE NULL
            END
            WHERE Full_Y IS NOT NULL AND MFI IS NOT NULL
        """)

        e_updated = cursor.rowcount
        connection.commit()
        print(f"E字段更新完成: {e_updated} 条记录")

        # 4. 显示最新记录
        cursor.execute("""
            SELECT Date, Close, Y_Value, Full_Y, Controller, E_Value, E
            FROM eab_0023hk
            ORDER BY Date DESC
            LIMIT 5
        """)

        latest_records = cursor.fetchall()
        print(f"\n最新5条记录:")
        print("日期        收盘价    Y_Value   Full_Y    Controller  E_Value     E")
        print("-" * 75)

        for record in latest_records:
            date, close, y_value, full_y, controller, e_value, e = record
            full_y_str = f"{full_y:7.4f}" if full_y is not None else "   NULL"
            controller_str = f"{controller:3d}" if controller is not None else "NULL"
            e_value_str = f"{e_value:8.4f}" if e_value is not None else "    NULL"
            e_str = f"{e:8.4f}" if e is not None else "    NULL"
            print(f"{date}  {close:7.2f}   {y_value:7.4f}   {full_y_str}   {controller_str:>4}     {e_value_str}  {e_str}")

        # 5. 统计信息
        cursor.execute("""
            SELECT
                COUNT(*) as total,
                COUNT(CASE WHEN Controller = 1 THEN 1 END) as controller_1,
                AVG(Full_Y) as avg_full_y
            FROM eab_0023hk
            WHERE Controller IS NOT NULL
        """)

        stats = cursor.fetchone()
        if stats:
            total, controller_1, avg_full_y = stats
            print(f"\n统计信息:")
            print(f"  总记录数: {total}")
            print(f"  Controller=1: {controller_1} ({controller_1/total*100:.1f}%)")
            print(f"  平均Full_Y: {avg_full_y:.6f}")

        cursor.close()
        connection.close()

        print(f"\nFull_Y更新完成！")
        print(f"完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

        return True

    except Exception as e:
        print(f"更新失败: {e}")
        return False

def main():
    """主函数"""
    success = update_full_y_simple()
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
