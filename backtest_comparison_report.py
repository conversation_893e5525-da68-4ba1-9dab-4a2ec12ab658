#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
回测结果对比分析报告
==================
对比HSI50回测与散户资金占比策略的表现
"""

import pandas as pd
import numpy as np
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

def analyze_backtest_comparison():
    """分析两个回测结果的对比"""
    
    print("📊 回测结果对比分析报告")
    print("=" * 80)
    print(f"📅 报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 回测结果对比数据
    backtest_comparison = {
        '策略名称': [
            'HSI50回测系统',
            '散户资金占比策略'
        ],
        '数据源': [
            'HSI50指数 (SQLite)',
            'hkhsi50数据 (MySQL)'
        ],
        '数据期间': [
            '2000-07-14 至 2025-07-14 (25年)',
            '1990-01-02 至 2025-07-18 (35.6年)'
        ],
        '数据记录数': [
            '6,158条',
            '8,772条'
        ],
        '初始资金': [
            '10,000元',
            '30,000港元'
        ],
        '每月追加': [
            '3,000元',
            '1,000港元'
        ],
        '最终资金': [
            '2,894,611.27元',
            '876,644.39港元'
        ],
        '年化收益率': [
            '25.43%',
            '1.81%'
        ],
        '总交易次数': [
            '1,399笔',
            '1,620笔'
        ],
        '胜率': [
            '41.53%',
            '46.3%'
        ],
        '最大回撤': [
            '未提供',
            '4.98%'
        ],
        '策略逻辑': [
            'XYE方法+回归线',
            'Y>0.43且X>0.43 (X=散户资金占比)'
        ]
    }
    
    df_comparison = pd.DataFrame(backtest_comparison)
    print(f"\n📊 回测结果对比总览:")
    print(df_comparison.to_string(index=False))
    
    # 深度分析
    print(f"\n🔍 深度对比分析:")
    
    print(f"\n   1. 📈 收益表现对比:")
    print(f"      🥇 HSI50回测系统:")
    print(f"         • 年化收益率: 25.43% (极高)")
    print(f"         • 最终资金: 289万元")
    print(f"         • 总收益率: 28,846.11%")
    print(f"         • 实际收益率: 213.95% (考虑每月追加)")
    
    print(f"\n      🥈 散户资金占比策略:")
    print(f"         • 年化收益率: 1.81% (保守)")
    print(f"         • 最终资金: 87.7万港元")
    print(f"         • 总收益率: 89.46%")
    print(f"         • 净收益: 41.4万港元")
    
    print(f"\n      💡 收益差异分析:")
    print(f"         • HSI50策略收益率是散户策略的14倍")
    print(f"         • 但两者数据期间和市场环境不同")
    print(f"         • HSI50策略风险可能更高")
    
    print(f"\n   2. 🎯 交易特征对比:")
    print(f"      📊 HSI50回测系统:")
    print(f"         • 总交易: 1,399笔 (25年)")
    print(f"         • 平均每年: 56笔交易")
    print(f"         • 胜率: 41.53%")
    print(f"         • 平均盈利: 11,746元")
    print(f"         • 平均亏损: -5,917元")
    print(f"         • 盈亏比: 1.98:1")
    
    print(f"\n      📊 散户资金占比策略:")
    print(f"         • 总交易: 1,620笔 (35.6年)")
    print(f"         • 平均每年: 45笔交易")
    print(f"         • 胜率: 46.3%")
    print(f"         • 看涨交易: 82.0%")
    print(f"         • 看跌交易: 18.0%")
    print(f"         • 看跌胜率: 62.9%")
    
    print(f"\n      💡 交易特征分析:")
    print(f"         • 散户策略胜率更高 (46.3% vs 41.53%)")
    print(f"         • HSI50策略盈亏比更好 (1.98 vs 未知)")
    print(f"         • 散户策略实现了多空平衡")
    print(f"         • 交易频率相近")
    
    print(f"\n   3. 🛡️ 风险控制对比:")
    print(f"      📊 HSI50回测系统:")
    print(f"         • 最大回撤: 未提供")
    print(f"         • 止盈: 1.2%")
    print(f"         • 止损: 0.6%")
    print(f"         • 风险控制: 严格的止盈止损")
    
    print(f"\n      📊 散户资金占比策略:")
    print(f"         • 最大回撤: 4.98% (优秀)")
    print(f"         • 止盈: 1.6%")
    print(f"         • 止损: 0.8%")
    print(f"         • 卡尔玛比率: 0.36")
    
    print(f"\n      💡 风险分析:")
    print(f"         • 散户策略回撤控制优秀")
    print(f"         • HSI50策略止损更严格")
    print(f"         • 高收益通常伴随高风险")
    
    print(f"\n   4. 📊 策略逻辑对比:")
    print(f"      🎯 HSI50回测系统:")
    print(f"         • 使用XYE方法")
    print(f"         • 结合回归线趋势判断")
    print(f"         • 多头: E>0 且 X>0.45 且 Y>0.45 且价格低于回归线")
    print(f"         • 空头: Y<0.25 或 X<0.25 且价格高于回归线")
    print(f"         • 复杂的多因子模型")
    
    print(f"\n      🎯 散户资金占比策略:")
    print(f"         • 使用XY方法")
    print(f"         • X = 散户资金占比")
    print(f"         • Y = 控股商控制比例")
    print(f"         • 多头: Y>0.43 且 X>0.43")
    print(f"         • 简单清晰的逻辑")
    
    print(f"\n      💡 策略逻辑分析:")
    print(f"         • HSI50策略更复杂，考虑因子更多")
    print(f"         • 散户策略更简单，易于理解和执行")
    print(f"         • 两者都基于市场参与者行为分析")
    
    # 市场环境分析
    print(f"\n🌍 市场环境影响分析:")
    
    print(f"\n   📊 数据期间差异:")
    print(f"   • HSI50: 2000-2025 (25年)")
    print(f"     - 包含2000年科技泡沫")
    print(f"     - 包含2008年金融危机")
    print(f"     - 包含2020年疫情冲击")
    print(f"     - 包含近期市场波动")
    
    print(f"\n   • 散户策略: 1990-2025 (35.6年)")
    print(f"     - 包含1997年亚洲金融危机")
    print(f"     - 包含更长的历史周期")
    print(f"     - 经历了更多市场环境")
    
    print(f"\n   💡 环境影响:")
    print(f"   • HSI50策略在特定25年期间表现优异")
    print(f"   • 散户策略在更长周期中表现稳定")
    print(f"   • 不同时期的市场特征可能影响策略效果")
    
    # 实用性分析
    print(f"\n🚀 实用性对比分析:")
    
    print(f"\n   📋 执行难度:")
    print(f"   • HSI50策略:")
    print(f"     - 需要计算回归线")
    print(f"     - 需要XYE三个指标")
    print(f"     - 逻辑相对复杂")
    print(f"     - 参数较多")
    
    print(f"\n   • 散户策略:")
    print(f"     - 只需要XY两个指标")
    print(f"     - 逻辑简单清晰")
    print(f"     - 参数较少")
    print(f"     - 易于理解和执行")
    
    print(f"\n   💰 资金要求:")
    print(f"   • HSI50策略: 初始1万+每月3千")
    print(f"   • 散户策略: 初始3万+每月1千")
    print(f"   • 两者资金要求都不高")
    
    print(f"\n   🎯 适用人群:")
    print(f"   • HSI50策略: 追求高收益的激进投资者")
    print(f"   • 散户策略: 稳健型投资者")
    print(f"   • 不同风险偏好的投资者")
    
    # 优化建议
    print(f"\n💡 策略优化建议:")
    
    print(f"\n   🔧 HSI50策略优化:")
    print(f"   • 增加回撤控制机制")
    print(f"   • 考虑动态止损")
    print(f"   • 优化仓位管理")
    print(f"   • 增加风险指标监控")
    
    print(f"\n   🔧 散户策略优化:")
    print(f"   • 提高收益率水平")
    print(f"   • 优化信号质量")
    print(f"   • 增加交易频率")
    print(f"   • 考虑动态参数")
    
    print(f"\n   🤝 策略结合:")
    print(f"   • 可以考虑两策略组合使用")
    print(f"   • HSI50策略用于追求收益")
    print(f"   • 散户策略用于风险控制")
    print(f"   • 根据市场环境动态调整权重")
    
    # 风险提示
    print(f"\n⚠️ 重要风险提示:")
    
    print(f"\n   📊 HSI50策略风险:")
    print(f"   • 高收益伴随高风险")
    print(f"   • 回撤情况未知")
    print(f"   • 策略复杂度较高")
    print(f"   • 可能存在过拟合")
    
    print(f"\n   📊 散户策略风险:")
    print(f"   • 收益率相对较低")
    print(f"   • 信号覆盖率有限")
    print(f"   • 散户行为可能变化")
    print(f"   • 需要长期坚持")
    
    print(f"\n   💰 通用风险:")
    print(f"   • 历史表现不代表未来")
    print(f"   • 市场环境可能变化")
    print(f"   • 需要严格执行纪律")
    print(f"   • 定期监控和调整")
    
    # 总结
    print(f"\n🎉 总结:")
    
    print(f"\n   🎯 核心发现:")
    print(f"   • HSI50策略: 高收益高风险，适合激进投资者")
    print(f"   • 散户策略: 稳健收益低风险，适合保守投资者")
    print(f"   • 两策略各有优势，可以互补")
    print(f"   • 投资者应根据自身情况选择")
    
    print(f"\n   📊 选择建议:")
    print(f"   • 追求高收益: 选择HSI50策略")
    print(f"   • 注重风控: 选择散户策略")
    print(f"   • 平衡需求: 考虑组合使用")
    print(f"   • 新手投资者: 建议从散户策略开始")
    
    print(f"\n   💡 投资哲学:")
    print(f"   两个回测结果展示了不同的投资理念：")
    print(f"   HSI50策略追求最大化收益，散户策略注重")
    print(f"   风险控制。投资者应根据自身风险承受")
    print(f"   能力和投资目标，选择合适的策略。")

def calculate_risk_adjusted_metrics():
    """计算风险调整指标"""
    
    print(f"\n📊 风险调整指标对比:")
    print(f"=" * 50)
    
    # HSI50策略数据
    hsi50_annual_return = 0.2543
    hsi50_trades = 1399
    hsi50_years = 25
    hsi50_win_rate = 0.4153
    
    # 散户策略数据
    retail_annual_return = 0.0181
    retail_max_drawdown = 0.0498
    retail_trades = 1620
    retail_years = 35.6
    retail_win_rate = 0.463
    retail_calmar = 0.36
    
    print(f"   指标 | HSI50策略 | 散户策略 | 优势")
    print(f"   " + "-" * 45)
    print(f"   年化收益率 | {hsi50_annual_return*100:7.2f}% | {retail_annual_return*100:7.2f}% | HSI50")
    print(f"   胜率 | {hsi50_win_rate*100:7.2f}% | {retail_win_rate*100:7.2f}% | 散户")
    print(f"   交易频率 | {hsi50_trades/hsi50_years:7.1f}/年 | {retail_trades/retail_years:7.1f}/年 | 相近")
    print(f"   最大回撤 | 未知 | {retail_max_drawdown*100:7.2f}% | 散户")
    print(f"   卡尔玛比率 | 未知 | {retail_calmar:7.2f} | 散户")
    
    print(f"\n   💡 风险调整后的评估:")
    print(f"   • HSI50策略收益率极高，但风险未知")
    print(f"   • 散户策略风险调整收益良好")
    print(f"   • 需要HSI50策略的回撤数据才能全面比较")

def main():
    """主函数"""
    analyze_backtest_comparison()
    calculate_risk_adjusted_metrics()
    
    print(f"\n🎉 回测对比分析完成！")
    print(f"   关键结论: 两个策略各有优势，HSI50策略收益高，")
    print(f"   散户策略风险低，投资者可根据需求选择。")

if __name__ == "__main__":
    main()
